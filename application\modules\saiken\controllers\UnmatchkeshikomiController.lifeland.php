<?php

/**
 *  Saiken_UploadfurikomiController
 *
 * 入金アンマッチ消込 コントローラクラス
 *
 * @category   Saiken
 * @package    controller
 * <AUTHOR> Kino
 * @since      2021/02/XX
 * @filesource 
 */
class Saiken_UnmatchkeshikomiController extends Zend_Controller_Action {

    /** 処理方法：銀行 */
    const BANK_JUDG = 1010;
    /** 処理方法：コンビニ */
    const CONVENI_JUDG = 120;
    /** 処理タイプ：新規連結 */
    const PROC_TYPE_NEW = 1;
    /** 処理タイプ：履歴表示 */
    const PROC_TYPE_HIST = 2;
    /** ファイルの最大値（単位：M） */
    const FILE_MAX_SIZE = 2;
    /** ファイル種別 **/
    const FILE_TYPE_BANK = 1; // 1：振込
    const FILE_TYPE_CONV = 2; // 2：コンビニ
    /** 1レコードのバイト数 */
    const ONE_RECORD_BYTE = 200;
    /**
     * @var $doInsCnt 処理件数(Insert)
     */
    protected $doInsCnt    = 0;    // 処理件数(Insert)

    /*
     * メイン処理
     */
    public function nyukinAction() {
        // 利用 JavaScript 設定
        App_Smarty::pushJsFile([
            'app/saiken.unmatchkeshikomi.js',
//            'app/saiken.seikyumulti.js'
        ]);

        // 利用 CSS 設定
        App_Smarty::pushCssFile([
            'lib_jq.css',
            'app/sais.css',
            'main_pub.css',
//            'app/mstr.css',
            'app/saiken.unmatchkeshikomi.css',
        ]);

        //画面初期表示時のデータ取得
        $data = $this->_getInitData();
        $jsonData = Msi_Sys_Utils::json_encode($data);
        $this->view->data_json = $jsonData;
    }
    
    /**
     * 画面初期表示用データ取得
     *
     * <AUTHOR> Kino
     * @since  2021/02/xx
     */
    private function _getInitData() {
        
        $db = Msi_Sys_DbManager::getMyDb();
        // プルダウン 会社
        $param = array('bumon_cd' => array('<>', '00001') // 全社は表示しない
                      ,'bumon_kbn' => 1                   // 部門区分：部門
            );
        $bumon_cd = array();
        $bumon_ref = App_Utils2::getBumonRef(1, null, $db);
        if (isset($bumon_ref) && Msi_Sys_Utils::myCount($bumon_ref) > 0) {
            // 参照権限部門を条件に追加
            $param['__x99'] = DataMapper_Utils::condOneOf('bumon_cd', implode(',', $bumon_ref));
            $bumonSel = DataMapper_Bumon::find($db, $param);
            $bumon_cd = Msi_Sys_Utils::remapArray( $bumonSel, 'bumon_cd  bumon_lnm',
                                               array('bumon_cd'=>'id', 'bumon_lnm'=>'text') );
        }
        // プルダウン 消込区分
        $keshikomi_kbn = array(array('id' => '0','text' => '未消込')
                              ,array('id' => '1','text' => '消込済み')
                              ,array('id' => '9','text' => '削除'));
        
        $kaisyu_kbn = DataMapper_CodeNmMst::find($db, array('code_kbn' => '0430'));
        $fee_kbn = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8552'));

        $data = array(
            'bumon_cd' => $bumon_cd,
            'keshikomi_kbn' => $keshikomi_kbn,
            'kaisyu_kbn' => $kaisyu_kbn,
            'fee_kbn' => $fee_kbn
        );

        return $data;
    }
    
    /**
     * 検索結果表示 アクション
     *
     * <AUTHOR> Kino
     * @since  2020/02/XX
     * @params boolean $isRtnJson   JSON 形式で返すか
     */
    public function searchAction()
    {
        try {
            if ( isset($this->_params) ) {
                $params  = $this->_params;
            } else {
                $this->_params = $params = Msi_Sys_Utils::webInputs();
            }

            $db = Msi_Sys_DbManager::getMyDb();
            $limit = Msi_Sys_Utils::easyGetVar($params, 'limit', 'DIGIT', 100);
            $this->_params['offset'] = $offset = Msi_Sys_Utils::easyGetVar($params, 'offset', 'DIGIT', 0);
            

            $cond = array(
                          '__etc_orderby' => array('history_no desc','msi_no desc'),
                          '__etc_limit'   => $limit,
                          '__etc_offset'  => $offset,
                          );

            $bumon_cd   = Msi_Sys_Utils::easyGetVar($params, 's_kaisya_cd');
            $upload_date = Msi_Sys_Utils::easyGetVar($params, 's_torikomi_ymd', 'DATE', null, 'emptyToNull');
            $payment_kbn = Msi_Sys_Utils::easyGetVar($params, 's_keshikomi_kbn');
            
            // 選択された部門の親部門を取得する
            $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $bumon_cd));
            $kaisya_cd = $bumonData[0]['oya_bumon_cd'];
            // 完全一致
            foreach ( Msi_Sys_Utils::strArrayify_qw( 'kaisya_cd payment_kbn') as $k ) {
                if ( strlen($$k) > 0 ) {
                    $cond[$k] = $$k;
                }
            }
            
            // 取込日
            if ( strlen($upload_date) > 0 ) {
                if($payment_kbn == '0'){
                    $cond['__x11'] = array( 'x', "upload_date <= :x1_1", array('x1_1'=>$upload_date) );
                }else{
                    $cond['__x11'] = array( 'x', "upload_date = :x1_1", array('x1_1'=>$upload_date) );
                }
            }
            
            // 自動消込フラグ (常に検索対象)
            $cond['auto_payment_flg'] = 0; // 0：自動未消込(アンマッチ) 1：自動消込(マッチ)

            $select = $this->_search( $db, $cond, $payment_kbn=null, $mode=0, $bumon_cd);

            $data = array();
            $count = 0;
            foreach ( $select as $rec ) {
                $count++;
                $rec['row'] = $count;
                $data[] = $rec;
            }
            
            // 次ページにデータが何件あるのかの判定用
            $nextOffset = $offset * 2;
            $nextOffset == 0 ? $nextOffset = 100 : $nextOffset;
            $next_cond = $cond;
            $next_cond['__etc_offset'] = $nextOffset;
            $nextSelect = $this->_search( $db, $next_cond);
            if(empty($nextSelect)){
                $nextCnt = 0;
            }else{
                $nextCnt = count($nextSelect);
            }
        }
        catch ( Msi_Sys_Exception_InputException $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::debug( '検索条件エラー=>' . $err );
            $msg = '検索条件エラーです';
        }
        catch ( Exception $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::err( basename(__FILE__).': '. $err );
            $msg = '内部エラーです';
        }

        if ( isset($data) ) {
            if ( $count <= 0 ) {
                $msg = '該当するデータはありません';
            }
        }
        
        if ( isset($msg) ) {
            $rtnData = array(
                            'status'      => 'NG',
                            'msg'         => $msg,
                            'dataPayment' => $data,
                            'nextcount'   => $nextCnt,
                            );
        } else {
            $rtnData = array(
                            'status'      => 'OK',
                            'msg'         => '',
                            'dataPayment' => $data,
                            'nextcount'   => $nextCnt,
                             );
        }
        Msi_Sys_Utils::outJson( $rtnData );
        return;
    }
    
    /**
     * 請求検索結果表示 アクション
     *
     * <AUTHOR> Kino
     * @since  2020/02/XX
     * @params boolean $isRtnJson   JSON 形式で返すか
     */
    public function searchseikyuAction($isRtnJson=false)
    {
        try {
            if ( isset($this->_params) ) {
                $params  = $this->_params;
            } else {
                $this->_params = $params = Msi_Sys_Utils::webInputs();
            }

            $db = Msi_Sys_DbManager::getMyDb();
            $limit = Msi_Sys_Utils::easyGetVar($params, 'limit', 'DIGIT', 100);

            $dataCol     = Msi_Sys_Utils::json_decode($params['DataColJson']);
            $bumon_cd     = Msi_Sys_Utils::json_decode($params['bumon_cd']);
            $br_koza_no  = Msi_Sys_Utils::easyGetVar($dataCol, 'kouza_no');
            $sekyu_knm    = Msi_Sys_Utils::easyGetVar($dataCol, 'furikomi_nm');
            $history_no  = Msi_Sys_Utils::easyGetVar($dataCol, 'history_no');
            $msi_no    = Msi_Sys_Utils::easyGetVar($dataCol, 'msi_no');
            $payment_kbn    = Msi_Sys_Utils::easyGetVar($dataCol, 'payment_kbn');
            
            if($payment_kbn == 0){ // 0：未消込 1：消込済み 9：削除
                // 名前の補正処理 (振込入金消込画面と同様の処理を呼び出す)
//                $rpl_sekyu_knm = Logic_UploadFurikomiMake::changName($sekyu_knm);

                // 口座番号
                if(strlen($br_koza_no) > 0){
                    $cond['__x1'] = array( 'x', "(br_koza_no = :x1_1 AND seikyu_zan <> :x1_2)",
                                       array('x1_1'=>$br_koza_no, 'x1_2'=>0) );
                }

                // 請求先名カナ
//                if(strlen($rpl_sekyu_knm) > 0){
//                    $cond['__x2'] = array( 'x', "(rpl_sekyu_knm = :x2_1 AND seikyu_zan <> :x2_2)",
//                                           array('x2_1'=>$rpl_sekyu_knm, 'x2_2'=>0) );
//                }
            }
            
            // マッチ済み請求伝票
            $cond['__x3'] = array( 'x', "(history_no = :x3_1 AND msi_no = :x3_2)",
                                   array('x3_1'=>$history_no, 'x3_2'=>$msi_no) );
            
            $condTxt = '';
            $condArr = array();
            foreach($cond as $condRec){
                if(empty($condArr)){
                    $condArr = $condRec[2];
                    $condTxt = $condRec[1];
                }else{
                    $condArr = array_merge ($condArr, $condRec[2]);
                    $condTxt .= 'OR'. $condRec[1];
                }
                $condAll['__x'] = array('x', $condTxt, $condArr);
            }
            
            $condAll['__etc_orderby'] = array('seikyu_den_no desc');
            
                // 部門コード
            if ( strlen($bumon_cd) > 0 ) {
                $condAll['bumon_cd'] = array( '~', $bumon_cd );
            }
//            $condAll['__etc_limit'] = $limit +1;
            
            $select = $this->_search( $db, $condAll, $payment_kbn, 1, $bumon_cd);
            
            $dataSeikyu = $this->_pickDataApp( $select );

            $data = array();
            $count = 0;
            if(!empty($dataSeikyu)){
                foreach ( $dataSeikyu as $rec ) {
                    $count++;
                    if ( $count > $limit ) {
                        break;
                    }
                    $rec['row'] = $count;
                    $data[] = $rec;
                }
            }else{
                $data = array();
            }
            
        }
        catch ( Msi_Sys_Exception_InputException $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::debug( '検索条件エラー=>' . $err );
            $msg = '検索条件エラーです';
        }
        catch ( Exception $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::err( basename(__FILE__).': '. $err );
            $msg = '内部エラーです';
        }

        if ( isset($data) ) {
            if ( $count <= 0 ) {
//                $msg = '該当するデータはありません';
            } else if ( $count > $limit ) {
//                $msg = '該当するデータが他に存在します';
            }
        }

        if ( isset($msg) ) {
            $rtnData = array(
                          'status'     => 'NG',
                          'msg'        => $msg,
                          'dataSeikyu' => $data,
                          );
        } else {
            $rtnData = array(
                             'status' => 'OK',
                             'msg' => '',
                             'dataSeikyu' => $data,
                             );
        }
        Msi_Sys_Utils::outJson( $rtnData );
        return;
    }
    
    /**
     * 一覧情報を検索する
     *
     * <AUTHOR> Kino
     * @param  Msi_Sys_Db $db
     * @param  array      $keyHash  条件
     * @param  string      $payment_kbn  消込区分
     * @param  array      $mode     0：振込入金消込情報検索、1：請求伝票検索
     * @since  2021/02/XX
     */
    protected function  _search($db, $keyHash, $payment_kbn=null, $mode=0, $bumon_cd=null)
    {
        if($mode == 0){
            // アンマッチデータ取得
            return DataMapper_UnMatchKeshikomi::findFurikomiInfo( $db, $keyHash, false, $bumon_cd);
        }else if($mode == 1){
            // 請求データ取得
            return DataMapper_UnMatchKeshikomi::findSeikyuDenpyoForSeikyu($db, $keyHash, $payment_kbn);
        }
    }
    
    /**
     * msi 請求明細(複数)取得アクション
     * 
     * <AUTHOR> Kino
     * @since  2021/02/XX
     */
    public function sekyumsimultiAction() {
        $req = $this->getRequest();
        if ($req->isPost()) {
            $dataMsi = array();
            $dataMulti = Msi_Sys_Utils::json_decode($req->getPost('dataJson'));
            foreach ($dataMulti as $one) {
                $dataMsi[] = $this->getSeikyuData($one); // 請求データ
            }
            $dataSeikyu = $this->_pickDataApp( $dataMsi );
            $data = array(
                'status' => 'OK',
                'msg' => '情報を取得しました',
                'dataSeikyu' => $dataSeikyu,
            );
            Msi_Sys_Utils::outJson($data);
        }
    }
    
    /**
     * 請求情報取得処理 
     *
     * <AUTHOR> Kino
     * @since  2021/02/XX
     * @param array $dataOneMsi 一明細情報
     * @return array 請求情報
     */
    public function getSeikyuData($dataOneMsi) {
        $seikyu = array();
        $cond['seikyu_den_no'] = $dataOneMsi['seikyu_den_no'];
        $db = Msi_Sys_DbManager::getMyDb();
        $select = DataMapper_UnMatchKeshikomi::findSeikyuDenpyo( $db, $cond);
        if (count($select) > 0) {
            $seikyu = $select[0];
        }
        return $seikyu;
    }
    
    /**
     * 伝票データから項目を抜き出して返す
     *
     * <AUTHOR> Kino
     * @since  2021/02/XX
     * @param  array  $hdrData
     * @return array
     */
    protected function _pickDataApp($hdrData)
    {
        $data = array();
        
        foreach ( $hdrData as $rec ) {
            $newRec = Msi_Sys_Utils::remapArrayFlat( $rec, <<< END_OF_TXT
seikyu_kbn  seko_no  seko_no_sub  seikyu_den_no  kaisya_cd  bumon_cd
seikyu_ymd  br_koza_no  sekyu_knm
sekyu_tel  seikyu_zei_prc  nyukin_prc  seikyu_zan seikyu_zan_org
history_no  data_kbn
END_OF_TXT
                                                  );
            $data[] = $newRec;
        }

        return $data;
    }

    /**
     * save アクション　保存処理
     *
     * <AUTHOR> Kino
     * @since      2021/02/XX
     * 
     */
    public function saveAction() {
        $req = $this->getRequest();
        try {
            if ($req->isPost()) {
                //データ取得
                $paymentCol = Msi_Sys_Utils::json_decode($req->getPost('paymentColJson'));   //画面データ(振込入金消込情報)
                $seikyuCol = Msi_Sys_Utils::json_decode($req->getPost('seikyuColJson'));       //画面データ(請求伝票)
                
                if(empty($paymentCol)){
                    $message = sprintf("対象の入金データを選択してください.");
                    throw new Msi_Logic_ExceptionInput($message);
                }
                
                $db = Msi_Sys_DbManager::getMyDb();
                
                //新規登録処理
                $keshikomi_kbn = $paymentCol[0]['payment_kbn']; // 消込区分
                $cnt = Logic_UnmatchKeshikomiMake::Main($db, $paymentCol[0], $seikyuCol, $keshikomi_kbn);
                
                //コミット
                $db->commit();

                $data = array(
                    'status' => 'OK',
                    'msg' => ('保存しました'),
                );
                Msi_Sys_Utils::outJson($data);
            }
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            Msi_Sys_Utils::outJson($errData);
        }
    }
    
    /**
     * cancel アクション　マッチ解除保存処理
     *
     * <AUTHOR> Kino
     * @since      2021/02/XX
     * 
     */
    public function cancelAction() {
        $req = $this->getRequest();
        try {
            if ($req->isPost()) {
                //データ取得
                $paymentCol = Msi_Sys_Utils::json_decode($req->getPost('paymentColJson'));   //画面データ(振込入金消込情報)
                
                if(empty($paymentCol)){
                    $message = sprintf("対象の入金データを選択してください.");
                    throw new Msi_Logic_ExceptionInput($message);
                }
                
                $db = Msi_Sys_DbManager::getMyDb();
                
                //新規登録処理            
                $cnt = Logic_UnmatchKeshikomiMake::Main($db, $paymentCol[0], array(), 1);
                
                //コミット
                $db->commit();

                $data = array(
                    'status' => 'OK',
                    'msg' => ('保存しました'),
                );
                Msi_Sys_Utils::outJson($data);
            }
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            Msi_Sys_Utils::outJson($errData);
        }
    }
}
