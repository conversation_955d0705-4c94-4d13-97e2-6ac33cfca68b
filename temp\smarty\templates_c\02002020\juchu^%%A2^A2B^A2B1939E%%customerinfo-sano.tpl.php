<?php /* Smarty version 2.6.28, created on 2025-06-05 11:47:57
         compiled from juchu/customerinfo/customerinfo-sano.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'juchu/customerinfo/customerinfo-sano.tpl', 45, false),)), $this); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "fdn_head_std.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "fdn_header_1.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<div class="container div-fixed-for-spin" id="main-container">
    <form  id="customerinfo-form-id">
        <div id="main">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "header_info.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "side_menu.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <div id="detail">
                <div id = "customer-div-wrapper" style="display: none">
                    <fieldset class="base_1">
                        <label for="apply_type" class="lbl_apply_type require done">申込区分</label>
                        <input type="hidden" name="apply_type" id="apply_type" class="cls_apply_type"/>
                        <label for="funeral_type" class="lbl_funeral_type require done">葬儀区分</label>
                        <input type="hidden" name="funeral_type" id="funeral_type" class="cls_funeral_type"/>
                        <label for="code" class="lbl_code require done">台帳番号</label>
                        <input name="code_1" id="code_1" type="text" class="txt" value="" maxlength = "2"/>
                        <div class="label code">-</div>
                        <input name="code_2" id="code_2" type="text" class="txt" value="" maxlength = "2"/>
                        <div class="label code">-</div>
                        <input name="code_3" id="code_3" type="text" class="txt" value="" maxlength = "4"/>
                        <label for="personal_info" class="lbl_personal_info require done">個人情報保護</label>
                        <input type="hidden" name="personal_info" id="personal_info" class="cls_personal_info"/>
                    </fieldset>
                    <fieldset class="base_2">
                        <label for="member" class="lbl_member require done">会員</label>
                        <input type="hidden" name="member" id="member" class="cls_member"/>
                        <label for="carn_no" class="lbl_carn_no require done">ｶｰﾆﾊﾞﾙ番号</label>
                        <input name="carn_no" id="carn_no" type="text" class="txt" value="" maxlength = "10"/>
                                                <label for="staff_1" class="lbl_staff require">見積担当者</label>
                        <input name="staff_1" id="staff_1" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                        <div class="label dlg_staff dlg_staff1 cursor-pointer"></div>
                        <label for="staff_2" class="lbl_staff require done">施行担当者</label>
                        <input name="staff_2" id="staff_2" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                        <div class="label dlg_staff dlg_staff2 cursor-pointer"></div>
                    </fieldset>

                    <ul class="tab">
                        <li id="tab-input-info1"><span>受付情報①</span></li>
                        <li id="tab-input-info2"><a href="javascript:void(0)">受付情報②</a></li>
                        <li id="tab-seko-info"><a href="javascript:void(0)">施行情報</a></li>
                        <li id="tab-seikyu-info"><a href="javascript:void(0)">喪主・御請求情報</a></li>
                        <li id="tab-kaiin-info"><a href="javascript:void(0)">会員情報</a></li>
                        <li id="tab-other-info"><a href="javascript:void(0)">ご依頼確認書</a></li>
                        <?php if (((is_array($_tmp=$this->_tpl_vars['kashidashi_disp'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')) == 1): ?><li id="tab-kashidasi-info"><a href="javascript:void(0)">貸出備品</a></li><?php endif; ?>
                        <li id="tab-report-info"><a href="javascript:void(0)">報告書</a></li>
                    </ul>
                        <div id = "customer-tab" class="customer_div_cls">
                                                <div id = "input-tab-info" class = "tab-contents">
                            <fieldset class="base_1">
                                <label for="uketuke_date" class="lbl_uketuke_date require done">受注日</label>
                                <input name="uketuke_date" id="uketuke_date" type="text" class="txt ime-off" value="" maxlength = "10"/>
                                <div class="label dlg_date"></div>
                                <input name="uketuke_time" id="uketuke_time" type="text" class="txt ime-off" value="" maxlength = "5"/>
                                <div class="label dlg_time"></div>
                                <label for="uketuke_danto" class="require done">電話受付</label>
                                <input name="uketuke_danto" id="uketuke_danto" type="text" class="txt" value="" maxlength = "20"/>
                                <div class="label dlg_staff dlg_uketuke_danto cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="uketuke_kojin_name" class="lbl_name require lbl_replace_kojin done">故人様のお名前</label>
                                <input name="uketuke_kojin_name" id="uketuke_kojin_name" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label lbl_name_sama">様</div>
                                <label for="uketuke_kojin_kana" class="lbl_uketuke_kojin_kana require done">フリガナ</label>
                                <input name="uketuke_kojin_kana" id="uketuke_kojin_kana" type="text" class="txt" value="" maxlength = "30"/>
                            </fieldset>
                            <fieldset class="address_1">
                                <div class="lbl_address subtitle require done">現住所</div>
                                <label for="u_zip_1" class="lbl_zip done">〒</label>
                                <input name="u_zip_1" id="u_zip_1" type="text" data-zip = "uk1" class="txt zip_helper ime-off" value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="u_address_1_1" id="u_address_1_1" type="text" class="txt" value=""  maxlength = "30"/>
                                <div class="label dlg_map"></div>
                                <label for="u_tel_1" class="require done">TEL</label>
                                <input name="u_tel_1" id="u_tel_1" type="text" class="txt ime-off" value="" maxlength = "15" placeholder="************"/>
                                <input name="u_address_1_2" id="u_address_1_2" type="text" class="txt" value=""  maxlength = "30"/>
                                <label for="u_head_1" class="require done">世帯主</label>
                                <input type="hidden" name="u_head_1" id="u_head_1" class="cls_head_1"/>
                            </fieldset>
                            <fieldset class="base_00 sibo_date_field">
                                <label for="sibo_date" class="lbl_sibo_date require done">死亡日</label>
                                <input name="sibo_date" id="sibo_date" type="text" class="txt ime-off" value="" maxlength = "10"/>
                                <div class="label dlg_date"></div>
                                <input name="sibo_time" id="sibo_time" type="text" class="txt ime-off" value="" maxlength = "5"/>
                                <div class="label dlg_time"></div>
                                <label id="lbl_pacemaker" class="option done">ペースメーカー</label>
                                <span id="pacemaker_set" class="radio_set" data-ref_attr = 'free4_kbn'>
                                    <label for="pacemaker_1" class="lbl_pacemaker_1">無</label><input name="pacemaker" class="" id="pacemaker_1" type="radio" value="0" />
                                    <label for="pacemaker_2" class="lbl_pacemaker_2">有</label><input name="pacemaker" class="" id="pacemaker_2" type="radio" value="1" />
                                </span>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="sibo_basho" class="lbl_sibo_basho require done">亡くなられた場所</label>
                                <input type="hidden" name="sibo_basho" id="sibo_basho" class="cls_sibo_basho"/>
                                <input name="sibo_basho_name" id="sibo_basho_name" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_sibo_basho cursor-pointer"></div>
                                <input name="byouto" id="byouto" type="text" class="txt" value="" maxlength = "20"/>
                                <div class="label lbl_name_sama lbl_name_byouto">病棟</div>
                                <input name="byouto_gai" id="byouto_gai" type="text" class="txt" value="" maxlength = "4"/>
                                <div class="label lbl_name_sama">階</div>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="keisatsu" class="lbl_keisatsu require done">警察署</label>
                                <input name="keisatsu" id="keisatsu" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_keisatsu_basho dlg_place cursor-pointer"></div>
                                <label for="keisatsu_tantosya" class="lbl_keisatsu_tantosya require done">担当者</label>
                                <input name="keisatsu_tantosya" id="keisatsu_tantosya" type="text" class="txt" value="" maxlength = "30"/>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="pickup_date" class="lbl_pickup_date done">お迎え予定時間</label>
                                <input name="pickup_date" id="pickup_date" type="text" class="txt ime-off" value="" maxlength = "10"/>
                                <div class="label dlg_date"></div>
                                <input name="pickup_time" id="pickup_time" type="text" class="txt ime-off" value="" maxlength = "5"/>
                                <div class="label dlg_time"></div>
                                <label for="dropoff" class="lbl_dropoff require done">安置先</label>
                                <input type="hidden" name="dropoff_type" id="dropoff_type" class="cls_dropoff_type"/>
                                <input name="dropoff_name" id="dropoff_name" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_dropoff_name cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="pickup_date2" class="lbl_pickup_date done">お迎え予定時間</label>
                                <input name="pickup_date2" id="pickup_date2" type="text" class="txt ime-off" value="" maxlength = "10"/>
                                <div class="label dlg_date"></div>
                                <input name="pickup_time2" id="pickup_time2" type="text" class="txt ime-off" value="" maxlength = "5"/>
                                <div class="label dlg_time"></div>
                                <label for="pickup" class="lbl_pickup done">お連れする場所</label>
                                <input type="hidden" name="pickup_type" id="pickup_type" class="cls_pickup_type"/>
                                <input name="pickup_name" id="pickup_name" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_pickup_name cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="pickup_date3" class="lbl_pickup_date done">お迎え予定時間</label>
                                <input name="pickup_date3" id="pickup_date3" type="text" class="txt ime-off" value="" maxlength = "10"/>
                                <div class="label dlg_date"></div>
                                <input name="pickup_time3" id="pickup_time3" type="text" class="txt ime-off" value="" maxlength = "5"/>
                                <div class="label dlg_time"></div>
                                <label for="pickup2" class="lbl_pickup done">お連れする場所2</label>
                                <input type="hidden" name="pickup_type2" id="pickup_type2" class="cls_pickup_type"/>
                                <input name="pickup_name2" id="pickup_name2" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_pickup_name2 cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="base_0">
                                <label for="renraku_name" class="lbl_name require done lbl_replace_kojin">連絡者のお名前</label>
                                <input name="renraku_name" id="renraku_name" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label lbl_name_sama">様</div>
                                <label for="renraku_zoku" class="lbl_renraku_zoku done">続柄</label>
                                <input type="hidden" name="renraku_zoku" id="renraku_zoku" class="cls_renraku_zoku"/>
                                <label for="renraku_tel" class="lbl_renraku_tel require done">携帯番号</label>
                                <input name="renraku_tel" id="renraku_tel" type="text" class="txt ime-off" value="" maxlength = "15" placeholder="000-0000-0000"/>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="iso_kbn1" class="lbl_iso_kbn require done">搬送者1</label>
                                <input type="hidden" name="iso_kbn1" id="iso_kbn1" class="iso_kbn cls_iso_knn1"/>
                                <input name="staff_3" id="staff_3" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff3 cursor-pointer"></div>
                                <input name="staff_3_2" id="staff_3_2" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff3_2 cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="iso_kbn2" class="lbl_iso_kbn done">搬送者2</label>
                                <input type="hidden" name="iso_kbn2" id="iso_kbn2" class="iso_kbn"/>
                                <input name="staff_4" id="staff_4" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff4 cursor-pointer"></div>
                                <input name="staff_4_2" id="staff_4_2" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff4_2 cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="iso_kbn3" class="lbl_iso_kbn done">搬送者3</label>
                                <input type="hidden" name="iso_kbn3" id="iso_kbn3" class="iso_kbn"/>
                                <input name="staff_5" id="staff_5" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff5 cursor-pointer"></div>
                                <input name="staff_5_2" id="staff_5_2" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff5_2 cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="iso_kbn4" class="lbl_iso_kbn done">搬送者4</label>
                                <input type="hidden" name="iso_kbn4" id="iso_kbn4" class="iso_kbn"/>
                                <input name="staff_6" id="staff_6" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff6 cursor-pointer"></div>
                                <input name="staff_6_2" id="staff_6_2" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff6_2 cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="iso_kbn5" class="lbl_iso_kbn done">搬送者5</label>
                                <input type="hidden" name="iso_kbn5" id="iso_kbn5" class="iso_kbn"/>
                                <input name="staff_7" id="staff_7" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff7 cursor-pointer"></div>
                                <input name="staff_7_2" id="staff_7_2" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                <div class="label dlg_staff dlg_staff7_2 cursor-pointer"></div>
                            </fieldset>
                            <fieldset class="base_0">
                                <label for="iso_ts" class="lbl_iso_date done">移送日時</label>
                                <input name="iso_ts" id="iso_ts" type="text" class="txt ime-off" value="" maxlength = "10"/>
                                <div class="label dlg_date"></div>
                                <input name="iso_time" id="iso_time" type="text" class="txt ime-off" value="" maxlength = "5"/>
                                <div class="label dlg_time"></div>
                            </fieldset>
                            <fieldset class="base_0">
                                <label for="jizen_soudan" class="lbl_jizen_soudan done">事前相談</label>
                                <input type="hidden" name="jizen_soudan" id="jizen_soudan" class="cls_jizen_soudan"/>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="seko_irai" class="lbl_name require lbl_seko_irai done">施行依頼</label>
                                <input type="hidden" name="seko_irai" id="seko_irai" class="cls_irai_kbn"/>
                                <input name="irai_detail" id="irai_detail" type="text" class="txt" value=""  maxlength = "30" disabled="disabled"/>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="shokai_kbn" class="lbl_shokai_kbn done">依頼区分</label>
                                <input type="hidden" name="shokai_kbn" id="shokai_kbn" class="cls_shokai_kbn"/>
                                <input name="shokai_kbn_detail" id="shokai_kbn_detail" type="text" class="txt" value=""  maxlength = "30" disabled="disabled"/>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="shokai_saki_shosai" class="lbl_shokai_saki_shosai done">紹介先詳細</label>
                                <label for="shokai_dantai" class="lbl_shokai_dantai done">団体名</label>
                                <input name="shokai_dantai_mei" id="shokai_dantai_mei" type="text" class="txt" value=""  maxlength = "30"/>
                                <label for="shokai_kojin" class="lbl_shokai_kojin done">個人名</label>
                                <input name="shokai_kojin_mei" id="shokai_kojin_mei" type="text" class="txt" value=""  maxlength = "30"/>
                            </fieldset>
                        </div>
                        
                                                <div id = "input-tab" class = "tab-contents off">
                            <fieldset class="person_1 radio_set">
                                <label for="name" class="lbl_name require lbl_replace_kojin done">故人お名前</label>
                                <input name="name" id="name" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label lbl_name_sama">様</div>
                                <input id = "name_clip" data-clip ="1" style="display: none" type=file accept="image/*">
                                <div class="label dlg_clip cursor-pointer"></div>
                                <div id = "name_clip_link" class="label"></div>
                                <div id = "name_clip_clear" class = ""></div>
                                <div class="lbl_sex subtitle require done">性別</div>
                                <span id="sex_set" class="radio_set">
                                    <label for="male" class="lbl_male">男性</label><input name="sex" class="radio_sex" id="male" type="radio" value="1" checked="checked" />
                                    <label for="female" class="lbl_female">女性</label><input name="sex" class="radio_sex" id="female" type="radio" value="2" />
                                </span>
                                <label for="spouse" class="lbl_spouse require done">配偶者</label>
                                <input type="hidden" name="spouse" id="spouse" class="cls_spouse"/>
                            </fieldset>
                            <fieldset class="person_2">
                                <label for="kana" class="lbl_kana require done">フリガナ</label>
                                <input name="kana" id="kana" type="text" class="txt" value="" maxlength = "30"/>
                                <label for="birthday_date" class="lbl_birthday require done">生年月日</label>
                                <input type="hidden" name="birthday_era" id="birthday_era" class="cls_birthday_era"/>
                                <input name="birthday_date" id="birthday_date" type="text" class="txt" value="" maxlength = "8" placeholder="00/00/00" />
                                                                <div class="label" id="age_at_death_pref">満</div>
                                <input name="age" id="age" type="text" class="txt" value="" maxlength = "3"  style="width: 3%;border-right: none;"/>
                                <div class="label" id="age_at_death_suf">歳</div>
                                                            </fieldset>
                            <fieldset class="address_1">
                                <div class="lbl_address subtitle require done">現住所</div>
                                <label for="zip_1" class="lbl_zip done">〒</label>
                                <input name="zip_1" id="zip_1" type="text" data-zip = "k1" class="txt zip_helper" value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="address_1_1" id="address_1_1" type="text" class="txt" value=""  maxlength = "30"/>
                                <div class="label dlg_map"></div>
                                <label for="tel_1" class="require done">TEL</label>
                                <input name="tel_1" id="tel_1" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                                <input name="address_1_2" id="address_1_2" type="text" class="txt" value=""  maxlength = "30"/>
                                <label for="head_1" class="require done">世帯主</label>
                                <input type="hidden" name="head_1" id="head_1" class="cls_head_1"/>
                            </fieldset>
                            <fieldset class="address_2">
                                <div class="lbl_address subtitle option">
                                    住民登録住所<br />
                                    <span class="radio_set">
                                        <input name="as_address_2" id="as_address_2" type="checkbox" value="1" />
                                        <label for="as_address_2" class="lbl_as_address">現住所に同じ</label>
                                    </span>
                                </div>
                                <label for="zip_2" class="lbl_zip done">〒</label>
                                <input name="zip_2" id="zip_2" type="text" data-zip = "k2" class="txt zip_helper" value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="address_2_1" id="address_2_1" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_map"></div>
                                <label for="tel_2" class="option">TEL</label>
                                <input name="tel_2" id="tel_2" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                                <input name="address_2_2" id="address_2_2" type="text" class="txt" value="" maxlength = "30"/>
                                <label for="head_2" class="option">世帯主</label>
                                <input type="hidden" name="head_2" id="head_2" class="cls_head_2"/>
                            </fieldset>
                            <fieldset class="address_3">
                                <div class="lbl_address subtitle option">本籍<br />
                                    <span class="radio_set">
                                        <input name="as_address_3" id="as_address_3" type="checkbox" value="1" />
                                        <label for="as_address_3" class="lbl_as_address">現住所に同じ</label>
                                    </span>
                                </div>
                                <label for="zip_3" class="lbl_zip done">〒</label>
                                <input name="zip_3" id="zip_3" type="text" data-zip = "k3" class="txt zip_helper" value="" maxlength = "10"/>
                                <div class="label dlg_zip cursor-pointer"></div>
                                <input name="address_3_1" id="address_3_1" type="text" class="txt" value="" maxlength = "30"/>
                                <div class="label dlg_map"></div>
                                <label for="head_3" class="option">筆頭者</label>
                                <input type="hidden" name="head_3" id="head_3" class="cls_head_3"/>
                                <input name="address_3_2" id="address_3_2" type="text" class="txt" value="" maxlength = "30"/>
                            </fieldset>
                                                        <fieldset class="funeral_1">
                                <label for="family_name" class="lbl_family_name require done">葬家</label>
                                <input name="family_name" id="family_name" type="text" class="txt" value=""  maxlength = "10"/>
                                <div class="label" id="lbl_family_name2">家</div>
                                <label for="family_name_kana" class="lbl_family_name_soke option require done">フリガナ</label>
                                <input name="family_name_kana" id="family_name_kana" type="text" class="txt" value="" maxlength = "12"/>
                                <label for="family_count" class="lbl_family_count require done">親族人数</label>
                                <input name="family_count" id="family_count" type="text" class="txt ime-off text-right" value=""  maxlength = "6"/>
                                <div class="label" id="lbl_family_name2">名</div>
                                <label for="family_count" class="lbl_family_count require done">会葬者</label>
                                <input name="kaiso_count" id="kaiso_count" type="text" class="txt ime-off text-right" value=""  maxlength = "6"/>
                                <div class="label" id="lbl_family_name2">名</div>
                                                            </fieldset>
                            <fieldset class="base_1">
                                <label for="funeral_style" class="lbl_funeral_style require done">形式</label>
                                <input type="hidden" name="funeral_style" id="funeral_style" class="cls_funeral_style"/>
                                <label for="syushi_cd" class="lbl_religion option require done">宗旨</label>
                                <input type="hidden" id="syushi_cd" name="syushi_cd" class="cls_syushi_cd"/>
                                <label for="syuha_cd" class="lbl_denomination require done">宗派</label>
                                <div id="syuha_nm_div">
                                    <input type="hidden" id="syuha_cd" name="syuha_cd" class="cls_syuha_cd"/>
                                    <input type="text" id="syuha_nm_other" name="syuha_nm_other" class="txt" maxlength="20"/>
                                </div>
                                <div class="label" id="syuha_knm"></div>
                                <input type="text" id="syuha_knm2" name="syuha_knm2" class="txt" maxlength="40"/>
                            </fieldset>
                            <fieldset class="base_1">
                                <label for="tera_shokai" class="option require done">導師紹介</label>
                                <input type="hidden" id="tera_shokai" name="tera_shokai" class="cls_tera_shokai"/>
                                <input name="ofuse" id="ofuse" type="text" class="txt" value="御布施"  maxlength = "10"/>
                                <input name="ofuse_prc" id="ofuse_prc" type="text" class="txt ime-off" value=""  maxlength = "10" onblur="$.msiJqlib.commaFilterTemp($(this));"/>
                                <label for="temple" class="lbl_temple require done">寺院名</label>
                                <input name="temple" id="temple" type="text" class="txt" value="" maxlength = "30" class="cls_temple" data-kind2 = "1" data-code = "jyusho_cd" data-name = "jyusho_nm" data-kname = "jyusho_knm" readonly="readonly"/>
                                <div class="label dlg_temple cursor-pointer"></div>
                                <div class="label" id="temple_knm"></div>

                            </fieldset>
                            <fieldset class="funeral_2">
                                <label for="otsutome" class="option require done">お勤め</label>
                                <input name="otsutome" id="otsutome" type="text" class="txt" value=""  maxlength = "30"/>
                                <label for="homyo_kaimyo" class="option require done lbl_homyo_kaimyo">法名・戒名</label>
                                <input name="homyo_kaimyo" id="homyo_kaimyo" type="text" class="txt" value=""  maxlength = "30"/>
                                <label for="ihai_kbn1" class="option require done lbl_ihai_kbn">位牌</label>
                                <input type="hidden" id="ihai_kbn1" name="ihai_kbn1" class="cls_ihai_kbn"/>
                                <input type="hidden" id="ihai_kbn2" name="ihai_kbn2" class="cls_ihai_kbn"/>
                            </fieldset>
                            <fieldset class="memo">
                                <label for="renraku_jiko" class="lbl_memo option">寺院連絡事項</label>
                                <textarea name="renraku_jiko" id="renraku_jiko" class="txt" cols="1" rows="10" maxlength = "512"></textarea>
                            </fieldset>
                            <fieldset class="memo">
                                <label for="memo" class="lbl_memo option">メモ（出棺経路・納骨・親族数など）</label>
                                <textarea name="memo" id="memo" class="txt" cols="1" rows="10" maxlength = "256"></textarea>
                            </fieldset>
                        </div>
                                                <div id = "infodate-tab" class = "tab-contents off">
                            <div id="infodate">
                            </div>
                            <div id="add_charge">
                                <fieldset class="base_1">
                                    <label for="tanto_cd7" class="lbl_tanto_cd7 done">通夜チーフ</label>
                                    <input name="tanto_cd7" id="tanto_cd7" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                    <div class="label dlg_staff dlg_tanto_cd7 cursor-pointer"></div>
                                </fieldset>
                                <fieldset class="base_1">
                                    <label for="tanto_cd8" class="lbl_tanto_cd8 done">通夜サブ</label>
                                    <input name="tanto_cd8" id="tanto_cd8" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                    <div class="label dlg_staff dlg_tanto_cd8 cursor-pointer"></div>
                                </fieldset>
                                <fieldset class="base_1">
                                    <label for="free11_code_cd" class="option require done lbl_free11_code_cd">通夜他</label>
                                    <input type="hidden" id="free11_code_cd" name="free11_code_cd" class="cls_free11_code_cd"/>
                                </fieldset>
                                <fieldset class="base_1">
                                    <label for="tanto_cd9" class="lbl_tanto_cd9 done">葬儀チーフ</label>
                                    <input name="tanto_cd9" id="tanto_cd9" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                    <div class="label dlg_staff dlg_tanto_cd9 cursor-pointer"></div>
                                </fieldset>
                                <fieldset class="base_1">
                                    <label for="tanto_cd10" class="lbl_tanto_cd10 done">葬儀サブ</label>
                                    <input name="tanto_cd10" id="tanto_cd10" type="text" class="txt" value="" maxlength = "20" readonly="readonly"/>
                                    <div class="label dlg_staff dlg_tanto_cd10 cursor-pointer"></div>
                                </fieldset>
                                <fieldset class="base_1">
                                    <label for="free12_code_cd" class="option require done lbl_free12_code_cd">葬儀他</label>
                                    <input type="hidden" id="free12_code_cd" name="free12_code_cd" class="cls_free12_code_cd"/>
                                </fieldset>
                                                                                            </div>
                        </div>
                                                <div id = "infochief-tab" class = "tab-contents off">
                            <div id="chief" class="info_area">
                                <h3 class = "lbl_replace_moshu">喪主</h3>
                                <fieldset class="person_1 radio_set">
                                    <label for="name" class="lbl_name require lbl_replace_moshu done">喪主お名前</label>
                                    <input name="name" id="name" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label lbl_name_sama">様</div>
                                    <input id = "name_clip"  data-clip ="2" style="display: none" type=file accept="image/*">
                                    <div class="label dlg_clip cursor-pointer"></div>
                                    <div id = "name_clip_link" class="label"></div>
                                    <div id = "name_clip_clear" class = ""></div>
                                    <label for="chief_relationship" class="lbl_relationship require done">続柄</label>
                                    <input type="hidden" name="s_chief_relationship" id="s_chief_relationship" class="cls_s_chief_relationship"/>
                                    <label for="chief_relationship2" class="lbl_relationship require done" style="font-size:10px;">喪主様からみた故人様の続柄</label>
                                    <input type="hidden" name="s_chief_relationship2" id="s_chief_relationship2" class="cls_s_chief_relationship"/>
                                </fieldset>
                                <fieldset class="person_2">
                                    <label for="kana" class="lbl_kana require done">フリガナ</label>
                                    <input name="kana" id="kana" type="text" class="txt" value="" maxlength = "30"/>
                                    <label for="birthday_date" class="lbl_birthday require done">生年月日</label>
                                    <input type="hidden" name="birthday_era" id="birthday_era" class="cls_birthday_era"/>
                                    <input name="birthday_date" id="birthday_date" type="text" class="txt" value="" maxlength = "8" placeholder="00/00/00"/>
                                    <div class="label" id="age"></div>
                                </fieldset>
                                <fieldset class="address_1">
                                    <div class="lbl_address subtitle option">現住所<br />
                                        <span class="radio_set">
                                            <input name="as_address_4" id="as_address_4" type="checkbox" value="1" />
                                            <label for="as_address_4" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                                        </span>
                                    </div>
                                    <label for="zip_1" class="lbl_zip done">〒</label>
                                    <input name="zip_1" id="zip_1" type="text" data-zip = "m1" class="txt zip_helper" value="" maxlength = "10"/>
                                    <div class="label dlg_zip cursor-pointer"></div>
                                    <input name="address_1_1" id="address_1_1" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_map"></div>
                                    <label for="tel_1" class="option require done">TEL</label>
                                    <input name="tel_1" id="tel_1" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                                    <input name="address_1_2" id="address_1_2" type="text" class="txt" value="" maxlength = "30"/>
                                    <label for="mobile_tel_1" class="option">携帯</label>
                                    <input name="mobile_tel_1" id="mobile_tel_1" type="text" class="txt ime-off" value="" maxlength = "15" placeholder="000-0000-0000"/>
                                </fieldset>
                                <fieldset class="address_2">
                                    <div class="lbl_address subtitle option sub2">住民登録住所<br />
                                        <span class="radio_set">
                                            <input name="as_address_5" id="as_address_5" type="checkbox" value="1" />
                                            <label for="as_address_5" class="lbl_as_address lbl_as_address_5 lbl_replace_kojin">故人に同じ</label>
                                                                                                                                </span>
                                    </div>
                                    <label for="zip_2" class="lbl_zip done">〒</label>
                                    <input name="zip_2" id="zip_2" type="text" data-zip = "m2" class="txt zip_helper"  value="" maxlength = "10"/>
                                    <div class="label dlg_zip cursor-pointer"></div>
                                    <input name="address_2_1" id="address_2_1" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_map"></div>
                                    <label for="tel_2" class="option">TEL</label>
                                    <input name="tel_2" id="tel_2" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                                    <input name="address_2_2" id="address_2_2" type="text" class="txt" value="" maxlength = "30"/>
                                </fieldset>
                                <fieldset class="address_3" style="display:none"><!-- cstm:sano display:none -->
                                    <div class="lbl_address subtitle option">本籍<br />
                                        <span class="radio_set">
                                            <input name="as_address_6" id="as_address_6" type="checkbox" value="1" />
                                            <label for="as_address_6" class="lbl_as_address lbl_replace_kojin">故人に同じ</label>
                                        </span>
                                    </div>
                                    <label for="zip_3" class="lbl_zip done">〒</label>
                                    <input name="zip_3" id="zip_3" type="text" data-zip = "m3" class="txt zip_helper"  value="" maxlength = "10"/>
                                    <div class="label dlg_zip cursor-pointer"></div>
                                    <input name="address_3_1" id="address_3_1" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_map"></div>
                                    <input name="address_3_2" id="address_3_2" type="text" class="txt" value="" maxlength = "30"/>
                                </fieldset>
                                <fieldset class="business_1">
                                    <label for="company" class="option">勤務先</label>
                                    <input type="hidden" name="employee" id="employee" class="cls_employee"/>
                                    <input name="company" id="company" type="text" class="txt" value="" maxlength = "30"/>
                                    <label for="company_tel" class="option">TEL</label>
                                    <input name="company_tel" id="company_tel" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                                </fieldset>
                                <fieldset class="business_2">
                                    <label for="position" class="option">役職／職種</label>
                                    <input name="position" id="position" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_position"></div>
                                    <label for="company_fax" class="option">FAX</label>
                                    <input name="company_fax" id="company_fax" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                                </fieldset>
                            </div>
                            <div id="bill" class="info_area">
                                <h3>請求先</h3>
                                <span class="radio_set">
                                    <input name="as_chief" id="as_chief" type="checkbox" value="1" />
                                    <label for="as_chief" class="lbl_as_chief lbl_replace_moshu">喪主に同じ</label>
                                </span>
                                <fieldset class="person_1 radio_set">
                                    <label for="name" class="lbl_name require done">御請求先名</label>
                                    <input name="name" id="name" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label lbl_name_sama">様</div>
                                    <input id = "name_clip"  data-clip ="3" style="display: none" type=file accept="image/*">
                                    <div class="label dlg_clip cursor-pointer"></div>
                                    <div id = "name_clip_link" class="label"></div>
                                    <div id = "name_clip_clear" class = ""></div>
                                    <label for="bill_relationship" class="lbl_bill_relationship require done lbl_replace_moshu">喪主との関係</label>
                                    <input type="hidden" name="s_bill_relationship" id="s_bill_relationship" class="cls_s_bill_relationship"/>
                                    <input name="bill_relationship_name" id="bill_relationship_name" type="text" class="txt" value="" maxlength = "10"/>
                                    <div class="label dlg_relationship"></div>
                                </fieldset>
                                <fieldset class="person_2">
                                    <label for="kana" class="lbl_kana require done">フリガナ</label>
                                    <input name="kana" id="kana" type="text" class="txt" value="" maxlength = "30"/>
                                    <label for="bill_relationship2" class="lbl_bill_relationship require done lbl_replace_kojin font_12">故人様からみた続柄</label>
                                    <input type="hidden" name="s_bill_relationship2" id="s_bill_relationship2" class="cls_s_bill_relationship"/>
                                </fieldset>
                                <fieldset class="bill_address">
                                    <div class="lbl_address subtitle require done">現住所</div>
                                    <label for="zip_4" class="lbl_zip done">〒</label>
                                    <input name="zip_4" id="zip_4" type="text" data-zip = "s1" class="txt zip_helper"  value="" maxlength = "10"/>
                                    <div class="label dlg_zip cursor-pointer"></div>
                                    <input name="address_4_1" id="address_4_1" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_map"></div>
                                    <label for="tel_4" class="require done">TEL</label>
                                    <input name="tel_4" id="tel_4" type="text" class="txt" value="" maxlength = "15" placeholder="************"/>
                                    <input name="address_4_2" id="address_4_2" type="text" class="txt" value="" maxlength = "30"/>
                                    <label for="mobile_tel_2" class="option done">携帯</label>
                                    <input name="mobile_tel_2" id="mobile_tel_2" type="text" class="txt ime-off" value="" maxlength = "15" placeholder="000-0000-0000"/>
                                </fieldset>
                                <fieldset class="memo">
                                    <label for="memo" class="lbl_memo option">備考</label>
                                    <input name="memo" id="memo" type="text" class="txt" value="" maxlength = "60"/>
                                </fieldset>
                            </div>
                        </div>
                                                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "juchu/customerinfo/gojokai_kanyu_tmpl.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                                                <div id = "infomisc-tab" class = "tab-contents off">
                            <div id="misc_1" class="misc">
                                <h3>診断書</h3>
                                <fieldset class="karte_1 radio_set">
                                    <div class="lbl_publish subtitle">発行</div>
                                    <span id="publish_set">
                                        <label for="unfinished" class="lbl_unfinished">未</label><input name="publish" id="unfinished" type="radio" value="2" checked="checked"/>
                                        <label for="finished" class="lbl_finished">済</label><input name="publish" id="finished" type="radio" value="1"  />
                                    </span>
                                    <label for="steps" class="lbl_steps">手続き</label>
                                    <input type="hidden" name="steps" id="steps" class="cls_steps"/>
                                </fieldset>
                                <fieldset class="karte_2">
                                    <label for="datetime" class="lbl_datetime option">発行予定日時</label>
                                    <input name="date" id="date" type="text" class="txt" value="" maxlength = "10"/>
                                    <div class="label dlg_date"></div>
                                    <input name="time" id="time" type="text" class="txt" value="" maxlength = "5"/>
                                    <div class="label dlg_time"></div>
                                    <label for="copy" class="lbl_copy option">コピー</label>
                                    <input type="hidden" name="copys" id="copys" class="cls_copys"/>
                                </fieldset>
                            </div>
                                                        <div id="misc_5" class="misc" style="border-bottom: 1px solid #7FAACD;">
                                <h3>お預り品</h3>
                                <fieldset class="deposit_1">
                                    <label for="certificate" class="lbl_certificate option">死亡診断書</label>
                                    <input type="hidden" name="certificate" id="certificate" class="cls_certificate"/>
                                    <div class="lbl_stamp subtitle option">印鑑</div>
                                    <span id="stamp_set" class="radio_set">
                                        <input name="stamp" id="stamp" type="checkbox" value="1" /><label for="stamp" class="lbl_stamp_check"></label>
                                    </span>
                                </fieldset>
                                <fieldset class="deposit_1">
                                    <label for="portrait" class="lbl_portrait option">御写真</label>
                                    <input type="hidden" name="portrait" id="portrait" class="cls_portrait"/>
                                    <div id="portrait_up" class="file_upload">
                                        <input name="file" id = "file_1" class = "file_up" style="display: none" type = 'file' accept="image/*">
                                        <div id = "file_link_portrait" class="file_link label">
                                            <div class = "file_name"></div>
                                            <div id = "file_clear_1" class = "file_clear"></div>
                                        </div>
                                        <div id = "file_clip_portrait" class="file_clip label dlg_clip cursor-pointer"></div>
                                    </div>
                                </fieldset>
                                <fieldset class="deposit_4">
                                    <label for="membership" class="lbl_membership option">その他</label>
                                    <input name="membership" id="membership" type="text" class="txt" value="" maxlength = "30" />
                                </fieldset>
                            </div>
                            <div id="misc_6" class="misc">
                                <h3>依頼確認</h3>
                                <fieldset class="base_1">
                                    <label for="irai_kakunin_1" class="lbl_irai_kakunin_1 option">各方面よりお問合せのあった場合、お答えして良い項目は</label>
                                    <input type="hidden" id="irai_kakunin_1" class="cls_irai_kakunin_1"/>
                                </fieldset>
                                <fieldset class="base_1">
                                    <label for="irai_kakunin_2" class="lbl_irai_kakunin_2 option">各方面よりお供物(生花等)のご注文があった場合、承りは</label>
                                    <input type="hidden" id="irai_kakunin_2" class="cls_irai_kakunin_2"/>
                                </fieldset>
                                <fieldset class="">
                                    <label for="jyokenka" class="lbl_jyokenka option">条件付で可の条件</label>
                                    <input name="jyokenka" id="jyokenka" type="text" class="txt" value="" maxlength = "40"/>
                                </fieldset>
                                <fieldset class="">
                                    <label for="t_tokijiko" class="lbl_t_tokijiko option">特記事項</label>
                                    <input name="t_tokijiko" id="t_tokijiko" type="text" class="txt" value="" maxlength = "40"/>
                                </fieldset>
                                <fieldset class="base_1">
                                    <label for="irai_kakunin_3" class="lbl_irai_kakunin_3 option">ご葬儀後のお仏壇・お位牌や墓地・墓石・手続きのご案内について</label>
                                </fieldset>
                                <fieldset class="base_1">
                                    <label class="lbl_irai_kakunin_3_1 option">(株)はせがわ</label>   
                                    <span class="radio_set" data-ref_attr = 'free3_code_cd'>
                                        <label for="annai_1_hitsuyo" class="lbl_select_kbn lbl_irai_kakunin_set width18">後日専門スタッフより連絡が入ります</label><input name="annai_1_kbn" id="annai_1_hitsuyo" type="radio" value="0" class="annai_check_set"/>
                                        <label for="annai_1_huyo" class="lbl_select_kbn lbl_irai_kakunin_set">不要</label><input name="annai_1_kbn" id="annai_1_huyo" type="radio" value="1"  class="annai_check_set" />
                                    </span> 
                                    <label class="lbl_irai_kakunin_3_2 option">(株)阿部石材店</label> 
                                    <span class="radio_set" data-ref_attr = 'free4_code_cd'>  
                                        <label for="annai_2_hitsuyo" class="lbl_select_kbn lbl_irai_kakunin_set">必要</label><input name="annai_2_kbn" id="annai_2_hitsuyo" type="radio" value="0" class="annai_check_set"/>
                                        <label for="annai_2_huyo" class="lbl_select_kbn lbl_irai_kakunin_set">不要</label><input name="annai_2_kbn" id="annai_2_huyo" type="radio" value="1"  class="annai_check_set" />
                                    </span>            
                                </fieldset>
                                <fieldset class="base_1">
                                    <label class="lbl_irai_kakunin_3_1 option">手続</label>   
                                    <span class="radio_set" data-ref_attr = 'free5_code_cd'>
                                        <label for="annai_3_hitsuyo" class="lbl_select_kbn lbl_irai_kakunin_set width18">後日専門スタッフより連絡が入ります</label><input name="annai_3_kbn" id="annai_3_hitsuyo" type="radio" value="0" class="annai_check_set"/>
                                        <label for="annai_3_huyo" class="lbl_select_kbn lbl_irai_kakunin_set">不要</label><input name="annai_3_kbn" id="annai_3_huyo" type="radio" value="1"  class="annai_check_set" />
                                    </span> 
                                    <label class="lbl_irai_kakunin_3_2 option">川崎清風霊園</label> 
                                    <span class="radio_set" data-ref_attr = 'free6_code_cd'>  
                                        <label for="annai_4_hitsuyo" class="lbl_select_kbn lbl_irai_kakunin_set">必要</label><input name="annai_4_kbn" id="annai_4_hitsuyo" type="radio" value="0" class="annai_check_set"/>
                                        <label for="annai_4_huyo" class="lbl_select_kbn lbl_irai_kakunin_set">不要</label><input name="annai_4_kbn" id="annai_4_huyo" type="radio" value="1"  class="annai_check_set" />
                                    </span>            
                                </fieldset>
                                <fieldset class="base_1">
                                    <label class="lbl_irai_kakunin_3_1 option">ギフトカタログ</label>   
                                    <span class="radio_set" data-ref_attr = 'free7_code_cd'>
                                        <label for="annai_5_0" class="lbl_select_kbn lbl_irai_kakunin_set">渡した</label><input name="annai_5_kbn" id="annai_5_0" type="radio" value="0" class="annai_check_set"/>
                                        <label for="annai_5_1" class="lbl_select_kbn lbl_irai_kakunin_set">郵送希望</label><input name="annai_5_kbn" id="annai_5_1" type="radio" value="1"  class="annai_check_set" />
                                        <label for="annai_5_2" class="lbl_select_kbn lbl_irai_kakunin_set">不要</label><input name="annai_5_kbn" id="annai_5_2" type="radio" value="2"  class="annai_check_set" />
                                    </span> 
                                    <label class="lbl_irai_kakunin_3_2 option">石蔵</label> 
                                    <span class="radio_set" data-ref_attr = 'free9_code_cd'>  
                                        <label for="annai_6_hitsuyo" class="lbl_select_kbn lbl_irai_kakunin_set">必要</label><input name="annai_6_kbn" id="annai_6_hitsuyo" type="radio" value="0" class="annai_check_set"/>
                                        <label for="annai_6_huyo" class="lbl_select_kbn lbl_irai_kakunin_set">不要</label><input name="annai_6_kbn" id="annai_6_huyo" type="radio" value="1"  class="annai_check_set" />
                                    </span>            
                                </fieldset>
                            </div>
                            <div id="misc_7" class="misc">
                                <h3>駐車場警備</h3>
                                <fieldset class="base_1">
                                    <label for="chusya_hachu" class="lbl_chusya_hachu option">発注先</label>
                                    <input type="hidden" id="chusya_hachu" class="cls_chusya_hachu"/>
                                </fieldset>
                            </div>

                            <div id="misc_4" class="misc" style="display:none">
                                <h3>隣組</h3>
                                <fieldset class="neighborhood_1">
                                    <label for="association" class="lbl_association option">町内会名</label>
                                    <input name="association" id="association" type="text" class="txt" value="" maxlength = "30"/>
                                    <div class="label dlg_association"></div>
                                </fieldset>
                                <fieldset class="neighborhood_2">
                                    <label for="leader" class="lbl_leader option">隣組長様</label>
                                    <input name="leader" id="leader" type="text" class="txt" value="" maxlength = "30"/>
                                    <input type="text" name="doors" id="doors" class="txt cls_doors" maxlength = "2"/>
                                    <div class="label lbl_name_sama">軒</div>
                                    <input type="text" name="persons" id="persons" class="txt cls_persons" maxlength = "2"/>
                                    <div class="label lbl_name_sama">名</div>
                                </fieldset>
                                <fieldset class="neighborhood_3">
                                    <label for="area" id="lbl_area" class="lbl_area option">地区</label>
                                    <input type="hidden" id="area" class="cls_area"/>
                                    <label for="group" id="lbl_group" class="lbl_group option">組</label>
                                    <input type="hidden" id="group" class="cls_group"/>
                                </fieldset>
                            </div>
                            <div id="misc_5" class="misc" style="display:none">
                                <h3>目録手配</h3>
                                <fieldset class="catalog_1 radio_set">
                                    <div class="lbl_catalog subtitle option">依頼</div>
                                    <span id="catalog_set">
                                        <label for="catalog_yes" class="lbl_catalog_check">有</label><input name="catalog" id="catalog_yes" type="radio" value="1"  />
                                        <label for="catalog_no" class="lbl_catalog_check">無</label><input name="catalog" id="catalog_no" type="radio" value="2" checked="checked" />
                                    </span>
                                </fieldset>
                                <fieldset class="catalog_2">
                                    <label for="catalog_area" class="lbl_catalog_area option">対象エリア</label>
                                    <input type="hidden" name="catalog_area" id="catalog_area" class="cls_catalog_area"/>
                                </fieldset>
                                <fieldset class="catalog_3">
                                    <label for="catalog_item" class="lbl_catalog_item option">手配品目</label>
                                    <input type="hidden" name="catalog_item" id="catalog_item" class="cls_catalog_item"/>
                                </fieldset>
                                <fieldset class="catalog_4">
                                    <label for="catalog_datetime" class="lbl_datetime option">搬入日時</label>
                                    <input name="catalog_date" id="catalog_date" type="text" class="txt ime-off" value="" />
                                    <div class="label dlg_date"></div>
                                    <input name="catalog_time_from" id="catalog_time_from" type="text" class="txt ime-off" value="" />
                                    <div class="label dlg_time" id="lbl_dlg_time_from"></div>
                                    <div class="label lbl_catalog_time">〜</div>
                                    <input name="catalog_time_to" id="catalog_time_to" type="text" class="txt ime-off" value="" />
                                    <div class="label dlg_time"></div>
                                </fieldset>
                                <fieldset class="catalog_5">
                                    <label for="delivery" class="lbl_catalog_place">搬入場所</label>
                                    <input type="hidden" id="delivery" class="cls_delivery"/>
                                    <input name="catalog_place" id="catalog_place" type="text" class="txt" value="" maxlength="30"/>
                                                                    </fieldset>
                                <fieldset class="catalog_6">
                                    <label for="delivery_memo_cd" class="lbl_catalog_memo">備考</label>
                                    <input type="hidden" id="delivery_memo_cd" class="cls_delivery_memo_cd"/>
                                </fieldset>
                                <fieldset class="catalog_7 foot-margin-bottom">
                                    <textarea name="delivery_memo" id="delivery_memo" class="txt" cols="1" rows="10" maxlength = "256"></textarea>
                                </fieldset>
                            </div>
                        </div>
                                                <?php if (((is_array($_tmp=$this->_tpl_vars['kashidashi_disp'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')) == 1): ?>
                            <div id = "inforental-tab" class = "tab-contents off">
                                <div id="items">
                                    <ul>
                                        <div id="kashidasi-bihin">
                                        </div>
                                        <li class="add"><a href="javascript:void(0)" id = "item_add" class="item_ad">+</a></li>
                                    </ul>
                                </div>
                            </div>
                        <?php endif; ?>

                                                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "juchu/customerinfo/report_tmpl.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    </div>
                    <div class="buttons">
                        <input type="button" name="btn_save" id="btn_save" value="保存" />
                        <input type="button" name="btn_print" id="btn_print" value="印刷" />
                        <input type="button" name="btn_delete" id="btn_delete" value="削除" />
                        <input type="button" name="btn_cancel" id="btn_cancel" value="取消" />
                        <input type="button" name="btn_print_seko" id="btn_print_seko" value="施行情報印刷" />
                        <input type="button" name="btn_seko_copy" id="btn_seko_copy" value="葬儀施行コピー" />
                        <input type="button" name="btn_seko_copy4" id="btn_seko_copy4" value="葬儀施行コピー" />
                        <input type="button" name="btn_seko_copy3" id="btn_seko_copy3" value="事前相談にコピー" />
                        <input type="button" name="btn_seko_copy2" id="btn_seko_copy2" value="サンプルにコピー" />
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<?php echo '
    <!-- 日程タブ 亡日テンプレート -->
    <script type="text/template" id="tmpl-nitei-1">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date nitei_st_date" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time nitei_st_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value="" data-kind2 = "5" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
    </script>
    <!-- 日程タブ 湯灌テンプレート -->
    <script type="text/template" id="tmpl-nitei-2">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date nitei_st_date" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time nitei_st_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt place basho_nm" value="" maxlength = "30" />
        <div class="label dlg_place cursor-pointer disabled"></div>
    </script>
    <!-- 日程タブ 入棺テンプレート -->
    <script type="text/template" id="tmpl-nitei-3">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date nitei_st_date" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time nitei_st_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value=""  maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
    </script>
    <!-- 日程タブ 通夜テンプレート -->
    <script type="text/template" id="tmpl-nitei-4">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date nitei_st_date" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time nitei_st_time tuya_st_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt time nitei_ed_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value="" maxlength = "30" readonly="readonly" style="width: 16%;"/>
        <div class="label dlg_place cursor-pointer"></div>
        <input type="hidden" class="cls_sikijo_cd kaso_sikijo"/>
        <input type="text" class="txt sikijo_nm_other" maxlength="10"/>
    </script>
    <!-- 日程タブ 出棺テンプレート -->
    <script type="text/template" id="tmpl-nitei-5">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date nitei_st_date" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time nitei_st_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value="" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
    </script>
    <!-- 日程タブ 火葬テンプレート -->
    <script type="text/template" id="tmpl-nitei-6">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date nitei_st_date" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time nitei_st_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt place basho_nm" value="" data-kind2 = "3" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra lbl_nitei_other">火葬経</label>
        <input type="hidden" class="sutra kaso_kyo"/>
    </script>
    <!-- 日程タブ 葬儀テンプレート -->
    <script type="text/template" id="tmpl-nitei-7">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date nitei_st_date" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time nitei_st_time sogi_st_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="text" class="txt time nitei_ed_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value=""  data-kind2 = "2" maxlength = "30" readonly="readonly" style="width: 16%;"/>
        <div class="label dlg_place cursor-pointer"></div>
        <input type="hidden" class="cls_sikijo_cd kaso_sikijo"/>
        <input type="text" class="txt sikijo_nm_other" maxlength="10"/>
        <label class="lbl_sutra lbl_nitei_other">導師</label>
        <input type="hidden" class="sutra shishikisha_ninzu"/>
    </script>
    <!-- 日程タブ 壇払いテンプレート -->
    <script type="text/template" id="tmpl-nitei-8">
        <label class="lbl_date done"><%=ts_based_nm%></label>
        <input type="text" class="txt date nitei_date nitei_st_date" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <input type="text" class="txt time nitei_time nitei_st_time" value="" maxlength = "5"/>
        <div class="label dlg_time"></div>
        <input type="hidden" class="nitei_spot_cd"/>
        <input type="text" class="txt place basho_nm haskbn" value="" data-kind2 = "2" maxlength = "30" readonly="readonly"/>
        <div class="label dlg_place cursor-pointer"></div>
        <label class="lbl_sutra lbl_nitei_other">法事</label>
        <input type="hidden" class="sutra nanoka"/>
    </script>
    <!-- 貸出備品テンプレート -->
    <script type="text/template" id="tmpl-kashidasi-bihin">
        <div class = "delete"></div>
        <% if (nm_input_kbn == \'2\' || nm_input_kbn == \'4\') { %>
        <input class="name nm_input" maxlength = "30"></input>
        <% } else { %>
        <span class="name"></span>
        <% }%>
        <span class="num">0</span>
        <span class="minus">-</span>
        <span class="plus">+</span>
        <fieldset class="f_return">
        <label class="lbl_return">回収<br />予定</label>
        <input class="return txt" />
        </fieldset>
    </script>
'; ?>


<script id="data-customerinfo" type="application/json">
    <?php echo $this->_tpl_vars['customerinfo_json']; ?>

</script>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "fdn_footer_std.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
