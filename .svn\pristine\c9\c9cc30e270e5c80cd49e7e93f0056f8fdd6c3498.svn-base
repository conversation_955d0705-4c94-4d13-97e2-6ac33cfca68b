<?php
  /**
   * DataMapper_SekoNitei
   *
   * PDF出力 施行日程法事 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mogi
   * @since      2017/12/19
   * @filesource 
   */

  /**
   * PDF出力 施行日程法事 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mogi
   * @since      2017/12/19
   */
class DataMapper_SekoNiteiHouji extends DataMapper_Abstract
{
    /**
     * PDF出力 施行日程法事 取得
     *
     * <AUTHOR> Mogi
     * @since      2017/12/19
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no, T.disp_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
SELECT  nitei.seko_no -- 施行番号
        ,nitei.nitei_kbn -- 日程区分
        ,nitei_cn.kbn_value_snm AS nitei_kbn_nm -- 日程区分名
        ,nitei.disp_no -- 表示順序
        ,nitei.nitei_ymd -- 日程（日時）
        ,nitei.nitei_ed_ymd -- 日程（終了日時）
        ,nitei.spot_code_kbn  -- 場所区分コード 区分
        ,nitei.spot_cd -- 場所区分コード
        ,nitei.basho_kbn  -- 場所区分
        ,nitei.basho_cd -- 場所コード
        ,nitei.basho_nm  -- 場所名
        ,nitei.free1_code_cd  -- フリー１区分コード
        ,nitei.free2_code_cd  -- フリー２区分コード
        ,nitei.free3_code_cd  -- フリー３区分コード
        ,nitei.free4_code_cd  -- フリー４区分コード
        ,nitei.free5_code_cd  -- フリー５区分コード
        ,nitei.free1_kbn  -- フリー１区分
        ,nitei.free2_kbn  -- フリー２区分
        ,nitei.free3_kbn  -- フリー３区分
        ,nitei.free3_kbn    AS   time_inp_kbn -- 時間入力区分 2017/10/03 ADD Kayo
        ,nitei.free4_kbn  -- フリー４区分
        ,nitei.free5_kbn  -- フリー５区分
        ,nitei.v_free1  -- 文字フリー項目１
        ,nitei.v_free2  -- 文字フリー項目２
        ,nitei.v_free3  -- 文字フリー項目３
        ,nitei.v_free4  -- 文字フリー項目４
        ,nitei.v_free5  -- 文字フリー項目５
        ,to_char(nitei.nitei_ymd, 'HH24:MI')    AS nitei_time
        ,to_char(nitei.nitei_ed_ymd, 'HH24:MI') AS nitei_ed_time
        ,to_char(nitei.nitei_ymd, 'YYYY/MM/DD') AS nitei_time_date
  FROM seko_nitei_houji nitei
  LEFT JOIN code_nm_mst nitei_cn 
    ON nitei_cn.code_kbn = '8140' -- 日程区分
	AND nitei.nitei_kbn = nitei_cn.kbn_value_cd_num
	AND nitei_cn.delete_flg = 0
 WHERE nitei.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
}
