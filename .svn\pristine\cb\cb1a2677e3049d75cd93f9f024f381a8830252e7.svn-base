
<?php

/**
 * Saiken_RyoshulistController
 *
 * 領収証発行一覧 コントローラクラス
 *
 * @category   App
 * @package    controllers\Saiken
 * <AUTHOR> Sugiyama
 * @since      2020/12/xx
 * @filesource 
 */

/**
 *
 * 領収証発行一覧 コントローラクラス
 *
 * @category   App
 * @package    controllers\Saiken
 * <AUTHOR> Sugiyama
 * @since      2020/12/xx
 */
class Saiken_RyoshulistController extends Msi_Zend_Controller_Action {
    /**
     * index アクション
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx
     */
    public function indexAction() {
        $this->_params = $params = Msi_Sys_Utils::webInputs();
        
        if (isset($this->_params['readonly']) && $this->_params['readonly']) {
            $this->_readonly = true;
        } else {
            $this->_readonly = Msi_Sys_Utils::isReadOnlyCtxt();
        }
        // 参照専用
        if ($this->_readonly) {
            $this->view->ctxt_readonly = 'my-ctxt-readonly';
        }
        // ログイン部門コード取得
        $s_bumon_cd = App_Utils::getTantoBumonCd();
        $s_bumon_nm = null;
        // 部門参照権限のリストを取得
        $oya_bumon  = Logic_Cale_ComLogic::get_bumon2Bylogin();
        $not_bumon_flg = true;
        foreach ($oya_bumon as $bumon) {
            if($s_bumon_cd == $bumon['id']) {
                $not_bumon_flg = false;
            }
        }
        // 該当する部門コードが無い場合
        if($not_bumon_flg){
            foreach ($oya_bumon as $bumon) {
                // 先頭の部門コードを設定
                $s_bumon_cd = $bumon['id'];
                break;
            }
        }
        $s_seko_ymd_from = date('Y/m/01', strtotime('-1 month'));   //先月
        // 初期値
        $data = array( 
                'dataApp'  => array(
                    's_bumon_cd'          => $s_bumon_cd,
                    's_bumon_nm'          => $s_bumon_nm,
                    's_ryosyu_prt_status' => 0,
                    's_seko_ymd_from'     => $s_seko_ymd_from
                ),
                'dataKbns' => array(
                    'ryosyu_prt_status'   => $this->getCodeKbn('8523'),  // 領収証出力状況
                    'ryosyu_irai_kbn'     => $this->getCodeKbn('8524'),  // 領収証依頼区分
                    'ryosyu_irai_kbn2'    => $this->getCodeKbn2('8524'), // 領収証依頼区分（検索結果用）
                    'ryosyu_prt_fuka_kbn' => $this->getCodeKbn('8525'),  // 領収書出力不可区分
                    'payment_kbn'         => $this->getCodeKbn('9757'),  // 入金方法
                    'moushi_kbn'          => $this->getCodeKbn('8527'),  // 申込区分
                    'oya_bumon'           => $oya_bumon,                 // 会社(親部門)
                ),
                'dataCol'  => array()
            );
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
        $this->view->page_title = '領収証発行一覧';
        
        App_Smarty::pushCssFile(['app/saiken.ryoshulist.css']);
        App_Smarty::pushJsFile(['app/saiken.ryoshulist.js']);
    }
    /**
     * 
     * コード区分取得
     * 
     * @param type $code_kbn
     */
    private function getCodeKbn($code_kbn){
        $db = Msi_Sys_DbManager::getMyDb();
        $select = DataMapper_CodeNmMst::find($db, array('code_kbn' => $code_kbn));
        return $select;
    }
    /**
     * 
     * コード区分取得（検索結果用）
     * 
     * @param type $code_kbn
     */
    private function getCodeKbn2($code_kbn){
        $db = Msi_Sys_DbManager::getMyDb();
        $cond = array(
              'code_kbn' => $code_kbn
            , '__raw1'   => "T.kbn_value_cd_num <> 3"
        );
        $select = DataMapper_CodeNmMst::find($db, $cond);
        return $select;
    }
    /**
     * 
     * コード区分値名取得
     * 
     * @param type $code_kbn
     * @param type $kbn_value_cd_num
     */
    private function getCodeKbnValueLnm($code_kbn, $kbn_value_cd_num){
        $db = Msi_Sys_DbManager::getMyDb();
        $select = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => $code_kbn, 'kbn_value_cd_num' => $kbn_value_cd_num));
        if(Msi_Sys_Utils::myCount($select) > 0){
            return $select['kbn_value_lnm'];
        }
        return "";
    }
    /**
     * 
     * 検索結果表示 アクション
     *
     * <AUTHOR> Sugiyama
     * @since 2020/12/xx
     */
    public function searchAction() {
        try {
            $params = Msi_Sys_Utils::webInputs();
            $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
            $db = Msi_Sys_DbManager::getMyDb();
            $limit  = Msi_Sys_Utils::easyGetVar($params , 'limit' , 'DIGIT', 300);
            $offset = Msi_Sys_Utils::easyGetVar($dataApp, 'offset', 'DIGIT', 0);
            $cond = array(
                '__etc_limit'  => $limit + 1,
                '__etc_offset' => $offset,
                '__raw_1'      => " T.moushi_kbn IN (1, 2, 3, 4, 7, 8, 9)",
            );
            $bumon_cd          = Msi_Sys_Utils::easyGetVar($dataApp, 's_bumon_cd');          // 部門コード
            $seko_tanto_cd     = Msi_Sys_Utils::easyGetVar($dataApp, 's_seko_tanto_cd');     // 施行担当者コード
            $ryosyu_prt_status = Msi_Sys_Utils::easyGetVar($dataApp, 's_ryosyu_prt_status'); // 領収証出力状況
            $pay_method_cd     = Msi_Sys_Utils::easyGetVar($dataApp, 's_pay_method_cd');     // 入金方法
            $ryosyu_irai_kbn   = Msi_Sys_Utils::easyGetVar($dataApp, 's_ryosyu_irai_kbn');   // 領収証依頼区分
            $nyukin_ymd_from   = Msi_Sys_Utils::easyGetVar($dataApp, 's_nyukin_ymd_from');   // 入金日開始
            $nyukin_ymd_to     = Msi_Sys_Utils::easyGetVar($dataApp, 's_nyukin_ymd_to');     // 入金日終了
            $nyukin_den_no     = Msi_Sys_Utils::easyGetVar($dataApp, 's_nyukin_den_no');     // 入金日終了
            $moushi_kbn        = Msi_Sys_Utils::easyGetVar($dataApp, 's_moushi_kbn');        // 申込区分
            $seko_no           = Msi_Sys_Utils::easyGetVar($dataApp, 's_seko_no');           // 施行番号
            $juchu_den_no      = Msi_Sys_Utils::easyGetVar($dataApp, 's_juchu_den_no');      // 受注伝票No
            $seikyu_den_no     = Msi_Sys_Utils::easyGetVar($dataApp, 's_seikyu_no');         // 請求No
            $sekyu_nm          = Msi_Sys_Utils::easyGetVar($dataApp, 's_seikyu_nm');         // 請求先名
            $seko_ymd_from     = Msi_Sys_Utils::easyGetVar($dataApp, 's_seko_ymd_from');     // 施行日/納入日開始
            $seko_ymd_to       = Msi_Sys_Utils::easyGetVar($dataApp, 's_seko_ymd_to');       // 施行日/納入日終了
            $ryosyu_no         = Msi_Sys_Utils::easyGetVar($dataApp, 's_ryosyu_no');         // 領収証番号
            $mode              = Msi_Sys_Utils::easyGetVar($params, 'mode');
            // 部門.  配下(ホール)へ展開
            if (strlen($bumon_cd) > 0) {
                $cond['__x1'] = DataMapper_BumonEx::findDesCond( $db, $bumon_cd ,false);
            }
            // 完全一致
            foreach (Msi_Sys_Utils::strArrayify_qw('seko_tanto_cd pay_method_cd moushi_kbn') as $k) {
                if (strlen($$k) > 0) {
                    $cond[$k] = $$k;
                }
            }
            // 部分一致
            foreach (Msi_Sys_Utils::strArrayify_qw('seko_no juchu_den_no seikyu_den_no sekyu_nm') as $k) {
                if (strlen($$k) > 0) {
                    $cond[$k] = array('~', $$k);
                }
            }
            // 領収証出力状況
            if(strlen($ryosyu_prt_status) > 0){
                if($ryosyu_prt_status == 0){
                    $cond['__x5'] = array( 'x', "T.hako_ryosyu_prc = :x5_1", array('x5_1' => 0) );
                }else{
                    $cond['__x5'] = array( 'x', "T.hako_ryosyu_prc <> :x5_1", array('x5_1' => 0) );
                }
            }
            // 入金日
            //if(strlen($nyukin_ymd_from) > 0 && strlen($nyukin_ymd_to) > 0){
            //    $cond['__x2'] = array( 'x', "T.nyukin_ymd BETWEEN :x2_1 AND :x2_2", array('x2_1' => $nyukin_ymd_from, 'x2_2' => $nyukin_ymd_to));
            //}else if(strlen($nyukin_ymd_from) > 0){
            //    $cond['__x2'] = array( 'x', "T.nyukin_ymd >= :x2_1", array('x2_1'=>$nyukin_ymd_from) );
            //}else if(strlen($nyukin_ymd_to) > 0){
            //    $cond['__x2'] = array( 'x', "T.nyukin_ymd <= :x2_1", array('x2_1'=>$nyukin_ymd_to) );
            //}
            // 施行日/納入日
            if(strlen($seko_ymd_from) > 0 && strlen($seko_ymd_to) > 0){
                $cond['__x3'] = array( 'x', "T.taisyo_ymd BETWEEN :x3_1 AND :x3_2", array('x3_1' => $seko_ymd_from, 'x3_2' => $seko_ymd_to));
            }else if(strlen($seko_ymd_from) > 0){
                $cond['__x3'] = array( 'x', "T.taisyo_ymd >= :x3_1", array('x3_1'=>$seko_ymd_from) );
            }else if(strlen($seko_ymd_to) > 0){
                $cond['__x3'] = array( 'x', "T.taisyo_ymd <= :x3_1", array('x3_1'=>$seko_ymd_to) );
            }
            // 領収証番号
            if(strlen($ryosyu_no) > 0){
                $cond['__x4'] = array( 'x', "T.ryosyu_no ~ :x4_1", array('x4_1'=>$ryosyu_no) );
            }
            $bumon_cd = App_Utils::getCtxtHallWithKaisya();
            $bumon_ref = App_Utils2::getBumonRef($mode, $bumon_cd, $db);
            // 参照権限部門を条件に追加
            $cond['__x20'] = DataMapper_Utils::condOneOf('bumon_cd', implode(',', $bumon_ref));

            // 内金分取得
            $select2 = DataMapper_SekyuExData::findForRyoshulistJuchuUchikin($db, $cond);
            //$select2 = DataMapper_SekyuExData::findForRyoshulistUchikin($db, $cond);
            // 検索実行
            $select  = DataMapper_SekyuExData::findForRyoshulist($db, $cond);
            $select3 = array_merge($select, $select2);
            $isMoreData = false;
            $data = array();
            $count = 0;
            foreach ($select3 as $rec) {
                $count++;
                if ($count > $limit) {
                    $isMoreData = true;
                    break;
                }
                // 領収証依頼区分設定
                if(!isset($rec['ryosyu_irai_kbn'])){
                    if(in_array($rec['moushi_kbn'], array(1, 2))){   // 施行・アフター
                        if(!isset($rec['rs_print_kbn'])){
                            $rec['ryosyu_irai_kbn'] = null;
                        }else{
                            $rec['ryosyu_irai_kbn'] = 0; // 不要
                        }
                    }else if(in_array($rec['moushi_kbn'], array(3, 4))){
                        // 領収書発行区分 && 領収書用紙区分
                        if(!isset($rec['rs_print_kbn'])){
                            $rec['ryosyu_irai_kbn'] = null;
                        }else if($rec['rs_print_kbn'] == 1 && $rec['rs_soufu_kbn'] == 1){         // 要 && 郵送
                            $rec['ryosyu_irai_kbn'] = 1; // 郵送
                        }else if($rec['rs_print_kbn'] == 1 && $rec['rs_soufu_kbn'] == 0){   // 要 && 不要
                            $rec['ryosyu_irai_kbn'] = 0; // 不要
                        }else if($rec['rs_print_kbn'] == 0){                                // 不要
                            $rec['ryosyu_irai_kbn'] = 0; // 不要
                        }else{
                            $rec['ryosyu_irai_kbn'] = 0; // 不要
                        }
                    }else{
                        $rec['ryosyu_irai_kbn'] = 0; // 不要
                    }
                    $rec['ryosyu_irai_kbn_nm'] = $this->getCodeKbnValueLnm('8524', $rec['ryosyu_irai_kbn']); // 領収証依頼区分名
                }
                if(strlen($ryosyu_irai_kbn) > 0){
                    if($ryosyu_irai_kbn != $rec['ryosyu_irai_kbn']){
                        $count--;
                        continue;
                    }
                }
                $rec['oddEven'] = $count % 2 ? 'odd' : 'even';
                $rec['my_id']   = isset($rec['seikyu_den_no']) ? $rec['seikyu_den_no'] : $rec['uri_den_no'];
                $rec['row']     = $count;
                $data[] = $rec;
            }
            $next_offset = -1;
            $prev_offset = -1;
            if ( $count <= 0 ) {
                $msg = '該当するデータはありません';
            } else if ( $isMoreData ) {
                $msg = '該当するデータが他に存在します';
                $next_offset = $offset + $count; // 次ページ
                $prev_offset = $offset;
            }else{
                $prev_offset = $offset;
            }
            $rtnData = array(
                'status'      => 'OK',
                'dataApp'     => $dataApp,
                'dataCol'     => $data,
                'next_offset' => $next_offset,
                'prev_offset' => $prev_offset,
                'msg'         =>  isset($msg) ? $msg : null,
            );
        } catch (Msi_Sys_Exception_InputException $e) {
            $err = $e->getMessage();
            $msg = '検索条件エラーです';
            $rtnData = array(
                'status' => 'NG',
                'msg' => $msg,
            );
        } catch (Exception $e) {
            $err = $e->getMessage();
            Msi_Sys_Utils::err(basename(__FILE__) . ': ' . $err);
            $msg = '内部エラーです';
            $rtnData = array(
                'status' => 'NG',
                'msg' => $msg,
            );
        }
        Msi_Sys_Utils::outJson($rtnData);
    }
    /**
     * 
     * 保存 アクション
     *
     * <AUTHOR> Sugiyama
     * @since 2020/12/xx
     */
    public function saveAction(){
        try {
            $params  = Msi_Sys_Utils::webInputs();
            $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
            $dataCol = Msi_Sys_Utils::json_decode($params['dataColJson']);
            $db = Msi_Sys_DbManager::getMyDb();
            foreach ($dataCol as $value) {
                $this->saveRysyuIraiKbn($db, $value);
            }
            $db->commit();
            $data = array(
                'status' => 'OK',
                'msg' => '保存しました',
            );
        }catch ( Exception $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::err( basename(__FILE__).': '. $err );
            $data = array(
                'status' => 'NG',
                'msg' => $err, // $userMsg,
            );
        }
        Msi_Sys_Utils::outJson($data);
    }
    /**
     * 
     * 領収証依頼区分保存
     * 
     * @param type $db
     * @param type $value
     * @return type
     */
    private function saveRysyuIraiKbn($db, $value){
        // 更新内容
        $data = array();
        $data['ryosyu_irai_kbn'] = Msi_Sys_Utils::emptyToNull( $value['ryosyu_irai_kbn'] );
        // 条件部
        $where = array();
        $where['denpyo_no']  = $value['nyukin_den_no'];
        $where['delete_flg'] = 0;  // 削除フラグ
        // 更新SQL
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("nyukin_denpyo", $data, $where);
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }
    /**
     * 
     * 一括発行 アクション
     * 
     * <AUTHOR> Sugiyama
     * @since 2022/08/xx
     */
    public function ikatuhakoAction() {
        try {
            $db      = Msi_Sys_DbManager::getMyDb();
            $params  = Msi_Sys_Utils::webInputs();
            $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
            $dataCol = Msi_Sys_Utils::json_decode($params['dataColJson']);
            $col = array();
            // 選択行取得
            foreach ($dataCol as $value) {
                if($value['ryoshu_check'] == 1) {
                    $col[] = $value;
                }
            }
            // 選択無し
            if(count($col) == 0){
                throw new Exception("選択されていません。");
            }
            // 発行日時
            $hako_date = date("Y/m/d H:i:s");
            $hako_ymd  = date("YmdHis");
            // 領収証情報チェック
            $this->checkRyosyu($db, $col);
            // 領収証情報作成
            $col3 = array();
            foreach ($col as $colVal) {
                $colVal['hako_date'] = $hako_date;
                $colVal['hako_ymd']  = $hako_ymd;
                // 領収証登録情報作成、保存
                $col2 = $this->createRyosyu($db, $colVal);
                $col3 = array_merge($col3, $col2);
            }
            // 領収証発行
            $this->ryosyuOut($db, $col3, $hako_date);
            // コミット
            $db->commit();
        }catch ( Exception $e ) { 
            $err = $e->getMessage();
            Msi_Sys_Utils::err( basename(__FILE__).': '. $err );
            $data = array(
                'status' => 'NG',
                'msg' => $err,
            );
            Msi_Sys_Utils::outJson($data);
        }
    }
    /**
     * 領収証情報チェック
     * 
     * @param type $db
     * @param type $col
     * @throws Exception
     */
    private function checkRyosyu($db, $col){
        // 一括発行件数チェック
        if(Msi_Sys_Utils::myCount($col) > 1){
            // 供花供物（喪家外）以外
            foreach ($col as $value) {
                if($value['data_kbn'] != 4){
                    throw new Exception("領収証未発行の供花供物(喪家外)以外が選択されています。");
                }
            }
            // 領収証発行済み
            foreach ($col as $value) {
                $cond = array('uri_den_no' => $value['seikyu_den_no'], '__raw_1' => 'hako_kbn IN (0, 1)',);
                $ryosyu_check = DataMapper_RyosyushoHistory::find($db, $cond);
                if(Msi_Sys_Utils::myCount($ryosyu_check) > 0){
                    throw new Exception("領収証未発行の供花供物(喪家外)以外が選択されています。");
                }
            }            
        }
        // 領収証情報チェック
        foreach ($col as $checkvalue) {
            // 発行済みチェック
            $cond = array('uri_den_no' => $checkvalue['seikyu_den_no'], '__raw_1' => 'hako_kbn IN (0, 1)',);
            $ryosyu_check = DataMapper_RyosyushoHistory::find($db, $cond);
            if(Msi_Sys_Utils::myCount($ryosyu_check) > 0){
                throw new Exception("発行済みの領収証データです。画面の再検索を行ってください。");
            }
            $msi = "";
            // 領収名義
            if(!isset($checkvalue['ryosyu_meigi']) || strlen($checkvalue['ryosyu_meigi']) == 0){
                $msi .= "領収名義";
            }
            // 領収書送付先
            if(!isset($checkvalue['ryosyu_soufu_nm']) || strlen($checkvalue['ryosyu_soufu_nm']) == 0){
                if(strlen($msi) > 0){
                    $msi .= "、";
                }
                $msi .= "領収書送付先";
            }
            // 領収書送付先郵便番号
            if(!isset($checkvalue['ryosyu_soufu_yubin_no']) || strlen($checkvalue['ryosyu_soufu_yubin_no']) == 0){
                if(strlen($msi) > 0){
                    $msi .= "、";
                }
                $msi .= "領収書送付先郵便番号";
            }
            // 領収書送付先住所1
            if(!isset($checkvalue['ryosyu_soufu_addr1']) || strlen($checkvalue['ryosyu_soufu_addr1']) == 0){
                if(strlen($msi) > 0){
                    $msi .= "、";
                }
                $msi .= "領収書送付先住所";
            }
            // メッセージ判定
            if(strlen($msi) > 0){
                throw new Exception($msi."が設定されていません。（請求伝票番号：".$checkvalue['seikyu_den_no']."）");
            }
        }
    }
    /**
     * 領収証発行
     * 
     * @param type $db
     * @param type $col2
     * @param type $hako_date
     */
    private function ryosyuOut($db, $col2, $hako_date){
        $count = 0;
        foreach ($col2 as $key => $hakodata) {
            // 領収証印刷処理
            list($temp_file, $filename) = Saiken_RyoshuPdf::outputPdfSeikyu($db, $hakodata, true, $count);
            $files1[] = array( 'path' => $filename, 'file' => $temp_file );                
            $col2[$key]['meisai']          = null;
            $col2[$key]['temp']            = null;
            $col2[$key]['ryosyu_youshiki'] = "60";
            $col2[$key]['meisai_yoshiki']  = null;
            $col2[$key]['meisai_file']     = null;
            $col2[$key]['ryosyu_file']     = $filename;
            $col2[$key]['moushi_kbn_nm']   = $this->getMoushiKbnNm($db, $col2[$key]);
            $col2[$key]['ryosyu_irai_kbn_nm'] = $this->getCodeKbnValueLnm('8524', $hakodata['ryosyu_irai_kbn']); // 領収証依頼区分名
            $optData['ryosyu_no']          = $hakodata['ryosyusho_no'];
            $optData['eda_no']             = $hakodata['ryosyusho_eda_no'];
            $count++;
        }
        // bdlファイル出力
        $outMsg = '';
        $isOutMsgErr = false;
        $outMsg .= sprintf( "領収データ %s 件を出力しました", count($col2));
        // 最終行に件数を追加
        $last_col = array();
        foreach ($col2[0] as $key => $colvalue3) {   // 項目分ループ
            $last_col[$key] = null;
        }
        $last_col['ryosyusho_no'] = count($col2);
        $col2[] = $last_col;
        $files = $files1;
        // ファイル名
        $fileTs = Msi_Sys_Utils::getDatetime(null, 'YmdHis');
        $bdl_filename = $fileTs . '.bdl';
        // 複数ファイル出力準備
        $outdata = Msi_Sys_Utils::genBdlFileData($files, 'ryoshudbl');
        Msi_Sys_Utils::out2wayPush($outdata, $bdl_filename, 'application/x-msi-bdl');
        // 出力履歴を登録
        App_FileBlobUtils::putIntoBdlFileLog($db, $outdata, $bdl_filename, $hako_date, 2, null);
        // 複数ファイル出力
        Msi_Sys_Utils::out2wayFlush( array('outMsg'=>$outMsg, 'isOutMsgErr'=>$isOutMsgErr) );
    }
    /**
     * 領収証作成
     * 
     * @param type $db
     * @param type $colVal
     * @return type
     */
    private function createRyosyu($db, $colVal){
        // 発行部門分
        $inshi_type_cd = null;
        $inshi_type = DataMapper_CodeNmMst::findOne($db, array('code_kbn'=> '8551', 'kbn_value_snm' => 10, 'kbn_value_lnm' => $colVal['bumon_cd']));
        if(Msi_Sys_Utils::myCount($inshi_type) > 0){
            $inshi_type_cd = $inshi_type['kbn_value_cd'];
        }
        // 互助会部門分
        $inshi_type_cd_gojo = null;
        $inshi_type_gojo = DataMapper_CodeNmMst::findOne($db, array('code_kbn'=> '8551', 'kbn_value_snm' => 20, 'kbn_value_lnm' => $colVal['bumon_cd']));
        if(Msi_Sys_Utils::myCount($inshi_type_gojo) > 0){
            $inshi_type_cd_gojo = $inshi_type_gojo['kbn_value_cd'];
        }
        $col2 = array();
        if($colVal['data_kbn'] == 1){
            /**
             * 葬儀領収証
             */
            // 葬儀費用領収証作成
            // 但し書き取得
            $code_mst = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '8564', 'kbn_value_cd_num' => 1));
            // 但し書き整形
            $tadashikaki = "";
            $k_nm = $colVal['k_nm'];
            if(Msi_Sys_Utils::myCount($code_mst) > 0){
                $tadashikaki = $code_mst['kbn_value_snm'];
                $tadashikaki = str_replace('@K@', $k_nm, $tadashikaki);
            }
            // 葬儀費用金額取得
            $ryosyu_prc = 0;
            $sougi_prc = $this->getSougiPrc($db, $colVal);
            if(Msi_Sys_Utils::myCount($sougi_prc) > 0){
                foreach($sougi_prc as $sougi){
                    $ryosyu_prc += $sougi['sum_prc'];
                }
            }
            $colVal['nyukin_prc'] = $sougi_prc;
            // 領収証番号発行
            $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, 'ryosyusho_history', 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
            $colVal['ryosyu_meigi']    = $colVal['ryosyu_meigi'];
            $colVal['ryosyusho_no']    = $ryosyusho_no;
            $colVal['ryosyu_prc']      = $ryosyu_prc;
            $colVal['ryosyu_prc_disp'] = $ryosyu_prc;
            $colVal['tadashikaki']     = $tadashikaki;
            $colVal['tadashikaki_cd']  = 1;
            $colVal['inshi_type_cd']   = $inshi_type_cd; // 発行部門分
            // 領収証保存
            $resultRyosyu = $this->saveRyousyu($db, $colVal);
            $colVal['hako_count']      = $resultRyosyu['hako_count'];
            $colVal['ryosyusho_eda_no']= $resultRyosyu['ryosyusho_eda_no'];
            $colVal['uchiwake_flg']    = false;
            $col2[] = $colVal;
            
            
            // 互助会費用作成
            // 互助会費用金額取得
            $ryosyu_prc = 0;
            $taisho_gaku = 0;
            $early_use_cost = 0;
            $keiyaku_zei = 0;
            $gojokai_prc = $this->getGojokaiPrc($db, $colVal);
            if(Msi_Sys_Utils::myCount($gojokai_prc) > 0){
                foreach($gojokai_prc as $gojokai){
                    $ryosyu_prc += $gojokai['sum_prc'];
                    $taisho_gaku += $gojokai['taisho_gaku'];
                    $early_use_cost += $gojokai['early_use_cost'];
                    $keiyaku_zei += $gojokai['keiyaku_zei'];
                }
            }
            // 但し書き文言
            $tadashikaki_footer = '会費残精算として';
            // 会費消費税のみ発生した場合
            if($taisho_gaku == 0 && $early_use_cost == 0){
                $tadashikaki_footer = "コース利用消費税として";
            }
            // 金額が有った場合
            if($ryosyu_prc != 0){
                $colVal['nyukin_prc'] = $gojokai_prc;
                // 但し書き整形
                $tadashikaki = "";
                $k_nm = $colVal['k_nm'];
                if(Msi_Sys_Utils::myCount($gojokai_prc) > 0){
                    $tadashikaki = $gojokai_prc[0]['kain_no'];
                }
                $tadashikaki = "故 ".$k_nm."　様\n".$tadashikaki.$tadashikaki_footer;
                // 領収証番号発行
                $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, 'ryosyusho_history', 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
                $colVal['ryosyu_meigi']    = $colVal['ryosyu_meigi'];
                $colVal['ryosyusho_no']    = $ryosyusho_no;
                $colVal['ryosyu_prc']      = $ryosyu_prc;
                $colVal['ryosyu_prc_disp'] = $ryosyu_prc;
                $colVal['tadashikaki']     = $tadashikaki;
                $colVal['tadashikaki_cd']  = 1;
                $colVal['inshi_type_cd']   = $inshi_type_cd_gojo; // 互助会部門分
                // 領収証保存
                $resultRyosyu = $this->saveRyousyu($db, $colVal);
                $colVal['hako_count']      = $resultRyosyu['hako_count'];
                $colVal['ryosyusho_eda_no']= $resultRyosyu['ryosyusho_eda_no'];
                $colVal['uchiwake_flg']    = false;
                $col2[] = $colVal;
            }
            
            
            // 個別領収供花供物作成
            // 但し書き取得
            $code_mst = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '8564', 'kbn_value_cd_num' => 3));
            // 但し書き整形
            $tadashikaki = "";
            $k_nm = $colVal['k_nm'];
            if(Msi_Sys_Utils::myCount($code_mst) > 0){
                $tadashikaki = $code_mst['kbn_value_snm'];
                $tadashikaki = str_replace('@K@', $k_nm, $tadashikaki);
            }
            // 個別領収供花供物金額取得
            $kyoka_prc = $this->getKyokaPrc($db, $colVal);
            $colVal['nyukin_prc'] = $kyoka_prc;
            foreach ($kyoka_prc as $kyoka){
                // 領収証番号発行
                $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, 'ryosyusho_history', 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
                $colVal['ryosyu_meigi']    = $kyoka['ryosyusyo_meigi'];
                $colVal['ryosyusho_no']    = $ryosyusho_no;
                $colVal['ryosyu_prc']      = $kyoka['sum_prc'];
                $colVal['ryosyu_prc_disp'] = $kyoka['sum_prc'];
                $colVal['tadashikaki']     = $tadashikaki;
                $colVal['tadashikaki_cd']  = 3;
                $colVal['inshi_type_cd']   = $inshi_type_cd; // 発行部門分
                // 領収証保存
                $resultRyosyu = $this->saveRyousyu($db, $colVal);
                $colVal['hako_count']      = $resultRyosyu['hako_count'];
                $colVal['ryosyusho_eda_no']= $resultRyosyu['ryosyusho_eda_no'];
                $colVal['uchiwake_flg']    = true;
                $col2[] = $colVal;
            }
        }else if($colVal['data_kbn'] == 2){
            /**
             * 法事領収証
             */
            // 葬儀費用領収証作成
            // 但し書き取得
            $code_mst = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '8564', 'kbn_value_cd_num' => 2));
            // 但し書き整形
            $tadashikaki = "";
            $k_nm = $colVal['k_nm'];
            if(Msi_Sys_Utils::myCount($code_mst) > 0){
                $tadashikaki = $code_mst['kbn_value_lnm']."として";
            }
            // 葬儀費用金額取得
            $ryosyu_prc = 0;
            $houji_prc = $this->getHoujiPrc($db, $colVal);
            if(Msi_Sys_Utils::myCount($houji_prc) > 0){
                foreach($houji_prc as $houji){
                    $ryosyu_prc += $houji['sum_prc'];
                }
            }
            $colVal['nyukin_prc'] = $houji_prc;
            // 領収証番号発行
            $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, 'ryosyusho_history', 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
            $colVal['ryosyu_meigi']    = $colVal['ryosyu_meigi'];
            $colVal['ryosyusho_no']    = $ryosyusho_no;
            $colVal['ryosyu_prc']      = $ryosyu_prc;
            $colVal['ryosyu_prc_disp'] = $ryosyu_prc;
            $colVal['tadashikaki']     = $tadashikaki;
            $colVal['tadashikaki_cd']  = 2;
            $colVal['inshi_type_cd']   = $inshi_type_cd; // 発行部門分
            // 領収証保存
            $resultRyosyu = $this->saveRyousyu($db, $colVal);
            $colVal['hako_count']      = $resultRyosyu['hako_count'];
            $colVal['ryosyusho_eda_no']= $resultRyosyu['ryosyusho_eda_no'];
            $colVal['uchiwake_flg']    = false;
            $col2[] = $colVal;
            
            // 互助会費用作成
            // 互助会費用金額取得
            $ryosyu_prc = 0;
            $gojokai_prc = $this->getGojokaiPrc($db, $colVal);
            if(Msi_Sys_Utils::myCount($gojokai_prc) > 0){
                foreach($gojokai_prc as $gojokai){
                    $ryosyu_prc += $gojokai['sum_prc'];
                }
            }
            // 金額が有った場合
            if($ryosyu_prc != 0){
                $colVal['nyukin_prc'] = $gojokai_prc;
                // 但し書き整形
                $tadashikaki = "";
                if(Msi_Sys_Utils::myCount($gojokai_prc) > 0){
                    $tadashikaki = $gojokai_prc[0]['kain_no'];
                }
                $tadashikaki = $tadashikaki."会費残精算として";
                // 領収証番号発行
                $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, 'ryosyusho_history', 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
                $colVal['ryosyu_meigi']    = $colVal['ryosyu_meigi'];
                $colVal['ryosyusho_no']    = $ryosyusho_no;
                $colVal['ryosyu_prc']      = $ryosyu_prc;
                $colVal['ryosyu_prc_disp'] = $ryosyu_prc;
                $colVal['tadashikaki']     = $tadashikaki;
                $colVal['tadashikaki_cd']  = 2;
                $colVal['inshi_type_cd']   = $inshi_type_cd_gojo; // 互助会部門分
                // 領収証保存
                $resultRyosyu = $this->saveRyousyu($db, $colVal);
                $colVal['hako_count']      = $resultRyosyu['hako_count'];
                $colVal['ryosyusho_eda_no']= $resultRyosyu['ryosyusho_eda_no'];
                $colVal['uchiwake_flg']    = false;
                $col2[] = $colVal;                
            }
            
            
            // 個別領収供花供物作成
            // 但し書き取得
            $code_mst = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '8564', 'kbn_value_cd_num' => 5));
            // 但し書き整形
            $tadashikaki = "";
            $k_nm = $colVal['k_nm'];
            if(Msi_Sys_Utils::myCount($code_mst) > 0){
                $tadashikaki = $code_mst['kbn_value_snm'];
                $tadashikaki = str_replace('@K@', $k_nm, $tadashikaki);
            }
            // 個別領収供花供物金額取得
            $kyoka_prc = $this->getKyokaPrc($db, $colVal);
            $colVal['nyukin_prc'] = $kyoka_prc;
            foreach ($kyoka_prc as $kyoka){
                // 領収証番号発行
                $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, 'ryosyusho_history', 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
                $colVal['ryosyu_meigi']    = $kyoka['ryosyusyo_meigi'];
                $colVal['ryosyusho_no']    = $ryosyusho_no;
                $colVal['ryosyu_prc']      = $kyoka['sum_prc'];
                $colVal['ryosyu_prc_disp'] = $kyoka['sum_prc'];
                $colVal['tadashikaki']     = $tadashikaki;
                $colVal['tadashikaki_cd']  = 5;
                $colVal['inshi_type_cd']   = $inshi_type_cd; // 発行部門分
                // 領収証保存
                $resultRyosyu = $this->saveRyousyu($db, $colVal);
                $colVal['hako_count']      = $resultRyosyu['hako_count'];
                $colVal['ryosyusho_eda_no']= $resultRyosyu['ryosyusho_eda_no'];
                $colVal['uchiwake_flg']    = true;
                $col2[] = $colVal;
            }
        }else if($colVal['data_kbn'] == 3){
            /**
             * アフター領収証
             */
            // アフター領収証作成
            // 但し書き取得
            $code_mst = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '8564', 'kbn_value_cd_num' => 4));
            // 但し書き整形
            $tadashikaki = "";
            if(Msi_Sys_Utils::myCount($code_mst) > 0){
                $tadashikaki = $code_mst['kbn_value_snm'];
            }
            $ryosyu_prc = 0;
            $after_prc = $this->getAfterPrc($db, $colVal);
            if(Msi_Sys_Utils::myCount($after_prc) > 0){
                foreach($after_prc as $after){
                    $ryosyu_prc += $after['sum_prc'];
                }
            }
            $colVal['nyukin_prc'] = $after_prc;
            // 領収証番号発行
            $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, 'ryosyusho_history', 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
            $colVal['ryosyu_meigi']    = $colVal['ryosyu_meigi'];
            $colVal['ryosyusho_no']    = $ryosyusho_no;
            $colVal['ryosyu_prc']      = $ryosyu_prc;
            $colVal['ryosyu_prc_disp'] = $ryosyu_prc;
            $colVal['tadashikaki']     = $tadashikaki;
            $colVal['tadashikaki_cd']  = 4;
            $colVal['inshi_type_cd']   = $inshi_type_cd; // 発行部門分
            // 領収証保存
            $resultRyosyu = $this->saveRyousyu($db, $colVal);
            $colVal['hako_count']      = $resultRyosyu['hako_count'];
            $colVal['ryosyusho_eda_no']= $resultRyosyu['ryosyusho_eda_no'];
            $colVal['uchiwake_flg']    = false;
            $col2[] = $colVal;
        }else if($colVal['data_kbn'] == 4){
            /**
             * 喪家外供花供物領収証
             */
            // 申込区分取得
            $moushi_kbn = App_ClsSekoKanri::getMoushiKbn($db, $colVal['seko_no']);
            // 但し書き区分設定
            $tadashi_kbn = 3;
            if($moushi_kbn == 2){
                // 法事の場合
                $tadashi_kbn = 5;
            }
            // 但し書き取得
            $code_mst = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '8564', 'kbn_value_cd_num' => $tadashi_kbn));
            // 但し書き整形
            $tadashikaki = "";
            $k_nm = $colVal['k_nm'];
            if(Msi_Sys_Utils::myCount($code_mst) > 0){
                $tadashikaki = $code_mst['kbn_value_lnm'];
            }
            $tadashikaki = "故 ".$k_nm."様 ".$tadashikaki."として";
            // 喪家外供花供物金額取得
            $kyoka_prc = $this->getKyokaPrc2($db, $colVal);
            $colVal['nyukin_prc'] = $kyoka_prc;
            foreach ($kyoka_prc as $kyoka) {
                // 領収証番号発行
                $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, 'ryosyusho_history', 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
                $colVal['ryosyu_meigi']    = $kyoka['ryosyusyo_meigi'];
                $colVal['ryosyusho_no']    = $ryosyusho_no;
                $colVal['ryosyu_prc']      = $kyoka['sum_prc'];
                $colVal['ryosyu_prc_disp'] = $kyoka['sum_prc'];
                $colVal['tadashikaki']     = $tadashikaki;
                $colVal['tadashikaki_cd']  = $tadashi_kbn;
                $colVal['inshi_type_cd']   = $inshi_type_cd; // 発行部門分
                // 領収証保存
                $resultRyosyu = $this->saveRyousyu($db, $colVal);
                $colVal['hako_count']      = $resultRyosyu['hako_count'];
                $colVal['ryosyusho_eda_no']= $resultRyosyu['ryosyusho_eda_no'];
                $colVal['uchiwake_flg']    = true;
                $col2[] = $colVal;
            }
        }
        return $col2;
    }
    /**
     * 葬儀費用取得
     * 
     * @param type $db
     * @param type $colVal
     */
    private function getSougiPrc($db, $colVal){
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT
         sdm.zei_kbn
        ,sdm.zei_cd
        ,sdm.reduced_tax_rate
        ,zei.zei_rtu
        ,zei.zei_hasu_kbn
        ,COALESCE(SUM(sdm.uri_prc + sdm.gojokai_nebiki_prc + sdm.nebiki_prc + sdm.hoshi_prc + sdm.out_zei_prc),0) + 
         CASE sdm.reduced_tax_rate
              WHEN 1 THEN COALESCE(sd.sougi_premium_service_prc,0) -- 標準税率から割増を引く
              ELSE 0 
         END           AS sum_prc
    FROM
        seikyu_denpyo_msi sdm
    LEFT JOIN seikyu_denpyo sd
    ON  sd.seikyu_den_no = sdm.seikyu_den_no
    AND sd.data_kbn      = 1
    AND sd.delete_flg    = 0
    LEFT JOIN zei_mst zei
    ON  zei.zei_cd     = sdm.zei_cd
    AND zei.delete_flg = 0
    WHERE
        sdm.seikyu_den_no = :seikyu_den_no
    AND sdm.k_free1 IS NULL -- 領収証個別 チェックOFFのみ
    AND sdm.delete_flg    = 0
    GROUP BY 
         sdm.zei_kbn
        ,sdm.zei_cd
        ,sdm.reduced_tax_rate
        ,zei.zei_rtu
        ,zei.zei_hasu_kbn
        ,sd.sougi_premium_service_prc
    ORDER BY 
         sdm.reduced_tax_rate
        ,sdm.zei_cd DESC
END_OF_SQL
            , array('seikyu_den_no' => $colVal['seikyu_den_no']));
        // 充当金額を取得
        $select2 = $db->easySelOne(<<< END_OF_SQL
    SELECT 
        sd.sougi_tokuten_prc 
    FROM seikyu_denpyo sd
    WHERE 
        sd.seikyu_den_no = :seikyu_den_no
    AND sd.delete_flg    = 0
END_OF_SQL
            , array('seikyu_den_no' => $colVal['seikyu_den_no']));
        // 標準税率から充当金額を引く
        if(Msi_Sys_Utils::myCount($select2) > 0){
            $sougi_tokuten_prc = $select2['sougi_tokuten_prc'];
            foreach ($select as $key => $value){
                $select[$key]['sum_prc'] += $sougi_tokuten_prc;
                break;
            }
        }
        return $select;
    }
    /**
     * 葬儀費用取得
     * 
     * @param type $db
     * @param type $colVal
     */
    private function getHoujiPrc($db, $colVal){
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT
         sdm.zei_kbn
        ,sdm.zei_cd
        ,sdm.reduced_tax_rate
        ,zei.zei_rtu
        ,zei.zei_hasu_kbn
        ,COALESCE(SUM(sdm.uri_prc + sdm.gojokai_nebiki_prc + sdm.nebiki_prc + sdm.hoshi_prc + sdm.out_zei_prc),0) + 
         CASE sdm.reduced_tax_rate
              WHEN 1 THEN COALESCE(sd.sougi_premium_service_prc,0) -- 標準税率から割増を引く
              ELSE 0 
         END           AS sum_prc
    FROM
        seikyu_denpyo_msi sdm
    LEFT JOIN seikyu_denpyo sd
    ON  sd.seikyu_den_no = sdm.seikyu_den_no
    AND sd.data_kbn      = 2
    AND sd.delete_flg    = 0
    LEFT JOIN zei_mst zei
    ON  zei.zei_cd     = sdm.zei_cd
    AND zei.delete_flg = 0
    WHERE
        sdm.seikyu_den_no = :seikyu_den_no
    AND sdm.k_free1 IS NULL -- 領収証個別 チェックOFFのみ
    AND sdm.delete_flg    = 0
    GROUP BY 
         sdm.zei_kbn
        ,sdm.zei_cd
        ,sdm.reduced_tax_rate
        ,zei.zei_rtu
        ,zei.zei_hasu_kbn
        ,sd.sougi_premium_service_prc
    ORDER BY 
         sdm.reduced_tax_rate
        ,sdm.zei_cd DESC
END_OF_SQL
            , array('seikyu_den_no' => $colVal['seikyu_den_no']));
        // 充当金額を取得
        $select2 = $db->easySelOne(<<< END_OF_SQL
    SELECT 
        sd.sougi_tokuten_prc 
    FROM seikyu_denpyo sd
    WHERE 
        sd.seikyu_den_no = :seikyu_den_no
    AND sd.delete_flg    = 0
END_OF_SQL
            , array('seikyu_den_no' => $colVal['seikyu_den_no']));
        // 標準税率から充当金額を引く
        if(Msi_Sys_Utils::myCount($select2) > 0){
            $sougi_tokuten_prc = $select2['sougi_tokuten_prc'];
            foreach ($select as $key => $value){
                $select[$key]['sum_prc'] += $sougi_tokuten_prc;
                break;
            }
        }
        return $select;
    }
    /**
     * アフター金額取得
     * 
     * @param type $db
     * @param type $colVal
     */
    private function getAfterPrc($db, $colVal){
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT
         sdm.zei_kbn
        ,sdm.zei_cd
        ,sdm.reduced_tax_rate
        ,zei.zei_rtu
        ,zei.zei_hasu_kbn
        ,COALESCE(SUM(sdm.uri_prc + sdm.gojokai_nebiki_prc + sdm.nebiki_prc + sdm.hoshi_prc + sdm.out_zei_prc),0) AS sum_prc
    FROM
        seikyu_denpyo_msi sdm
    LEFT JOIN zei_mst zei
    ON  zei.zei_cd     = sdm.zei_cd
    AND zei.delete_flg = 0
    WHERE
        sdm.seikyu_den_no = :seikyu_den_no
    AND sdm.delete_flg    = 0
    GROUP BY 
         sdm.zei_kbn
        ,sdm.zei_cd
        ,sdm.reduced_tax_rate
        ,zei.zei_rtu
        ,zei.zei_hasu_kbn
    ORDER BY 
         sdm.reduced_tax_rate
        ,sdm.zei_cd DESC
END_OF_SQL
            , array('seikyu_den_no' => $colVal['seikyu_den_no']));
        return $select;
    }
    /**
     * 個別領収供花供物
     * 
     * @param type $db
     * @param type $colVal
     */
    private function getKyokaPrc($db, $colVal){
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT
         udm.k_free1  -- 領収証個別 1:チェックON / NULL:チェックOFF
        ,rs_print_kbn -- 領収書発行 1:要 / 2:不要
        ,usi.ryosyusyo_meigi
        ,udm.uri_prc
        ,udm.gojokai_nebiki_prc
        ,udm.nebiki_prc
        ,udm.zei_kbn
        ,udm.zei_cd
        ,udm.reduced_tax_rate
        ,zei.zei_rtu
        ,zei.zei_hasu_kbn
        ,udm.out_zei_prc 
        ,udm.uri_prc + udm.out_zei_prc  AS sum_prc
    FROM
        uriage_denpyo ud 
    INNER JOIN uriage_sekyu_saki_info usi 
    ON  usi.uri_den_no = ud.uri_den_no
    AND usi.delete_flg = 0
    INNER JOIN uriage_denpyo_msi udm 
    ON  udm.uri_den_no = ud.uri_den_no 
    AND usi.delete_flg = 0
    LEFT JOIN zei_mst zei
    ON  zei.zei_cd     = udm.zei_cd
    AND zei.delete_flg = 0
    WHERE
        ud.seko_no      = :seko_no
    AND ud.data_kbn     = 4 
    AND ud.delete_flg   = 0 
    AND ud.juchusaki_kbn= 1 -- 喪家
    AND udm.k_free1     = 1 -- 領収証個別 チェックON
    AND usi.rs_print_kbn= 1 -- 領収証発行 要
    ORDER BY
        ud.uri_den_no
END_OF_SQL
            , array('seko_no' => $colVal['seko_no']));
        return $select;
    }
    /**
     * 喪家外供花供物
     * 
     * @param type $db
     * @param type $colVal
     */
    private function getKyokaPrc2($db, $colVal){
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT
         udm.k_free1  -- 領収証個別 1:チェックON / NULL:チェックOFF
        ,rs_print_kbn -- 領収書発行 1:要 / 2:不要
        ,usi.ryosyusyo_meigi
        ,udm.uri_prc
        ,udm.gojokai_nebiki_prc
        ,udm.nebiki_prc
        ,udm.zei_kbn
        ,udm.zei_cd
        ,udm.reduced_tax_rate
        ,zei.zei_rtu
        ,zei.zei_hasu_kbn
        ,udm.out_zei_prc 
        ,udm.uri_prc + udm.out_zei_prc  AS sum_prc
    FROM
        uriage_denpyo ud 
    INNER JOIN uriage_sekyu_saki_info usi 
    ON  usi.uri_den_no = ud.uri_den_no
    AND usi.delete_flg = 0
    INNER JOIN uriage_denpyo_msi udm 
    ON  udm.uri_den_no = ud.uri_den_no 
    AND usi.delete_flg = 0
    LEFT JOIN zei_mst zei
    ON  zei.zei_cd     = udm.zei_cd
    AND zei.delete_flg = 0
    WHERE
        ud.uri_den_no   = :uri_den_no
    AND ud.data_kbn     = 4 
    AND ud.delete_flg   = 0 
    AND ud.juchusaki_kbn= 2 -- 喪家外
    AND udm.k_free1     = 0 -- 領収証個別 チェックOFF
    ORDER BY
        ud.uri_den_no
END_OF_SQL
            , array('uri_den_no' => $colVal['uri_den_no']));
        return $select;
    }
    /**
     * 互助会費用取得
     * 
     * @param type $db
     * @param type $colVal
     */
    private function getGojokaiPrc($db, $colVal){
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT
         2                  AS zei_kbn
        ,sd.sougi_zei_cd    AS zei_cd 
        ,zei.reduced_tax_rate
        ,zei.zei_rtu
        ,zei.zei_hasu_kbn
        ,sd.sougi_keiyaku_prc + sd.sougi_wari_prc + sd.sougi_early_use_cost + sd.sougi_harai_prc + COALESCE(sd.n_free5,0)
                                                                                    AS taisho_gaku
        ,sd.sougi_early_use_cost + sd.sougi_early_use_cost_zei                      AS early_use_cost
        ,sd.sougi_keiyaku_zei + sd.sougi_wari_zei                                   AS keiyaku_zei
        ,sd.sougi_keiyaku_zei + sd.sougi_wari_zei + sd.sougi_early_use_cost_zei     AS out_zei_prc
        ,sd.sougi_keiyaku_prc + sd.sougi_wari_prc + sd.sougi_early_use_cost + sd.sougi_harai_prc + COALESCE(sd.n_free5,0) +
         sd.sougi_keiyaku_zei + sd.sougi_wari_zei + sd.sougi_early_use_cost_zei     AS sum_prc
        ,array_to_string(ARRAY(SELECT TRIM(kain_no) FROM seko_gojokai_member WHERE seko_no = sd.seko_no AND yoto_kbn IN(1,3) ORDER BY kain_no),',') 
                                                                                    AS kain_no
    FROM
        seikyu_denpyo sd
    LEFT JOIN zei_mst zei
    ON  CURRENT_DATE BETWEEN zei.tekiyo_st_date AND zei.tekiyo_ed_date
    AND zei.reduced_tax_rate = 1
    AND zei.delete_flg       = 0
    WHERE
        sd.seko_no     = :seko_no
    AND sd.data_kbn    = :data_kbn
    AND sd.uchikin_prc = 0
    AND sd.delete_flg  = 0
END_OF_SQL
            , array('seko_no' => $colVal['seko_no'], 'data_kbn' => $colVal['data_kbn']));
        return $select;
    }
    /**
     * 領収証一括発行保存処理
     * 
     * @param type $db
     * @param type $data
     */
    private function saveRyousyu($db, $data){
        // 発行回数取得
        $cond =array(
                '__etc_limit'   => 1,
                '__etc_orderby' => array('hako_count DESC')
        );
        $hako_count       = 1; // 発行回数
        $ryosyusho_eda_no = 0; // 領収証枝番号
        $cond['uri_den_no'] = $data['seikyu_den_no'];
        $history = DataMapper_RyosyushoHistory::findOne($db, $cond);
        if(Msi_Sys_Utils::myCount($history) > 0) {
            $hako_count = $history['hako_count'] + 1;
        }
        // 領収証枝番号取得
        $cond2 =array(
                '__etc_limit'   => 1,
                '__etc_orderby' => array('ryosyu_no_sub DESC')
        );
        $cond2['uri_den_no'] = $data['seikyu_den_no'];
        $history2 = DataMapper_RyosyushoHistory::findOne($db, $cond2);
        if(Msi_Sys_Utils::myCount($history2) > 0) {
            $ryosyusho_eda_no = $history2['ryosyusho_eda_no'] + 1;
        }
        $data['hako_count']       = $hako_count;
        $data['ryosyusho_eda_no'] = $ryosyusho_eda_no;
        // 入金金額振り分け
        self::setNyukinInfo($data);
        // 領収証名義
        $ryosyu_meigi = $data['ryosyu_meigi'];
        // 印紙税金額
        $inshi_prc = 0;
        $inshi_kbn = 1;
        if($data['pay_method_cd'] != "4" && $data['pay_method_cd'] != "5"){
            // 8551:印紙税納付税務署(領収証)
            $inshi = $this->getInshiCodeKbn($db, $data['bumon_cd']);
            if(Msi_Sys_Utils::myCount($inshi) == 0){
                $inshi_prc = 0;
            }else{
                $zei_nuki_prc = $data['ryosyu_prc'] - $data['zei_prc_std'] - $data['zei_prc_keigen'] - $data['zei_prc_3'] - $data['zei_prc_5'] - $data['zei_prc_8'];;
                $inshi_prc = App_Utils2::getStampPrc($zei_nuki_prc, $inshi_kbn);
            }
        }
        // 登録情報設定
        $row = array();
        $row['atena']               = $ryosyu_meigi;
        $row['ryosyusho_no']        = $data['ryosyusho_no'];
        $row['ryosyusho_eda_no']    = 0;
        $row['ryosyu_no']           = $data['ryosyusho_no'];
        $row['ryosyu_no_sub']       = 0;
        $row['hako_count']          = $data['hako_count'];
        $row['uri_den_no']          = $data['seikyu_den_no'];
        $row['seko_no']             = $data['seko_no'];
        $row['seko_no_sub']         = $data['seko_no_sub'];
        $row['data_kbn']            = $data['data_kbn'];
        $row['hako_kbn']            = 0;
        $row['hako_date']           = $data['hako_date'];
        $row['disp_kbn']            = 1;
        $row['nyukin_den_no']       = $data['nyukin_den_no'];
        $row['syukin_tanto_cd']     = App_Utils::getTantoCd();
        $row['jimu_tanto_cd']       = App_Utils::getTantoCd();
        $row['tadashikaki']         = $data['tadashikaki'];
        $row['tadashikaki_cd']      = $data['tadashikaki_cd'];
        // 領収証金額情報
        $row['gokei_prc']           = $data['ryosyu_prc'];
        $row['furikomi_prc_hikazei']= $data['furikomi_prc_hikazei'];
        $row['furikomi_prc_keigen'] = $data['furikomi_prc_keigen'];
        $row['furikomi_prc_3']      = $data['furikomi_prc_3'];
        $row['furikomi_prc_5']      = $data['furikomi_prc_5'];
        $row['furikomi_prc_8']      = $data['furikomi_prc_8'];
        $furikomi_prc = $data['furikomi_prc'] + $data['furikomi_prc_hikazei'] + $data['furikomi_prc_keigen'] + $data['furikomi_prc_3'] + $data['furikomi_prc_5'] + $data['furikomi_prc_8'];
        $row['furikomi_prc']        = $furikomi_prc;
        $row['genkin_prc_hikazei']  = $data['genkin_prc_hikazei'];
        $row['genkin_prc_keigen']   = $data['genkin_prc_keigen'];
        $row['genkin_prc_3']        = $data['genkin_prc_3'];
        $row['genkin_prc_5']        = $data['genkin_prc_5'];
        $row['genkin_prc_8']        = $data['genkin_prc_8'];
        $genkin_prc = $data['genkin_prc'] + $data['genkin_prc_hikazei'] + $data['genkin_prc_keigen'] + $data['genkin_prc_3'] + $data['genkin_prc_5'] + $data['genkin_prc_8'];
        $row['genkin_prc']          = $genkin_prc;
        $row['credit_prc_hikazei']  = $data['credit_prc_hikazei'];
        $row['credit_prc_keigen']   = $data['credit_prc_keigen'];
        $row['credit_prc_3']        = $data['credit_prc_3'];
        $row['credit_prc_5']        = $data['credit_prc_5'];
        $row['credit_prc_8']        = $data['credit_prc_8'];
        $credit_prc = $data['credit_prc'] + $data['credit_prc_hikazei'] + $data['credit_prc_keigen'] + $data['credit_prc_3'] + $data['credit_prc_5'] + $data['credit_prc_8'];
        $row['credit_prc']          = $credit_prc;
        $row['j_debit_prc_hikazei'] = $data['j_debit_prc_hikazei'];
        $row['j_debit_prc_keigen']  = $data['j_debit_prc_keigen'];
        $row['j_debit_prc_3']       = $data['j_debit_prc_3'];
        $row['j_debit_prc_5']       = $data['j_debit_prc_5'];
        $row['j_debit_prc_8']       = $data['j_debit_prc_8'];
        $j_debit_prc = $data['credit_prc'] + $data['credit_prc_hikazei'] + $data['credit_prc_keigen'] + $data['credit_prc_3'] + $data['credit_prc_5'] + $data['credit_prc_8'];
        $row['j_debit_prc']         = $j_debit_prc;
        $row['zei_prc_std']         = $data['zei_prc_std'];
        $row['zei_prc_keigen']      = $data['zei_prc_keigen'];
        $row['zei_prc_3']           = $data['zei_prc_3'];
        $row['zei_prc_5']           = $data['zei_prc_5'];
        $row['zei_prc_8']           = $data['zei_prc_8'];
        // 印紙税金額
        $row['inshi_zei_kbn']       = $inshi_kbn;
        $row['inshi_zei_prc']       = $inshi_prc;
        $row['inshi_type_cd']       = $data['inshi_type_cd'];
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('ryosyusho_history', $row);
        $cnt = $db->easyExecute($sql, $param);
        return $row;        
    }
    /**
     * 入金金額振り分け
     * 
     * @param type $db
     * @param type $data
     */
    private function setNyukinInfo(&$data) {
        // 項目初期化
        $data['furikomi_prc_hikazei'] = 0;
        $data['furikomi_prc_keigen']  = 0;
        $data['furikomi_prc_3']       = 0;
        $data['furikomi_prc_5']       = 0;
        $data['furikomi_prc_8']       = 0;
        $data['furikomi_prc']         = 0;
        $data['genkin_prc_hikazei']   = 0;
        $data['genkin_prc_keigen']    = 0;
        $data['genkin_prc_3']         = 0;
        $data['genkin_prc_5']         = 0;
        $data['genkin_prc_8']         = 0;
        $data['genkin_prc']           = 0;
        $data['credit_prc_hikazei']   = 0;
        $data['credit_prc_keigen']    = 0;
        $data['credit_prc_3']         = 0;
        $data['credit_prc_5']         = 0;
        $data['credit_prc_8']         = 0;
        $data['credit_prc']           = 0;
        $data['j_debit_prc_hikazei']  = 0;
        $data['j_debit_prc_keigen']   = 0;
        $data['j_debit_prc_3']        = 0;
        $data['j_debit_prc_5']        = 0;
        $data['j_debit_prc_8']        = 0;
        $data['j_debit_prc']          = 0;
        $data['zei_prc_std']          = 0;
        $data['zei_prc_keigen']       = 0;
        $data['zei_prc_3']            = 0;
        $data['zei_prc_5']            = 0;
        $data['zei_prc_8']            = 0;
        $nyukin_prc = $data['nyukin_prc'];
        foreach ($nyukin_prc as $prc) {
            $genkin   = 0;
            $furikomi = 0;
            $credit   = 0;
            $jdebit   = 0;
            $zei_gaku = 0;
            // 入金方法振り分け
            if($data['pay_method_cd'] == '1'){
                // 振込
                $furikomi = $prc['sum_prc']; 
            }else if($data['pay_method_cd'] == '2'){
                // 現金
                $genkin = $prc['sum_prc'];
            }else if($data['pay_method_cd'] == '4'){
                // クレジットカード/デビットカード
                $credit   = $prc['sum_prc'];
            }else if($data['pay_method_cd'] == '5'){
                // ジェイデビットカード
                $jdebit   = $prc['sum_prc'];
            }
            // 個別供花の明細単位で取得するものは消費税を設定
            if(isset($prc['out_zei_prc'])){
                $zei_gaku = $prc['out_zei_prc'];
            }else if(isset($prc['in_zei_prc'])){
                $zei_gaku = $prc['in_zei_prc'];
            }else{
                // 消費税の計算
                $TaxPrc = App_ClsTaxLib::CalcTax($prc['sum_prc'], 1, $prc['zei_rtu'], $prc['zei_hasu_kbn']);
                $zei_gaku = $TaxPrc['ZeiPrc'];
            }
            // 税率振り分け
            if ($prc['zei_cd'] == 0){
                // 非課税
                $data['furikomi_prc_hikazei'] = $furikomi;
                $data['genkin_prc_hikazei']   = $genkin;
                $data['credit_prc_hikazei']   = $credit;
                $data['j_debit_prc_hikazei']  = $jdebit;
            }else if($prc['reduced_tax_rate'] == 2){
                // 軽減税率
                $data['furikomi_prc_keigen']  = $furikomi;
                $data['genkin_prc_keigen']    = $genkin;
                $data['credit_prc_keigen']    = $credit;
                $data['j_debit_prc_keigen']   = $jdebit;
                $data['zei_prc_keigen']       = $zei_gaku;
            }else if($prc['zei_cd'] == 1){
                // 税率3%
                $data['furikomi_prc_3']       = $furikomi;
                $data['genkin_prc_3']         = $genkin;
                $data['credit_prc_3']         = $credit;
                $data['j_debit_prc_3']        = $jdebit;
                $data['zei_prc_3']            = $zei_gaku;
            }else if($prc['zei_cd'] == 2){
                // 税率5%
                $data['furikomi_prc_5']       = $furikomi;
                $data['genkin_prc_5']         = $genkin;
                $data['credit_prc_5']         = $credit;
                $data['j_debit_prc_5']        = $jdebit;
                $data['zei_prc_5']            = $zei_gaku;
            }else if($prc['zei_cd'] == 3){
                // 税率8%
                $data['furikomi_prc_8']       = $furikomi;
                $data['genkin_prc_8']         = $genkin;
                $data['credit_prc_8']         = $credit;
                $data['j_debit_prc_8']        = $jdebit;
                $data['zei_prc_8']            = $zei_gaku;
            }else{
                // 標準税率
                $data['furikomi_prc']         = $furikomi;
                $data['genkin_prc']           = $genkin;
                $data['credit_prc']           = $credit;
                $data['j_debit_prc']          = $jdebit;
                $data['zei_prc_std']          = $zei_gaku;
            }
        }
    }
    /**
     * 
     * 請求明細の大分類毎の金額を取得する
     * 
     * @param type $db
     * @param type $seikyu_den_no
     * @return type
     */
    private function getSeikyuMsiData($db, $seikyu_den_no, $data_kbn = 1){
        $order_by = "";
        if($data_kbn == 2){
            $order_by = "ORDER BY T3.dai_bunrui_cd DESC";
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT 
     T3.seikyu_den_no
    ,T3.seko_no
    ,T3.dai_bunrui_cd
    ,T3.tadashikaki
    ,SUM(T3.uri_prc) AS uri_prc
    ,T3.ryosyu_check
    ,T3.rs_print_kbn
    FROM (
        SELECT
             T2.seikyu_den_no
            ,T2.seko_no
            ,CASE T2.dai_bunrui_cd
                WHEN '0080' THEN T2.dai_bunrui_cd
                WHEN '0110' THEN T2.dai_bunrui_cd
                WHEN '0100' THEN T2.dai_bunrui_cd
                ELSE '0010'
             END dai_bunrui_cd
            ,CASE T2.dai_bunrui_cd
                WHEN '0080' THEN '供物代'
                WHEN '0110' THEN '御法事代'
                WHEN '0100' THEN null 
                ELSE 'ご葬儀代'
             END tadashikaki
            ,T2.uri_prc
            ,T2.ryosyu_check
            ,T2.rs_print_kbn
        FROM (
            SELECT
                 T.seikyu_den_no
                ,T.seko_no
                ,T.dai_bunrui_cd
                ,T.tadashikaki
                ,SUM(CASE 
                        WHEN T.dai_bunrui_cd IN('0010','0110') THEN 
                            T.uri_prc  
                            + COALESCE(sdm2.uri_prc, 0) -- 0010:葬送儀礼、0110:法事代から特別値引を引く
                            + COALESCE(sgm1.zangaku, 0) -- 用途区分:1 の互助会関係の金額
                            + COALESCE(sgm2.zangaku, 0) -- 用途区分:3 の互助会関係の金額
                        ELSE T.uri_prc
                 END)    AS uri_prc
                ,T.ryosyu_check
                ,T.rs_print_kbn
            FROM (
                SELECT 
                     sdm.seikyu_den_no
                    ,sdm.seko_no
                    ,sdm.dai_bunrui_cd
                    ,COALESCE(ussi.ryosyu_prc,COALESCE(SUM(sdm.uri_prc + sdm.out_zei_prc + sdm.gojokai_nebiki_prc + sdm.nebiki_prc),0)) AS uri_prc
                    ,CASE sdm.dai_bunrui_cd 
                        WHEN '0010' THEN 'ご葬儀代'
                        WHEN '0020' THEN '返礼品代'
                        WHEN '0030' THEN 'お料理代'
                        WHEN '0050' THEN '別途費用代'
                        WHEN '0060' THEN '立替費用代'
                        WHEN '0080' THEN '供物代'
                        WHEN '0110' THEN '御法事代'
                        WHEN '0100' THEN CASE sd.v_free10 WHEN '100' THEN '生花代' ELSE null END
                        ELSE null 
                     END tadashikaki
                    ,COALESCE(udm.k_free1, 0) AS ryosyu_check -- NULL は 0　にする
                    ,CASE sd.data_kbn WHEN 3 THEN null ELSE ussi.rs_print_kbn END AS rs_print_kbn -- アフターを一つにまとめる為
                FROM seikyu_denpyo_msi sdm 
                LEFT JOIN uriage_denpyo_msi udm
                ON  udm.seko_no    = sdm.seko_no
                AND udm.n_free1    = sdm.n_free1
                AND udm.delete_flg = 0
                LEFT JOIN uriage_sekyu_saki_info ussi
                ON  ussi.uri_den_no = udm.uri_den_no
                AND ussi.delete_flg = 0
                LEFT JOIN shohin_dai_bunrui_mst dai
                ON  dai.dai_bunrui_cd = sdm.dai_bunrui_cd
                AND dai.delete_flg    = 0
                LEFT JOIN seikyu_denpyo sd
                ON  sd.seikyu_den_no = sdm.seikyu_den_no
                AND sd.delete_flg    = 0
                WHERE 
                    sdm.seikyu_den_no = :seikyu_den_no
                AND sdm.uri_prc   <> 0
                AND sdm.dai_bunrui_cd NOT IN('0070') -- 特別値引以外を取得
                AND sdm.delete_flg = 0
                GROUP BY
                     sdm.seikyu_den_no
                    ,sdm.seko_no
                    ,sdm.dai_bunrui_cd
                    ,dai.dai_bunrui_nm
                    ,udm.k_free1
                    ,ussi.rs_print_kbn
                    ,ussi.ryosyu_prc
                    ,sd.v_free10 
                    ,sd.data_kbn
                ORDER BY 
                     sdm.seikyu_den_no
                    ,sdm.dai_bunrui_cd
            ) T
            LEFT JOIN (
                -- 特別値引を取得
                SELECT 
                     seikyu_den_no
                    ,SUM(uri_prc + out_zei_prc + gojokai_nebiki_prc + nebiki_prc) AS uri_prc
                FROM seikyu_denpyo_msi
                WHERE seikyu_den_no = :seikyu_den_no
                AND dai_bunrui_cd IN('0070')
                AND delete_flg      = 0
                GROUP BY 
                    seikyu_den_no
            ) sdm2
            ON sdm2.seikyu_den_no = T.seikyu_den_no
            -- 用途区分:1 の互助会関係の金額
            LEFT JOIN (
                SELECT 
                    SUM(
                          COALESCE(sgm.keiyaku_gaku, 0)
                        - COALESCE(sgm.harai_gaku, 0)
                        - COALESCE(sgm.wari_gaku, 0)
                        - COALESCE(sgm.n_free3, 0) -- premium_gaku
                        - COALESCE(sgm.n_free4, 0) -- kannnou_gaku
                        - COALESCE(sgm.waribiki_gaku, 0)
                        + COALESCE(sgm.kanyu_tax, 0)
                        + COALESCE(sgm.early_use_cost, 0)
                        + COALESCE(sgm.early_use_cost_zei, 0)
                        + COALESCE(sgm.meigi_chg_cost, 0)
                        + COALESCE(sgm.meigi_chg_cost_zei, 0)
                    ) AS zangaku
                    ,sd.seikyu_den_no
                FROM seikyu_denpyo sd
                LEFT JOIN seko_gojokai_member sgm
                ON  sgm.seko_no    = sd.seko_no
                AND sgm.yoto_kbn   = 1
                AND sgm.delete_flg = 0
                WHERE sd.seikyu_den_no = :seikyu_den_no
                AND sd.delete_flg = 0
                GROUP BY sd.seikyu_den_no
            ) sgm1
            ON sgm1.seikyu_den_no = T.seikyu_den_no
            -- 用途区分:3 の互助会関係の金額
            LEFT JOIN (
                SELECT 
                    SUM(
                         (COALESCE(sgm.harai_gaku, 0) * -1)
                        + COALESCE(sgm.meigi_chg_cost, 0)
                        + COALESCE(sgm.meigi_chg_cost_zei, 0)
                    ) AS zangaku
                    ,sd.seikyu_den_no
                FROM seikyu_denpyo sd
                LEFT JOIN seko_gojokai_member sgm
                ON  sgm.seko_no    = sd.seko_no
                AND sgm.yoto_kbn   = 3
                AND sgm.delete_flg = 0
                WHERE sd.seikyu_den_no = :seikyu_den_no
                AND sd.delete_flg = 0
                GROUP BY sd.seikyu_den_no
            ) sgm2
            ON sgm2.seikyu_den_no = T.seikyu_den_no
            WHERE T.rs_print_kbn IS NULL OR T.rs_print_kbn IN (1) -- 領収証発行：要 OR NULL
            GROUP BY 
                 T.seikyu_den_no
                ,T.seko_no
                ,T.dai_bunrui_cd
                ,T.tadashikaki
                ,T.ryosyu_check
                ,T.rs_print_kbn
        ) T2
    ) T3
    GROUP BY 
         T3.seikyu_den_no
        ,T3.seko_no
        ,T3.dai_bunrui_cd
        ,T3.tadashikaki
        ,T3.ryosyu_check
        ,T3.rs_print_kbn
    $order_by
END_OF_SQL
            , array('seikyu_den_no' => $seikyu_den_no));
        // 葬送儀礼の存在確認　チェックOFFのもの かつ 領収証発行が要で葬送儀礼が無い場合の対応
        $sousou_count = 0;
        foreach ($select as $value3) {
            // 0010:葬送儀礼 OR 0110:法要
            if($value3['dai_bunrui_cd'] == '0010' || $value3['dai_bunrui_cd'] == '0110'){
                $sousou_count++;
            }
        }
        $tmp_ary = array();
        // 供花供物の、チェックOFFのもの かつ 領収証発行が要のもの を葬儀に入れる
        $delkey = null;
        foreach ($select as $key => $value) {
            if($value['dai_bunrui_cd'] == '0080'){
                if($value['ryosyu_check'] == 0 && $value['rs_print_kbn'] == 1){
                    $tmp_ary = $value;
                    $delkey = $key;
                    break;
                }
            }
        }
        // 対象の供花供物または葬送儀礼が無い場合はスルー
        if(isset($delkey) && $sousou_count > 0){
            unset($select[$key]); // 削除
            // 喪家供花の個別発行OFFの場合
            $select_tmp = $select;
            foreach ($select_tmp as $key2 => $value2) {
                // 0010:葬送儀礼 OR 0110:法要
                if($value2['dai_bunrui_cd'] == '0010' || $value2['dai_bunrui_cd'] == '0110'){
                    $select[$key2]['uri_prc']  += $tmp_ary['uri_prc'];
                    $select[$key2]['kyoka_flg'] = true;
                }
            }
        }
        return $select;
    }
    /**
     * 
     * 売上明細の大分類毎の金額を取得する
     * 
     * @param type $db
     * @param type $uri_den_no
     * @return type
     */
    private function getUriageMsiData($db, $uri_den_no){
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT
             T.uri_den_no
            ,T.dai_bunrui_cd
            ,T.dai_bunrui_nm
            ,T.tadashikaki
            ,SUM(CASE
                    WHEN T.dai_bunrui_cd IN('0010','0110') THEN 
                        T.uri_prc  
                        + COALESCE(udm2.uri_prc, 0) -- 0010:葬送儀礼、0110:法事代から特別値引を引く
                        + COALESCE(sgm1.zangaku, 0) -- 用途区分:1 の互助会関係の金額
                        + COALESCE(sgm2.zangaku, 0) -- 用途区分:3 の互助会関係の金額
                    ELSE T.uri_prc
             END)    AS uri_prc
            ,T.ryosyu_check
        FROM (
            SELECT 
                 udm.uri_den_no
                ,udm.dai_bunrui_cd
                ,dai.dai_bunrui_nm
                ,COALESCE(SUM(udm.uri_prc + udm.out_zei_prc + udm.gojokai_nebiki_prc + udm.nebiki_prc),0) AS uri_prc
                ,CASE udm.dai_bunrui_cd 
                    WHEN '0010' THEN 'ご葬儀代'
                    WHEN '0020' THEN '返礼品代'
                    WHEN '0030' THEN 'お料理代'
                    WHEN '0050' THEN '別途費用代'
                    WHEN '0060' THEN '立替費用代'
                    WHEN '0080' THEN '供物代'
                    WHEN '0110' THEN '御法事代'
                    WHEN '0100' THEN CASE ud.v_free10 WHEN '100' THEN '生花代' ELSE null END
                    ELSE null 
                 END tadashikaki
                ,udm.k_free1 AS ryosyu_check
            FROM uriage_denpyo_msi udm 
            LEFT JOIN shohin_dai_bunrui_mst dai
            ON  dai.dai_bunrui_cd = udm.dai_bunrui_cd
            AND dai.delete_flg    = 0
            LEFT JOIN uriage_denpyo ud
            ON  ud.uri_den_no = udm.uri_den_no
            AND ud.delete_flg = 0
            WHERE 
                udm.uri_den_no = :uri_den_no
            AND udm.uri_prc   <> 0
            AND udm.dai_bunrui_cd NOT IN('0070') -- 特別値引以外を取得
            AND udm.delete_flg = 0
            GROUP BY
                 udm.uri_den_no
                ,udm.dai_bunrui_cd
                ,dai.dai_bunrui_nm
                ,udm.k_free1
                ,ud.v_free10
            ORDER BY 
                 udm.uri_den_no
                ,udm.dai_bunrui_cd
        ) T
        LEFT JOIN (
            -- 特別値引を取得
            SELECT 
                 uri_den_no
                ,SUM(uri_prc + out_zei_prc + gojokai_nebiki_prc + nebiki_prc) AS uri_prc
            FROM uriage_denpyo_msi
            WHERE uri_den_no = :uri_den_no
            AND dai_bunrui_cd IN('0070')
            AND delete_flg   = 0
            GROUP BY 
                uri_den_no
        ) udm2
        ON udm2.uri_den_no = T.uri_den_no
        -- 用途区分:1 の互助会関係の金額
        LEFT JOIN (
            SELECT 
                SUM(
                      COALESCE(sgm.keiyaku_gaku, 0)
                    - COALESCE(sgm.harai_gaku, 0)
                    - COALESCE(sgm.wari_gaku, 0)
                    - COALESCE(sgm.n_free3, 0) -- premium_gaku
                    - COALESCE(sgm.n_free4, 0) -- kannnou_gaku
                    - COALESCE(sgm.waribiki_gaku, 0)
                    + COALESCE(sgm.kanyu_tax, 0)
                    + COALESCE(sgm.early_use_cost, 0)
                    + COALESCE(sgm.early_use_cost_zei, 0)
                    + COALESCE(sgm.meigi_chg_cost, 0)
                    + COALESCE(sgm.meigi_chg_cost_zei, 0)
                ) AS zangaku
                ,ud.uri_den_no
            FROM uriage_denpyo ud
            LEFT JOIN seko_gojokai_member sgm
            ON  sgm.seko_no    = ud.seko_no
            AND sgm.yoto_kbn   = 1
            AND sgm.delete_flg = 0
            WHERE ud.uri_den_no = :uri_den_no
            AND ud.delete_flg = 0
            AND ud.data_kbn   = 1
            GROUP BY ud.uri_den_no
        ) sgm1
        ON sgm1.uri_den_no = T.uri_den_no
        -- 用途区分:3 の互助会関係の金額
        LEFT JOIN (
            SELECT 
                SUM(
                     (COALESCE(sgm.harai_gaku, 0) * -1)
                    + COALESCE(sgm.meigi_chg_cost, 0)
                    + COALESCE(sgm.meigi_chg_cost_zei, 0)
                ) AS zangaku
                ,ud.uri_den_no
            FROM uriage_denpyo ud
            LEFT JOIN seko_gojokai_member sgm
            ON  sgm.seko_no    = ud.seko_no
            AND sgm.yoto_kbn   = 3
            AND sgm.delete_flg = 0
            WHERE ud.uri_den_no = :uri_den_no
            AND ud.delete_flg = 0
            AND ud.data_kbn   = 1
            GROUP BY ud.uri_den_no
        ) sgm2
        ON sgm2.uri_den_no = T.uri_den_no
        GROUP BY 
             T.uri_den_no
            ,T.dai_bunrui_cd
            ,T.dai_bunrui_nm
            ,T.tadashikaki
            ,T.ryosyu_check
END_OF_SQL
            , array('uri_den_no' => $uri_den_no));
        return $select;
    }
    /**
     * 
     * 請求明細の大分類毎の金額を取得する（合算用）
     * 
     * @param type $db
     * @param type $seikyu_den_no
     * @return type
     */    
    private function getBunGasSeikyuMsiData($db, $seikyu_den_no){
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT
             seikyu.ryosyusyo_meigi
            ,sdm.dai_bunrui_cd
            ,CASE 
                WHEN sdm.dai_bunrui_cd IN('0010','0110') THEN 
                     sd.seikyu_zan + sd.nyukin_prc + COALESCE(sgm1.zangaku, 0) + COALESCE(sgm2.zangaku, 0)
                ELSE sd.seikyu_zan + sd.nyukin_prc
             END                AS uri_prc
            ,CASE sdm.dai_bunrui_cd 
                WHEN '0010' THEN 'ご葬儀代'
                WHEN '0020' THEN '返礼品代'
                WHEN '0030' THEN 'お料理代'
                WHEN '0050' THEN '別途費用代'
                WHEN '0060' THEN '立替費用代'
                WHEN '0080' THEN '供物代'
                WHEN '0110' THEN '御法事代'
                WHEN '0100' THEN CASE sd.v_free10 WHEN '100' THEN '生花代' ELSE null END
                ELSE null 
             END tadashikaki
            ,NULL                           AS ryosyu_check
        FROM seikyu_denpyo sd
        LEFT JOIN seikyu_sekyu_saki_info seikyu
        ON  seikyu.seikyu_den_no = sd.seikyu_den_no
        AND seikyu.delete_flg    = 0
        LEFT JOIN (
            -- １行目の大分類を取得
            SELECT seikyu_den_no,dai_bunrui_cd
            FROM seikyu_denpyo_msi 
            WHERE seikyu_den_no = :seikyu_den_no
            ORDER BY msi_no
            LIMIT 1
        ) sdm
        ON  sdm.seikyu_den_no = sd.seikyu_den_no
        -- 用途区分:1 の互助会関係の金額
        LEFT JOIN (
            SELECT 
                SUM(
                      COALESCE(sgm.keiyaku_gaku, 0)
                    - COALESCE(sgm.harai_gaku, 0)
                    - COALESCE(sgm.wari_gaku, 0)
                    - COALESCE(sgm.n_free3, 0) -- premium_gaku
                    - COALESCE(sgm.n_free4, 0) -- kannnou_gaku
                    - COALESCE(sgm.waribiki_gaku, 0)
                    + COALESCE(sgm.kanyu_tax, 0)
                    + COALESCE(sgm.early_use_cost, 0)
                    + COALESCE(sgm.early_use_cost_zei, 0)
                    + COALESCE(sgm.meigi_chg_cost, 0)
                    + COALESCE(sgm.meigi_chg_cost_zei, 0)
                ) AS zangaku
                ,sd.seikyu_den_no
            FROM seikyu_denpyo sd
            LEFT JOIN seko_gojokai_member sgm
            ON  sgm.seko_no    = sd.seko_no
            AND sgm.yoto_kbn   = 1
            AND sgm.delete_flg = 0
            WHERE sd.seikyu_den_no = :seikyu_den_no
            AND sd.delete_flg = 0
            GROUP BY sd.seikyu_den_no
        ) sgm1
        ON sgm1.seikyu_den_no = sd.seikyu_den_no
        -- 用途区分:3 の互助会関係の金額
        LEFT JOIN (
            SELECT 
                SUM(
                     (COALESCE(sgm.harai_gaku, 0) * -1)
                    + COALESCE(sgm.meigi_chg_cost, 0)
                    + COALESCE(sgm.meigi_chg_cost_zei, 0)
                ) AS zangaku
                ,sd.seikyu_den_no
            FROM seikyu_denpyo sd
            LEFT JOIN seko_gojokai_member sgm
            ON  sgm.seko_no    = sd.seko_no
            AND sgm.yoto_kbn   = 3
            AND sgm.delete_flg = 0
            WHERE sd.seikyu_den_no = :seikyu_den_no
            AND sd.delete_flg = 0
            GROUP BY sd.seikyu_den_no
        ) sgm2
        ON sgm2.seikyu_den_no = sd.seikyu_den_no
        WHERE sd.seikyu_den_no = :seikyu_den_no
        AND sd.delete_flg      = 0
END_OF_SQL
            , array('seikyu_den_no' => $seikyu_den_no));
        return $select;
    }
    
    
    /**
     * 
     * 喪家供物供花取得
     * 
     * @param type $db
     * @param type $seko_no
     */
    private function getKumotsu($db, $seko_no){
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
             sdm.seko_no
            ,sdm.seikyu_den_no
            ,sdm.k_free1
            ,sdm.n_free1
            ,udm.uri_den_no
            ,ussi.ryosyusyo_meigi
            ,CASE WHEN ussi.ryosyu_prc IS NOT NULL THEN ussi.ryosyu_prc
                  ELSE COALESCE(ud.uri_prc_sum, 0) + COALESCE(ud.uri_nebk_sum, 0) + COALESCE(ud.uri_hepn_sum, 0) + COALESCE(ud.out_zei_prc, 0) + COALESCE(ud.out_zei_prc_keigen, 0)
             END ryosyu_prc 
            ,sdm.zei_cd
        FROM seikyu_denpyo_msi sdm
        LEFT JOIN seikyu_denpyo sd
        ON  sd.seikyu_den_no = sdm.seikyu_den_no
        AND sd.delete_flg    = 0
        LEFT JOIN uriage_denpyo_msi udm
        ON  udm.seko_no    = sdm.seko_no
        AND udm.n_free1    = sdm.n_free1
        AND udm.delete_flg = 0
        LEFT JOIN uriage_sekyu_saki_info ussi
        ON  ussi.uri_den_no = udm.uri_den_no
        AND ussi.delete_flg = 0
        LEFT JOIN uriage_denpyo ud
        ON  ud.uri_den_no = udm.uri_den_no
        AND ud.delete_flg = 0
        WHERE 
            sdm.delete_flg = 0
        AND sdm.seko_no    = :seko_no
        AND udm.k_free1    = 1
        AND ussi.rs_print_kbn = 1
        AND COALESCE(sd.juchusaki_kbn,0) NOT IN(2)
END_OF_SQL
            , array('seko_no' => $seko_no));
        return $select;
    }
    
    private function getMoushiKbnNm($db, $data){
        $kbn = 0;
        if($data['data_kbn'] == "1" && $data['moushi_kbn'] == "1") {
            $kbn = 1;
        }else if($data['data_kbn'] == "1" && $data['moushi_kbn'] == "5"){
            $kbn = 5;
        }else if($data['data_kbn'] == "1" && $data['moushi_kbn'] == "6"){
            $kbn = 6;
        }else if($data['data_kbn'] == "1" && $data['moushi_kbn'] == "7"){
            $kbn = 7;
        }else if($data['data_kbn'] == "1" && $data['moushi_kbn'] == "8"){
            $kbn = 8;
        }else if($data['data_kbn'] == "1" && $data['moushi_kbn'] == "9"){
            $kbn = 9;
        }else if($data['data_kbn'] == "1" && $data['moushi_kbn'] == "10"){
            $kbn = 10;
        }else if($data['data_kbn'] == "2"){
            $kbn = 2;
        }else if($data['data_kbn'] == "3"){
            $kbn = 3;
        }else if($data['data_kbn'] == "4"){
            $kbn = 4;
        }else if($data['data_kbn'] == "20"){
            $kbn = 20;
        }else if($data['data_kbn'] == "21"){
            $kbn = 21;
        }else if($data['data_kbn'] == "22"){
            $kbn = 22;
        }
        $code_kbn = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8556', 'kbn_value_cd_num' => $kbn));
        if(Msi_Sys_Utils::myCount($code_kbn) > 0){
            return $code_kbn[0]['kbn_value_lnm'];
        }
        return "";
    }
    /**
     * 
     * 領収証出力状況を「2:一括発行済」に更新
     * 
     * @param type $db
     * @param type $nyukin_den_no
     */
    private function saveRyosyuPrtStatus($db ,$nyukin_den_no) {
        $nyukin = DataMapper_NyukinDenpyo::findOne($db , array("denpyo_no" => $nyukin_den_no));
        if(Msi_Sys_Utils::myCount($nyukin) > 0){
            if($nyukin['ryosyu_prt_status'] == 1){
                throw new Msi_Sys_Exception_InputException("個別発行済みのデータが含まれています。再度検索を行ってください。");
            }
        }
        // 更新
        $sql = "
            UPDATE 
                nyukin_denpyo 
            SET 
                ryosyu_prt_status = 2  -- 領収証出力状況
            WHERE 
                denpyo_no = :denpyo_no
        ";
        $cnt = $db->easyExecute($sql, array('denpyo_no' => $nyukin_den_no));
    }
    /**
     * 
     * BDLファイル出力
     * 
     * @param type $params
     * @param type $dataCol
     * @return type
     */
    private function dblFileOut($params, $dataCol){
        $isBdlStyle  = isset($params['dl_type']) && $params['dl_type'] == 'bdl';
        // 出力設定
        $opArr = array( 'isTitleOut' => true, 'isTitle2Out' => false ); // タイトル行出力可否
        $ryosyu_rule = static::_getOutputRule('ryosyu', $opArr);
        // 一時ファイルで出力
        $tempfile = App_Utils::genCsvFile( $dataCol, $ryosyu_rule );
        // ファイル名
         $fileTs = Msi_Sys_Utils::getDatetime(null, 'YmdHis');
        $filename = $ryosyu_rule['file_prefix'] . $fileTs . '.csv';
        $bdl_filename = $fileTs . '.bdl';
        $files = array();
        $files[] = array( 'path' => $filename, 'file' => $tempfile );
        //$outdata = Msi_Sys_Utils::genBdlFileData($files, 'ryoshudbl');
        // 複数ファイル出力準備
        //Msi_Sys_Utils::out2wayPush($outdata, $bdl_filename, 'application/x-msi-bdl');
        return $files;
    }
    
    /**
     * CSV出力設定情報
     *
     * <AUTHOR> Sugiyama
     * @since      2020/12/xx
     * @param  string  キー名
     * @param  array   上書きする値
     * @return array   CSV出力設定情報
     */    
    private static function _getOutputRule($key, $overwriteOp=array()) {
        static $_outputRule = null;
        if ( $_outputRule === null ) {
            $_outputRule  = array(
                                'ryosyu' => array(
                                                'file_prefix'   => '領収証郵送明細チェックリスト',
                                                'isTitleOut'    => true, // タイトル行を設定するか
                                                'isTitle2Out'   => true, // 項目キー名(開発用)
                                                'output_fields' =>  Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
seikyu_den_no ryosyusho_no moushi_kbn_nm ryosyu_prc seko_juchu_no est_shikijo_nm sougi_ymd k_nm 
ryosyu_soufu_nm ryosyu_soufu_yubin_no ryosyu_soufu_addr1 ryosyu_soufu_addr2 ryosyu_meigi ryosyu_irai_kbn_nm
meisai temp ryosyu_youshiki ryosyu_file meisai_yoshiki meisai_file nyukin_ymd oya_bumon_cd
END_OF_TXT
                                                ),
                                                // key_title_map: この対応情報があればタイトル項目名を置き換えます
                                                // 対応がない場合は、タイトル項目名として項目キー名をそのまま使います
                                                'key_title_map' => array( 
                                                        'seikyu_den_no'         => '請求番号',
                                                        'ryosyusho_no'          => '領収番号',
                                                        'moushi_kbn_nm'         => '申込区分',
                                                        'ryosyu_prc'            => '領収金額',
                                                        'seko_juchu_no'         => '施行番号',
                                                        'est_shikijo_nm'        => '式場',
                                                        'sougi_ymd'             => '施行日',
                                                        'k_nm'                  => '故人名',
                                                        'ryosyu_soufu_nm'       => '領収証送付先 氏名',
                                                        'ryosyu_soufu_yubin_no' => '領収証送付先 郵便番号',
                                                        'ryosyu_soufu_addr1'    => '領収証送付先 住所1',
                                                        'ryosyu_soufu_addr2'    => '領収証送付先 住所2',
                                                        'ryosyu_meigi'          => '領収証名義',
                                                        'ryosyu_irai_kbn_nm'    => '領収証依頼区分',
                                                        'meisai'                => '明細',
                                                        'temp'                  => '見積書・納品書添付',
                                                        'ryosyu_youshiki'       => '領収証様式',
                                                        'ryosyu_file'           => '領収証ファイル名',
                                                        'meisai_yoshiki'        => '明細様式',
                                                        'meisai_file'           => '明細ファイル名',
                                                        'nyukin_ymd'            => '入金日（領収日）',
                                                        'oya_bumon_cd'          => '会社コード',
                                                ),
                                ),
                            );
        }

        if ( !isset($_outputRule[$key]) ) {
            throw new Exception( " $key が不適切です" );
        }
        $rtn = array_merge($_outputRule[$key], $overwriteOp);
        return $rtn;        
    }
    /**
     * 
     * 発行済領収証の金額を取得
     * 
     * @param type $db
     * @param type $seikyu_den_no
     * @return type
     */
    private function getRyoshuData($db, $seikyu_den_no, $data) {
        $table_name = "ryosyusho_history";
        if(isset($data['seikyu_den_no'])){
            $table_name = "ryosyusho_history_uc";
        }
        $seikyu = $db->easySelOne( <<< END_OF_SQL
SELECT 
   COALESCE (SUM(gokei_prc), 0) AS nyukin_prc
FROM $table_name
WHERE uri_den_no = :seikyu_den_no
  AND hako_kbn NOT IN ( 9, 1)
  AND delete_flg = 0
END_OF_SQL
            , array( 'seikyu_den_no' => $seikyu_den_no ) );
        return $seikyu;
    }
    /**
     * 合計金額データを表示用に変換して返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx   RyoshuPdfからコピー
     * @params integer   $num
     * @return string    表示用文字列
     */
    protected static function _filterKingakuHyphen($num)
    {
        $yen   = mb_convert_encoding(pack("H*", "FFE5"), "UTF-8", "UTF-16"); // 00A5:yen, FFE5:full-width yen
        $trail = mb_convert_encoding(pack("H*", "FF0D"), "UTF-8", "UTF-16"); // 002D:hyphen, FF0D:full-width hyphen
        $str = sprintf('%s%s%s', $yen, Msi_Sys_Utils::filterComma($num), $trail);
        return $str;
    }
    /**
     * 
     * 領収証名義取得
     * 
     * @param type $db
     * @param type $seko_no
     * @return type
     */
    private function getRyosyuMeigi($db, $seko_no){
        $ryosyu_meigi = "";
        $select = $db->easySelOne(<<< END_OF_SQL
    SELECT 
         ski.seko_no
        ,ssi.ryosyu_meigi
    FROM seko_kihon_info ski
    LEFT JOIN sekyu_saki_info ssi
    ON ssi.sekyu_cd    = ski.sekyu_cd
    AND ssi.delete_flg = 0
    WHERE ski.seko_no = :seko_no     
END_OF_SQL
                    , array('seko_no' => $seko_no));
        if(Msi_Sys_Utils::myCount($select) > 0) {
            $ryosyu_meigi = $select['ryosyu_meigi'];
        }
        return $ryosyu_meigi;
    }
    /**
     * 
     * 8551:印紙税納付税務署
     * 
     * @param type $db
     * @param type $bumon_cd
     */
    private function getInshiCodeKbn($db, $bumon_cd){
        $select = $db->easySelect(<<< END_OF_SQL
SELECT 
    kbn_value_cd
FROM 
    code_nm_mst
WHERE 
    code_kbn     = '8551'
AND TRIM(kbn_value_lnm) = :bumon_cd
AND delete_flg   = 0
END_OF_SQL
            , array('bumon_cd' => $bumon_cd)
        );
        return $select;        
    }
}