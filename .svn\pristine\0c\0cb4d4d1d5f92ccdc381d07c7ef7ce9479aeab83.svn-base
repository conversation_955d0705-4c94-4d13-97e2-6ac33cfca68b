<?php

//require_once __DIR__. '/JuchuhenkoAbstract.php';

/**
 * Juchu_JuchuhenkoSubAbstract
 *
 * 受注変更 サブ抽象クラス（葬送儀礼～値引き）
 *
 * @category   App
 * @package    controllers\Juchu\JuchuhenkoSubAbstract
 * <AUTHOR> Sai
 * @since      2014/01/17
 * @version    2019/10/xx mihara 軽減税率対応
 * @filesource 
 */

/**
 * 受注変更 サブ抽象クラス（葬送儀礼～値引き）
 *
 * @category   App
 * @package    controllers\Juchu\JuchuhenkoSubAbstract
 * <AUTHOR>
 * @since      2014/01/17
 */
abstract class Juchu_JuchuhenkoSubAbstract extends Juchu_JuchuhenkoAbstract {

    /**
     *
     * 商品区分大分類コードを取得
     *
     * <AUTHOR> Sai
     * @since 2014/1/27
     * @return string 大分類コード
     */
    abstract public function getDaibunruiCd();

    /**
     *
     * 商品区分中分類条件を取得(葬送儀礼のみサブクラスで条件を取得するようにオーバーライド)
     *
     * <AUTHOR> Sai
     * @since 2014/3/27
     * @return string 中分類条件
     */
    public function getChubunruiWhere() {
        return '';
    }

    /**
     *
     * 施行管理情報の項目番号を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/05/09
     * @return string 項目番号
     */
    abstract public function getItemno();

    /**
     *
     * 目的区分を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/28
     * @return string 目的区分
     */
//    abstract public function getMokutekiKbn();

    /**
     *
     * データ種別を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/30
     * @return string データ種別
     */
    abstract public function getDataSbt();

    /**
     *
     * データ種別を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/30
     * @return string データ種別
     */
    public function getDataSbt2() {
        return $this->getDataSbt();
    }

    /**
     * 初期情報取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/01/20
     * @param string $sekoNo 施行番号
     * @return array jsonData
     */
    public function getInitData() {

        //$params = Msi_Sys_Utils::webInputs();
        //Msi_Sys_Utils::debug('params==>' . Msi_Sys_Utils::dump($params));
        //
        $this->_sekoNo = $this->getReqSekoNo();
        // 施行基本情報を設定する
        $this->setInitParam();
        // 奉仕率コードと奉仕率を取得する
        $hoshi = $this->getHosiritu();

        $dataApp = array();
        $dataApp["seko_no"] = $this->_sekoNo;
        $dataApp["moushi_kbn"] = $this->_moushiKbn;
        $dataApp["seko_plan_cd"] = $this->_sekoPlanCd;
        $dataApp["gojokai_cose_cd"] = $this->_gojokaiCoseCd;
        $dataApp["gojokai_kbn"] = $this->_gojokaiKbn;
        $dataApp["bumon_cd"] = $this->_bumonCd;
        $dataApp["seko_tanto_cd"] = $this->_sekoTantoCd;
        $dataApp["jichu_kakute_ymd"] = $this->_juchuKakuteiYMD;
        $dataApp["hoshi_ritu"] = $hoshi['zei_rtu'];
        $dataApp["hoshi_ritu_cd"] = $hoshi['hoshi_ritu_cd'];
        $dataApp["hasu_kbn"] = $hoshi['hasu_kbn'];
        $dataApp["kakutei_info"] = $this->_selectSekoKihon['kakutei_info'];
        $dataApp["kakutei_ymd1"] = $this->_selectSekoKihon['kakutei_ymd1'];
        $dataApp["kakutei_info_rr"] = $this->_selectSekoKihon['kakutei_info_rr'];
        $dataApp["shonin"] = $this->hasUriageShonin();
        $dataApp["clsass_name"] = get_class($this);
        $dataApp["denpyo_no"] = $this->getJuchudenpyoNo();
        $uri = $this->getUriageInfo();
        $dataApp["keijo_ymd"] = $uri["keijo_ymd"];
        $dataApp['sougi_ymd'] = $this->_selectSekoKihon['sougi_ymd']; // keigen
        $dataApp["denpyo_biko2"] = $uri["denpyo_biko2"];
        $dataApp['role_kbn'] = App_Utils::ifRolesEqualTo('sysman') || App_Utils::ifRolesEqualTo('jimu') || App_Utils::ifRolesEqualTo('manager');
        // 葬儀式場を設定
        $sogiInfo = App_Utils::getSougiInfo($this->_sekoNo);
        $sougi_shikijo = '';
        if (isset($sogiInfo) && count($sogiInfo) > 0) {
            $sougi_shikijo = $sogiInfo['sougi_shikijo'];
        }
        $dataApp['sougi_shikijo'] = $sougi_shikijo;

        //Msi_Sys_Utils::profilerMark('getkbns');
        // コース名を取得する
        $coseNames = $this->getPlanMst();
        // 商品区分名を取得する
        $shohinKbns = $this->getShohinKbnMst();
        // 目的名を取得する
        $mokutekiKbns = $this->getMokutekiKbns('1010');
        // 目的名2を取得する
        $mokutekiKbns2 = $this->getMokutekiKbns('1020');
        // 部門名を取得する
        $bumonNms = $this->getBumonMst();
        // 仕入先名を取得する
        $siireNms = $this->getSiireMst();
        // 伝区を取得する
        $denKbns = $this->getDenkbn();
        $denKbns2 = $this->getDenkbn2();
        // 商品階層データ(key->商品コード+商品区分 value->商品コード=>商品名称)
        $dataShohinTree = array();
        // 商品データ
        $dataShohin = array();
        // 商品階層データと商品データを取得する
        $this->getShohinData($dataShohinTree, $dataShohin);
        //Msi_Sys_Utils::profilerMark('getdetailstart');
        // テーブル明細データ取得
        $dataCol = $this->getGridData($dataApp);
        //Msi_Sys_Utils::profilerMark('getdetailend');
        // 画面データを設定する
        $dataKbn = array(
            'cose_names' => $coseNames,
            'mokuteki_kbn' => $this->getMokutekiKbn(),
            'daibunrui_cd' => $this->getDaibunruiCd(),
            'data_sbt' => $this->getDataSbt(),
            'shohin_kbns' => $shohinKbns,
            'mokuteki_kbns' => $mokutekiKbns,
            'mokuteki_kbns2' => $mokutekiKbns2,
            'bumon_nms' => $bumonNms,
            'siire_nms' => $siireNms,
            'denpyo_kbns' => $denKbns,
            'denpyo_kbns2' => $denKbns2,
        );
        $data = array(
            'dataApp' => $dataApp,
            'dataCol' => $dataCol,
            'dataShohinTree' => $dataShohinTree,
            'dataShohin' => $dataShohin,
            'dataKbn' => $dataKbn,
        );
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     * コース商品名変更処理
     *
     * <AUTHOR> Sai
     * @since 2014/02/06
     * @param array $dataApp 画面データ
     */
    public function change($dataApp) {
        $cnt = 0;
        //$params = Msi_Sys_Utils::webInputs();
        //Msi_Sys_Utils::debug('params==>' . Msi_Sys_Utils::dump($params));

        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        // 受注確定日を取得し、確定後はコース商品変更不可にする
//        $this->_juchuKakuteiYMD = $this->getJuchuKakuteiYMD();
        if ($this->_juchuKakuteiYMD) {
            return;
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $this->_sekoPlanCd = $dataApp['seko_plan_cd'];
        // 受注伝票番号取得する
        $denpyoNo = $this->getJuchudenpyoNo();
        // 大分類が葬送儀礼のデータを削除する
        $cnt += $this->deleteSosogireiData($db, $denpyoNo);
        // 施行プランコードを更新する
        $cnt +=$this->updateSekoPlanCode($db);
        // テーブル明細データ再取得
        $dataMst = $this->getJuchuDetailDataFromMst($dataApp, $this->getDaibunruiWhere());
        // 受注伝票を更新する
        $cnt += $this->saveJuchu($db, $dataApp, $dataMst);
        $cnt += $this->saveHachuInfo($db, $dataApp, $dataMst);
        // 受注伝票の自動作成した明細の表示順再設定処理
        $this->resetJuchuDenpyoDispNo();
        // テーブル明細データ再取得
        $dataColNew = $this->getGridData($dataApp);

        $db->commit();

        // サイドメニューデータを取得する
        $sideMenuData = $this->getSideMenuData();
        $data = array(
            'dataApp' => $dataApp,
            'dataCol' => $dataColNew,
            'dataSideMenu' => $sideMenuData,
            'status' => 'OK',
            'msg' => ('正常に終了しました'),
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/01/28
     * @version 入金伝票（互助会）自動作成 処理を追加 2014/06/29 
     * @param array $dataApp 画面Appデータ
     * @param array $dataCol グリッドデータ
     * @param array $dataTrnDelCol グリッドトラン削除データ
     */
    public function save($dataApp, $dataCol, $dataTrnDelCol) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        $this->_sekoPlanCd = $dataApp['seko_plan_cd'];
//        $this->_sekoTantoCd = $dataApp['seko_tanto_cd'];
        $this->_nakunaribi = $this->getNakunaribi();
        $jichuKakuteYmdOld = $dataApp["jichu_kakute_ymd"];

     //Msi_Sys_Utils::debug( '* dataCol=>' . Msi_Sys_Utils::dump($dataCol) );
     //Msi_Sys_Utils::debug( '* dataTrnDelCol=>' . Msi_Sys_Utils::dump($dataTrnDelCol) );
        
//        $this->_juchuKakuteiYMD = $this->getJuchuKakuteiYMD();
        // 画面表示時は未確定、保存時は確定された場合
        if (empty($jichuKakuteYmdOld) && $this->_juchuKakuteiYMD) {
            $data = array(
                'status' => 'NG',
                'msg' => ('ただいま見積が確定されたため、保存することができません。ページをもう一度読み込んでください。'),
            );
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 発注済みのチェック 2018/12/21 ADD Kayo
        $msg = App_HachuLib::chkHachuiOrder($db, $this->_sekoNo, $dataCol, $dataTrnDelCol);
        if (strlen($msg) > 0)   {
            $data = array(
                'status' => 'NG',
                'msg'    => $msg
            );
            Msi_Sys_Utils::outJson($data);
            return;
        }
        if ($this->_juchuKakuteiYMD) {// 確定済み
            $cnt +=$this->saveUriage($db, $dataApp, $dataCol, $dataTrnDelCol);
            $cnt +=$this->updateUriagedenpyo($db, $dataApp);
            $denpuo_no = $this->getJuchudenpyoNo();
            $data_kbn = $this->getDataKbn();
            $cnt +=Logic_SyukeiTblUpdate::SyukeiMain($db, $denpuo_no, $data_kbn); // 各種集計テーブル作成、更新処理
        } else { // 未確定
//            $saveSpecial = $this->getDaibunruiCd() === '0010'; // 特殊処理フラグ：葬送儀礼
            $cnt +=$this->saveJuchu($db, $dataApp, $dataCol, $dataTrnDelCol);
        }
        // 施行プランコードを更新する
        $cnt += $this->updateSekoPlanCode($db);
        // 施行発注管理情報を保存する
        $cnt +=$this->saveHachuInfo($db, $dataApp, $dataCol, $dataTrnDelCol);

        $sbt = 2;
        if ($this->_moushiKbn === self::MOUSHI_KBN_HOUJI) {
            $sbt = 5;
        }
        // 施行管理情報を更新する
        App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, $sbt, $this->getItemno());
        $db->commit();
        // 受注データ再取得
        $dataColNew = $this->getGridData($dataApp);
        if (isset($dataApp['sidemenu_key']) && !empty($dataApp['sidemenu_key'])) {
            $sideMenuData = $this->getSideMenuData(array(), $dataApp['sidemenu_key']);
        } else {
            $sideMenuData = $this->getSideMenuData();
        }
        // サイドメニューデータを取得する

        $data = array(
            'dataApp' => $dataApp,
            'dataCol' => $dataColNew,
            'dataSideMenu' => $sideMenuData,
            'status' => 'OK',
            'msg' => ('更新しました'),
        );
        Msi_Sys_Utils::outJson($data);
//        Msi_Sys_Utils::profilerMark('7');
    }

    /**
     *
     * 施行プランマスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/27
     * @return array コース名配列
     */
    private function getPlanMst() {
        $arrSekoNames = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT 
            spm.seko_plan_cd
            ,spm.web_disp_plan || spm.seko_plan_nm AS seko_plan_nm
        FROM
            seko_plan_mst spm
        WHERE
                CURRENT_DATE BETWEEN spm.tekiyo_st_date AND spm.tekiyo_ed_date
            AND spm.delete_flg = 0
            AND spm.main_pt_cd = :main_pt_cd
            AND spm.gojokai_kbn = :gojokai_kbn
        ORDER BY
            spm.seko_plan_cd
                ";
        $select = $db->easySelect($sql, array('main_pt_cd' => $this->_mainPtCd, 'gojokai_kbn' => $this->_gojokaiKbn));
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $arrSekoNames[$select[$i]['seko_plan_cd']] = $select[$i]['seko_plan_nm'];
            }
        }
        return $arrSekoNames;
    }

    /**
     *
     * 商品区分マスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/27
     * @return array 商品区分名配列
     */
    public function getShohinKbnMst() {
        $arrShohinKbnNames = array();
        $chubunruiWhere = $this->getChubunruiWhere();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            skm.shohin_kbn
            ,skm.shohin_kbn_nm
        FROM
            shohin_mst sm
            INNER JOIN
                shohin_bunrui_mst sbm
            ON  (
                    sm.shohin_cd = sbm.shohin_cd
				AND sm.kaisya_cd = sbm.kaisya_cd	-- 2016/12/03 ADD Kayo
				AND sm.bumon_cd	 = sbm.bumon_cd		-- 2016/12/03 ADD Kayo
                AND sbm.delete_flg = 0
                )
            INNER JOIN
                shohin_kbn_mst skm
            ON  (sbm.shohin_kbn = skm.shohin_kbn)
        WHERE
            CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
        AND sm.delete_flg = 0
        AND skm.delete_flg = 0
        AND sbm.dai_bunrui_cd = :daibunrui_cd
        {$chubunruiWhere}
        GROUP BY
            skm.shohin_kbn
            ,skm.shohin_kbn_nm
        ORDER BY
            skm.shohin_kbn
                ";
        $select = $db->easySelect($sql, array('daibunrui_cd' => $this->getDaibunruiCd()));
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $arrShohinKbnNames[$select[$i]['shohin_kbn']] = $select[$i]['shohin_kbn_nm'];
            }
        }
        return $arrShohinKbnNames;
    }

    /**
     *
     * 目的名を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/27
     * @param string $codeKbn
     * @return array 目的名配列
     */
    private function getMokutekiKbns($codeKbn) {
        $kbns = array();
        $select = DataMapper_MsterGetLib::GetCodeNameMst(Msi_Sys_DbManager::getMyDb(), $codeKbn);
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $kbns[$select[$i]['kbn_value_cd_num']] = $select[$i]['kbn_value_lnm'];
            }
        }
        return $kbns;
    }

    /**
     *
     * 部門マスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/27
     * @return array 部門名配列
     */
    private function getBumonMst() {
        $arrBumonNames = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            bm.bumon_cd
            ,bm.bumon_lnm
        FROM
            BUMON_MST bm
        WHERE
            CURRENT_DATE BETWEEN bm.tekiyo_st_date AND bm.tekiyo_ed_date
        AND bm.bumon_kbn <> 1
        AND bm.delete_flg = 0
        ORDER BY
            bm.bumon_cd
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $arrBumonNames[$select[$i]['bumon_cd']] = $select[$i]['bumon_lnm'];
            }
        }
        return $arrBumonNames;
    }

    /**
     *
     * 仕入れマスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/27
     * @return array 仕入れ配列
     */
    private function getSiireMst() {
        $arrSiireNames = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            sm.siire_cd
            ,sm.siire_lnm
        FROM
            SIIRE_MST sm
        WHERE
            CURRENT_DATE BETWEEN sm.tekiyo_st_date AND sm.tekiyo_ed_date
        AND sm.delete_flg = 0
		AND sm.kaisya_cd = :kaisya_cd	-- 2016/12/03 ADD Kayo
        ORDER BY
            sm.siire_cd
                ";
        $KaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $select = $db->easySelect($sql, array('kaisya_cd' => $KaisyaCd)); // 2016/12/03 ADD Kayo
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $arrSiireNames[$select[$i]['siire_cd']] = $select[$i]['siire_lnm'];
            }
        }
        return $arrSiireNames;
    }

    /**
     *
     * 伝票区分を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/27
     * @return array 伝区配列
     */
    private function getDenkbn() {
        $arrDenkbns = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $select = DataMapper_MsterGetLib::GetCodeNameMst($db, '0420');
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $kbn = $select[$i]['kbn_value_cd_num'];
                // 伝区は1：受注と2：返品を対応
                if ($kbn === '1' || $kbn === '2') {
                    $arrDenkbns[$kbn] = $select[$i]['kbn_value_lnm'];
                }
            }
        }
        return $arrDenkbns;
    }

    /**
     *
     * 伝票区分を取得する
     *
     * <AUTHOR> Sai
     * @since 2015/6/25
     * @return array 伝区配列
     */
    private function getDenkbn2() {
        $arrDenkbns = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $select = DataMapper_MsterGetLib::GetCodeNameMst($db, '2030');
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $kbn = $select[$i]['kbn_value_cd_num'];
                $arrDenkbns[$kbn] = $select[$i]['kbn_value_lnm'];
            }
        }
        return $arrDenkbns;
    }

    /**
     *
     * 商品階層データと商品データを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/27
     * @param array &$dataShohinTree 商品階層データ(key->商品コード+商品区分 value->商品コード=>商品名称)
     *                                   例：'01100000043' => array('0000043' => '玄関前飾り菖蒲',)
     * @param array &$dataShohin 商品データ 例：'0000001' => array('hanbai_tnk' => 150000,)
     * @return           
     */
    public function getShohinData(&$dataShohinTree, &$dataShohin) {
        $chubunruiWhere = $this->getChubunruiWhere();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            sm.shohin_cd                -- 商品コード
            ,sbm.shohin_kbn              -- 商品区分
            ,sbm.shohin_kbn || sm.shohin_cd AS mixed_cd  -- 商品区分+商品コード
            ,sm.shohin_nm               -- 商品名称
            ,sm.shohin_tkiyo_nm         -- 商品摘要
            ,sm.nm_input_kbn            -- 名称入力区分
            ,COALESCE (stm.hanbai_tnk ,0) AS hanbai_tnk -- 受注金額
            ,sbm.dai_bunrui_cd           -- 大分類コード
            ,sbm.chu_bunrui_cd           -- 中分類コード
            ,sm.tani_cd                 -- 単位コード
            ,COALESCE (stm.siire_tnk ,0) AS gen_tnk  -- 原価単価
            ,sm.hoshi_umu_kbn           -- 奉仕料有無区分
            ,sm.siire_cd                -- 仕入れコード
            ,sm.uri_zei_kbn AS zei_kbn  -- 売上課税区分
            ,sm.tnk_chg_kbn             -- 売上単価変更区分
            ,sbm.hachu_kbn              -- 発注書区分
            ,sm.uri_kamoku_cd           -- 科目コード
            ,sbm.mokuteki_kbn           -- 目的区分
            ,skm.mokuteki_inp_kbn       -- 使用目的入力区分
            , 0 AS nebiki_prc           -- 値引き金額
            , 0 AS gojokai_nebiki_prc   -- 互助会値引き金額
            ,sm.uri_zei_kbn             -- 売上課税区分
            ,sbm.mitumori_print_seq     -- 見積書、請求書印刷順序
            ,sm.uri_reduced_tax_rate AS shohin_uri_reduced_tax_rate              -- 商品.売上軽減税率区分  keigen
            ,sm.shiire_reduced_tax_rate AS shohin_shiire_reduced_tax_rate        -- 商品.仕入軽減税率区分  keigen
            ,skm.uri_reduced_tax_rate AS shohin_kbn_uri_reduced_tax_rate         -- 商品区分.売上軽減税率区分  keigen
            ,skm.shiire_reduced_tax_rate AS shohin_kbn_shiire_reduced_tax_rate   -- 商品区分.仕入軽減税率区分  keigen
            ,COALESCE(sm.uri_reduced_tax_rate, skm.uri_reduced_tax_rate, 1) AS uri_reduced_tax_rate_ex -- 売上軽減税率区分
            ,COALESCE(sm.shiire_reduced_tax_rate, skm.shiire_reduced_tax_rate, 1) AS shiire_reduced_tax_rate_ex -- 仕入軽減税率区分
        FROM
            shohin_mst sm
            INNER JOIN
                shohin_bunrui_mst sbm
            ON  (
                    sm.shohin_cd = sbm.shohin_cd
				AND sm.kaisya_cd = sbm.kaisya_cd		-- 2016/12/03 ADD Kayo
				AND sm.bumon_cd	 = sbm.bumon_cd		-- 2016/12/03 ADD Kayo
                AND sbm.delete_flg = 0
                )
            INNER JOIN
                shohin_kbn_mst skm
            ON  (sbm.shohin_kbn = skm.shohin_kbn)
            LEFT OUTER JOIN
                SHOHIN_TANKA_MST stm
            ON  (       sm.shohin_cd = stm.shohin_cd
					AND sm.kaisya_cd = stm.kaisya_cd	-- 2016/12/03 ADD Kayo
					AND sm.bumon_cd	 = stm.bumon_cd		-- 2016/12/03 ADD Kayo
                    AND CURRENT_DATE BETWEEN stm.tekiyo_st_date AND tekiyo_ed_date
                    AND stm.delete_flg = 0
                )
        WHERE
            CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
        AND sm.delete_flg = 0
        AND  sm.hihyoji_kbn = 0
        AND skm.delete_flg = 0
        AND sbm.dai_bunrui_cd = :daibunrui_cd
        AND sm.kaisya_cd      = :kaisya_cd
        AND sm.bumon_cd       = :bumon_cd
        {$chubunruiWhere}
        ORDER BY
            sm.shohin_cd
                ";
        $KaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $bumonCd = $this->_bumonCd;
        $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $KaisyaCd, 'bumon_cd' => $bumonCd));
        if ($bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
            $select = $db->easySelect($sql, array('daibunrui_cd' => $this->getDaibunruiCd(), 'kaisya_cd' => $KaisyaCd, 'bumon_cd' => $bumonCd));
        } else {
            $select = $db->easySelect($sql, array('daibunrui_cd' => $this->getDaibunruiCd(), 'kaisya_cd' => $KaisyaCd, 'bumon_cd' => '00000'));
        }
        // ホール、通夜室等の単価の置換えを行う
        App_Utils2::adjHallPrice($this->_sekoNo, $select, array('shohin_cd' => 'shohin_cd',
            'hanbai_tnk' => 'hanbai_tnk',
            'hanbai_tnk_zei' => null,
            'tanka' => null));
        // 値引き金額の設定を行う
        App_Utils2::adjGojokaiNebiki2($this->_sekoNo, $select, array('shohin_cd' => 'shohin_cd',
            'hanbai_tnk' => 'hanbai_tnk',
            'nebiki_tnk' => 'nebiki_prc'));
        App_Utils2::setGojokaiNebiki($select, $this->_sekoNo, array('hanbai_tnk' => 'hanbai_tnk'));
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $dataShohinTree[$select[$i]['mixed_cd']] = array($select[$i]['shohin_cd'] => $select[$i]['shohin_nm'],);

                $shohinOneRowData = array();
                $shohinOneRowData['shohin_nm'] = $select[$i]['shohin_nm'];
                $shohinOneRowData['shohin_tkiyo_nm'] = $select[$i]['shohin_tkiyo_nm'];
                $shohinOneRowData['nm_input_kbn'] = $select[$i]['nm_input_kbn'];
                $shohinOneRowData['hanbai_tnk'] = $select[$i]['hanbai_tnk'];
                $shohinOneRowData['dai_bunrui_cd'] = $select[$i]['dai_bunrui_cd'];
                $shohinOneRowData['chu_bunrui_cd'] = $select[$i]['chu_bunrui_cd'];
                $shohinOneRowData['tani_cd'] = $select[$i]['tani_cd'];
                $shohinOneRowData['gen_tnk'] = $select[$i]['gen_tnk'];
                $shohinOneRowData['hoshi_umu_kbn'] = $select[$i]['hoshi_umu_kbn'];
                $shohinOneRowData['siire_cd'] = $select[$i]['siire_cd'];
                $shohinOneRowData['zei_kbn'] = $select[$i]['zei_kbn'];
                $shohinOneRowData['tnk_chg_kbn'] = $select[$i]['tnk_chg_kbn'];
                $shohinOneRowData['hachu_kbn'] = $select[$i]['hachu_kbn'];
                $shohinOneRowData['uri_kamoku_cd'] = $select[$i]['uri_kamoku_cd'];
                $shohinOneRowData['mokuteki_kbn'] = $select[$i]['mokuteki_kbn'];
                $shohinOneRowData['mokuteki_inp_kbn'] = $select[$i]['mokuteki_inp_kbn'];
                $shohinOneRowData['nebiki_prc'] = $select[$i]['nebiki_prc'];
                $shohinOneRowData['gojokai_nebiki_prc'] = $select[$i]['gojokai_nebiki_prc'];
                $shohinOneRowData['mitumori_print_seq'] = $select[$i]['mitumori_print_seq'];
                $shohinOneRowData['mixed_cd'] = $select[$i]['mixed_cd'];
                $shohinOneRowData['shohin_cd'] = $select[$i]['shohin_cd'];
                $shohinOneRowData['uri_reduced_tax_rate_ex'] = $select[$i]['uri_reduced_tax_rate_ex']; //  keigen
                $shohinOneRowData['shiire_reduced_tax_rate_ex'] = $select[$i]['shiire_reduced_tax_rate_ex']; //  keigen
                $dataShohin[$select[$i]['shohin_cd'] . $select[$i]['shohin_kbn']] = $shohinOneRowData;
            }
        }
    }

    /**
     *
     * 伝票情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/28
     * @version 2015/01/28 施行番号のJOINを追加 Kayo
     * @param array $dataApp
     * @return array 伝票情報配列
     */
    public function getGridData($dataApp) {
        $dataDtl = array();
        if ($this->_juchuKakuteiYMD) {// 確定済み
            // 売上伝票データがを取得する
            $dataDtl = $this->getUriageDetailData();
        } else {
            // 受注伝票取得SQL
            $sql = $this->selectJuchudenpyoMsiSQL2();
            $db = Msi_Sys_DbManager::getMyDb();
            $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'seko_no_sub' => $this->_sekoNoSub, 'dai_bunrui_cd' => $this->getDaibunruiCd()));
            if (Msi_Sys_Utils::myCount($select) > 0) {
                // 受注伝票データがある場合、トランより取得する
                $dataDtl = $this->getJuchuDetailDataFromTrn($select);
            } else {
                // 受注伝票データがない場合、マスタより取得する
                $dataDtl = $this->getJuchuDetailDataFromMst($dataApp, $this->getDaibunruiWhere());
            }
        }
        return $dataDtl;
    }

    /**
     *
     * 受注情報をトランより取得する
     * 
     * <AUTHOR> Sai
     * @since 2014/1/30
     * @version 2015/01/31 発注済みは、施行発注管理情報から表示するように変更 Kayo
     * @version 2015/05/03 施行発注管理情報のJOINにデータ区分を追加 Kayo
     * @param array $juchuDenpyo 受注伝票情報
     * @return array 受注情報
     */
    public function getJuchuDetailDataFromTrn($juchuDenpyo) {
        $data_sbt = $this->getDataSbt2();
        $data_sbt_hide = "";
        $chu_bunrui = "";
        if($this->_moushiKbn === "2"){
            $data_sbt_hide = "--";
            $chu_bunrui = $this->getHoujiJuchuChuBunrui($data_sbt);
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
             jdm.msi_no         -- 受注明細№
            ,jdm.disp_no        -- 表示順
            ,jdm.denpyo_kbn     -- 伝票区分
            ,jdm.bumon_cd       -- 部門コード
            ,jdm.shohin_cd      -- 商品コード
            ,jdm.shohin_kbn     -- 商品区分
            ,skm.shohin_kbn_nm  -- 商品区分名
            ,jdm.shohin_nm      -- 商品名称
            ,jdm.shohin_tkiyo_nm-- 商品摘要
            ,jdm.mokuteki_kbn   -- 目的区分
            ,skm.mokuteki_inp_kbn -- 使用目的入力区分
            ,jdm.dai_bunrui_cd  -- 大分類コード
            ,jdm.chu_bunrui_cd  -- 中分類コード
            ,jdm.juchu_tnk      -- 値引後単価
            ,jdm.juchu_suryo    -- 数量
            ,jdm.juchu_tnk * juchu_suryo + jdm.hoshi_prc + jdm.nebiki_prc + jdm.gojokai_nebiki_prc AS juchu_gokei -- 受注金額合計
            ,jdm.juchu_prc      -- 受注金額
            ,jdm.nebiki_prc     -- 値引額
            ,jdm.gojokai_nebiki_prc     -- 互助会値引額
            ,jdm.gen_tnk        -- 原価単価
            ,null AS shohin_type -- 商品タイプ
            --,jdm.nonyu_nm       -- 納入場所
            --,TO_CHAR(jdm.nonyu_dt,'YYYY/MM/DD HH24:MI') AS nonyu_dt-- 納入予定日
            ,CASE shi.delivery_kbn
                WHEN 1 THEN '故人宅'
                WHEN 2 THEN '喪主宅'
                ELSE shi.nonyu_nm
             END nonyu_nm
            ,TO_CHAR(shi.nonyu_ymd,'YYYY/MM/DD HH24:MI') AS nonyu_dt-- 納入予定日
            ,sm.nm_input_kbn    -- 名称入力区分
            ,jdm.tani_cd        -- 単位コード
            ,jdm.zei_kbn        -- 消費税区分
            ,COALESCE(shi.siire_cd, jdm.siire_cd)		AS siire_cd       -- 仕入コード 2015/01/31 UPD Kayo
            ,COALESCE(siirem.siire_lnm, jdm.siire_lnm)	AS siire_lnm      -- 仕入名     2015/01/31 UPD Kayo
            ,sm.tnk_chg_kbn     -- 売上単価変更区分
            ,sbm.hachu_kbn      -- 発注書区分
            ,sm.uri_kamoku_cd   -- 科目コード
            ,jdm.hoshi_umu_kbn  -- 奉仕料有無区分
            ,jdm.toku_wari_kbn  -- 特別割引区分 2017/12/19 add Sugiyama
            ,jdm.hoshi_prc      -- 奉仕料
            ,jdm.nonyu_cd       -- 納入先コード 
            ,jdm.nonyu_knm      -- 納入先名カナ
            ,jdm.nonyu_yubin_no -- 納入先郵便番号
            ,jdm.nonyu_addr1    -- 納入先住所1
            ,jdm.nonyu_addr2    -- 納入先住所2
            ,jdm.nonyu_tel      -- 納入先電話番号
            ,jdm.nonyu_fax      -- 納入先FAX
            ,2 AS data_status   -- データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
            ,shi.order_flg      -- 発注済み 0:未発注 1:発注済み
            ,CASE 
                WHEN jdm.add_kbn = 8 
                THEN jdm.add_kbn 
                ELSE 0
             END add_kbn -- 特典区分
            ,TO_CHAR(jdm.add_henpin_ymd,'YYYY/MM/DD') AS add_henpin_ymd-- 追加・返品日
            ,jdm.v_free1
            ,jdm.zei_cd  --  keigen
            ,jdm.reduced_tax_rate --   keigen
        FROM juchu_denpyo	jdh						-- 2015/05/03 ADD Kayo
            LEFT OUTER JOIN	juchu_denpyo_msi jdm	-- 2015/05/03 UPD Kayo
			ON	jdh.denpyo_no = jdm.denpyo_no		-- 2015/05/03 UPD Kayo
			AND 0			  = jdm.delete_flg		-- 2015/05/03 UPD Kayo
            INNER JOIN
                shohin_kbn_mst skm
            ON  (jdm.shohin_kbn = skm.shohin_kbn)
			AND skm.delete_flg = 0					-- 2015/05/03 MOV Kayo
            INNER JOIN
                shohin_bunrui_mst sbm
            ON  (
                    jdm.dai_bunrui_cd = sbm.dai_bunrui_cd
                AND jdm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                AND jdm.shohin_kbn  = sbm.shohin_kbn
                AND jdm.shohin_cd  = sbm.shohin_cd
                AND jdm.kaisya_cd		= sbm.kaisya_cd		-- 2016/12/03 ADD Kayo
                AND jdm.shohin_bumon_cd	= sbm.bumon_cd		-- 2016/12/03 ADD Kayo
            AND sbm.delete_flg = 0
                )
            LEFT OUTER JOIN
                shohin_mst sm
                ON  (
                        jdm.shohin_cd = sm.shohin_cd
					AND jdm.kaisya_cd		= sm.kaisya_cd		-- 2016/12/03 ADD Kayo
					AND jdm.shohin_bumon_cd	= sm.bumon_cd		-- 2016/12/03 ADD Kayo
                    AND CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
                    AND sm.delete_flg = 0
                    )
            LEFT OUTER JOIN
                seko_hachu_info shi
                ON  (
                    jdm.seko_no			= shi.seko_no		-- 2015/01/28 Kayo ADD
					AND jdh.data_kbn	= shi.data_kbn		-- 2015/05/03 ADD Kayo
                    AND jdm.msi_no		= shi.jc_msi_no
                    AND jdm.shohin_cd	= shi.shohin_cd
                    AND shi.hachu_no_moto IS NULL
                    AND shi.delete_flg	= 0
                    )
			-- 仕入先マスタ
            LEFT OUTER JOIN siire_mst siirem			--　2015/01/31 ADD Kayo
                ON  shi.siire_cd = siirem.siire_cd		--　2015/01/31 ADD Kayo
				AND shi.kaisya_cd= siirem.kaisya_cd		-- 2016/12/03 ADD Kayo
                AND 0			 = siirem.delete_flg	--　2015/01/31 ADD Kayo
        WHERE
            jdh.seko_no = :seko_no
        AND jdh.seko_no_sub = :seko_no_sub
        $data_sbt_hide AND jdm.data_sbt IN({$data_sbt})
        $chu_bunrui
        AND jdh.delete_flg = 0
		AND jdh.data_kbn	IN (1,2)				-- 2015/05/03 ADD Kayo
        ORDER BY
            sbm.mitumori_print_seq
            ,jdm.disp_no
            ,jdm.msi_no
                ";
        $select = $db->easySelect($sql, array(
            'seko_no' => $this->_sekoNo,
            'seko_no_sub' => $this->_sekoNoSub));
        // データを設定する
        $dataDtl = $this->setSelectData($select);
        return $dataDtl;
    }

    /**
     *
     * 受注伝票明細取得SQL
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @return string SQL
     */
    private function selectJuchudenpyoMsiSQL2() {
        // 受注伝票明細取得SQL
        $sql = "
        SELECT
            denpyo_no
        FROM
            juchu_denpyo_msi
        WHERE
            seko_no = :seko_no
        AND seko_no_sub = :seko_no_sub
        AND dai_bunrui_cd = :dai_bunrui_cd
        AND delete_flg = 0
                ";
        return $sql;
    }

    /**
     *
     * 施行基本情報テーブルのタ施行プランコードを更新する
     *
     * <AUTHOR> Sai
     * @since 2014/2/10
     * @param Msi_Sys_Db $db db
     * @return int 更新件数
     */
    private function updateSekoPlanCode($db) {
        $cnt = 0;

        // 施行基本情報テーブル更新SQL
        $sqlUpdate = <<< END_OF_SQL
UPDATE 
    seko_kihon_info
SET
    seko_plan_cd = :seko_plan_cd
WHERE
        seko_no = :seko_no 
    AND delete_flg = 0
END_OF_SQL;
        $select = $this->selectSekoKihon();
        if (count($select) > 0 && $this->_sekoPlanCd !== $select['seko_plan_cd']) {
            $cnt = $db->easyExecute($sqlUpdate, array('seko_plan_cd' => $this->_sekoPlanCd, 'seko_no' => $this->_sekoNo));
        }
        return $cnt;
    }

    /** 見積確定時処理　 START */

    /**
     *
     * 売上伝票情報を取得する
     * 
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @version 2015/01/31 発注済みは、施行発注管理情報から表示するように変更 Kayo
     * @return array 売上伝票情報
     */
    public function getUriageDetailData() {
        $data_sbt = $this->getDataSbt2();
        $data_sbt_hide = "";
        $chu_bunrui = "";
        if($this->_moushiKbn === "2"){
            $data_sbt_hide = "--";
            $chu_bunrui = $this->getHoujiUriageChuBunrui($data_sbt);
        }
        $db = Msi_Sys_DbManager::getMyDb();
        // 売上伝票取得SQL
        $sql = "
        SELECT
             udm.msi_no         -- 受注明細№
            ,udm.disp_no        -- 表示順
            ,udm.denpyo_kbn     -- 伝票区分
            ,udm.bumon_cd       -- 部門コード
            ,udm.shohin_cd      -- 商品コード
            ,udm.shohin_kbn     -- 商品区分
            ,skm.shohin_kbn_nm  -- 商品区分名
            ,udm.shohin_nm      -- 商品名称
            ,udm.shohin_tkiyo_nm-- 商品摘要
            ,udm.mokuteki_kbn   -- 目的区分
            ,skm.mokuteki_inp_kbn -- 使用目的入力区分
            ,udm.dai_bunrui_cd  -- 大分類コード
            ,udm.chu_bunrui_cd  -- 中分類コード
            ,udm.uri_tnk AS juchu_tnk      -- 値引後単価
            ,udm.juchu_suryo    -- 数量
            ,udm.uri_tnk * udm.juchu_suryo + udm.hoshi_prc + udm.nebiki_prc + udm.gojokai_nebiki_prc AS juchu_gokei -- 売上金額合計
            ,udm.uri_prc AS juchu_prc      -- 売上金額
            ,udm.nebiki_prc     -- 値引額
            ,udm.gojokai_nebiki_prc     -- 互助会値引額
            ,udm.gen_tnk        -- 原価単価
            ,null AS shohin_type -- 商品タイプ
            --,udm.nonyu_nm       -- 納入場所
            --,TO_CHAR(udm.nonyu_dt,'YYYY/MM/DD HH24:MI') AS nonyu_dt-- 納入予定日
            ,CASE shi.delivery_kbn
                WHEN 1 THEN '故人宅'
                WHEN 2 THEN '喪主宅'
                ELSE shi.nonyu_nm
             END nonyu_nm
            ,TO_CHAR(shi.nonyu_ymd,'YYYY/MM/DD HH24:MI') AS nonyu_dt-- 納入予定日
            ,sm.nm_input_kbn    -- 名称入力区分
            ,udm.tani_cd        -- 単位コード
            ,udm.zei_kbn        -- 消費税区分
            ,COALESCE(shi.siire_cd, udm.siire_cd)			AS siire_cd      -- 仕入コード　2015/01/31 UPD Kayo
            ,COALESCE(siirem.siire_lnm, udm.siire_lnm)		AS siire_lnm	 -- 仕入名		2015/01/31 UPD Kayo
            ,sm.tnk_chg_kbn     -- 売上単価変更区分
            ,sbm.hachu_kbn      -- 発注書区分
            ,udm.hoshi_umu_kbn  -- 奉仕料有無区分
            ,udm.toku_wari_kbn  -- 特別割引区分 2017/12/19 add Sugiyama
            ,udm.hoshi_prc      -- 奉仕料
            ,udm.uri_kamoku_cd  -- 科目コード
            ,udm.nonyu_cd       -- 納入先コード 
            ,udm.nonyu_knm      -- 納入先名カナ
            ,udm.nonyu_yubin_no -- 納入先郵便番号
            ,udm.nonyu_addr1    -- 納入先住所1
            ,udm.nonyu_addr2    -- 納入先住所2
            ,udm.nonyu_tel      -- 納入先電話番号
            ,udm.nonyu_fax      -- 納入先FAX
            ,2 AS data_status   -- データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
            ,shi.order_flg      -- 発注済み 0:未発注 1:発注済み
            ,CASE 
                WHEN udm.add_kbn = 8 
                THEN udm.add_kbn 
                ELSE 0
             END add_kbn -- 特典区分
            ,TO_CHAR(udm.add_henpin_ymd,'YYYY/MM/DD') AS add_henpin_ymd-- 追加・返品日
            ,udm.v_free1
            ,udm.zei_cd  --  keigen
            ,udm.reduced_tax_rate --   keigen
        FROM	uriage_denpyo	udh						-- 2015/05/03 ADD Kayo
            LEFT OUTER JOIN	uriage_denpyo_msi udm		-- 2015/05/03 UPD Kayo
			ON	udh.uri_den_no	=	udm.uri_den_no		-- 2015/05/03 UPD Kayo
			AND	0				=	udm.delete_flg		-- 2015/05/03 UPD Kayo
            INNER JOIN
                shohin_kbn_mst skm
            ON  (udm.shohin_kbn = skm.shohin_kbn)
			AND skm.delete_flg	= 0						-- 2015/05/03 MOV Kayo
            LEFT JOIN
                shohin_bunrui_mst sbm
            ON  (
                    udm.dai_bunrui_cd  = sbm.dai_bunrui_cd
                AND udm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                AND udm.shohin_kbn     = sbm.shohin_kbn
                AND udm.shohin_cd	   = sbm.shohin_cd
				AND udm.kaisya_cd		= sbm.kaisya_cd		-- 2016/12/03 ADD Kayo
				AND udm.shohin_bumon_cd	= sbm.bumon_cd		-- 2016/12/03 ADD Kayo
                AND sbm.delete_flg	   = 0
                )
            LEFT OUTER JOIN
                shohin_mst sm
                ON  (
                        udm.shohin_cd = sm.shohin_cd
					AND udm.kaisya_cd		= sm.kaisya_cd		-- 2016/12/03 ADD Kayo
					AND udm.shohin_bumon_cd	= sm.bumon_cd		-- 2016/12/03 ADD Kayo
                    AND CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
                    AND sm.delete_flg = 0
                    )
            LEFT OUTER JOIN
                seko_hachu_info shi
                ON  (
                        udm.seko_no		= shi.seko_no
					AND udh.data_kbn	= shi.data_kbn	-- 2015/05/03 ADD Kayo
                    AND udm.msi_no		= shi.jc_msi_no
                    AND udm.shohin_cd	= shi.shohin_cd
                    AND shi.delete_flg	= 0
                    AND shi.hachu_no_moto IS NULL
                    )
			-- 仕入先マスタ
            LEFT OUTER JOIN siire_mst siirem			--　2015/01/31 ADD Kayo
                ON  shi.siire_cd = siirem.siire_cd		--　2015/01/31 ADD Kayo
				AND shi.kaisya_cd= siirem.kaisya_cd		-- 2016/12/03 ADD Kayo
                AND 0			 = siirem.delete_flg	--　2015/01/31 ADD Kayo
        WHERE
            udh.delete_flg	= 0						-- 2015/05/03 UPD Kayo
        $data_sbt_hide AND udm.data_sbt IN({$data_sbt})
        $chu_bunrui
        AND udh.seko_no		= :seko_no					-- 2015/05/03 UPD Kayo
        AND udh.seko_no_sub = :seko_no_sub			-- 2015/05/03 UPD Kayo
		AND udh.data_kbn	IN (1,2)				-- 2015/05/03 ADD Kayo
        ORDER BY
            sbm.mitumori_print_seq
            ,udm.disp_no
            ,udm.msi_no
                ";
        $select = $db->easySelect($sql, array(
            'seko_no' => $this->_sekoNo
            , 'seko_no_sub' => $this->_sekoNoSub));
        // データを設定する
        $dataDtl = $this->setSelectData($select);
        return $dataDtl;
    }

    /**
     *
     * 大分類の条件取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/06/17
     * @return string SQL
     */
    private function getDaibunruiWhere() {
        $where = " AND sbm.dai_bunrui_cd = '" . $this->getDaibunruiCd() . "' ";
        return $where;
    }

    /**
     *
     * 売上計上日と備考１取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/08/11
     * @return array 売上計上日と備考１
     */
    private function getUriageInfo() {
        $keijo_ymd = null;
        $biko = null;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            uri_den_no
            ,TO_CHAR(keijo_ymd,'YYYY/MM/DD') AS keijo_ymd
            ,denpyo_biko2
        FROM
            uriage_denpyo
        WHERE
            seko_no = :seko_no
        AND delete_flg = 0
        AND seko_no_sub = :seko_no_sub
	AND data_kbn IN (1, 2)
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'seko_no_sub' => $this->_sekoNoSub));
        if (isset($select) && count($select) > 0) {
            $keijo_ymd = $select['keijo_ymd'];
            $biko = $select['denpyo_biko2'];
        }
        // 売上計上日が存在しない場合は、葬儀日をセット
        if (empty($keijo_ymd)) {
            // ミニプランの場合
            if ($this->_sekoPlanCd === self::PLAN_MINI) {
                $keijo_ymd = $this->getNiteiYmdData(6);
            } else {
                $keijo_ymd = $this->_selectSekoKihon['sougi_ymd'];
            }
        }
        $uri["keijo_ymd"] = $keijo_ymd;
        $uri["denpyo_biko2"] = $biko;
        return $uri;
    }

    /**
     *
     * 売上伝票の売上計上日と備考を更新処理
     *
     * <AUTHOR> Sai
     * @since 2014/08/11
     * @return int 更新件数
     */
    private function updateUriagedenpyo($db, $dataApp) {
        $uriage['keijo_ymd'] = Msi_Sys_Utils::emptyToNull($dataApp['keijo_ymd']);
        $uriage['denpyo_biko2'] = $dataApp['denpyo_biko2'];
        // 条件部
        $where['uri_den_no'] = $this->getUriagedenpyoNo();  // 伝票番号
        $where['delete_flg'] = 0;  // 削除フラグ
        // 更新SQL
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $uriage, $where);
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /** 見積確定時処理　 END */

    /**
     * 料理確定処理 
     *
     * <AUTHOR> Sai
     * @since 2017/03/09
     * @param array $dataApp 画面Appデータ
     */
    public function ryorifix($dataApp) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $kihon = array(
            'kakutei_ymd1' => Msi_Sys_Utils::getDatetimeStd(),
            'kakutei_tanto_cd1' => App_Utils::getTantoCd(),
        );
        $where['seko_no'] = $this->_sekoNo;
        $where['delete_flg'] = 0;
        // 施行基本情報を更新する
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihon, $where);
        $cnt += $db->easyExecute($sql, $param);
        $db->commit();
        // 施行基本情報を設定する
        $this->setInitParam();
        $dataApp["kakutei_ymd1"] = $this->_selectSekoKihon['kakutei_ymd1'];
        $dataApp["kakutei_info_rr"] = $this->_selectSekoKihon['kakutei_info_rr'];

        $data = array(
            'dataApp' => $dataApp,
            'status' => 'OK',
            'msg' => ('確定しました'),
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 料理確定取消処理 
     *
     * <AUTHOR> Sai
     * @since 2017/03/09
     * @param array $dataApp 画面Appデータ
     */
    public function ryoriunfix($dataApp) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $kihon = array(
            'kakutei_ymd1' => null,
            'kakutei_tanto_cd1' => null,
        );
        $where['seko_no'] = $this->_sekoNo;
        $where['delete_flg'] = 0;
        // 施行基本情報を更新する
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihon, $where);
        $cnt += $db->easyExecute($sql, $param);
        $db->commit();
        // 施行基本情報を設定する
        $this->setInitParam();
        $dataApp["kakutei_ymd1"] = $this->_selectSekoKihon['kakutei_ymd1'];
        $dataApp["kakutei_info_rr"] = $this->_selectSekoKihon['kakutei_info_rr'];
        $data = array(
            'dataApp' => $dataApp,
            'status' => 'OK',
            'msg' => ('確定取消をしました'),
        );
        Msi_Sys_Utils::outJson($data);
    }
    
    
    private function getHoujiJuchuChuBunrui($data_sbt) {
        $data_sbt_ary = explode(",", $data_sbt);
        $result = "";
        foreach ($data_sbt_ary as $value) {
            if($value === "1"){
                $result = " AND jdm.chu_bunrui_cd NOT IN ('1090','2090','1120','2080','1130','2130','1140','1150','2140','2150') ";
            }else if($value === "2"){
                $result = " AND jdm.chu_bunrui_cd IN ('1090','2090') ";
            }else if($value === "3"){
                $result = " AND jdm.chu_bunrui_cd IN ('1120','2080') ";
            }else if($value === "6"){
                $result = " AND jdm.chu_bunrui_cd IN ('1130','2130') ";
            }else if($value === "5"){
                $result = " AND jdm.chu_bunrui_cd IN ('1140','2140') ";
            }else if($value === "7"){
                $result = " AND jdm.chu_bunrui_cd IN ('1150','2150') ";
            }            
        }
        return $result;
    }
    private function getHoujiUriageChuBunrui($data_sbt) {
        $data_sbt_ary = explode(",", $data_sbt);
        $result = "";
        foreach ($data_sbt_ary as $value) {
            if($value === "1"){
                $result = " AND udm.chu_bunrui_cd NOT IN ('1090','2090','1120','2080','1130','2130','1140','1150','2140','2150') ";
            }else if($value === "2"){
                $result = " AND udm.chu_bunrui_cd IN ('1090','2090') ";
            }else if($value === "3"){
                $result = " AND udm.chu_bunrui_cd IN ('1120','2080') ";
            }else if($value === "6"){
                $result = " AND udm.chu_bunrui_cd IN ('1130','2130') ";
            }else if($value === "5"){
                $result = " AND udm.chu_bunrui_cd IN ('1140','2140') ";
            }else if($value === "7"){
                $result = " AND udm.chu_bunrui_cd IN ('1150','2150') ";
            }            
        }
        return $result;
    }
}
