<?php /* Smarty version 2.6.28, created on 2025-06-05 11:48:39
         compiled from sysmref/dlg/list-skaisya.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'sysmref/dlg/list-skaisya.tpl', 3, false),array('function', 'cycle', 'sysmref/dlg/list-skaisya.tpl', 4, false),)), $this); ?>
<table>
  <?php $_from = $this->_tpl_vars['list_data']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }$this->_foreach['loop'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['loop']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['myrec']):
        $this->_foreach['loop']['iteration']++;
?>
  <!-- tr class="<?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['oddEven'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
 msi-dialog-selection myid_<?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['myid'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
" data-myid="<?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['myid'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
" -->
  <tr class="<?php echo smarty_function_cycle(array('values' => 'odd,even'), $this);?>
 msi-dialog-selection myid_<?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['myid'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
" data-myid="<?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['myid'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
">
	<td class="code" style="width:20%"><a href="javascript:void(0)" id="a_myid_<?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['myid'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['code'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</a></td>
	<td class="name" style="width:30%"><?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</td>
	<td class="name" style="width:20%;text-align:center"><?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['st_date'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</td>
	<td class="name" style="width:20%;text-align:center"><?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['ed_date'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</td>
	<td class="name" style="width:10%;text-align:center"><?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['status'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</td>
	<!-- td class="kaisya_cd" style="width:25%"><?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['kaisya_cd'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</td -->
  </tr>
  <?php endforeach; endif; unset($_from); ?>
</table>