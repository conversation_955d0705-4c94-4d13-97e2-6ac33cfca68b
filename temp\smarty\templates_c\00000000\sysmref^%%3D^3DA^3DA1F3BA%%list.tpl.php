<?php /* Smarty version 2.6.28, created on 2025-06-05 11:48:39
         compiled from sysmref/dlg/list.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'sysmref/dlg/list.tpl', 6, false),)), $this); ?>
<div class="result">
  <div class="head">
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "sysmref/dlg/".($this->_tpl_vars['dlg_tpl_head']), 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </div><!-- /.head -->
  <div class="list">
<?php if (((is_array($_tmp=$this->_tpl_vars['list_count'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')) > 0): ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "sysmref/dlg/".($this->_tpl_vars['dlg_tpl_list']), 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?><?php if (((is_array($_tmp=$this->_tpl_vars['my_msg'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
  <div id="msi-dialog-list-msg"><span><?php echo ((is_array($_tmp=$this->_tpl_vars['my_msg'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</span></div>
<?php endif; ?>  </div><!-- /.list -->
</div><!-- /.result -->

<?php if (((is_array($_tmp=$this->_tpl_vars['mydata_json'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<script id="msi-dialog-data-json" type="application/json">
<?php echo $this->_tpl_vars['mydata_json']; ?>

</script>
<?php endif; ?>
<?php if (((is_array($_tmp=$this->_tpl_vars['myctldata_json'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<script id="msi-dialog-ctldata-json" type="application/json">
<?php echo $this->_tpl_vars['myctldata_json']; ?>

</script>
<?php endif; ?>