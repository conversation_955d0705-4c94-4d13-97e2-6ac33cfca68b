<?php
  /**
   * PDF 領収証・印紙確認一覧表
   *
   * @category   App
   * @package    controller
   * <AUTHOR> Sugiyama
   * @since      2020/12/xx
   * @filesource 
   */

  /**
   * PDF 領収証・印紙確認一覧表
   *
   * @category   App
   * @package    controller
   * <AUTHOR> Sugiyama
   * @since      2020/11/xx
   */
class Saiken_Pdf1202Controller extends Zend_Controller_Action
{
    private static $title = '領収証・印紙確認一覧表';
    private static $row_height = 20;
    private static $row_count = 28;
    
    /**
     * アクション
     *
     * <AUTHOR> Sugiyama
     * @since 2020/12/xx
     */
    public function indexAction() {
        $db = Msi_Sys_DbManager::getMyDb();
        $params  = Msi_Sys_Utils::webInputs();
        $pdfObj = new App_Pdf(self::$title);
        $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);
        // 条件
        $cond = array();
        // 部門
        $cond['oya_bumon_cd']  = null;
        if (array_key_exists('s_bumon_cd', $dataAppAry) && $dataAppAry['s_bumon_cd'] != '') {
            $cond['oya_bumon_cd'] = $dataAppAry['s_bumon_cd'];
        }
        if (array_key_exists('taisho_st_ymd', $dataAppAry) && $dataAppAry['taisho_st_ymd'] != '' && array_key_exists('taisho_ed_ymd', $dataAppAry) && $dataAppAry['taisho_ed_ymd'] != '') {
            // 期間（開始）
            // 期間（終了）
            $cond['__raw1'] = " T.hako_ymd BETWEEN '".$dataAppAry['taisho_st_ymd']."' AND '".$dataAppAry['taisho_ed_ymd']."'";
        }        
        // CSV出力
        $csv = false;
        if (array_key_exists('csv', $dataAppAry)) {
            $csv = $dataAppAry['csv'];
        }        
        // 出力
        if ($csv) {
            $csvData = $this->getCsv($db, $cond);
            if (!isset($csvData)) {
                App_PdfKanriLib::err(App_PdfKanriLib::STATUS_NODATA);
                return;
            }
            $buf = Msi_Sys_Utils::csvOutString($csvData);
            Msi_Sys_Utils::out2way1($buf, self::$title . '.csv', 'text/csv');
        } else {
            $ret = $this->outData($pdfObj, $db, $dataAppAry, $cond);
            if ($ret != App_PdfKanriLib::STATUS_OK) {
                App_PdfKanriLib::err($ret);
                return;
            }
            $pdfObj->download();
        }
    }

    /**
     * 領収証・印紙確認一覧表出力
     * 
     * <AUTHOR> Sugiyama
     * @since 2020/11/xx
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param string $prn_ymd	印刷日付
     * @param array  $kari_denpyo_no
     * @return void
     */
    private function outData($pdfObj, $db, $dataAppAry, $cond) {
        $pdfObj->set_default_font_size(10);
        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');
        $select = DataMapper_Saiken_Pdf1202::find($db, $cond);
        if (count($select) == 0) {
            return App_PdfKanriLib::STATUS_NODATA;
        }
        $bumon_info = DataMapper_BumonMst::findOne($db, array('bumon_cd' => $cond['oya_bumon_cd'], 'bumon_kbn' => 0));
        $kaisya_nm = NULL;
        if(Msi_Sys_Utils::myCount($bumon_info) > 0){
            $kaisya_nm = $bumon_info['char_free2'];
        }        
        $title = "印紙後納　集計表";
        $kikan = "領収証発行 対象期間";
        $kikan_ymd = $dataAppAry['taisho_st_ymd']."　～　".$dataAppAry['taisho_ed_ymd'];
        $today = date("Y/m/d");
        $head_ary[] = array('x' =>410, 'y' => 55, 'width' => 100, 'height' => 15, 'align' => 'L', 'value' => "出力日");     // 出力日タイトル
        $head_ary[] = array('x' =>460, 'y' => 55, 'width' => 100, 'height' => 15, 'align' => 'L', 'value' => $today);       // 出力日
        $head_ary[] = array('x' =>410, 'y' => 75, 'width' => 150, 'height' => 15, 'align' => 'L', 'value' => $kaisya_nm);   // 会社名
        $head_ary[] = array('x' => 50, 'y' => 95, 'width' => 495, 'height' => 15, 'align' => 'C', 'font_size' => 18, 'value' => $title);   // 表題
        $head_ary[] = array('x' => 30, 'y' =>150, 'width' => 100, 'height' => 15, 'align' => 'L', 'value' => $kikan);       // 期間タイトル
        $head_ary[] = array('x' =>160, 'y' =>150, 'width' => 300, 'height' => 15, 'align' => 'L', 'value' => $kikan_ymd);   // 期間
        $head_ary[] = array('x' => 30, 'y' =>200, 'width' => 100, 'height' => 15, 'align' => 'C', 'value' => "名称");
        $head_ary[] = array('x' =>130, 'y' =>200, 'width' => 100, 'height' => 15, 'align' => 'C', 'value' => "承認年月日");
        $head_ary[] = array('x' =>230, 'y' =>200, 'width' =>  80, 'height' => 15, 'align' => 'C', 'value' => "承認番号");
        $head_ary[] = array('x' =>310, 'y' =>200, 'width' => 100, 'height' => 15, 'align' => 'C', 'value' => "税率・税額区分(円)");
        $head_ary[] = array('x' =>410, 'y' =>200, 'width' =>  80, 'height' => 15, 'align' => 'C', 'value' => "数量(通)");
        $head_ary[] = array('x' =>490, 'y' =>200, 'width' =>  80, 'height' => 15, 'align' => 'C', 'value' => "税額(円)");
        $head_ary[] = array('x1' => 30, 'y1' => 220, 'x2' => 570, 'y2' => 220, 'width' => 1, 'type' => 'line'); // 上線
        //$head_ary[] = array('x' => 0, 'y' =>200, 'width' => 595, 'height' => 15, 'align' => 'C', 'value' => "|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|----+----|");   // 承認番号
        $set_ary = array(
            array('x' => 30, 'y' =>225, 'width' => 100, 'height' => 15, 'align' => 'C'),    // 名称
            array('x' =>130, 'y' =>225, 'width' => 100, 'height' => 15, 'align' => 'C'),    // 承認年月日
            array('x' =>230, 'y' =>225, 'width' =>  70, 'height' => 15, 'align' => 'C'),    // 承認番号
            array('x' =>310, 'y' =>225, 'width' =>  90, 'height' => 15, 'align' => 'R'),    // 税率・税額区分(円)
            array('x' =>410, 'y' =>225, 'width' =>  70, 'height' => 15, 'align' => 'R'),    // 数量(通)
            array('x' =>490, 'y' =>225, 'width' =>  70, 'height' => 15, 'align' => 'R'),    // 税額(円)
            array('x1' => 30, 'y' => 0, 'y1' => 225, 'x2' => 570, 'y2' => 225, 'width' => 1, 'type' => 'line'),    // 下線
            array('x' =>310, 'y' =>230, 'width' =>  90, 'height' => 15, 'align' => 'C'),    // 合計タイトル
            array('x' =>410, 'y' =>230, 'width' =>  70, 'height' => 15, 'align' => 'R'),    // 数量(通)合計
            array('x' =>490, 'y' =>230, 'width' =>  70, 'height' => 15, 'align' => 'R'),    // 税額(円)合計
        );
        $suryo = 0;
        $sum   = 0;
        $index = 0;
        foreach ($select as $value) {
            $row_ary[] = array(
                 $value['ryosyu_nm']
                ,$value['ryosyu_syonin_ymd']
                ,$value['ryosyu_syonin_no']
                ,Msi_Sys_Utils::filterComma($value['inshi_zei_prc'])." 円"
                ,Msi_Sys_Utils::filterComma($value['suryo'])." 通"
                ,Msi_Sys_Utils::filterComma($value['sum'])." 円"
                ,NULL
                ,NULL
            );
            $index++;
            $suryo += $value['suryo'];
            $sum   += $value['sum'];
            if($index >= self::$row_count - 1){
                // 下線
                $row_ary[] = array(
                     NULL
                    ,NULL
                    ,NULL
                    ,NULL
                    ,NULL
                    ,NULL
                    ,1
                    ,"合計"
                    ,Msi_Sys_Utils::filterComma($suryo)." 通"
                    ,Msi_Sys_Utils::filterComma($sum)." 円"
                );                
                $index = 0;
            }
        }
        // 下線
        $row_ary[] = array(
             NULL
            ,NULL
            ,NULL
            ,NULL
            ,NULL
            ,NULL
            ,1
            ,"合計"
            ,Msi_Sys_Utils::filterComma($suryo)." 通"
            ,Msi_Sys_Utils::filterComma($sum)." 円"
        );
        $pdfObj->write_table($set_ary, self::$row_height, $row_ary, self::$row_count, NULL, 1, $head_ary);
        return App_PdfKanriLib::STATUS_OK;
    }
    /**
     * CSV出力メイン
     *
     * <AUTHOR> Kayo
     * @since 2014/08/14
     * @param Msi_Sys_Db $db	データベース
     * @param string $cond	条件
     * @return	   viod
     */
    private function getCsv($db, $cond) {
        // 発注分
        $select = DataMapper_Saiken_Pdf1202::findCsv($db, $cond);
        if (count($select) == 0) {
            return null;
        }
        $csvData = array();
        // ヘッダー
        $csvData[] = Msi_Sys_Utils::strArrayify_qw('発行日 領収証名称 領収No 領収証名義 領収証但し書き 領収金額 印紙金額 事務担当 備考');
        foreach ($select as $value) {
            $csvData[] = array(
                $value['hako_ymd'],
                $value['ryosyu_nm'],
                $value['ryosyu_no'],
                $value['atena'],
                $value['tadashikaki'],
                Msi_Sys_Utils::filterComma($value['gokei_prc']),
                Msi_Sys_Utils::filterComma($value['inshi_zei_prc']),
                $value['jimu_tanto_nm'],
                $value['biko'],
            );
        }
        return $csvData;
    }
}
