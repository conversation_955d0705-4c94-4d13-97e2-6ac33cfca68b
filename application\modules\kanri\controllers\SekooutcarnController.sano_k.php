<?php
  /**
   * Ka<PERSON><PERSON>_SekooutcarnController
   *
   * カーニバル連携用出力 コントローラクラス
   *
   * @category   App
   * @package    controllers\Juchu
   * <AUTHOR> Mihara
   * @since      2015/xx/xx
   * @version    2019/06/xx sai 軽減税率対応
   * @version    2024/12/xx mihara SekooutcarnController.sano_k.php をコピーして作成. #11675 事前相談を出力対象. 項目追加.
   * @version    2025/04/xx mihara SekooutcarnController.sano_k.php をコピーして作成. #11675
   * @filesource 
   */

  /**
   * カーニバル連携用出力 コントローラクラス
   *
   * @category   App
   * @package    controllers\Juchu
   * <AUTHOR> Mihara
   * @since      2015/xx/xx
   */
class Kanri_SekooutcarnController extends Msi_Zend_Controller_Action
{
    /**
     * CSV出力設定情報
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param  string  キー名
     * @param  array   上書きする値
     * @return array   CSV出力設定情報
     */
    protected static function _getOutputRule($key, $overwriteOp=array())
    {
        static $_outputRule = null;
        if ( $_outputRule === null ) {
            $_outputRule = array(
                             'jutyu' => array(
                                              'file_prefix'   => 'JUTYU_T_',
                                              'isTitleOut'    => true, // タイトル行を設定するか
                                              'isTitle2Out'   => true, // 項目キー名(開発用)
                                              'output_fields' =>  Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
kaisya_cd          siten_cd             bumon_cd               jutyu_no                 jutyu_tan_cd
jutyu_ymd          seko_kbn             seko_nm                tuya_ymd                 tuya_str_time
tuya_end_time      tuya_basyo_nm        sogi_ymd               sogi_str_time            sogi_end_time
sogi_basyo_nm      kasou_ymd            kasou_str_time         kasou_end_time           kasou_basyo_nm
syukyo_nm          syuha_nm             jiin_nm                sogi_keitai_cd1          sogi_keitai_cd2
seko_kaihi_nyukin  seko_kaihi_waribiki  seko_kaihi_zan_nyukin  seko_kaihi_zan_waribiki  seko_kaihi_zei
seko_soki_kin      seko_soki_zei
hanso_basho1  hanso_basho2
member_id_1   apply_no_1     member_id_2   apply_no_2    member_id_3   apply_no_3
member_id_4   apply_no_4     member_id_5   apply_no_5
noukan_ts     noukan_basyo
tel_tanto     
iso_kbn       iso_tanto      iso_tanto2
seko_no
sibo_pl_cd    sibo_pl_nm     keisatsu_nm   pickup_ymd    ofuse_prc
END_OF_TXT
                                                                                                 ),
                                              // key_title_map: この対応情報があればタイトル項目名を置き換えます
                                              // 対応がない場合は、タイトル項目名として項目キー名をそのまま使います
                                              'key_title_map' => array( 
                                                                       'kaisya_cd'    => '会社CD',
                                                                       'siten_cd'     => '支店CD',
                                                                       'bumon_cd'     => '部門CD',
                                                                       'jutyu_no'     => '受注No',
                                                                       'jutyu_tan_cd' => '受注担当者CD',
                                                                       'jutyu_ymd'    => '受注日時',
                                                                       'seko_kbn'     => '施行区分',
                                                                       'seko_nm'      => '葬家名',
                                                                       'tuya_ymd'      => '通夜日付',
                                                                       'tuya_str_time' => '通夜開始時刻',
                                                                       'tuya_end_time' => '通夜終了時刻',
                                                                       'tuya_basyo_nm' => '通夜場所名',
                                                                       'sogi_ymd'      => '葬儀日付',
                                                                       'sogi_str_time' => '葬儀開始時刻',
                                                                       'sogi_end_time' => '葬儀終了時刻',
                                                                       'sogi_basyo_nm' => '葬儀場所名',
                                                                       'kasou_ymd'     => '火葬場日付',
                                                                       'kasou_str_time' => '火葬場開始時刻',
                                                                       'kasou_end_time' => '火葬場終了時刻',
                                                                       'kasou_basyo_nm' => '火葬場場所名',
                                                                       'syukyo_nm'      => '宗旨名称',
                                                                       'syuha_nm'       => '宗派名称',
                                                                       'jiin_nm'        => '寺院名称',
                                                                       'sogi_keitai_cd1' => '葬儀形態CD1',
                                                                       'sogi_keitai_cd2' => '葬儀形態CD2',
                                                                       'seko_kaihi_nyukin'       => '払込金額',
                                                                       'seko_kaihi_waribiki'     => '前納割引',
                                                                       'seko_kaihi_zan_nyukin'   => '残金',
                                                                       'seko_kaihi_zan_waribiki' => '残金割引',
                                                                       'seko_kaihi_zei'          => '掛金消費税',
                                                                       'seko_soki_kin'           => '早期利用費',
                                                                       'seko_soki_zei'           => '早期利用費消費税',
                                                                       'hanso_basho1'            => '搬送場所1',      // 2024/12 #11675
                                                                       'hanso_basho2'            => '搬送場所2',      // 2024/12 #11675
                                                                       'member_id_1'             => '加入番号1',      // 2024/12 #11675
                                                                       'apply_no_1'              => '契約番号1',      // 2024/12 #11675
                                                                       'member_id_2'             => '加入番号2',      // 2024/12 #11675
                                                                       'apply_no_2'              => '契約番号2',      // 2024/12 #11675
                                                                       'member_id_3'             => '加入番号3',      // 2024/12 #11675
                                                                       'apply_no_3'              => '契約番号3',      // 2024/12 #11675
                                                                       'member_id_4'             => '加入番号4',      // 2024/12 #11675
                                                                       'apply_no_4'              => '契約番号4',      // 2024/12 #11675
                                                                       'member_id_5'             => '加入番号5',      // 2024/12 #11675
                                                                       'apply_no_5'              => '契約番号5',      // 2024/12 #11675
                                                                       'noukan_ts'               => '納棺日時',       // 2024/12 #11675
                                                                       'noukan_basyo'            => '納棺場所',       // 2024/12 #11675
                                                                       'tel_tanto'               => '電話受付担当者', // 2024/12 #11675
                                                                       'iso_kbn'                 => '移送区分',       // 2024/12 #11675
                                                                       'iso_tanto'               => '移送担当者',     // 2024/12 #11675
                                                                       'iso_tanto2'              => '移送担当者2',    // 2025/01 #11675
                                                                       'seko_no'                 => 'FDN施行番号',    // 2025/04 #11675
                                                                       'sibo_pl_cd'              => '亡くなられた場所区分',  // 2025/04 #11675
                                                                       'sibo_pl_nm'              => '亡くなられた場所',      // 2025/04 #11675
                                                                       'keisatsu_nm'             => '警察署',                // 2025/04 #11675
                                                                       'pickup_ymd'              => 'お迎え先予定日',        // 2025/04 #11675
                                                                       'ofuse_prc'               => 'お布施'                 // 2025/04 #11675
                                                                       ),
                                              ),
                             'jutyum' => array(
                                              'file_prefix'   => 'JUTYUM_T_',
                                              'isTitleOut'    => true,
                                              'isTitle2Out'   => true,
                                              'output_fields' =>  Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
kaisya_cd          siten_cd         bumon_cd            jutyu_no          seko_kbn
tab_no             jutyum_seq       syo_cd              syo_nm            tnk
syohin_num         goukei_kin       uriage_kin          kojo_kin1         kojo_kin2
seko_kin           zei_rnd_kbn      zei_kbn             zei_rit        
service_flg        svc_rnd_kbn      svc_rit
course_syohin_flg  plan_syohin_flg  biko  reduced_tax_rate
END_OF_TXT
                                                                                                 ),
                                              'key_title_map' => array(
                                                                       'kaisya_cd'    => '会社CD',
                                                                       'siten_cd'     => '支店CD',
                                                                       'bumon_cd'     => '部門CD',
                                                                       'jutyu_no'     => '受注No',
                                                                       'seko_kbn'     => '施行区分',
                                                                       'tab_no'       => 'タブ番号',
                                                                       'jutyum_seq'   => '行番号',
                                                                       'syo_cd'       => '商品CD',
                                                                       'syo_nm'       => '商品名',
                                                                       'tnk'          => '商品単価',
                                                                       'syohin_num'   => '受注数量',
                                                                       'goukei_kin'   => '金額',
                                                                       'uriage_kin'   => '売上金額',
                                                                       'kojo_kin1'    => '控除額１',
                                                                       'kojo_kin2'    => '控除額２',
                                                                       'seko_kin'     => '施行金額',
                                                                       'zei_rnd_kbn'  => '消費税計算区分',
                                                                       'zei_kbn'      => '消費税区分',
                                                                       'zei_rit'      => '消費税率',
                                                                       'service_flg'  => 'サービス料対象フラグ',
                                                                       'svc_rnd_kbn'  => 'サービス料計算区分',
                                                                       'svc_rit'      => 'サービス料率',
                                                                       'course_syohin_flg' => '互助会コース商品フラグ',
                                                                       'plan_syohin_flg'   => 'プラン対象商品フラグ',
                                                                       'biko'              => '明細備考',
                                                                       'reduced_tax_rate'  => '軽減税率区分',
                                                                       // 'seko_zei_tai_kin'     => '外税対象金額',
                                                                       // 'seko_zei_kin'         => '外税消費税',
                                                                       // 'seko_utizei_tai_kin'  => '内税対象金額',
                                                                       // 'seko_utizei_kin'      => '内税消費税',
                                                                       // 'seko_hikazei_tai_kin' => '非課税対象金額',
                                                                       // 'seko_svc_tai_kin' => 'サービス料対象金額',
                                                                       // 'seko_svc_kin'     => 'サービス料金額',
                                                                       // 'seko_svc_zei_kin' => 'サービス料消費税',
                                                                       ),
                                              ),
                             'kokyaku' => array(
                                              'file_prefix'   => 'FNL_KOKYAKU_T_',
                                              'isTitleOut'    => true,
                                              'isTitle2Out'   => true,
                                              'output_fields' =>  Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
kaisya_cd        siten_cd      bumon_cd       seko_kbn     fnl_kokyaku_no
fnl_kokyaku_kbn  jutyu_no      sogi_jutyu_no  simei_nm     simei_kana
tel_no1          tel_no2       tel_no3        zip1         zip2
adr1             adr2          birth_ymd      sibo_ymd
mob_no1          mob_no2       mob_no3        sex          sibo_time
zokugara_nm
END_OF_TXT
                                                                                                 ),
                                              'key_title_map' => array(
                                                                       'kaisya_cd'       => '会社CD',
                                                                       'siten_cd'        => '支店CD',
                                                                       'bumon_cd'        => '部門CD',
                                                                       'seko_kbn'        => '施行区分',
                                                                       'fnl_kokyaku_no'  => '葬儀顧客情報No',
                                                                       'fnl_kokyaku_kbn' => '顧客区分',
                                                                       'jutyu_no'        => '受注No',
                                                                       'sogi_jutyu_no'   => '葬儀受注NO',
                                                                       'simei_nm'        => '氏名',
                                                                       'simei_kana'      => '氏名カナ',
                                                                       'tel_no1'         => '市外局番',
                                                                       'tel_no2'         => '市内局番',
                                                                       'tel_no3'         => '加入番号',
                                                                       'zip1'            => '郵便番号番号１',
                                                                       'zip2'            => '郵便番号番号２',
                                                                       'adr1'            => '顧客住所住所１',
                                                                       'adr2'            => '顧客住所住所２',
                                                                       'birth_ymd'       => '生年月日',
                                                                       'sibo_ymd'        => '死亡日付',
                                                                       'mob_no1'         => '携帯番号1', // 2024/12 #11675
                                                                       'mob_no2'         => '携帯番号2', // 2024/12 #11675
                                                                       'mob_no3'         => '携帯番号3', // 2024/12 #11675
                                                                       'sex'             => '性別',      // 2024/12 #11675
                                                                       'sibo_time'       => '死亡時刻',  // 2024/12 #11675
                                                                       // 'zokugara_cd'     => '続柄',      // 2024/12 #11675  削除
                                                                       'zokugara_nm'     => '続柄名',    // 2024/12 #11675
                                                                       ),
                                              ),
                             );
        }

        if ( !isset($_outputRule[$key]) ) {
            throw new Exception( "Kanri_SekooutcarnController: $key が不適切です" );
        }

        $rtn = array_merge($_outputRule[$key], $overwriteOp);

        return $rtn;
    }

    /**
     * index アクション
     *
     * <AUTHOR> Mihara
     * @since  2015/xx/xx
     */
	public function indexAction()
    {
        $this->_forward( 'main' );
	}

    /**
     * main アクション
     *
     * <AUTHOR> Mihara
     * @since  2015/xx/xx
     */
    public function mainAction()
    {
        $req = $this->getRequest();

        $params  = Msi_Sys_Utils::webInputs();
        // Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        App_Smarty::pushCssFile( ['app/kanri.sekooutcarn.css'] );

        App_Smarty::pushJsFile( ['app/kanri.sekooutcarn.js'] );

        list($bumon_cd, $bumon_nm) = App_Utils::getBumonDflt();

        // 初期値
        $data = array( 
                'dataApp' => array(
                                   's_sougi_from' => '', // Msi_Sys_Utils::getDate(),
                                   's_apply'      => null, // '1,2', // 1：葬儀 2：法事 6:その他
                                   's_output_kbn' => 1, // 0:すべて, 1:新規(未連携), (2:連携済み)
                                   's_status_kbn' => null, // 0:受付中(?) 1：見積中 2：施行中 3：請求済み 4:入金済み 9：その他（失注）
                                   // 's_staff_2'    => App_Utils::getTantoNm(),
                                   // 's_staff_2_cd' => App_Utils::getTantoCd(),
                                   's_bumon_cd'   => null, // $bumon_cd,
                                   's_bumon'      => null, // $bumon_nm,
                                   'init_search'  => false,
                                   's_page_size'  => 200,
                                   's_sort_k1'    => 1, //  0:降順, 1:昇順(古いほうが先頭)
                                   ),
                       );
        $json = Msi_Sys_Utils::json_encode( $data );
        $this->view->mydata_json = $json;

        $this->view->page_title = 'カーニバル連携';
    }

    /**
     * 検索結果表示 アクション
     * cf. Mref_SekodialogController::searchAction
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     */
	public function searchAction()
    {
        if ( isset($this->_params) ) {
            $params  = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }

        Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        list($module, $controller, $action) = Msi_Sys_Utils::getModContAct();

        // tpl として result-list を使うように設定して、mref/sekodialog/search をコールする
        Msi_Sys_Utils::globalSet( '_set_tpl', array('result-list', $controller, $module) );

        $this->_forward( 'search', 'sekodialogcarn', 'mref' );
    }

    /**
     * データ出力 アクション
     * cf. Mref_SekodialogController::searchAction
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     */
	public function dlsekoAction()
    {
        $params  = Msi_Sys_Utils::webInputs();
        Msi_Sys_Utils::debug( 'dlsekoAction params==>' . Msi_Sys_Utils::dump($params) );

        try {
            $seko_nos = $params['seko_nos'];
            Msi_Sys_Utils::debug( 'dlsekoAction seko_nos==>' . Msi_Sys_Utils::dump($seko_nos) );

            $isBdlStyle = isset($params['dl_type']) && $params['dl_type'] == 'bdl';
            $isTitleLine = isset($params['title_line']) && $params['title_line'] == '1';

            if ( count($seko_nos) <= 0 ) {
                throw new Exception( "施行Noが指定されていません" );
            }

            $data = Logic_SekoOutCarn::doSekoExport($seko_nos);

            $metaInfo = $data['_metaInfo'];
            $outMsg = '';
            $isOutMsgErr = false;
            $outMsg .= sprintf( "施行データ %s 件を出力しました", $metaInfo['updCnt'] );
            if ( $metaInfo['errCnt'] > 0 ) {
                $isOutMsgErr = true;
                $outMsg .= sprintf( "\n出力エラーが %s 件発生しました", $metaInfo['inCnt'] );
                $outMsg .= sprintf( "\n施行No: %s\n", implode(', ', $metaInfo['errSekos']) );
                if ( count($metaInfo['errInfo']) > 5 ) {
                    $outMsg .= implode( " \n", array_slice($metaInfo['errInfo'], 0, 4) ) . " ...";
                } else {
                    $outMsg .= implode( " \n", $metaInfo['errInfo']);
                }
            }

            $fileTs = Msi_Sys_Utils::getDatetime(null, 'YmdHis');

            $jutyu_arr = $data['jutyu_t']; // 受注T
            $jutyum_arr = $data['jutyum_t']; // 受注明細T
            $kokyaku_arr = $data['kokyaku_t']; // 葬儀顧客情報T

            // 出力設定
            $opArr = array( 'isTitleOut' => $isTitleLine, 'isTitle2Out' => $isTitleLine ); // タイトル行出力可否
            $jutyu_rule = static::_getOutputRule('jutyu', $opArr);
            $jutyum_rule = static::_getOutputRule('jutyum', $opArr);
            $kokyaku_rule = static::_getOutputRule('kokyaku', $opArr);

            // 一時ファイルで出力
            $jutyu_tempfile = static::_gen_csv_file($jutyu_arr, $jutyu_rule);
            $jutyum_tempfile = static::_gen_csv_file($jutyum_arr, $jutyum_rule);
            $kokyaku_tempfile = static::_gen_csv_file($kokyaku_arr, $kokyaku_rule);

            // ファイル名
            $jutyu_filename   = $jutyu_rule['file_prefix'] . $fileTs . '.csv';
            $jutyum_filename  = $jutyum_rule['file_prefix'] . $fileTs . '.csv';
            $kokyaku_filename = $kokyaku_rule['file_prefix'] . $fileTs . '.csv';
            
            // 形式に従ってクライアントに返す
            if ( $isBdlStyle ) {
                $bdl_filename = $fileTs . '.bdl';
                $files = array();
                $files[] = array( 'path' => $jutyu_filename, 'file' => $jutyu_tempfile );
                $files[] = array( 'path' => $jutyum_filename, 'file' => $jutyum_tempfile );
                $files[] = array( 'path' => $kokyaku_filename, 'file' => $kokyaku_tempfile );
                $outdata = Msi_Sys_Utils::genBdlFileData($files, 'carnival');
                Msi_Sys_Utils::out2way1( $outdata, $bdl_filename, 'application/x-msi-bdl',
                                         array('outMsg'=>$outMsg, 'isOutMsgErr'=>$isOutMsgErr) );
            } else {
                static::_csv_push($jutyu_filename, $jutyu_tempfile);
                static::_csv_push($jutyum_filename, $jutyum_tempfile);
                static::_csv_push($kokyaku_filename, $kokyaku_tempfile);
                Msi_Sys_Utils::out2wayFlush( array('outMsg'=>$outMsg, 'isOutMsgErr'=>$isOutMsgErr) );
            }

            return;
        } 
        catch ( Exception $e ) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $outData = array(
                             'status' => 'NG',
                             'msg' => $userMsg,
                             );
        }

        Msi_Sys_Utils::outJson( $outData );
    }

    /**
     * データと出力設定情報からCSVファイルを作成する
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param  array $data        データ  [['k1'=>'v1', 'k2'=>'v2',...], ['k1'=>'v1',...], ...]
     * @param  array $outputRule  出力設定情報
     * @return string             CSVファイルを格納した一時ファイル名
     */
    protected function _gen_csv_file( $data, $outputRule )
    {
        return App_Utils::genCsvFile( $data, $outputRule );
    }

    /**
     * 複数ファイル出力処理
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param  string $filename   ファイル名
     * @param  string $tempFile   データが格納された一時ファイル名
     * @return void
     */
    protected function _csv_push( $filename, $tempFile )
    {
        $buf = array( 'file' => $tempFile );
        Msi_Sys_Utils::out2wayPush($buf, $filename, 'text/csv' );
    }


}
