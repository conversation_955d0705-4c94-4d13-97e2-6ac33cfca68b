<?php /* Smarty version 2.6.28, created on 2025-06-05 11:48:36
         compiled from file:system/skaisya/main_tab_atta_files.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'file:system/skaisya/main_tab_atta_files.tpl', 4, false),)), $this); ?>
<div class="tab-contents-main">

  <?php unset($this->_sections['counter']);
$this->_sections['counter']['name'] = 'counter';
$this->_sections['counter']['loop'] = is_array($_loop=5) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['counter']['show'] = true;
$this->_sections['counter']['max'] = $this->_sections['counter']['loop'];
$this->_sections['counter']['step'] = 1;
$this->_sections['counter']['start'] = $this->_sections['counter']['step'] > 0 ? 0 : $this->_sections['counter']['loop']-1;
if ($this->_sections['counter']['show']) {
    $this->_sections['counter']['total'] = $this->_sections['counter']['loop'];
    if ($this->_sections['counter']['total'] == 0)
        $this->_sections['counter']['show'] = false;
} else
    $this->_sections['counter']['total'] = 0;
if ($this->_sections['counter']['show']):

            for ($this->_sections['counter']['index'] = $this->_sections['counter']['start'], $this->_sections['counter']['iteration'] = 1;
                 $this->_sections['counter']['iteration'] <= $this->_sections['counter']['total'];
                 $this->_sections['counter']['index'] += $this->_sections['counter']['step'], $this->_sections['counter']['iteration']++):
$this->_sections['counter']['rownum'] = $this->_sections['counter']['iteration'];
$this->_sections['counter']['index_prev'] = $this->_sections['counter']['index'] - $this->_sections['counter']['step'];
$this->_sections['counter']['index_next'] = $this->_sections['counter']['index'] + $this->_sections['counter']['step'];
$this->_sections['counter']['first']      = ($this->_sections['counter']['iteration'] == 1);
$this->_sections['counter']['last']       = ($this->_sections['counter']['iteration'] == $this->_sections['counter']['total']);
?>
  <?php $this->assign('i', ((is_array($_tmp=$this->_sections['counter']['iteration'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))); ?>
  <fieldset class="base_2 key-contains" data-key="attfile<?php echo ((is_array($_tmp=$this->_tpl_vars['i'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
">
    <label for="atta_file_<?php echo ((is_array($_tmp=$this->_tpl_vars['i'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
" class="">添付ファイル<?php echo mb_convert_kana($this->_tpl_vars['i'], 'N'); ?></label>
<span class="atta_file_<?php echo ((is_array($_tmp=$this->_tpl_vars['i'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
_cont">
  <input style="display:none" type="file" class="file_up" />
  <div class="label file_link file_nm"><div class="file_name"></div></div>
  <div id="atta_file_<?php echo ((is_array($_tmp=$this->_tpl_vars['i'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
_clip" class="label dlg_clip"></div>
  <div class="label dlg_del file_clear"></div>
</span>
</fieldset>
<?php endfor; endif; ?>

</div><!-- /.tab-contents-main -->
<div class="tab-contents-side"><img src="" alt="" class="img-atta-file-tab" /></div><!-- /.tab-contents-side -->


<?php echo '

<!-- 証明書有効ルール テンプレート -->
<script type="text/template" id="tmpl-cert-kbn">
  <fieldset class="base_1">
    <label for="cert_kbn_x" class="">ルール-<span class="line_no"></span>
<span style="padding-left:10px">
    <a href="javascript:void(0)" class="delete my-readonly-hidden"><i title="この行を削除します"
                                                                       class="glyphicon glyphicon-trash"></i></a>
    <span style="margin-left:5px"> </span>
    <a href="javascript:void(0)" class="add my-readonly-hidden"><i title="直下に行追加します"
                                                                   class="glyphicon glyphicon-plus-sign"></i></a>
</span>
    </label>
    <input type="hidden" class="cert_kbn sel2-picker" style="width:150px" />
    <input type="text" class="txt msi_biko text-right" style="width:300px" title="可否設定する証明書のシリアルNo"
           placeholder="(ex. EDB7EA6B4959CDEF | *)" />
  </fieldset>
</script>

'; ?>

