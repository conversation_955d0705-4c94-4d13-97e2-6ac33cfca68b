{include file="fdn_head_std.tpl"}
{include file="fdn_header_0.tpl"}

<form id="my-form-id" method="post" class="{$ctxt_readonly}">
    <div id="main">
        <div id="order" >
            <div class="page-title"><span>{$page_title}</span></div>   
            <div id="searchbtnarea">
                <input name="btn_search" id="btn_search" type="button" value="検索">
                <input name="btn_clear" id="btn_clear" type="button" value="クリア">
                <input name="btn_search_next" id="btn_search_next" class="act-button-search-next" type="button" title="次ページ" value="次" />
                <input name="btn_search_prev" id="btn_search_prev" class="act-button-search-prev" type="button" title="前ページ" value="前" />
            </div>
            <div class="search">
                <!-- 検索条件 -->
                <fieldset class="base_3">
                    <label for="s_bumon" class="lbl_s_bumon option">会社・部門</label>
                    <input type="hidden" id="s_bumon_cd" class="cls_s_bumon_cd"/>
                    <label for="s_seko_tanto_nm" class="lbl_s_seko_tanto_nm option">施行担当者</label>
                    <input name="s_seko_tanto_nm" id="s_seko_tanto_nm" type="text" class="txt" value="" readonly/>
                    <div class="label dlg_common s_seko_tanto-ref cursor-pointer"></div>
                </fieldset>
                <fieldset class="base_1">
                    <label for="s_ryosyu_prt_status" class="lbl_s_ryosyu_prt_status option">領収証出力状況</label>
                    <input type="hidden" name="s_ryosyu_prt_status" id="s_ryosyu_prt_status" class="cls_s_ryosyu_prt_status"/>
                    <label for="s_ryosyu_irai_kbn" class="lbl_s_ryosyu_irai_kbn option">領収証依頼区分</label>
                    <input type="hidden" name="s_ryosyu_irai_kbn" id="s_ryosyu_irai_kbn" class="cls_s_ryosyu_irai_kbn"/>
                    <label for="s_ryosyu_no" class="lbl_s_ryosyu_no option">領収証No</label>
                    <input name="s_ryosyu_no" id="s_ryosyu_no" type="text" class="txt" value="" maxlength="10"/>
                    <label for="s_pay_method_cd" class="lbl_s_pay_method_cd option">支払方法</label>
                    <input type="hidden" name="s_pay_method_cd" id="s_pay_method_cd" class="cls_s_pay_method_cd"/>
                    <label class="lbl_dummy"></label>
                </fieldset>
                <fieldset class="base_2">
                    <label for="s_moushi_kbn" class="lbl_s_moushi option">申込区分</label>
                    <input type="hidden" name="s_moushi_kbn" id="s_moushi_kbn" class="cls_s_moushi_kbn"/>
                    <label for="s_seko_no" class="lbl_s_seko_no option">施行番号</label>
                    <input name="s_seko_no" id="s_seko_no" type="text" class="txt" value="" maxlength="10"/>
                    <div class="label dlg_common s_seko_no-ref cursor-pointer"></div>
                    <label for="s_juchu_den_no" class="lbl_s_juchu_den_no option">受注伝票No</label>
                    <input name="s_juchu_den_no" id="s_juchu_den_no" type="text" class="txt" value="" maxlength="10"/>
                    <label for="s_seikyu_no" class="lbl_s_seikyu_no option">請求No</label>
                    <input name="s_seikyu_no" id="s_seikyu_no" type="text" class="txt" value="" maxlength="10"/>
                    <label for="s_seikyu_nm" class="lbl_s_seikyu_nm option">請求先名</label>
                    <input name="s_seikyu_nm" id="s_seikyu_nm" type="text" class="txt" value="" maxlength="60"/>
                </fieldset>
                <fieldset class="base_3">
                    <label for="s_seko_ymd" class="lbl_s_seko_ymd option">施行日/納品日</label>
                    <input name="s_seko_ymd_from" id="s_seko_ymd_from" type="text" class="txt my-type-date" value="" maxlength="10"/>
                    <div class="label dlg_date dlg_common cursor-pointer"></div>
                    <div class="label lbl_range">～</div>
                    <input name="s_seko_ymd_to" id="s_seko_ymd_to" type="text" class="txt my-type-date" value="" maxlength="10"/>
                    <div class="label dlg_date dlg_common dlg_date_to cursor-pointer"></div>
                </fieldset>
            </div><!-- /.search -->
            <div class="result-list">
                <div class="header">
                    <table>
                        <tr>
                            <td class="row chkAllToggle" rowspan="2">選択</td>
                            <td class="oya_bumon_nm">会社</td>
                            <td class="moushi_kbn" rowspan="2">申込区分</td>
                            <td class="seko_no">施行番号</td>
                            <td class="sougi_ymd" rowspan="2">施行日/納品日</td>
                            <td class="seikyu_den_no" rowspan="2">請求No</td>
                            <td class="sekyu_nm" rowspan="2">請求先名</td>
                            <td class="sekyu_prc" rowspan="2">請求金額</td>
                            <td class="ryosyu_irai_kbn">領収証依頼区分</td>
                            <td class="ryosyu_prc" rowspan="2">領収証金額</td>
                            <td class="ryosyu_prt_status" rowspan="2">領収証<br>出力状況</td>
                        </tr>
                        <tr>
                            <td class="bumon_nm">部門</td>
                            <td class="juchu_den_no">受注伝票No</td>
                            <td class="pay_method_cd">支払方法</td>
                        </tr>
                    </table>
                </div><!-- /.header -->
                <div class="list">
                    <table class='list_table'>
                    </table>
                </div>
            </div>
            <!-- 処理ボタン -->
            <div class="buttons sekolist-cmd-buttons">
                <input type="button" name="btn_kobetu" id="btn_kobetu"  value="個別発行" />
                <input type="button" name="btn_ikatu"  id="btn_ikatu"   value="一括発行" />
                <div class="air-app-div"><a href="{$app_base}air/default_badge.html" target="_blank" title="一括ダウンロード用の AIR アプリを導入します. 別画面を開きます">AIRアプリ導入<i class="glyphicon glyphicon-new-window"></i></a></div>
            </div><!-- /.buttons -->
        </div><!-- /#order -->
    </div><!-- /#main -->
</form><!-- /#my-form-id -->

<script id="my-data-init-id" type="application/json">
    {$mydata_json|smarty:nodefaults}
</script>

{literal}
<script type="text/template" id="tmpl-result">
    <tr class="result-list-sel">
        <td class="row not-chkAllToggle" rowspan="2">
            <span class="radio_set">
                <label for="ryoshu_check_<%=idx%>" class="lbl_ryoshu_check lbl_com_check lbl_select_kbn "></label>
                <input name="ryoshu_check_<%=idx%>" id="ryoshu_check_<%=idx%>" class="ryoshu_check cursor-pointer" type="checkbox" value="1" />
            </span>
        </td>
        <td class="oya_bumon_nm"></td>
        <td class="moushi_kbn" rowspan="2"></td>
        <td class="seko_no"></td>
        <td class="sougi_ymd" rowspan="2"></td>
        <td class="seikyu_den_no" rowspan="2"></td>
        <td class="sekyu_nm" rowspan="2"></td>
        <td class="sekyu_prc" rowspan="2"></td>
        <!--td class="nyukin_den_no" rowspan="2"></td>
        <td class="nyukin_ymd" rowspan="2"></td>
        <td class="pay_method_nm" rowspan="2"></td-->
        <td class="ryosyu_irai_kbn_m">
            <input type="hidden" name="ryosyu_irai_kbn" class="ryosyu_irai_kbn cls_ryosyu_irai_kbn"/>
        </td>
        <td class="ryosyu_prc" rowspan="2"></td>
        <td class="ryosyu_prt_status_nm" rowspan="2"></td>
    </tr>
    <tr class="result-list-sel">
        <td class="bumon_nm"></td>
        <td class="juchu_den_no"></td>
        <td class="pay_method_nm"></td>
    </tr>
</script>
{/literal}
{include file="fdn_footer_std.tpl"}
