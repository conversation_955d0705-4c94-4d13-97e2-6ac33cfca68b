<?php

/**
 * Saiken_TairyulistController
 *
 * （ライフランド） 滞留債権管理 コントローラクラス
 *
 * @category   App
 * @package    controllers\Saiken
 * <AUTHOR> Tosaka
 * @since      2020/07/22
 * @version    2022/09/15 mihara 初期画面で条件を受けて検索実行. _reload_s_seikyu_den_no
 * @version    2025/05/28 mihara SeikyulistController.lifeland.php からコピーして作成
 * @filesource 
 */

/**
 * （ライフランド）滞留債権管理 コントローラクラス
 *
 * @category   App
 * @package    controllers\Saiken
 * <AUTHOR> Tosaka
 * @since      2020/07/22
 */
class Saiken_TairyulistController extends Msi_Zend_Controller_Action {

    /** コード区分: 1070=>承認状況 */
    const CODE_KBN_SHONIN = '1070';
    /** コード区分: 7991=>申込区分 */
    const CODE_KBN_MOUSHI = '7991';
    /** コード区分: 9757=>支払方法 */
    const CODE_KBN_PAY_ALL = '9757';
    /** コード区分: 1130=>支払方法 */
    const CODE_KBN_PAY = '1130';
    /** コード区分: 7812=>支払方法 */
    const CODE_KBN_PAY_KYOKA = '7812';
    /** コード区分: 1060=>入金状況 */
    const CODE_KBN_NYUKIN = '1060';
    /** コード区分: 1060=>請求書郵送区分 */
    const CODE_KBN_YUSO = '7992';
    /** コード区分: 7990=>合算分割状況 */
    const CODE_KBN_BUNGAS = '7990';
    /** コード区分: 1050=>請求状況 */
    const CODE_KBN_SEKYU = '1050';
    /** コード区分: 8554=>委託者番号 */
    const CODE_KBN_ITAKU = '8554';
    /** コード区分: 8567=>消費税区分(貸倒) */
    const CODE_KBN_ZEI_BADDEBT = '8567';
    /** コード区分: 8568=>貸倒損失勘定科目 */
    const CODE_KBN_BADDEBT = '8568';
    
    /** データ区分: 1=>葬儀 */
    const DATA_KBN_SOUGI = '1';
    /** データ区分: 2=>法事 */
    const DATA_KBN_HOUJI = '2';
    /** データ区分: 3=>単品 */
    const DATA_KBN_TAN = '3';
    /** データ区分: 4=>供花供物 */
    const DATA_KBN_BECHU = '4';
    
    /** ステータス: 3=>施行確定 */
    const STATUS_SEKO_KAKUTEI = '3';
    /** ステータス: 4=>請求確定 */
    const STATUS_SEKYU_KAKUTEI = '4';
    /** ステータス: 5=>入金済 */
    const STATUS_NYUKIN = '5';
    
    /** 申込区分: 99=>全て */
    const MOUSHI_KBN_ALL = '99';
    
    /** 承認状況: 0=>未承認 */
    const NO_SHONIN = '0';
    /** 承認状況: 1=>承認済 */
    const SUMI_SHONIN = '1';
    
    /** 受注先区分: 1=>喪家 */
    const JUCHUSAKI_MOKE = '1';
    
    /** 郵送区分: 0=>郵送 */
    const POST_KBN_YUSO = '0';
    /** 郵送区分: 1=>不要 */
    const POST_KBN_NOUSE = '1';
    /** 郵送区分: 2=>未消込 */
    const POST_KBN_MIKESHI = '2';
    /** 郵送区分: 3=>現金未収 */
    const POST_KBN_MISYU = '3';
    /** 分割合算区分: 0=>通常 */
    const BUNGAS_KBN_NOM = '0';
    /** 分割合算区分: 1=>分割元 */
    const BUNGAS_KBN_BUNMOTO = '1';
    /** 分割合算区分: 2=>分割先 */
    const BUNGAS_KBN_BUNSAKI = '2';
    /** 分割合算区分: 10=>合算元 */
    const BUNGAS_KBN_GASMOTO = '10';
    /** 分割合算区分: 20=>合算先 */
    const BUNGAS_KBN_GASSAKI = '20';
    
    /** 支払方法: 1=>振込 */
    const PAY_FURI = '1';
    /** 支払方法: 2=>現金 */
    const PAY_GENKIN = '2';
    /** 支払方法: 6=>清算済 */
    const PAY_SEISAN = '6';
    
    /** 印紙税区分: 1=>領収証 */
    const INSHI_ZEI_RYOSYU = '1';
    
    /** バーコード生成基準額 */
    const BARCODE_SEIKYU_BASE = 300000;
    
    /** バーコード固定項目*/
    /** バーコード項目①：AI */
    const BARCODE_ITEM1 = '91';
    /** バーコード項目②：国・商品メーカーコード */
    const BARCODE_ITEM2 = '908171';
    /** バーコード項目⑤：再発行区分 */
    const BARCODE_ITEM5 = '0';
    /** バーコード項目⑥：支払期限 */
    const BARCODE_ITEM6 = '999999';
    
    /** 入金区分(明細)3：貸倒損失 */
    const NYUKN_KBN_BADDEBT = '3';
    /** 入金区分(ヘッダー)4：貸倒損失 */
    const NYU_KBN_BADDEBT = '4';
    /** 入金区分5：香典 */
    const NYU_KBN_KOUDEN = '5';
    /** 科目コード：施行未収入金 */
    const KAMOKU_SEKO_MISHU = '113004';
    
    /** 会員区分 400：アスカラメイト */
    const KAIIN_KBN_A = '400';
    /** 会員区分 500：ファーストステップ */
    const KAIIN_KBN_F = '500';

    /**
     * index アクション
     *
     * <AUTHOR> Mihara
     * @since  2014/04/18
     */
    public function indexAction() {
        $this->_forward('main');
    }

    /**
     * main アクション
     *
     * <AUTHOR> Tosaka
     * @since  2020/04/18
     */
    public function mainAction() {
        $req = $this->getRequest();
        $params = Msi_Sys_Utils::webInputs();
        $db = Msi_Sys_DbManager::getMyDb();
        // Msi_Sys_Utils::debug( 'params==>' . Msi_Sys_Utils::dump($params) );

        App_Smarty::pushCssFile(['app/saiken.tairyulist.css']);
        App_Smarty::pushJsFile(['app/kaiin/kaikokyakudlg.js','app/saiken.tairyulist.js']);
        $hallData = App_Utils::getDfltHallArea();
        $kaisyacd = App_Utils::getCtxtKaisyaEasy();
        $kaisya = DataMapper_KaisyaInfo::find($db);

        // 初期値
        $data = array(
            'dataApp' => array(
                's_oya_bumon' => $hallData[1],
                's_bumon' => $hallData[0],
                's_seko_tanto' => null,
                's_seko_tanto_cd' => null,
                's_shonin' => static::NO_SHONIN,
                's_nyukin_st' => null,
                's_sekyu_st' => null,
                's_moushi' => null,
                's_kaishu_ymd_from' => null,
                's_kaishu_ymd_to' => null,
                's_seko_no' => null,
                's_souke_nm' => null,
                's_k_nm' => null,
                's_sekyu_tel' => null,
                's_sekyu_nm' => null,
                's_seikyu_den_no' => null,
                'maturity_day' => $kaisya[0]['maturity_day'],
                'role_nm' => App_Utils2::getRoleNm(App_Utils::getTantoCd()),
                's_gassan_st' => null,
                's_keijo_ymd_from' => null,
                's_keijo_ymd_to' => null,
                's_denpyo_no' => null,
                's_kokyaku_no' => null,
                's_pay_method_cd' => null,
            ),
            'dataKbns' => array(
                'oya_bumon' => Logic_Cale_ComLogic::get_bumonBylogin(), // 会社(親部門)
                'bumon' => Logic_Cale_ComLogic::get_bumon2Bylogin(), // 部門
                'shonin_st' => DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_SHONIN)), // 承認状況
                'moushi_kbn' => DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_MOUSHI)), // 申込区分
                'pay_method_all' => DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_PAY_ALL)), // 支払方法
                'pay_method' => DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_PAY)), // 支払方法
                'pay_method_kyoka' => DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_PAY_KYOKA)), // 支払方法
                'nyukin_st' => DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_NYUKIN)), // 入金状況
                'yuso_kbn' => DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_YUSO)), // 請求書郵送区分
                'bun_gas' => DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_BUNGAS)), // 合算分割状況
                'sekyu_st' => DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_SEKYU)), // 請求状況
            ),
            'dataCol' => array()
        );

        // 初期画面で条件を受けて検索実行  2022/09/15 mihara
        if ( true ) {
            $s_seikyu_den_no = Msi_Sys_Utils::webInputVar('ini_seikyu_den_no', 'DIGIT', null, 'U');
            if ( strlen($s_seikyu_den_no) > 0 ) {
                // 検索条件の初期値を一部クリア
                $data['dataApp'] = Msi_Sys_Utils::elimArrayFlat( $data['dataApp'], <<< END_OF_TXT
s_oya_bumon  s_bumon  s_seko_tanto  s_seko_tanto_cd   s_shonin
s_sekyu_st   s_moushi
END_OF_TXT
                );
                $data['dataApp']['s_seikyu_den_no'] = $s_seikyu_den_no;
                $data['dataApp']['_reload_s_seikyu_den_no'] = $s_seikyu_den_no;
            }
        }

        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;

        $this->view->page_title = '滞留債権管理';
    }

    /**
     * 検索結果表示 アクション
     * cf. Mref_SekodialogController::searchAction
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     */
    public function searchAction() {
        try {
            if (isset($this->_params)) {
                $params = $this->_params;
            } else {
                $this->_params = $params = Msi_Sys_Utils::webInputs();
            }
            $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
            // Msi_Sys_Utils::debug( ' params==>' . Msi_Sys_Utils::dump($params) );

            $db = Msi_Sys_DbManager::getMyDb();
            $limit = Msi_Sys_Utils::easyGetVar($params, 'limit', 'DIGIT', 300);
            $cond = array(
//                '__etc_orderby' => array('seikyu_no desc',), // 2016/11/18 UPD Kayo
                '__etc_limit' => $limit + 1,
            );
            $seko_no = Msi_Sys_Utils::easyGetVar($params, 's_seko_no');
            $seikyu_den_no = Msi_Sys_Utils::easyGetVar($params, 's_seikyu_den_no');
            $denpyo_no = Msi_Sys_Utils::easyGetVar($params, 's_denpyo_no');
            $sekyu_nm = Msi_Sys_Utils::easyGetVar($params, 's_sekyu_nm');
            $sekyu_tel = Msi_Sys_Utils::easyGetVar($params, 's_sekyu_tel');
            $seko_tanto_cd = Msi_Sys_Utils::easyGetVar($params, 's_seko_tanto_cd');
            $moushi_kbn = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_moushi'));
            $souke_nm = Msi_Sys_Utils::easyGetVar($params, 's_souke_nm');
            $k_nm = Msi_Sys_Utils::easyGetVar($params, 's_k_nm');
            $oya_bumon_cd = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_oya_bumon'));
            $bumon_cd = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_bumon'));
            $kaishu_ymd_from = Msi_Sys_Utils::easyGetVar($params, 's_kaishu_ymd_from', 'DATE', null, 'emptyToNull');
            $kaishu_ymd_to = Msi_Sys_Utils::easyGetVar($params, 's_kaishu_ymd_to', 'DATE', null, 'emptyToNull');
            $keijo_ymd_from = Msi_Sys_Utils::easyGetVar($params, 's_keijo_ymd_from', 'DATE', null, 'emptyToNull');
            $keijo_ymd_to = Msi_Sys_Utils::easyGetVar($params, 's_keijo_ymd_to', 'DATE', null, 'emptyToNull');
            $nyukin_status = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_nyukin_st'));
            $pay_method_cd = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_pay_method_cd'));
            $seikyu_post_kbn = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_yuso_kbn'));
            $bun_gas_kbn_num = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_gassan_st'));
            $seikyu_approval_status = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_shonin'));
            $s_sekyu_st = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_sekyu_st'));
            $kokyaku_no = Msi_Sys_Utils::emptyToNull(Msi_Sys_Utils::easyGetVar($params, 's_kokyaku_no'));

            // 部分一致
            foreach (Msi_Sys_Utils::strArrayify_qw('seko_no sekyu_tel seikyu_den_no denpyo_no kokyaku_no') as $k) {
                if (strlen($$k) > 0) {
                    $cond[$k] = array('~', $$k);
                }
            }
            // 完全一致
            foreach (Msi_Sys_Utils::strArrayify_qw('seko_tanto_cd oya_bumon_cd bumon_cd pay_method_cd '
                    . 'seikyu_post_kbn nyukin_status bun_gas_kbn_num seikyu_approval_status') as $k) {
                if (isset($$k) && strlen($$k) > 0) {
                    $cond[$k] = $$k;
                }
            }
            if ( strlen($kaishu_ymd_from) > 0 ) {
                $cond['__x1'] = array( 'x', "kaishu_ymd >= :x1_1", array('x1_1'=>$kaishu_ymd_from) );
            }
            if ( strlen($kaishu_ymd_to) > 0 ) {
                $cond['__x2'] = array( 'x', "kaishu_ymd <= :x2_1", array('x2_1'=>$kaishu_ymd_to) );
            }
            if ( strlen($keijo_ymd_from) > 0 ) {
                $cond['__x3'] = array( 'x', "keijo_ymd >= :x3_1", array('x3_1'=>$keijo_ymd_from) );
            }
            if ( strlen($keijo_ymd_to) > 0 ) {
                $cond['__x4'] = array( 'x', "keijo_ymd <= :x4_1", array('x4_1'=>$keijo_ymd_to) );
            }
            // 申込区分
            // お客様名検索で漢字・カナ両方に対応
            if (mb_strlen($souke_nm) > 0) {
                $cond['__raw_souke_nm'] = "(souke_nm LIKE '%" . $souke_nm . "%' OR souke_knm LIKE '%" . $souke_nm . "%')";
            }
            // 故人名検索で漢字・カナ両方に対応
            if (mb_strlen($k_nm) > 0) {
                $cond['__raw_kojin_nm'] = "(k_nm LIKE '%" . $k_nm . "%' OR k_knm LIKE '%" . $k_nm . "%')";
            }
            // 請求先名検索で漢字・カナ両方に対応
            if (mb_strlen($sekyu_nm) > 0) {
                $cond['__raw_seikyu_nm'] = "(sekyu_nm LIKE '%" . $sekyu_nm . "%' OR sekyu_knm LIKE '%" . $sekyu_nm . "%')";
            }
            if (mb_strlen($moushi_kbn) > 0) {
                // 99はアフター以外
                if ($moushi_kbn == static::MOUSHI_KBN_ALL) {
                    $cond['__raw_data_kbn'] = "data_kbn NOT IN (3,20,21,22)";
                } else {
                    $cond['data_kbn'] = DataMapper_Utils::condOneOf( 'data_kbn', $moushi_kbn, 'dk2_' );
                }
            } else {
                $cond['__raw_data_kbn'] = "data_kbn NOT IN (20,21,22)";
            }
            // 請求状況は請求回数で判別
            if (isset($s_sekyu_st) && $s_sekyu_st == '1') {
                $cond['__raw_sekkyu_kaisu'] = "sekkyu_kaisu >= 1";
            } else if (isset($s_sekyu_st)) {
                $cond['__raw_sekkyu_kaisu'] = "sekkyu_kaisu = 0";
            } else {
                $cond['__raw_sekkyu_kaisu'] = "sekkyu_kaisu >= 0";
            }
            if (!isset($bumon_cd)) {
                $bumon_ref = App_Utils2::getBumonRef();
                if (isset($bumon_ref) && Msi_Sys_Utils::myCount($bumon_ref) > 0) {
                    // 参照権限部門を条件に追加
                    $cond['__x99'] = DataMapper_Utils::condOneOf('bumon_cd', implode(',', $bumon_ref));
                } else if (isset($bumon_ref)) {
                    // 参照権限部門が無い場合は無し
                    $rtnData = array(
                        'status' => 'NG',
                        'msg' => '該当するデータはありません',
                    );
                    Msi_Sys_Utils::outJson($rtnData);
                    return;
                }
            }

            $select = DataMapper_SekyuExData::findForSeikyulist($db, $cond);

            $data = array();
            $count = 0;
            foreach ($select as $rec) {
                // 喪家のものは除外する
                if (isset($rec['juchusaki_kbn']) && $rec['juchusaki_kbn'] === static::JUCHUSAKI_MOKE) {
                    continue;
                }
                $count++;
                if ($count > $limit) {
                    break;
                }
                // 請求先情報テーブルが存在すればそこから情報を設定する
                $seikyuSekyuSakiInfo = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('seikyu_den_no' => $rec['seikyu_den_no']));
                if (count($seikyuSekyuSakiInfo) > 0) {
                    if (isset($seikyuSekyuSakiInfo[0]['free_kbn1']) && $seikyuSekyuSakiInfo[0]['free_kbn1'] == '1') {
                        $rec['sekyu_nm'] = $seikyuSekyuSakiInfo[0]['sekyu_nm'];
                    } else {
                        $rec['sekyu_nm'] = $seikyuSekyuSakiInfo[0]['sekyu_nm1'].$seikyuSekyuSakiInfo[0]['sekyu_nm2'];
                    }
                    // 供花供物の場合は送付先情報を表示する
                    if ($rec['denpyo_data_kbn'] === '4') {
                        $rec['sekyu_tel'] = $seikyuSekyuSakiInfo[0]['soufu_tel'];
                    } else {
                        $rec['sekyu_tel'] = $seikyuSekyuSakiInfo[0]['sekyu_tel'];
                    }
                    $rec['sekyu_addr'] = $seikyuSekyuSakiInfo[0]['soufu_addr1'].$seikyuSekyuSakiInfo[0]['soufu_addr2'];
                }
                // 分割合算されていたら元の受注伝票番号を取得する
                if (isset($rec['bun_gas_seikyu_den_no'])) {
                    $bungasSekyu = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $rec['bun_gas_seikyu_den_no']));
                    if (count($bungasSekyu) > 0) {
                        $uriData = DataMapper_UriageDenpyo2::find($db, array('seikyu_no' => $bungasSekyu[0]['uri_den_no']));
                        if (count($uriData) > 0) {
                            $rec['juchu_denpyo_no'] = $uriData[0]['denpyo_no'];
                            $rec['uri_den_no'] = $uriData[0]['uri_den_no'];
                        }
                    }
                }
                // アフター以外で赤伝がある場合は最新のものを訂正計上日の初期値にする
                $historyData = DataMapper_UriageDenpyoHistory::find($db, array('uri_den_no' => $rec['uri_den_no'],'aka_kuro_kbn' =>-1,'__etc_orderby' => array('history_no DESC')));
                if (count($historyData) > 0) {
                    $rec['fix_keijo_ymd'] = Msi_Sys_Utils::getDate(strtotime($historyData[0]['keijo_ymd']), 'Y/m/d');
//                    $rec['fix_keijo_ymd'] = $historyData[0]['keijo_ymd'];
                }
                // 郵送区分がNULLの場合設定する
                if (!isset($rec['seikyu_post_kbn'])) {
                    $rec['seikyu_post_kbn'] = static::POST_KBN_YUSO;
                }
                $postKbn = DataMapper_CodeNmMst::find($db, array('code_kbn' => static::CODE_KBN_YUSO, 'kbn_value_cd_num' => $rec['seikyu_post_kbn']));
                if (count($postKbn) > 0) {
                    $rec['yuso_kbn_nm'] = $postKbn[0]['kbn_value_lnm'];
                }
                $rec['oddEven'] = $count % 2 ? 'odd' : 'even';
                $rec['my_id'] = $rec['seikyu_den_no'] . '-' . ( strlen($rec['seko_no']) > 0 ? $rec['seko_no'] : '' );
                $rec['row'] = $count;
                $data[] = $rec;
            }

            // Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($data) );
        } catch (Msi_Sys_Exception_InputException $e) {
            $err = $e->getMessage();
            Msi_Sys_Utils::debug('検索条件エラー=>' . $err);
            $msg = '検索条件エラーです';
        } catch (Exception $e) {
            $err = $e->getMessage();
            Msi_Sys_Utils::err(basename(__FILE__) . ': ' . $err);
            $msg = '内部エラーです';
        }
        if (isset($msg)) {
            $rtnData = array(
                'status' => 'NG',
                'msg' => $msg,
            );
        } else {
            $rtnData = array(
                'status' => 'OK',
                'msg' => '',
                'dataApp' => $dataApp,
                'dataCol' => $data,
            );
        }
        Msi_Sys_Utils::outJson($rtnData);
    }
    
    /**
     * 請求書承認確定処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/07/22
     * @param string $bumon_kbn 部門区分
     * @return  array 
     */
    protected function kakuteiAction() {
//        App_DevCoverage_Manager::easyStart();
        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }
        $this->_shoninDate = Msi_Sys_Utils::getDatetimeStd();
        $this->_shoninTantoCd = App_Utils::getTantoCd();
        try {
            $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
            $dataSeikyu = Msi_Sys_Utils::json_decode($params['dataSeikyuJson']);
            // Msi_Sys_Utils::debug( ' params==>' . Msi_Sys_Utils::dump($params) );
            $db = Msi_Sys_DbManager::getMyDb();
            $shoninDate = Msi_Sys_Utils::getDate();
            // ステータスを更新する
            foreach ($dataSeikyu as $row) {
                $seikyuDenpyo = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $row['seikyu_den_no']));
                $this->checkData($db, $row);
                $this->updateData($db, $row);
                // MOCに送信
//                if ($row['data_kbn'] == self::DATA_KBN_SOUGI || $row['data_kbn'] == self::DATA_KBN_HOUJI) {
//                    $dataSekoKihon = DataMapper_SekoKihon::find2($row['seko_no']);
//                    if (strlen($dataSekoKihon['sougi_ymd']) <= 0) {
//                        $dataSekoKihon['kijun_ymd'] = Msi_Sys_Utils::getDate();
//                    } else {
//                        $dataSekoKihon['kijun_ymd'] = $dataSekoKihon['sougi_ymd'];
//                    }
//                    $dataGojokaiMemberCol = $this->getGojokaiMember($row['seko_no']);
//                    if ($row['data_kbn'] == '2') {
//                        $dataKojinInfoCol = DataMapper_SekoKojinInfoHouji::find($db, array('seko_no' => $row['seko_no']));
//                        if (isset($dataKojinInfoCol[0]['hk_nm'])) {
//                            $dataSekoKihon['k_nm'] = $dataKojinInfoCol[0]['hk_nm'];
//                        }
//                    }
//                    $reqData = $this->makeMocData($db, $dataSekoKihon, $dataGojokaiMemberCol);
//                    if (count($reqData['ContractInfos']) > 0) {
//                        $rtnData = Logic_Exkaiin_Moc2KaiinUpdSeko2::doExec($db, $reqData);
//                        $msg = null;
//                        foreach ($rtnData['ContractInfos'] as $onerow) {
//                            if (isset($onerow['UpdateStatus']) && $onerow['UpdateStatus'] == '1') {
//                                $msg = '加入者番号(' . $onerow['ContractNo'] . ')' . $onerow['UpdateMessage'];
//                                continue;
//                            }
//                        }
//                        if (isset($msg)) {
//                            throw new Msi_Sys_Exception_InputException('異常終了しました: ' . $msg);
//                        }
//                        $Status = null;
//                        if (array_key_exists('Result', $rtnData) && array_key_exists('Status', $rtnData['Result'])) {
//                            $Status = +$rtnData['Result']['Status'];
//                        }
//                        if ($Status == 1) { // 1:異常終了
//                            $this->outWarnJson('異常終了しました: ' . $msg);
//                            throw new Msi_Sys_Exception_InputException('異常終了しました: ' . $msg);
//                        }
//                    }
//                }
                $db->commit();
            }
            $rtnData = array('status' => 'OK', 'msg' => '請求書承認をしました。');
            $rtnData['dataApp'] = $dataApp;
            $rtnData['dataCol'] = array();
            Msi_Sys_Utils::outJson($rtnData);
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            Msi_Sys_Utils::outJson($errData);
        }
    }

    /**
     * 請求書承認取消処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/07/22
     */
    protected function cancelAction() {
//        App_DevCoverage_Manager::easyStart();
        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }
        $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
        $dataSeikyu = Msi_Sys_Utils::json_decode($params['dataSeikyuJson']);
        // Msi_Sys_Utils::debug( ' params==>' . Msi_Sys_Utils::dump($params) );
        $db = Msi_Sys_DbManager::getMyDb();
        try {
            // ステータスを更新する
            foreach ($dataSeikyu as $row) {
                    // チェック処理を行う
                    $this->checkCancelData($db, $row);
                    $this->cancelUpdateData($db, $row);
            }
            $db->commit();
            $rtnData = array('status' => 'OK', 'msg' => '請求書承認を取消しました。');
            $rtnData['dataApp'] = $dataApp;
            $rtnData['dataCol'] = array();
            Msi_Sys_Utils::outJson($rtnData);
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            Msi_Sys_Utils::outJson($errData);
        }
    }

    /**
     * テンプレート変数設定
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     * @return void
     */
    protected function _setScriptVar() {
        // $this->view->dlg_title = '(タイトル)';
    }

    /**
     * 部門マスタ取得処理(プルダウン用)
     * 
     * <AUTHOR> Tosaka
     * @since      2020/07/22
     * @param string $bumon_kbn 部門区分
     * @return  array 
     */
    protected function getCdBumon($bumon_kbn) {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
            SELECT
                bm.bumon_cd AS id
                ,bm.bumon_lnm AS text
            FROM bumon_mst bm
            WHERE bm.bumon_kbn = :bumon_kbn
            ORDER BY bm.bumon_cd
        ";
        $select = $db->easySelect($sql, array('bumon_kbn' => $bumon_kbn));
        return $select;
    }
    
    /**
     * 保存処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * 
     * @return  array 
     */
    protected function saveAction() {
//        App_DevCoverage_Manager::easyStart();
        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }
        $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
        $dataSeikyu = Msi_Sys_Utils::json_decode($params['dataSeikyuJson']);
        // Msi_Sys_Utils::debug( ' params==>' . Msi_Sys_Utils::dump($params) );
        $db = Msi_Sys_DbManager::getMyDb();
        // 納品書添付区分・支払方法・支払期日・請求書発行日を更新する
        foreach ($dataSeikyu as $row) {
            $upd = array();
            $where = array();
            // 請求伝票更新
            // 元々NULLだった場合のみ更新
            $seikyuData = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $row['seikyu_den_no']));
            if (!isset($seikyuData['seikyu_post_kbn'])) {
                // 画面データで3,4が設定されたものは1に変更して更新する
                if ($row['seikyu_post_kbn'] == static::POST_KBN_MIKESHI || $row['seikyu_post_kbn'] == static::POST_KBN_MISYU) {
                    $upd['seikyu_post_kbn'] = static::POST_KBN_NOUSE;
                } else {
                    $upd['seikyu_post_kbn'] = $row['seikyu_post_kbn'];
                }
            }
            $upd['k_free2'] = $row['yuso_check'];
            $upd['ryoshusho_syurui_kbn'] = $row['msi_check'];
            $upd['kaishu_ymd'] = $row['kaishu_ymd'];
            $upd['pay_method_cd'] = $row['pay_method_cd'];
//            $upd['d_free1'] = $row['seikyu_hakko_ymd'];
            $where['seikyu_den_no'] = $row['seikyu_den_no'];
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $upd, $where);
            $db->easyExecute($sql, $param);
            // 請求請求先情報も更新処理
            $sekyusakiUpd = array();
            $sekyusakiWhere = array();
            $sekyusakiUpd['kaishu_ymd'] = $row['kaishu_ymd'];
            $sekyusakiUpd['syorui_tenpu_kbn'] = $row['syorui_tenpu_kbn'];
            $sekyusakiUpd['pay_kbn'] = $row['pay_method_cd'];
            $sekyusakiWhere['seikyu_den_no'] = $row['seikyu_den_no'];
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_sekyu_saki_info", $sekyusakiUpd, $sekyusakiWhere);
            $db->easyExecute($sql, $param);
        }
//        $cnt = Logic_BrKozaUpdate::BrKozaMain($db, $row['seikyu_den_no'], false);
        $db->commit();
        $rtnData = array('status' => 'OK', 'msg' => '保存しました。');
        Msi_Sys_Utils::outJson($rtnData);
    }
    
    /**
     * バーコードデータ生成処理
     * 
     * <AUTHOR> Tosaka
     * @since      2021/xx/xx
     * @param      Msi_Sys_Db $db	データベース
     * @param      string  $seikyu_den_no 請求伝票№
     * @param      string  $seikyu_zan 請求残
     * @return     string $cvs_bar_cd バーコードデータ
     */
    protected function makeCvsBarCd($db, $seikyu_den_no, $seikyu_zan) {
        
        $cvs_bar_cd = null;
        // 請求伝票データ取得
        $seikyuData = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $seikyu_den_no)); 
        // 部門データ取得
        $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $seikyuData[0]['bumon_cd']));
        $itaku = DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_ITAKU, 'kbn_value_cd' => $bumonData[0]['oya_bumon_cd']));
        if (count($itaku) === 0) {
            return $cvs_bar_cd;
        }
        // 請求残が30万以下の場合はバーコードデータを生成する
        if ($seikyu_zan <= static::BARCODE_SEIKYU_BASE) {
            // ①AI
            $code1 = static::BARCODE_ITEM1;
            // ②国・商品メーカーコード
            $code2 = static::BARCODE_ITEM2;
            // ③委託者番号
            $code3 = $itaku[0]['kbn_value_cd_num'];
            // ④お客様番号(バーチャル口座番号＋請求番号)
            $code4 = $seikyuData[0]['br_koza_no'].$seikyu_den_no;
            // ⑤再発行区分
            $code5 = static::BARCODE_ITEM5;
            // ⑥支払期限日
            $code6 = static::BARCODE_ITEM6;
            // ⑦印紙フラグ
            // 印紙税マスタから取得して印紙税が発生すれば10なら0
            $shohizei = App_KeigenUtils::getSeikyuZanShohizei($db, $seikyu_den_no);
            $taisho_prc = array_sum(array_column($shohizei, 'zanPrc')) - array_sum(array_column($shohizei, 'zeiPrc'));
            $inshi_zei_prc = $this->getInshiInfo($db, $taisho_prc);
            if ($inshi_zei_prc === 0) {
                $inshi_flg = 0;
            } else {
                $inshi_flg = 1;
            }
            $code7 = $inshi_flg;
            // ⑧支払金額(請求残)
            $code8 = sprintf('%06d', $seikyu_zan);
            // ⑨全体チェックデジット
            $tmpcode = $code1.$code2.$code3.$code4.$code5.$code6.$code7.$code8;
            $code9 = $this->modulus10w31($tmpcode);
            $cvs_bar_cd = $tmpcode . $code9;
        }
        return $cvs_bar_cd;
    }
    
    /**
     * モジュラス10ウェイト3-1の処理
     *
     * <AUTHOR> Tosaka
     * @since  2021/xx/xx
     * @params stringr   $num
     * @return string    モジュラス10ウェイト3-1
     */
    protected function modulus10w31($num) {
        $arr = array_reverse(str_split($num));
        $t = 0;
        for ($i = 0; $i < count($arr); $i++) {
            $t += ( ($i + 1) % 2) == 0 ? intval($arr[$i]) : intval($arr[$i]) * 3;
        }
        $cd = intval(substr($t, strlen($t) - 1, 1));
        return $cd > 0 ? 10 - $cd : 0;
    }
    
    /**
     * 印紙税額取得処理
     *
     * <AUTHOR> Tosaka
     * @since  2021/xx/xx
     * @params stringr   $db
     * @params stringr   $seikyu_zan
     * @return string    印紙税額
     */
    protected function getInshiInfo($db, $taisho_prc){
        $prc = 0;
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT izm.inshi_zei_kingaku
        FROM inshi_zei_mst izm
        WHERE CURRENT_DATE BETWEEN izm.tekiyo_st_date AND izm.tekiyo_ed_date
        AND :inshi_taisho_prc BETWEEN izm.kingaku_from AND izm.kingaku_to
        AND inshi_zei_kbn = :inshi_zei_kbn

END_OF_SQL
        ,array('inshi_taisho_prc' => $taisho_prc, 'inshi_zei_kbn' => static::INSHI_ZEI_RYOSYU));
        if (count($select) > 0) {
            $prc = (int)$select[0]['inshi_zei_kingaku'];
        }
        return $prc;
    }
    
    /**
     * 承認時チェック処理
     *
     * <AUTHOR> Tosaka
     * @since  2021/xx/xx
     * @params stringr   $db
     * @params stringr   $seikyu_zan
     * @return string    印紙税額
     */
    protected function checkData($db, $data){
        
        // 請求伝票データと請求先情報データを取得する
        $seikyuDenpyo = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $data['seikyu_den_no']));
        $seikyuSakiInfo = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('seikyu_den_no' => $data['seikyu_den_no']));
        if (count($seikyuDenpyo) === 0) {
            throw new Exception(sprintf("伝票No(%s)のデータがありません", $data['seikyu_den_no']));
        }
        if (count($seikyuDenpyo) === 0) {
            throw new Exception(sprintf("請求先情報(%s)のデータがありません", $data['seikyu_den_no']));
        }
        // 既に承認済ならエラー
        if ($seikyuDenpyo[0]['seikyu_approval_status'] == 1) {
            throw new Msi_Sys_Exception_InputException("既に請求承認済です。");
        }
        // 分割合算状況が変更されている場合はエラー
        if ($data['bun_gas_kbn_num'] != $seikyuDenpyo[0]['bun_gas_kbn_num']) {
            throw new Exception(sprintf("伝票No(%s)の分割合算状況が変更されています。画面を更新してください。", $data['seikyu_den_no']));
        }
        // 振込銀行が未設定ならエラー
//        if (!isset($seikyuSakiInfo[0]['v_free3']) || strlen($seikyuSakiInfo[0]['v_free3']) == 0) {
//            throw new Exception(sprintf("伝票No(%s)の振込銀行が未設定です。", $data['seikyu_den_no']));
//        }
        // 郵送区分が「郵送」の場合、請求先名・請求書送付先名・送付先郵便番号・送付先住所1のいずれかがNULLのときはエラー
        $msg = '';
        // データ区分が葬儀OR法事の場合
        if ($data['denpyo_data_kbn'] === static::DATA_KBN_SOUGI || $data['denpyo_data_kbn'] === static::DATA_KBN_HOUJI) {
            if ($data['seikyu_post_kbn'] === static::POST_KBN_YUSO) {
                if (!isset($seikyuSakiInfo[0]['sekyu_nm1']) && isset($seikyuSakiInfo[0]['sekyu_nm2'])) {
                    $msg .= '請求先名,';
                }
                if (!isset($seikyuSakiInfo[0]['sekyu_soufu_nm'])) {
                    $msg .= '請求書送付先名,';
                }
                if (!isset($seikyuSakiInfo[0]['soufu_yubin_no']) || !isset($seikyuSakiInfo[0]['soufu_addr1'])) {
                    $msg .= '請求書送付先住所,';
                }
            }
        }
        // データ区分が葬儀OR法事の場合
        // 喪主の氏名・生年月日・領収証名義・送付先名(姓)・送付先郵便番号・送付先住所1の入力チェック
        // 領収証名義・送付先名(姓)・送付先郵便番号・送付先住所1は分割合算区分が通常の場合はチェック
        if ($data['denpyo_data_kbn'] === static::DATA_KBN_SOUGI || $data['denpyo_data_kbn'] === static::DATA_KBN_HOUJI) {
            $_kihonInfo = DataMapper_SekoKihon::find2($data['seko_no']);
            $kihonInfo = Msi_Sys_Utils::emptyToNullArr($_kihonInfo);
            $_sekyuSakiInfo = DataMapper_SekyuSakiInfo::findOne($db, array('seko_no' => $data['seko_no'], 'sekyu_cd' => $kihonInfo['sekyu_cd']));
            $sekyuSakiInfo = Msi_Sys_Utils::emptyToNullArr($_sekyuSakiInfo);
            if (!isset($kihonInfo['m_nm'])) {
                $msg .= '喪主名,';
            }
            if (!isset($kihonInfo['m_knm'])) {
                $msg .= '喪主名カナ,';
            }
//            if (!isset($kihonInfo['m_seinengappi_ymd_y'])) {
//                $msg .= '喪主生年月日,';
//            }
            if ($data['bun_gas_kbn_num'] == static::BUNGAS_KBN_NOM) {
                if (!isset($sekyuSakiInfo['ryosyu_meigi'])) {
                    $msg .= '領収証名義,';
                }
                if (!isset($sekyuSakiInfo['ryosyu_soufu_last_nm'])) {
                    $msg .= '領収証送付先名(姓),';
                }
                if (!isset($sekyuSakiInfo['ryosyu_soufu_yubin_no'])) {
                    $msg .= '領収証送付先郵便番号,';
                }
                if (!isset($sekyuSakiInfo['ryosyu_soufu_addr1'])) {
                    $msg .= '領収証送付先住所1,';
                }
            }
        }
        if (strlen($msg) > 0) {
            throw new Exception(sprintf(trim($msg, ',') . "が設定されていません(請求伝票番号：%s)", $data['seikyu_den_no']));
        }

        return;
    }
    
    /**
     * ステータス等更新処理
     *
     * <AUTHOR> Tosaka
     * @since  2021/xx/xx
     * @params stringr   $db
     * @params array   $row
     */
    protected function updateData($db, $row){
        
        $upd = array();
        $kihonupd = array();
        $kihonwhere = array();
        $seikyuwhere = array();
        // 請求伝票データ取得
        $seikyuDenpyo = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $row['seikyu_den_no']));
        if (Msi_Sys_Utils::myCount($seikyuDenpyo) === 0) {
            throw new Exception(sprintf("伝票No(%s)のデータがありません", $row['seikyu_den_no']));
        }
        // 請求残高が0なら入金済、それ以外は請求確定に設定
        if ($row['seikyu_zan'] == '0') {
            $upd['status_kbn'] = static::STATUS_NYUKIN;
            // 売上伝票を更新
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $seikyuDenpyo['uri_den_no']));
            $db->easyExecute($sql, $param);
        } else {
            $upd['status_kbn'] = static::STATUS_SEKYU_KAKUTEI;
        }
        // 分割OR合算されていたら元伝票も更新する
        if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_BUNSAKI) {
            // 分割先の場合は他の分割伝票の承認状況をチェックする
            $bunSakiDenpyo = DataMapper_SeikyuDenpyo::find($db, array('bun_gas_seikyu_den_no' => $row['bun_gas_seikyu_den_no']
                        , 'seikyu_approval_status' => static::NO_SHONIN
                        , '__raw_seikyu' => "seikyu_den_no <> '" . $row['seikyu_den_no'] . "'"));
            // 分割先が全て承認されていれば分割元のステータスも更新する
            if (count($bunSakiDenpyo) == 0) {
                $motoUpd = array();
                $motoSeikyuwhere = array();
                $motoUpd['seikyu_approval_status'] = static::SUMI_SHONIN;
                $motoUpd['status_kbn'] = static::STATUS_SEKYU_KAKUTEI;
                $motoSeikyuwhere['seikyu_den_no'] = $row['bun_gas_seikyu_den_no'];
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $motoUpd, $motoSeikyuwhere);
                $db->easyExecute($sql, $param);
                // データ区分が葬儀と法事の場合は施行基本情報のステータスも更新する
                if ($row['denpyo_data_kbn'] == static::DATA_KBN_SOUGI || $row['denpyo_data_kbn'] == static::DATA_KBN_HOUJI) {
                    $kihonwhere['seko_no'] = $row['seko_no'];
                    $kihonupd['status_kbn'] = static::STATUS_SEKYU_KAKUTEI;
                    $kihonupd['kakutei_ymd2'] = $this->_shoninDate;
                    $kihonupd['kakutei_tanto_cd2'] = $this->_shoninTantoCd;
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihonupd, $kihonwhere);
                    $db->easyExecute($sql, $param);
                }
            }
        } else if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_GASSAKI) {
            // 合算先の場合は合算元のステータスも更新する
            $gasMotoDenpyo = DataMapper_SeikyuDenpyo::find($db, array('bun_gas_seikyu_den_no' => $row['seikyu_den_no']));
            foreach ($gasMotoDenpyo as $moto) {
                $motoUpd = array();
                $motoSeikyuwhere = array();
                $motoUpd['seikyu_approval_status'] = static::SUMI_SHONIN;
                $motoUpd['status_kbn'] = static::STATUS_SEKYU_KAKUTEI;
                $motoSeikyuwhere['seikyu_den_no'] = $moto['seikyu_den_no'];
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $motoUpd, $motoSeikyuwhere);
                $db->easyExecute($sql, $param);
            }
            // データ区分が葬儀と法事の場合は施行基本情報のステータスも更新する
            if ($row['denpyo_data_kbn'] == static::DATA_KBN_SOUGI || $row['denpyo_data_kbn'] == static::DATA_KBN_HOUJI) {
                $kihonwhere['seko_no'] = $row['seko_no'];
                $kihonupd['status_kbn'] = static::STATUS_SEKYU_KAKUTEI;
                $kihonupd['kakutei_ymd2'] = $this->_shoninDate;
                $kihonupd['kakutei_tanto_cd2'] = $this->_shoninTantoCd;
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihonupd, $kihonwhere);
                $db->easyExecute($sql, $param);
            }
        } else if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_NOM) {
            // データ区分が葬儀と法事の場合は施行基本情報のステータスも更新する
            if ($row['denpyo_data_kbn'] == static::DATA_KBN_SOUGI || $row['denpyo_data_kbn'] == static::DATA_KBN_HOUJI) {
                if ($row['seikyu_zan'] == '0') {
                    $kihonupd['status_kbn'] = static::STATUS_NYUKIN;
                } else {
                    $kihonupd['status_kbn'] = static::STATUS_SEKYU_KAKUTEI;
                }
                $kihonwhere['seko_no'] = $row['seko_no'];
                $kihonupd['kakutei_ymd2'] = $this->_shoninDate;
                $kihonupd['kakutei_tanto_cd2'] = $this->_shoninTantoCd;
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihonupd, $kihonwhere);
                $db->easyExecute($sql, $param);
            }
        }
        $upd['seikyu_approval_status'] = static::SUMI_SHONIN;
        $seikyuwhere['seikyu_den_no'] = $row['seikyu_den_no'];
        $upd['shonin_dt1'] = $this->_shoninDate;
        $upd['input_tanto_cd'] = $this->_shoninTantoCd;
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $upd, $seikyuwhere);
        $db->easyExecute($sql, $param);
        // 会費集計処理
        $this->calcKaihiPrc($db, $row['seikyu_den_no']); 
        // 履歴作成処理
        // 分割OR合算されていたら元伝票を更新する
        if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_BUNSAKI) {
            // 分割先の場合は他の分割伝票の承認状況をチェックする
            $bunSakiDenpyo = DataMapper_SeikyuDenpyo::find($db, array('bun_gas_seikyu_den_no' => $row['bun_gas_seikyu_den_no']
                        , 'seikyu_approval_status' => static::NO_SHONIN
                        , '__raw_seikyu' => "seikyu_den_no <> '" . $row['seikyu_den_no'] . "'"));
            // 分割先が全て承認されていれば分割元のステータスも更新する
            if (count($bunSakiDenpyo) == 0) {
                $bunMotoDenpyo = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $row['bun_gas_seikyu_den_no']));
                $uriData = DataMapper_UriageDenpyo2::findOne($db, array('seikyu_no' => $bunMotoDenpyo['uri_den_no']));
                $historyData = $db->easySelect(
                        "SELECT uri_den_no
                    FROM uriage_denpyo_history
                    WHERE uri_den_no = :uri_den_no
                    AND aka_kuro_kbn = -1
                    AND zaimu_rendo_kbn = 0
                    AND delete_flg = 0"
                        , array('uri_den_no' => $uriData['uri_den_no']));
                // 売上伝票の財務連動区分が1:連携済かつ売上伝票履歴に未連携で論理削除されていない赤伝がない場合
                // 売上伝票に赤黒区分は-1:赤を設定して作成
                if ($uriData['zaimu_rendo_kbn'] == '1' && count($historyData) == 0) {
                    Logic_DenpyoHistoryMake::UriageHistory($db, $uriData, -1);
                }
                // 売上伝票の財務連動区分が1:連携済の場合
                // 売上伝票の財務連動区分を0:未連携、財務連動番号をクリアする。
                if ($uriData['zaimu_rendo_kbn'] == '1') {
                    $upd = array('zaimu_rendo_kbn' => 0, 'zaimu_rendo_date' => null, 'zaimu_tanto_cd' => null, 'zaimu_rendo_denno' => null);
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $uriData['uri_den_no']));
                    $db->easyExecute($sql, $param);
                }
                Logic_DenpyoHistoryMake::UriageHistory($db, $uriData, 0);
            }
        } else if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_GASSAKI) {
            // 合算先の場合は合算元も作成する
            $gasMotoDenpyo = DataMapper_SeikyuDenpyo::find($db, array('bun_gas_seikyu_den_no' => $row['seikyu_den_no']));
            foreach ($gasMotoDenpyo as $moto) {
                $uriData = DataMapper_UriageDenpyo2::findOne($db, array('seikyu_no' => $moto['uri_den_no']));
                $historyData = $db->easySelect(
                        "SELECT uri_den_no
                    FROM uriage_denpyo_history
                    WHERE uri_den_no = :uri_den_no
                    AND aka_kuro_kbn = -1
                    AND zaimu_rendo_kbn = 0
                    AND delete_flg = 0"
                        , array('uri_den_no' => $uriData['uri_den_no']));
                // 売上伝票の財務連動区分が1:連携済かつ売上伝票履歴に未連携で論理削除されていない赤伝がない場合
                // 売上伝票に赤黒区分は-1:赤を設定して作成
                if ($uriData['zaimu_rendo_kbn'] == '1' && count($historyData) == 0) {
                    Logic_DenpyoHistoryMake::UriageHistory($db, $uriData, -1);
                }
                // 売上伝票の財務連動区分が1:連携済の場合
                // 売上伝票の財務連動区分を0:未連携、財務連動番号をクリアする。
                if ($uriData['zaimu_rendo_kbn'] == '1') {
                    $upd = array('zaimu_rendo_kbn' => 0, 'zaimu_rendo_date' => null, 'zaimu_tanto_cd' => null, 'zaimu_rendo_denno' => null);
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $uriData['uri_den_no']));
                    $db->easyExecute($sql, $param);
                }
                Logic_DenpyoHistoryMake::UriageHistory($db, $uriData, 0);
            }
        } else if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_NOM) {
            $uriData = DataMapper_UriageDenpyo2::findOne($db, array('seikyu_no' => $seikyuDenpyo['uri_den_no']));
            $historyData = $db->easySelect(
            "SELECT uri_den_no
            FROM uriage_denpyo_history
            WHERE uri_den_no = :uri_den_no
            AND aka_kuro_kbn = -1
            AND zaimu_rendo_kbn = 0
            AND delete_flg = 0"
                    , array('uri_den_no' => $seikyuDenpyo['uri_den_no']));
            // 売上伝票の財務連動区分が1:連携済かつ売上伝票履歴に未連携で論理削除されていない赤伝がない場合
            // 売上伝票に赤黒区分は-1:赤を設定して作成
            if ($uriData['zaimu_rendo_kbn'] == '1' && count($historyData) == 0) {
                Logic_DenpyoHistoryMake::UriageHistory($db, $uriData, -1);
            }
            // 売上伝票の財務連動区分が1:連携済の場合
            // 売上伝票の財務連動区分を0:未連携、財務連動番号をクリアする。
            if ($uriData['zaimu_rendo_kbn'] == '1') {
                $upd = array('zaimu_rendo_kbn' => 0, 'zaimu_rendo_date' => null, 'zaimu_tanto_cd' => null, 'zaimu_rendo_denno' => null);
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $seikyuDenpyo['uri_den_no']));
                $db->easyExecute($sql, $param);
            }
            Logic_DenpyoHistoryMake::UriageHistory($db, $uriData, 0);
            // 葬儀か法事の場合は喪家供花も同様の処理をする
            if ($row['denpyo_data_kbn'] == self::DATA_KBN_SOUGI || $row['denpyo_data_kbn'] == self::DATA_KBN_HOUJI) {
                $mokeData = DataMapper_UriageDenpyo2::find($db, array('seko_no' => $row['seko_no'], 'juchusaki_kbn' => 1));
                foreach ($mokeData as $mokeOne) {
                    $historyData = $db->easySelect(
                    "SELECT uri_den_no
                    FROM uriage_denpyo_history
                    WHERE uri_den_no = :uri_den_no
                    AND aka_kuro_kbn = -1
                    AND zaimu_rendo_kbn = 0
                    AND delete_flg = 0"
                            , array('uri_den_no' => $mokeOne['uri_den_no']));
                    // 売上伝票の財務連動区分が1:連携済かつ売上伝票履歴に未連携で論理削除されていない赤伝がない場合
                    // 売上伝票に赤黒区分は-1:赤を設定して作成
                    if ($mokeOne['zaimu_rendo_kbn'] == '1' && count($historyData) == 0) {
                        Logic_DenpyoHistoryMake::UriageHistory($db, $mokeOne, -1);
                    }
                    // 売上伝票の財務連動区分が1:連携済の場合
                    // 売上伝票の財務連動区分を0:未連携、財務連動番号をクリアする。
                    if ($uriData['zaimu_rendo_kbn'] == '1') {
                        $upd = array('zaimu_rendo_kbn' => 0, 'zaimu_rendo_date' => null, 'zaimu_tanto_cd' => null, 'zaimu_rendo_denno' => null);
                        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $mokeOne['uri_den_no']));
                        $db->easyExecute($sql, $param);
                    }
                    Logic_DenpyoHistoryMake::UriageHistory($db, $mokeOne, 0);
                }
            }
        }
    }

    /**
     * ステータス等更新処理(承認取消)
     *
     * <AUTHOR> Tosaka
     * @since  2021/xx/xx
     * @params stringr   $db
     * @params array   $row
     */
    protected function cancelUpdateData($db, $row){
        
        $upd = array();
        $kihonupd = array();
        $kihonwhere = array();
        $seikyuwhere = array();
        // 請求伝票データ取得
        $seikyuDenpyo = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $row['seikyu_den_no']));
        if (Msi_Sys_Utils::myCount($seikyuDenpyo) === 0) {
            throw new Exception(sprintf("伝票No(%s)のデータがありません", $row['seikyu_den_no']));
        }
        $upd['status_kbn'] = static::STATUS_SEKO_KAKUTEI;
        // 売上伝票を更新する
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $seikyuDenpyo['uri_den_no']));
        $db->easyExecute($sql, $param);
        // 分割OR合算されていたら元伝票も更新する
        if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_BUNSAKI) {
            // 分割先の場合は他の分割伝票の承認状況をチェックする
            $bunSakiDenpyo = DataMapper_SeikyuDenpyo::find($db, array('bun_gas_seikyu_den_no' => $row['bun_gas_seikyu_den_no']
                        , 'seikyu_approval_status' => static::SUMI_SHONIN
                        , '__raw_seikyu' => "seikyu_den_no <> '" . $row['seikyu_den_no'] . "'"));
            // 分割先が全て承認されていれば分割元のステータスも更新する
            if (count($bunSakiDenpyo) == 0) {
                $motoUpd = array();
                $motoSeikyuwhere = array();
                $motoUpd['seikyu_approval_status'] = static::NO_SHONIN;
                $motoUpd['status_kbn'] = static::STATUS_SEKO_KAKUTEI;
                $motoSeikyuwhere['seikyu_den_no'] = $row['bun_gas_seikyu_den_no'];
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $motoUpd, $motoSeikyuwhere);
                $db->easyExecute($sql, $param);
                // データ区分が葬儀と法事の場合は施行基本情報のステータスも更新する
                if ($row['denpyo_data_kbn'] == static::DATA_KBN_SOUGI || $row['denpyo_data_kbn'] == static::DATA_KBN_HOUJI) {
                    $kihonwhere['seko_no'] = $row['seko_no'];
                    $kihonupd['status_kbn'] = static::STATUS_SEKO_KAKUTEI;
                    $kihonupd['kakutei_ymd2'] = null;
                    $kihonupd['kakutei_tanto_cd2'] = null;
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihonupd, $kihonwhere);
                    $db->easyExecute($sql, $param);
                }
            }
        } else if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_GASSAKI) {
            // 合算先の場合は合算元のステータスも更新する
            $gasMotoDenpyo = DataMapper_SeikyuDenpyo::find($db, array('bun_gas_seikyu_den_no' => $row['seikyu_den_no']));
            foreach ($gasMotoDenpyo as $moto) {
                $motoUpd = array();
                $motoSeikyuwhere = array();
                $motoUpd['seikyu_approval_status'] = static::NO_SHONIN;
                $motoUpd['status_kbn'] = static::STATUS_SEKO_KAKUTEI;
                $motoUpd['shonin_dt1'] = null;
                $motoUpd['input_tanto_cd'] = null;
                $motoSeikyuwhere['seikyu_den_no'] = $moto['seikyu_den_no'];
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $motoUpd, $motoSeikyuwhere);
                $db->easyExecute($sql, $param);
            }
            // データ区分が葬儀と法事の場合は施行基本情報のステータスも更新する
            if ($row['denpyo_data_kbn'] == static::DATA_KBN_SOUGI || $row['denpyo_data_kbn'] == static::DATA_KBN_HOUJI) {
                $kihonwhere['seko_no'] = $row['seko_no'];
                $kihonupd['status_kbn'] = static::STATUS_SEKO_KAKUTEI;
                $kihonupd['kakutei_ymd2'] = null;
                $kihonupd['kakutei_tanto_cd2'] = null;
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihonupd, $kihonwhere);
                $db->easyExecute($sql, $param);
            }
        } else if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_NOM) {
            // データ区分が葬儀と法事の場合は施行基本情報のステータスも更新する
            if ($row['denpyo_data_kbn'] == static::DATA_KBN_SOUGI || $row['denpyo_data_kbn'] == static::DATA_KBN_HOUJI) {
                $kihonupd['status_kbn'] = static::STATUS_SEKO_KAKUTEI;
                $kihonwhere['seko_no'] = $row['seko_no'];
                $kihonupd['kakutei_ymd2'] = null;
                $kihonupd['kakutei_tanto_cd2'] = null;
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihonupd, $kihonwhere);
                $db->easyExecute($sql, $param);
            }
        }
        $upd['seikyu_approval_status'] = static::NO_SHONIN;
        $seikyuwhere['seikyu_den_no'] = $row['seikyu_den_no'];
        $upd['shonin_dt1'] = null;
        $upd['input_tanto_cd'] = null;
        $upd['kaishu_ymd'] = null;
        $upd['d_free1'] = null;
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $upd, $seikyuwhere);
        $db->easyExecute($sql, $param);
        // 履歴作成処理
        // 分割OR合算されていたら元伝票を更新する
        if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_BUNSAKI) {
            // 分割の場合は他の分割先伝票の承認状況をチェックする
            $bunSakiDenpyo = DataMapper_SeikyuDenpyo::find($db, array('bun_gas_seikyu_den_no' => $row['bun_gas_seikyu_den_no']
                        , 'seikyu_approval_status' => static::NO_SHONIN
                        , '__raw_seikyu' => "seikyu_den_no <> '" . $row['seikyu_den_no'] . "'"));
            // 分割先が全て承認されていれば分割元のステータスも更新する
            $bunMotoDenpyo = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $row['bun_gas_seikyu_den_no']));
            $uriData = DataMapper_UriageDenpyo2::findOne($db, array('seikyu_no' => $bunMotoDenpyo['uri_den_no']));
            if (count($bunSakiDenpyo) == 0) {
                // 財務連動連携済なら赤伝票作成(アフターを除き計上日を訂正計上日で作成する)
                // そうでないなら履歴の最大値のものを削除
                if (isset($uriData['zaimu_rendo_kbn']) && $uriData['zaimu_rendo_kbn'] == '1') {
                    if ($uriData['data_kbn'] != self::DATA_KBN_TAN) {
                        $uriData['fix_keijo_ymd'] = $row['fix_keijo_ymd'];
                    }
                    Logic_DenpyoHistoryMake::UriageHistory($db, $uriData,-1);
                    // 財務連動関連の項目をリセット
                    $upd = array('zaimu_rendo_kbn'=>0,'zaimu_rendo_date'=>null,'zaimu_tanto_cd'=>null,'zaimu_rendo_denno'=>null);
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $uriData['uri_den_no']));
                    $db->easyExecute($sql, $param);
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo_msi", $upd, array('uri_den_no' => $uriData['uri_den_no']));
                    $db->easyExecute($sql, $param);
                } else {
                    Logic_DenpyoHistoryMake::DeleteUriageMaxHistory($db, $uriData['uri_den_no']);
                }
            }
        } else if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_GASSAKI) {
            // 合算先の場合は合算元も作成する
            $gasMotoDenpyo = DataMapper_SeikyuDenpyo::find($db, array('bun_gas_seikyu_den_no' => $row['seikyu_den_no']));
            foreach ($gasMotoDenpyo as $moto) {
                $uriData = DataMapper_UriageDenpyo2::findOne($db, array('seikyu_no' => $moto['uri_den_no']));
                // 財務連動連携済なら赤伝票作成(アフターを除き計上日を訂正計上日で作成する)
                // そうでないなら履歴の最大値のものを削除
                if (isset($uriData['zaimu_rendo_kbn']) && $uriData['zaimu_rendo_kbn'] == '1') {
                    if ($uriData['data_kbn'] != self::DATA_KBN_TAN) {
                        $uriData['fix_keijo_ymd'] = $row['fix_keijo_ymd'];
                    }
                    Logic_DenpyoHistoryMake::UriageHistory($db, $uriData,-1);
                    // 財務連動関連の項目をリセット
                    $upd = array('zaimu_rendo_kbn'=>0,'zaimu_rendo_date'=>null,'zaimu_tanto_cd'=>null,'zaimu_rendo_denno'=>null);
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $moto['uri_den_no']));
                    $db->easyExecute($sql, $param);
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo_msi", $upd, array('uri_den_no' => $moto['uri_den_no']));
                    $db->easyExecute($sql, $param);
                } else {
                    Logic_DenpyoHistoryMake::DeleteUriageMaxHistory($db, $moto['uri_den_no']);
                }
            }
        } else if ($row['bun_gas_kbn_num'] == static::BUNGAS_KBN_NOM) {
            $uriData = DataMapper_UriageDenpyo2::findOne($db, array('seikyu_no' => $seikyuDenpyo['uri_den_no']));
            // 財務連動連携済なら赤伝票作成(アフターを除き計上日を訂正計上日で作成する)
            // そうでないなら履歴の最大値のものを削除
            if (isset($uriData['zaimu_rendo_kbn']) && $uriData['zaimu_rendo_kbn'] == '1') {
                if ($uriData['data_kbn'] != self::DATA_KBN_TAN) {
                    $uriData['fix_keijo_ymd'] = $row['fix_keijo_ymd'];
                }
                Logic_DenpyoHistoryMake::UriageHistory($db, $uriData,-1);
                // 財務連動関連の項目をリセット
                $upd = array('zaimu_rendo_kbn'=>0,'zaimu_rendo_date'=>null,'zaimu_tanto_cd'=>null,'zaimu_rendo_denno'=>null);
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $seikyuDenpyo['uri_den_no']));
                $db->easyExecute($sql, $param);
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo_msi", $upd, array('uri_den_no' => $seikyuDenpyo['uri_den_no']));
                $db->easyExecute($sql, $param);
            } else {
                Logic_DenpyoHistoryMake::DeleteUriageMaxHistory($db, $seikyuDenpyo['uri_den_no']);
            }
            // 葬儀か法事の場合は喪家供花も同様の処理をする
            if ($row['denpyo_data_kbn'] == self::DATA_KBN_SOUGI || $row['denpyo_data_kbn'] == self::DATA_KBN_HOUJI) {
                $mokeData = DataMapper_UriageDenpyo2::find($db, array('seko_no' => $row['seko_no'], 'juchusaki_kbn' => 1));
                foreach ($mokeData as $mokeOne) {
                    if (isset($mokeOne['zaimu_rendo_kbn']) && $mokeOne['zaimu_rendo_kbn'] == '1') {
                        $mokeOne['fix_keijo_ymd'] = $row['fix_keijo_ymd'];
                        Logic_DenpyoHistoryMake::UriageHistory($db, $mokeOne,-1);
                        // 財務連動関連の項目をリセット
                        $upd = array('zaimu_rendo_kbn'=>0,'zaimu_rendo_date'=>null,'zaimu_tanto_cd'=>null,'zaimu_rendo_denno'=>null);
                        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, array('uri_den_no' => $mokeOne['uri_den_no']));
                        $db->easyExecute($sql, $param);
                        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo_msi", $upd, array('uri_den_no' => $mokeOne['uri_den_no']));
                        $db->easyExecute($sql, $param);
                    } else {
                        Logic_DenpyoHistoryMake::DeleteUriageMaxHistory($db, $mokeOne['uri_den_no']);
                    }
                }
            }
        }
        // 会費初期化処理
        $this->deleteKaihiPrc($db, $row['seikyu_den_no']); 
        
    }

    /**
     * 貸倒処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * 
     * @return  array 
     */
    protected function baddebtAction() {
        
        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }
        try {
            $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
            $dataSeikyu = Msi_Sys_Utils::json_decode($params['dataSeikyuJson']);
            // Msi_Sys_Utils::debug( ' params==>' . Msi_Sys_Utils::dump($params) );
            $db = Msi_Sys_DbManager::getMyDb();
            // 貸倒損失用入金処理を行う
            $role_nm = App_Utils2::getRoleNm(App_Utils::getTantoCd());
            // 権限が業務管理部（本社事務）以外はNG
            $role_flg = false;
            if ($role_nm == 'gyomukanri' || $role_nm == 'gyomukanri2' || $role_nm == 'edigyomukanri' || $role_nm == 'nyukinkanri') {
                $role_flg = true;
            }
            if (!$role_flg) {
                throw new Exception("権限エラーです。");
            }
            $nyukin_ymd = Msi_Sys_Utils::getDate();
            foreach ($dataSeikyu as $row) {
                $sekyuData = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $row['seikyu_den_no']));
                if (count($sekyuData) == 0) {
                    throw new Exception(sprintf("請求伝票No(%s)のデータがありません", $row['seikyu_den_no']));
                }
                // 受注伝票番号を取得する
                if ($sekyuData[0]['bun_gas_kbn_num'] == '0') {
                    $seikyu_den_no = $row['seikyu_den_no'];
                } else {
                    // 分割先OR合算元から請求伝票番号を取得
                    $seikyu_den_no = $row['bun_gas_seikyu_den_no'];
                }
                // 既に貸倒損失額がある場合はエラー
                if ($sekyuData[0]['bad_debt_loss_prc'] != 0) {
                    throw new Exception(sprintf("既に貸倒損失額が存在しています。(%s)", $row['seikyu_den_no']));
                }
                // 入金伝票用データ作成
                $nyukinData = array();
                $zeiData = array();
                $nyukinData['mode'] = 'baddebt';
                $nyukinData['seikyu_no'] = $row['seikyu_den_no'];
                $nyukinData['nyukin_ymd'] = $nyukin_ymd;
                $nyukinData['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy();
                $nyukinData['denpyo_no'] = null;
                $nyukinData['data_kbn'] = $row['denpyo_data_kbn'];
                $nyukinData['seko_no'] = $row['seko_no'];
                $nyukinData['seko_no_sub'] = null;
                $nyukinData['uri_den_no'] = $row['uri_den_no'];
                $nyukinData['nyu_kbn'] = static::NYU_KBN_BADDEBT; 
                $nyukinData['bumon_cd'] = $row['bumon_cd'];
                $nyukinData['tanto_cd'] = App_Utils::getTantoCd();
                $nyukinData['_dtl_'] = array();
                // 入金の有無で取得する税率がかわり、明細数も変動する
                $nyukin_prc = $sekyuData[0]['nyukin_prc'] + $sekyuData[0]['uchikin_prc'];
                if ($nyukin_prc == 0) {
                    $shohizei = App_KeigenUtils::getSeikyuShohizei4($db, $sekyuData[0]['seikyu_den_no']);
                    foreach ($shohizei as $value) {
                        $prc = $value[1];
                        $zeiInfo = $value[2];
                        $zeiData[$zeiInfo['zei_cd']]['zei_cd'] = $zeiInfo['zei_cd'];
                        $zeiData[$zeiInfo['zei_cd']]['nyukin_prc'] += $prc;
                    }
                } else {
                    // データ区分が葬儀OR法事の場合は葬儀日、それ以外は計上日基準で税率を取得する
                    if ($row['denpyo_data_kbn'] === DATA_KBN_SOUGI || $row['denpyo_data_kbn'] === DATA_KBN_HOUJI) {
                        $kihonInfo = DataMapper_SekoKihon::find($db, array('seko_no' => $row['seko_no']));
                        $kijunYmd = $kihonInfo[0]['sougi_ymd'];
                    } else {
                        $kijunYmd = $row['keijo_ymd'];
                    }
                    $ZeiTbl = App_ClsTaxLib::GetTaxInfo($db, $kijunYmd);
                    $zeiData[] = array('zei_cd' => $ZeiTbl['zei_cd'], 'nyukin_prc' => $row['seikyu_zan']);
                }
                // キーをリセット
                $zeiData = array_merge($zeiData);
                for ($i=0; $i < count($zeiData); $i++) {
                    $msiData = array();
                    $msiData['line_no'] = $i+1;    
                    $msiData['nyukn_kbn'] = static::NYUKN_KBN_BADDEBT;
                    $msiData['bumon_cd'] = $row['bumon_cd'];
                    $msiData['kaisya_cd'] = App_Utils::getCtxtKaisyaEasy();
                    $codeMst = DataMapper_CodeNmMst::find($db, array('code_kbn' => static::CODE_KBN_BADDEBT));
                    if (count($codeMst) > 0) {
                        // 一つしか存在しない前提 
                        $msiData['kamoku_cd'] = $codeMst[0]['kbn_value_lnm'];
                    } else {
                        $msiData['kamoku_cd'] = null;
                    }
                    $msiData['hojo_cd'] = null; // 補助科目はない想定
                    $msiData['shoihi_zei_cd'] = $zeiData[$i]['zei_cd'];
                    $msiData['nyukin_prc'] = $zeiData[$i]['nyukin_prc'];
                    $codeMst = DataMapper_CodeNmMst::find($db, array('code_kbn' => static::CODE_KBN_ZEI_BADDEBT, 'kbn_value_cd' => $msiData['shoihi_zei_cd']));
                    if (count($codeMst) > 0) {
                        $msiData['zei_cd'] = $codeMst[0]['kbn_value_cd_num'];
                    } else {
                        $msiData['shoihi_zei_cd'] = null;
                        $msiData['zei_cd'] = null;
                    }
                    $nyukinData['_dtl_'][] = $msiData;
                }
                // 入金伝票作成処理
                $dataNew = Logic_NyukinDenpyo::upsert($nyukinData);
            }
            $db->commit();
            $rtnData = array('status' => 'OK', 'msg' => '貸倒処理を実行しました。');
            Msi_Sys_Utils::outJson($rtnData);
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            Msi_Sys_Utils::outJson($errData);
        }
    }
    
    /**
     * 承認取消時チェック処理
     *
     * <AUTHOR> Tosaka
     * @since  2021/xx/xx
     * @params stringr   $db
     * @params stringr   $seikyu_zan
     * @return string    印紙税額
     */
    protected function checkCancelData($db, $data){
        
        // 請求伝票データと請求先情報データを取得する
        $seikyuDenpyo = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $data['seikyu_den_no']));
        if (count($seikyuDenpyo) === 0) {
            throw new Exception(sprintf("伝票No(%s)のデータがありません", $data['seikyu_den_no']));
        }
        // 既に取消済ならエラー
        if ($seikyuDenpyo[0]['seikyu_approval_status'] == 0) {
            throw new Msi_Sys_Exception_InputException("既に請求承認取消済です。");
        }
        // 入金チェック
        // 入金伝票の入金額のSUMが0かどうかチェック
        $nyukin = DataMapper_NyukinDenpyo::find($db, array('seikyu_no' => $seikyuDenpyo[0]['seikyu_den_no']));
        if (count($nyukin) > 0) {
            $total = array_sum(array_column($nyukin, 'nyukin_prc'));
            if ($total != 0) {
                throw new Msi_Sys_Exception_InputException("入金データがあるため承認取消ができません。");
            }
        }
        // 領収証履歴が存在する場合はメッセージを返す
        if (App_Utils2::hasRyosyu($data['seikyu_den_no'])) {
            throw new Msi_Sys_Exception_InputException("領収証発行済みのため承認取消ができません。");
        }
        // アフターかつ財務連動済みの場合はメッセージを返す
//        $uriData = DataMapper_UriageDenpyo2::findOne($db, array('seikyu_no' => $data['uri_den_no']));
//        if ($data['denpyo_data_kbn'] == self::DATA_KBN_TAN && $uriData['zaimu_rendo_kbn'] == '1') {
//            throw new Msi_Sys_Exception_InputException("財務連動済みのため承認取消ができません。");
//        }
        // 月次チェック
        // データ確定テーブルを取得
        $select = $db->easySelect(<<< END_OF_SQL
            SELECT MAX(TO_CHAR(fix_date,'YYYY/MM/DD')) AS fix_date
            FROM data_fix_table
            WHERE delete_flg = 0		  			
            AND fix_kbn = 1		
END_OF_SQL
        );
        // テーブルがなければ未確定
        if (count($select) > 0) {
            // 伝票日付＞確定月
            if ($data['keijo_ymd'] <= $select[0]['fix_date']) {
                 //売上計上日が月次確定している場合に、訂正計上日も月次確定している場合はエラーとする。
                if ($data['fix_keijo_ymd'] <= $select[0]['fix_date']) {
                    throw new Msi_Sys_Exception_InputException("訂正計上日の日付はすでに月次確定されているため取消ができません。");
                }
            } else {
                // 売上計上日が月次確定していない場合に、訂正計上日が売上計上日と一致していない場合はエラーとする。
                if ($data['fix_keijo_ymd'] != $data['keijo_ymd']) {
                    throw new Msi_Sys_Exception_InputException("訂正計上日と計上日が異なるため取消ができません。");
                }
            }
        } else {
            // 売上計上日が月次確定していない場合に、訂正計上日が売上計上日と一致していない場合はエラーとする。
            if ($data['fix_keijo_ymd'] != $seikyuDenpyo[0]['keijo_ymd']) {
                throw new Msi_Sys_Exception_InputException("訂正計上日と計上日が異なるため取消ができません。");
            }
        }
        return;
    }

    /**
     *
     * 施行互助会加入者を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 施行互助会加入者情報
     */
    protected function getGojokaiMember($seko_no) {

        $dataGojokaiMember = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                gm.seko_no          -- 施行番号
                ,gm.kain_no         -- 会員番号
                ,gm.gojokai_cose_cd -- 互助会コースコード
                ,gm.kanyu_nm        -- 加入者名
                ,gm.yoto_kbn        -- 用途
                ,gm.harai_gaku      -- 払込金額
                ,gm.wari_gaku       -- 前納割引額
                ,gm.harai_no        -- 払込回数
                ,TO_CHAR(gm.kanyu_dt ,'YYYY/MM/DD') AS kanyu_dt-- 加入年月日
                ,TO_CHAR(gm.zei_kijyn_ymd ,'YYYY/MM/DD') AS zei_kijyn_ymd-- 消費税基準日
                ,gm.zei_cd          -- 消費税コード
                ,gm.keiyaku_gaku    -- 契約金額
                ,gm.cose_chg_gaku   -- コース変更差額金
                ,gm.early_use_cost  -- 早期利用費
                ,gm.early_use_cost_zei  -- 早期利用費消費税
                ,gm.early_use_cost_zei_cd  -- 早期利用費消費税コード
                ,gm.meigi_chg_cost  -- 名義変更手数料
                ,gm.meigi_chg_cost_zei  -- 名義変更手数料消費税
                ,gm.meigi_chg_cost_zei_cd  -- 名義変更手数料消費税コード
                ,gm.course_snm_cd   -- 互助会コース名（イニシャル）
                ,gm.v_free1 AS cur_cd   -- 現況コード
                ,gm.v_free2 AS other_cose_nm   -- 他社時コース名
                ,gm.v_free3 -- OMプランコード
                ,gm.v_free10
                ,gm.free_kbn1 AS kaiin_info_kbn   -- 会員情報区分
                ,gm.kake_zei_rtu
                ,gm.kake_zei_sagaku
                ,gm.cif_no
                ,gm.waribiki_gaku
                ,gm.kanyu_tax
                ,gm.n_free1
                ,gm.n_free2
                ,gm.n_free3
                ,gm.n_free4
            FROM
                seko_gojokai_member gm
                LEFT JOIN gojokai_couse_mst gcm
                    ON
                    (
                        gm.gojokai_cose_cd = gcm.gojokai_cose_cd
                    AND CURRENT_DATE BETWEEN gcm.tekiyo_st_date AND gcm.tekiyo_ed_date
                    AND gcm.delete_flg = 0
                    )
            WHERE
                    gm.seko_no = :seko_no
                AND gm.delete_flg = 0
            ORDER BY
                gm.yoto_kbn 
                ,gm.kanyu_dt
                ";
        $select = $db->easySelect($sql, array('seko_no' => $seko_no));
        // 施行互助会加入者に存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $oneRowData = array();
                $oneRowData['seko_no'] = $select[$i]['seko_no'];
                $oneRowData['kain_no'] = trim($select[$i]['kain_no']);
                $oneRowData['gojokai_cose_cd'] = $select[$i]['gojokai_cose_cd'];
                $oneRowData['kanyu_nm'] = $select[$i]['kanyu_nm'];
                $oneRowData['yoto_kbn'] = $select[$i]['yoto_kbn'];
                $oneRowData['harai_gaku'] = $select[$i]['harai_gaku'];
                $oneRowData['wari_gaku'] = $select[$i]['wari_gaku'];
                $oneRowData['harai_no'] = $select[$i]['harai_no'];
                $oneRowData['kanyu_dt'] = $select[$i]['kanyu_dt'];
                $oneRowData['zei_kijyn_ymd'] = $select[$i]['zei_kijyn_ymd'];
                $oneRowData['keiyaku_gaku'] = $select[$i]['keiyaku_gaku'];
                $oneRowData['cose_chg_gaku'] = $select[$i]['cose_chg_gaku'];
                $oneRowData['early_use_cost'] = $select[$i]['early_use_cost'];
                $oneRowData['early_use_cost_zei'] = $select[$i]['early_use_cost_zei'];
                $oneRowData['early_use_cost_disp'] = $select[$i]['early_use_cost'] + $select[$i]['early_use_cost_zei'];
                $oneRowData['early_use_cost_zei_cd'] = $select[$i]['early_use_cost_zei_cd'];
                $oneRowData['meigi_chg_cost'] = $select[$i]['meigi_chg_cost'];
                $oneRowData['meigi_chg_cost_zei'] = $select[$i]['meigi_chg_cost_zei'];
                $oneRowData['meigi_chg_cost_disp'] = $select[$i]['meigi_chg_cost'] + $select[$i]['meigi_chg_cost_zei'];
                $oneRowData['meigi_chg_cost_zei_cd'] = $select[$i]['meigi_chg_cost_zei_cd'];
                $oneRowData['course_snm_cd'] = $select[$i]['course_snm_cd'];
                $oneRowData['kaiin_info_kbn'] = $select[$i]['kaiin_info_kbn'];
                $oneRowData['cur_cd'] = $select[$i]['cur_cd'];
                $oneRowData['v_free10'] = $select[$i]['v_free10'];
                $oneRowData['zei_cd'] = $select[$i]['zei_cd'];
                $oneRowData['kake_zei_rtu'] = $select[$i]['kake_zei_rtu'];
                $oneRowData['kake_zei_sagaku'] = $select[$i]['kake_zei_sagaku'];
                $oneRowData['other_cose_nm'] = $select[$i]['other_cose_nm'];
                $oneRowData['waribiki_gaku'] = $select[$i]['waribiki_gaku'];
                $oneRowData['kanyu_tax'] = $select[$i]['kanyu_tax'];
                $oneRowData['n_free1'] = $select[$i]['n_free1'];
                $oneRowData['n_free2'] = $select[$i]['n_free2'];
                $oneRowData['n_free3'] = $select[$i]['n_free3'];
                $oneRowData['n_free4'] = $select[$i]['n_free4'];
                $oneRowData['v_free3'] = $select[$i]['v_free3'];
                $oneRowData['zan_gaku'] = $select[$i]['keiyaku_gaku']-$select[$i]['harai_gaku']-$select[$i]['wari_gaku']-$select[$i]['waribiki_gaku']-$select[$i]['n_free3']-$select[$i]['n_free4'];
                $dataGojokaiMember[$i] = $oneRowData;
            }
        }
        return $dataGojokaiMember;
    }

    /**
     *
     * 施行互助会加入者を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 施行互助会加入者情報
     */
    protected function getKojinInfo($seko_no) {

        $dataKojinInfo = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $gengo = DataMapper_EraMst::getCodeNmEra();
        $sql = "
            SELECT skih.*
                ,TO_CHAR(skih.hk_death_ymd, 'YYYY/MM/DD') AS hk_death_ymd
            FROM seko_kojin_info_houji skih
            WHERE skih.seko_no = :seko_no
                AND skih.delete_flg = 0
            ORDER BY skih.seq_no
                ";
        $select = $db->easySelect($sql, array('seko_no' => $seko_no));
        // 施行故人情報法事が存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $oneRowData = array();
                if (isset($select[$i]['hk_birth_year'])) {
                    $keyIndex = array_search($select[$i]['hk_birth_year'], array_column($gengo, 'kbn_value_cd_num'));
                    $result = $gengo[$keyIndex];
                    $oneRowData['hk_wa_year'] = $result['kbn_value_snm'];
                }
                if (isset($select[$i]['hk_birth_year']) && isset($select[$i]['hk_birth_month']) && isset($select[$i]['hk_birth_day'])) {
                    $select[$i]['hk_seinengappi_ymd_y'] = $select[$i]['hk_birth_year'] . '-' . $select[$i]['hk_birth_month'] . '-' . $select[$i]['hk_birth_day'];
                }
                $oneRowData['seko_no'] = $select[$i]['seko_no'];
                $oneRowData['seq_no'] = $select[$i]['seq_no'];
                $oneRowData['sekohoyo_kbn'] = $select[$i]['sekohoyo_kbn'];
                $oneRowData['hk_last_nm'] = $select[$i]['hk_last_nm'];
                $oneRowData['hk_first_nm'] = $select[$i]['hk_first_nm'];
                $oneRowData['hk_last_knm'] = $select[$i]['hk_last_knm'];
                $oneRowData['hk_first_knm'] = $select[$i]['hk_first_knm'];
                $oneRowData['hk_kaimyo'] = $select[$i]['hk_kaimyo'];
                $oneRowData['hk_death_ymd'] = $select[$i]['hk_death_ymd'];
                $oneRowData['hk_cif_no'] = $select[$i]['hk_cif_no'];
                $oneRowData['hk_sex_kbn'] = $select[$i]['hk_sex_kbn'];
                $oneRowData['hk_gengo'] = $select[$i]['hk_gengo'];
                $oneRowData['hk_birth_year'] = $select[$i]['hk_birth_year'];
                $oneRowData['hk_birth_month'] = $select[$i]['hk_birth_month'];
                $oneRowData['hk_birth_day'] = $select[$i]['hk_birth_day'];
                $oneRowData['hk_seinengappi_ymd'] = $select[$i]['hk_seinengappi_ymd'];
                $oneRowData['hk_seinengappi_ymd_y'] = $select[$i]['hk_seinengappi_ymd_y'];
                $oneRowData['hk_nenrei_man'] = $select[$i]['hk_nenrei_man'];
                $oneRowData['hk_file_oid'] = $select[$i]['hk_file_oid'];
                $oneRowData['hk_file_nm'] = $select[$i]['hk_file_nm'];
                $oneRowData['hkg_yubin_no'] = $select[$i]['hkg_yubin_no'];
                $oneRowData['hkg_addr1'] = $select[$i]['hkg_addr1'];
                $oneRowData['hkg_addr2'] = $select[$i]['hkg_addr2'];
                $oneRowData['hkg_tel'] = $select[$i]['hkg_tel'];
                $dataKojinInfo[$i] = $oneRowData;
            }
        }
        return $dataKojinInfo;
    }
    
    /**
     *
     * mod_cnt取得処理
     *
     * <AUTHOR> Tosaka
     * @param  
     * @since 2022/11/xx
     * @return array $array
     */
    protected function makeMocData($db,$dataSekoKihon,$dataGojokaiMemberCol) {
        
        $array = array();
        foreach ($dataGojokaiMemberCol as $one) {
            if ($one['kaiin_info_kbn'] <> '1') {
                continue;
            }
            $row = array();
            $row['ContractNo'] = $one['v_free17'];
            $row['UsageType'] = $one['yoto_kbn'];
            $array[] = $row;
        }
        $data = array('EnforcementNo' => $dataSekoKihon['seko_no'],'ApplicationDate' => $dataSekoKihon['kijun_ymd'],'UserName' => $dataSekoKihon['k_nm'], 'ContractInfos' => $array);
        return $data;
    }
    
    /**
     * 会費集計処理
     *
     * <AUTHOR> Tosaka
     * @since      2025/04/xx
     * @param      Msi_Sys_Db $db	データベース
     * @param      string  $seikyu_den_no 請求伝票番号
     * @return     
     */
    protected  function calcKaihiPrc($db, $seikyu_den_no) {
        // 請求伝票データ取得
        $seikyuDenpyo = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $seikyu_den_no));
        if (Msi_Sys_Utils::myCount($seikyuDenpyo) === 0) {
            return;
        }
        // 請求伝票の施行番号とデータ区分に該当し、入金区分:内金(88)の論理削除されていない入金伝票を取得
        $nyukinData = DataMapper_NyukinDenpyo::find($db, array('seko_no' => $seikyuDenpyo['seko_no'], 'data_kbn' => $seikyuDenpyo['data_kbn'], 'nyu_kbn' => 88,'__etc_orderby' => array('nyukin_ymd ASC','denpyo_no ASC')));
        // 内金入金伝票がなければ処理をしない
        if (count($nyukinData) == 0) {
            return;
        }
        // ①処理対象の判定
        // データ区分が葬儀OR法事以外は対象外
        if ($seikyuDenpyo['data_kbn'] != '1' && $seikyuDenpyo['data_kbn'] != '2') {
            return;
        }
        // 請求伝票の葬儀契約金額が0円かつ、利用券(n_free9)とポイント(n_free10)が0円以外のデータは対象外
        if ($seikyuDenpyo['sougi_keiyaku_prc'] == 0 && ($seikyuDenpyo['n_free9'] != 0 || $seikyuDenpyo['n_free10'] != 0)) {
            return;
        }
        // 葬儀特典金額が0円以外かつ(葬儀契約金額 + 葬儀払込金額 + 葬儀前納割引額)が0円は対象外
        if ($seikyuDenpyo['sougi_tokuten_prc'] == 0 && (($seikyuDenpyo['sougi_keiyaku_prc'] + $seikyuDenpyo['sougi_harai_prc'] + $seikyuDenpyo['sougi_wari_prc']) == 0)) {
            return;
        }
        // 葬儀契約金額消費税が0円かつ(葬儀契約金額 + 葬儀払込金額 + 葬儀前納割引額)が0円は対象外
        if ($seikyuDenpyo['sougi_keiyaku_zei'] == 0 && (($seikyuDenpyo['sougi_keiyaku_prc'] + $seikyuDenpyo['sougi_harai_prc'] + $seikyuDenpyo['sougi_wari_prc']) == 0)) {
            return;
        }
        // ②消込残額の取得
        $keshikomi_array = array('kaihi_zei' => 0,'early_zei' => 0,'early_prc' => 0,'seisan_prc' => 0,'seko_prc' => 0);
        // 1.会費消費税入金額の消込残額：葬儀契約金額消費税 - 葬儀前納割引額消費税
        $keshikomi_array['kaihi_zei'] = $seikyuDenpyo['sougi_keiyaku_zei'] + $seikyuDenpyo['sougi_wari_zei'];    
        // 2.早期利用費消費税入金額の消込残額：葬儀早期利用費消費税
        $keshikomi_array['early_zei'] = $seikyuDenpyo['sougi_early_use_cost_zei'];    
        // 3.早期利用費入金額の消込残額：葬儀早期利用費
        $keshikomi_array['early_prc'] = $seikyuDenpyo['sougi_early_use_cost'];     
        // 4.互助会精算入金額の消込残額：葬儀契約金額 + 葬儀払込金額 + 葬儀前納割引額
        $keshikomi_array['seisan_prc'] = $seikyuDenpyo['sougi_keiyaku_prc'] + $seikyuDenpyo['sougi_harai_prc'] + $seikyuDenpyo['sougi_wari_prc'];   
        // 5.施行代金入金額(計算)の消込残額：（入金金額＋請求残高＋内金入金額）－（上記1～4の金額の合計）
        $keshikomi_array['seko_prc'] = 
                ($seikyuDenpyo['nyukin_prc'] + $seikyuDenpyo['seikyu_zan'] + $seikyuDenpyo['uchikin_prc']) 
                - ($keshikomi_array['kaihi_zei'] + $keshikomi_array['early_zei'] + $keshikomi_array['early_prc'] + $keshikomi_array['seisan_prc']);    
        // ③消込処理
        $nyukin_array = array('seko_nyukin_prc' => 0,'early_use_cost_nyukin_prc' => 0,'gojokai_nyukin_prc' => 0,'kaihi_zei_nyukin_prc' => 0,'early_use_cost_zei_nyukin_prc' => 0);
        foreach ($nyukinData as $row) {
            $n_free_array = array('n_free2' => 0,'n_free3' => 0,'n_free4' => 0,'n_free5' => 0);
            $nyukinPrc = $row['nyukin_prc'];
            // 入金額がマイナスのときは逆順で処理する
            if ($nyukinPrc < 0) {
                $keshikomi_array = array_reverse($keshikomi_array);
            }
            foreach ($keshikomi_array as $key => &$prc) {
                if ($nyukinPrc == 0) {
                    break;
                } else if ($prc == 0) {
                    continue;
                }
                if ($prc >= $nyukinPrc) {
                    $jutoPrc = $nyukinPrc;
                    $prc = $prc - $nyukinPrc;
                    $nyukinPrc = 0;
                } else if ($prc < $nyukinPrc) {
                    $jutoPrc = $prc;
                    $nyukinPrc = $nyukinPrc - $prc;
                    $prc = 0;
                }
                switch ($key) {
                    case 'kaihi_zei' :
                        $n_free_array['n_free2'] = $jutoPrc;
                        $nyukin_array['kaihi_zei_nyukin_prc'] += $jutoPrc;
                        break;
                    case 'early_zei' :
                        $n_free_array['n_free3'] = $jutoPrc;
                        $nyukin_array['early_use_cost_zei_nyukin_prc'] += $jutoPrc;
                        break;
                    case 'early_prc' :
                        $n_free_array['n_free4'] = $jutoPrc;
                        $nyukin_array['early_use_cost_nyukin_prc'] += $jutoPrc;
                        break;
                    case 'seisan_prc' :
                        $n_free_array['n_free5'] = $jutoPrc;
                        $nyukin_array['gojokai_nyukin_prc'] += $jutoPrc;
                        break;
                    case 'seko_prc' :
                        $nyukin_array['seko_nyukin_prc'] += $jutoPrc;
                        break;
                }
            }
            // 入金伝票更新処理
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("nyukin_denpyo", $n_free_array, array('denpyo_no' => $row['denpyo_no']));
            $db->easyExecute($sql, $param);
        }
        // 売上伝票更新処理
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $nyukin_array, array('uri_den_no' => $seikyuDenpyo['uri_den_no']));
        $db->easyExecute($sql, $param);
    }
    
    /**
     * 会費初期化処理
     *
     * <AUTHOR> Tosaka
     * @since      2025/04/xx
     * @param      Msi_Sys_Db $db	データベース
     * @param      string  $seikyu_den_no 請求伝票番号
     * @return     
     */
    protected  function deleteKaihiPrc($db, $seikyu_den_no) {
        // 請求伝票データ取得
        $seikyuDenpyo = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $seikyu_den_no));
        if (Msi_Sys_Utils::myCount($seikyuDenpyo) === 0) {
            return;
        }
        // 請求伝票の施行番号とデータ区分に該当し、入金区分:内金(88)の論理削除されていない入金伝票を取得
        $nyukinData = DataMapper_NyukinDenpyo::find($db, array('seko_no' => $seikyuDenpyo['seko_no'], 'data_kbn' => $seikyuDenpyo['data_kbn'], 'nyu_kbn' => 88,'__etc_orderby' => array('nyukin_ymd ASC')));
        // 内金入金伝票がなければ処理をしない
        if (count($nyukinData) == 0) {
            return;
        }
        // ①処理対象の判定
        // データ区分が葬儀OR法事以外は対象外
        if ($seikyuDenpyo['data_kbn'] != '1' && $seikyuDenpyo['data_kbn'] != '2') {
            return;
        }
        // 請求伝票の葬儀契約金額が0円かつ、利用券(n_free9)とポイント(n_free10)が0円以外のデータは対象外
        if ($seikyuDenpyo['sougi_keiyaku_prc'] == 0 && ($seikyuDenpyo['n_free9'] != 0 || $seikyuDenpyo['n_free10'] != 0)) {
            return;
        }
        // 葬儀特典金額が0円以外かつ(葬儀契約金額 + 葬儀払込金額 + 葬儀前納割引額)が0円は対象外
        if ($seikyuDenpyo['sougi_tokuten_prc'] == 0 && (($seikyuDenpyo['sougi_keiyaku_prc'] + $seikyuDenpyo['sougi_harai_prc'] + $seikyuDenpyo['sougi_wari_prc']) == 0)) {
            return;
        }
        // 葬儀契約金額消費税が0円かつ(葬儀契約金額 + 葬儀払込金額 + 葬儀前納割引額)が0円は対象外
        if ($seikyuDenpyo['sougi_keiyaku_zei'] == 0 && (($seikyuDenpyo['sougi_keiyaku_prc'] + $seikyuDenpyo['sougi_harai_prc'] + $seikyuDenpyo['sougi_wari_prc']) == 0)) {
            return;
        }
        $nyukin_array = array('seko_nyukin_prc' => 0,'early_use_cost_nyukin_prc' => 0,'gojokai_nyukin_prc' => 0,'kaihi_zei_nyukin_prc' => 0,'early_use_cost_zei_nyukin_prc' => 0);
        foreach ($nyukinData as $row) {
            $n_free_array = array('n_free2' => 0,'n_free3' => 0,'n_free4' => 0,'n_free5' => 0);
            // 入金伝票更新処理
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("nyukin_denpyo", $n_free_array, array('denpyo_no' => $row['denpyo_no']));
            $db->easyExecute($sql, $param);
        }
        // 売上伝票更新処理
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $nyukin_array, array('uri_den_no' => $seikyuDenpyo['uri_den_no']));
        $db->easyExecute($sql, $param);
    }

}
