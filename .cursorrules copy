# Cursor IDE設定ファイル
# バージョン: 1

# ファイル命名規則
[カスタマイズキー規則]
説明: 会社ごとのカスタマイズキーに基づくファイル命名規則
パターン: .*\.(php|js|css|tpl|pdf)$
カスタマイズキーパターン: .*\.(nowl|ceremore|saien|sanmen|ceremo|ceremony|ipal|gonkiya|plaza|lifesystem|jasennan|saikane|mitsuwa|seinan|gashoen|gessin|sousinki|linkmore|kouseisya|miyakojima|famld|fujiya|kyouei|nkoueki|syouwa|tzouka|hakuzen|snlife|mitsugi|aiaru|miyakotenrei|sagamikumiai|hakuunsya|oonosikiten|bellmony|narikoh|itabashi|bellco|sunray|lifeland)\.(php|js|css|tpl|pdf)$

[PHPファイル規則]
説明: PHPファイルの命名規則と構造
パターン: .*\.php$
コントローラーパターン: .*Controller\.php$
モデルパターン: .*Model\.php$
ビューパターン: .*\.tpl$

[JavaScriptファイル規則]
説明: JavaScriptファイルの命名規則
パターン: .*\.js$
モジュールパターン: .*\.(module|controller)\.js$

[CSSファイル規則]
説明: CSSファイルの命名規則
パターン: .*\.css$

[テンプレートファイル規則]
説明: Smartyテンプレートファイルの命名規則
パターン: .*\.tpl$

[PDFテンプレート規則]
説明: PDFテンプレートファイルの命名規則
パターン: .*\.pdf$

# 無視パターン
[無視パターン]
vendor/.*
node_modules/.*
\.git/.*
\.svn/.*
temp/.*
data/.*

# 検索パス
[検索パス]
application/
public_dev/
library/
configs/

# リソース読み込み設定
[リソース読み込み]
優先順位:
  - タイプ: カスタマイズ
    説明: カスタマイズキーによるファイル置き換え
    順序: 前方優先
  - タイプ: 標準
    説明: 標準ファイル
    順序: 最後

ファイルタイプ:
  コントローラ:
    パス: application/modules/{module}/controllers/
    パターン: {Controller}Controller.{cstmKey}.php
  ビュー:
    パス: application/views/smarty/{module}/{controller}/
    パターン: {action}-{cstmKey}.tpl
  JavaScript:
    パス: public_dev/js/app/{module}/
    パターン: {module}.{controller}.{cstmKey}.js
  CSS:
    パス: public_dev/css/app/{module}/
    パターン: {module}.{controller}.{cstmKey}.css
  PDF:
    テンプレート:
      パス: application/modules/{module}/pdf_tmpl/
      パターン: {template}-{cstmKey}.pdf
    コントローラ:
      パス: application/modules/{module}/controllers/
      パターン: {Controller}Controller.{cstmKey}.php

# 画面マッピング
[画面マッピング]
メニューファイル:
  メイン: application/configs/menuTable01.{cstmKey}.php
  サイド: application/configs/sideMenu{type}.{cstmKey}.php

画面とファイルの対応:
  新規施行受付:
    モジュール: juchu
    コントローラー: customerinfo
    アクション: input
    パス: new/1
    コントローラーファイル: application/modules/juchu/controllers/CustomerinfoController.php
    ビューファイル: application/views/smarty/juchu/customerinfo/input.tpl
    JavaScript: public_dev/js/app/juchu/juchu.customerinfo.js
    CSS: public_dev/css/app/juchu/juchu.customerinfo.css
  施行打合せ:
    モジュール: juchu
    コントローラー: mitsu
    アクション: input
    パス: %m_sn%
    コントローラーファイル: application/modules/juchu/controllers/MitsuController.php
    ビューファイル: application/views/smarty/juchu/mitsu/input.tpl
    JavaScript: public_dev/js/app/juchu/juchu.mitsu.js
    CSS: public_dev/css/app/juchu/juchu.mitsu.css
  別注品一覧:
    モジュール: juchu
    コントローラー: bechulist
    アクション: index
    パス: readonly/1
    コントローラーファイル: application/modules/juchu/controllers/BechulistController.php
    ビューファイル: application/views/smarty/juchu/bechulist/index.tpl
    JavaScript: public_dev/js/app/juchu/juchu.bechulist.js
    CSS: public_dev/css/app/juchu/juchu.bechulist.css

URLパスとファイルの対応:
  パターン: {module}/{controller}/{action}
  コントローラーファイル: application/modules/{module}/controllers/{Controller}Controller.php
  ビューファイル: application/views/smarty/{module}/{controller}/{action}.tpl
  JavaScript: public_dev/js/app/{module}/{module}.{controller}.js
  CSS: public_dev/css/app/{module}/{module}.{controller}.css

# コーディングスタイル
[コーディングスタイル]
インデント: 4スペース
改行コード: LF
文字コード: UTF-8
命名規則:
  クラス: PascalCase
  メソッド: camelCase
  変数: camelCase
  定数: UPPER_CASE
コメント:
  言語: 日本語
  必須: クラス、メソッド、プロパティのPHPDoc
  推奨: 複雑なロジックの行コメント

# エラー処理
[エラー処理]
例外: 適切な例外クラスを使用
メッセージ: 日本語で記述
ログ: 適切なログレベルを使用

# セキュリティ
[セキュリティ]
SQLインジェクション: プリペアドステートメントを使用
XSS対策: 出力時にエスケープ処理を実施
CSRF対策: トークンを使用

# パフォーマンス
[パフォーマンス]
データベース:
  クエリ: 必要最小限に
  キャッシュ: 適切に活用
  ループ内DBアクセス: 避ける

# カスタマイズキー
[カスタマイズキー]
nowl:
  名前: 株式会社ナウエル
  キー: nowl

ceremore:
  名前: セレモア
  キー: ceremore, hakuzen, nise02, nise01, gonkiya, sanmen

saien:
  名前: 株式会社彩苑
  キー: saien2, saien, sano

sanmen:
  名前: サンメンバーズ株式会社
  キー: sanmen2, sanmen_k, sanmen, sano

ceremo:
  名前: 株式会社セレモ
  キー: ceremo_k, day20, ceremo2, ceremo, hiragana, ver02, sanmen, sano

ceremony:
  名前: 株式会社セレモニー
  キー: ceremony_k, ceremony, ver02, ceremo, sanmen

ipal:
  名前: アイパル株式会社
  キー: ipal_k, ipal, sanmen, sano

gonkiya:
  名前: 株式会社ごんきや
  キー: gonkiya_k, gonkiya, sanmen, sano

plaza:
  名前: 株式会社西村企業
  キー: plaza2_k, plaza2, plaza_k, plaza, sanmen, sano

lifesystem:
  名前: 株式会社ライフシステム
  キー: lifesystem_k, lifesystem, sanmen, sano

jasennan:
  名前: 株式会社ジェイエイ仙南サービス
  キー: jasennan_k, jasennan, nise01_k, nise01, gonkiya, sanmen

saikane:
  名前: (株)埼玉金周
  キー: saikane_k, saikane, nise01_k, nise01, gonkiya, sanmen

mitsuwa:
  名前: (株)みつわ
  キー: mitsuwa_k, mitsuwa, nise01_k, nise01, gonkiya, sanmen

seinan:
  名前: (株)成南協心社
  キー: seinan, nise01, gonkiya, sanmen

gashoen:
  名前: 株式会社雅裳苑
  キー: gashoen2, gashoen_k, gashoen, sano

gessin:
  名前: (株)公益社
  キー: gessin_k, gessin, nise01_k, nise01, gonkiya, sanmen

sousinki:
  名前: (株)想心季
  キー: sousinki_k, sousinki, gessin_k, gessin, nise01_k, nise01, gonkiya, sanmen

linkmore:
  名前: 株式会社リンクモア
  キー: linkmore_k, linkmore, gessin_k, gessin, nise01_k, nise01, gonkiya, sanmen

kouseisya:
  名前: 株式会社幸成社
  キー: kouseisya, nise01, gonkiya, sanmen

miyakojima:
  名前: 株式会社都島葬祭
  キー: miyakojima, nise01, gonkiya, sanmen

famld:
  名前: ファミリード株式会社
  キー: famld, nise02, nise01, gonkiya, sanmen

fujiya:
  名前: 株式会社ふじや本店
  キー: fujiya, nise01_k, nise01, gonkiya, sanmen

kyouei:
  名前: 株式会社共栄会館
  キー: haifu, kyouei, nise02, nise01, gonkiya, sanmen

nkoueki:
  名前: 株式会社公益社
  キー: nkoueki, nise01_k, nise01, gonkiya, sanmen

syouwa:
  名前: 昭和興業株式会社
  キー: syouwa, nise03

tzouka:
  名前: 東條造花店
  キー: tzouka, nise02, nise01, gonkiya, sanmen

hakuzen:
  名前: 株式会社博全社
  キー: hakuzen, nise02, nise01, gonkiya, sanmen

snlife:
  名前: 株式会社サニーライフ
  キー: snlife, nise02, nise01, gonkiya, sanmen

mitsugi:
  名前: ミツギ
  キー: mitsugi, saikane_k, saikane, nise01_k, nise01, gonkiya, sanmen

aiaru:
  名前: あいあーる
  キー: aiaru, nise02, nise01_k, nise01, gonkiya, sanmen_k, sanmen

miyakotenrei:
  名前: 有限会社都典礼
  キー: miyakotenrei, saikane_k, saikane, nise01_k, nise01, gonkiya, sanmen

sagamikumiai:
  名前: (株)さがみくみあいサービス
  キー: sagamikumiai, nise04, nise01_k, nise01, gonkiya, sanmen

hakuunsya:
  名前: (株)白雲社
  キー: hakuunsya, nise04, nise01_k, nise01, gonkiya, sanmen

oonosikiten:
  名前: (株)おおの式典
  キー: oonosikiten, nise04, nise01_k, nise01, gonkiya, sanmen

bellmony:
  名前: 株式会社ベルモニー
  キー: bellmony, ceremore, hakuzen, nise02, nise01, gonkiya, sanmen

narikoh:
  名前: 株式会社ナリコー
  キー: narikoh, nise04, nise01_k, nise01, gonkiya, sanmen

itabashi:
  名前: 株式会社板橋
  キー: itabashi, bellmony, ceremore, hakuzen, nise02, nise01, gonkiya, sanmen

bellco:
  名前: 株式会社マリアージュインベルコ
  キー: bellco, bellmony, ceremore, hakuzen, nise02, nise01, gonkiya, sanmen

sunray:
  名前: 株式会社サンレー
  キー: sunray, bellmony, ceremore, hakuzen, nise02, nise01, gonkiya, sanmen

lifeland:
  名前: 株式会社ライフランド
  キー: lifeland, bellmony, ceremore, hakuzen, nise02, nise01, gonkiya, sanmen

[カスタマイズキー適用ルール]
説明: カスタマイズキーに対応するファイルが存在しない場合は、標準実装ファイルを利用すること。
例: application/modules/{module}/controllers/{Controller}Controller.{cstmKey}.php が存在しない場合、application/modules/{module}/controllers/{Controller}Controller.php を利用する。 