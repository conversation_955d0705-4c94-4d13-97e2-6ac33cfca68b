<?php

/**
 * Juchu_JuchuAbstract
 *
 * 受注 抽象クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuAbstract
 * <AUTHOR> Tosaka
 * @since      2020/03/25
 * @version    2025/02/xx Tosaka bellmony版よりコピー
 * @filesource 
 */

/**
 * 受注 抽象クラス
 * 
 * @category   App
 * @package    controllers\Juchu\JuchuAbstract
 * <AUTHOR> Sai
 * @since      2014/02/04
 */
abstract class Juchu_JuchuAbstract {

    /** 施行番号 */
    protected $_sekoNo = '';

    /** 申込区分 */
    protected $_moushiKbn = '';

    /** 会員区分 */
    protected $_kaiinKbn = '';

    /** 部門コード */
    protected $_bumonCd = '';

    /** 見積担当コード */
    protected $_mitsuTantoCd = '';

    /** 施行担当コード */
    protected $_sekoTantoCd = '';

    /** 施行プランコード */
    protected $_sekoPlanCd = '';

    /** 互助会会員番号 */
    protected $_kaiinNo = '';

    /** 互助会コースコード */
    protected $_gojokaiCoseCd = '';

    /** 互助会特典完納フラグ 0：未 1：済み */
    protected $_complet_flg = '';

    /** タイムスケジュールコード */
    protected $_tmScheduleCd = '';

    /** 故人亡日（生前依頼時はシステム日付） */
    protected $_nakunaribi = '';

    /** 受注確定日  */
    protected $_juchuKakuteiYMD = '';

    /** 請求先コード  */
    protected $_sekyuCd = '';

    /** 基本パターン区分コード  */
    protected $_mainPtCd = '';
    protected $_mainPtKbn = '';

    /** 互助会区分 */
    protected $_gojokaiKbn = '';

    /** 消費税計算基準（葬儀日設定されたら葬儀日、そうでなければはシステム日付） */
    protected $_zeiKijunYmd = '';

    /** 商品選択基準（葬儀日設定されたら葬儀日、そうでなければはシステム日付） 2016/04/03 ADD Kayo */
    protected $_selKijunYmd = '';

    /** 施行基本データ  */
    protected $_selectSekoKihon = array();

    /** 見積式場 */
    protected $_estShikijoCd = '';
    protected $_nebikiGojokaiKbn = '';
    protected $_nebikiYotoKbn = '';

    const MITSU = 'mitsu';

    /** 申込区分: 1=>葬儀 */
    const MOUSHI_KBN_SOUGI = '1';

    /** 申込区分: 2=>法事 */
    const MOUSHI_KBN_HOUJI = '2';

    /** 申込区分: 10=>その他施行 */
    const MOUSHI_KBN_OTHER = '10';

    /** 申込区分: 5=>生前依頼 */
    const MOUSHI_KBN_SEIZEN = '5';

    /** 申込区分: 19=>オーダーメイド */
    const MOUSHI_KBN_ORDERMADE = '19';

    /** 申込区分: 99=>サンプル */
    const MOUSHI_KBN_SAMPLE = '99';

    /** 申込区分: 7=>貸式場 */
    const MOUSHI_KBN_KASHI = '7';

    /** 申込区分: 8=>エンバー */
    const MOUSHI_KBN_EMB = '8';

    /** 申込区分: 9=>搬送 */
    const MOUSHI_KBN_HANSO = '9';

    /** ステータス区分: 3=>施行金額確定 */
    const STATUS_KBN_SEKOKAKUTEI = '3';
    /** ステータス区分: 4=>請求承認 */
    const STATUS_KBN_SHONIN = '4';

    /** カテゴリ区分: 2=>新聞掲載 */
    const CATEGORY_KBN2 = 2;

    /** 受注伝票追加区分: 4=>搬送物品 */
    const ADD_KBN_HB = '4';

    /** 受注伝票追加区分: 8=>完納特典 */
    const ADD_KBN_TK = '8';

    /** 受注伝票追加区分: 9=>自動作成 */
    const ADD_KBN_AUTO = '9';

    /** ミニプラン */
    const PLAN_MINI = '01170';

    /** OMプラン */
    const OM_PLAN_CD = '99999';

    /** 発注書区分　14：生花祭壇・アレンジ花　 */
    const HA_RP_KBN_SEIKA = '14';

    /** 葬儀場所　0：自宅　 */
    const SOUGI_BASHO_HOME = '0';

    /** 日程区分　4：通夜　 */
    const NITEI_KBN_TUYA = '4';

    /** 日程区分　6：火葬　 */
    const NITEI_KBN_KASO = '6';

    /** 日程区分　11：葬儀　 */
    const NITEI_KBN_SOUGI = '11';

    /** 日程区分　0：法事施行日　 */
    const NITEI_KBN_HOUJI_SEKO = '0';

    /** 日程区分　1：法要　 */
    const NITEI_KBN_HOYO = '1';

    /** 葬儀形式区分　4：火葬式　 */
    const KEISHIKI_KBN_KASO = '4';

    /** 部門区分　3：共通部門　 */
    const BUMON_KBN_KYOTU = '3';

    /** サイドメニューキー：請求　 */
    const SIDEMENUKEY_SEKYU = 'sekyu';

    /** 施行番号枝番 00：メイン */
    protected $_sekoNoSub = '00';

    /** 会員区分 100：互助会 */
    const KAIIN_KBN_GOJO = '100';

    /** 会員区分 400：アスカラメイト */
    const KAIIN_KBN_A = '400';

    /** 会員区分 500：ファーストステップ */
    const KAIIN_KBN_F = '500';

    /** 会員区分 600：一般 */
    const KAIIN_KBN_IPAN = '600';

    /** 会員区分 700：企業 */
    const KAIIN_KBN_CO = '700';

    /** 会員区分: 800=>コプセ */
    const KAIIN_KBN_NET = '800';

    /** 会員区分: 900=>ライフコミュニティ */
    const KAIIN_KBN_LIFE = '900';

    /** 値引用途区分 1：コース施行 */
    const NEBIKI_YOTO_COURSE = '1';

    /** 値引用途区分 2：プラン施行 */
    const NEBIKI_YOTO_PLAN = '2';

    /** アップグレード区分 0：通常 */
    const UPGRADE_KBN_NORMAL = '0';

    /** アップグレード区分 1：プラン商品 */
    const UPGRADE_KBN_PLAN = '1';

    /** アップグレード区分 2：アップグレード商品 */
    const UPGRADE_KBN_UPGRADE = '2';

    /** アップグレード区分クラス名 1：プラン商品 */
    const UPGRADE_CLASS_PLAN = 'upgrade-plan';

    /** アップグレード区分クラス名 2：アップグレード商品 */
    const UPGRADE_CLASS_UPGRADE = 'upgrade-upgrade';

    /** 会員情報区分 2：他社 */
    const KAIIN_INFO_TASYA = '2';

    /** 会員情報区分 3：OM */
    const KAIIN_INFO_OM = '3';

    /** 用途区分: 1=>コース施行 */
    const YOTO_KBN_COURSE = '1';

    /** 用途区分: 2=>プラン施行 */
    const YOTO_KBN_PLAN = '2';

    /** 用途区分: 3=>金額充当 */
    const YOTO_KBN_JUTO = '3';

    /** 用途区分: 4=>使用しない */
    const YOTO_KBN_NOUSE = '4';

    /** 用途区分: 5=>解約指図払い */
    const YOTO_KBN_KAIYAKU = '5';

    /** 試算APIのステータス(戻値) */
    const SIM_STATUS_COMP = 4;

    /** その他分類区分物品販売:8006=>作業料 */
    const OTHER_BUNRUI_KBN_SAGYOPRC = '8006';

    /** その他分類区分物品販売:8007=>高速料金 */
    const OTHER_BUNRUI_KBN_KOUSOKUPRC = '8007';

    /** その他分類区分物品販売:8008=>値引金額 */
    const OTHER_BUNRUI_KBN_NEBIKIPRC = '8008';

    /** その他分類区分物品販売START */
    const OTHER_BUNRUI_KBN_BUPPIN_ST = '9000';

    /** その他分類区分物品販売END */
    const OTHER_BUNRUI_KBN_BUPPIN_ED = '9010';

    /** 見積式場デフォルト設定 */
    const DEF_EST_SHIKIJO_CD = '10000';
    /** システムオプション奉仕料設定 */
    const SYS_OPTION_HOUSHI = '10001';

    /**
     *
     * 施行基本情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/2/10
     */
    protected function setInitParam() {
        // 会員区分 1:互助会,2:一般,9:その他
        $kaiinKbn = '';
        $db = Msi_Sys_DbManager::getMyDb();
        $select1 = $this->selectSekoKihon();
        if (isset($select1) && count($select1) > 0) {       // 2019/09/12 PHP Warning
            $this->_moushiKbn = $select1['moushi_kbn'];
            $this->_bumonCd = $select1['bumon_cd'];
            if (isset($select1['est_shikijo_cd'])) {
                $this->_estShikijoCd = $select1['est_shikijo_cd'];
            }
            if (isset($select1['seko_tanto_cd'])) {
                $this->_sekoTantoCd = $select1['seko_tanto_cd'];
            }
            $this->_sekoPlanCd = $select1['seko_plan_cd'];
            $kaiinKbn = $select1['kaiin_kbn'];
            $this->_kaiinKbn = $kaiinKbn;
            $this->_tmScheduleCd = $select1['tm_schedule_cd'];
            $this->_juchuKakuteiYMD = $select1['jichu_kakute_ymd'];
            $this->_sekyuCd = $select1['sekyu_cd'];
            $this->_mainPtCd = $select1['main_pt_cd'];
            $this->_mainPtKbn = $select1['main_pt_kbn'];
            $this->_gojokaiKbn = $select1['gojokai_kbn'];
            $this->_selectSekoKihon = $select1;
            $this->_selKijunYmd = $select1['sougi_ymd']; // 2016/03/04 ADD Kayo
            if (strlen($this->_selKijunYmd) <= 0) {
                $this->_selKijunYmd = Msi_Sys_Utils::getDate();
            }
        }
        $kihonFree = DataMapper_SekoKihonAllFree::find($db, array('seko_no' => $this->_sekoNo));
        if (count($kihonFree) > 0) {
            $this->_mitsuTantoCd = $kihonFree[0]['tanto_cd1'];
        }
        $sql2 = "
        SELECT
            gojokai_cose_cd
            ,kain_no
            ,harai_gaku
            ,complet_flg
            ,cose_chg_gaku
        FROM
            seko_gojokai_member
        WHERE
            seko_no = :seko_no
        AND yoto_kbn = 1 -- 用途区分　1:葬送儀礼
        AND delete_flg = 0
                ";
        $select2 = $db->easySelOne($sql2, array('seko_no' => $this->_sekoNo));
        if (isset($select2) && count($select2) > 0) {
            $this->_gojokaiCoseCd = $select2['gojokai_cose_cd'];
            $this->_kaiinNo = $select2['kain_no'];
            $this->_complet_flg = $select2['complet_flg'];
            $this->_selectSekoKihon = array_merge($this->_selectSekoKihon, $select2);
        }
        $sql3 = "
        SELECT
            plan_change_prc
            ,nebiki_gojokai_kbn
            ,nebiki_yoto_kbn
        FROM
            seko_gojokai_info
        WHERE
            seko_no = :seko_no
        AND delete_flg = 0
                ";
        $select3 = $db->easySelOne($sql3, array('seko_no' => $this->_sekoNo));
        if (isset($select3) && count($select3) > 0) {
            $this->_nebikiGojokaiKbn = $select3['nebiki_gojokai_kbn'];
            $this->_nebikiYotoKbn = $select3['nebiki_yoto_kbn'];
            $this->_selectSekoKihon = array_merge($this->_selectSekoKihon, $select3);
        }
    }

    /**
     *
     * <AUTHOR> Sai
     * @since 2014/1/28
     * @param array $dataApp 受注伝票情報
     * @param string $where 条件
     * @return array 受注情報
     */
    protected function getJuchuDetailDataFromMst($dataApp, $where = '') {
        if (empty($dataApp["seko_plan_cd"])) {
            return array();
        }
        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen(); // '2019/10/01'; // 軽減税率適用開始日   keigen mihara
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            msi_no              -- 受注明細№
            ,add_kbn            -- 追加区分
            ,row_number () OVER () AS disp_no -- 表示順
            ,'{$dataApp['bumon_cd']}' AS bumon_cd -- 部門コード
            ,bumon_cd AS shohin_bumon_cd -- 商品部門コード
            ,1 AS denpyo_kbn    -- 伝票区分
            ,shohin_cd		-- 商品コード
            ,shohin_cd AS item		-- 商品コード
            ,shohin_kbn		-- 商品区分
            ,shohin_kbn_nm	-- 商品区分名
            ,shohin_nm		-- 商品名称
            ,shohin_tkiyo_nm    -- 商品摘要
            ,mokuteki_inp_kbn   -- 使用目的入力区分
            ,mokuteki_kbn       -- 使用目的区分
            ,dai_bunrui_cd	-- 大分類コード
            ,chu_bunrui_cd	-- 中分類コード
            ,juchu_tnk		-- 値引後単価
            ,juchu_suryo	-- 数量
            ,hoshi_prc_tnk      -- サービス料
            ,(juchu_tnk + hoshi_prc_tnk) * juchu_suryo + nebiki_prc + gojokai_nebiki_prc AS juchu_gokei-- 受注金額合計
            ,juchu_prc		-- 受注金額
            ,nebiki_prc		-- 値引額
            ,gojokai_nebiki_prc	-- 互助会値引額
            ,gen_tnk		-- 原価単価
            ,shohin_type	-- 商品タイプ
            ,nonyu_nm		-- 納入場所
            ,nonyu_dt		-- 納入予定日
            ,nm_input_kbn	-- 名称入力区分
            ,tani_cd		-- 単位コード
            ,zei_kbn            -- 売上課税区分
            ,uri_kamoku_cd      -- 科目コード
            ,CASE 
                WHEN siire_lnm IS NULL THEN
                    NULL 
                ELSE siire_cd 
            END siire_cd-- 仕入コード
            ,siire_lnm          -- 仕入名
            ,tnk_chg_kbn        -- 売上単価変更区分
            ,hachu_kbn          -- 発注書区分
            ,hoshi_umu_kbn	-- 奉仕料有無区分
            ,hoshi_prc_tnk * juchu_suryo hoshi_prc  -- 奉仕料
            ,NULL AS nonyu_cd   -- 納入先コード 
            ,NULL AS nonyu_knm  -- 納入先名カナ
            ,NULL AS nonyu_yubin_no -- 納入先郵便番号
            ,NULL AS nonyu_addr1    -- 納入先住所1
            ,NULL AS nonyu_addr2    -- 納入先住所2
            ,NULL AS nonyu_tel  -- 納入先電話番号
            ,NULL AS nonyu_fax  -- 納入先FAX
            ,0 AS data_status   -- データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
            ,0 AS order_flg     -- 発注済み 0:未発注 1:発注済み
            ,select_shohin_cd   -- 選択商品コード
            ,refer_uchiwk_no    -- 参照先明細№
            ,service_kbn        -- サービス区分
            ,reduced_tax_rate   -- 軽減税率区分 1：対象外 2：軽減8%(商品マスタを優先) 2019/05/21 keigen Kayo ADD
            ,zei_cd				-- 消費税コード 
            ,1 AS upgrade_kbn
        FROM
            (SELECT
                spsm.msi_no
                ,spsm.add_kbn
                ,spsm.bumon_cd
                ,spsm.shohin_cd
                ,skm.shohin_kbn
                ,skm.shohin_kbn_nm
                ,sm.shohin_nm
                ,sm.shohin_tkiyo_nm
                ,sbm.dai_bunrui_cd
                ,sbm.chu_bunrui_cd
                ,COALESCE (spsm.hanbai_tnk ,0) AS juchu_tnk
                ,COALESCE (spsm.suryo ,0) AS juchu_suryo
                ,COALESCE (spsm.hanbai_tnk ,0)  AS juchu_prc
                ,0 AS nebiki_prc
                ,0 AS gojokai_nebiki_prc
                ,COALESCE (stm.siire_tnk ,0)  AS gen_tnk
                ,NULL AS shohin_type
                ,NULL AS nonyu_nm
                ,NULL AS nonyu_dt
                ,sm.nm_input_kbn
                ,sm.tani_cd
                ,sm.uri_zei_kbn AS zei_kbn
                ,sm.siire_cd 
                ,sim.siire_lnm
                ,sm.hoshi_umu_kbn
                ,sm.tnk_chg_kbn    
                ,sbm.hachu_kbn
                ,sm.uri_kamoku_cd
                -- 奉仕料
                ,CASE WHEN hoshi_umu_kbn = 1 THEN 
                    CASE WHEN {$dataApp['hasu_kbn']} = 1 THEN -- 丸め処理 0:切捨て 1:四捨五入 2:切上げ
                        ROUND(COALESCE (stm.hanbai_tnk ,0) * {$dataApp['hoshi_ritu']})
                    WHEN {$dataApp['hasu_kbn']} = 2 THEN 
                        CEIL(COALESCE (stm.hanbai_tnk ,0) * {$dataApp['hoshi_ritu']})
                    ELSE 
                        FLOOR(COALESCE (stm.hanbai_tnk ,0) * {$dataApp['hoshi_ritu']})
                    END
                ELSE 0
                END hoshi_prc_tnk
                ,spsm.select_shohin_cd
                ,spsm.refer_uchiwk_no
                ,skm.mokuteki_inp_kbn
                ,sbm.mokuteki_kbn
                ,spsm.service_kbn
                ,CASE WHEN '{{$this->_zeiKijunYmd}' < '{$keigenBgnYmd}' THEN 1
                ELSE
                    COALESCE(spsm.reduced_tax, skm.uri_reduced_tax_rate, 1)
                END AS reduced_tax_rate -- 軽減税率区分 1：対象外 2：軽減8%(商品マスタを優先) 2019/05/21 keigen Kayo ADD
		,z.zei_cd               -- 消費税コード                                       2019/05/21 keigen Kayo ADD  
            FROM
                (SELECT
                    pm.seko_plan_uchiwk_no AS msi_no
                    ,pm.add_kbn
                    ,pm.bumon_cd
                    ,pm.shohin_cd
                    ,pm.dai_bunrui_cd
                    ,pm.chu_bunrui_cd
                    ,pm.shohin_kbn
                    ,pm.hanbai_tnk
                    ,pm.suryo
                    ,pm.select_shohin_cd
                    ,pm.refer_uchiwk_no
                    ,pm.service_kbn
                    ,pm.reduced_tax
                FROM seko_plan_smsi_mst pm
                WHERE pm.delete_flg = 0
                    AND '{$this->_selKijunYmd}' BETWEEN pm.tekiyo_st_date AND pm.tekiyo_ed_date -- 2017/03/01 ADD Kayo
                    AND pm.kaisya_cd = :kaisya_cd       -- 2016/10/06 ADD Kayo    
                    AND pm.seko_plan_cd = :seko_plan_cd
                ) spsm
            INNER JOIN shohin_mst sm
                ON spsm.shohin_cd = sm.shohin_cd
                AND sm.kaisya_cd = :kaisya_cd  
                AND sm.bumon_cd = spsm.bumon_cd  
                AND '{$this->_selKijunYmd}' BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
            INNER JOIN shohin_bunrui_mst sbm
                ON sm.shohin_cd = sbm.shohin_cd
                AND sbm.kaisya_cd = :kaisya_cd       -- 2016/10/06 ADD Kayo    
                AND sbm.bumon_cd = spsm.bumon_cd
                AND sbm.delete_flg = 0
            INNER JOIN shohin_kbn_mst skm
                ON sbm.shohin_kbn = skm.shohin_kbn
            LEFT JOIN shohin_tanka_mst stm
                ON spsm.shohin_cd = stm.shohin_cd
                AND stm.kaisya_cd = :kaisya_cd       -- 2016/10/06 ADD Kayo    
                AND stm.bumon_cd = spsm.bumon_cd 
                AND '{$this->_selKijunYmd}' BETWEEN stm.tekiyo_st_date AND stm.tekiyo_ed_date
                AND stm.delete_flg = 0
            LEFT JOIN siire_mst sim
                ON sm.siire_cd = sim.siire_cd
                AND sim.kaisya_cd = :kaisya_cd       -- 2016/10/06 ADD Kayo    
                AND sim.delete_flg = 0
            LEFT JOIN zei_mst z	
		ON '{$this->_zeiKijunYmd}' BETWEEN TO_CHAR(z.tekiyo_st_date,'YYYY/MM/DD')
		AND TO_CHAR(z.tekiyo_ed_date,'YYYY/MM/DD')	-- 2019/05/21 keigen Kayo ADD
		AND z.delete_flg = 0
                AND z.reduced_tax_rate = (CASE WHEN '{$this->_zeiKijunYmd}' < '{$keigenBgnYmd}' THEN 1
                                           ELSE COALESCE(spsm.reduced_tax, skm.uri_reduced_tax_rate, 1) END) -- 2019/05/21 keigen Kayo ADD
            WHERE sm.delete_flg = 0
                AND skm.delete_flg = 0
                AND sbm.dai_bunrui_cd = spsm.dai_bunrui_cd 
                AND sbm.chu_bunrui_cd = spsm.chu_bunrui_cd 
                AND sbm.shohin_kbn = spsm.shohin_kbn 
                {$where}
            ORDER BY spsm.msi_no
        ) M
        ORDER BY disp_no
                ";
        // 複数会社対応 2016/10/06 ADD Kayo
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $select = $db->easySelect($sql, array(
            'seko_plan_cd' => $dataApp["seko_plan_cd"],
            'kaisya_cd' => $curKaisyaCd
        ));
        // 佐野付帯値引設定処理
        App_Utils2::adjGojokaiNebiki3($this->_sekoNo, $select);
        // データを設定する
        $dataDtl = $this->setSelectData($select);
        // 表示順を再設定する
        App_Utils::resetDispNo($dataDtl, $this->_sekoNo, $this->_sekoNoSub);
        return $dataDtl;
    }

    /**
     *
     * <AUTHOR> Tosaka
     * @since 2021/xx/xx
     * @param array $dataApp 受注伝票情報
     * @param string $where 条件
     * @return array 受注情報
     */
    protected function getJuchuDetailDataFromMstForOm($dataApp, $where = '') {
        if (empty($dataApp["seko_plan_cd"])) {
            return array();
        }
        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen(); // '2019/10/01'; // 軽減税率適用開始日   keigen mihara
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            msi_no              -- 受注明細№
            ,add_kbn            -- 追加区分
            ,row_number () OVER () AS disp_no -- 表示順
            ,'{$dataApp['bumon_cd']}' AS bumon_cd -- 部門コード
            ,bumon_cd AS shohin_bumon_cd -- 商品部門コード
            ,1 AS denpyo_kbn    -- 伝票区分
            ,shohin_cd		-- 商品コード
            ,shohin_cd AS item		-- 商品コード
            ,shohin_kbn		-- 商品区分
            ,shohin_kbn_nm	-- 商品区分名
            ,shohin_nm		-- 商品名称
            ,shohin_tkiyo_nm    -- 商品摘要
            ,mokuteki_inp_kbn   -- 使用目的入力区分
            ,mokuteki_kbn       -- 使用目的区分
            ,dai_bunrui_cd	-- 大分類コード
            ,chu_bunrui_cd	-- 中分類コード
            ,juchu_tnk		-- 値引後単価
            ,juchu_suryo	-- 数量
            ,hoshi_prc_tnk      -- サービス料
            ,(juchu_tnk + hoshi_prc_tnk) * juchu_suryo + nebiki_prc + gojokai_nebiki_prc AS juchu_gokei-- 受注金額合計
            ,juchu_prc		-- 受注金額
            ,nebiki_prc		-- 値引額
            ,gojokai_nebiki_prc	-- 互助会値引額
            ,gen_tnk		-- 原価単価
            ,shohin_type	-- 商品タイプ
            ,nonyu_nm		-- 納入場所
            ,nonyu_dt		-- 納入予定日
            ,nm_input_kbn	-- 名称入力区分
            ,tani_cd		-- 単位コード
            ,zei_kbn            -- 売上課税区分
            ,uri_kamoku_cd      -- 科目コード
            ,CASE 
                WHEN siire_lnm IS NULL THEN
                    NULL 
                ELSE siire_cd 
            END siire_cd-- 仕入コード
            ,siire_lnm          -- 仕入名
            ,tnk_chg_kbn        -- 売上単価変更区分
            ,hachu_kbn          -- 発注書区分
            ,hoshi_umu_kbn	-- 奉仕料有無区分
            ,hoshi_prc_tnk * juchu_suryo hoshi_prc  -- 奉仕料
            ,NULL AS nonyu_cd   -- 納入先コード 
            ,NULL AS nonyu_knm  -- 納入先名カナ
            ,NULL AS nonyu_yubin_no -- 納入先郵便番号
            ,NULL AS nonyu_addr1    -- 納入先住所1
            ,NULL AS nonyu_addr2    -- 納入先住所2
            ,NULL AS nonyu_tel  -- 納入先電話番号
            ,NULL AS nonyu_fax  -- 納入先FAX
            ,0 AS data_status   -- データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
            ,0 AS order_flg     -- 発注済み 0:未発注 1:発注済み
            ,NULL AS select_shohin_cd   -- 選択商品コード
            ,NULL AS refer_uchiwk_no    -- 参照先明細№
            ,NULL AS service_kbn        -- サービス区分
            ,reduced_tax_rate   -- 軽減税率区分 1：対象外 2：軽減8%(商品マスタを優先) 2019/05/21 keigen Kayo ADD
            ,zei_cd				-- 消費税コード 
            ,1 AS upgrade_kbn
        FROM
            (SELECT
                spsm.msi_no
                ,spsm.add_kbn
                ,spsm.bumon_cd
                ,spsm.shohin_cd
                ,skm.shohin_kbn
                ,skm.shohin_kbn_nm
                ,sm.shohin_nm
                ,sm.shohin_tkiyo_nm
                ,sbm.dai_bunrui_cd
                ,sbm.chu_bunrui_cd
                ,COALESCE (spsm.hanbai_tnk ,0) AS juchu_tnk
                ,COALESCE (spsm.suryo ,0) AS juchu_suryo
                ,COALESCE (spsm.hanbai_tnk ,0)  AS juchu_prc
                ,0 AS nebiki_prc
                ,0 AS gojokai_nebiki_prc
                ,COALESCE (stm.siire_tnk ,0)  AS gen_tnk
                ,NULL AS shohin_type
                ,NULL AS nonyu_nm
                ,NULL AS nonyu_dt
                ,sm.nm_input_kbn
                ,sm.tani_cd
                ,sm.uri_zei_kbn AS zei_kbn
                ,sm.siire_cd 
                ,sim.siire_lnm
                ,sm.hoshi_umu_kbn
                ,sm.tnk_chg_kbn    
                ,sbm.hachu_kbn
                ,sm.uri_kamoku_cd
                -- 奉仕料
                ,CASE WHEN hoshi_umu_kbn = 1 THEN 
                    CASE WHEN {$dataApp['hasu_kbn']} = 1 THEN -- 丸め処理 0:切捨て 1:四捨五入 2:切上げ
                        ROUND(COALESCE (stm.hanbai_tnk ,0) * {$dataApp['hoshi_ritu']})
                    WHEN {$dataApp['hasu_kbn']} = 2 THEN 
                        CEIL(COALESCE (stm.hanbai_tnk ,0) * {$dataApp['hoshi_ritu']})
                    ELSE 
                        FLOOR(COALESCE (stm.hanbai_tnk ,0) * {$dataApp['hoshi_ritu']})
                    END
                ELSE 0
                END hoshi_prc_tnk
                ,skm.mokuteki_inp_kbn
                ,sbm.mokuteki_kbn
            --    ,spsm.service_kbn
                ,CASE WHEN '{{$this->_zeiKijunYmd}' < '{$keigenBgnYmd}' THEN 1
                ELSE
                    COALESCE(spsm.reduced_tax, skm.uri_reduced_tax_rate, 1)
                END AS reduced_tax_rate -- 軽減税率区分 1：対象外 2：軽減8%(商品マスタを優先) 2019/05/21 keigen Kayo ADD
		,z.zei_cd               -- 消費税コード                                       2019/05/21 keigen Kayo ADD  
            FROM
                (SELECT
                    pm.msi_no
                    ,pm.add_kbn
                    ,pm.shohin_bumon_cd AS bumon_cd
                    ,pm.shohin_cd
                    ,pm.dai_bunrui_cd
                    ,pm.chu_bunrui_cd
                    ,pm.shohin_kbn
                    ,pm.juchu_tnk AS hanbai_tnk
                    ,pm.juchu_suryo AS suryo
                    ,pm.reduced_tax_rate AS reduced_tax
                FROM juchu_denpyo_msi pm
                INNER JOIN juchu_denpyo d
                    ON d.denpyo_no = pm.denpyo_no
                    AND d.data_kbn = 1
                WHERE pm.delete_flg = 0
                    AND pm.seko_no = :seko_no
                ) spsm
            INNER JOIN shohin_mst sm
                ON spsm.shohin_cd = sm.shohin_cd
                AND sm.kaisya_cd = :kaisya_cd  
                AND sm.bumon_cd = spsm.bumon_cd  
            INNER JOIN shohin_bunrui_mst sbm
                ON sm.shohin_cd = sbm.shohin_cd
                AND sbm.kaisya_cd = :kaisya_cd       -- 2016/10/06 ADD Kayo    
                AND sbm.bumon_cd = spsm.bumon_cd
                AND sbm.delete_flg = 0
            INNER JOIN shohin_kbn_mst skm
                ON sbm.shohin_kbn = skm.shohin_kbn
            LEFT JOIN shohin_tanka_mst stm
                ON spsm.shohin_cd = stm.shohin_cd
                AND stm.kaisya_cd = :kaisya_cd       -- 2016/10/06 ADD Kayo    
                AND stm.bumon_cd = spsm.bumon_cd 
                AND {$this->_selKijunYmd} BETWEEN stm.tekiyo_st_date AND stm.tekiyo_ed_date
                AND stm.delete_flg = 0
            LEFT JOIN siire_mst sim
                ON sm.siire_cd = sim.siire_cd
                AND sim.kaisya_cd = :kaisya_cd       -- 2016/10/06 ADD Kayo    
                AND sim.delete_flg = 0
            LEFT JOIN zei_mst z	
		ON '{$this->_zeiKijunYmd}' BETWEEN TO_CHAR(z.tekiyo_st_date,'YYYY/MM/DD')
		AND TO_CHAR(z.tekiyo_ed_date,'YYYY/MM/DD')	-- 2019/05/21 keigen Kayo ADD
		AND z.delete_flg = 0
                AND z.reduced_tax_rate = (CASE WHEN '{$this->_zeiKijunYmd}' < '{$keigenBgnYmd}' THEN 1
                                           ELSE COALESCE(spsm.reduced_tax, skm.uri_reduced_tax_rate, 1) END) -- 2019/05/21 keigen Kayo ADD
            WHERE sm.delete_flg = 0
                AND skm.delete_flg = 0
                AND sbm.dai_bunrui_cd = spsm.dai_bunrui_cd 
                AND sbm.chu_bunrui_cd = spsm.chu_bunrui_cd 
                AND sbm.shohin_kbn = spsm.shohin_kbn 
                {$where}
            ORDER BY spsm.msi_no
        ) M
        ORDER BY disp_no
                ";
        // 複数会社対応 2016/10/06 ADD Kayo
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $select = $db->easySelect($sql, array(
            'seko_no' => $dataApp['om_seko_no']
            , 'kaisya_cd' => $curKaisyaCd
        ));
        // 佐野付帯値引設定処理
        App_Utils2::adjGojokaiNebiki3($this->_sekoNo, $select);
        // データを設定する
        $dataDtl = $this->setSelectData($select);
        // 表示順を再設定する
        App_Utils::resetDispNo($dataDtl, $this->_sekoNo, $this->_sekoNoSub);
        return $dataDtl;
    }

    /**
     * 互助会コース特典マスタの互助会コースコードの条件と特典区分の条件を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/06/13
     * @return string $where 互助会コース特典マスタ絞込み条件
     */
    private function getTkWhere() {
        $where = '';
        $coseCd = $this->_gojokaiCoseCd;
        // コース変更差額があったら、互助会コース特典マスタの互コースコード固定 9999
        if (isset($this->_selectSekoKihon['plan_change_prc']) && $this->_selectSekoKihon['plan_change_prc'] > 0) {
            $coseCd = '9999';
            $where .= " AND gtm.gojokai_kbn = :gojokai_kbn";
        }
        $where .= " AND gtm.gojokai_cose_cd = '" . $coseCd . "'";
        if ($this->_complet_flg === '0' || $coseCd === '9999') {
            $where .= " AND gtm.cose_tkn_kbn IN (1) ";
        } else if ($this->_complet_flg === '1') {
            $where .= " AND gtm.cose_tkn_kbn IN (2) ";
        }
        return $where;
    }

    /**
     *
     * 画面表示の受注データを設定する
     *
     * <AUTHOR> Sai
     * @since 2014/1/30
     * @param array $select クエリデータ
     * @return array 画面表示データ
     */
    protected function setSelectData($select) {
        $dataDtl = array();
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $dataDtl[$i] = array(); // 
                $dataDtl[$i]['msi_no'] = (int) $select[$i]['msi_no']; //受注明細№
                $dataDtl[$i]['disp_no'] = (int) $select[$i]['disp_no']; //表示順
                // 表示順が0の時
                if ($i === 0) {
                    $dataDtl[$i]['disp_no'] = 1;
                } else {
                    $dataDtl[$i]['disp_no'] = $dataDtl[$i - 1]['disp_no'] + 1;
                }
                if (isset($select[$i]['data_sbt'])) {
                    $dataDtl[$i]['data_sbt'] = $select[$i]['data_sbt'];
                }
                if (isset($select[$i]['add_kbn'])) {
                    $dataDtl[$i]['add_kbn'] = $select[$i]['add_kbn'];
                }
                if (isset($select[$i]['select_shohin_cd'])) {
                    $dataDtl[$i]['select_shohin_cd'] = $select[$i]['select_shohin_cd'];
                }
                if (isset($select[$i]['refer_uchiwk_no'])) {
                    $dataDtl[$i]['refer_uchiwk_no'] = $select[$i]['refer_uchiwk_no'];
                }
                if (isset($select[$i]['service_kbn'])) {
                    $dataDtl[$i]['service_kbn'] = $select[$i]['service_kbn'];
                }
                $dataDtl[$i]['denpyo_kbn'] = $select[$i]['denpyo_kbn']; //伝票区分
                $dataDtl[$i]['shohin_cd'] = $select[$i]['shohin_cd']; //商品コード
                $dataDtl[$i]['shohin_kbn'] = $select[$i]['shohin_kbn']; //商品区分
                $dataDtl[$i]['shohin_kbn_nm'] = $select[$i]['shohin_kbn_nm']; //商品区分名
                $dataDtl[$i]['shohin_nm'] = $select[$i]['shohin_nm']; //商品名称
                $dataDtl[$i]['shohin_tkiyo_nm'] = $select[$i]['shohin_tkiyo_nm']; //商品摘要
                if (isset($select[$i]['shohin_tkiyo_nm2'])) {
                    $dataDtl[$i]['shohin_tkiyo_nm2'] = $select[$i]['shohin_tkiyo_nm2']; //商品摘要2 mihara 2015/11/02
                }
                $dataDtl[$i]['dai_bunrui_cd'] = $select[$i]['dai_bunrui_cd']; //大分類コード
                $dataDtl[$i]['chu_bunrui_cd'] = $select[$i]['chu_bunrui_cd']; //中分類コード
                $dataDtl[$i]['juchu_tnk'] = $select[$i]['juchu_tnk']; //値引後単価
                $dataDtl[$i]['juchu_suryo'] = $select[$i]['juchu_suryo']; //数量
                $dataDtl[$i]['juchu_gokei'] = (float) $select[$i]['juchu_gokei']; //受注金額合計
                $dataDtl[$i]['juchu_prc'] = $select[$i]['juchu_prc']; //受注金額
                $dataDtl[$i]['nebiki_prc'] = $select[$i]['nebiki_prc']; //値引額
                $dataDtl[$i]['gojokai_nebiki_prc'] = $select[$i]['gojokai_nebiki_prc']; //値引額
                $dataDtl[$i]['gen_tnk'] = $select[$i]['gen_tnk']; //原価単価
                $dataDtl[$i]['shohin_type'] = $select[$i]['shohin_type']; //商品タイプ
                $dataDtl[$i]['nonyu_nm'] = $select[$i]['nonyu_nm']; //納入場所
                $dataDtl[$i]['nonyu_dt'] = $select[$i]['nonyu_dt']; //納入予定日
                $dataDtl[$i]['nm_input_kbn'] = $select[$i]['nm_input_kbn']; //名称入力区分
                $dataDtl[$i]['mokuteki_kbn'] = $select[$i]['mokuteki_kbn']; //目的区分
                $dataDtl[$i]['mokuteki_inp_kbn'] = $select[$i]['mokuteki_inp_kbn']; // 使用目的入力区分
                $dataDtl[$i]['bumon_cd'] = $select[$i]['bumon_cd']; //部門コード
                $dataDtl[$i]['tani_cd'] = $select[$i]['tani_cd']; //単位コード
                $dataDtl[$i]['uri_kamoku_cd'] = $select[$i]['uri_kamoku_cd']; //科目コード
                $dataDtl[$i]['zei_kbn'] = $select[$i]['zei_kbn']; //消費税区分
                $dataDtl[$i]['siire_cd'] = $select[$i]['siire_cd']; //仕入コード
                $dataDtl[$i]['siire_lnm'] = $select[$i]['siire_lnm']; //仕入名
                $dataDtl[$i]['tnk_chg_kbn'] = $select[$i]['tnk_chg_kbn']; //売上単価変更区分
                $dataDtl[$i]['hachu_kbn'] = $select[$i]['hachu_kbn']; //発注書区分
                $dataDtl[$i]['hoshi_umu_kbn'] = $select[$i]['hoshi_umu_kbn']; //奉仕料有無区分
                $dataDtl[$i]['data_status'] = $select[$i]['data_status']; // データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
                $dataDtl[$i]['data_up_flg'] = 0; // データ更新対象フラグ 0:対象外 1:対象(画面新規入力データOR画面より変更)
                $dataDtl[$i]['order_flg'] = $select[$i]['order_flg']; // 発注済み 0:未発注 1:発注済み
                $dataDtl[$i]['hoshi_prc'] = $select[$i]['hoshi_prc']; // 奉仕料金額
                $dataDtl[$i]['nonyu_cd'] = $select[$i]['nonyu_cd']; // 納入先コード
                $dataDtl[$i]['nonyu_knm'] = $select[$i]['nonyu_knm']; // 納入先名カナ
                $dataDtl[$i]['nonyu_yubin_no'] = $select[$i]['nonyu_yubin_no']; // 納入先郵便番号
                $dataDtl[$i]['nonyu_addr1'] = $select[$i]['nonyu_addr1']; // 納入先住所1
                $dataDtl[$i]['nonyu_addr2'] = $select[$i]['nonyu_addr2']; // 納入先住所2
                $dataDtl[$i]['nonyu_tel'] = $select[$i]['nonyu_tel']; // 納入先電話番号
                $dataDtl[$i]['nonyu_fax'] = $select[$i]['nonyu_fax']; // 納入先FAX
                if (isset($select[$i]['tani_nm'])) {
                    $dataDtl[$i]['tani_nm'] = $select[$i]['tani_nm'];
                } else {
                    $dataDtl[$i]['tani_nm'] = null;
                }
                if (isset($select[$i]['reduced_tax_rate'])) {                           // 2019/05/21 Kayo ADD keigen
                    $dataDtl[$i]['reduced_tax_rate'] = $select[$i]['reduced_tax_rate']; // 軽減税率区分 
                }
                if (isset($select[$i]['zei_cd'])) {                                     // 2019/05/21 Kayo ADD keigen
                    $dataDtl[$i]['zei_cd'] = $select[$i]['zei_cd']; // 消費税コード
                }
                if (isset($select[$i]['shohin_cd_mix'])) {
                    $dataDtl[$i]['shohin_cd_mix'] = $select[$i]['shohin_cd_mix'];
                }
                if (isset($select[$i]['print_group_cd'])) {
                    $dataDtl[$i]['print_group_cd'] = $select[$i]['print_group_cd'];
                }
                if (isset($select[$i]['add_henpin_ymd'])) {
                    $dataDtl[$i]['add_henpin_ymd'] = $select[$i]['add_henpin_ymd'];
                }
                if (isset($select[$i]['shohin_bumon_cd'])) {
                    $dataDtl[$i]['shohin_bumon_cd'] = $select[$i]['shohin_bumon_cd'];
                }
                if (isset($select[$i]['upgrade_kbn'])) {
                    $dataDtl[$i]['upgrade_kbn'] = $select[$i]['upgrade_kbn'];
                }
                if (isset($select[$i]['v_free1'])) {
                    $dataDtl[$i]['v_free1'] = $select[$i]['v_free1'];
                }
                // 受注金額が0円かつ付帯特典、割引額がいずれも0円の場合はNULL
                if ($dataDtl[$i]['juchu_prc'] == 0 && $dataDtl[$i]['nebiki_prc'] == 0 && $dataDtl[$i]['gojokai_nebiki_prc'] == 0) {
                    $dataDtl[$i]['n_free2'] = null;
                } else {
                    $dataDtl[$i]['n_free2'] = $select[$i]['n_free2'];
                }
                if (isset($select[$i]['d_import_kbn'])) {
                    $dataDtl[$i]['d_import_kbn'] = $select[$i]['d_import_kbn'];
                }
                if (isset($select[$i]['tanto_cd'])) {
                    $dataDtl[$i]['tanto_cd'] = $select[$i]['tanto_cd'];
                }
            }
        }
        return $dataDtl;
    }

    /**
     *
     * 受注伝票採番処理
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @since 2014/05/12 基準日にシステム日付を設定する
     * @param Msi_Sys_Db $db db
     * @return string 受注伝票番号
     */
    protected function getAutoDenpyoNo($db) {
        $kijyunYmd = Msi_Sys_Utils::getDate();
        $juchuDenpyoNo = App_ClsGetCodeNo::GetCodeNo($db, 'juchu_denpyo', 'denpyo_no', $kijyunYmd);
        return $juchuDenpyoNo;
    }

    /**
     *
     * 売上伝票採番処理
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @since 2014/05/12 基準日にシステム日付を設定する
     * @param Msi_Sys_Db $db db
     * @return string 売上伝票番号
     */
    protected function getAutoUriageDenpyoNo($db) {
        $kijyunYmd = $this->getSogiYmd();
        $uriageDenpyoNo = App_ClsGetCodeNo::GetCodeNo($db, 'uriage_denpyo', 'uri_den_no', $kijyunYmd);
        return $uriageDenpyoNo;
    }

    /**
     *
     * 施行番号採番処理
     *
     * <AUTHOR> Sai
     * @since 2014/2/
     * @since 2014/05/12 基準日にシステム日付を設定する
     * @param Msi_Sys_Db $db db
     * @return string 施行番号
     */
    protected function getAutoSekoNo($db) {
        if (Msi_Sys_Utils::isSystemServerModeLocal()) { // ローカルの場合は sekoNo 登録できない  2015/08/18 mihara
            $data = array(
                'status' => 'NG',
                'msg' => ('ローカル版では施行データの新規作成はできません'),
            );
            Msi_Sys_Utils::outJson($data);
            return null;
        }
        $kijyunYmd = Msi_Sys_Utils::getDate();
        $sekoNo = App_ClsGetCodeNo::GetCodeNo($db, 'seko_kihon_info', 'seko_no', $kijyunYmd);
//        $sekoNo = App_ClsGetDailyNo::GetSekoNoSogi($db);
        return $sekoNo;
    }

    /**
     *
     * 施行番号採番処理
     * 葬儀日を優先して採番する
     * <AUTHOR> Sai
     * @since 2017/10/05 基準日に葬儀日またはシステム日付を設定する
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihon 
     * @return string 施行番号
     */
    protected function getAutoSekoNo2($db, $dataSekoKihon) {
        if (Msi_Sys_Utils::isSystemServerModeLocal()) { // ローカルの場合は sekoNo 登録できない  2015/08/18 mihara
            $data = array(
                'status' => 'NG',
                'msg' => ('ローカル版では施行データの新規作成はできません'),
            );
            Msi_Sys_Utils::outJson($data);
            return null;
        }
        //$kijyunYmd = Msi_Sys_Utils::getDate();
        //if (isset($dataSekoKihon['sougi_ymd']) && !empty($dataSekoKihon['sougi_ymd'])) {
        //    $kijyunYmd = $dataSekoKihon['sougi_ymd'];
        //}
        //$sekoNo = App_ClsGetCodeNo::GetCodeNo($db, 'seko_kihon_info', 'seko_no', $kijyunYmd);
        $sekoNo = App_ClsGetDailyNo::GetSekoNoSogi($db);
        return $sekoNo;
    }

    /**
     *
     * 最大受注明細№を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/30
     * @param string $denpyoNo 伝票番号
     * @return int 最大受注明細№
     */
    protected function getMaxdenpyoMsiNo($denpyoNo) {
        // 受注伝票明細の最大受注明細№
        $maxMsiNo = 0;
        // 施行プラン商品明細マスタの最大施行プラン明細№
        $maxSekoPlanUchiwkNo = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql1 = "
        SELECT
            COALESCE(MAX(msi_no),0) AS msi_no
        FROM
            juchu_denpyo_msi
        WHERE
            denpyo_no = :denpyo_no
                ";
        $select1 = $db->easySelOne($sql1, array('denpyo_no' => $denpyoNo));
        if (count($select1) > 0) {
            $maxMsiNo = $select1['msi_no'];
        }

        $sql2 = "
        SELECT
            COALESCE(MAX(seko_plan_uchiwk_no),0) AS seko_plan_uchiwk_no
        FROM
            seko_plan_smsi_mst
        --WHERE
            --seko_plan_cd = :seko_plan_cd
                ";
        $select2 = $db->easySelOne($sql2);
        if (count($select2) > 0) {
            $maxSekoPlanUchiwkNo = $select2['seko_plan_uchiwk_no'];
        }
        $max = max($maxMsiNo, $maxSekoPlanUchiwkNo);
        return $max;
    }

    /**
     *
     * 最大売上明細№を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param string $denpyoNo 伝票番号
     * @return int 最大受注明細№
     */
    protected function getMaxUriagedenpyoMsiNo($denpyoNo) {
        // 売上伝票明細の最大売上明細№
        $maxMsiNo = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            COALESCE(MAX(msi_no),0) AS msi_no
        FROM
            uriage_denpyo_msi
        WHERE
            uri_den_no = :denpyo_no
                ";
        $select = $db->easySelOne($sql, array('denpyo_no' => $denpyoNo));
        if (count($select) > 0) {
            $maxMsiNo = $select['msi_no'];
        }
        return $maxMsiNo;
    }

    /**
     *
     * 施行発注管理情報最大明細№を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param string $sekoNo 施行番号
     * @return int 最大№
     */
    protected function getMaxHachuMsiNo($sekoNo) {
        // 売上伝票明細の最大売上明細№
        $maxMsiNo = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            COALESCE(MAX(hachu_no),0) AS hachu_no
        FROM
            seko_hachu_info
        WHERE
            seko_no = :seko_no
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $sekoNo));
        if (count($select) > 0) {
            $maxMsiNo = $select['hachu_no'];
        }
        return $maxMsiNo;
    }

    /**
     *
     * 登録SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @param string $tableName テーブル名称
     * @param array $tableInfo テーブル情報
     * @param array $except 除外カラム
     * @return string SQL
     */
    protected function makeInsertSQL($tableName, $tableInfo, $except = array()) {
        $sql = "INSERT INTO {$tableName}\n";
        $sqlKey = "(\n";
        $sqlValue = "values (\n";
        foreach ($tableInfo as $key => $value) {
            if (!in_array($key, $except)) {
                $sqlKey .= "{$key}\n,";
                if ($value === null || $value === '') {
                    $sqlValue .= "null\n,";
                } else {
                    $sqlValue .= "'{$value}'\n,";
                }
            }
        }
        $sqlKey_ = rtrim($sqlKey, ',');
        $sqlValue_ = rtrim($sqlValue, ',');
        $sqlKey_ .= ")";
        $sqlValue_ .= ");";
        return $sql . $sqlKey_ . $sqlValue_;
    }

    /**
     *
     * 更新SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @param string $tableName テーブル名称
     * @param array $tableInfo テーブル情報
     * @param array $where 条件
     * @param array $except 除外カラム
     * @return string SQL
     */
    protected function makeUpdateSQL($tableName, $tableInfo, $where, $except = array()) {

        $sql = "UPDATE {$tableName} SET\n";
        foreach ($tableInfo as $key => $value) {
            if (!in_array($key, $except)) {
                if ($key !== 'where') {
                    if ($value === null || $value === '') {
                        $sql .= "{$key} = null\n,";
                    } else {
                        $sql .= "{$key} = '{$value}'\n,";
                    }
                }
            }
        }
        $sql_ = rtrim($sql, ',');
        $sqlWhere = "WHERE 1=1\n";
        foreach ($where as $key => $value) {
            $sqlWhere .= "AND {$key} = '{$value}'\n";
        }

        return $sql_ . $sqlWhere;
    }

    /**
     *
     * 施行基本select処理
     *
     * <AUTHOR> Sai
     * @since 2014/2/18
     * @param string $sekoNo
     * @return array $select
     */
    protected function selectSekoKihon($sekoNo = null) {
        if (empty($sekoNo)) {
            $select = DataMapper_SekoKihon::find2($this->_sekoNo);
        } else {
            $select = DataMapper_SekoKihon::find2($sekoNo);
        }
        return $select;
    }

    /**
     *
     * 施行基本情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/12
     * @version 2014/05/30 お知らせ状で使用するため、Juchu_JuchuCustomerinfoから移動
     * @version 2014/11/30 台帳番号の空白を削除 Kayo
     * @return array 施行基本情報
     */
    protected function getSekoKihon() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            k.seko_no           -- 施行番号
            ,k.bumon_cd         -- 部門コード
            ,k.moushi_cd        -- 申込コード
            ,k.moushi_kbn       -- 申込区分
            ,k.sougi_cd         -- 葬儀コード
            ,k.sougi_kbn        -- 葬儀区分
            ,ltrim(rtrim(daicho_no_eria))   AS daicho_no_eria	-- 台帳番号（エリア）
            ,ltrim(rtrim(k.daicho_no_mm))	AS daicho_no_mm		-- 台帳番号（月）
            ,ltrim(rtrim(k.daicho_no_seq))  AS daicho_no_seq	-- 台帳番号（連番）
            ,k.p_info_cd        -- 個人情報保護コード
            ,k.p_info_kbn       -- 個人情報保護区分
            ,k.kaiin_cd         -- 会員コード
            ,k.kaiin_kbn        -- 会員区分
            ,k.kaiin_sonota     -- 会員区分その他
            ,k.uketuke_tanto_cd -- 受付担当者コード
            ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
            ,k.seko_tanto_cd    -- 施行担当者コード
            ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
            ,k.k_nm             -- 故人名
            ,k.k_file_nm        -- 故人名添付ファイル
            ,k.k_sex_cd         -- 性別コード
            ,k.k_sex_kbn        -- 性別区分
            ,k.k_haigu_cd       -- 配偶者コード
            ,k.k_haigu_kbn      -- 配偶者区分
            ,k.k_knm            -- 故人カナ名
            ,k.k_gengo          -- 生年月日元号
            ,TRIM(k.k_seinengappi_ymd) AS k_seinengappi_ymd  -- 生年月日
            ,k.k_nenrei_man     -- 故人年齢
            ,k.k_nenrei_kyounen -- 享年
            ,k.kg_yubin_no      -- 現住所郵便番号
            ,k.kg_addr1         -- 現住所1
            ,k.kg_tel           -- 現住所TEL
            ,k.kg_addr2         -- 現住所2
            ,k.kk_kinmusaki_kbn -- 勤務先
            ,k.kk_kinmusaki_nm  -- 勤務先名
            ,k.kk_tel           -- 勤務先TEL
            ,k.kk_yakusyoku_nm  -- 役職／職種
            ,k.kk_fax           -- 勤務先FAX
            ,k.souke_nm         -- 葬家
            ,k.souke_knm        -- 葬家カナ
            ,k.souke_addr_cd    -- 葬家住所コード
            ,k.souke_addr_kbn   -- 葬家住所区分
            ,k.souke_tel        -- 葬家TEL
            ,k.keishiki_cd      -- 葬儀形式コード
            ,k.keishiki_kbn     -- 葬儀形式区分
            ,k.syushi_cd       -- 宗旨コード
            ,k.syushi_kbn       -- 宗旨区分
            ,k.syuha_cd        -- 宗派コード
            ,k.syuha_kbn        -- 宗派区分
            ,k.syuha_nm         -- 宗派名
            ,k.jyusho_cd        -- 寺院コード
            ,k.jyusho_nm        -- 寺院名
            ,k.biko1            -- メモ（出棺経路・納骨・壇払など）
            ,k.sd_hakko_kbn     -- 診断書発行区分
            ,k.sd_step_kbn      -- 診断書手続
            ,k.sd_yotei_ymd     -- 診断書発行予定時刻
            ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD HH24:MI') AS sd_yotei_ymd-- 診断書発行予定スタンプ
            ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD') AS sd_yotei_date-- 診断書発行予定時日付のみ
            ,TO_CHAR(k.sd_yotei_ymd ,'HH24:MI') AS sd_yotei_time-- 診断書発行予定時刻のみ
            ,k.sd_copy_cnt      -- 診断書コピー枚数
            ,k.hs_kbn           -- 搬送区分
            ,k.hs_gyomu_cd      -- 搬送業務コード
            ,k.hs_gyomu_kbn     -- 搬送業務区分
            ,k.hs_spot_kbn      -- お伺い先区分
            ,k.hs_spot_cd       -- お伺い先コード
            ,k.hs_spot_nm       -- お伺い先
            ,k.hs_gyomu_cd2     -- 搬送業務コード2
            ,k.hs_gyomu_kbn2    -- 搬送業務区分2
            ,k.hs_anchi_kbn     -- 安置先区分
            ,k.hs_anchi_cd      -- 安置先コード
            ,k.hs_anchi_nm      -- 安置先
            ,k.kasoba_cd        -- 火葬場コード
            ,k.kasoba_nm        -- 火葬場名
            ,k.az_death_cnt     -- 死亡診断書枚数
            ,k.az_inkan_kbn     -- 印鑑
            ,k.az_photo_cnt     -- 御写真枚数
            ,k.az_gojokai_nm    -- 互助会証書名称
            ,k.m_nm             -- 喪主名
            ,k.m_knm            -- 喪主名カナ
            ,k.m_file_nm        -- 喪主添付ファイル
            ,k.m_zoku_cd        -- 喪主続柄コード
            ,k.m_zoku_kbn       -- 喪主続柄区分
            ,k.m_zoku_nm    -- 喪主続柄名
            ,k.m_zoku_cd2       -- 喪主からみた続柄コード
            ,k.m_zoku_kbn2      -- 喪主からみた続柄区分
            ,k.m_zoku_nm2   -- 喪主からみた続柄名
            ,k.m_gengo          -- 喪主生年月日元号
            ,TRIM(k.m_seinengappi_ymd) AS m_seinengappi_ymd-- 喪主生年月日
            ,k.m_nenrei_man     -- 喪主年齢
            ,k.mg_kbn           -- 喪主 故人に同じ
            ,k.mg_yubin_no      -- 喪主現住所郵便番号
            ,k.mg_addr1         -- 喪主現住所1
            ,k.mg_tel           -- 喪主現住所TEL
            ,k.mg_m_tel         -- 喪主携帯
            ,k.mg_addr2         -- 喪主現住所2
            ,k.mk_kinmusaki_kbn -- 喪主勤務先
            ,k.mk_kinmusaki_nm  -- 喪主勤務先名
            ,k.mk_tel           -- 喪主勤務先TEL
            ,k.mk_yakusyoku_nm  -- 喪主役職／職種
            ,k.mk_fax           -- 喪主勤務先FAX
            ,k.sekyu_kbn        -- 請求先の喪主に同じ
            ,k.sekyu_cd         -- 請求先コード
            ,k.jichu_kakute_ymd -- 受注確定日
            ,TO_CHAR(k.mt_hannyu_start ,'YYYY/MM/DD HH24:MI') AS mt_hannyu_start -- 搬入開始日時
            ,TO_CHAR(k.mt_hannyu_end ,'YYYY/MM/DD HH24:MI') AS mt_hannyu_end -- 搬入終了日時
            ,TO_CHAR(k.mt_hannyu_start ,'YYYY/MM/DD') AS mt_hannyu_ymd -- 搬入日
            ,TO_CHAR(k.mt_hannyu_start ,'HH24:MI') AS mt_hannyu_start_time -- 搬入開始時刻
            ,TO_CHAR(k.mt_hannyu_end ,'HH24:MI') AS mt_hannyu_end_time -- 搬入終了時刻
            ,k.mt_hannyu_cd     -- 搬入場所コード
            ,k.hs_delivery_cd   -- 納品場所コード
            ,k.hs_delivery_kbn  -- 納品場所区分
            ,k.mt_hannyu_nm     -- 搬入場所名
            ,TO_CHAR(skf.ts_free1::date, 'YYYY/MM/DD')   AS  uketsuke_date -- 受付日
            ,k.free3_cd AS consult_seko_no
            ,k.free5_cd AS hasno_seko_no
            ,moushi.kbn_value_lnm AS moushi_kbn_nm
        FROM
            seko_kihon_info k
            LEFT JOIN tanto_mst t1
                ON k.uketuke_tanto_cd = t1.tanto_cd
                AND t1.delete_flg = 0
            LEFT JOIN tanto_mst t2
                ON k.seko_tanto_cd = t2.tanto_cd
                AND t2.delete_flg = 0
            LEFT JOIN nm_jyusho_mst jm
                ON k.kasoba_cd = jm.jyusho_cd
                AND jm.jyusho_kbn = 3
                AND jm.delete_flg = 0
            LEFT JOIN seko_kihon_all_free skf
                ON k.seko_no = skf.seko_no
                AND skf.seq_no = 0
                AND skf.delete_flg = 0
            LEFT JOIN code_nm_mst moushi
                ON moushi.code_kbn = '0010'
                AND moushi.kbn_value_cd = k.moushi_cd
                AND moushi.delete_flg = 0
            WHERE k.seko_no = :seko_no
                AND k.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     *
     * 施行日程より亡日を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/26
     * @return array 施行日程情報
     */
    protected function getNakunaribi() {
        $nakunaribi = null;
        $select = $this->getNakunariData();
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $nakunaribi = $select['nitei_date'];
        }
        if (!isset($nakunaribi)) {
            $nakunaribi = Msi_Sys_Utils::getDate();
        }
        return $nakunaribi;
    }

    /**
     *
     * 施行日程より亡日データを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/5/30
     * @return array 施行日程情報
     */
    protected function getNakunariData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD') AS nitei_date-- 日程日付のみ
                ,TO_CHAR(sn.nitei_ymd ,'FMMM/FMDD') AS nitei_md
                ,TO_CHAR(sn.nitei_ymd ,'FMMM月FMDD日') AS nitei_md2
                ,TO_CHAR(sn.nitei_ymd ,'am/FMHH/FMMI') AS nitei_hm
                ,CASE 
                    WHEN TO_CHAR(sn.nitei_ymd, 'FMMI') = '0' 
                    THEN TO_CHAR(sn.nitei_ymd, 'FMHH時') 
                    ELSE TO_CHAR(sn.nitei_ymd, 'FMHH時FMMI分') 
                END nitei_hm2
            FROM
                seko_nitei sn
            WHERE
                    sn.seko_no = :seko_no
                AND sn.nitei_kbn = 1
                AND sn.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     *
     * 受注金額合計,原価金額合計,受注返品合計,受注値引合計,奉仕料合計,粗利益額合計を求める
     *
     * <AUTHOR> Sai
     * @param string $denpyoNo 伝票番号
     * @since 2014/2/26
     * @return 受注金額合計:array[juchu_prc_sum] 
     *          原価金額合計:array[genka_prc_sum] 
     *          受注返品合計:array[juchu_hepn_sum] 
     *          受注値引合計:array[juchu_nebk_sum] 
     *          奉仕料合計:array[hoshi_prc_sum]
     *          粗利益額合計:array[arari_prc_sum]
     */
    protected function getJuchuPrcSum($denpyoNo) {
        return App_Utils::getJuchuPrcSum($denpyoNo);
    }

    /**
     *
     * 売上金額合計,原価金額合計,売上返品合計,売上値引合計,奉仕料合計,粗利益額合計を求める
     *
     * <AUTHOR> Sai
     * @param string $denpyoNo 伝票番号
     * @since 2014/3/7
     * @return 売上金額合計:array[uri_prc_sum] 
     *          原価金額合計:array[genka_prc_sum] 
     *          売上返品合計:array[uri_hepn_sum] 
     *          売上値引合計:array[uri_nebk_sum] 
     *          奉仕料合計:array[hoshi_prc_sum]
     *          粗利益額合計:array[arari_prc_sum]
     */
    protected function getUriagePrcSum($denpyoNo) {
        return App_Utils::getUriagePrcSum($denpyoNo);
    }

    /**
     *
     * 受注伝票明細より外税課税対象額,内税課税対象額,非税課税対象額を求める
     *
     * <AUTHOR> Sai
     * @param string $denpyoNo 伝票番号
     * @since 2014/2/26
     * @return 外税課税対象額:array[szei_katax_taisho_prc] 
     *          内税課税対象額:array[uzei_katax_taisho_prc] 
     *          非税課税対象額:array[hitax_katax_taisho_prc] 
     */
    protected function getJuchuZeiSum($denpyoNo) {
        return App_Utils::getJuchuZeiSum_juchu($denpyoNo);
    }

    /**
     * 受注伝票明細 の sum より伝票の外税額,内税額を求める
     *
     * <AUTHOR> mihara
     * @param string $denpyoNo 伝票番号
     * @since 2019/04/30
     * @return array(in_zei_prc,out_zei_prc,szei_katax_taisho_prc,uzei_katax_taisho_prc,hitax_katax_taisho_prc)
     */
    protected function getJuchuZeiSum2($denpyoNo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $rtnData = App_KeigenUtils::calcJuchuZeiDenpyo($db, $denpyoNo);
        return $rtnData;
    }

    /**
     * 売上伝票明細 の sum より伝票の外税額,内税額を求める
     *
     * <AUTHOR> mihara
     * @param string $denpyoNo 伝票番号
     * @since 2019/04/30
     * @return array(in_zei_prc,out_zei_prc,szei_katax_taisho_prc,uzei_katax_taisho_prc,hitax_katax_taisho_prc)
     */
    protected function getUriageZeiSum2($denpyoNo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $rtnData = App_KeigenUtils::calcUriageZeiDenpyo($db, $denpyoNo);
        return $rtnData;
    }

    /**
     *
     * 売上伝票明細より外税課税対象額,内税課税対象額,非税課税対象額を求める
     *
     * <AUTHOR> Sai
     * @param string $denpyoNo 伝票番号
     * @since 2014/3/7
     * @return 外税課税対象額:array[szei_katax_taisho_prc] 
     *          内税課税対象額:array[uzei_katax_taisho_prc] 
     *          非税課税対象額:array[hitax_katax_taisho_prc] 
     */
    protected function getUriageZeiSum($denpyoNo) {
        return App_Utils::getUriageZeiSum_uri($denpyoNo);
    }

    /**
     *
     * 受注確定日を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/28
     * @return string 受注確定日
     */
    protected function getJuchuKakuteiYMD() {
        if ($this->_juchuKakuteiYMD) {
            return $this->_juchuKakuteiYMD;
        }
        $select = $this->selectSekoKihon();
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $this->_juchuKakuteiYMD = $select['jichu_kakute_ymd'];
        }
        return $this->_juchuKakuteiYMD;
    }

    /**
     *
     * 受注伝票明細の存在チェック
     *
     * <AUTHOR> Sai
     * @since 2014/3/3
     * @return boolean 
     */
    protected function hasJuchuDenpyoMsi() {
        $db = Msi_Sys_DbManager::getMyDb();
        // 受注伝票明細取得SQL
        $sql = "
        SELECT m.denpyo_no
        FROM juchu_denpyo_msi m
        INNER JOIN juchu_denpyo d 
            ON d.denpyo_no = m.denpyo_no
            AND d.delete_flg = 0
            AND d.data_kbn NOT IN (20)    -- データ区分：搬送は除外
        WHERE m.seko_no = '{$this->_sekoNo}'
            AND m.delete_flg = 0    
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 施行番号設定処理 
     *
     * <AUTHOR> Sai
     * @since 2014/03/06
     * @param string $controllerName コントローラ名
     */
    protected function setSekoNo($controllerName = null) {
        // 施行情報を画面より取得してセッションに設定する
        $this->setSekoParam();
    }

    /**
     * セッション施行番号設定処理 
     *
     * <AUTHOR> Sai
     * @since 2014/03/06
     * @deprecated since 2014/06/09
     * @param string $controllerName コントローラ名
     */
    protected function DELsetSessionSekoNo($controllerName) {
        if ($controllerName === self::MITSU) {
            App_Utils::setSessionData('seko_no_mitsu', $this->_sekoNo);
        } else {
            App_Utils::setSessionData('seko_no', $this->_sekoNo);
        }
    }

    /**
     * セッション施行番号クリア処理 
     *
     * <AUTHOR> Sai
     * @since 2014/03/06
     * @deprecated since 2014/06/09
     * @param string $controllerName コントローラ名
     */
    protected function DELclearSessionSekoNo($controllerName = null) {
        if ($controllerName === self::MITSU) {
            App_Utils::setSessionData('seko_no_mitsu', null);
        } else {
            App_Utils::setSessionData('seko_no', null);
        }
    }

    /**
     *
     * 受注日を基準日とする奉仕率情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/12
     * @return array $hoshi
     * 
     */
    protected function getHosiritu() {
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        $db = Msi_Sys_DbManager::getMyDb();
        // 基準日から奉仕率情報を取得する	2014/05/18 ADD Kayo
        $hoshi = App_ClsTaxLib::GetHosiritu($db, $this->_zeiKijunYmd);
        return $hoshi;
    }

    /**
     * 未確定時保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/26
     * @version 2014/09/15 消費税端数区分を追加 Kayo
     * @param Msi_Sys_Db $db db
     * @param array &$dataApp 画面Appデータ
     * @param array &$dataCol グリッドデータ
     * @param array $dataTrnDelCol グリッドトラン削除データ
     * @param array $delete_cds 削除データ（商品選択画面）
     * @return int 更新件数
     */
    public function saveJuchu($db, &$dataApp, &$dataCol, $dataTrnDelCol = array(), $delete_cds = array()) {

        $isAutoSaiban = false; // 自動採番しているか(初回登録)
        $cnt = 0;
        // 葬儀日設定されたら葬儀日、そうでなければはシステム日付
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        // 消費税取得処理
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        $dataApp['zei_rtu'] = (int) $taxInfo['zei_rtu'];
        $dataApp['zei_cd'] = (int) $taxInfo['zei_cd'];
        $dataApp['zei_hasu_kbn'] = (int) $taxInfo['zei_hasu_kbn']; // 2014/09/15 ADD Kayo
        $dataApp['zei_kijun_ymd'] = $this->_zeiKijunYmd;

        if (!isset($dataApp['hasu_kbn'])) {
            // 奉仕率コードと奉仕率を取得する
            $hoshi = $this->getHosiritu();
            $dataApp["hoshi_ritu"] = $hoshi['zei_rtu'];
            $dataApp["hoshi_ritu_cd"] = $hoshi['hoshi_ritu_cd'];
            $dataApp["hasu_kbn"] = $hoshi['hasu_kbn'];
        }
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();
        if (empty($juchuDenpyoNo)) {
            $isAutoSaiban = true;
            // 伝票番号採番処理
            $juchuDenpyoNo = $this->getAutoDenpyoNo($db);
        }
        $dataApp["denpyo_no"] = $juchuDenpyoNo;
        // グリッドトラン削除データ処理を行う
        $sqlDeleteDenpyoMsi = $this->deleteJuchudenpyoMsiSQL();
        foreach ($dataTrnDelCol as $DenpyoMsiDelRow) {
            if (isset($DenpyoMsiDelRow["msi_no"])) {
                $cnt += $db->easyExecute($sqlDeleteDenpyoMsi, array('denpyo_no' => $dataApp['denpyo_no'], 'msi_no' => $DenpyoMsiDelRow["msi_no"],));
            }
        }

        // 摘要置換処理(法友会)
        App_Utils2::adjTekiyo($dataCol);
        // 法友会セット商品の値引き金額を設定する
        App_Utils2::adjJuchuNebiki($this->_sekoNo, $this->_gojokaiKbn, $this->_sekoPlanCd, $dataCol);

//        Msi_Sys_Utils::profilerMark('2');
        // 最大受注明細№を取得する
        $maxMsiNo = $this->getMaxdenpyoMsiNo($juchuDenpyoNo);
        // 受注伝票明細データがない場合、登録する
        // 受注伝票明細データがある場合、更新する
        foreach ($dataCol as &$DenpyoMsiRow) {
            $DenpyoMsiRow["denpyo_no"] = $juchuDenpyoNo;
            // 最大受注明細№取得後、明細更新処理が終了する前に別のトランザクションで同じ葬儀の明細を更新処理をすると重複なる可能性がある
            if (isset($DenpyoMsiRow['dai_bunrui_cd'])) {
                if (!isset($DenpyoMsiRow['msi_no']) || $DenpyoMsiRow['msi_no'] === 0) {
                    // 画面新規入力データの場合、明細番号を設定する
                    $DenpyoMsiRow['msi_no'] = ++$maxMsiNo;
                }
                // 受注伝票明細存在チェック
                $sqlSelectDenpyoMsi = $this->selectJuchudenpyoMsiSQL();
//            Msi_Sys_Utils::profilerMark('3');
                $selectDenpyoMsi = $db->easySelect($sqlSelectDenpyoMsi, array('denpyo_no' => $dataApp['denpyo_no'], 'msi_no' => $DenpyoMsiRow["msi_no"]));
//            Msi_Sys_Utils::profilerMark('4');
                if (count($selectDenpyoMsi) === 0) {
                    // 受注伝票明細テーブル登録情報を設定する
//                Msi_Sys_Utils::profilerMark('beInSql');
                    $DenpyoMsiRow['upsert'] = 1; // 登録
                    $juchuDenpyoMsi = $this->setJuchudenpyoMsiInstertInfo($dataApp, $DenpyoMsiRow);
                    list($sql, $param) = $this->insertJuchudenpyoMsiSQL($juchuDenpyoMsi);
//                Msi_Sys_Utils::profilerMark('afInSql');
                } else {
                    // 受注伝票明細テーブル更新情報を設定する
//                Msi_Sys_Utils::profilerMark('beUp1Sql');
                    $DenpyoMsiRow['upsert'] = 2; // 更新
                    $juchuDenpyoMsi = $this->setJuchudenpyoMsiUpdateInfo($dataApp, $DenpyoMsiRow);
                    // 条件部
                    $where1['denpyo_no'] = $juchuDenpyoNo;  // 伝票番号
                    $where1['seko_no'] = $this->_sekoNo;  // 施行番号
                    $where1['seko_no_sub'] = $this->_sekoNoSub;  // 施行番号枝番
                    $where1["msi_no"] = $DenpyoMsiRow['msi_no'];  // 受注明細№
                    $where1['delete_flg'] = 0;
//                Msi_Sys_Utils::profilerMark('beUp2Sql');
                    list($sql, $param) = $this->updateJuchudenpyoMsiSQL($juchuDenpyoMsi, $where1);
//                Msi_Sys_Utils::profilerMark('afUpSql');
                }
                $cnt += $db->easyExecute($sql, $param);
            }
//            Msi_Sys_Utils::profilerMark('5');
        }

        // 施行基本情報を設定する
        $this->setInitParam();
        // 法友会相殺金額特殊処理
        App_Utils2::adjJuchu($db, $this->_sekoNo, $juchuDenpyoNo, $this->_juchuKakuteiYMD, $this->_gojokaiKbn, $this->_sekoPlanCd, $dataCol, $dataApp, $dataTrnDelCol, $delete_cds);

        // 受注伝票データがない場合、登録する
        // 受注伝票データがある場合、更新する
        if ($isAutoSaiban) {
            // 受注伝票テーブル情報を設定する
            $juchuDenpyo = $this->setJuchudenpyoInsertInfo($dataApp);
            // 受注伝票登録SQLを取得する
            list($sql, $param) = $this->insertJuchudenpyoSQL($juchuDenpyo);
        } else {
            // 受注伝票テーブル情報を設定する
            $juchuDenpyo = $this->setJuchudenpyoUpdateInfo($dataApp);
            // 受注伝票更新SQLを取得する
            // 条件部
            $where2['denpyo_no'] = $juchuDenpyoNo;  // 伝票番号
            $where2['seko_no'] = $this->_sekoNo;  // 施行番号
            $where2['seko_no_sub'] = $this->_sekoNoSub;  // 施行番号枝番
            $where2['delete_flg'] = 0;
            list($sql, $param) = $this->updateJuchudenpyoSQL($juchuDenpyo, $where2);
        }
        $cnt += $db->easyExecute($sql, $param);
        // 受注伝票の明細とヘッダーの消費税差額設定処理
        $cnt += $this->setJuchuDenpyoAdjTax($juchuDenpyoNo);
        return $cnt;
    }

    /**
     *
     * 受注伝票番号を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @version 2014/07/06 データ区分を参照するように修正　Kayo
     * @return string 受注伝票番号
     */
    protected function getJuchudenpyoNo() {
        $juchuDenpyoNo = '';
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            denpyo_no
        FROM
            juchu_denpyo
        WHERE
            seko_no = :seko_no
        AND delete_flg	= 0
        AND seko_no_sub = :seko_no_sub
	AND data_kbn IN (1, 2) --葬儀と法事
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'seko_no_sub' => $this->_sekoNoSub));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $juchuDenpyoNo = $select['denpyo_no'];
        }
        return $juchuDenpyoNo;
    }

    /**
     *
     * 受注伝票明細削除SQL
     *
     * <AUTHOR> Sai
     * @since 2014/2/5
     * @return string SQL
     */
    private function deleteJuchudenpyoMsiSQL() {
        // 削除処理は論理削除(deleteflgを更新)する
        $sql = "
        UPDATE
            juchu_denpyo_msi
        SET 
            delete_flg = 1
            ,disp_no = -1
        WHERE
            denpyo_no = :denpyo_no
        AND msi_no = :msi_no
        AND delete_flg = 0
                ";
        return $sql;
    }

    /**
     *
     * 受注伝票明細取得SQL
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @return string SQL
     */
    protected function selectJuchudenpyoMsiSQL() {
        // 受注伝票取得SQL
        $sql = "
        SELECT
            denpyo_no
        FROM
            juchu_denpyo_msi
        WHERE
            denpyo_no = :denpyo_no
        AND msi_no = :msi_no
        --AND delete_flg = 0
                ";
        return $sql;
    }

    /**
     *
     * 受注伝票明細テーブル登録情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @return array 受注伝票明細情報
     */
    private function setJuchudenpyoMsiInstertInfo($dataApp, $record) {
        // 受注伝票明細テーブル登録時と更新時の共通情報を設定する
        $juchuDenpyoMsi = $this->setJuchudenpyoMsiComInfo($dataApp, $record);
        $juchuDenpyoMsi['denpyo_no'] = $dataApp['denpyo_no'];  // 受注伝票
        $juchuDenpyoMsi['msi_no'] = $record['msi_no'];  // 受注明細№
        // select時にdata_sbtが設定された場合は(式場商品・通夜会場商品の特殊処理用)、それを使う、
        // それ以外はオーバーライド関数を呼び出す
        if (isset($record['data_sbt'])) {
            $juchuDenpyoMsi['data_sbt'] = $record['data_sbt'];  // データ種別
        } else {
            // $recordパラメーターは見積書呼び出す時に使用する
            $juchuDenpyoMsi['data_sbt'] = $this->getDataSbt($record);  // データ種別
        }
        if (isset($record['add_kbn'])) {
            $juchuDenpyoMsi['add_kbn'] = $record['add_kbn'];  // 追加区分
        }
        $juchuDenpyoMsi['seko_no'] = $this->_sekoNo;  // 施行番号
        $juchuDenpyoMsi['seko_no_sub'] = $this->_sekoNoSub;  // 施行番号枝番
        $juchuDenpyoMsi['nafuda_nm'] = null;  // 名札
        $juchuDenpyoMsi['msi_biko1'] = null;  // 明細備考1
        $juchuDenpyoMsi['msi_biko2'] = null;  // 明細備考2
        return $juchuDenpyoMsi;
    }

    /**
     *
     * 受注伝票明細テーブル更新情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @return array 受注伝票明細情報
     */
    private function setJuchudenpyoMsiUpdateInfo($dataApp, $record) {
        // 受注伝票明細テーブル登録時と更新時の共通情報を設定する
        $juchuDenpyoMsi = $this->setJuchudenpyoMsiComInfo($dataApp, $record);
        return $juchuDenpyoMsi;
    }

    /**
     *
     * 受注伝票明細テーブル共通情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/2/5
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @return array 受注伝票明細情報
     */
    protected function setJuchudenpyoMsiComInfo($dataApp, $record) {
        $juchuDenpyoMsi = array();
//        $db = Msi_Sys_DbManager::getMyDb();
        // emptyToNull
        $record = Msi_Sys_Utils::emptyToNullArr($record);

        Msi_Sys_Utils::profilerMark('setJuchudenpyoMsiComInfo-start');
        $juchuDenpyoMsi['disp_no'] = $record['disp_no'];  // 表示順
        $juchuDenpyoMsi['denpyo_kbn'] = $record['denpyo_kbn'];  // 伝票区分
        $juchuDenpyoMsi['juchu_ymd'] = $this->_zeiKijunYmd;  // 受注日
        $juchuDenpyoMsi['bumon_cd'] = $record['bumon_cd'];  // 売上部門コード
        $juchuDenpyoMsi['mokuteki_kbn'] = $record['mokuteki_kbn'];  // 使用目的区分
        $juchuDenpyoMsi['dai_bunrui_cd'] = $record['dai_bunrui_cd'];  // 大分類コード
        $juchuDenpyoMsi['chu_bunrui_cd'] = $record['chu_bunrui_cd'];  // 中分類コード
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy(); // 2016/11/28 ADD Kayo
        $juchuDenpyoMsi['kaisya_cd'] = $kaisya_cd;   // 会社コード 2016/11/28 ADD Kayo
        $juchuDenpyoMsi['shohin_bumon_cd'] = $record['shohin_bumon_cd'];
        $juchuDenpyoMsi['shohin_kbn'] = $record['shohin_kbn'];  // 商品区分
        $juchuDenpyoMsi['shohin_cd'] = $record['shohin_cd'];  // 商品コード
        $juchuDenpyoMsi['shohin_nm'] = $record['shohin_nm'];  // 商品名
        $juchuDenpyoMsi['shohin_tkiyo_nm'] = $record['shohin_tkiyo_nm'];  // 商品摘要名
        if (isset($record['shohin_tkiyo_nm2'])) {
            $juchuDenpyoMsi['shohin_tkiyo_nm2'] = $record['shohin_tkiyo_nm2'];  // 商品摘要名2 mihara 2015/11/02
        }
        $juchuDenpyoMsi['juchu_suryo'] = $record['juchu_suryo'];  // 商品数量
        $juchuDenpyoMsi['tani_cd'] = $record['tani_cd'];  // 単位コード
        $juchuDenpyoMsi['juchu_tnk'] = $record['juchu_tnk'];  // 単価
        $juchuDenpyoMsi['juchu_prc'] = $this->getPrc($record); // 受注金額
        if (!isset($record['nebiki_prc_update_flg'])) { // 更新時は値引き額を更新しない 佐野(フラグはApp_Utils2::adjGojokaiNebiki3で設定)
            $juchuDenpyoMsi['nebiki_prc'] = $record['nebiki_prc'];  // 値引額
        }
        if (isset($record['gojokai_nebiki_prc'])) {
            $juchuDenpyoMsi['gojokai_nebiki_prc'] = $record['gojokai_nebiki_prc'];  // 互助会値引き
        } else {
            $juchuDenpyoMsi['gojokai_nebiki_prc'] = 0;  // 互助会値引き
        }
        $record['gojokai_nebiki_prc'] = $juchuDenpyoMsi['gojokai_nebiki_prc'];
        $juchuDenpyoMsi['gen_tnk'] = $record['gen_tnk'];  // 原価単価
        $juchuDenpyoMsi['gen_gaku'] = $this->getGenka($record);  // 原価金額
        $juchuDenpyoMsi['arari_gaku'] = $this->getAari($record); // 粗利益額
        $juchuDenpyoMsi['hoshi_umu_kbn'] = $record['hoshi_umu_kbn'];  // 奉仕料有無区分
        $juchuDenpyoMsi['hoshi_ritu_cd'] = $dataApp['hoshi_ritu_cd'];  // 奉仕料率コード
        $hoshiTaisho = App_Utils2::getHoshiTaisho($juchuDenpyoMsi); //奉仕料を出すための受注金額（佐野が異なるため）
        // 奉仕料を計算する	2014/05/18 ADD Kay
        $hoshiprc = App_ClsTaxLib::CalcHosiPrc($hoshiTaisho, $dataApp["hasu_kbn"], $dataApp["hoshi_ritu"], $juchuDenpyoMsi['hoshi_umu_kbn']);
        $juchuDenpyoMsi['hoshi_prc'] = $hoshiprc;   // 奉仕料金額	2014/05/18 UPD Kayo
        $juchuDenpyoMsi['nonyu_cd'] = $record['nonyu_cd'];  // 納入先コード
        $juchuDenpyoMsi['nonyu_nm'] = $record['nonyu_nm'];  // 納入先名
        $juchuDenpyoMsi['nonyu_knm'] = $record['nonyu_knm'];  // 納入先名カナ
        $juchuDenpyoMsi['nonyu_yubin_no'] = $record['nonyu_yubin_no'];  // 納入先郵便番号
        $juchuDenpyoMsi['nonyu_addr1'] = $record['nonyu_addr1'];  // 納入先住所1
        $juchuDenpyoMsi['nonyu_addr2'] = $record['nonyu_addr2'];  // 納入先住所2
        $juchuDenpyoMsi['nonyu_tel'] = $record['nonyu_tel'];  // 納入先電話番号
        $juchuDenpyoMsi['nonyu_fax'] = $record['nonyu_fax'];  // 納入先FAX
        $juchuDenpyoMsi['nonyu_dt'] = $record['nonyu_dt'];  // 納入予定日
        $juchuDenpyoMsi['siire_cd'] = $record['siire_cd'];  // 仕入先コード
        $juchuDenpyoMsi['siire_lnm'] = $record['siire_lnm'];  // 仕入先名
        $juchuDenpyoMsi['uri_kamoku_cd'] = $record['uri_kamoku_cd'];  // 科目コード
        if (isset($record['k_free4'])) {
            $juchuDenpyoMsi['k_free4'] = $record['k_free4']; // OM引き継ぎ商品
        }
        // 消費税項目を設定する
        $this->setZeiInfo($dataApp, $juchuDenpyoMsi, $record, $juchuDenpyoMsi['juchu_prc'] + $hoshiprc + $record['nebiki_prc'] + $record['gojokai_nebiki_prc']); // mitsu,seikyu,juhen 3箇所
        if (isset($record['select_shohin_cd'])) {
            $juchuDenpyoMsi['select_shohin_cd'] = $record['select_shohin_cd']; // 選択商品コード
        }
        if (isset($record['refer_uchiwk_no'])) {
            $juchuDenpyoMsi['refer_uchiwk_no'] = $record['refer_uchiwk_no']; // 参照先明細№
        }
        if (isset($record['add_henpin_ymd'])) {
            $juchuDenpyoMsi['add_henpin_ymd'] = $record['add_henpin_ymd'];
        }
        if (isset($record['upgrade_kbn'])) {
            $juchuDenpyoMsi['upgrade_kbn'] = $record['upgrade_kbn'];
        }
        if (isset($record['plan_shohin_cd'])) {
            $juchuDenpyoMsi['plan_shohin_cd'] = $record['plan_shohin_cd'];
        }
        if (isset($record['plan_shohin_nm'])) {
            $juchuDenpyoMsi['plan_shohin_nm'] = $record['plan_shohin_nm'];
        }
        if (isset($record['plan_shohin_bumon_cd'])) {
            $juchuDenpyoMsi['plan_shohin_bumon_cd'] = $record['plan_shohin_bumon_cd'];
        }
        if (isset($record['plan_shohin_suryo'])) {
            $juchuDenpyoMsi['plan_shohin_suryo'] = $record['plan_shohin_suryo'];
        }
        if (isset($record['plan_shohin_tnk'])) {
            $juchuDenpyoMsi['plan_shohin_tnk'] = $record['plan_shohin_tnk'];
        }
        // 受注金額が0円かつ付帯特典、割引額がいずれも0円の場合はNULL
        if ($juchuDenpyoMsi['juchu_prc'] == 0 && $juchuDenpyoMsi['nebiki_prc'] == 0 && $juchuDenpyoMsi['gojokai_nebiki_prc'] == 0) {
            $juchuDenpyoMsi['n_free2'] = null;
        } else {
            if (isset($record['n_free2'])) {
                $juchuDenpyoMsi['n_free2'] = $record['n_free2'];
            } else {
                $juchuDenpyoMsi['n_free2'] = null;
            }
        }

        Msi_Sys_Utils::profilerMark('setJuchudenpyoMsiComInfo-end');
        return $juchuDenpyoMsi;
    }

    /**
     *
     * 受注伝票テーブル登録情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面の情報
     * @return array 受注伝票情報
     */
    private function setJuchudenpyoInsertInfo($dataApp) {
        $juchuDenpyo = $this->setJuchudenpyoComInfo($dataApp);
        $juchuDenpyo["denpyo_no"] = $dataApp['denpyo_no'];  // 受注伝票№
        $juchuDenpyo["seko_no"] = $this->_sekoNo;  // 施行番号
        $juchuDenpyo["seko_no_sub"] = $this->_sekoNoSub;  // 施行番号枝番
        $juchuDenpyo["nonyu_cd"] = null;  // 納入先コード
        $juchuDenpyo["nonyu_nm"] = null;  // 納入先名
        $juchuDenpyo["nonyu_knm"] = null;  // 納入先名カナ
        $juchuDenpyo["nonyu_yubin_no"] = null;  // 納入先郵便番号
        $juchuDenpyo["nonyu_addr1"] = null;  // 納入先住所1
        $juchuDenpyo["nonyu_addr2"] = null;  // 納入先住所2
        $juchuDenpyo["nonyu_tel"] = null;  // 納入先電話番号
        $juchuDenpyo["nonyu_fax"] = null;  // 納入先FAX
        $juchuDenpyo["nonyu_dt"] = null;  // 納入予定日
        $juchuDenpyo["denpyo_biko1"] = null;  // 伝票備考１
        $juchuDenpyo["denpyo_biko2"] = null;  // 伝票備考２
        return $juchuDenpyo;
    }

    /**
     *
     * 受注伝票テーブル更新情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @param array $dataApp 画面の情報
     * @return array 受注伝票情報
     */
    private function setJuchudenpyoUpdateInfo($dataApp) {
        $juchuDenpyo = $this->setJuchudenpyoComInfo($dataApp);
        return $juchuDenpyo;
    }

    /**
     *
     * 受注伝票テーブル共通情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/2/26
     * @version 2014/09/15 消費税端数区分を追加 Kayo
     * @param array $dataApp 画面の情報
     * @return array 受注伝票情報
     */
    private function setJuchudenpyoComInfo($dataApp) {
        $juchuDenpyo = array();
        $db = Msi_Sys_DbManager::getMyDb();
        Msi_Sys_Utils::profilerMark('setJuchudenpyoComInfo-start');
        $juchuDenpyo["data_kbn"] = $this->getDataKbn();  // データ区分
        $juchuDenpyo["juchu_ymd"] = $this->_zeiKijunYmd;  // 受注日
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();      // 2016/11/28 ADD Kayo
        $juchuDenpyo['kaisya_cd'] = $kaisya_cd;           // 会社コード 2016/11/28 ADD Kayo
        $juchuDenpyo["bumon_cd"] = $this->_bumonCd;  // 売上部門コード
        $juchuDenpyo["tanto_cd"] = $this->_sekoTantoCd;  // 担当者コード
        $juchuDenpyo["gojokai_cose_cd"] = $this->_gojokaiCoseCd;  // 互助会コースコード
        $juchuDenpyo["seko_plan_cd"] = $this->_sekoPlanCd;  // 施行プランコード
        // 回収区分と回収予定日を取得する
        $kaishuInfo = $this->getKashuInfo();
        $juchuDenpyo["kaishu_kbn"] = $kaishuInfo["kaishu_kbn"];  // 回収区分 
//        $juchuDenpyo["kaishu_ymd"] = $kaishuInfo["kaishu_ymd"];  // 回収予定日
        // 金額合計求め処理 
        $juchuPrcSum = $this->getJuchuPrcSum($dataApp['denpyo_no']);
        $juchuDenpyo["juchu_prc_sum"] = $juchuPrcSum['juchu_prc_sum'];  // 受注金額合計
        $juchuDenpyo["genka_prc_sum"] = $juchuPrcSum['genka_prc_sum'];  // 原価金額合計
        $juchuDenpyo["juchu_hepn_sum"] = $juchuPrcSum['juchu_hepn_sum'];  // 受注返品合計
        $juchuDenpyo["juchu_nebk_sum"] = $juchuPrcSum['juchu_nebk_sum'];  // 受注値引合計
        $juchuDenpyo["hoshi_prc_sum"] = $juchuPrcSum['hoshi_prc_sum'];  // 奉仕料合計
        $juchuDenpyo["arari_prc"] = $juchuPrcSum['arari_prc_sum'];  // 粗利益額
        // 消費税処理 new 2019/04/30 mihara keigen
        $juchuPrcSum2 = $this->getJuchuZeiSum2($dataApp['denpyo_no']);
        $juchuDenpyo["in_zei_prc"] = $juchuPrcSum2['in_zei_prc']; // 内税消費税額
        $juchuDenpyo["out_zei_prc"] = $juchuPrcSum2['out_zei_prc']; // 外税消費税額
        $juchuDenpyo["szei_katax_taisho_prc"] = $juchuPrcSum2["szei_katax_taisho_prc"];  // 外税課税対象額
        $juchuDenpyo["uzei_katax_taisho_prc"] = $juchuPrcSum2["uzei_katax_taisho_prc"];  // 内税課税対象額
        $juchuDenpyo["hitax_katax_taisho_prc"] = $juchuPrcSum2["hitax_katax_taisho_prc"];  // 非税課税対象額
        // 請求先情報を取得する
        $selectSekyuInfo = App_Utils::getSekoSekyuInfo($this->_sekoNo);
        if (Msi_Sys_Utils::myCount($selectSekyuInfo) > 0) {
            $juchuDenpyo["sekyu_cd"] = $selectSekyuInfo["sekyu_cd"];  // 請求先コード
            $juchuDenpyo["sekyu_nm"] = $selectSekyuInfo["sekyu_nm"];  // 請求先名
            $juchuDenpyo["sekyu_knm"] = $selectSekyuInfo["sekyu_knm"];  // 請求先名カナ
            $juchuDenpyo["sekyu_soufu_nm"] = $selectSekyuInfo["sekyu_nm"];  // 請求書送付先名
            $juchuDenpyo["sekyu_yubin_no"] = $selectSekyuInfo["yubin_no"];  // 請求先郵便番号
            $juchuDenpyo["sekyu_addr1"] = $selectSekyuInfo["addr1"];  // 請求先住所1
            $juchuDenpyo["sekyu_addr2"] = $selectSekyuInfo["addr2"];  // 請求先住所2
            $juchuDenpyo["sekyu_tel"] = $selectSekyuInfo["tel"];  // 請求先電話番号
            $juchuDenpyo["sekyu_fax"] = $selectSekyuInfo["fax"];  // 請求先FAX
        }
        // プラン情報を設定
        $selectSekoInfo = DataMapper_SekoKihonInfo::find($db, array('seko_no' => $this->_sekoNo));
        $juchuDenpyo["main_pt_cd"] = $this->_mainPtCd;  // 基本パターンコード
        $juchuDenpyo["main_pt_kbn"] = Msi_Sys_Utils::emptyToNull($this->_mainPtKbn);  // 基本パターン区分
        $juchuDenpyo["seko_kbn"] = $juchuDenpyo["data_kbn"];  // 施行区分
        $juchuDenpyo["saidan_sbt"] = Msi_Sys_Utils::emptyToNull($selectSekoInfo[0]["saidan_sbt"]);  // 祭壇種別
        $juchuDenpyo["syushi_kbn"] = Msi_Sys_Utils::emptyToNull($selectSekoInfo[0]["plan_syushi_kbn"]);  // 宗旨区分
        $juchuDenpyo["gojokai_kbn"] = Msi_Sys_Utils::emptyToNull($this->_gojokaiKbn);  // 互助会区分
        $juchuDenpyo["v_free3"] = $this->_kaiinKbn;  // 会員コード
        $juchuDenpyo["plan_shikijo_cd"] = $selectSekoInfo[0]["plan_shikijo_cd"];  // プラン用式場コード
        if (isset($this->_estShikijoCd)) {
            $juchuDenpyo["est_shikijo_cd"] = $this->_estShikijoCd;
        }
        Msi_Sys_Utils::profilerMark('setJuchudenpyoComInfo-end');
        return $juchuDenpyo;
    }

    /**
     *
     * 受注伝票登録SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @param array $juchuDenpyo 受注伝票テーブル情報
     * @return string SQL
     */
    private function insertJuchudenpyoSQL($juchuDenpyo) {
        list($sql, $param) = DataMapper_Utils::makeInsertSQL("juchu_denpyo", $juchuDenpyo);
        return array($sql, $param);
    }

    /**
     *
     * 受注伝票更新SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/1/29
     * @param array $juchuDenpyo 受注伝票テーブル情報
     * @param array $dataApp 受注伝票画面情報
     * @param array $where 条件
     * @return string SQL
     */
    private function updateJuchudenpyoSQL($juchuDenpyo, $where) {
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo", $juchuDenpyo, $where);
        return array($sql, $param);
    }

    /**
     *
     * 受注伝票明細登録SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/1/30
     * @param array $DenpyoMsi 受注伝票明細テーブル情報
     * @return string SQL
     */
    private function insertJuchudenpyoMsiSQL($DenpyoMsi) {
        list($sql, $param) = DataMapper_Utils::makeInsertSQL("juchu_denpyo_msi", $DenpyoMsi);
        return array($sql, $param);
    }

    /**
     *
     * 受注伝票明細更新SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/1/30
     * @param array $DenpyoMsi 受注伝票明細テーブル情報
     * @param array $where 条件部
     * @return string SQL
     */
    private function updateJuchudenpyoMsiSQL($DenpyoMsi, $where) {
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo_msi", $DenpyoMsi, $where);
        return array($sql, $param);
    }

    /**
     *
     * 回収区分と回収予定日を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @return array 回収区分と回収予定日
     */
    protected function getKashuInfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $kaishuInfo["kaishu_kbn"] = 0;  // MYTODO 保留 回収区分 
        $kaisya = DataMapper_KaisyaInfo::find($db);
        if (count($kaisya) <= 0) {
            $addDay = 0;
        } else {
            $addDay = $kaisya[0]['maturity_day'];
        }
        $kaishu_date = new DateTime($this->_zeiKijunYmd);
        $kaishuInfo["kaishu_ymd"] = $kaishu_date->modify('+' . $addDay . ' days')->format('Y/m/d');  // 回収予定日
        return $kaishuInfo;
    }

    /**
     *
     * 受注・売上伝票明細の受注・売上金額を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/1
     * @param array $record 受注・売上伝票明細
     * @return float 受注・売上金額
     */
    protected function getPrc($record) {
        // 受注・売上金額受注単価 * 数量
        return $record['juchu_tnk'] * $record['juchu_suryo'];
    }

    /**
     *
     * 受注・売上伝票明細の受注・売上原価金額を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/1
     * @param array $record 受注・売上伝票明細
     * @return float 受注・売上原価金額
     */
    protected function getGenka($record) {
        // 原価金額
        return $record['gen_tnk'] * $record['juchu_suryo'];
    }

    /**
     *
     * 受注・売上伝票明細の受注・売上粗利を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/1
     * @param array $record 受注・売上伝票明細
     * @return float 受注・売上粗利
     */
    protected function getAari($record) {
        if (!isset($record['gojokai_nebiki_prc'])) {
            $record['gojokai_nebiki_prc'] = 0;
        }
        // 粗利益額:(受注単価 - 値引額 - 原価単価) * 数量  + 奉仕料
        return $juchuDenpyoMsi['arari_gaku'] = ($record['juchu_tnk'] - $record['gen_tnk']) * $record['juchu_suryo'] + $record['hoshi_prc'] + $record['nebiki_prc'] + $record['gojokai_nebiki_prc'];
    }

    /**
     *
     * 受注・売上伝票明細の消費税項目を設定する
     *   軽減税率対応 keigen mihara
     *
     * <AUTHOR> Sai
     * @since 2014/4/1
     * @version 2019/04/30 mihara 軽減税率対応
     * @version 2014/09/15 消費税の端数区分を追加
     * @param array $dataApp appデータ
     * @param array &$denpyoMsi 受注・売上伝票明細更新対象
     * @param array $record 受注・売上伝票明細
     * @param float $prc 受注・売上金額
     */
    protected function setZeiInfo($dataApp, &$denpyoMsi, $record, $prc) {

        $zeiKbn = (int) $record['zei_kbn'];
        $denpyoMsi['zei_kbn'] = $zeiKbn;  // 消費税区分
        // 消費税処理
        if (!array_key_exists('zei_cd', $record)) { // XXX for dev   keigen mihara XXX DELME
            Msi_Sys_Utils::_debug('!! setZeiInfo calling not setting zei_cd !!' . Msi_Sys_Utils::dump($denpyoMsi));
            if (array_key_exists('zei_cd', $denpyoMsi)) {
                $record['zei_cd'] = $denpyoMsi['zei_cd'];
            } else {
                throw new Exception('!! setZeiInfo calling not setting zei_cd !!');
            }
        }
        $denpyoMsi["zei_cd"] = (int) $record['zei_cd']; // keigen  mihara  $dataApp["zei_cd"];  // 消費税コード
        list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($denpyoMsi["zei_cd"]);
        $zei = App_ClsTaxLib::CalcTax($prc, $zeiKbn, $zei_rtu, $zei_hasu_kbn); // $dataApp["zei_rtu"], $dataApp["zei_hasu_kbn"]);
        if ($zeiKbn === 1) {
            $denpyoMsi["in_zei_prc"] = $zei["ZeiPrc"];  // 行内税消費税額
            $denpyoMsi['out_zei_prc'] = 0;    // 行外税消費税額
        } else if ($zeiKbn === 2) {
            $denpyoMsi['in_zei_prc'] = 0;     // 行内税消費税額
            $denpyoMsi["out_zei_prc"] = $zei["ZeiPrc"];  // 行外税消費税額
        } else {
            $denpyoMsi['in_zei_prc'] = 0;     // 行内税消費税額 2016/04/23 ADD Kayo
            $denpyoMsi["out_zei_prc"] = 0;    // 行外税消費税額 2016/04/23 ADD Kayo
        }
        $denpyoMsi['reduced_tax_rate'] = $reduced_tax_rate;
    }

    /**
     *
     * 受注・売上伝票明細の消費税項目を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/4/1
     * @version 2014/09/15 消費税の端数区分を追加
     * @param array $dataApp appデータ
     * @param array &$denpyoMsi 受注・売上伝票明細更新対象
     * @param array $record 受注・売上伝票明細
     * @param float $prc 受注・売上金額
     */
    protected function OLD_setZeiInfo($dataApp, &$denpyoMsi, $record, $prc) {

        $zeiKbn = (int) $record['zei_kbn'];
        $denpyoMsi['zei_kbn'] = $zeiKbn;  // 消費税区分
        // 消費税処理
        $denpyoMsi["zei_cd"] = $dataApp["zei_cd"];  // 消費税コード
        $zei = App_ClsTaxLib::CalcTax($prc, $zeiKbn, $dataApp["zei_rtu"], $dataApp["zei_hasu_kbn"]);
        if ($zeiKbn === 1) {
            $denpyoMsi["in_zei_prc"] = $zei["ZeiPrc"];  // 行内税消費税額
            $denpyoMsi['out_zei_prc'] = 0;    // 行外税消費税額
        } else if ($zeiKbn === 2) {
            $denpyoMsi['in_zei_prc'] = 0;     // 行内税消費税額
            $denpyoMsi["out_zei_prc"] = $zei["ZeiPrc"];  // 行外税消費税額
        } else {
            $denpyoMsi['in_zei_prc'] = 0;     // 行内税消費税額 2016/04/23 ADD Kayo
            $denpyoMsi["out_zei_prc"] = 0;    // 行外税消費税額 2016/04/23 ADD Kayo
        }
    }

    /**
     *
     * 売上伝票番号を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @return string 売上伝票番号
     */
    protected function getUriagedenpyoNo() {
        $uriageDenpyoNo = '';
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            uri_den_no
        FROM
            uriage_denpyo
        WHERE
            seko_no = :seko_no
        AND delete_flg = 0
        AND seko_no_sub = :seko_no_sub
	AND data_kbn IN (1, 2) -- 葬儀と法事
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'seko_no_sub' => $this->_sekoNoSub));
        if (isset($select) && count($select) > 0) {
            $uriageDenpyoNo = $select['uri_den_no'];
        }
        return $uriageDenpyoNo;
    }

    /**
     *
     * 請求伝票番号を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2020/08/17
     * @return string 請求伝票番号
     */
    protected function getSeikyudenpyoNo() {
        $seikyuDenpyoNo = '';
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            seikyu_den_no
        FROM
            seikyu_denpyo
        WHERE
            seko_no = :seko_no
        AND delete_flg = 0
        AND seko_no_sub = :seko_no_sub
	AND data_kbn IN (1, 2) -- 葬儀と法事
	AND bun_gas_kbn_num IN (0, 1) -- 通常と分割元
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'seko_no_sub' => $this->_sekoNoSub));
        if (isset($select) && count($select) > 0) {
            $seikyuDenpyoNo = $select['seikyu_den_no'];
        }
        return $seikyuDenpyoNo;
    }

    /**
     * 施行発注管理情報保存処理
     *
     * <AUTHOR> Sai
     * @since 2014/03/18
     * @version 2015/12/17 花輪特典などの商品は摘要を再度更新する Kayo
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面Appデータ
     * @param array $dataCol グリッドデータ
     * @param array $dataTrnDelCol グリッドトラン削除データ
     * @return int 更新件数
     */
    public function saveHachuInfo($db, $dataApp, $dataCol, $dataTrnDelCol = array()) {
        $cnt = 0;
        // 発注管理特殊処理
        App_Utils2::adjHachuinfo($db, $this->_sekoNo, $this->_juchuKakuteiYMD, $dataCol);
        // レポート管理マスタを取得する
        $reportKanri = $this->getReportKanri($db);
        // 施行発注管理情報削除データ処理を行う
        $sqlDeleteHachuInfo = $this->deleteHachuInfoSQL();
        foreach ($dataTrnDelCol as $DenpyoMsiDelRow) {
            if (isset($DenpyoMsiDelRow["msi_no"])) {
                $cnt += $db->easyExecute($sqlDeleteHachuInfo, array('seko_no' => $this->_sekoNo, 'msi_no' => $DenpyoMsiDelRow["msi_no"], 'data_kbn' => $this->getDataKbn()));
            }
        }

        // 最大発注管理明細№を取得する
        $maxHachuNo = $this->getMaxHachuMsiNo($this->_sekoNo);
        // 施行発注管理情報がない場合、登録する
        // 施行発注管理情報がある場合、更新する
        foreach ($dataCol as $DenpyoMsiRow) {
            // 数量0は対象外(削除処理をしておく)
            if ($DenpyoMsiRow['juchu_suryo'] == '0') {
                if (isset($DenpyoMsiRow["msi_no"])) {
                    $cnt += $db->easyExecute($sqlDeleteHachuInfo, array('seko_no' => $this->_sekoNo, 'msi_no' => $DenpyoMsiRow["msi_no"], 'data_kbn' => $this->getDataKbn()));
                }
                continue;
            }
            // 発注書区分なしは対象外
            // 施行発注管理情報存在チェック
            $sqlHachuInfo = $this->selectHachuInfoSQL();
            $selectHachuInfo = $db->easySelOne($sqlHachuInfo, array('seko_no' => $this->_sekoNo, 'msi_no' => $DenpyoMsiRow["msi_no"], 'data_kbn' => $this->getDataKbn()));
            if (isset($DenpyoMsiRow['hachu_kbn']) && $DenpyoMsiRow['hachu_kbn'] !== '0') {
                // 商品の発注区分に該当するレポート管理マスタを取得する
                $reportKanriOne = $this->getReportKanriOne($DenpyoMsiRow['hachu_kbn'], $reportKanri);
                if (count($reportKanriOne) > 0) {
                    if (!isset($selectHachuInfo) || count($selectHachuInfo) === 0) {
                        // 施行発注管理情報登録情報を設定する
                        $hachuInfo = $this->setHachuInstertInfo($dataApp, $DenpyoMsiRow, $reportKanriOne);
                        $hachuInfo['hachu_no'] = ++$maxHachuNo;  // 発注管理番号
                        //$sql = $this->makeInsertSQL("seko_hachu_info", $hachuInfo);
                        list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_hachu_info", $hachuInfo);
                        $cnt += $db->easyExecute($sql, $param);
                    } else {
                        // 施行発注管理情報更新情報を設定する
                        $hachuInfo = $this->setHachuUpdateInfo($dataApp, $DenpyoMsiRow, $reportKanriOne);
                        // 発注済の場合、発注数量、発注単価、発注金額は更新対象外
                        if ($selectHachuInfo['order_flg'] === '1') {
                            unset($hachuInfo['hachu_suryo']);
                            unset($hachuInfo['hachu_tnk']);
                            unset($hachuInfo['hachu_prc']);
                        }
                        // 条件部
                        $where['seko_no'] = $this->_sekoNo;  // 施行番号
                        $where["hachu_no"] = $selectHachuInfo['hachu_no'];  // 発注明細№
                        $where['data_kbn'] = $this->getDataKbn();
                        $where['delete_flg'] = 0;
                        //$sql = $this->makeUpdateSQL("seko_hachu_info", $hachuInfo, $where);
                        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_hachu_info", $hachuInfo, $where);
                        $cnt += $db->easyExecute($sql, $param);
                        // 花輪特典などの商品は摘要を再度更新する   2015/12/17 ADD Kayo
                        $select_hanawa = $db->easySelect($sqlHachuInfo, array('seko_no' => $this->_sekoNo
                            , 'msi_no' => $DenpyoMsiRow["msi_no"]
                            , 'data_kbn' => $this->getDataKbn()));
                        if (count($select_hanawa) > 1) {
                            $hacyu_hanawa = array();
                            $hacyu_hanawa['shohin_tkiyo_nm'] = $DenpyoMsiRow['shohin_tkiyo_nm'];  // 商品摘要名
                            // 条件部
                            $where_hanawa['seko_no'] = $this->_sekoNo;               // 施行番号
                            $where_hanawa["jc_msi_no"] = $DenpyoMsiRow['msi_no'];      // 発注明細№
                            $where_hanawa['data_kbn'] = $this->getDataKbn();          // データ区分
                            $where_hanawa['delete_flg'] = 0;
                            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_hachu_info", $hacyu_hanawa, $where_hanawa);
                            $cnt += $db->easyExecute($sql, $param);
                        }
                    }
                }
            } else {
                if (Msi_Sys_Utils::myCount($selectHachuInfo) > 0) {
                    $cnt += $db->easyExecute($sqlDeleteHachuInfo, array('seko_no' => $this->_sekoNo, 'msi_no' => $DenpyoMsiRow["msi_no"], 'data_kbn' => $this->getDataKbn()));
                }
            }
        }
        return $cnt;
    }

    /**
     *
     * レポート管理マスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/17
     * @return array レポート管理マスタ
     */
    private function getReportKanri($db) {

        $reportKanriMst = array();
        $sql = "
        SELECT
            rkm.report_cd       -- 帳票コード
            ,rkm.ha_rp_cd       -- 発注書区分コード
            ,rkm.ha_rp_kbn      -- 発注書区分
            ,rkm.ha_entry_kbn   -- 発注書入力区分
            ,rkm.ha_syori_kbn   -- 発注書処理区分
        FROM
            report_kanri_mst rkm
        WHERE
            CURRENT_DATE BETWEEN rkm.tekiyo_st_date AND rkm.tekiyo_ed_date
        AND rkm.delete_flg = 0
        GROUP BY
	    rkm.report_cd
            ,rkm.ha_rp_cd       -- 発注書区分コード
            ,rkm.ha_rp_kbn      -- 発注書区分
            ,rkm.ha_entry_kbn   -- 発注書入力区分
            ,rkm.ha_syori_kbn   -- 発注書処理区分
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $oneRowData = array();
                $oneRowData['report_cd'] = $select[$i]['report_cd'];
                $oneRowData['ha_rp_cd'] = $select[$i]['ha_rp_cd'];
                $oneRowData['ha_rp_kbn'] = $select[$i]['ha_rp_kbn'];
                $oneRowData['ha_entry_kbn'] = $select[$i]['ha_entry_kbn'];
                $oneRowData['ha_syori_kbn'] = $select[$i]['ha_syori_kbn'];
                $reportKanriMst[$oneRowData['ha_rp_kbn']] = $oneRowData;
            }
        }
        return $reportKanriMst;
    }

    /**
     *
     * 施行発注管理情報削除SQL
     *
     * <AUTHOR> Sai
     * @since 2014/3/17
     * @return string SQL
     */
    private function deleteHachuInfoSQL() {
        // 削除処理は論理削除(deleteflgを更新)する
        $sql = "
        UPDATE
            seko_hachu_info
        SET 
            delete_flg = 1
        WHERE
            seko_no = :seko_no
        AND jc_msi_no = :msi_no
        AND data_kbn = :data_kbn
        AND delete_flg = 0
                ";
        return $sql;
    }

    /**
     *
     * 施行発注管理情報取得SQL
     *
     * <AUTHOR> Sai
     * @since 2014/3/17
     * @return string SQL
     */
    private function selectHachuInfoSQL() {
        // 施行発注管理情報取得SQL
        $sql = "
        SELECT
            seko_no
            ,hachu_no
            ,order_flg
        FROM
            seko_hachu_info
        WHERE
            seko_no = :seko_no
        AND jc_msi_no = :msi_no
        AND data_kbn = :data_kbn
        AND delete_flg = 0
                ";
        return $sql;
    }

    /**
     *
     * 施行発注管理登録情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/3/17
     * @version 2016/10/11 商品単価マスタから仕入率により仕入単価を求める Kayo
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)db
     * @param array $reportKanri レポート管理マスタ
     * @return array 施行発注管理情報
     */
    private function setHachuInstertInfo($dataApp, $record, $reportKanri) {
        // 施行発注管理登録時と更新時の共通情報を設定する
        $hachuInfo = $this->setHachuComInfo($dataApp, $record, $reportKanri);
        $hachuInfo['seko_no'] = $this->_sekoNo;  // 施行番号
        $hachuInfo['ha_rp_code_kbn'] = '0270';  // 発注書区分コード
        $hachuInfo['ha_etc_kbn'] = 0;  // 発注書その他区分 0：なし 
        $hachuInfo['hachu_den_kbn'] = 1;  // 発注伝票区分 1：通常
        $denpyo_no = $this->getJuchudenpyoNo();
        if (isset($denpyo_no)) {
            $hachuInfo['jc_denpyo_no'] = $denpyo_no;  // 受注伝票№
        }
        $hachuInfo['jc_msi_no'] = $record['msi_no'];  // 受注明細№
        $hachuInfo['order_flg'] = 0;  // 発注済み 0:未発注
        $hachuInfo['chk_order_flg'] = 0;  // 発注確認済み 0:未確認
        $hachuInfo['hacyu_cnt'] = 0;  // 発注回数
        $hachuInfo['status_kbn'] = 0;  // 発注ステータス 0:なし 
        $hachuInfo['copy_kbn'] = 0;  // コピー区分 0:なし（オリジナル） 
        $hachuInfo['hachu_suryo'] = $record['juchu_suryo'];  // 商品数量
        // 商品単価マスタから仕入率により仕入単価を求める 2016/10/11 ADD Kayo
        $retData = $this->getShohinTankaMst($record['shohin_cd'], $record['juchu_tnk'], $record['juchu_suryo']);
        if ($retData['siire_keisan_kbn'] == 1) {
            $hachuInfo['hachu_tnk'] = $retData['hachu_tnk'];  // 発注単価
            $hachuInfo['hachu_prc'] = $retData['hachu_prc'];  // 発注金額
        } else {
            $hachuInfo['hachu_prc'] = $record['gen_tnk'] * $record['juchu_suryo'];  // 発注金額
            $hachuInfo['hachu_tnk'] = $record['gen_tnk'];  // 発注単価
        }
        if (isset($record['auto_make_flg'])) {
            $hachuInfo['auto_make_flg'] = $record['auto_make_flg'];  // 自動作成フラグ  2016/06/29 ADD Kayo
        }
        if (!isset($hachuInfo['data_kbn'])) {
            $hachuInfo['data_kbn'] = $this->getDataKbn();
        }
        return $hachuInfo;
    }

    /**
     *
     * 施行発注管理更新情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/3/17
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @param array $reportKanri レポート管理マスタ
     * @return array 施行発注管理情報
     */
    private function setHachuUpdateInfo($dataApp, $record, $reportKanri) {
        // 受注伝票明細テーブル登録時と更新時の共通情報を設定する
        $hachuInfo = $this->setHachuComInfo($dataApp, $record, $reportKanri);
        return $hachuInfo;
    }

    /**
     *
     * 施行発注管理共通情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/3/17
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @param array $reportKanri レポート管理マスタ
     * @return array 施行発注管理情報
     */
    protected function setHachuComInfo($dataApp, $record, $reportKanri) {
        $hachuInfo = array();
        $hachuInfo['data_kbn'] = $this->getDataKbn();
        $hachuInfo['jc_denpyo_no'] = $dataApp['denpyo_no'];  // 受注伝票№   2014/10/16 ADD Kayo
        $hachuInfo['jc_msi_no'] = $record['msi_no'];  // 受注明細№  2014/10/16 ADD Kayo
        $hachuInfo['siire_cd'] = $record['siire_cd'];  // 仕入先コード
        $hachuInfo['report_cd'] = $reportKanri['report_cd'];  // 帳票コード
        $hachuInfo['ha_rp_cd'] = $reportKanri['ha_rp_cd'];  // 発注書区分コード
        $hachuInfo['ha_rp_kbn'] = $record['hachu_kbn'];  // 発注書区分
        $hachuInfo['ha_entry_kbn'] = $reportKanri['ha_entry_kbn'];  // 発注書入力区分
        $hachuInfo['ha_syori_kbn'] = $reportKanri['ha_syori_kbn'];  // 発注書処理区分
        $hachuInfo['dai_bunrui_cd'] = $record['dai_bunrui_cd'];  // 大分類コード
        $hachuInfo['chu_bunrui_cd'] = $record['chu_bunrui_cd'];  // 中分類コード
        $hachuInfo['shohin_kbn'] = $record['shohin_kbn'];  // 商品区分
        $hachuInfo['shohin_cd'] = $record['shohin_cd'];  // 商品コード
        $hachuInfo['shohin_nm'] = $record['shohin_nm'];  // 商品名
        $hachuInfo['shohin_tkiyo_nm'] = $record['shohin_tkiyo_nm'];  // 商品摘要名
        $hachuInfo['shohin_bumon_cd'] = $record['shohin_bumon_cd'];  // 商品部門コード
        if (isset($record['shohin_tkiyo_nm2'])) {
            $hachuInfo['shohin_tkiyo_nm2'] = $record['shohin_tkiyo_nm2'];  // 商品摘要名 2015/11/02 mihara
        }
        $hachuInfo['tani_cd'] = $record['tani_cd'];  // 単位コード
        $hachuInfo['hanbai_tnk'] = $record['juchu_tnk'];  // 販売単価
        $hachuInfo['hachu_suryo'] = $record['juchu_suryo'];  // 商品数量
        if ($hachuInfo['ha_rp_kbn'] != self::HA_RP_KBN_SEIKA) {
            // 商品単価マスタから仕入率により仕入単価を求める 2016/10/11 ADD Kayo
            $retData = $this->getShohinTankaMst($record['shohin_cd'], $record['juchu_tnk'], $record['juchu_suryo']);
            if ($retData['siire_keisan_kbn'] == 1) {
                $hachuInfo['hachu_tnk'] = $retData['hachu_tnk'];  // 発注単価
                $hachuInfo['hachu_prc'] = $retData['hachu_prc'];  // 発注金額
            } else {
                $hachuInfo['hachu_tnk'] = $record['gen_tnk'];  // 発注単価
                $hachuInfo['hachu_prc'] = $record['gen_tnk'] * $record['juchu_suryo'];  // 発注金額
            }
        }
        // 仲介区分のデフォルトを取得
        $chukai_bumon_cd = DataMapper_HachuCommon::getChukaibumoncd($record['shohin_bumon_cd'], $record['shohin_cd'], $record['siire_cd']);
        if ($chukai_bumon_cd !== null) {
            $hachuInfo['chukai_bumon_cd'] = $chukai_bumon_cd;
        }
        if (isset($record['k_free1'])) {
            $hachuInfo['k_free1'] = $record['k_free1'];  // フリー区分１
        }
        if (isset($record['k_free2'])) {
            $hachuInfo['k_free2'] = $record['k_free2'];  // フリー区分2 2016/04/20 ADD Kayo
        }
        // 見積確定されている場合は売上伝票Noと明細Noを設定する
        if (isset($dataApp['jichu_kakute_ymd'])) {
            $hachuInfo['uri_denpyo_no'] = $this->getUriagedenpyoNo();
            $hachuInfo['uri_msi_no'] = $record['msi_no'];
        } else {
            $hachuInfo['uri_denpyo_no'] = null;
            $hachuInfo['uri_msi_no'] = null;
        }
        // 会社コード
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy(); // 2016/11/22 ADD Kayo
        $hachuInfo['kaisya_cd'] = $kaisya_cd;    // 2016/11/22 ADD Kayo
        return $hachuInfo;
    }

    /**
     *
     * 商品単価マスタから仕入率により仕入単価を求める
     *
     * <AUTHOR> Kayo
     * @version 2016/11/22 複数会社対応 Kayo
     * @since 2016/10/11
     * @param string $shohin_cd 商品コード
     * @param string $juchu_tnk 受注単価
     * @param string $juchu_suryo 受注数量
     * @return array 計算した発注単価
     */
    private function getShohinTankaMst($shohin_cd, $juchu_tnk, $juchu_suryo) {

        $db = Msi_Sys_DbManager::getMyDb();
        // 2016/10/31 ADD Kayo
        if (strlen($this->_selKijunYmd) <= 0) {
            $KijunYmd = Msi_Sys_Utils::getDate();
        } else {
            $KijunYmd = $this->_selKijunYmd;
        }
        // 会社コード
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy(); // 2016/11/22 ADD Kayo
        $rec = $db->easySelOne(<<< END_OF_SQL
SELECT *
FROM shohin_tanka_mst
WHERE delete_flg    = 0
AND tekiyo_st_date <= :basedate    -- 適用開始日
AND tekiyo_ed_date >= :basedate    -- 適用終了日                
AND	shohin_cd       = :shohin_cd   -- 商品コード
AND kaisya_cd       = :kaisya_cd   -- 会社コード 2016/11/22 ADD Kayo                 
END_OF_SQL
                , array('shohin_cd' => $shohin_cd, 'basedate' => $KijunYmd, 'kaisya_cd' => $kaisya_cd));   // 2016/10/31 UPD Kayo
        $retData = array();
        $retData['siire_keisan_kbn'] = 0;    // 仕入計算区分 0:仕入単価 1:率で計算
        $retData['hachu_tnk'] = 0;    // 発注単価
        $retData['hachu_prc'] = 0;    // 発注金額
        if (count($rec) <= 0) {
            return $retData;
        }
        if ($rec['siire_keisan_kbn'] == 1) {   // 1:率で計算
            $retData['siire_keisan_kbn'] = $rec['siire_keisan_kbn'];    // 仕入計算区分 0:仕入単価 1:率で計算
            $retData['hachu_tnk'] = round($juchu_tnk * $rec['siire_ritu'], 3);
            $retData['hachu_prc'] = $retData['hachu_tnk'] * $juchu_suryo;
        }
        return $retData;
    }

    /**
     *
     * 商品の発注区分に該当するレポート管理マスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/17
     * @param string $hachuKbn 発注区分
     * @param array $reportKanri レポート管理マスタ
     * @return array レポート管理マスタ一行データ
     */
    private function getReportKanriOne($hachuKbn, $reportKanri) {

        $reportKanriOne = array();
        foreach ($reportKanri as $value) {
            if ($value['ha_rp_kbn'] === $hachuKbn) {
                $reportKanriOne = $value;
                break;
            }
        }
        return $reportKanriOne;
    }

    /**
     *
     * 見積が確定されているかチェックする
     *
     * <AUTHOR> Sai
     * @since 2014/4/7
     * @return boolean true => 確定済, false =>未確定
     */
    protected function isMitsuKakutei() {
        $juchukakuteiYMD = $this->getJuchuKakuteiYMD();
        if (!empty($juchukakuteiYMD)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     *
     * 受注伝票明細全削除SQL
     *
     * <AUTHOR> Sai
     * @since 2014/2/5
     * @return string SQL
     */
    protected function deleteAllJuchudenpyoMsiSQL() {
        // 削除処理は論理削除(deleteflgを更新)する
        // 受注伝票取得SQL
        $sql = "
        UPDATE
            juchu_denpyo_msi
        SET 
            delete_flg = 1
        WHERE
            delete_flg = 0
        AND denpyo_no = :denpyo_no
                ";
        return $sql;
    }

    /**
     *
     * 施行基本情報の承認情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/16
     * @return array 承認情報
     */
    protected function getUriageShoninInfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $uri_den_no = $this->getUriagedenpyoNo();
        $sql = "
            SELECT
                seko_tm.inkan_img AS inkan_img1
                ,shonin_tm.inkan_img AS inkan_img2
                ,TO_CHAR(ski.kakutei_ymd1, 'MM/DD HH24:MI') AS shonin_dt1
                ,TO_CHAR(ski.kakutei_ymd2, 'MM/DD HH24:MI') AS shonin_dt2
                ,TO_CHAR(ski.kakutei_ymd1, 'YYYY/MM/DD HH24:MI') AS shonin_ymd1
                ,TO_CHAR(ski.kakutei_ymd2, 'YYYY/MM/DD HH24:MI') AS shonin_ymd2
            FROM seko_kihon_info ski
            LEFT JOIN tanto_mst mitsu_tm
                ON mitsu_tm.tanto_cd = ski.jichu_kakute_cd
                AND mitsu_tm.delete_flg = 0
            LEFT JOIN tanto_mst seko_tm
                ON seko_tm.tanto_cd = ski.kakutei_tanto_cd1
                AND seko_tm.delete_flg = 0
            LEFT JOIN tanto_mst shonin_tm
                ON shonin_tm.tanto_cd = ski.kakutei_tanto_cd2
                AND shonin_tm.delete_flg = 0
            WHERE ski.seko_no = :seko_no
            AND ski.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        if ($select === null) {
            $select = array();
        }
        return $select;
    }

    /**
     *
     * 売上伝票の承認情報が存在するか
     *
     * <AUTHOR> Sai
     * @since 2014/4/17
     * @param array $dateUriShonin 承認情報
     * @param string $flg 'ALL' =>　すべて承認済みか
     * @return boolean true|false
     */
    protected function hasUriageShonin($dateUriShonin = null, $flg = null) {
        if ($dateUriShonin === null) {
            // 売上伝票の承認情報を取得する
            $dateUriShonin = $this->getUriageShoninInfo();
        }
        if ($flg === 'ALL') {
            // すべて承認されたらTRUE
            if (isset($dateUriShonin['inkan_img1']) && isset($dateUriShonin['inkan_img2'])) {
                return true;
            } else {
                return false;
            }
        } else {
            // ひとつでも承認されたらTRUE
            if (isset($dateUriShonin['inkan_img1']) || isset($dateUriShonin['inkan_img2'])) {
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     *
     * 売上伝票の承認情報が存在するか
     *
     * <AUTHOR> Sai
     * @since 2014/4/17
     * @param array $dateUriShonin 承認情報
     * @param string $flg 'ALL' =>　すべて承認済みか
     * @return boolean true|false
     */
    protected function hasNyukinData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $flg = false;
        $sql = "
            SELECT ud.uchikin_prc,ud.cupon_prc
            FROM uriage_denpyo ud
            WHERE ud.delete_flg = 0
                AND ud.seko_no  = :seko_no
                AND ud.data_kbn  = :data_kbn
            ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn()));
        if (count($select) > 0) {
            if ($select[0]['uchikin_prc'] > 0 || $select[0]['cupon_prc'] > 0) {
                $flg = true;
            }
        }
        return $flg;
    }

    /**
     *
     * ワーニングのJSONデータを出力する
     *
     * <AUTHOR> Sai
     * @since 2014/4/17
     * @param string $msg メッセージ
     * @return
     */
    protected function outWarnJson($msg) {
        $data['status'] = 'WARN';
        $data['msg'] = $msg;
        Msi_Sys_Utils::outJson($data);
    }

    /**
     *
     * NGのJSONデータを出力する
     *
     * <AUTHOR> Sai
     * @since 2014/4/17
     * @param string $msg メッセージ
     * @return
     */
    protected function outNgJson($msg) {
        $data['status'] = 'NG';
        $data['msg'] = $msg;
        Msi_Sys_Utils::outJson($data);
    }

    /**
     *
     * 葬儀情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/15
     * @version 2014/4/25 Juchu_JuchuTimescheduleより移動
     * @return array 葬儀情報
     */
    protected function getSougiInfo() {
        // 葬儀基本情報
        $dataSougiInfo = App_Utils::getSougiInfo($this->_sekoNo);
        return $dataSougiInfo;
    }

    /**
     *
     * 法事情報を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2021/xx/xx
     * @return array 法事情報
     */
    protected function getHoujiInfo() {
        // 法事基本情報
        $dataHoujiInfo = App_Utils::getHoujiInfo($this->_sekoNo);
        return $dataHoujiInfo;
    }

    /**
     *
     * 受注情報をwebマスタより取得する
     * 
     * <AUTHOR> Sai
     * @since 2014/04/25
     * @version 2014/05/01 Juchu_JuchuPressより引越し
     * @param int $categoryKbn カテゴリ区分
     * @param int $moushKbn 申込区分
     * @param string $mokutekiKbn 目的区分
     * @param array $dataApp 受注伝票情報
     * @return array webマスタと受注情報
     */
    protected function getJuchuDataFromWebMst($categoryKbn, $moushKbn = 1, $mokutekiKbn = null, $dataApp = array()) {
        $db = Msi_Sys_DbManager::getMyDb();
        if (!isset($dataApp['hasu_kbn'])) {
            // 奉仕率コードと奉仕率を取得する
            $hoshi = $this->getHosiritu();
            $dataApp["hoshi_ritu"] = $hoshi['zei_rtu'];
            $dataApp["hoshi_ritu_cd"] = $hoshi['hoshi_ritu_cd'];
            $dataApp["hasu_kbn"] = $hoshi['hasu_kbn'];
        }
        // keigen
        if (strlen($this->_selKijunYmd) <= 0) {
            $KijunYmd = Msi_Sys_Utils::getDate();
        } else {
            $KijunYmd = $this->_selKijunYmd;
        }
        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen(); // '2019/10/01'; // 軽減税率適用開始日   keigen
        $sql = "
        SELECT
             moushikomi_kbn     -- 申込区分
            ,category_kbn       -- カテゴリ区分
            ,tab_no             -- タブ順
            ,tekiyo_st_date     -- 適用開始日
            ,tekiyo_ed_date     -- 適用終了日
            ,disp_no            -- 表示№
            ,disp_kbn           -- 表示区分
            ,dai_bunrui_cd      -- 大分類コード
            ,chu_bunrui_cd      -- 中分類コード
            ,shohin_kbn         -- 商品区分
            ,shohin_cd          -- 商品コード
            ,suryo_inp_kbn      -- 数量入力区分
            ,prc_inp_kbn        -- 金額入力区分
            ,fuksu_sel_shohin   -- 複数商品選択区分
            ,kbn1               -- 区分１
            ,kbn2               -- 区分２
            ,msi_no             -- 受注明細№
            ,add_kbn            -- 追加区分
            ,'{$this->_bumonCd}' AS bumon_cd -- 部門コード
            ,1 AS denpyo_kbn    -- 伝票区分
            ,1 AS data_sbt      -- データ種別
            ,shohin_nm		-- 商品名称
            ,kani_shohin_nm     -- 商品簡略名称
            ,shohin_tkiyo_nm    -- 商品摘要
            ,mokuteki_kbn       -- 目的区分
            ,juchu_tnk		-- 単価
            ,juchu_suryo	-- 数量
            ,(juchu_tnk + hoshi_prc_tnk) * juchu_suryo AS juchu_gokei-- 受注金額合計
            ,juchu_prc		-- 受注金額
            ,nebiki_prc		-- 値引額
            ,gen_tnk		-- 原価単価
            ,shohin_type	-- 商品タイプ
            ,nonyu_nm		-- 納入場所
            ,nonyu_dt		-- 納入予定日
            ,nm_input_kbn	-- 名称入力区分
            ,tani_cd		-- 単位コード
            ,zei_kbn            -- 売上課税区分
            ,uri_kamoku_cd      -- 科目コード
            ,CASE 
                WHEN siire_lnm IS NULL THEN
                    NULL 
                ELSE siire_cd 
            END siire_cd-- 仕入コード
            ,siire_lnm          -- 仕入名
            ,tnk_chg_kbn        -- 売上単価変更区分
            ,hachu_kbn          -- 発注書区分
            ,hoshi_umu_kbn	-- 奉仕料有無区分
            ,hoshi_prc_tnk * juchu_suryo hoshi_prc  -- 奉仕料
            ,NULL AS nonyu_cd   -- 納入先コード 
            ,NULL AS nonyu_knm  -- 納入先名カナ
            ,NULL AS nonyu_yubin_no -- 納入先郵便番号
            ,NULL AS nonyu_addr1    -- 納入先住所1
            ,NULL AS nonyu_addr2    -- 納入先住所2
            ,NULL AS nonyu_tel  -- 納入先電話番号
            ,NULL AS nonyu_fax  -- 納入先FAX
            ,0 AS data_status   -- データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
            ,0 AS order_flg     -- 発注済み 0:未発注 1:発注済み
            ,m_suryo -- 施行プラン内訳マスタの数量
            ,m_shohin_cd -- 施行プラン内訳マスタの商品コード
            ,select_shohin_cd -- 施行プラン内訳マスタの選択商品コード
            ,seko_plan_uchiwk_no -- 施行プラン内訳マスタのコード
            ,reduced_tax_rate -- keigen
            ,zei_cd -- keigen
        FROM
            (
                SELECT
                    wsm.moushikomi_kbn
                    ,wsm.category_kbn
                    ,wsm.tab_no
                    ,wsm.tekiyo_st_date
                    ,wsm.tekiyo_ed_date
                    ,wsm.disp_no
                    ,wsm.disp_kbn
                    ,wsm.dai_bunrui_cd
                    ,wsm.chu_bunrui_cd
                    ,wsm.shohin_kbn
                    ,wsm.shohin_cd
                    ,wsm.suryo_inp_kbn
                    ,wsm.prc_inp_kbn
                    ,wsm.fuksu_sel_shohin
                    ,wsm.kbn1 -- 区分１
                    ,wsm.kbn2 -- 区分２
                    ,dm.msi_no
                    ,dm.add_kbn
                    ,sm.shohin_nm
                    ,sm.kani_shohin_nm
                    ,sm.shohin_tkiyo_nm
                    ,COALESCE (stm.hanbai_tnk ,0) AS juchu_tnk
                    ,1 AS juchu_suryo
                    ,COALESCE (stm.hanbai_tnk ,0)  AS juchu_prc
                    ,0 AS nebiki_prc
                    ,COALESCE (stm.siire_tnk ,0)  AS gen_tnk
                    ,NULL AS shohin_type
                    ,NULL AS nonyu_nm
                    ,NULL AS nonyu_dt
                    ,sm.nm_input_kbn
                    ,sm.tani_cd
                    ,sm.uri_zei_kbn AS zei_kbn
                    ,sm.siire_cd 
                    ,sim.siire_lnm
                    ,sm.hoshi_umu_kbn
                    ,sm.tnk_chg_kbn    
                    ,sbm.hachu_kbn
                    ,CASE WHEN sbm.mokuteki_kbn IS NULL THEN 0 ELSE sbm.mokuteki_kbn END mokuteki_kbn
                    ,sm.uri_kamoku_cd
                    -- 奉仕料
                    ,CASE
                        WHEN sm.hoshi_umu_kbn = 1 THEN 
                            CASE
                                 -- 丸め処理 0:切捨て 1:四捨五入 2:切上げ
                                WHEN {$dataApp['hasu_kbn']} = 1 THEN 
                                    ROUND(COALESCE (stm.hanbai_tnk ,0) * {$dataApp['hoshi_ritu']})
                                WHEN {$dataApp['hasu_kbn']} = 2 THEN 
                                    CEIL(COALESCE (stm.hanbai_tnk ,0) * {$dataApp['hoshi_ritu']})
                                ELSE 
                                    FLOOR(COALESCE (stm.hanbai_tnk ,0) * {$dataApp['hoshi_ritu']})
                             END
                        ELSE 0
                     END hoshi_prc_tnk
                     ,CASE WHEN spsm.suryo IS NULL THEN 0 ELSE spsm.suryo END m_suryo
                     ,spsm.shohin_cd AS m_shohin_cd
                     ,spsm.select_shohin_cd
                     ,spsm.seko_plan_uchiwk_no
                     ,CASE WHEN '{$KijunYmd}' < '$keigenBgnYmd' THEN 1
                        ELSE COALESCE(sm.uri_reduced_tax_rate, skm.uri_reduced_tax_rate, 1) END AS reduced_tax_rate -- keigen
                     ,z.zei_cd --  keigen
                FROM
                    web_disp_shohin_mst wsm
                    INNER JOIN
                        shohin_mst sm
                    ON  (
                            wsm.shohin_cd = sm.shohin_cd
                        AND '{$KijunYmd}' BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
                        AND wsm.kaisya_cd   = sm.kaisya_cd -- 2016/10/06 ADD Kayo   
                        AND sm.delete_flg = 0
                        )
                    LEFT OUTER JOIN
                        shohin_tanka_mst stm
                    ON  (
                            wsm.shohin_cd = stm.shohin_cd
                        AND '{$KijunYmd}' BETWEEN stm.tekiyo_st_date AND stm.tekiyo_ed_date
                        AND wsm.kaisya_cd   = stm.kaisya_cd -- 2016/10/06 ADD Kayo   
                        AND stm.delete_flg = 0
                        )
                    LEFT OUTER JOIN
                        siire_mst sim
                    ON  (
                            sm.siire_cd = sim.siire_cd
                        AND wsm.kaisya_cd   = sim.kaisya_cd -- 2016/10/06 ADD Kayo   
                        AND sim.delete_flg = 0
                        )
                    LEFT OUTER JOIN
                        juchu_denpyo_msi dm
                    ON  (
                            wsm.shohin_cd = dm.shohin_cd
                        AND wsm.dai_bunrui_cd = dm.dai_bunrui_cd
                        AND wsm.chu_bunrui_cd = dm.chu_bunrui_cd
                        AND wsm.shohin_kbn = dm.shohin_kbn
                        AND dm.seko_no = :seko_no
                        AND dm.seko_no_sub = :seko_no_sub
                        AND dm.delete_flg = 0
                        )
                    LEFT OUTER JOIN
                        shohin_bunrui_mst sbm
                    ON  (
                            wsm.dai_bunrui_cd   = sbm.dai_bunrui_cd
                        AND wsm.chu_bunrui_cd   = sbm.chu_bunrui_cd
                        AND wsm.shohin_kbn      = sbm.shohin_kbn
                        AND wsm.shohin_cd       = sbm.shohin_cd
                        AND wsm.kaisya_cd       = sbm.kaisya_cd -- 2016/11/17 ADD Kayo   
                        AND sbm.delete_flg      = 0
                        )
                    LEFT OUTER JOIN
                        seko_plan_smsi_mst spsm
                    ON  (
                            wsm.shohin_kbn  = spsm.shohin_kbn
                        AND '{$KijunYmd}' BETWEEN spsm.tekiyo_st_date AND spsm.tekiyo_ed_date -- 2017/03/01 ADD Kayo
                        AND wsm.shohin_cd  = spsm.shohin_cd
                        AND spsm.seko_plan_cd = :seko_plan_cd
                        AND spsm.gojokai_kbn = :gojokai_kbn
                        AND wsm.kaisya_cd   = spsm.kaisya_cd -- 2016/10/06 ADD Kayo   
                        AND spsm.delete_flg = 0
                        )
                    LEFT JOIN shohin_kbn_mst skm	-- keigen	
                            ON sbm.shohin_kbn	=	skm.shohin_kbn
                            AND skm.delete_flg	=	0
                    LEFT	JOIN zei_mst	z	-- keigen
                            ON '{$KijunYmd}'	BETWEEN TO_CHAR(z.tekiyo_st_date,'YYYY/MM/DD')
                            AND			TO_CHAR(z.tekiyo_ed_date,'YYYY/MM/DD')
                            AND	z.delete_flg	= 0	
                            AND z.reduced_tax_rate = (CASE WHEN '{$KijunYmd}' < '{$keigenBgnYmd}' THEN 1
										   ELSE COALESCE(sm.uri_reduced_tax_rate, skm.uri_reduced_tax_rate, 1) END)
                WHERE
                    wsm.delete_flg = 0
                AND wsm.category_kbn = :category_kbn
                AND wsm.moushikomi_kbn = :moushikomi_kbn
                AND '{$KijunYmd}' BETWEEN wsm.tekiyo_st_date AND wsm.tekiyo_ed_date
                AND wsm.kaisya_cd   = :kaisya_cd -- 2016/10/06 ADD Kayo   
            ) M
        ORDER BY
            kbn2
	    ,kbn1
            ,disp_no
                ";
        // 複数会社対応 2016/10/06 ADD Kayo
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo
            , 'seko_no_sub' => $this->_sekoNoSub
            , 'category_kbn' => $categoryKbn
            , 'moushikomi_kbn' => $moushKbn
            , 'seko_plan_cd' => $this->_sekoPlanCd
            , 'gojokai_kbn' => $this->_gojokaiKbn
            , 'kaisya_cd' => $curKaisyaCd            // 2016/10/06 ADD Kayo              
        ));
        return $select;
    }

    /**
     *
     * 施行プラン商品明細マスタを一行取得する
     * 
     * <AUTHOR> Sai
     * @since 2014/07/14
     * @param string $shohin_cd 商品コード
     * @return array 施行プラン商品明細
     */
    protected function getSekoPlanMsiMst($shohin_cd) {
        $db = Msi_Sys_DbManager::getMyDb();
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();    // 2016/11/22 ADD Kayo
        $sql1 = "
                SELECT
                     spsm.shohin_cd
                FROM
                    seko_plan_smsi_mst spsm
                WHERE
                    spsm.select_shohin_cd  = :shohin_cd
                AND CURRENT_DATE BETWEEN spsm.tekiyo_st_date AND spsm.tekiyo_ed_date -- 2017/03/01 ADD Kayo
                AND spsm.seko_plan_cd = :seko_plan_cd
                AND spsm.gojokai_kbn  = :gojokai_kbn
                AND spsm.kaisya_cd    = :kaisya_cd  -- 2016/11/22 ADD Kayo 
                AND spsm.delete_flg = 0
                ";
        $select1 = $db->easySelOne($sql1, array('shohin_cd' => $shohin_cd
            , 'seko_plan_cd' => $this->_sekoPlanCd
            , 'gojokai_kbn' => $this->_gojokaiKbn
            , 'kaisya_cd' => $kaisya_cd            // 2016/11/22 ADD Kayo              
        ));
        if (count($select1) === 0) {
            return array();
        }
        $sql2 = "
                SELECT
                     spsm.shohin_cd
                     ,spsm.suryo
                     ,spsm.hanbai_tnk
                FROM
                    seko_plan_smsi_mst spsm
                WHERE
                    spsm.shohin_cd  = :shohin_cd
                AND CURRENT_DATE BETWEEN spsm.tekiyo_st_date AND spsm.tekiyo_ed_date -- 2017/03/01 ADD Kayo
                AND spsm.seko_plan_cd = :seko_plan_cd
                AND spsm.gojokai_kbn = :gojokai_kbn
                AND spsm.kaisya_cd    = :kaisya_cd  -- 2016/11/22 ADD Kayo 
                AND spsm.delete_flg = 0
                ";
        $select2 = $db->easySelOne($sql2, array('shohin_cd' => $select1['shohin_cd']
            , 'seko_plan_cd' => $this->_sekoPlanCd
            , 'gojokai_kbn' => $this->_gojokaiKbn
        ));
        return $select2;
    }

    /**
     *
     * 新聞掲載詳細データをトランより取得する
     *
     * <AUTHOR> Sai
     * @since 2014/04/25
     * @version 2014/05/01 Juchu_JuchuPressより引越し
     * @return array 明細データ
     */
    protected function getKeisaiDetailData() {

        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            kd.seko_no -- 施行番号
            ,kd.disp_no -- 表示順
            ,kd.shohin_cd -- 新聞社コード
            ,kd.shohin_nm -- 新聞社名
            ,kd.kani_shohin_nm -- 簡易新聞社名
            ,kd.kbn1 -- 区分１
            ,kd.kbn2 -- 区分2
            ,kd.keisai_kbn -- 掲載希望
            ,kd.tatekae_kbn -- 立替区分
            ,kd.tatekae_prc -- 立替金
            ,kd.biko -- メモ
        FROM
            seko_keisai_detail kd
        WHERE
            kd.seko_no = :seko_no
        AND kd.delete_flg = 0
        ORDER BY
            kd.disp_no
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * 葬送儀礼データ削除処理
     *
     * <AUTHOR> Sai
     * @since 2014/05/12
     * @param Msi_Sys_Db $db db
     * @param string  $denpyoNo 伝票番号
     * @return int 削除件数
     */
    protected function deleteSosogireiData($db, $denpyoNo) {
        $cnt = 0;
        if ($denpyoNo) {
            $sekoPlanCd = $this->_selectSekoKihon['seko_plan_cd'];
            // 施行プラン明細マスタに存在するデータを絞って削除する
            // 施行プランがない場合、基本プランの全データを削除すると、通夜会場・式場使用料が削除されるため、施行プラン明細に絞って削除する
            // 複数会社対応 2016/10/06 ADD Kayo
            $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
            $sqlDeleteJuchudenpyoMsi = $this->deleteJuchudenpyoMsiBySekoPlan();
            $sqlDeleteHachuInfo = $this->deleteHachuInfoBySekoPlan();
            $cnt += $db->easyExecute($sqlDeleteHachuInfo, array('denpyo_no' => $denpyoNo));
            $juchudenpyoMsi = $db->easySelect($sqlDeleteJuchudenpyoMsi, array('denpyo_no' => $denpyoNo)); // 2016/10/06 UPD Kayo
            foreach ($juchudenpyoMsi as $DenpyoMsiRow) {
                if ($DenpyoMsiRow['order_flg'] != '1') {
                    $delMsi = "DELETE FROM juchu_denpyo_msi WHERE denpyo_no = :denpyo_no AND msi_no = :msi_no";
                    $cnt += $db->easyExecute($delMsi, array('denpyo_no' => $denpyoNo, 'msi_no' => $DenpyoMsiRow['msi_no']));
                }
            }
        }
        return $cnt;
    }

    /**
     *
     * 受注伝票明細削除SQL
     *
     * <AUTHOR> Sai
     * @since 2014/6/5
     * @return string SQL
     */
    private function deleteJuchudenpyoMsiBySekoPlan() {
        // 削除処理は物理削除する
        // プラン商品とアップグレード商品が対象
        $sql = "
        SELECT
            dm.denpyo_no
            ,dm.msi_no
            ,shi.order_flg
        FROM juchu_denpyo_msi dm
        LEFT OUTER JOIN seko_hachu_info shi
            ON dm.denpyo_no = shi.jc_denpyo_no
            AND dm.seko_no  = shi.seko_no
            AND dm.msi_no  = shi.jc_msi_no
        WHERE dm.denpyo_no = :denpyo_no
            AND dm.upgrade_kbn IN (1,2)
                ";
        return $sql;
    }

    /**
     *
     * 施行発注管理情報削除SQL
     *
     * <AUTHOR> Sai
     * @since 2014/6/5
     * @return string SQL
     */
    private function deleteHachuInfoBySekoPlan() {
        // 削除処理は物理削除する
        $sql = "
        DELETE
            FROM
                seko_hachu_info shi
            USING 
                juchu_denpyo_msi dm
        WHERE
            shi.jc_denpyo_no = :denpyo_no
            AND shi.jc_denpyo_no = dm.denpyo_no
            AND shi.jc_msi_no = dm.msi_no
            AND dm.upgrade_kbn IN (1,2)
        AND shi.order_flg <> 1
                ";
        return $sql;
    }

    /**
     *
     * 受注伝票特典明細削除SQL
     *
     * <AUTHOR> Sai
     * @since 2014/6/13
     * @return string SQL
     */
    private function deleteJuchudenpyoMsiTokuten() {
        // 削除処理は物理削除する
        $sql = "
        DELETE
            FROM
                juchu_denpyo_msi
        WHERE
            denpyo_no = :denpyo_no
        AND add_kbn = :add_kbn_tk
                ";
        return $sql;
    }

    /**
     *
     * 施行発注管理特典情報削除SQL
     *
     * <AUTHOR> Sai
     * @since 2014/6/13
     * @return string SQL
     */
    private function deleteHachuInfoTokuten() {
        // 削除処理は物理削除する
        $sql = "
        DELETE
            FROM
                seko_hachu_info shi
            USING 
                juchu_denpyo_msi dm
        WHERE
            shi.jc_denpyo_no = :denpyo_no
        AND shi.jc_denpyo_no = dm.denpyo_no
        AND shi.seko_no = dm.seko_no
        AND shi.jc_msi_no = dm.msi_no
        AND dm.add_kbn = :add_kbn_tk
                ";
        return $sql;
    }

    /**
     *
     * 消費税基準日を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/5/14
     * @return string 消費税基準日
     */
    protected function getZeiKijunYmd() {
        $zeikijun = '';
        if (isset($this->_selectSekoKihon['sougi_ymd'])) {
            $zeikijun = $this->_selectSekoKihon['sougi_ymd'];
        }
        if (empty($zeikijun)) {
            $zeikijun = Msi_Sys_Utils::getDate();
        }
        return $zeikijun;
    }

    /**
     *
     * 葬儀日を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/08/01
     * @return string 葬儀日
     */
    private function getSogiYmd() {
        $sogiYmd = '';
        if (isset($this->_selectSekoKihon['sougi_ymd'])) {
            $sogiYmd = $this->_selectSekoKihon['sougi_ymd'];
        }
        if (empty($sogiYmd)) {
            $sogiYmd = Msi_Sys_Utils::getDate();
        }
        return $sogiYmd;
    }

    /**
     *
     * 見積確定済みの場合、NGのメッセージを出力する
     *
     * <AUTHOR> Sai
     * @since 2014/5/21
     * @return boolean 見積確定済み
     */
    protected function isKakuteiOutNg() {
        if ($this->_juchuKakuteiYMD) {
            $this->outNgJson('見積が確定されたため、保存することができません。');
            return true;
        } else {
            return false;
        }
    }

    /**
     * 施行情報をリクエストオブジェクトより取得して設定する
     *
     * <AUTHOR> Sai
     * @since 2014/06/09
     * @param string $controllerName コントローラ名
     */
    private function setSekoParam() {
        $seko_no = '';
        $request = Msi_Sys_Utils::getRequestObject();
        $param = $request->getParams();
        if (isset($param['sn'])) {
            $seko_no = $param['sn'];
        }
        $this->_sekoNo = $seko_no;
    }
    
    /**
     * リクエストオブジェクトより施行番号を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/06/09
     */
    protected function getReqSekoNo() {
        $seko_no = '';
        $request = Msi_Sys_Utils::getRequestObject();
        $param = $request->getParams();
        if (isset($param['sn'])) {
            $seko_no = $param['sn'];
            $select = $this->selectSekoKihon($seko_no);
            if (Msi_Sys_Utils::myCount($select) === 0) {
                throw new Exception('施行番号がありません');
            }
            // ログイン者の部門参照権限チェック
            $this->checkBumonRef($select);
            $moushiKBn = $select['moushi_kbn'];
            $cn = App_Utils::getParamsControllerName();
            $ac = App_Utils::getParamsActionName();
            if ($cn === 'seikyusyo') {
                if ($ac === 'shonin' && $moushiKBn === self::MOUSHI_KBN_HOUJI) {
                    throw new Exception('葬儀の請求画面で法事を呼び出すことができません。');
                } else if ($ac === 'shoninh' && $moushiKBn !== self::MOUSHI_KBN_HOUJI) {
                    throw new Exception('法事の請求画面で葬儀を呼び出すことができません。');
                }
            } else {
                if ($cn === 'houji' || $cn === 'juchuhenkoh') {
                    if ($moushiKBn !== self::MOUSHI_KBN_HOUJI) {
                        throw new Exception('該当データがありません。');
                    }
                } else if ($cn === 'mitsu' && $ac === 'input') {
                    if ($moushiKBn === self::MOUSHI_KBN_HOUJI || $moushiKBn == self::MOUSHI_KBN_ORDERMADE || $moushiKBn == self::MOUSHI_KBN_SEIZEN) {
                        throw new Exception('該当データがありません。');
                    }
                } else if ($cn === 'juchuhenko') {
                    if ($moushiKBn === self::MOUSHI_KBN_HOUJI || $moushiKBn == self::MOUSHI_KBN_SEIZEN || $moushiKBn == self::MOUSHI_KBN_ORDERMADE) {
                        throw new Exception('該当データがありません。');
                    }
                } else if ($cn === 'preconsult') {
                    if ($moushiKBn !== self::MOUSHI_KBN_SEIZEN) {
                        throw new Exception('該当データがありません。');
                    }
                } else if ($cn === 'ordermade') {
                    if ($moushiKBn !== self::MOUSHI_KBN_ORDERMADE) {
                        throw new Exception('該当データがありません。');
                    }
                } else {
                    if ($moushiKBn === self::MOUSHI_KBN_HOUJI) {
                        throw new Exception('該当データがありません。');
                    }
                }
            }
            if ($moushiKBn === self::MOUSHI_KBN_HOUJI) {
                if (App_Utils::isHoujiInJuchu()) {
                    App_Utils::setSessionData('seko_no_mitsu_houji', $seko_no);
                } else {
                    App_Utils::setSessionData('seko_no_houji', $seko_no);
                }
            } else {
                if (App_Utils::isMitsuInJuchu()) {
                    if (App_Utils::isPreconsultInJuchu()) {
                        // 申込区分が事前相談じゃなければクリアする
                        if ($moushiKBn != self::MOUSHI_KBN_SEIZEN) {
                            App_Utils::setSessionData('seko_no_preconsult', null);
                            App_Utils::setSessionData('seko_no_ordermade', null);
                            App_Utils::setSessionData('seko_no_mitsu', $seko_no);
                        } else {
                            App_Utils::setSessionData('seko_no_preconsult', $seko_no);
                            App_Utils::setSessionData('seko_no_ordermade', null);
                            App_Utils::setSessionData('seko_no_mitsu', null);
                        }
                    } else if (App_Utils::isOrdermadeInJuchu()) {
                        // 申込区分がオーダーメイドじゃなければクリアする
                        if ($moushiKBn != self::MOUSHI_KBN_ORDERMADE) {
                            App_Utils::setSessionData('seko_no_preconsult', null);
                            App_Utils::setSessionData('seko_no_ordermade', null);
                            App_Utils::setSessionData('seko_no_mitsu', $seko_no);
                        } else {
                            App_Utils::setSessionData('seko_no_ordermade', $seko_no);
                            App_Utils::setSessionData('seko_no_preconsult', null);
                            App_Utils::setSessionData('seko_no_mitsu', null);
                        }
                    } else {
                        // 申込区分が事前相談であればクリアする
                        if ($moushiKBn == self::MOUSHI_KBN_SEIZEN) {
                            App_Utils::setSessionData('seko_no_preconsult', $seko_no);
                            App_Utils::setSessionData('seko_no_ordermade', null);
                            App_Utils::setSessionData('seko_no_mitsu', null);
                        } else if ($moushiKBn == self::MOUSHI_KBN_ORDERMADE) {
                            App_Utils::setSessionData('seko_no_ordermade', $seko_no);
                            App_Utils::setSessionData('seko_no_preconsult', null);
                            App_Utils::setSessionData('seko_no_mitsu', null);
                        } else {
                            App_Utils::setSessionData('seko_no_preconsult', null);
                            App_Utils::setSessionData('seko_no_ordermade', null);
                            App_Utils::setSessionData('seko_no_mitsu', $seko_no);
                        }
                    }
                } else {
                    App_Utils::setSessionData('seko_no', $seko_no);
                }
            }
        }
        if (isset($seko_no) && strlen($seko_no) > 0) { // 2016/09/xx Mihara 権限チェック
            if (!App_Utils::canAccessSekoData($seko_no)) {
                throw new Exception("施行番号($seko_no)のアクセス権限がありません(J)");
            }
        }
        if (empty($seko_no)) {
            App_Utils::setSessionData('seko_no_mitsu', null);
            App_Utils::setSessionData('seko_no', null);
            App_Utils::setSessionData('seko_no_mitsu_houji', null);
            App_Utils::setSessionData('seko_no_houji', null);
            App_Utils::setSessionData('seko_no_preconsult', null); // 事前相談用
            App_Utils::setSessionData('seko_no_om', null); // オーダーメイド用 
            $actionName = $request->getActionName();
            // お客様情報以外の画面で施行番号が存在しない場合はエラー
            if (!($actionName === 'input' || $actionName === 'customerinfo' || $actionName === 'order' || $actionName === 'inputrr' || $actionName === 'customerinforr' || $actionName === 'preconsult' || $actionName === 'ordermade')) {
                throw new Exception('URLが不正です');
            }
        }
        return $seko_no;
    }

    /**
     *
     * 基本プランのパターンを求める。
     *
     * <AUTHOR> Sai
     * @since 2014/06/19
     * @param string $sogi  葬儀区分 1:個人 2:社葬 3:寺院葬
     * @param string $keishiki 形式 1:個人葬 2:団体葬（合同葬）3:家族葬 4:火葬式 5:お別れ会
     * @param string $anchi 安置先 00:自宅 01:通夜会場 99:その他
     * @param string $sikijyo 葬儀場所 00 自宅 01 寺院 02 ホール 03 他会館 04 公民館 05 通夜会場 06 他寺院 99 その他
     * @return array[main_pt_cd]      => 基本パターン区分コード
     *          array[main_pt_kbn]     => 基本パターン区分
     */
    protected function calcKihonPtn($sogi, $keishiki, $anchi, $sikijyo) {
        return App_Utils2::calcKihonPtn($sogi, $keishiki, $anchi, $sikijyo);
    }

    /**
     *
     * サイドメニューのカレントを設定する
     *
     * <AUTHOR> Sai
     * @since 2014/06/26
     * @param array &$dataCol サイドメニューデータ
     * @param string $current カレントクラス名
     * @param array $sideMenuInfo サイドメニュ更新情報
     */
    protected function setMenuCurrent(&$dataCol, $current, $sideMenuInfo) {
        foreach ($dataCol as &$value) {
            $disp_no = $value['disp_no'];
            for ($i = 0; $i < count($sideMenuInfo); $i++) {
                if ($disp_no == $sideMenuInfo[$i]['item_no']) {
                    // 更新日を設定する
                    $value['update'] = $sideMenuInfo[$i]['item_use_ts'] . ' 更新';
                    $value['item_info'] = $sideMenuInfo[$i];
                    break;
                }
            }
            // カレントをを設定する
            if ($value['css_class'] === $current) {
                $value['current'] = 1;
            }
        }
    }

    /**
     * 受注伝票の明細とヘッダーの消費税差額設定処理
     * 軽減税率考慮版 keigen
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @return int 更新件数
     */
    protected function setJuchuDenpyoAdjTax($denpyoNo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $cnt = App_KeigenUtils::setJuchuDenpyoAdjTax($db, $denpyoNo);
        return $cnt;
    }

    /**
     *
     * 受注伝票の明細とヘッダーの消費税差額設定処理
     *
     * <AUTHOR> Sai
     * @version 2014/09/27 調整の更新を無条件に実行するように修正 Kayo
     * @param string $denpyoNo 受注伝票番号
     * @since 2014/07/09
     * @return int 更新件数
     */
    protected function OLD_setJuchuDenpyoAdjTax($denpyoNo) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            msi_no
            ,zei_kbn
            ,out_zei_prc
            ,in_zei_prc
            ,juchu_prc + hoshi_prc + nebiki_prc + gojokai_nebiki_prc AS prc_sum
            ,dai_bunrui_cd
            ,chu_bunrui_cd
            ,shohin_kbn
            ,shohin_cd
            ,zei_cd  -- 2019/04/30 mihara keigen
            ,reduced_tax_rate --  2019/04/30 mihara keigen
        FROM
            juchu_denpyo_msi
        WHERE
            denpyo_no = :denpyo_no
        AND delete_flg = 0
        ORDER BY
            msi_no
                ";
        $select = $db->easySelect($sql, array('denpyo_no' => $denpyoNo));
        if (count($select) > 0) {
            list($pChoseiPrc, $pChoseiPrc_in_zei) = $this->getDenpyoAdjTax($select);
            $record = App_Utils2::getAdjTaxRecord($select);
            $dataApp = $this->getTaxinfo();
            $this->setZeiInfo($dataApp, $record, $record, $record['prc_sum']);
            $denpyoMsi['out_zei_prc'] = $record['out_zei_prc'] + $pChoseiPrc;
            $denpyoMsi['in_zei_prc'] = $record['in_zei_prc'] + $pChoseiPrc_in_zei;
            $where['msi_no'] = $record['msi_no'];
            $where['denpyo_no'] = $denpyoNo;
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo_msi", $denpyoMsi, $where);
            $cnt = $db->easyExecute($sqlUp, $param);
        }
        return $cnt;
    }

    /**
     * 売上伝票の明細とヘッダーの消費税差額設定処理
     * 軽減税率考慮版 keigen
     *
     * <AUTHOR> mihara
     * @since 2019/05/21
     * @param string $denpyoNo 売上伝票番号
     * @return int 更新件数
     */
    protected function setUriageDenpyoAdjTax($denpyoNo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $cnt = App_KeigenUtils::setUriageDenpyoAdjTax($db, $denpyoNo);
        return $cnt;
    }

    /**
     *
     * 売上伝票の明細とヘッダーの消費税差額設定処理
     *
     * <AUTHOR> Sai
     * @version 2014/09/27 調整の更新を無条件に実行するように修正 Kayo
     * @param string $denpyoNo 売上伝票番号
     * @since 2014/07/09
     * @return int 更新件数
     */
    protected function OLD_setUriageDenpyoAdjTax($denpyoNo) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            msi_no
            ,zei_kbn
            ,out_zei_prc
            ,in_zei_prc
            ,uri_prc + hoshi_prc + nebiki_prc + gojokai_nebiki_prc AS prc_sum
            ,dai_bunrui_cd
            ,chu_bunrui_cd
            ,shohin_kbn
            ,shohin_cd
            ,zei_cd  -- 2019/04/30 mihara keigen
            ,reduced_tax_rate --  2019/04/30 mihara keigen
        FROM
            uriage_denpyo_msi
        WHERE
            uri_den_no = :uri_den_no
        AND delete_flg = 0
        ORDER BY
            msi_no
                ";
        $select = $db->easySelect($sql, array('uri_den_no' => $denpyoNo));
        if (count($select) > 0) {
            list($pChoseiPrc, $pChoseiPrc_in_zei) = $this->getDenpyoAdjTax($select);
            $record = App_Utils2::getAdjTaxRecord($select);
            $dataApp = $this->getTaxinfo();
            $this->setZeiInfo($dataApp, $record, $record, $record['prc_sum']);
            $denpyoMsi['out_zei_prc'] = $record['out_zei_prc'] + $pChoseiPrc;
            $denpyoMsi['in_zei_prc'] = $record['in_zei_prc'] + $pChoseiPrc_in_zei;
            $where['msi_no'] = $record['msi_no'];
            $where['uri_den_no'] = $denpyoNo;
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo_msi", $denpyoMsi, $where);
            $cnt = $db->easyExecute($sqlUp, $param);
        }
        return $cnt;
    }

    /**
     *
     * 受注伝票・売上伝票の明細とヘッダーの消費税差額取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/07/09
     * @param array $select 伝票明細
     * @return string 消費税差額
     */
    private function getDenpyoAdjTax($select) {
        $pZeiNo = $pZeiRt = $pShouhiZeiPrc = null;
        $pRuikeiBaikaPrc = $pRuikeiShohiZeiPrc = $pChoseiPrc = 0;
        $pRuikeiBaikaPrc_in_zei = $pRuikeiShohiZeiPrc_in_zei = $pChoseiPrc_in_zei = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $kijunYmd = $this->getZeiKijunYmd();
        foreach ($select as $dm) {
            App_ClsTaxLib::GetTaxMsiInfo($db, $kijunYmd, $dm['prc_sum'], $dm['zei_kbn'], $pZeiNo, $pZeiRt, $pShouhiZeiPrc, $pRuikeiBaikaPrc, $pRuikeiShohiZeiPrc, $pChoseiPrc);
            App_ClsTaxLib::GetTaxMsiInfoUchiZei($db, $kijunYmd, $dm['prc_sum'], $dm['zei_kbn'], $pZeiNo, $pZeiRt, $pShouhiZeiPrc, $pRuikeiBaikaPrc_in_zei, $pRuikeiShohiZeiPrc_in_zei, $pChoseiPrc_in_zei);
        }
        return array($pChoseiPrc, $pChoseiPrc_in_zei);
    }

    /**
     *
     * 受注伝票の自動作成した明細の表示順再設定処理
     *
     * <AUTHOR> Sai
     * @since 2014/07/25
     * @return int 更新件数
     */
    protected function resetJuchuDenpyoDispNo() {
        $cnt = 0;
        $juchuDenNo = $this->getJuchudenpyoNo();
        if (!empty($juchuDenNo)) {
            $maxDispNo = 0;
            $db = Msi_Sys_DbManager::getMyDb();

            $sql = "
                SELECT
                    COALESCE(MAX(disp_no),0) AS disp_no
                FROM
                    juchu_denpyo_msi
                WHERE
                    denpyo_no = :denpyo_no
                    ";
            $select = $db->easySelOne($sql, array('denpyo_no' => $juchuDenNo));
            if (count($select) > 0) {
                $maxDispNo = $select['disp_no'];
            }

            $sql2 = "
                SELECT 
                    disp_no
                    ,msi_no
                FROM
                (
                    SELECT
                        dm.disp_no
                        ,dm.msi_no
                        ,spsm.seko_plan_uchiwk_no
                    FROM
                        juchu_denpyo_msi dm
                    LEFT OUTER JOIN
                        seko_plan_smsi_mst spsm
                    ON  (
                            dm.shohin_kbn  = spsm.shohin_kbn
                        AND CURRENT_DATE BETWEEN spsm.tekiyo_st_date AND spsm.tekiyo_ed_date -- 2017/03/01 ADD Kayo
                        AND dm.shohin_cd  = spsm.shohin_cd
                        AND dm.msi_no  = spsm.seko_plan_uchiwk_no
                        AND spsm.seko_plan_cd = :seko_plan_cd
                        AND spsm.gojokai_kbn = :gojokai_kbn
                        AND spsm.kaisya_cd  = :kaisya_cd        -- 2016/10/06 ADD Kayo
                        AND spsm.delete_flg = 0
                        )
                    WHERE
                        dm.denpyo_no = :denpyo_no
                    AND dm.data_sbt = 1
                    AND dm.delete_flg = 0
                    AND dm.add_kbn <> :add_kbn
                 ) T
                 WHERE seko_plan_uchiwk_no IS NULL 
                ORDER BY
                    disp_no
                    ";
            $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();  // 2016/10/06 ADD Kayo
            $select2 = $db->easySelect($sql2, array('denpyo_no' => $juchuDenNo, 'add_kbn' => self::ADD_KBN_TK, 'seko_plan_cd' => $this->_sekoPlanCd, 'gojokai_kbn' => $this->_gojokaiKbn
                , 'kaisya_cd' => $curKaisyaCd));    // 2016/10/06 UPD Kayo
            foreach ($select2 as $oneRow) {
                $oneRow['disp_no'] = ++$maxDispNo;
                // 条件部
                $where['denpyo_no'] = $juchuDenNo;
                $where['msi_no'] = $oneRow['msi_no'];
                $where['delete_flg'] = 0;  // 削除フラグ
                // 更新SQL
                list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL('juchu_denpyo_msi', $oneRow, $where);
                $cnt += $db->easyExecute($sqlUp, $param);
            }
            return $cnt;
        }
    }

    /**
     *
     * 消費税情報取得k処理
     *
     * <AUTHOR> Sai
     * @since 2014/07/28
     * @return array 消費税情報
     */
    private function getTaxinfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        // 葬儀日設定されたら葬儀日、そうでなければはシステム日付
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        // 消費税取得処理
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        $dataApp['zei_rtu'] = (int) $taxInfo['zei_rtu'];
        $dataApp['zei_cd'] = (int) $taxInfo['zei_cd'];
        $dataApp['zei_hasu_kbn'] = (int) $taxInfo['zei_hasu_kbn'];
        return $dataApp;
    }

    /**
     *
     * 申込区分よりデータ区分を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/08/13
     * @return string データ区分
     */
    protected function getDataKbn() {
        $data_kbn = '';
        if ($this->_moushiKbn === self::MOUSHI_KBN_HOUJI) {
            $data_kbn = $this->_moushiKbn;
        } else {
            $data_kbn = self::MOUSHI_KBN_SOUGI;
        }
        return $data_kbn;
    }

    /**
     * 確定時保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/03/07
     * @version 2014/09/15 消費税端数区分を追加 Kayo
     * @version 2014/12/26 Juchu_JuchuhenkoSubAbstractより移動
     * @param Msi_Sys_Db $db db
     * @param array &$dataApp 画面Appデータ
     * @param array &$dataCol グリッドデータ
     * @param array $dataTrnDelCol グリッドトラン削除データ
     * @return int 更新件数
     */
    protected function saveUriage($db, &$dataApp, &$dataCol, $dataTrnDelCol) {

        $isAutoSaiban = false; // 自動採番しているか(初回登録)
        $cnt = 0;
        // 葬儀日設定されたら葬儀日、そうでなければはシステム日付
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        // 消費税取得処理
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        $dataApp['zei_rtu'] = (int) $taxInfo['zei_rtu'];
        $dataApp['zei_cd'] = (int) $taxInfo['zei_cd'];
        $dataApp['zei_hasu_kbn'] = (int) $taxInfo['zei_hasu_kbn']; // 2014/09/15 ADD Kayo
        $dataApp['zei_kijun_ymd'] = $this->_zeiKijunYmd;
        if (!isset($dataApp['hasu_kbn'])) {
            // 奉仕率コードと奉仕率を取得する
            $hoshi = $this->getHosiritu();
            $dataApp["hoshi_ritu"] = $hoshi['zei_rtu'];
            $dataApp["hoshi_ritu_cd"] = $hoshi['hoshi_ritu_cd'];
            $dataApp["hasu_kbn"] = $hoshi['hasu_kbn'];
        }
        // 売上伝票番号取得する
        $uriageDenpyoNo = $this->getUriagedenpyoNo();
        if (empty($uriageDenpyoNo)) {
            $isAutoSaiban = true;
            // 伝票番号採番処理
            $uriageDenpyoNo = $this->getAutoUriageDenpyoNo($db);
        }
        $dataApp["denpyo_no"] = $uriageDenpyoNo;

        // グリッドトラン削除データ処理を行う
        $sqlDeleteDenpyoMsi = $this->deleteUriagedenpyoMsiSQL();
        foreach ($dataTrnDelCol as $DenpyoMsiDelRow) {
            $cnt += $db->easyExecute($sqlDeleteDenpyoMsi, array('denpyo_no' => $dataApp['denpyo_no'], 'msi_no' => $DenpyoMsiDelRow["msi_no"],));
        }

        // 最大売上明細№を取得する
        $maxMsiNo = $this->getMaxUriagedenpyoMsiNo($uriageDenpyoNo);
        // 売上伝票明細データがない場合、登録する
        // 売上伝票明細データがある場合、更新する
        foreach ($dataCol as &$DenpyoMsiRow) {
            $DenpyoMsiRow["denpyo_no"] = $uriageDenpyoNo;
            if (isset($DenpyoMsiRow['dai_bunrui_cd'])) {
                if (!isset($DenpyoMsiRow['msi_no'])) {
                    // 画面新規入力データの場合、明細番号を設定する
                    $DenpyoMsiRow['msi_no'] = ++$maxMsiNo;
                }
                // 売上伝票明細存在チェック
                $sqlSelectDenpyoMsi = $this->selectUriagedenpyoMsiSQL();
                $selectDenpyoMsi = $db->easySelect($sqlSelectDenpyoMsi, array('denpyo_no' => $dataApp['denpyo_no'], 'msi_no' => $DenpyoMsiRow["msi_no"]));
                if (count($selectDenpyoMsi) === 0) {
                    // 売上伝票明細テーブル登録情報を設定する
                    $uriageDenpyoMsi = $this->setUriagedenpyoMsiInstertInfo($dataApp, $DenpyoMsiRow);
                    list($sql, $param) = $this->insertUriagedenpyoMsiSQL($uriageDenpyoMsi);
                } else {
                    // 売上伝票明細テーブル更新情報を設定する
                    $uriageDenpyoMsi = $this->setUriagedenpyoMsiUpdateInfo($dataApp, $DenpyoMsiRow);
                    // 条件部
                    $where1['uri_den_no'] = $uriageDenpyoNo;  // 伝票番号
                    $where1['seko_no'] = $this->_sekoNo;  // 施行番号
                    $where1['seko_no_sub'] = $this->_sekoNoSub;  // 施行番号枝番
                    $where1["msi_no"] = $DenpyoMsiRow['msi_no'];  // 売上明細№
                    $where1['delete_flg'] = 0;
                    list($sql, $param) = $this->updateUriagedenpyoMsiSQL($uriageDenpyoMsi, $where1);
                }
                $cnt += $db->easyExecute($sql, $param);
            }
        }

        // 法友会相殺金額特殊処理
        App_Utils2::adjJuchu($db, $this->_sekoNo, $uriageDenpyoNo, $this->_juchuKakuteiYMD, $this->_gojokaiKbn, $this->_sekoPlanCd, $dataCol, $dataApp, $dataTrnDelCol);

        // 売上伝票データがない場合、登録する
        // 売上伝票データがある場合、更新する
        if ($isAutoSaiban) {
            // 売上伝票テーブル情報を設定する
            $uriageDenpyo = $this->setUriagedenpyoInsertInfo($dataApp);
            // 売上伝票登録SQLを取得する
            list($sql, $param) = $this->insertUriagedenpyoSQL($uriageDenpyo);
        } else {
            // 売上伝票テーブル情報を設定する
            $uriageDenpyo = $this->setUriagedenpyoUpdateInfo($dataApp);
            // 売上伝票更新SQLを取得する
            // 条件部
            $where2['uri_den_no'] = $uriageDenpyoNo;  // 伝票番号
            $where2['seko_no'] = $this->_sekoNo;  // 施行番号
            $where2['seko_no_sub'] = $this->_sekoNoSub;  // 施行番号枝番
            $where2['delete_flg'] = 0;
            list($sql, $param) = $this->updateUriagedenpyoSQL($uriageDenpyo, $where2);
        }
        $cnt += $db->easyExecute($sql, $param);
        // 売上伝票の明細とヘッダーの消費税差額設定処理
        $cnt += $this->setUriageDenpyoAdjTax($uriageDenpyoNo);
        return $cnt;
    }

    /**
     *
     * 売上伝票明細削除SQL
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @return string SQL
     */
    private function deleteUriagedenpyoMsiSQL() {
        // 削除処理は論理削除(deleteflgを更新)する
        // 売上伝票取得SQL
        $sql = "
        UPDATE
            uriage_denpyo_msi
        SET 
            delete_flg = 1
        WHERE
            uri_den_no = :denpyo_no
        AND msi_no = :msi_no
        AND delete_flg = 0
                ";
        return $sql;
    }

    /**
     *
     * 売上伝票明細取得SQL
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @return string SQL
     */
    private function selectUriagedenpyoMsiSQL() {
        // 売上伝票取得SQL
        $sql = "
        SELECT
            uri_den_no
        FROM
            uriage_denpyo_msi
        WHERE
            uri_den_no = :denpyo_no
        AND msi_no = :msi_no
        --AND delete_flg = 0
                ";
        return $sql;
    }

    /**
     *
     * 売上伝票明細テーブル登録情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @return array 売上伝票明細情報
     */
    private function setUriagedenpyoMsiInstertInfo($dataApp, $record) {
        // 売上伝票明細テーブル登録時と更新時の共通情報を設定する
        $uriageDenpyoMsi = $this->setUriagedenpyoMsiComInfo($dataApp, $record);
        $uriageDenpyoMsi['uri_den_no'] = $dataApp['denpyo_no'];  // 売上伝票
        $uriageDenpyoMsi['msi_no'] = $record['msi_no'];  // 受注明細№
        if (isset($record['data_sbt'])) {
            $uriageDenpyoMsi['data_sbt'] = $record['data_sbt'];  // データ種別
        } else {
            // $recordパラメーターは見積書呼び出す時に使用する
            $uriageDenpyoMsi['data_sbt'] = $this->getDataSbt();  // データ種別
        }
        $uriageDenpyoMsi['seko_no'] = $this->_sekoNo;  // 施行番号
        $uriageDenpyoMsi['seko_no_sub'] = $this->_sekoNoSub;  // 施行番号枝番
        if (isset($record['add_kbn'])) {
            $uriageDenpyoMsi['add_kbn'] = $record['add_kbn'];  // 追加区分
        }
        return $uriageDenpyoMsi;
    }

    /**
     *
     * 売上伝票明細テーブル更新情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @return array 売上伝票明細情報
     */
    private function setUriagedenpyoMsiUpdateInfo($dataApp, $record) {
        // 売上伝票明細テーブル登録時と更新時の共通情報を設定する
        $uriageDenpyoMsi = $this->setUriagedenpyoMsiComInfo($dataApp, $record);
        return $uriageDenpyoMsi;
    }

    /**
     *
     * 売上伝票明細テーブル共通情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @return array 売上伝票明細情報
     */
    private function setUriagedenpyoMsiComInfo($dataApp, $record) {
        $uriageDenpyoMsi = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $uriageDenpyoMsi['disp_no'] = $record['disp_no'];  // 表示順
        $uriageDenpyoMsi['denpyo_kbn'] = $record['denpyo_kbn'];  // 伝票区分
        $uriageDenpyoMsi['juchu_ymd'] = $this->_zeiKijunYmd;  // 受注日
        $uriageDenpyoMsi['bumon_cd'] = $record['bumon_cd'];  // 売上部門コード
        $uriageDenpyoMsi['mokuteki_kbn'] = $record['mokuteki_kbn'];  // 使用目的区分
        $uriageDenpyoMsi['dai_bunrui_cd'] = $record['dai_bunrui_cd'];  // 大分類コード
        $uriageDenpyoMsi['chu_bunrui_cd'] = $record['chu_bunrui_cd'];  // 中分類コード
        $uriageDenpyoMsi['shohin_kbn'] = $record['shohin_kbn'];  // 商品区分
        $uriageDenpyoMsi['shohin_cd'] = $record['shohin_cd'];  // 商品コード
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();                        // 2016/12/25 ADD Kayo
        $uriageDenpyoMsi['kaisya_cd'] = $kaisya_cd;                   // 会社コード   2016/12/05 ADD Kayo
        $uriageDenpyoMsi['shohin_bumon_cd'] = $record['shohin_bumon_cd'];

        $uriageDenpyoMsi['shohin_nm'] = $record['shohin_nm'];  // 商品名
        $uriageDenpyoMsi['shohin_tkiyo_nm'] = $record['shohin_tkiyo_nm'];  // 商品摘要名
        if (isset($record['shohin_tkiyo_nm2'])) {
            $uriageDenpyoMsi['shohin_tkiyo_nm2'] = $record['shohin_tkiyo_nm2'];  // 商品摘要名 2015/11/02 mihara
        }
        $uriageDenpyoMsi['juchu_suryo'] = $record['juchu_suryo'];  // 商品数量
        $uriageDenpyoMsi['tani_cd'] = $record['tani_cd'];  // 単位コード
        $uriageDenpyoMsi['uri_tnk'] = $record['juchu_tnk'];  // 単価
        $uriageDenpyoMsi['uri_prc'] = $this->getPrc($record); // 売上金額
        $uriageDenpyoMsi['nebiki_prc'] = $record['nebiki_prc'];  // 値引額
        if (isset($record['gojokai_nebiki_prc'])) {
            $uriageDenpyoMsi['gojokai_nebiki_prc'] = $record['gojokai_nebiki_prc'];  // 互助会値引き
        } else {
            $uriageDenpyoMsi['gojokai_nebiki_prc'] = 0;  // 互助会値引き
            $record['gojokai_nebiki_prc'] = 0;
        }
        $uriageDenpyoMsi['gen_tnk'] = $record['gen_tnk'];  // 原価単価
        $uriageDenpyoMsi['gen_gaku'] = $this->getGenka($record);  // 原価金額
        $uriageDenpyoMsi['arari_gaku'] = $this->getAari($record); // 粗利益額
        $uriageDenpyoMsi['hoshi_umu_kbn'] = $record['hoshi_umu_kbn'];  // 奉仕料有無区分
        $uriageDenpyoMsi['hoshi_ritu_cd'] = $dataApp['hoshi_ritu_cd'];  // 奉仕料率コード
        $uriageDenpyoMsi['hoshi_prc'] = $record['hoshi_prc'];  // 奉仕料金額
        $uriageDenpyoMsi['nonyu_cd'] = $record['nonyu_cd'];  // 納入先コード
        $uriageDenpyoMsi['nonyu_nm'] = $record['nonyu_nm'];  // 納入先名
        $uriageDenpyoMsi['nonyu_knm'] = $record['nonyu_knm'];  // 納入先名カナ
        $uriageDenpyoMsi['nonyu_yubin_no'] = $record['nonyu_yubin_no'];  // 納入先郵便番号
        $uriageDenpyoMsi['nonyu_addr1'] = $record['nonyu_addr1'];  // 納入先住所1
        $uriageDenpyoMsi['nonyu_addr2'] = $record['nonyu_addr2'];  // 納入先住所2
        $uriageDenpyoMsi['nonyu_tel'] = $record['nonyu_tel'];  // 納入先電話番号
        $uriageDenpyoMsi['nonyu_fax'] = $record['nonyu_fax'];  // 納入先FAX
        $uriageDenpyoMsi['nonyu_dt'] = $record['nonyu_dt'];  // 納入予定日
        $uriageDenpyoMsi['siire_cd'] = $record['siire_cd'];  // 仕入先コード
        $uriageDenpyoMsi['siire_lnm'] = $record['siire_lnm'];  // 仕入先名
        $uriageDenpyoMsi['tanto_cd'] = $record['tanto_cd'];  // 担当コード
        // emptyToNull
        $record = Msi_Sys_Utils::emptyToNullArr($record);
        if (isset($record['add_henpin_ymd'])) {
            $uriageDenpyoMsi['add_henpin_ymd'] = $record['add_henpin_ymd'];
        }
        if (isset($record['upgrade_kbn'])) {
            $uriageDenpyoMsi['upgrade_kbn'] = $record['upgrade_kbn'];
        }
        if (isset($record['plan_shohin_cd'])) {
            $uriageDenpyoMsi['plan_shohin_cd'] = $record['plan_shohin_cd'];
        }
        if (isset($record['plan_shohin_nm'])) {
            $uriageDenpyoMsi['plan_shohin_nm'] = $record['plan_shohin_nm'];
        }
        if (isset($record['plan_shohin_bumon_cd'])) {
            $uriageDenpyoMsi['plan_shohin_bumon_cd'] = $record['plan_shohin_bumon_cd'];
        }
        if (isset($record['plan_shohin_suryo'])) {
            $uriageDenpyoMsi['plan_shohin_suryo'] = $record['plan_shohin_suryo'];
        }
        if (isset($record['plan_shohin_tnk'])) {
            $uriageDenpyoMsi['plan_shohin_tnk'] = $record['plan_shohin_tnk'];
        }
        // 受注金額が0円かつ付帯特典、割引額がいずれも0円の場合はNULL
        if ($uriageDenpyoMsi['uri_prc'] == 0 && $uriageDenpyoMsi['nebiki_prc'] == 0 && $uriageDenpyoMsi['gojokai_nebiki_prc'] == 0) {
            $uriageDenpyoMsi['n_free2'] = null;
        } else {
            $uriageDenpyoMsi['n_free2'] = $record['n_free2'];
        }
        // 消費税項目を設定する
        $this->setZeiInfo($dataApp, $uriageDenpyoMsi, $record, $uriageDenpyoMsi['uri_prc'] + $record['hoshi_prc'] + $record['nebiki_prc'] + $record['gojokai_nebiki_prc']);
        return $uriageDenpyoMsi;
    }

    /**
     *
     * 売上伝票テーブル登録情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面の情報
     * @return array 売上伝票情報
     */
    private function setUriagedenpyoInsertInfo($dataApp) {
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();
        $uriageDenpyo = $this->setUriagedenpyoComInfo($dataApp);
        $uriageDenpyo["uri_den_no"] = $dataApp['denpyo_no'];  // 売上伝票№
        $uriageDenpyo["denpyo_no"] = $juchuDenpyoNo;  // 受注伝票№
        $uriageDenpyo["seko_no"] = $this->_sekoNo;  // 施行番号
        $uriageDenpyo["seko_no_sub"] = $this->_sekoNoSub;  // 施行番号枝番
        return $uriageDenpyo;
    }

    /**
     *
     * 売上伝票テーブル更新情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面の情報
     * @return array 売上伝票情報
     */
    private function setUriagedenpyoUpdateInfo($dataApp) {
        $uriageDenpyo = $this->setUriagedenpyoComInfo($dataApp);
        return $uriageDenpyo;
    }

    /**
     *
     * 売上伝票テーブル共通情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @version 2014/09/14 消費税の取得関数を修正 Kayo
     * @param array $dataApp 画面の情報
     * @return array 売上伝票情報
     */
    private function setUriagedenpyoComInfo($dataApp) {
        $uriageDenpyo = array();
        $uriageDenpyo["data_kbn"] = $this->getDataKbn();  // データ区分
        $uriageDenpyo["juchu_ymd"] = $this->_zeiKijunYmd;  // 受注日
        $uriageDenpyo["bumon_cd"] = $this->_bumonCd;  // 売上部門コード
        $uriageDenpyo["tanto_cd"] = App_Utils2::getTantoCd($this->_sekoTantoCd, $this->_selectSekoKihon);  // 担当者コード
        $uriageDenpyo["gojokai_cose_cd"] = $this->_gojokaiCoseCd;  // 互助会コースコード
        $uriageDenpyo["seko_plan_cd"] = $this->_sekoPlanCd;  // 施行プランコード
        $kihon = $this->selectSekoKihon();
        $uriageDenpyo['keijo_ymd'] = $kihon['sougi_ymd'];
        // 回収区分と回収予定日を取得する
        $kaishuInfo = $this->getKashuInfo();
        $uriageDenpyo["kaishu_kbn"] = $kaishuInfo["kaishu_kbn"];  // 回収区分 
//        $uriageDenpyo["kaishu_ymd"] = $kaishuInfo["kaishu_ymd"];  // 回収予定日
        // 金額合計求め処理 
        $uriPrcSum = $this->getUriagePrcSum($dataApp['denpyo_no']);
        $uriageDenpyo["uri_prc_sum"] = $uriPrcSum['uri_prc_sum'];  // 売上金額合計
        $uriageDenpyo["genka_prc_sum"] = $uriPrcSum['genka_prc_sum'];  // 原価金額合計
        $uriageDenpyo["uri_hepn_sum"] = $uriPrcSum['uri_hepn_sum'];  // 売上返品合計
        $uriageDenpyo["uri_nebk_sum"] = $uriPrcSum['uri_nebk_sum'];  // 売上値引合計
        $uriageDenpyo["hoshi_prc_sum"] = $uriPrcSum['hoshi_prc_sum'];  // 奉仕料合計
        $uriageDenpyo["arari_prc"] = $uriPrcSum['arari_prc_sum'];  // 粗利益額
        // 消費税処理 new 2019/04/30 mihara keigen
        $uriagePrcSum2 = $this->getUriageZeiSum2($dataApp['denpyo_no']); // denpyo_no is uri_den_no
        $uriageDenpyo["in_zei_prc"] = $uriagePrcSum2['in_zei_prc']; // 内税消費税額
        $uriageDenpyo["out_zei_prc"] = $uriagePrcSum2['out_zei_prc']; // 外税消費税額
        $uriageDenpyo["szei_katax_taisho_prc"] = $uriagePrcSum2["szei_katax_taisho_prc"];  // 外税課税対象額
        $uriageDenpyo["uzei_katax_taisho_prc"] = $uriagePrcSum2["uzei_katax_taisho_prc"];  // 内税課税対象額
        $uriageDenpyo["hitax_katax_taisho_prc"] = $uriagePrcSum2["hitax_katax_taisho_prc"];  // 非税課税対象額
        // 請求先情報を取得する
        $selectSekyuInfo = App_Utils::getSekoSekyuInfo($this->_sekoNo);
        if (count($selectSekyuInfo) > 0) {
            $uriageDenpyo["sekyu_cd"] = $selectSekyuInfo["sekyu_cd"];  // 請求先コード
            $uriageDenpyo["sekyu_nm"] = $selectSekyuInfo["sekyu_nm"];  // 請求先名
            $uriageDenpyo["sekyu_knm"] = $selectSekyuInfo["sekyu_knm"];  // 請求先名カナ
            $uriageDenpyo["sekyu_soufu_nm"] = $selectSekyuInfo["sekyu_nm"];  // 請求書送付先名
            $uriageDenpyo["sekyu_yubin_no"] = $selectSekyuInfo["yubin_no"];  // 請求先郵便番号
            $uriageDenpyo["sekyu_addr1"] = $selectSekyuInfo["addr1"];  // 請求先住所1
            $uriageDenpyo["sekyu_addr2"] = $selectSekyuInfo["addr2"];  // 請求先住所2
            $uriageDenpyo["sekyu_tel"] = $selectSekyuInfo["tel"];  // 請求先電話番号
            $uriageDenpyo["sekyu_fax"] = $selectSekyuInfo["fax"];  // 請求先FAX
        }
        // 入金額を取得する
        $nyukin = $this->getUriageNyukin();
        // 請求情報
        $uriageDenpyo["seikyu_zan"] = $uriageDenpyo["uri_prc_sum"] + $uriageDenpyo["uri_hepn_sum"] + $uriageDenpyo["uri_nebk_sum"] +
                $uriageDenpyo["hoshi_prc_sum"] + $uriageDenpyo["out_zei_prc"] + $uriageDenpyo["in_zei_prc"] - $nyukin; // 請求残高

        return $uriageDenpyo;
    }

    /**
     *
     * 売上伝票入金額取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/06/27
     * @return 売上伝票の入金額
     */
    private function getUriageNyukin() {
        $nyukin = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            nyukin_prc
        FROM
            uriage_denpyo
        WHERE
            seko_no = :seko_no
        AND delete_flg = 0
        AND seko_no_sub = :seko_no_sub
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'seko_no_sub' => $this->_sekoNoSub));
        if (count($select) > 0) {
            $nyukin = $select['nyukin_prc'];
        }
    }

    /**
     *
     * 売上伝票明細登録SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param array $DenpyoMsi 売上伝票明細テーブル情報
     * @return string SQL
     */
    private function insertUriagedenpyoMsiSQL($DenpyoMsi) {
        list($sql, $param) = DataMapper_Utils::makeInsertSQL("uriage_denpyo_msi", $DenpyoMsi);
        return array($sql, $param);
    }

    /**
     *
     * 売上伝票明細更新SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param array $DenpyoMsi 売上伝票明細テーブル情報
     * @param array $where 条件部
     * @return string SQL
     */
    private function updateUriagedenpyoMsiSQL($DenpyoMsi, $where) {
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo_msi", $DenpyoMsi, $where);
        return array($sql, $param);
    }

    /**
     *
     * 売上伝票登録SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param array $uriageDenpyo 売上伝票テーブル情報
     * @return string SQL
     */
    private function insertUriagedenpyoSQL($uriageDenpyo) {
        list($sql, $param) = DataMapper_Utils::makeInsertSQL("uriage_denpyo", $uriageDenpyo);
        return array($sql, $param);
    }

    /**
     *
     * 売上伝票更新SQL組立処理
     *
     * <AUTHOR> Sai
     * @since 2014/3/7
     * @param array $uriageDenpyo 売上伝票テーブル情報
     * @param array $dataApp 売上伝票画面情報
     * @param array $where 条件
     * @return string SQL
     */
    private function updateUriagedenpyoSQL($uriageDenpyo, $where) {
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $uriageDenpyo, $where);
        return array($sql, $param);
    }

    /**
     *
     * 会社情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2015/02/16
     * @version 2015/11/14 互助会消費税計算区分を追加 Kayo
     * @version 2017/01/03 会社コードを追加 Kayo
     * @return array 会社情報
     */
    protected function getKaisyaInfo($kaisya_cd = null) {
        $db = Msi_Sys_DbManager::getMyDb();
        if (strlen($kaisya_cd) <= 0) { // 2017/01/03 ADD Kayo
            $sql = "
				SELECT
					 ki.kashidashi_kaisyu_day    -- 貸出品の回収日
					,ki.gojyo_tax_keian_kbn		 -- 互助会消費税計算区分 2015/11/14 ADD Kayo
				FROM
					kaisya_info ki
				WHERE
					  ki.delete_flg = 0
					";
            $select = $db->easySelOne($sql);
        } else {
            $sql = "
				SELECT
					 ki.kashidashi_kaisyu_day    -- 貸出品の回収日
					,ki.gojyo_tax_keian_kbn		 -- 互助会消費税計算区分 2015/11/14 ADD Kayo
				FROM
					kaisya_info ki
				WHERE
					  ki.delete_flg = 0
				AND   ki.kaisya_cd  = :kaisya_cd	  
					";
            $select = $db->easySelOne($sql, array('kaisya_cd' => $kaisya_cd));
        }
        return $select;
    }

    /**
     *
     * 施行日程より日程を取得する
     *
     * <AUTHOR> Sai
     * @since 2015/3/9
     * @param string $nitei_kbn 日程区分
     * @return array 施行日程
     */
    protected function getNiteiYmdData($nitei_kbn) {
        $niteiYmd = null;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD') AS nitei_date-- 日程日付のみ
            FROM
                seko_nitei sn
            WHERE
                    sn.seko_no = :seko_no
                AND sn.nitei_kbn = :nitei_kbn
                AND sn.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => $nitei_kbn));
        if (count($select) > 0) {
            $niteiYmd = $select['nitei_date'];
        }
        return $niteiYmd;
    }

    /**
     *
     * 施行日程情報を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2020/xx/xx
     * @param string $nitei_kbn 日程区分
     * @return array 施行日程
     */
    protected function getNiteiData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $dataKbn = $this->getDataKbn();
        if ($dataKbn == self::MOUSHI_KBN_HOUJI) {
            $table_nm = 'seko_nitei_houji';
        } else {
            $table_nm = 'seko_nitei';
        }
        $sql = "
            SELECT sn.*
            FROM $table_nm sn
            WHERE sn.seko_no = :seko_no
                AND sn.delete_flg = 0
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     *
     * 発注書区分に絞って発注管理情報を削除する
     *
     * <AUTHOR> Sai
     * @since 2014/07/25
     * @version 2015/03/17 Juchu_JuchuPhotoより引越し
     * @param Msi_Sys_Db $db db
     * @param string $ha_rp_kbn 発注書区分
     * @return $cnt 処理件数
     */
    protected function deleteHachuInfoByHarpKbn($db, $ha_rp_kbn) {
        // 削除処理は論理削除(deleteflgを更新)する
        $sql = "
        UPDATE
            seko_hachu_info
        SET 
            delete_flg = 1
        WHERE
            seko_no = :seko_no
        AND ha_rp_kbn = :ha_rp_kbn
        AND delete_flg = 0
                ";
        $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo, 'ha_rp_kbn' => $ha_rp_kbn));
        return $cnt;
    }

    /**
     *
     * 施行発注管理作成処理
     *
     * <AUTHOR> Sai
     * @since 2014/07/25
     * @version 2015/03/17 Juchu_JuchuPhotoより引継ぎ
     * @param Msi_Sys_Db $db db
     * @param string $ha_rp_kbn 発注書区分
     * @return $cnt 処理件数
     */
    protected function createHachuInfo($db, $ha_rp_kbn) {
        $cnt = 0;
        $selFreeMst = $this->getFreemstData($ha_rp_kbn);
        if (Msi_Sys_Utils::myCount($selFreeMst) > 0) {
            $shohinCd = $selFreeMst['shohin_cd'];
            // 発注管理情報を取得する
            $dataHachu = $this->getHachuInfoOne($shohinCd);
            $dataApp['denpyo_no'] = $this->getJuchudenpyoNo();
            if (count($dataHachu) > 0) {
                $dataHachu['hachu_kbn'] = $ha_rp_kbn;
                $cnt += $this->saveHachuInfo($db, $dataApp, array($dataHachu));
            }
        }
        return $cnt;
    }

    /**
     *
     * 商品フリーマスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/07/25
     * @version 2015/03/17 Juchu_JuchuPhotoより引越し
     * @param string $frKbn1 区分フリー項目１
     * @return array 商品フリーマスタ情報
     */
    private function getFreemstData($frKbn1) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            sfm.shohin_cd -- 商品コード
        FROM
            shohin_free_mst sfm
        WHERE
            sfm.k_free1 = :k_free1
                ";
        $select = $db->easySelOne($sql, array('k_free1' => $frKbn1));
        return $select;
    }

    /**
     *
     * 発注管理情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/07/25
     * @version 2015/03/17 Juchu_JuchuPhotoより引越し
     * @param string $shohinCd 商品コード
     * @return array 発注管理情報
     */
    private function getHachuInfoOne($shohinCd) {

        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                NULL AS msi_no 
                ,sm.shohin_cd 
                ,skm.shohin_kbn
                ,skm.shohin_kbn_nm
                ,sm.shohin_nm
                ,sm.shohin_tkiyo_nm
                ,sbm.dai_bunrui_cd
                ,sbm.chu_bunrui_cd
                ,0 AS juchu_tnk
                ,1 AS juchu_suryo
                ,0  AS juchu_prc
                ,0 AS nebiki_prc
                ,0 AS gen_tnk
                ,NULL AS nonyu_cd
                ,NULL AS nonyu_nm
                ,NULL AS nonyu_dt
                ,sm.tani_cd
                ,sm.siire_cd 
                ,sbm.hachu_kbn 
            FROM
                shohin_mst sm
                INNER JOIN
                    shohin_bunrui_mst sbm
                ON  (
                        sm.shohin_cd = sbm.shohin_cd
                    AND sbm.delete_flg = 0
                    )
                INNER JOIN
                    shohin_kbn_mst skm
                ON  (sbm.shohin_kbn = skm.shohin_kbn)
            WHERE
                sm.delete_flg = 0
            AND skm.delete_flg = 0
            AND sm.shohin_cd = :shohin_cd
            AND CURRENT_DATE BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd
            AND sm.kaisya_cd   = :kaisya_cd -- 2016/10/06 ADD Kayo   
                ";
        // 複数会社対応 2016/10/06 ADD Kayo
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $select = $db->easySelOne($sql, array('shohin_cd' => $shohinCd
            , 'kaisya_cd' => $curKaisyaCd            // 2016/10/06 ADD Kayo              
        ));
        return $select;
    }

    /**
     *
     * 施行日程より施行場所を取得する
     *
     * <AUTHOR> Okuyama
     * @since 2017/10/03
     * @param string $plan_cd   プランコード
     *         string $Hall_cd   会場コード
     * @return array 施行日程（）
     */
    protected function getHallPlanData($plan_cd, $hall_cd) {
        $retCnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                count(*) AS data_cnt
            FROM
                seko_plan_hall_mst
            WHERE
                    seko_plan_cd  = :plan_cd
                AND kaijyo_cd     = :hall_cd
                AND delete_flg    = 0
                ";
        $select = $db->easySelOne($sql, array('plan_cd' => $plan_cd, 'hall_cd' => $hall_cd));
        if (count($select) > 0) {
            $retCnt = $select['data_cnt'];
        }
        return $retCnt;
    }

    /**
     *
     * 現在設定されている部門が売上部門かどうかチェック
     *
     * <AUTHOR> Tosaka
     * @since 2020/xx/xx
     * @return boolean 
     */
    protected function checkUriBumon($bumon_cd) {
        $flg = true;
        $db = Msi_Sys_DbManager::getMyDb();
        // 部門データ取得
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $bumon_cd));
        if (count($bumonData) == 0) {
            $flg = false;
            return $flg;
        }
        // 会計部門コードが未設定のものは売上未定部門
        if (!isset($bumonData[0]['kaikei_bumon_cd'])) {
            $flg = false;
        }
        return $flg;
    }

    /**
     *
     * 施行契約先情報を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2020/09/30
     * @return array 施行契約先情報
     */
    protected function getKeiyakusakiInfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            ski.seko_no
            ,ski.partner_cd
            ,ski.partner_nm
            ,ski.user_kbn
            ,ski.user_kbn_nm
            ,ski.user_nm
            ,ski.user_last_nm
            ,ski.user_first_nm
            ,ski.user_knm
            ,ski.user_last_knm
            ,ski.user_first_knm
            ,ski.user_kinmusaki_nm
            ,ski.user_department_nm
            ,ski.kigou_no1
            ,ski.kigou_no2
            ,ski.ministries_cd
            ,user_birth_year
            ,user_gengo
            ,user_birth_month
            ,user_birth_day
            ,user_seinengappi_ymd
            ,user_seinengappi_ymd_y
            ,kihon.v_free31 AS shokai_partner_cd -- 紹介契約先コード
            ,kihon.v_free32 AS shokai_partner_nm -- 紹介契約先名
            ,ski.houkoku_kbn
            ,ski.kokuchi_kbn
            ,ski.ringi_kbn
            ,ski.kaiin_no
            ,ski.member_no
            ,ski.moushi_keii
        FROM
            seko_keiyakusaki_info ski
            LEFT JOIN seko_kihon_info kihon
                ON  kihon.seko_no    = ski.seko_no
                AND kihon.delete_flg = 0
        WHERE
            ski.seko_no = :seko_no
        AND ski.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        $cdGengo = DataMapper_EraMst::getCodeNmEra();
        if (isset($select['user_birth_year'])) {
            $keyIndex = array_search($select['user_birth_year'], array_column($cdGengo, 'kbn_value_cd_num'));
            $result = $cdGengo[$keyIndex];
            $select['user_wa_year'] = $result['kbn_value_snm'];
        }
        return $select;
    }

    /**
     *
     * 施行基本情報のステータス区分を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2020/09/30
     * @return string ステータス区分
     */
    protected function getSekoStatusKbn() {
        $db = Msi_Sys_DbManager::getMyDb();
        $status_kbn = null;
        $sql = "
        SELECT ski.status_kbn
        FROM seko_kihon_info ski
        WHERE ski.seko_no = :seko_no
        AND ski.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $status_kbn = $select['status_kbn'];
        }
        return $status_kbn;
    }

    /**
     *
     * オーダーメイドの試算APIの結果を返す
     * 
     * <AUTHOR> Tosaka
     * @since 2021/xx/xx
     * @return array 試算APIの戻り値
     */
    protected function getOmSimulationData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $juchuPrcSum = $this->getJuchuPrcSum($this->getJuchudenpyoNo());
        $juchuPrcSum2 = $this->getJuchuZeiSum2($this->getJuchudenpyoNo());
        $mitsuPrc = $juchuPrcSum['juchu_prc_sum'] + $juchuPrcSum['juchu_hepn_sum'] + $juchuPrcSum['juchu_nebk_sum'] + $juchuPrcSum['hoshi_prc_sum'] - $juchuPrcSum2['in_zei_prc'];
        $kihonInfo = $this->selectSekoKihon();
        $gojokaiCol = DataMapper_SekoGojokaiMember::find($db, array('seko_no' => $this->_sekoNo, 'yoto_kbn' => 2));
        // 試算区分によって入力値が変わる
        $planCd = '';
        $haraiNo = null;
        $kakePrc = null;
        $iniPrc = null;
        $gojoHaraiPrc = 0;
        $gojoWariPrc = 0;
        $gojoUsePrc = 0;
        $gojokaiData = array();
        if (count($gojokaiCol) > 0) {
            foreach ($gojokaiCol as $onerow) {
                $gojokaiData[] = array('ContractNo' => trim($onerow['kain_no']));
            }
        }
        if (isset($kihonInfo['om_plan_cd']) && strlen($kihonInfo['om_plan_cd']) > 0) {
            $planCd = $kihonInfo['om_plan_cd'];
        }
        $_reqData = array('OrdermadeNo' => $this->_sekoNo
            , 'PlanCode' => $planCd
            , 'TerminationValue' => (int) $mitsuPrc
//                , 'InitialPayValue' => $iniPrc
            , 'ContractLists' => $gojokaiData
        );
        if ($kihonInfo['om_est_method_kbn'] === '1') {    //   支払回数
            $_reqData['PaymentNum'] = (int) $kihonInfo['om_est_harai_no'];
        } else {    // 月額掛金
            $_reqData['Installment'] = (int) $kihonInfo['om_est_kake_prc'];
        }
        // サンプル
//        $_reqData = array('OrdermadeNo' => '12345678'
//                , 'PlanCode' => ''
//                , 'TerminationValue' => 300000
//                , 'Installment' => 0
//                , 'PaymentNum' => 24
//                , 'InitialPayValue' => 0
//                , 'ContractLists' => array()
//                );
        // 見積金額が0円の場合はコールしない
        if ($mitsuPrc != 0) {
            list($errMsg, $aResult, $resData) = Logic_HakuApi_MmbrApiOrdermadeSimulation::search($_reqData);
        } else {
            $errMsg = null;
            $aResult = array();
            $resData = array();
        }
//        if (strlen($errMsg) > 0) {
//            Msi_Sys_Utils::err('会員管理連携：オーダーメイド試算エラー==>' . Msi_Sys_Utils::dump(array('登録データ' => Msi_Sys_Utils::json_encode($_reqData), 'エラーメッセージ' => $errMsg)));
//        } else {
//            Msi_Sys_Utils::info('会員管理連携：オーダーメイド試算==>' . Msi_Sys_Utils::dump(array('登録データ' => Msi_Sys_Utils::json_encode($_reqData), '返却データ' => $resData)));
//        }
        $rtn = array($errMsg, $aResult, $resData);
        return $rtn;
    }

    /**
     *
     * 搬送伝票明細から受注伝票明細作成処理
     *
     * <AUTHOR> Tosaka
     * @since 2021/04/xx
     * @return array 施行契約先情報
     */
    protected function makeJuchuMsiDataFromHanso($kihonInfo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen(); // '2019/10/01'; // 軽減税率適用開始日 keigen
        if (isset($kihonInfo['sougi_ymd'])) {
            $kijun_ymd = $kihonInfo['sougi_ymd'];
        } else {
            $kijun_ymd = Msi_Sys_Utils::getDate();
        }
        $msiData = array();
        // 物品販売の商品を取得する
        $select1 = DataMapper_Hanso_HansoDenpyoMsi::find($db, array('uketsuke_no' => $this->_sekoNo,
                    '__raw_1' => "(msi_kbn BETWEEN " . self::OTHER_BUNRUI_KBN_BUPPIN_ST . " AND " . self::OTHER_BUNRUI_KBN_BUPPIN_ED . " OR msi_kbn = 0)")); // 0:物品販売,9001
        // 値引・高速料金の商品を取得する
        $select2 = DataMapper_Hanso_HansoDenpyoMsi::find($db, array('uketsuke_no' => $this->_sekoNo,
                    '__raw_1' => "(msi_kbn IN ('" . self::OTHER_BUNRUI_KBN_KOUSOKUPRC . "', '" . self::OTHER_BUNRUI_KBN_NEBIKIPRC . "'))"));
        // 申込区分が搬送の時は作業料を取得する
        if ($this->_moushiKbn === self::MOUSHI_KBN_HANSO) {
            $select3 = DataMapper_Hanso_HansoDenpyoMsi::find($db, array('uketsuke_no' => $this->_sekoNo,
                        '__raw_1' => "(msi_kbn IN ('" . self::OTHER_BUNRUI_KBN_SAGYOPRC . "'))"));
        } else {
            $select3 = array();
        }
        $msiArray = array_merge($select1, $select2, $select3);
        $add_kbn = self::ADD_KBN_HB;
        if (count($msiArray) > 0) {
            foreach ($msiArray as $value) {
                $sql = "
                SELECT
                    msi_no              -- 受注明細№
                    ,0 AS disp_no       -- 表示順
                    ,{$add_kbn}  AS add_kbn       -- 追加区分
                    ,1 AS data_sbt      -- データ種別
                    ,'{$kihonInfo['bumon_cd']}' AS bumon_cd -- 部門コード
                    ,1 AS denpyo_kbn    -- 伝票区分
                    ,shohin_cd          -- 商品コード
                    ,shohin_cd AS item  -- 商品コード
                    ,shohin_bumon_cd    -- 商品部門コード
                    ,shohin_kbn         -- 商品区分
                    ,shohin_kbn_nm	-- 商品区分名
                    ,shohin_nm		-- 商品名称
                    ,shohin_tkiyo_nm    -- 商品摘要
                    ,1 AS mokuteki_kbn  -- 目的区分
                    ,dai_bunrui_cd	-- 大分類コード
                    ,chu_bunrui_cd	-- 中分類コード
                    ,juchu_tnk		-- 単価
                    ,juchu_tnk AS price	-- 単価
                    ,juchu_suryo	-- 数量
                    ,juchu_suryo AS quantity -- 数量
                    ,juchu_tnk * juchu_suryo + gojokai_nebiki_prc AS juchu_gokei -- 受注金額合計
                    ,juchu_tnk * juchu_suryo AS juchu_prc -- 受注金額
                    ,nebiki_prc		-- 値引額
                    ,gojokai_nebiki_prc	-- 互助会値引額
                    ,gen_tnk		-- 原価単価
                    ,shohin_type	-- 商品タイプ
                    ,nonyu_nm		-- 納入場所
                    ,nonyu_dt		-- 納入予定日
                    ,nm_input_kbn	-- 名称入力区分
                    ,tani_cd		-- 単位コード
                    ,zei_kbn            -- 売上課税区分
                    ,uri_kamoku_cd      -- 科目コード
                    ,siire_cd           -- 仕入コード
                    ,siire_lnm          -- 仕入名
                    ,tnk_chg_kbn        -- 売上単価変更区分
                    ,hachu_kbn          -- 発注書区分
                    ,hoshi_umu_kbn	-- 奉仕料有無区分
                    ,hoshi_prc_tnk * juchu_suryo hoshi_prc  -- 奉仕料
                    ,NULL AS nonyu_cd   -- 納入先コード 
                    ,NULL AS nonyu_knm  -- 納入先名カナ
                    ,NULL AS nonyu_yubin_no -- 納入先郵便番号
                    ,NULL AS nonyu_addr1    -- 納入先住所1
                    ,NULL AS nonyu_addr2    -- 納入先住所2
                    ,NULL AS nonyu_tel  -- 納入先電話番号
                    ,NULL AS nonyu_fax  -- 納入先FAX
                    ,zei_cd             -- 消費税コード
                    ,reduced_tax_rate	-- 軽減税率区分
                    ,0 AS upgrade_kbn  -- アップグレード区分
                    ,other_bunrui_kbn
                FROM
                    (
                        SELECT
                            NULL AS msi_no
                            ,row_number () OVER () AS disp_no 
                            ,sm.shohin_cd
                            ,sm.bumon_cd AS shohin_bumon_cd
                            ,skm.shohin_kbn
                            ,skm.shohin_kbn_nm
                            ,sm.shohin_nm
                            ,sm.shohin_tkiyo_nm
                            ,sbm.dai_bunrui_cd
                            ,sbm.chu_bunrui_cd
                            ,{$value['uri_tnk']} AS juchu_tnk
                            ,{$value['juchu_suryo']} AS juchu_suryo
                            ,{$value['nebiki_prc']} AS nebiki_prc
                            ,0 AS gojokai_nebiki_prc
                            ,COALESCE (stm.siire_tnk ,0)  AS gen_tnk
                            ,NULL AS shohin_type
                            ,NULL AS nonyu_nm
                            ,NULL AS nonyu_dt
                            ,sm.nm_input_kbn
                            ,sm.tani_cd
                            ,sm.uri_zei_kbn AS zei_kbn
                            ,sm.siire_cd 
                            ,sim.siire_lnm
                            ,sm.hoshi_umu_kbn
                            ,sm.tnk_chg_kbn    
                            ,sbm.hachu_kbn
                            ,sm.uri_kamoku_cd
                            ,0 AS hoshi_prc_tnk -- 奉仕料 非対応
                            ,NULL AS nonyu_cd   -- 納入先コード 
                            ,NULL AS nonyu_knm  -- 納入先名カナ
                            ,NULL AS nonyu_yubin_no -- 納入先郵便番号
                            ,NULL AS nonyu_addr1    -- 納入先住所1
                            ,NULL AS nonyu_addr2    -- 納入先住所2
                            ,NULL AS nonyu_tel  -- 納入先電話番号
                            ,NULL AS nonyu_fax  -- 納入先FAX
                            ,0 AS data_status   -- データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
                            ,0 AS order_flg     -- 発注済み 0:未発注 1:発注済み
                            ,z.zei_cd       
                            ,CASE WHEN '$kijun_ymd' < '$keigenBgnYmd' THEN 1
                                  ELSE COALESCE(sm.uri_reduced_tax_rate, skm.uri_reduced_tax_rate, 1) END AS reduced_tax_rate -- keigen 
                            ,sbm.other_bunrui_kbn
                        FROM
                            shohin_mst sm
                            INNER JOIN
                                shohin_bunrui_mst sbm
                            ON  (
                                    sm.shohin_cd = sbm.shohin_cd
                                AND  sm.bumon_cd = sbm.bumon_cd
                                AND sbm.dai_bunrui_cd = :dai_bunrui_cd
                                AND sbm.chu_bunrui_cd = :chu_bunrui_cd
                                AND sbm.shohin_kbn = :shohin_kbn
                                AND sbm.delete_flg = 0
                                )
                            INNER JOIN
                                shohin_kbn_mst skm
                            ON  (sbm.shohin_kbn = skm.shohin_kbn)
                            LEFT OUTER JOIN
                                shohin_tanka_mst stm
                            ON  (
                                    sm.shohin_cd = stm.shohin_cd
                                AND  sm.bumon_cd =  stm.bumon_cd
                                AND CURRENT_DATE BETWEEN stm.tekiyo_st_date AND stm.tekiyo_ed_date
                                AND ( TO_CHAR(stm.tekiyo_st_date,'YYYY/MM/DD') <= '$kijun_ymd' AND TO_CHAR(stm.tekiyo_ed_date,'YYYY/MM/DD') >= '$kijun_ymd' )
                                AND stm.delete_flg = 0
                                )
                            LEFT OUTER JOIN
                                siire_mst sim
                            ON  (
                                    sm.siire_cd = sim.siire_cd
                                AND sim.delete_flg = 0
                                )
                            LEFT JOIN zei_mst z		--  keigen
                                    ON '$kijun_ymd' BETWEEN TO_CHAR(z.tekiyo_st_date,'YYYY/MM/DD') AND TO_CHAR(z.tekiyo_ed_date,'YYYY/MM/DD')
                                    AND	z.delete_flg	= 0
                                    AND z.kaisya_cd = sm.kaisya_cd
                                    AND z.reduced_tax_rate = (CASE WHEN '$kijun_ymd' < '$keigenBgnYmd' THEN 1
                                        ELSE COALESCE(sm.uri_reduced_tax_rate, skm.uri_reduced_tax_rate, 1) END)
                        WHERE
                            sm.delete_flg = 0
                        AND skm.delete_flg = 0
                        AND TO_CHAR(sm.hanbai_st_ymd,'YYYY/MM/DD') <= '$kijun_ymd' AND TO_CHAR(sm.hanbai_end_ymd,'YYYY/MM/DD') >= '$kijun_ymd'
                        AND sm.shohin_cd = :shohin_cd
                        AND sm.bumon_cd = :shohin_bumon_cd
                    ) M
                ORDER BY
                    disp_no
                        ";
                $shohinSelect = $db->easySelOne($sql, array(
                    'shohin_cd' => $value['shohin_cd'],
                    'shohin_bumon_cd' => $value['shohin_bumon_cd'],
                    'dai_bunrui_cd' => $value['dai_bunrui_cd'],
                    'chu_bunrui_cd' => $value['chu_bunrui_cd'],
                    'shohin_kbn' => $value['shohin_kbn'],
                ));
                // 搬送伝票 取得
//                $hansoDen = DataMapper_Hanso_HansoDenpyo::findOne($db, array('hanso_den_no' => $value['hanso_den_no']));
//                if ($shohinSelect['other_bunrui_kbn'] == 9001) { // 搬送物販の商品のみ(商品分類マスタのその他分類コード9100の時のみ)
//                    // コード名称8574搬送料金利益割合の備考に設定した仕入先コードを設定
//                    list($wk_siire_cd, $wk_siire_lnm) = static::_hansoBupanSiireInfo($db, $hansoDen['seko_kaisya_cd']);
//                    if ( $wk_siire_cd !== null ) {
//                        $shohinSelect['siire_cd']  = $wk_siire_cd;
//                        $shohinSelect['siire_lnm'] = $wk_siire_lnm;
//                    } else {
//                        $shohinSelect['siire_cd']  = null;
//                        $shohinSelect['siire_lnm'] = null;
//                    }
//                }
                $msiData[] = $shohinSelect;
            }
        }
        return $msiData;
    }

    /**
     * コード名称8574搬送料金利益割合の備考に設定した仕入先を取得する
     *
     * <AUTHOR> Tosaka
     * @since  2021/xx/xx
     * @param  Msi_Sys_Db $db db
     * @param  string   $seko_kaisya_cd
     * @param  array    $hansoMsiRec
     * @return array(<siire_cd|null)>, <siire_lnm|null>)
     */
    protected function _hansoBupanSiireInfo($db, $seko_kaisya_cd) {
        $rec = Logic_Hanso_HansoUtils::getHansoRyoukinProfitRec($seko_kaisya_cd, $db);
        if ($rec) {
            $siire_cd = Msi_Sys_Utils::trim($rec['biko']);
            $siireRec = DataMapper_Siire::findOne($db, array('kaisya_cd' => '00000000', 'siire_cd' => $siire_cd));
            if ($siireRec) {
                $siire_lnm = $siireRec['siire_lnm'];
                return array($siire_cd, $siire_lnm);
            }
        }
        return array(null, null);
    }

    /**
     * 受注明細取得処理
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @return array 受注明細
     */
    protected function getJuchuMsiData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $denpyo_no = $this->getJuchudenpyoNo();
        $sql = "
        SELECT *
        FROM juchu_denpyo_msi
        WHERE denpyo_no = :denpyo_no
            AND delete_flg = 0
                ";
        $select = $db->easySelect($sql, array('denpyo_no' => $denpyo_no));
        return $select;
    }

    /**
     * 売上明細取得処理
     *
     * <AUTHOR> Tosaka
     * @since  2020/xx/xx
     * @return array 売上明細
     */
    protected function getUriageMsiData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $uri_den_no = $this->getUriagedenpyoNo();
        $sql = "
        SELECT *
            ,uri_tnk AS juchu_tnk
        FROM uriage_denpyo_msi
        WHERE uri_den_no = :uri_den_no
            AND delete_flg = 0
                ";
        $select = $db->easySelect($sql, array('uri_den_no' => $uri_den_no));
        return $select;
    }

    /**
     *
     * 互助会コースマスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/21
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 互助会コースマスタ
     */
    protected function getGojokaiCouseMst() {
        $db = Msi_Sys_DbManager::getMyDb();
        $bumon_cd = null;
        if(strlen($this->_bumonCd) > 0){
            $bumon_cd = $this->_bumonCd;
        }else{
            $bumon_cd = App_Utils::getCtxtHallWithKaisya();
        }
        // 部門参照権限
        $bumon_ref_cd = null;
        $bumon_ref = App_Utils2::getBumonRef(4, $bumon_cd, $db);
        if(Msi_Sys_Utils::myCount($bumon_ref) > 0){
            $bumon_ref_cd = $bumon_ref[0];
        }
        
        $dataGojokaiCouseMst = array();
        $sql = "
            SELECT
                gcm.gojokai_cose_cd     -- 互助会コースコード
                ,gcm.gojokai_cose_iw    -- 互助会コースコード頭文字
                ,gojokai_prc            -- 互助会金額
                ,harai_yotei_cnt        -- 回数
                ,gojokai_kbn            -- 互助会区分
                ,yoto1_flg              -- 葬送儀礼
                ,yoto2_flg              -- 返礼品充当
                ,yoto3_flg              -- 壇払い充当
                ,yoto4_flg              -- 法事
                ,yoto5_flg              -- 予備１
                ,yoto6_flg              -- 予備２
                ,yoto7_flg              -- 予備３
                ,yoto8_flg              -- 予備４
                ,yoto9_flg              -- 予備５
                ,multiple1_flg          -- 葬送儀礼
                ,multiple2_flg          -- 返礼品充当
                ,multiple3_flg          -- 壇払い充当
                ,multiple4_flg          -- 法事
                ,multiple5_flg          -- 予備１
                ,multiple6_flg          -- 予備２
                ,multiple7_flg          -- 予備３
                ,multiple8_flg          -- 予備４
                ,multiple9_flg          -- 予備５
                ,plan_convert_prc       -- 互助会プラン換算金額
                ,join_convert_cnt       -- 互助会加入換算口数
                ,cnm.kbn_value_cd_num AS gojokai_group_no -- 互助会グループ番号　
                ,gcm.monthly_gaku       -- 月額
            FROM
                gojokai_couse_mst gcm
            LEFT JOIN  code_nm_mst cnm 
            ON gcm.gojokai_cose_iw = cnm.kbn_value_cd
            AND cnm.code_kbn       = '1610'
            WHERE CURRENT_DATE BETWEEN gcm.tekiyo_st_date AND gcm.tekiyo_ed_date
            AND gcm.delete_flg = 0
            AND gcm.group_cd   = :group_cd
            ORDER BY gcm.disp_no
                ";
        $select = $db->easySelect($sql, array('group_cd' => $bumon_ref_cd));
        // 施行互助会加入者に存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $dataGojokaiCouseMst[$select[$i]['gojokai_cose_iw']] = $select[$i];
            }
        }
        return $dataGojokaiCouseMst;
    }

    /**
     *
     * 施行互助会加入者を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 施行互助会加入者情報
     */
    protected function getGojokaiMember($row = 5) {

        $dataGojokaiMember = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                gm.seko_no          -- 施行番号
                ,gm.kain_no         -- 会員番号
                ,gm.gojokai_cose_cd -- 互助会コースコード
                ,gm.kanyu_nm        -- 加入者名
                ,gm.yoto_kbn        -- 用途
                ,gm.harai_gaku      -- 払込金額
                ,gm.wari_gaku       -- 前納割引額
                ,gm.harai_no        -- 払込回数
                ,TO_CHAR(gm.kanyu_dt ,'YYYY/MM/DD') AS kanyu_dt-- 加入年月日
                ,TO_CHAR(gm.zei_kijyn_ymd ,'YYYY/MM/DD') AS zei_kijyn_ymd-- 消費税基準日
                ,gm.zei_cd          -- 消費税コード
                ,gm.keiyaku_gaku    -- 契約金額
                ,gm.cose_chg_gaku   -- コース変更差額金
                ,gm.early_use_cost  -- 早期利用費
                ,gm.early_use_cost_zei  -- 早期利用費消費税
                ,gm.early_use_cost_zei_cd  -- 早期利用費消費税コード
                ,gm.meigi_chg_cost  -- 名義変更手数料
                ,gm.meigi_chg_cost_zei  -- 名義変更手数料消費税
                ,gm.meigi_chg_cost_zei_cd  -- 名義変更手数料消費税コード
                ,gm.course_snm_cd   -- 互助会コース名（イニシャル）
                ,gm.v_free1 AS cur_cd   -- 現況コード
                ,gm.v_free2 AS other_cose_nm   -- 他社時コース名
                ,gm.v_free3 -- OMプランコード
                ,gm.v_free10
                ,gm.v_free11
                ,gm.v_free12
                ,gm.v_free13
                ,gm.v_free14
                ,gm.v_free15
                ,gm.v_free16
                ,gm.v_free17
                ,gm.free_kbn1 AS kaiin_info_kbn   -- 会員情報区分
                ,gm.kake_zei_rtu
                ,gm.kake_zei_sagaku
                ,gm.cif_no
                ,gm.waribiki_gaku
                ,gm.kanyu_tax
                ,gm.n_free1
                ,gm.n_free2
                ,gm.n_free3
                ,gm.n_free4
                ,gm.n_free5
                ,gm.riyoken
                ,gm.point
                ,gm.warimashi_gaku
            FROM
                seko_gojokai_member gm
                LEFT JOIN gojokai_couse_mst gcm
                    ON
                    (
                        gm.gojokai_cose_cd = gcm.gojokai_cose_cd
                    AND CURRENT_DATE BETWEEN gcm.tekiyo_st_date AND gcm.tekiyo_ed_date
                    AND gcm.delete_flg = 0
                    )
            WHERE
                    gm.seko_no = :seko_no
                AND gm.delete_flg = 0
            ORDER BY
                gm.yoto_kbn 
                ,gm.kanyu_dt
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        // 施行互助会加入者に存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $oneRowData = array();
                $oneRowData['seko_no'] = $select[$i]['seko_no'];
                $oneRowData['kain_no'] = trim($select[$i]['kain_no']);
                $oneRowData['gojokai_cose_cd'] = $select[$i]['gojokai_cose_cd'];
                $oneRowData['kanyu_nm'] = $select[$i]['kanyu_nm'];
                $oneRowData['yoto_kbn'] = $select[$i]['yoto_kbn'];
                $oneRowData['harai_gaku'] = $select[$i]['harai_gaku'];
                $oneRowData['wari_gaku'] = $select[$i]['wari_gaku'];
                $oneRowData['harai_no'] = $select[$i]['harai_no'];
                $oneRowData['kanyu_dt'] = $select[$i]['kanyu_dt'];
                $oneRowData['zei_kijyn_ymd'] = $select[$i]['zei_kijyn_ymd'];
                $oneRowData['keiyaku_gaku'] = $select[$i]['keiyaku_gaku'];
                $oneRowData['cose_chg_gaku'] = $select[$i]['cose_chg_gaku'];
                $oneRowData['early_use_cost'] = $select[$i]['early_use_cost'];
                $oneRowData['early_use_cost_disp'] = $select[$i]['early_use_cost'];
                $oneRowData['early_use_cost_zei'] = $select[$i]['early_use_cost_zei'];
                $oneRowData['early_use_cost_zei_disp'] = $select[$i]['early_use_cost_zei'];
                $oneRowData['early_use_cost_zei_cd'] = $select[$i]['early_use_cost_zei_cd'];
                $oneRowData['meigi_chg_cost'] = $select[$i]['meigi_chg_cost'];
                $oneRowData['meigi_chg_cost_zei'] = $select[$i]['meigi_chg_cost_zei'];
                $oneRowData['meigi_chg_cost_disp'] = $select[$i]['meigi_chg_cost'] + $select[$i]['meigi_chg_cost_zei'];
                $oneRowData['meigi_chg_cost_zei_cd'] = $select[$i]['meigi_chg_cost_zei_cd'];
                $oneRowData['course_snm_cd'] = $select[$i]['course_snm_cd'];
                $oneRowData['kaiin_info_kbn'] = $select[$i]['kaiin_info_kbn'];
                $oneRowData['cur_cd'] = $select[$i]['cur_cd'];
                $oneRowData['v_free10'] = $select[$i]['v_free10'];
                $oneRowData['v_free11'] = $select[$i]['v_free11'];
                $oneRowData['v_free12'] = $select[$i]['v_free12'];
                $oneRowData['v_free13'] = $select[$i]['v_free13'];
                $oneRowData['v_free14'] = $select[$i]['v_free14'];
                $oneRowData['v_free15'] = $select[$i]['v_free15'];
                $oneRowData['v_free16'] = $select[$i]['v_free16'];
                $oneRowData['v_free17'] = $select[$i]['v_free17'];
                $oneRowData['zei_cd'] = $select[$i]['zei_cd'];
                $oneRowData['kake_zei_rtu'] = $select[$i]['kake_zei_rtu'];
                $oneRowData['kake_zei_sagaku'] = $select[$i]['kake_zei_sagaku'];
                $oneRowData['other_cose_nm'] = $select[$i]['other_cose_nm'];
                $oneRowData['waribiki_gaku'] = $select[$i]['waribiki_gaku'];
                $oneRowData['kanyu_tax'] = $select[$i]['kanyu_tax'];
                $oneRowData['kanyu_tax_disp'] = $select[$i]['kanyu_tax'];
                $oneRowData['n_free1'] = $select[$i]['n_free1'];
                $oneRowData['n_free2'] = $select[$i]['n_free2'];
                $oneRowData['n_free3'] = $select[$i]['n_free3'];
                $oneRowData['n_free4'] = $select[$i]['n_free4'];
                $oneRowData['v_free3'] = $select[$i]['v_free3'];
                $oneRowData['zan_gaku'] = $select[$i]['keiyaku_gaku'] - $select[$i]['harai_gaku'] - $select[$i]['wari_gaku'] - $select[$i]['waribiki_gaku'] - $select[$i]['n_free3'];
                $oneRowData['riyoken'] = $select[$i]['riyoken'];
                $oneRowData['point'] = $select[$i]['point'];
                $oneRowData['warimashi_gaku'] = $select[$i]['warimashi_gaku'];
                $oneRowData['n_free5'] = $select[$i]['n_free5'];
                $dataGojokaiMember[$i] = $oneRowData;
            }
        }
        // 5レコード返す
        while (count($dataGojokaiMember) < $row) {
            array_push($dataGojokaiMember, array());
        }
        return $dataGojokaiMember;
    }

    /**
     * 施行互助会加入者保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return int 更新件数
     */
    protected function saveGojokaiMember($db, $dataGojokaiMemberCol) {

        // 削除→登録する
        $sql = "
            DELETE
                FROM
                    seko_gojokai_member
            WHERE
                    seko_no = :seko_no 
                ";
        $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo));
        foreach ($dataGojokaiMemberCol as $oneRow) {
            $except = array();
            array_push($except, 'gojokai_kbn');
            array_push($except, 'kaiin_info_kbn');
            array_push($except, 'cur_cd');
            array_push($except, 'delete_check');
            array_push($except, 'zankin');
            array_push($except, 'shohizei_1');
            array_push($except, 'use_prc');
            array_push($except, 'other_cose_nm');
            array_push($except, 'zan_gaku');
            array_push($except, 'early_use_cost_disp');
            array_push($except, 'early_use_cost_zei_disp');
            array_push($except, 'meigi_chg_cost_disp');
            array_push($except, 'kanyu_tax_disp');
            $oneRow['seko_no'] = $this->_sekoNo;
            // emptyToNull
            $oneRow = Msi_Sys_Utils::emptyToNullArr($oneRow);
            $oneRow['v_free1'] = $oneRow['cur_cd'];
            $oneRow['free_kbn1'] = $oneRow['kaiin_info_kbn'];
            $oneRow['v_free2'] = $oneRow['other_cose_nm'];
            $oneRow['v_free10'] = $oneRow['v_free10'];
            $oneRow['v_free11'] = $oneRow['v_free11'];
            $oneRow['v_free12'] = $oneRow['v_free12'];
            $oneRow['v_free13'] = $oneRow['v_free13'];
            $oneRow['v_free14'] = $oneRow['v_free14'];
            $oneRow['v_free15'] = $oneRow['v_free15'];
            $oneRow['v_free16'] = $oneRow['kain_no'];
            $oneRow['v_free17'] = $oneRow['v_free17'];
            $oneRow['minou_gaku'] = $oneRow['keiyaku_gaku'] - $oneRow['harai_gaku'] - $oneRow['wari_gaku'];    // 未納金額
            $oneRow['minou_no'] = $oneRow['minou_no'];    // 未納回数
            $zeiData = App_ClsTaxLib::GetTaxInfoZeiCd($db, $oneRow['zei_cd']);
            $oneRow['kake_zei_rtu'] = $zeiData['zei_rtu'];
            $oneRow['kake_zei_sagaku'] = $oneRow['keiyaku_gaku'] * ($oneRow['kake_zei_rtu'] / 100); // 掛金税
            // 他社の場合はharai_no = n_free2
            if ($oneRow['kaiin_info_kbn'] == '2') {
                $oneRow['n_free2'] = $oneRow['harai_no'];
            }
            // 税抜きにして保存する
            $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_selKijunYmd);
            $oneRow['early_use_cost'] = $oneRow['early_use_cost_disp'];
            $oneRow['early_use_cost_zei'] = $oneRow['early_use_cost_disp'] * ($taxInfo['zei_rtu'] / 100);
            $oneRow['early_use_cost_zei_cd'] = $taxInfo['zei_cd'];
            $oneRow['meigi_chg_cost'] = $oneRow['meigi_chg_cost_disp'] / (1 + $taxInfo['zei_rtu'] / 100);
            $oneRow['meigi_chg_cost_zei'] = $oneRow['meigi_chg_cost_disp'] - $oneRow['meigi_chg_cost'];
            $oneRow['meigi_chg_cost_zei_cd'] = $taxInfo['zei_cd'];
            // コース施行以外
            if($oneRow['yoto_kbn'] != '1'){
                $oneRow['kanyu_tax'] = 0;           // 加入時消費税
                $oneRow['wari_gaku_tax'] = 0;       // 前納割引消費税
                $oneRow['early_use_cost'] = 0;      // 早期利用費
                $oneRow['early_use_cost_zei'] = 0;  // 早期利用費消費税
            }
            // 施行互助会加入者登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_gojokai_member", $oneRow, $except);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     *
     * 施行互助会加入者を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 施行互助会加入者情報
     */
    protected function getKojinInfo($row = 4) {

        $dataKojinInfo = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $gengo = DataMapper_EraMst::getCodeNmEra();
        $sql = "
            SELECT skih.*
                ,TO_CHAR(skih.hk_death_ymd, 'YYYY/MM/DD') AS hk_death_ymd
            FROM seko_kojin_info_houji skih
            WHERE skih.seko_no = :seko_no
                AND skih.delete_flg = 0
            ORDER BY skih.seq_no
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        // 施行故人情報法事が存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $oneRowData = array();
                if (isset($select[$i]['hk_birth_year'])) {
                    $keyIndex = array_search($select[$i]['hk_birth_year'], array_column($gengo, 'kbn_value_cd_num'));
                    $result = $gengo[$keyIndex];
                    $oneRowData['hk_wa_year'] = $result['kbn_value_snm'];
                }
                if (isset($select[$i]['hk_birth_year']) && isset($select[$i]['hk_birth_month']) && isset($select[$i]['hk_birth_day'])) {
                    $select[$i]['hk_seinengappi_ymd_y'] = $select[$i]['hk_birth_year'] . '-' . $select[$i]['hk_birth_month'] . '-' . $select[$i]['hk_birth_day'];
                }
                $oneRowData['seko_no'] = $select[$i]['seko_no'];
                $oneRowData['seq_no'] = $select[$i]['seq_no'];
                $oneRowData['sekohoyo_kbn'] = $select[$i]['sekohoyo_kbn'];
                $oneRowData['hk_last_nm'] = $select[$i]['hk_last_nm'];
                $oneRowData['hk_first_nm'] = $select[$i]['hk_first_nm'];
                $oneRowData['hk_last_knm'] = $select[$i]['hk_last_knm'];
                $oneRowData['hk_first_knm'] = $select[$i]['hk_first_knm'];
                $oneRowData['hk_kaimyo'] = $select[$i]['hk_kaimyo'];
                $oneRowData['hk_death_ymd'] = $select[$i]['hk_death_ymd'];
                $oneRowData['hk_cif_no'] = $select[$i]['hk_cif_no'];
                $oneRowData['hk_sex_kbn'] = $select[$i]['hk_sex_kbn'];
                $oneRowData['hk_gengo'] = $select[$i]['hk_gengo'];
                $oneRowData['hk_birth_year'] = $select[$i]['hk_birth_year'];
                $oneRowData['hk_birth_month'] = $select[$i]['hk_birth_month'];
                $oneRowData['hk_birth_day'] = $select[$i]['hk_birth_day'];
                $oneRowData['hk_seinengappi_ymd'] = $select[$i]['hk_seinengappi_ymd'];
                $oneRowData['hk_seinengappi_ymd_y'] = $select[$i]['hk_seinengappi_ymd_y'];
                $oneRowData['hk_nenrei_man'] = $select[$i]['hk_nenrei_man'];
                $oneRowData['hk_file_oid'] = $select[$i]['hk_file_oid'];
                $oneRowData['hk_file_nm'] = $select[$i]['hk_file_nm'];
                $oneRowData['hkg_yubin_no'] = $select[$i]['hkg_yubin_no'];
                $oneRowData['hkg_addr1'] = $select[$i]['hkg_addr1'];
                $oneRowData['hkg_addr2'] = $select[$i]['hkg_addr2'];
                $oneRowData['hkg_tel'] = $select[$i]['hkg_tel'];
                $dataKojinInfo[$i] = $oneRowData;
            }
        }
        // レコードを返す
        $cnt = 1;
        while (count($dataKojinInfo) < $row) {
            array_push($dataKojinInfo, array('seko_no' => $this->_sekoNo, 'seq_no' => $cnt));
            $cnt++;
        }
        return $dataKojinInfo;
    }

    /**
     *
     * 更新API用データ成型処理
     *
     * <AUTHOR> Tosaka
     * @param  
     * @since 2022/11/xx
     * @return array $array
     */
    protected function makeMocData($db, $dataSekoKihon, $dataGojokaiMemberCol, $dataGojokaiMemberDeleteCol = array()) {

        $array = array();
        foreach ($dataGojokaiMemberCol as $one) {
            if ($one['kaiin_info_kbn'] <> '1') {
                continue;
            }
            $row = array();
            $row['ContractNo'] = $one['v_free17'];
            $row['UsageType'] = $one['yoto_kbn'];
            $array[] = $row;
        }
        foreach ($dataGojokaiMemberDeleteCol as $one) {
            if ($one['kaiin_info_kbn'] <> '1') {
                continue;
            }
            $row = array();
            $row['ContractNo'] = $one['v_free17'];
            $row['UsageType'] = 99;
            $array[] = $row;
        }
        $data = array('EnforcementNo' => $this->_sekoNo, 'ApplicationDate' => $this->_selKijunYmd, 'UserName' => $dataSekoKihon['k_nm'], 'ContractInfos' => $array);
        return $data;
    }

    /**
     *
     * 参照API用データ成型処理
     *
     * <AUTHOR> Tosaka
     * @param  
     * @since 2022/11/xx
     * @return array $array
     */
    protected function makeMocData2($db, $dataSekoKihon, $dataGojokaiMemberCol) {

        $array = array();
        foreach ($dataGojokaiMemberCol as $one) {
            if ($one['kaiin_info_kbn'] <> '1') {
                continue;
            }
            $array[] = $one['v_free17'];
        }
        $data = array('s_application_date' => $this->_selKijunYmd, 's_user_name' => $dataSekoKihon['k_nm'], 's_member_no_id_arr' => $array);
        return $data;
    }

    /**
     *
     * 基本パターンコードを取得する
     *
     * <AUTHOR> Tosaka
     * @since 2025/2/xx
     * @return string 基本パターンコード
     */
    protected function getKeiyakuMainPtCd($keiyaku_cd) {

        $db = Msi_Sys_DbManager::getMyDb();
        $main_pt_array = array();
        $sql = "
            SELECT LPAD(CAST(sougi_wari_kbn AS varchar),4,'0') AS main_pt_cd
                ,sougi_wari_kbn AS main_pt_kbn
            FROM partner_wari_kbn_mst
            WHERE partner_cd = :partner_cd
            AND seko_kbn = 1
            ";
        $select = $db->easySelect($sql, array('partner_cd' => $keiyaku_cd));
        if (count($select) > 0) {
            $main_pt_array = $select[0];
        }
        return $main_pt_array;
    }

    /**
     *
     * 会員区分とプランの互助会区分または基本パターンが一致してるかどうかの判定
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/07
     * @return boolean 
     */
    protected function checkGojokaiPlan() {
        $flg = true;
        $msg = null;
        // プランが設定されていないときはスルー
        if (!isset($this->_sekoPlanCd)) {
            return array($flg, $msg);
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $planData = DataMapper_SekoPlan::find($db, array('seko_plan_cd' => $this->_sekoPlanCd), false);
        $sql = "
        SELECT cnm.*
        FROM (
            SELECT kbn_value_cd,regexp_split_to_table(biko,',') AS biko
            FROM code_nm_mst 
            WHERE code_kbn = '9703'
            AND delete_flg = 0
        ) AS T
        LEFT JOIN code_nm_mst cnm
        ON cnm.code_kbn = '0030'
        AND cnm.kbn_value_cd = T.biko
        WHERE T.kbn_value_cd=:kaiin_kbn
        ORDER BY cnm.disp_nox
                ";
        $select = $db->easySelect($sql,array('kaiin_kbn' => $planData[0]['gojokai_kbn']));
        // 会員区分ごとにチェック
        if ($this->_kaiinKbn == static::KAIIN_KBN_GOJO) {
            if ($this->_nebikiGojokaiKbn != $planData[0]['gojokai_kbn']) {
                $flg = false;
                $msg = '互助会情報のご利用コースで利用可能なプランが選択されていません。';
            }
        } else if ($this->_kaiinKbn == static::KAIIN_KBN_IPAN) {
            if ($planData[0]['gojokai_kbn'] != static::KAIIN_KBN_IPAN) {
                $flg = false;
                $msg = '利用できないプランが選択されています。';
            }
        } else { 
            if ($planData[0]['gojokai_kbn'] == static::KAIIN_KBN_IPAN) {
                $kaiinFlg = false;
                foreach ($select as $one) {
                    if ($one['kbn_value_cd'] == $this->_kaiinKbn) {
                       $kaiinFlg = true;
                    }
                }
                if (!$kaiinFlg) {
                    $flg = false;
                    $msg = '利用できないプランが選択されています。';
                }
            } else {
                $kaiinFlg = false;
                foreach ($select as $one) {
                    if ($one['kbn_value_cd'] == $this->_kaiinKbn) {
                       $kaiinFlg = true;
                    }
                }
                if (!$kaiinFlg) {
                    $flg = false;
                    $msg = '契約先番号の契約先が利用可能なプランが選択されていません。';
                }
                $keiyakusakiInfoData = $db->easySelect(<<< END_OF_SQL
                    SELECT partner_nm,partner_cd
                    FROM seko_keiyakusaki_info 
                    WHERE seko_no = :seko_no
                    AND delete_flg = 0
END_OF_SQL
                        , array('seko_no' => $this->_sekoNo));
                $main_pt_array = $this->getKeiyakuMainPtCd($keiyakusakiInfoData[0]['partner_cd']);
                if ($main_pt_array['main_pt_kbn'] != $planData[0]['main_pt_kbn']) {
                    $flg = false;
                    $msg = '契約先番号の契約先が利用可能なプランが選択されていません。';
                }
            }
        }
        return array($flg, $msg);
    }

    /**
     * 明細チェック処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @return boolean 成功可否
     */
    protected function checkMsi($funcMsg) {

        $flg = true;
        $msg = '';
        $db = Msi_Sys_DbManager::getMyDb();
        // 商品部門チェック
        list($flg, $msg) = $this->checkMsiBumon($funcMsg);
        // 下代(仕入)0円時の明細の存在チェック
        $sql = "
            SELECT T.*
            FROM (
                SELECT m.seko_no
                    ,sbm.hachu_kbn
                    ,stm.siire_tnk
                    ,sm.kashidashi_kbn
                    ,m.shohin_cd
                    ,m.shohin_nm
                FROM juchu_denpyo d
                INNER JOIN seko_kihon_info ski
                    ON ski.seko_no = d.seko_no
                    AND ski.delete_flg = 0
                INNER JOIN juchu_denpyo_msi m
                    ON m.denpyo_no = d.denpyo_no
                    AND m.delete_flg = 0
                LEFT JOIN shohin_mst sm
                    ON sm.shohin_cd = m.shohin_cd
                    AND sm.bumon_cd = m.shohin_bumon_cd
                    AND sm.delete_flg = 0
                LEFT JOIN shohin_bunrui_mst sbm
                    ON sbm.shohin_cd = m.shohin_cd
                    AND sbm.bumon_cd = m.shohin_bumon_cd
                    AND sbm.dai_bunrui_cd = m.dai_bunrui_cd
                    AND sbm.chu_bunrui_cd = m.chu_bunrui_cd
                    AND sbm.shohin_kbn = m.shohin_kbn
                    AND sbm.delete_flg = 0
                LEFT JOIN shohin_tanka_mst stm
                    ON stm.shohin_cd = m.shohin_cd
                    AND stm.bumon_cd = m.shohin_bumon_cd
                    AND ( stm.tekiyo_st_date <= COALESCE(CAST(ski.sougi_ymd AS DATE),CURRENT_DATE) 
                        AND stm.tekiyo_ed_date >= COALESCE(CAST(ski.sougi_ymd AS DATE),CURRENT_DATE))
                    AND stm.delete_flg = 0
                WHERE d.seko_no = :seko_no
                    AND d.data_kbn = :data_kbn
                    AND d.delete_flg = 0
            ) AS T
            WHERE T.siire_tnk = 0
                        ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn()));
        // 明細が存在すればエラー
        if (count($select) > 0) {
            $flg = false;
            $shohin_cds = implode(',', array_map(function($el) {
                        return $el['shohin_cd'] . ':' . $el['shohin_nm'];
                    }, $select));
            $msg .= "※商品未選択※商品が含まれているため、".$funcMsg."｛" . $shohin_cds . "｝";
        }
        // 上代(販売)0円時の明細の存在チェック
//        $sql = "
//            SELECT T.*
//            FROM (
//                SELECT m.seko_no
//                    ,sbm.hachu_kbn
//                    ,stm.hanbai_tnk
//                    ,stm.siire_tnk
//                    ,sm.kashidashi_kbn
//                FROM juchu_denpyo d
//                INNER JOIN seko_kihon_info ski
//                    ON ski.seko_no = d.seko_no
//                    AND ski.delete_flg = 0
//                INNER JOIN juchu_denpyo_msi m
//                    ON m.denpyo_no = d.denpyo_no
//                    AND m.delete_flg = 0
//                LEFT JOIN shohin_mst sm
//                    ON sm.shohin_cd = m.shohin_cd
//                    AND sm.bumon_cd = m.shohin_bumon_cd
//                    AND sm.delete_flg = 0
//                LEFT JOIN shohin_bunrui_mst sbm
//                    ON sbm.shohin_cd = m.shohin_cd
//                    AND sbm.bumon_cd = m.shohin_bumon_cd
//                    AND sbm.dai_bunrui_cd = m.dai_bunrui_cd
//                    AND sbm.chu_bunrui_cd = m.chu_bunrui_cd
//                    AND sbm.shohin_kbn = m.shohin_kbn
//                    AND sbm.delete_flg = 0
//                LEFT JOIN shohin_tanka_mst stm
//                    ON stm.shohin_cd = m.shohin_cd
//                    AND stm.bumon_cd = m.shohin_bumon_cd
//                    AND ( stm.tekiyo_st_date <= COALESCE(CAST(ski.sougi_ymd AS DATE),CURRENT_DATE) 
//                        AND stm.tekiyo_ed_date >= COALESCE(CAST(ski.sougi_ymd AS DATE),CURRENT_DATE))
//                    AND stm.delete_flg = 0
//                WHERE d.seko_no = :seko_no
//                    AND d.data_kbn = :data_kbn
//                    AND d.delete_flg = 0
//            ) AS T
//            WHERE T.hachu_kbn <> 0
//                AND T.hanbai_tnk <> 0
//                AND T.kashidashi_kbn = 2
//                        ";
//        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn()));
//        // 明細が存在すればエラー
//        if (count($select) > 0) {
//            $flg = false;
//            $msg .= "貸出備品に販売単価が設定されている商品が含まれているため、見積を確定することができません。";
//        }
        // プラン商品単価チェック
        $sql = "
            SELECT T.*
            FROM(
                SELECT m.seko_no
                    ,m.shohin_cd
                    ,m.shohin_nm
                    ,CASE WHEN m.juchu_tnk = spsm.hanbai_tnk THEN true
                    ELSE false END AS tanka_boolean
                FROM juchu_denpyo d
                INNER JOIN juchu_denpyo_msi m
                    ON m.denpyo_no = d.denpyo_no
                    AND m.upgrade_kbn = 1
                    AND m.delete_flg = 0
                LEFT JOIN seko_plan_smsi_mst spsm
                    ON spsm.seko_plan_cd = d.seko_plan_cd
                    AND spsm.shohin_cd = m.shohin_cd
                    AND spsm.bumon_cd = m.shohin_bumon_cd
                    AND :sougi_ymd BETWEEN spsm.tekiyo_st_date AND spsm.tekiyo_ed_date
                    AND spsm.delete_flg = 0
                WHERE d.seko_no = :seko_no
                    AND d.data_kbn = :data_kbn
                    AND d.delete_flg = 0
            ) AS T
            WHERE T.tanka_boolean = false
                        ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn(), 'sougi_ymd' => $this->_selKijunYmd));
        // 明細が存在すればエラー
        if (count($select) > 0) {
            $flg = false;
            $shohin_cds = implode(',', array_map(function($el) {
                        return $el['shohin_cd'] . ':' . $el['shohin_nm'];
                    }, $select));
            $msg .= "プランマスタの価格と一致しないため、".$funcMsg."｛" . $shohin_cds . "｝";
        }
        // 商品単価チェック
        $sql = "
            SELECT T.*
            FROM(
                SELECT m.seko_no
                    ,m.shohin_cd
                    ,m.shohin_nm
                    ,CASE WHEN m.juchu_tnk = stm.hanbai_tnk THEN true
                    ELSE false END AS tanka_boolean
                FROM juchu_denpyo d
                INNER JOIN juchu_denpyo_msi m
                    ON m.denpyo_no = d.denpyo_no
                    AND m.upgrade_kbn != 1
                    AND m.delete_flg = 0
                INNER JOIN shohin_mst sm
                    ON sm.shohin_cd = m.shohin_cd
                    AND sm.bumon_cd = m.shohin_bumon_cd
                    AND sm.tnk_chg_kbn = 0
                    AND sm.delete_flg = 0
                LEFT JOIN shohin_tanka_mst stm
                    ON stm.shohin_cd = m.shohin_cd
                    AND stm.bumon_cd = m.shohin_bumon_cd
                    AND :sougi_ymd BETWEEN stm.tekiyo_st_date AND stm.tekiyo_ed_date
                    AND stm.delete_flg = 0
                WHERE d.seko_no = :seko_no
                    AND d.data_kbn = :data_kbn
                    AND d.delete_flg = 0
            ) AS T
            WHERE T.tanka_boolean = false
                        ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn(), 'sougi_ymd' => $this->_selKijunYmd));
        // 明細が存在すればエラー
        if (count($select) > 0) {
            $flg = false;
            $shohin_cds = implode(',', array_map(function($el) {
                        return $el['shohin_cd'] . ':' . $el['shohin_nm'];
                    }, $select));
            $msg .= "商品マスタの価格と一致しないため、".$funcMsg."｛" . $shohin_cds . "｝";
        }
        // 商品販売期間チェック
        $sql = "
            SELECT T.*
            FROM(
                SELECT m.seko_no
                    ,m.shohin_cd
                    ,m.shohin_nm
                    ,CASE WHEN :sougi_ymd BETWEEN sm.hanbai_st_ymd AND sm.hanbai_end_ymd THEN true
                    ELSE false END AS hanbai_ymd_boolean
                FROM juchu_denpyo d
                INNER JOIN juchu_denpyo_msi m
                    ON m.denpyo_no = d.denpyo_no
                    AND m.delete_flg = 0
                LEFT JOIN shohin_mst sm
                    ON sm.shohin_cd = m.shohin_cd
                    AND sm.bumon_cd = m.shohin_bumon_cd
                    AND sm.delete_flg = 0
                WHERE d.seko_no = :seko_no
                    AND d.data_kbn = :data_kbn
                    AND d.delete_flg = 0
            ) AS T
            WHERE T.hanbai_ymd_boolean = false
                        ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn(), 'sougi_ymd' => $this->_selKijunYmd));
        // 明細が存在すればエラー
        if (count($select) > 0) {
            $flg = false;
            $shohin_cds = implode(',', array_map(function($el) {
                        return $el['shohin_cd'] . ':' . $el['shohin_nm'];
                    }, $select));
            $msg .= "販売適用期間外の商品が含まれているため、".$funcMsg."｛" . $shohin_cds . "｝";
        }
        return array($flg, $msg);
    }

    /**
     *
     * 明細の部門と見積式場の部門のチェック
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/07
     * @return boolean 
     */
    protected function checkMsiBumon($funcMsg) {
        $flg = true;
        $msg = '';
        $db = Msi_Sys_DbManager::getMyDb();
        // 商品部門チェック
        $sql = "
            SELECT m.seko_no
            FROM juchu_denpyo d
            INNER JOIN juchu_denpyo_msi m
                ON m.denpyo_no = d.denpyo_no
                AND m.delete_flg = 0
            WHERE d.seko_no = :seko_no
                AND d.data_kbn IN (1,2,4)   -- 葬儀・法事・供花供物のみ
                AND d.delete_flg = 0
                        ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        // 明細が存在すればエラーチェック
        if (count($select) > 0) {
            // 現在登録可能な商品以外の商品部門コードが存在していたらエラー
            $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
            $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $this->_selectSekoKihon['est_shikijo_cd']));
            $bumonCds = null;
            if (count($bumonData) > 0 && $bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
                //自部門商品＋親部門商品(親部門はさかのぼる)
                $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $this->_selectSekoKihon['est_shikijo_cd'], null);
                foreach ($allOyaBumon as $oneBumon) {
                    if ($oneBumon['bumon_shohin_sel_kbn'] === '1') {
                        $bumonCds .= "'" . $oneBumon['ko_bumon_cd'] . "',";
                    }
                }
            } else {
                // 会計部門と違う場合は会計部門の親部門を取得する
                if (isset($bumonData[0]['kaikei_bumon_cd'])) {
                    $bumonData2 = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $bumonData[0]['kaikei_bumon_cd']));
                    if ($bumonData2[0]['bumon_shohin_sel_kbn'] === '1') {
                        $bumonCds .= "'" . $bumonData[0]['kaikei_bumon_cd'] . "',";
                    }
                    $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $bumonData[0]['kaikei_bumon_cd'], null);
                } else {
                    // 親部門商品(親部門はさかのぼる)のみ
                    $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $this->_selectSekoKihon['est_shikijo_cd'], null);
                }
                foreach ($allOyaBumon as $oneBumon) {
                    if ($oneBumon['bumon_kbn'] === '0') {
                        $bumonCds .= "'" . $oneBumon['bumon_cd'] . "',";
                    }
                }
            }
            $bumonWhere = "AND m.shohin_bumon_cd NOT IN (" . trim($bumonCds, ',') . ")";
            $sql = "
                SELECT m.shohin_cd,m.shohin_nm
                FROM juchu_denpyo d
                INNER JOIN juchu_denpyo_msi m
                    ON m.denpyo_no = d.denpyo_no
                    $bumonWhere
                    AND m.delete_flg = 0
                WHERE d.seko_no = :seko_no
                    AND d.data_kbn IN (1,2,4)   -- 葬儀・法事・供花供物のみ
                    AND d.delete_flg = 0
                            ";
            $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
            if (count($select) > 0) {
                $flg = false;
                $shohin_cds = implode(',', array_map(function($el) {
                            return $el['shohin_cd'] . ':' . $el['shohin_nm'];
                        }, $select));
                $msg .= '別式場の商品(' . $shohin_cds . ')が登録されているため'.$funcMsg;
            }
        }
        return array($flg, $msg);
    }
    /**
     * ログイン者の部門参照権限チェック
     * 
     * <AUTHOR> Sugiyama
     * @since  2025/02/xx 
     * @param  array $select 施行情報
     * @throws Exception
     */
    protected function checkBumonRef($select) {
        $bumon_cds = App_Utils2::getBumonRef();
        if(!is_null($bumon_cds)){
            $flg = true;
            if(Msi_Sys_Utils::myCount($bumon_cds) == 0){
                $flg = true;
            }else{
                foreach ($bumon_cds as $bumon_cd) {
                    if($bumon_cd == $select['bumon_cd']){
                        $flg = false;
                    }
                }
            }
            if($flg){
                throw new Exception('部門参照権限がありません');
            }
        }
    }
    /**
     * 部門権限参照で制限された担当者一覧を取得
     * 
     * @param type $db
     * @param type $data
     * @return $select 担当者リスト
     */
    protected function getBumonRefTantoList($db, $data = array()){
        $bumon_cd = null;
        if(isset($data['bumon_cd'])){
            $bumon_cd = $data['bumon_cd'];
        }else{
            $bumon_cd = App_Utils::getCtxtHallWithKaisya();
        }
        $bumon_ref = App_Utils2::getBumonRef(2, $bumon_cd, $db);
        if(isset($bumon_ref) && Msi_Sys_Utils::myCount($bumon_ref) > 0){
            // 参照権限部門を条件に追加
            $where = "AND tsm.bumon_cd IN ('" . implode("','", $bumon_ref) . "')";
            $sql = "
                SELECT
                     tm.tanto_cd AS id
                    ,tm.tanto_nm AS text
                    ,tm.tekiyo_ed_date
                    ,tsm.bumon_cd
                FROM tanto_mst tm
                INNER JOIN tanto_sozoku_mst tsm
                ON  tsm.tanto_cd   = tm.tanto_cd
                AND tsm.delete_flg = 0
                $where
                WHERE tm.delete_flg = 0
                AND tm.tekiyo_ed_date IS NULL OR CURRENT_DATE < tm.tekiyo_ed_date
                ORDER BY tm.tanto_cd
            ";
            $select = $db->easySelect($sql);
        }else if(isset($bumon_ref)){
            return array();
        }
        return $select;
    }
    
}
