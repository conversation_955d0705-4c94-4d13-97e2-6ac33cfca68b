<?php

/**
 * App_KeigenUtils
 *
 * 軽減税率対応 ユーティリティ関数
 *
 * @category   library
 * @package    library\App
 * <AUTHOR> mihara
 * @since      2019/04/30
 * @version    2019/04/30 mihara 既存Utils等に追加すべきだが（cstm_key によらない）共通に記述できないので別バージョン作成
 * @filesource 
 */

/**
 * 軽減税率対応 ユーティリティ関数
 * 
 * @category   library
 * @package    library\App
 * <AUTHOR> Mihara
 * @since      2019/04/30
 */
final class App_KeigenUtils extends App_KeigenUtilsAbstract {
    
    /**
     * 消費税額を計算して返す
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   array        $select
     * @return  array()|null
     */
    protected static function _getSomeShohizeiSub($select)
    {
        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $rtnData = array();
        // ex. array( array('非課税金額', 10000, array(...)),
        //            array('10％税抜対象額', 1000000, array(...)),
        //            array('消費税', 100000, array(...)),

        // 非課税金額
        // 複数行はないと思われるが一応 sum する
        $hiRec = null;
        $price = array_reduce( $select, function ($accm, $rec) use (&$hiRec) {
                if ( $rec['zei_kbn'] == 0 ) {
                    $hiRec = $rec;
                    $accm += $rec['price'];
                }
                return $accm; }, 0);
        if ( $hiRec !== null ) { // 金額 0 の場合もここでは返す
            $hiRec['type'] = 'price';
            $rtnData[] = array( '非課税金額', $price, $hiRec );
        }

        // 税種別ごとに出力
        foreach ( $select as $rec ) {
            if ( $rec['zei_kbn'] == 0 ) continue; // 非課税は処理済
            $price        = $rec['price'];
            $zei_kbn      = $rec['zei_kbn'];
            $zei_rtu      = $rec['zei_rtu'];
            $zei_hasu_kbn = $rec['zei_hasu_kbn'];
            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額
            if ( $zei_kbn == 2 ) { // 2:外税
                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('＊%d％税抜対象額', $zei_rtu);
                } else {
                    $cap = sprintf('%d％税抜対象額', $zei_rtu);
                }
                $rec['type'] = 'price';
                $data = array( $cap, $price, $rec );
                $rtnData[] = $data;

                $tax = $TaxPrc['ZeiPrc'];
                $cap = sprintf('消費税');
                $rec['type'] = 'tax';
                $data = array( $cap, $tax, $rec );
                $rtnData[] = $data;
            } else if ( $zei_kbn == 1 ) { // 1:内税
                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('＊%d％税込対象額', $zei_rtu);
                } else {
                    $cap = sprintf('%d％税込対象額', $zei_rtu);
                }
                $rec['type'] = 'price';
                $data = array( $cap, $price, $rec );
                $rtnData[] = $data;

                $tax = $TaxPrc['ZeiPrc'];
                $cap = sprintf('(内消費税)');
                $rec['type'] = 'tax';
                $data = array( $cap, $tax, $rec );
                $rtnData[] = $data;
            } else {
                Msi_Sys_Utils::warn( sprintf("(cae0d49f)zei_kbn=%s!?", $rec['zei_kbn']) );
                continue;
            }
        }

        return $rtnData;
    }     
    

    /**
     * 税CD ベースの array を 税率にまとめる内部処理
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   array  $select      税CD ベースの array  (*1)
     * @return  array               税率にまとめたもの   (*2)
     *
     *  (*1) ex.
     *   zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
     *  ---------+--------+---------+--------------+--------
     *         1 |      5 |      10 |            1 |  15000
     *         2 |      4 |       8 |            1 |  80000
     *         2 |      5 |      10 |            1 | 185000
     *         0 |      5 |      10 |            1 |  10800
     * (*2) ex. array( array('10％対象額', 10000,  '(内消費税)', 910),
     *                 array('8%対象額', 10000, '(内消費税)', 741) )
     *     対象額には消費税額も含まれます
     */
    protected static function _sumUpZeiRitsu($select)
    {
        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...

        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];

            if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            if ( $zei_kbn == 0 ) continue; // 非課税は skip

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0 );
            }

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
            $_wkData[ $zei_rtu ]['reduced_tax_rate'] = $rec['reduced_tax_rate'];
        }

        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ($_wkData as $wkK => $wkRec) {
            if ($wkRec['reduced_tax_rate'] == 2) {
                $cap1 = sprintf("＊%d%%対象額", $wkK);
            } else {
                $cap1 = sprintf("%d%%対象額", $wkK);
            }
            $amt1 = +$wkRec['totalPrice'];
            $cap2 = "(内消費税)";
            $amt2 = +$wkRec['ZeiPrc'];
            $_arr = array($cap1, $amt1, $cap2, $amt2);
            $rtnData[] = $_arr;
        }

        return $rtnData;
    }

    /**
     * 領収書発行画面用 売上明細伝票から消費税額を計算して返す
     * 税率で対象額をまとめる
     * ex. array( array( 'taisho_cap' => '非課税金額', 'taisho_gaku' => 10000, 'zei_gaku' => 0 ),
     *            array( 'taisho_cap' => '10％対象額', 'taisho_gaku' => 10000, 'zei_gaku' => 910),
     *            array( 'taisho_cap' => '8％対象額',  'taisho_gaku' => 10000, 'zei_gaku' => 741 ) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getSeikyuShohizeiEasy3($db, $uri_den_no)
    {
        $select = static::getSeikyuShohizei00Uri($db, $uri_den_no);

        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...

        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd       = +$rec['zei_cd'];

            //            if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            //            if ( $zei_kbn == 0 ) continue; // 非課税は skip

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0, 'zei_rtu'=>$zei_rtu, 'zei_kbn'=>$zei_kbn,
                                              'zei_cd' => $zei_cd );
            }

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
            $_wkData[ $zei_rtu ]['reduced_tax_rate'] = $rec['reduced_tax_rate'];
        }

        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ( $_wkData as $wkK => $wkRec ) {
            if ( $wkK == 0 ) {
                $cap1 = '非課税金額';
                $zei_kbn = 0;
            } else {
                if ($wkRec['reduced_tax_rate'] == 2) {
                    $cap1 = sprintf("＊%d%%対象額", $wkK);
                } else {
                    $cap1 = sprintf("%d%%対象額", $wkK);
                }
                $zei_kbn = +$wkRec['zei_kbn'];
            }
            $amt1 = +$wkRec['totalPrice'];
            $cap2 = "(内消費税)";
            $amt2 = +$wkRec['ZeiPrc'];
            $_arr = array( 'taisho_cap'  => $cap1, 
                           'taisho_gaku' => $amt1,
                           'zei_gaku'    => $amt2 );
            $_arr['zei_kbn'] = $zei_kbn;
            $_arr['zei_rtu'] = +$wkRec['zei_rtu'];
            $zei_cd = +$wkRec['zei_cd'];
            $_arr['zei_cd']  = $zei_cd;
            $_arr['reduced_tax_rate']  = DataMapper_ZeiMstEasy::getReducedTaxRate($zei_cd);
            $rtnData[] = $_arr;
        }

        // Msi_Sys_Utils::debug( 'rtnData PRE=>' . Msi_Sys_Utils::dump($rtnData) );
        // 出力順に並び替え
        // zei_kbn=0(非課税が先頭), 次に外税,内税の順で、外税,内税内では税率が高い順
        usort( $rtnData, function($a, $b) {
                if ( $a['zei_kbn'] == 0 ) return -1;
                if ( $b['zei_kbn'] == 0 ) return +1;
                return $a['zei_kbn'] > $b['zei_kbn'] ? -1 : 
                    ( $a['zei_rtu'] > $b['zei_rtu'] ? -1 : +1  );
            } );
        // Msi_Sys_Utils::debug( 'rtnData post=>' . Msi_Sys_Utils::dump($rtnData) );

        return $rtnData;
    }     
    /**
     * 領収書発行画面用 売上明細伝票から消費税額を計算して返す
     * 税率で対象額をまとめる
     * ex. array( array( 'taisho_cap' => '非課税金額', 'taisho_gaku' => 10000, 'zei_gaku' => 0 ),
     *            array( 'taisho_cap' => '10％対象額', 'taisho_gaku' => 10000, 'zei_gaku' => 910),
     *            array( 'taisho_cap' => '8％対象額',  'taisho_gaku' => 10000, 'zei_gaku' => 741 ) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> Sugiyama
     * @since 2021/07/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getSeikyuShohizeiEasy4($db, $seko_no, $seikyu_den_no)
    {
        $select = static::getSeikyuShohizei03Uri($db, $seko_no, $seikyu_den_no);

        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...

        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd       = +$rec['zei_cd'];

            //            if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            //            if ( $zei_kbn == 0 ) continue; // 非課税は skip

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0, 'zei_rtu'=>$zei_rtu, 'zei_kbn'=>$zei_kbn,
                                              'zei_cd' => $zei_cd );
            }

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
            $_wkData[ $zei_rtu ]['reduced_tax_rate'] = $rec['reduced_tax_rate'];
        }
        
        // 互助会（友の会）、後掛金、供物未精算 を調整   2019/10/xx mihara
        static::_adjEtcSeikyuShohizei02($db, $seikyu_den_no, $_wkData);

        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ( $_wkData as $wkK => $wkRec ) {
            if ( $wkK == 0 ) {
                $cap1 = '非課税金額';
                $zei_kbn = 0;
            } else {
                if ($wkRec['reduced_tax_rate'] == 2) {
                    $cap1 = sprintf("＊%d%%対象額", $wkK);
                } else {
                    $cap1 = sprintf("%d%%対象額", $wkK);
                }
                $zei_kbn = +$wkRec['zei_kbn'];
            }
            $amt1 = +$wkRec['totalPrice'];
            $cap2 = "(内消費税)";
            $amt2 = +$wkRec['ZeiPrc'];
            $_arr = array( 'taisho_cap'  => $cap1, 
                           'taisho_gaku' => $amt1,
                           'zei_gaku'    => $amt2 );
            $_arr['zei_kbn'] = $zei_kbn;
            $_arr['zei_rtu'] = +$wkRec['zei_rtu'];
            $zei_cd = +$wkRec['zei_cd'];
            $_arr['zei_cd']  = $zei_cd;
            $_arr['reduced_tax_rate']  = DataMapper_ZeiMstEasy::getReducedTaxRate($zei_cd);
            $rtnData[] = $_arr;
        }

        // Msi_Sys_Utils::debug( 'rtnData PRE=>' . Msi_Sys_Utils::dump($rtnData) );
        // 出力順に並び替え
        // zei_kbn=0(非課税が先頭), 次に外税,内税の順で、外税,内税内では税率が高い順
        usort( $rtnData, function($a, $b) {
                if ( $a['zei_kbn'] == 0 ) return -1;
                if ( $b['zei_kbn'] == 0 ) return +1;
                return $a['zei_kbn'] > $b['zei_kbn'] ? -1 : 
                    ( $a['zei_rtu'] > $b['zei_rtu'] ? -1 : +1  );
            } );
        // Msi_Sys_Utils::debug( 'rtnData post=>' . Msi_Sys_Utils::dump($rtnData) );

        return $rtnData;
    }
    /**
     * 領収書発行画面用 売上明細伝票から消費税額を計算して返す
     * 税率で対象額をまとめる
     * 合算用
     * ex. array( array( 'taisho_cap' => '非課税金額', 'taisho_gaku' => 10000, 'zei_gaku' => 0 ),
     *            array( 'taisho_cap' => '10％対象額', 'taisho_gaku' => 10000, 'zei_gaku' => 910),
     *            array( 'taisho_cap' => '8％対象額',  'taisho_gaku' => 10000, 'zei_gaku' => 741 ) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> Sugiyama
     * @since 2021/07/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getSeikyuShohizeiEasy4Bungas($db, $seko_no, $seikyu_den_no)
    {
        $select = static::getSeikyuShohizei04Uri($db, $seikyu_den_no);

        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...

        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd       = +$rec['zei_cd'];

            //            if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            //            if ( $zei_kbn == 0 ) continue; // 非課税は skip

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0, 'zei_rtu'=>$zei_rtu, 'zei_kbn'=>$zei_kbn,
                                              'zei_cd' => $zei_cd );
            }

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
            $_wkData[ $zei_rtu ]['reduced_tax_rate'] = $rec['reduced_tax_rate'];
        }
        
        // 互助会（友の会）、後掛金、供物未精算 を調整   2019/10/xx mihara
        // static::_adjEtcSeikyuShohizei02($db, $seikyu_den_no, $_wkData);

        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ( $_wkData as $wkK => $wkRec ) {
            if ( $wkK == 0 ) {
                $cap1 = '非課税金額';
                $zei_kbn = 0;
            } else {
                if ($wkRec['reduced_tax_rate'] == 2) {
                    $cap1 = sprintf("＊%d%%対象額", $wkK);
                } else {
                    $cap1 = sprintf("%d%%対象額", $wkK);
                }
                $zei_kbn = +$wkRec['zei_kbn'];
            }
            $amt1 = +$wkRec['totalPrice'];
            $cap2 = "(内消費税)";
            $amt2 = +$wkRec['ZeiPrc'];
            $_arr = array( 'taisho_cap'  => $cap1, 
                           'taisho_gaku' => $amt1,
                           'zei_gaku'    => $amt2 );
            $_arr['zei_kbn'] = $zei_kbn;
            $_arr['zei_rtu'] = +$wkRec['zei_rtu'];
            $zei_cd = +$wkRec['zei_cd'];
            $_arr['zei_cd']  = $zei_cd;
            $_arr['reduced_tax_rate']  = DataMapper_ZeiMstEasy::getReducedTaxRate($zei_cd);
            $rtnData[] = $_arr;
        }

        // Msi_Sys_Utils::debug( 'rtnData PRE=>' . Msi_Sys_Utils::dump($rtnData) );
        // 出力順に並び替え
        // zei_kbn=0(非課税が先頭), 次に外税,内税の順で、外税,内税内では税率が高い順
        usort( $rtnData, function($a, $b) {
                if ( $a['zei_kbn'] == 0 ) return -1;
                if ( $b['zei_kbn'] == 0 ) return +1;
                return $a['zei_kbn'] > $b['zei_kbn'] ? -1 : 
                    ( $a['zei_rtu'] > $b['zei_rtu'] ? -1 : +1  );
            } );
        // Msi_Sys_Utils::debug( 'rtnData post=>' . Msi_Sys_Utils::dump($rtnData) );

        return $rtnData;
    } 
    /**
     * 見積書用 受注明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> Sugiyama
     * @since 2019/06/04
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @version 2019/09/10 MSI Sai 一般合計と値引き合計を追加
     * @param   Msi_Sys_DB   $db
     * @param   string       $denpyo_no
     * @return  array()|null
     */
    protected static function getMitsuShohizei00($db, $denpyo_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(juchu_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   ,SUM(juchu_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price_i
   ,SUM(COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc) AS price_n
   FROM juchu_denpyo_msi jdm
  WHERE denpyo_no=:denpyo_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'denpyo_no' => $denpyo_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        // patch: 非課税で 税率設定(>0)されている場合は強制的に zei_rtu,zei_cd をゼロにする        
        $rtnSel = array();
        foreach ( $select as $rec ) {
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_cd       = +$rec['zei_cd'];
            if ( $zei_kbn == 0 && $zei_rtu != 0 ) { 
                $rec['zei_rtu'] = 0;
                $rec['zei_cd'] = 0;
            }
            $rtnSel[] = $rec;
        }

        return $rtnSel;
    }
    

    /**
     * 領収証要 受注伝票明細の sum から消費税額を計算して返す
     *
     * <AUTHOR> Sugiyama
     * @since 2025/04/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $juchu_den_no
     * @return  array()|null
     */
    protected static function getSeikyuShohizei00Juchu($db, $juchu_den_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(juchu_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   ,SUM(juchu_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price_i
   ,SUM(COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc) AS price_n
   FROM juchu_denpyo_msi jdm
  WHERE denpyo_no=:denpyo_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'denpyo_no'=>$juchu_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        // patch: 非課税で 税率設定(>0)されている場合は強制的に zei_rtu,zei_cd をゼロにする        
        $rtnSel = array();
        foreach ( $select as $rec ) {
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_cd       = +$rec['zei_cd'];
            if ( $zei_kbn == 0 && $zei_rtu != 0 ) { 
                $rec['zei_rtu'] = 0;
                $rec['zei_cd'] = 0;
            }
            $rtnSel[] = $rec;
        }

        return $rtnSel;
    }  
    /**
     * 請求書用 売上明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @version 2019/09/10 MSI Sai 一般合計と値引き合計を追加
     * @param   Msi_Sys_DB   $db
     * @param   string       $uri_den_no
     * @return  array()|null
     */
    protected static function getSeikyuShohizei00Uri($db, $uri_den_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(uri_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   ,SUM(uri_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price_i
   ,SUM(COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc) AS price_n
   FROM uriage_denpyo_msi jdm
  WHERE uri_den_no=:uri_den_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'uri_den_no'=>$uri_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        // patch: 非課税で 税率設定(>0)されている場合は強制的に zei_rtu,zei_cd をゼロにする        
        $rtnSel = array();
        foreach ( $select as $rec ) {
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_cd       = +$rec['zei_cd'];
            if ( $zei_kbn == 0 && $zei_rtu != 0 ) { 
                $rec['zei_rtu'] = 0;
                $rec['zei_cd'] = 0;
            }
            $rtnSel[] = $rec;
        }

        return $rtnSel;
    }  
    /**
     * 請求書用 売上明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> Sugiyama
     * @since 2021/07/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $seko_no
     * @return  array()|null
     */
    protected static function getSeikyuShohizei03Uri($db, $seko_no, $seikyu_den_no = null)
    {
        // 分割区分取得
        $bun_gas_kbn_num = $db->getOneVal("SELECT bun_gas_kbn_num FROM seikyu_denpyo WHERE seikyu_den_no=? AND delete_flg=0"
                                     , array($seikyu_den_no));
        if($bun_gas_kbn_num == 2){
            $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
    sdm. zei_kbn
   ,sdm.zei_cd
   ,SUM(sdm.uri_prc + COALESCE(sdm.gojokai_nebiki_prc, 0) + sdm.nebiki_prc + sdm.hoshi_prc + COALESCE(sdm.add_cost, 0)) AS price
   ,SUM(sdm.uri_prc + sdm.hoshi_prc + COALESCE(sdm.add_cost, 0)) AS price_i
   ,SUM(COALESCE(sdm.gojokai_nebiki_prc, 0) + sdm.nebiki_prc) AS price_n
   FROM seikyu_denpyo_msi sdm
   LEFT JOIN seikyu_denpyo sd
   ON  sd.seikyu_den_no = sdm.seikyu_den_no
   AND sd.delete_flg = 0
  WHERE sdm.seko_no       = :seko_no
    AND sdm.seikyu_den_no = :seikyu_den_no
    AND sdm.delete_flg    = 0
    AND CASE WHEN sd.data_kbn IN (1,2) THEN TRUE
             WHEN sd.data_kbn = 4 AND sd.juchusaki_kbn = 1 THEN TRUE
             ELSE FALSE END 
  GROUP BY sdm.zei_kbn, sdm.zei_cd
) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd  = :kaisya_cd
   AND T1.delete_flg = 0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;
            $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

            $select = $db->easySelect( $sql, array( 'seko_no'       => $seko_no,
                                                    'seikyu_den_no' => $seikyu_den_no,
                                                    'kaisya_cd'     => $kaisya_cd ) );
        }else{
            $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
   jdm. zei_kbn
   ,jdm.zei_cd
   ,SUM(jdm.uri_prc + COALESCE(jdm.gojokai_nebiki_prc, 0) + jdm.nebiki_prc + jdm.hoshi_prc + COALESCE(jdm.add_cost, 0)) AS price
   ,SUM(jdm.uri_prc + jdm.hoshi_prc + COALESCE(jdm.add_cost, 0)) AS price_i
   ,SUM(COALESCE(jdm.gojokai_nebiki_prc, 0) + jdm.nebiki_prc) AS price_n
   FROM uriage_denpyo_msi jdm
   LEFT JOIN uriage_denpyo ud
   ON  ud.uri_den_no = jdm.uri_den_no
   AND ud.delete_flg = 0
  WHERE jdm.seko_no=:seko_no
    AND jdm.delete_flg=0
    AND CASE WHEN ud.data_kbn IN (1,2) THEN TRUE
             WHEN ud.data_kbn = 4 AND ud.juchusaki_kbn = 1 THEN TRUE
             ELSE FALSE END 
  GROUP BY jdm.zei_kbn, jdm.zei_cd
) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;
            $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

            $select = $db->easySelect( $sql, array( 'seko_no'   => $seko_no,
                                                    'kaisya_cd' => $kaisya_cd ) );
        }
        // patch: 非課税で 税率設定(>0)されている場合は強制的に zei_rtu,zei_cd をゼロにする        
        $rtnSel = array();
        foreach ( $select as $rec ) {
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_cd       = +$rec['zei_cd'];
            if ( $zei_kbn == 0 && $zei_rtu != 0 ) { 
                $rec['zei_rtu'] = 0;
                $rec['zei_cd'] = 0;
            }
            $rtnSel[] = $rec;
        }

        return $rtnSel;
    }  
    /**
     * 請求書用 売上明細伝票の sum から消費税額を計算して返す
     * 合算用
     * <AUTHOR> Sugiyama
     * @since 2021/08/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no
     * @return  array()|null
     */
    protected static function getSeikyuShohizei04Uri($db, $seikyu_den_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
   jdm. zei_kbn
   ,jdm.zei_cd
   ,SUM(jdm.uri_prc + COALESCE(jdm.gojokai_nebiki_prc, 0) + jdm.nebiki_prc + jdm.hoshi_prc + COALESCE(jdm.add_cost, 0)) AS price
   ,SUM(jdm.uri_prc + jdm.hoshi_prc + COALESCE(jdm.add_cost, 0)) AS price_i
   ,SUM(COALESCE(jdm.gojokai_nebiki_prc, 0) + jdm.nebiki_prc) AS price_n
   FROM seikyu_denpyo_msi jdm
   LEFT JOIN seikyu_denpyo ud
   ON  ud.seikyu_den_no = jdm.seikyu_den_no
   AND ud.delete_flg = 0
  WHERE jdm.seikyu_den_no=:seikyu_den_no
    AND jdm.delete_flg=0
  GROUP BY jdm.zei_kbn, jdm.zei_cd
) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'seikyu_den_no'=>$seikyu_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        // patch: 非課税で 税率設定(>0)されている場合は強制的に zei_rtu,zei_cd をゼロにする        
        $rtnSel = array();
        foreach ( $select as $rec ) {
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_cd       = +$rec['zei_cd'];
            if ( $zei_kbn == 0 && $zei_rtu != 0 ) { 
                $rec['zei_rtu'] = 0;
                $rec['zei_cd'] = 0;
            }
            $rtnSel[] = $rec;
        }

        return $rtnSel;
    }  
    /**
     * 消費税額を計算して返す
     *
     * <AUTHOR> sai
     * @since 2019/09/XX
     * @param   array        $select
     * @return  array()|null
     */
    protected static function _getSomeShohizeiSub2($select)
    {
        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $rtnData = array();
        // ex. array( array('非課税金額', 10000, array(...)),
        //            array('10％税抜対象額', 1000000, array(...)),
        //            array('消費税', 100000, array(...)),

        // 非課税金額
        // 複数行はないと思われるが一応 sum する
        $hiRec = null;
        $price = array_reduce( $select, function ($accm, $rec) use (&$hiRec) {
                if ( $rec['zei_kbn'] == 0 ) {
                    $hiRec = $rec;
                    $accm += $rec['price'];
                }
                return $accm; }, 0);
        $price_i = array_reduce( $select, function ($accm, $rec) use (&$hiRec) {
                if ( $rec['zei_kbn'] == 0 ) {
                    $hiRec = $rec;
                    $accm += $rec['price_i'];
                }
                return $accm; }, 0);
        $price_n = array_reduce( $select, function ($accm, $rec) use (&$hiRec) {
                if ( $rec['zei_kbn'] == 0 ) {
                    $hiRec = $rec;
                    $accm += $rec['price_n'];
                }
                return $accm; }, 0);
        if ( $hiRec !== null ) { // 金額 0 の場合もここでは返す
            $hiRec['type'] = 'price';
            $rtnData[] = array('zei_kbn' => 0,'cap' =>'非課税金額','price' => $price,'price_i' => $price_i,'price_n' => $price_n);
        }

        // 税種別ごとに出力
        foreach ( $select as $rec ) {
            if ( $rec['zei_kbn'] == 0 ) continue; // 非課税は処理済
            $price        = $rec['price'];
            $price_i        = $rec['price_i'];
            $price_n        = $rec['price_n'];
            $zei_kbn      = $rec['zei_kbn'];
            $zei_rtu      = $rec['zei_rtu'];
            $zei_hasu_kbn = $rec['zei_hasu_kbn'];
            $rec['zeiDtl'] = $rec['zei_kbn'].'-'.$rec['zei_rtu'];
            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額
            if ( $zei_kbn == 2 ) { // 2:外税
                if ($rec['reduced_tax_rate'] == 2) {
                   $cap = sprintf('＊%d％税抜対象額', $zei_rtu);
                } else {
                    $cap = sprintf('%d％税抜対象額', $zei_rtu);
                }
                $tax = $TaxPrc['ZeiPrc'];
                $cap2 = sprintf('消費税');
                $data = array('zei_kbn' => $zei_kbn,'cap' =>$cap, 'price' => $price, 'cap2' =>$cap2, 'tax' =>$tax,'price_i' => $price_i,'price_n' => $price_n);
                $rtnData[] = $data;
            } else if ( $zei_kbn == 1 ) { // 1:内税
                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('＊%d％税込対象額', $zei_rtu);
                } else {
                    $cap = sprintf('%d％税込対象額', $zei_rtu);
                }
                $tax = $TaxPrc['ZeiPrc'];
                $cap2 = sprintf('(内消費税)');
                $data = array('zei_kbn' => $zei_kbn,'cap' =>$cap, 'price' => $price, 'cap2' =>$cap2, 'tax' =>$tax,'price_i' => $price_i,'price_n' => $price_n);
                $rtnData[] = $data;
            } else {
                Msi_Sys_Utils::warn( sprintf("(cae0d49f)zei_kbn=%s!?", $rec['zei_kbn']) );
                continue;
            }
        }

        return $rtnData;
    }
    /**
     * 見積書用に受注明細伝票から消費税額を計算して返す
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getMitsuShohizeiEasy3($db, $sekoNo, $sekoNoSub='00', $dataKbn=1)
    {
        $data00 = App_KeigenUtils::getMitsuShohizei3($db, $sekoNo, $sekoNoSub, $dataKbn);
        $rtnData = static::_shohizeiEasyFilter3($data00);
        return $rtnData;
    }
    /**
     * 見積書用に受注明細伝票から消費税額を計算して返す(受注伝票番号版)
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getMitsuShohizeiEasy4($db, $denpyo_no)
    {
        $data00 = App_KeigenUtils::getMitsuShohizei4($db, $denpyo_no);
        $rtnData = static::_shohizeiEasyFilter3($data00);
        return $rtnData;
    }
    /**
     * 見積書用に受注明細伝票から消費税額を計算して返す
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     * セレモア見積書文言
     * <AUTHOR> kobayashi
     * @since 2022/08/02
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getMitsuShohizeiEasy5($db, $sekoNo, $sekoNoSub='00', $dataKbn=1)
    {
        $data00 = App_KeigenUtils::getMitsuShohizei5($db, $sekoNo, $sekoNoSub, $dataKbn);
        $rtnData = static::_shohizeiEasyFilter4($data00);
        return $rtnData;
    }
    /**
     * 見積書用に受注明細伝票から消費税額を計算して返す(受注伝票番号版)
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     * セレモア見積書文言
     * <AUTHOR> kobayashi
     * @since 2022/08/02
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getMitsuShohizeiEasy6($db, $denpyo_no)
    {
        $data00 = App_KeigenUtils::getMitsuShohizei6($db, $denpyo_no);
        $rtnData = static::_shohizeiEasyFilter4($data00);
        return $rtnData;
    }
    /**
     * 引取返金用に引取返金明細伝票から消費税額を計算して返す
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     *
     * <AUTHOR> Sugiyama
     * @since  2022/08/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getMitsuShohizeiEasy7($db, $sekoNo, $sekoNoSub='00', $dataKbn=1, $keijo_ymd)
    {
        $data00 = App_KeigenUtils::getMitsuShohizei7($db, $sekoNo, $sekoNoSub, $dataKbn, $keijo_ymd);
        $rtnData = static::_shohizeiEasyFilter3($data00);
        return $rtnData;
    }
    /**
     * 見積書用に受注明細伝票から消費税額を計算して返す
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getUriShohizeiEasy($db, $sekoNo, $sekoNoSub, $dataKbn)
    {
        $data00 = static::getUriShohizei($db, $sekoNo, $sekoNoSub, $dataKbn);
        $rtnData = static::_shohizeiEasyFilter3($data00);
        return $rtnData;
    }
    /**
     * 見積書用に受注明細伝票から消費税額を計算して返す
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()
     */
    public static function getMitsuShohizeiFromHistoryEasy($db, $sekoNo, $sekoNoSub, $dataKbn, $history_no)
    {
        $data00 = App_KeigenUtils::getMitsuShohizeiFromHistory($db, $sekoNo, $sekoNoSub, $dataKbn, $history_no);
        $rtnData = static::_shohizeiEasyFilter($data00);
        return $rtnData;
    }
    /**
     * 見積書用 受注明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()|null
     */
    public static function getMitsuShohizei3($db, $sekoNo, $sekoNoSub='00', $dataKbn=1)
    {
        $denpyo_no = $db->getOneVal( <<< END_OF_SQL
SELECT denpyo_no FROM juchu_denpyo 
 WHERE seko_no=? AND seko_no_sub=? AND data_kbn=?
   AND delete_flg=0
END_OF_SQL
                                     , array($sekoNo, $sekoNoSub, $dataKbn) );
        if ( $denpyo_no === null ) {
            return null;
        }

        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(juchu_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   FROM juchu_denpyo_msi jdm
  WHERE denpyo_no=:denpyo_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'denpyo_no'=>$denpyo_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub3($select);

        return $rtnData;
    }
    
    /**
     * 見積書用 受注明細伝票の sum から消費税額を計算して返す(受注伝票番号版)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @param   Msi_Sys_DB   $db
     * @param   string       $denpyo_no 
     * @return  array()|null
     */
    public static function getMitsuShohizei4($db, $denpyo_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(juchu_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   FROM juchu_denpyo_msi jdm
  WHERE denpyo_no=:denpyo_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'denpyo_no'=>$denpyo_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub3($select);

        return $rtnData;
    }    
    
    /**
     * 見積書用 受注明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> kobayashi
     * @since 2022/08/02
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()|null
     */
    public static function getMitsuShohizei5($db, $sekoNo, $sekoNoSub='00', $dataKbn=1)
    {
        $denpyo_no = $db->getOneVal( <<< END_OF_SQL
SELECT denpyo_no FROM juchu_denpyo 
 WHERE seko_no=? AND seko_no_sub=? AND data_kbn=?
   AND delete_flg=0
END_OF_SQL
                                     , array($sekoNo, $sekoNoSub, $dataKbn) );
        if ( $denpyo_no === null ) {
            return null;
        }

        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(juchu_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   FROM juchu_denpyo_msi jdm
  WHERE denpyo_no=:denpyo_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'denpyo_no'=>$denpyo_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub4($select);

        return $rtnData;
    }
    
    /**
     * 見積書用 受注明細伝票の sum から消費税額を計算して返す(受注伝票番号版)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @param   Msi_Sys_DB   $db
     * @param   string       $denpyo_no 
     * @return  array()|null
     */
    public static function getMitsuShohizei6($db, $denpyo_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(juchu_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   FROM juchu_denpyo_msi jdm
  WHERE denpyo_no=:denpyo_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'denpyo_no'=>$denpyo_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub4($select);

        return $rtnData;
    }    
    /**
     * 引取返金用 引取返金明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> Sugiyama
     * @since   2022/08/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()|null
     */
    public static function getMitsuShohizei7($db, $sekoNo, $sekoNoSub='00', $dataKbn=1, $keijo_ymd)
    {
        $denpyo_no = $db->getOneVal( <<< END_OF_SQL
SELECT uri_den_no FROM hikitori_denpyo 
 WHERE seko_no=? AND seko_no_sub=? AND data_kbn=?
   AND delete_flg=0
END_OF_SQL
                                     , array($sekoNo, $sekoNoSub, $dataKbn) );
        if ( $denpyo_no === null ) {
            return null;
        }
        $add_sql = "";
        if(isset($keijo_ymd)){
           $add_sql = "AND jdm.keijo_ymd = '$keijo_ymd'";
        }else if(is_null($keijo_ymd)){
           $add_sql = "AND jdm.keijo_ymd IS NULL"; 
        }
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,ABS(SUM(uri_prc - COALESCE(gojokai_nebiki_prc, 0) - nebiki_prc + hoshi_prc)) AS price
   FROM hikitori_denpyo_msi jdm
  WHERE uri_den_no=:uri_den_no
    AND delete_flg=0
    $add_sql
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'uri_den_no'=>$denpyo_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub3($select);

        return $rtnData;
    }
    /**
     * 請求明細確認書用 売上伝票明細の sum から消費税額を計算して返す
     *
     * <AUTHOR> Tosaka
     * @since 2020/xx/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $sekoNo 
     * @param   string       $sekoNoSub 
     * @param   int          $dataKbn 
     * @return  array()|null
     */
    public static function getUriShohizei($db, $sekoNo, $sekoNoSub='00', $dataKbn=1)
    {
        $uri_den_no = $db->getOneVal( <<< END_OF_SQL
SELECT uri_den_no FROM uriage_denpyo 
 WHERE seko_no=? AND seko_no_sub=? AND data_kbn=?
   AND delete_flg=0
END_OF_SQL
                                     , array($sekoNo, $sekoNoSub, $dataKbn) );
        if ( $uri_den_no === null ) {
            return null;
        }

        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(uri_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   FROM uriage_denpyo_msi udm
  WHERE uri_den_no = :uri_den_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'uri_den_no'=>$uri_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub3($select);

        return $rtnData;
    }     
    
    /**
     * 消費税額を計算して返す
     * (博全社見積書の文言に合わせる)
     *
     * <AUTHOR> kobayashi
     * @since 2020/07/08
     * @param   array        $select
     * @return  array()|null
     */
    protected static function _getSomeShohizeiSub3($select)
    {
        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $rtnData = array();
        // ex. array( array('非課税金額', 10000, array(...)),
        //            array('10％税抜対象額', 1000000, array(...)),
        //            array('消費税', 100000, array(...)),

        // 非課税金額
        // 複数行はないと思われるが一応 sum する
        $hiRec = null;
        $price = array_reduce( $select, function ($accm, $rec) use (&$hiRec) {
                if ( $rec['zei_kbn'] == 0 ) {
                    $hiRec = $rec;
                    $accm += $rec['price'];
                }
                return $accm; }, 0);
        if ( $hiRec !== null ) { // 金額 0 の場合もここでは返す
            $hiRec['type'] = 'price';
            $rtnData[] = array( '非課税対象分 小計', $price, $hiRec );
        }

        // 税種別ごとに出力
        foreach ( $select as $rec ) {
            if ( $rec['zei_kbn'] == 0 ) continue; // 非課税は処理済
            $price        = $rec['price'];
            $zei_kbn      = $rec['zei_kbn'];
            $zei_rtu      = $rec['zei_rtu'];
            $zei_hasu_kbn = $rec['zei_hasu_kbn'];
            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額
            if ( $zei_kbn == 2 ) { // 2:外税
                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('外税 消費税%d％軽減対象分 小計', $zei_rtu);
                } else {
                    $cap = sprintf('外税 消費税%d％対象分 小計', $zei_rtu);
                }
                $rec['type'] = 'price';
                $data = array( $cap, $price, $rec );
                $rtnData[] = $data;

                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('外税 消費税（%d％軽減対象）お預かり分', $zei_rtu);
                } else {
                    $cap = sprintf('外税 消費税（%d％対象）お預かり分', $zei_rtu);
                }
                $tax = $TaxPrc['ZeiPrc'];
//                $cap = sprintf('消費税');
                $rec['type'] = 'tax';
                $data = array( $cap, $tax, $rec );
                $rtnData[] = $data;
            } else if ( $zei_kbn == 1 ) { // 1:内税
                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('内税 消費税%d％軽減対象分 小計', $zei_rtu);
                } else {
                    $cap = sprintf('内税 消費税%d％対象分 小計', $zei_rtu);
                }
                $rec['type'] = 'price';
                $data = array( $cap, $price, $rec );
                $rtnData[] = $data;

                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('内税 消費税（%d％軽減対象）お預かり分', $zei_rtu);
                } else {
                    $cap = sprintf('内税 消費税（%d％対象）お預かり分', $zei_rtu);
                }
                $tax = $TaxPrc['ZeiPrc'];
//                $cap = sprintf('(内消費税)');
                $rec['type'] = 'tax';
                $data = array( $cap, $tax, $rec );
                $rtnData[] = $data;
            } else {
                Msi_Sys_Utils::warn( sprintf("(cae0d49f)zei_kbn=%s!?", $rec['zei_kbn']) );
                continue;
            }
        }

        return $rtnData;
    }
    /**
     * 消費税額を計算して返す
     * (セレモア見積書の文言に合わせる)
     *
     * <AUTHOR> kobayashi
     * @since 2022/08/02
     * @param   array        $select
     * @return  array()|null
     */
    protected static function _getSomeShohizeiSub4($select)
    {
        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $rtnData = array();
        // ex. array( array('非課税金額', 10000, array(...)),
        //            array('10％税抜対象額', 1000000, array(...)),
        //            array('消費税', 100000, array(...)),

        // 非課税金額
        // 複数行はないと思われるが一応 sum する
        $hiRec = null;
        $price = array_reduce( $select, function ($accm, $rec) use (&$hiRec) {
                if ( $rec['zei_kbn'] == 0 ) {
                    $hiRec = $rec;
                    $accm += $rec['price'];
                }
                return $accm; }, 0);
        if ( $hiRec !== null ) { // 金額 0 の場合もここでは返す
            $hiRec['type'] = 'price';
            $rtnData[] = array( '非課税対象額', $price, $hiRec );
        }

        // 税種別ごとに出力
        foreach ( $select as $rec ) {
            if ( $rec['zei_kbn'] == 0 ) continue; // 非課税は処理済
            $price        = $rec['price'];
            $zei_kbn      = $rec['zei_kbn'];
            $zei_rtu      = $rec['zei_rtu'];
            $zei_hasu_kbn = $rec['zei_hasu_kbn'];
            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額
            if ( $zei_kbn == 2 ) { // 2:外税
                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('外税%d％軽減対象額', $zei_rtu);
                } else {
                    $cap = sprintf('外税%d％対象額', $zei_rtu);
                }
                $rec['type'] = 'price';
                $data = array( $cap, $price, $rec );
                $rtnData[] = $data;

                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('(外税軽減消費税)', $zei_rtu);
                } else {
                    $cap = sprintf('(外税消費税)', $zei_rtu);
                }
                $tax = $TaxPrc['ZeiPrc'];
//                $cap = sprintf('消費税');
                $rec['type'] = 'tax';
                $data = array( $cap, $tax, $rec );
                $rtnData[] = $data;
            } else if ( $zei_kbn == 1 ) { // 1:内税
                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('内税%d％軽減対象額', $zei_rtu);
                } else {
                    $cap = sprintf('内税%d％対象額', $zei_rtu);
                }
                $rec['type'] = 'price';
                $data = array( $cap, $price, $rec );
                $rtnData[] = $data;

                if ($rec['reduced_tax_rate'] == 2) {
                    $cap = sprintf('(内税軽減消費税)', $zei_rtu);
                } else {
                    $cap = sprintf('(内税消費税)', $zei_rtu);
                }
                $tax = $TaxPrc['ZeiPrc'];
//                $cap = sprintf('(内消費税)');
                $rec['type'] = 'tax';
                $data = array( $cap, $tax, $rec );
                $rtnData[] = $data;
            } else {
                Msi_Sys_Utils::warn( sprintf("(cae0d49f)zei_kbn=%s!?", $rec['zei_kbn']) );
                continue;
            }
        }

        return $rtnData;
    }
    /**
     * Easy 版の編集してデータを返す処理
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   array        $data00
     * @return  array()
     */
    protected static function _shohizeiEasyFilter3($data00)
    {
        if ( $data00 === null ) {
            return array();
        }

        $rtnData = array();
        foreach ( $data00 as $rec ) {
            $recDetail = $rec[2];
            $zei_kbn = $recDetail['zei_kbn'];
            $price   = $recDetail['price'];
            $type    = $recDetail['type'];
            if ( $zei_kbn == 0 && $price == 0 ) { // 非課税で対象金額ゼロなら出力しない
                continue;
            }
            $cap     = $rec[0];
            $kingaku = $rec[1];
//            if ( $type == 'tax' ) {
//                $cap = '　' . $cap; // インデントをつける
//            }
            $recRtn = array( $cap, $kingaku );
            $rtnData[] = $recRtn;
        }
        return $rtnData;
    }
    /**
     * Easy 版の編集してデータを返す処理(_shohizeiEasyFilter3の種別付き)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   array        $data00
     * @return  array()
     */
    protected static function _shohizeiEasyFilter4($data00)
    {
        if ( $data00 === null ) {
            return array();
        }

        $rtnData = array();
        foreach ( $data00 as $rec ) {
            $recDetail = $rec[2];
            $zei_kbn = $recDetail['zei_kbn'];
            $price   = $recDetail['price'];
            $type    = $recDetail['type'];
            if ( $zei_kbn == 0 && $price == 0 ) { // 非課税で対象金額ゼロなら出力しない
                continue;
            }
            $cap     = $rec[0];
            $kingaku = $rec[1];
//            if ( $type == 'tax' ) {
//                $cap = '　' . $cap; // インデントをつける
//            }
            $recRtn = array( $cap, $kingaku, $recDetail );
            $rtnData[] = $recRtn;
        }
        return $rtnData;
    }
    
    /**
     * 領収書用 ryosyusho_history_ からその分の消費税額を返す
     * ex. array( array('10％対象額', 10000,  '(内消費税)', 910),
     *            array('8%対象額', 10000, '(内消費税)', 741) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> Sugiyama
     * @since 2020/11/xx
     * @param   Msi_Sys_DB   $db
     * @params  string   $seikyu_den_no
     * @params  integer  $hako_count
     * @return  array()
     */
    public static function getSeikyuShohizeiEasy6($db, $seikyu_den_no, $hako_count)
    {
        $uridenRec = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $seikyu_den_no));
        if ($uridenRec === null) {
            throw new Exception(sprintf('請求伝票が存在しません(udn:%s)', $seikyu_den_no));
        }
        $ryoshuRec = DataMapper_RyosyushoHistory::findOne($db, array('uri_den_no' => $seikyu_den_no, 'hako_count' => $hako_count));
        if ($ryoshuRec === null) {
            throw new Exception(sprintf('領収書データが存在しません(udn:%s,cnt:%s)', $seikyu_den_no, $hako_count));
        }

        $select = App_KeigenUtils::getSeikyuShohizei02Uri($db, $seikyu_den_no);

        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...

        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd       = +$rec['zei_cd'];
            list($zei_rtu00, $zei_hasu_kbn00, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($zei_cd);

            if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            if ( $zei_kbn == 0 ) continue; // 非課税は skip

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0 );
            }

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
            $_wkData[ $zei_rtu ]['zei_cd'] = $zei_cd;
            $_wkData[ $zei_rtu ]['reduced_tax_rate'] = $reduced_tax_rate;
        }

        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ( $_wkData as $wkK => $wkRec ) {
            if ( $wkRec['reduced_tax_rate'] == 2 ) { // 軽減税率
                $cap1 = sprintf("%d%%対象額", $wkK);
                $amt1= +$ryoshuRec['genkin_prc_keigen'] + $ryoshuRec['kogite_prc_keigen'] + $ryoshuRec['furikomi_prc_keigen'];
                $cap2 = "(内消費税)";
                $amt2 = +$ryoshuRec['zei_prc_keigen'];
            } else { // 標準税率
                $cap1 = sprintf("%d%%対象額", $wkK);
                $amt1= +$ryoshuRec['genkin_prc'] + $ryoshuRec['kogite_prc'] + $ryoshuRec['furikomi_prc']
                    - ( $ryoshuRec['genkin_prc_keigen'] + $ryoshuRec['kogite_prc_keigen'] + $ryoshuRec['furikomi_prc_keigen'] )
                    - ( $ryoshuRec['genkin_prc_hikazei'] + $ryoshuRec['kogite_prc_hikazei'] + $ryoshuRec['furikomi_prc_hikazei'] );
                $cap2 = "(内消費税)";
                $amt2 = +$ryoshuRec['zei_prc_std'];
            }
            $_arr = array($cap1, $amt1, $cap2, $amt2);
            $rtnData[] = $_arr;
        }

        return $rtnData;
    }    
    /**
     * 請求書用 売上明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> Sugiyama
     * @since 2020/11/xx
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @version 2019/09/10 MSI Sai 一般合計と値引き合計を追加
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no
     * @return  array()|null
     */
    protected static function getSeikyuShohizei02Uri($db, $seikyu_den_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(uri_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   ,SUM(uri_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price_i
   ,SUM(COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc) AS price_n
   FROM seikyu_denpyo_msi jdm
  WHERE seikyu_den_no=:seikyu_den_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'seikyu_den_no'=>$seikyu_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        // patch: 非課税で 税率設定(>0)されている場合は強制的に zei_rtu,zei_cd をゼロにする        
        $rtnSel = array();
        foreach ( $select as $rec ) {
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_cd       = +$rec['zei_cd'];
            if ( $zei_kbn == 0 && $zei_rtu != 0 ) { 
                $rec['zei_rtu'] = 0;
                $rec['zei_cd'] = 0;
            }
            $rtnSel[] = $rec;
        }

        return $rtnSel;
    }
    /**
     * 領収書発行画面用 売上明細伝票から消費税額を計算して返す
     * 税率で対象額をまとめる
     * ex. array( array( 'taisho_cap' => '非課税金額', 'taisho_gaku' => 10000, 'zei_gaku' => 0 ),
     *            array( 'taisho_cap' => '10％対象額', 'taisho_gaku' => 10000, 'zei_gaku' => 910),
     *            array( 'taisho_cap' => '8％対象額',  'taisho_gaku' => 10000, 'zei_gaku' => 741 ) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no
     * @return  array()
     */
    public static function getSeikyuShohizeiEasy333($db, $seikyu_den_no)
    {
        $select = static::getSeikyuShohizei00seikyu($db, $seikyu_den_no);

        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...

        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd       = +$rec['zei_cd'];

            //            if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            //            if ( $zei_kbn == 0 ) continue; // 非課税は skip

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0, 'zei_rtu'=>$zei_rtu, 'zei_kbn'=>$zei_kbn,
                                              'zei_cd' => $zei_cd );
            }

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
        }

        // 互助会（友の会）、後掛金、供物未精算 を調整   2019/10/xx mihara
        static::_adjEtcSeikyuShohizei($db, $seikyu_den_no, $_wkData);

        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ( $_wkData as $wkK => $wkRec ) {
            if ( $wkK == 0 ) {
                $cap1 = '非課税金額';
                $zei_kbn = 0;
            } else {
                $cap1 = sprintf("%d%%対象額", $wkK);
                $zei_kbn = +$wkRec['zei_kbn'];
            }
            $amt1 = +$wkRec['totalPrice'];
            $cap2 = "(内消費税)";
            $amt2 = +$wkRec['ZeiPrc'];
            $_arr = array( 'taisho_cap'  => $cap1, 
                           'taisho_gaku' => $amt1,
                           'zei_gaku'    => $amt2 );
            $_arr['zei_kbn'] = $zei_kbn;
            $_arr['zei_rtu'] = +$wkRec['zei_rtu'];
            $zei_cd = +$wkRec['zei_cd'];
            $_arr['zei_cd']  = $zei_cd;
            $_arr['reduced_tax_rate']  = DataMapper_ZeiMstEasy::getReducedTaxRate($zei_cd);
            $rtnData[] = $_arr;
        }

        // Msi_Sys_Utils::debug( 'rtnData PRE=>' . Msi_Sys_Utils::dump($rtnData) );
        // 出力順に並び替え
        // zei_kbn=0(非課税が先頭), 次に外税,内税の順で、外税,内税内では税率が高い順
        usort( $rtnData, function($a, $b) {
                if ( $a['zei_kbn'] == 0 ) return -1;
                if ( $b['zei_kbn'] == 0 ) return +1;
                return $a['zei_kbn'] > $b['zei_kbn'] ? -1 : 
                    ( $a['zei_rtu'] > $b['zei_rtu'] ? -1 : +1  );
            } );
        // Msi_Sys_Utils::debug( 'rtnData post=>' . Msi_Sys_Utils::dump($rtnData) );

        return $rtnData;
    }
    /**
     * 互助会（友の会）、後掛金、供物未精算 を調整
     *
     * <AUTHOR> mihara
     * @since   2019/10/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no
     * @param   array        $_wkData
     * @param   array        $data_kbn
     * @return  void
     */
    public static function _adjEtcSeikyuShohizei($db, $seikyu_den_no, &$_wkData, $data_kbn = null)
    {
        $seko_no = $db->getOneVal( 'SELECT seko_no FROM seikyu_denpyo WHERE seikyu_den_no=? and delete_flg=0',
                                   array($seikyu_den_no) );
        if ( $seko_no === null ) {
            return;
        }
        $bun_gas_kbn_num = $db->getOneVal( 'SELECT bun_gas_kbn_num FROM seikyu_denpyo WHERE seikyu_den_no=? and delete_flg=0',
                                   array($seikyu_den_no) );
        if($bun_gas_kbn_num == 2){  // 請求分割
            return;
        }
        // cf. outDataFaceSum01() in Pdf1101Controller.ipal_k.php (PDF 請求書)
        // cf. addKeiyakuGaku() in KeigenUtils.linkmore_k.php/KeigenUtils.tojo.php

        // 最大税率
        $max_zei_rtu = max(array_keys($_wkData));
        $max_zei_cd  = $_wkData[$max_zei_rtu]['zei_cd'];

        // 葬儀日取得
        $sougi_ymd = static::getSougiYmd($seko_no);

        // 葬儀日の消費税
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $sougi_ymd);
        $sougi_zei_rtu      = +$taxInfo['zei_rtu'];
        $sougi_zei_hasu_kbn = +$taxInfo['zei_hasu_kbn'];

        //
        // 互助会関連 調整
        //
        $tomo_cose  = 0; // 友の会利用コース
        $atokake    = 0; // 後掛金
        $zei_sagaku = 0; // 旧税率適用差額
        $uchikin_prc = 0; // 内金
        // 内金取得
        $sql = <<< END_OF_SQL
        SELECT COALESCE(SUM(uchikin_prc),0) AS uchikin_prc
        FROM seikyu_denpyo WHERE seikyu_den_no = :seikyu_den_no AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelect( $sql, array( 'seikyu_den_no'=>$seikyu_den_no));
        $uchikin_prc = $select[0]['uchikin_prc'];
        
        if(isset($data_kbn) && strlen($data_kbn) > 0){
            $denpyo = DataMapper_JuchuDenpyo::findDenpyo2($db, array('seko_no' => $seko_no, 'data_kbn' => $data_kbn)); // 受注伝票取得
        }else{
            $denpyo = DataMapper_JuchuDenpyo::findDenpyo2($db, array('seko_no' => $seko_no)); // 受注伝票取得
        }
        if(count($denpyo) > 0){
            $tomo_cose  = +$denpyo[0]['sougi_keiyaku_prc'] * -1;                            // 契約金額
            $atokake    = +$denpyo[0]['sougi_keiyaku_prc'] + $denpyo[0]['sougi_harai_prc']; // 後掛金
            $zei_sagaku = +$denpyo[0]['sougi_zei_sagaku_prc'];                              // 旧税率適用差額
        }
        $tomo_cose_flg = false;
        if($tomo_cose != 0){
            $tomo_cose_flg = true;
        }
        if(!$tomo_cose_flg){
            if($tomo_cose == 0){
                $kaiyakuData = DataMapper_SekoGojokaiMember::find($db, array(
                                        'seko_no' => $seko_no,          // 施行番号
                                        '__raw_1' => 'yoto_kbn IN (5)'  // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                ));
                $harai_gaku = 0;
                foreach ($kaiyakuData as $kaiyaku) {
                    $harai_gaku += $kaiyaku['harai_gaku'];
                }
                if($harai_gaku > 0){
                    $tomo_cose_flg = true;
                }else{
                    // 金額充当
                    $tmpGojoData = DataMapper_SekoGojokaiMember::find($db, array(
                                                                              'seko_no' => $seko_no,  // 施行番号
                                                                              '__raw_1' => 'yoto_kbn IN (3)'         // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                                                              ));
                    if(Msi_Sys_Utils::myCount($tmpGojoData) > 0){
                        $tomo_cose_flg = true;
                    }
                }
            }
        }
        if ( $tomo_cose_flg ) { // 友の会利用 あり
            $total_gojo_keiyaku  = 0; // 互助会契約額
            $total_gojo_harai    = 0; // 互助会払込金額計
            $total_gojo_zei_past = 0; // 互助会加入時ベースの税額
            $total_gojo_zei_now  = 0; // 現在時点の税額
            $adj_totalPrice      = 0; // totalPrice の調整用
            $adj_ZeiPrc          = 0; // ZeiPrc の調整用
            $wari_gaku           = 0; // 割引金額
            $early_use_cost      = 0; // 早期利用費
            $early_use_cost_zei  = 0; // 早期利用費の消費税
            $meigi_chg_cost      = 0; // 名変手数料
            $meigi_chg_cost_zei  = 0; // 名変手数料の消費税
            $_atokake            = 0;
            $total_zangaku       = 0; // 残金額
            $total_juto          = 0; // 金額充当
            $total_other_zei     = 0;
            $total_kannnou_gaku  = 0;
            // 契約時点の税で調整するため SekoGojokaiMember から再度データ取得している
            $gojoData = DataMapper_SekoGojokaiMember::find($db, array(
                                                                      'seko_no' => $seko_no,  // 施行番号
                                                                      '__raw_1' => 'yoto_kbn IN (1, 2, 3, 5)'         // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                                                      ));
            foreach ($gojoData as $gojo) {
                if($gojo['yoto_kbn'] == 1) {
                    $total_zangaku += 
                                      $gojo['keiyaku_gaku']
                                    - $gojo['harai_gaku']
                                    - $gojo['wari_gaku']
                                    - $gojo['wari_gaku_tax']
                                    - $gojo['warimashi_gaku']
                                    + $gojo['kanyu_tax']
                                    + $gojo['early_use_cost']       // 早期利用費
                                    + $gojo['early_use_cost_zei'];  // 早期利用費消費税
                    $total_other_zei +=
                                    + $gojo['early_use_cost_zei'];  // 早期利用費消費税
                }else if($gojo['yoto_kbn'] == 3){
                    $total_juto += ($gojo['keiyaku_gaku'] * -1) + $gojo['minou_gaku'];
                }
            }
            // 最大税率で調整
            $_wkData[$max_zei_rtu]['totalPrice'] += $total_juto + $total_zangaku;
            $_wkData[$max_zei_rtu]['ZeiPrc'] += $total_other_zei;
            $_wkData_tmp = $_wkData;
            
            foreach ($gojoData as $gojo2){
                if($gojo2['yoto_kbn'] == 1){
                    // 会費残金額 ⇒ 契約金額(keiyaku_gaku) - 払込金額(harai_gaku) - 前納割引額(wari_gaku) - 数字フリー項目3(n_free3)※プレミアム割引 - 割引額(waribiki_gaku)
                    // 会費消費税 ⇒ 加入時消費税(kanyu_tax)
                    //$totalPrice = $gojo2['keiyaku_gaku'] + $gojo2['kanyu_tax'] - $gojo2['harai_gaku'] - $gojo2['wari_gaku'] - $gojo2['premium_gaku'] - $gojo2['waribiki_gaku'];
                    $totalPrice = 0;
                    if($totalPrice <> 0){
                        if(array_key_exists($gojo2['kake_zei_rtu2'] ,$_wkData_tmp) && $_wkData_tmp[$gojo2['kake_zei_rtu2']]['reduced_tax_rate'] == 2){
                            if(array_key_exists($gojo2['kake_zei_rtu2']."_1", $_wkData_tmp)){
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['totalPrice']      += $totalPrice;
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['ZeiPrc']          += $gojo2['kanyu_tax'];
                            }else{
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['totalPrice']       = $totalPrice;
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['ZeiPrc']           = $gojo2['kanyu_tax'];
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['zei_cd']           = $gojo2['zei_cd'];
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['zei_rtu']          = $gojo2['kake_zei_rtu2'];
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['zei_kbn']          = 1;
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['reduced_tax_rate'] = 1;
                            }
                        }else{
                            if(array_key_exists($gojo2['kake_zei_rtu2'], $_wkData_tmp)){
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']]['totalPrice']      += $totalPrice;
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']]['ZeiPrc']          += $gojo2['kanyu_tax'];
                            }else{
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']]['totalPrice']       = $totalPrice;
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']]['ZeiPrc']           = $gojo2['kanyu_tax'];
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']]['zei_cd']           = $gojo2['zei_cd'];
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']]['zei_rtu']          = $gojo2['kake_zei_rtu2'];
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']]['zei_kbn']          = 1;
                                $_wkData_tmp[$gojo2['kake_zei_rtu2']]['reduced_tax_rate'] = 1;
                            }                            
                        }
                    }
                }
            }
            $_wkData = $_wkData_tmp;
        }
        //Msi_Sys_Utils::debug( '*** _wkData=>' . Msi_Sys_Utils::dump($_wkData) );
        // Msi_Sys_Utils::debug( '*** sum(totalPrice) => ' . array_sum(array_column($_wkData, 'totalPrice')) );
        // Msi_Sys_Utils::debug( '*** sum(zeiPrc) => ' . array_sum(array_column($_wkData, 'ZeiPrc')) );
        // Msi_Sys_Utils::debug( '*** sum(zeiPrc)+zei_sagaku => ' . (array_sum(array_column($_wkData, 'ZeiPrc'))-$zei_sagaku) );
    }
    /**
     * 互助会（友の会）、後掛金、供物未精算 を調整
     *
     * <AUTHOR> mihara
     * @since   2019/10/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no
     * @param   array        $_wkData
     * @param   array        $data_kbn
     * @return  void
     */
    public static function _adjEtcSeikyuShohizeiIkatu($db, $seikyu_den_no, &$_wkData, $data_kbn)
    {
        $seko_no = $db->getOneVal( 'SELECT seko_no FROM seikyu_denpyo WHERE seikyu_den_no=? and delete_flg=0',
                                   array($seikyu_den_no) );
        if ( $seko_no === null ) {
            return;
        }
        $bun_gas_kbn_num = $db->getOneVal( 'SELECT bun_gas_kbn_num FROM seikyu_denpyo WHERE seikyu_den_no=? and delete_flg=0',
                                   array($seikyu_den_no) );
        if($bun_gas_kbn_num == 2){  // 請求分割
            return;
        }
        // cf. outDataFaceSum01() in Pdf1101Controller.ipal_k.php (PDF 請求書)
        // cf. addKeiyakuGaku() in KeigenUtils.linkmore_k.php/KeigenUtils.tojo.php

        // 最大税率
        $max_zei_rtu = max(array_keys($_wkData));
        $max_zei_cd  = $_wkData[$max_zei_rtu]['zei_cd'];
        
        // 葬儀日取得
        $sougi_ymd = static::getSougiYmd($seko_no);

        // 葬儀日の消費税
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $sougi_ymd);
        $sougi_zei_rtu      = +$taxInfo['zei_rtu'];
        $sougi_zei_hasu_kbn = +$taxInfo['zei_hasu_kbn'];

        //
        // 互助会関連 調整
        //
        $tomo_cose  = 0; // 友の会利用コース
        $atokake    = 0; // 後掛金
        $zei_sagaku = 0; // 旧税率適用差額
        $uchikin_prc = 0; // 内金
        // 内金取得
        $sql = <<< END_OF_SQL
        SELECT COALESCE(SUM(uchikin_prc),0) AS uchikin_prc
        FROM seikyu_denpyo WHERE seikyu_den_no = :seikyu_den_no AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelect( $sql, array( 'seikyu_den_no'=>$seikyu_den_no));
        $uchikin_prc = $select[0]['uchikin_prc'];
        $denpyo = DataMapper_JuchuDenpyo::findDenpyo3($db, array('seko_no' => $seko_no, 'data_kbn' => $data_kbn)); // 受注伝票取得
        if(count($denpyo) > 0){
            $tomo_cose  = +$denpyo[0]['sougi_keiyaku_prc'] * -1;                            // 契約金額
            $atokake    = +$denpyo[0]['sougi_keiyaku_prc'] + $denpyo[0]['sougi_harai_prc']; // 後掛金
            $zei_sagaku = +$denpyo[0]['sougi_zei_sagaku_prc'];                              // 旧税率適用差額
        }
        $tomo_cose_flg = false;
        if($tomo_cose != 0){
            $tomo_cose_flg = true;
        }
        if(!$tomo_cose_flg){
            if($tomo_cose == 0){
                $kaiyakuData = DataMapper_SekoGojokaiMember::find($db, array(
                                        'seko_no' => $seko_no,          // 施行番号
                                        '__raw_1' => 'yoto_kbn IN (5)'  // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                ));
                $harai_gaku = 0;
                foreach ($kaiyakuData as $kaiyaku) {
                    $harai_gaku += $kaiyaku['harai_gaku'];
                }
                if($harai_gaku > 0){
                    $tomo_cose_flg = true;
                }else{
                    // 金額充当
                    $tmpGojoData = DataMapper_SekoGojokaiMember::find($db, array(
                                                                              'seko_no' => $seko_no,  // 施行番号
                                                                              '__raw_1' => 'yoto_kbn IN (3)'         // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                                                              ));
                    if(Msi_Sys_Utils::myCount($tmpGojoData) > 0){
                        $tomo_cose_flg = true;
                    }
                }
            }
        }
        if ( $tomo_cose_flg ) { // 友の会利用 あり
            $total_gojo_keiyaku  = 0; // 互助会契約額
            $total_gojo_harai    = 0; // 互助会払込金額計
            $total_gojo_zei_past = 0; // 互助会加入時ベースの税額
            $total_gojo_zei_now  = 0; // 現在時点の税額
            $adj_totalPrice      = 0; // totalPrice の調整用
            $adj_ZeiPrc          = 0; // ZeiPrc の調整用
            $wari_gaku           = 0; // 割引金額
            $early_use_cost      = 0; // 早期利用費
            $early_use_cost_zei  = 0; // 早期利用費の消費税
            $meigi_chg_cost      = 0; // 名変手数料
            $meigi_chg_cost_zei  = 0; // 名変手数料の消費税
            $_atokake            = 0;
            $total_zangaku       = 0; // 残金額
            $total_juto          = 0; // 金額充当
            $total_other         = 0; // その他の互助会関係
            $total_other_zei     = 0;
            $total_kannnou_gaku  = 0;
            // 契約時点の税で調整するため SekoGojokaiMember から再度データ取得している
            $gojoData = DataMapper_SekoGojokaiMember::find($db, array(
                                                                      'seko_no' => $seko_no,  // 施行番号
                                                                      '__raw_1' => 'yoto_kbn IN (1, 2, 3, 5)'         // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                                                      ));
            foreach ($gojoData as $gojo) {
                if($gojo['yoto_kbn'] == 1) {
                    $total_kannnou_gaku += - $gojo['kannnou_gaku'];  // 完納後割増
                }else if($gojo['yoto_kbn'] == 3){
                    $total_juto += ($gojo['harai_gaku'] * -1) + $gojo['meigi_chg_cost'] + $gojo['meigi_chg_cost_zei'];
                }
            }
            // 最大税率で調整
            $_wkData[$max_zei_rtu]['totalPrice'] += $total_juto + $total_kannnou_gaku;
            $_wkData_tmp = $_wkData;
            
            foreach ($gojoData as $gojo2){
                if($gojo2['yoto_kbn'] == 1){
                    // 会費残金額 ⇒ 契約金額(keiyaku_gaku) - 払込金額(harai_gaku) - 前納割引額(wari_gaku) - 数字フリー項目3(n_free3)※プレミアム割引 - 割引額(waribiki_gaku)
                    // 会費消費税 ⇒ 加入時消費税(kanyu_tax)
                    $totalPrice = $gojo2['keiyaku_gaku'] + $gojo2['kanyu_tax'] - $gojo2['harai_gaku'] - $gojo2['wari_gaku'] - $gojo2['premium_gaku'] - $gojo2['waribiki_gaku'];
                    if($totalPrice <> 0){
                        if(array_key_exists($gojo2['kake_zei_rtu2']."_1" ,$_wkData_tmp)){
                            $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['totalPrice']       += $totalPrice;
                            $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['ZeiPrc']           += $gojo2['kanyu_tax'];
                        }else{
                            $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['totalPrice']       = $totalPrice;
                            $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['ZeiPrc']           = $gojo2['kanyu_tax'];
                            $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['zei_cd']           = $gojo2['zei_cd'];
                            $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['zei_rtu']          = $gojo2['kake_zei_rtu2'];
                            $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['zei_kbn']          = 1;
                            $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['reduced_tax_rate'] = 1;
                            $_wkData_tmp[$gojo2['kake_zei_rtu2']."_1"]['gojo_flg']         = 1;
                        }
                    }
                    // 早期利用費 + 早期利用費消費税 + 名義変更手数料 + 名義変更手数料消費税
                    $total_zangaku = $gojo2['early_use_cost'] + $gojo2['early_use_cost_zei'] + $gojo2['meigi_chg_cost'] + $gojo2['meigi_chg_cost_zei'];
                    // 早期利用費消費税 + 名義変更手数料消費税
                    $total_other_zei = $gojo2['early_use_cost_zei'] + $gojo2['meigi_chg_cost_zei'];
                    if($total_zangaku <> 0){
                        if(array_key_exists($max_zei_rtu."_1" ,$_wkData_tmp)){
                            // 該当する税率に加算
                            $_wkData_tmp[$max_zei_rtu."_1"]['totalPrice']      += $total_zangaku;
                            $_wkData_tmp[$max_zei_rtu."_1"]['ZeiPrc']          += $total_other_zei;
                        }else{
                            // 該当する税率が無い場合に追加
                            $_wkData_tmp[$max_zei_rtu."_1"]['totalPrice']       = $total_zangaku;
                            $_wkData_tmp[$max_zei_rtu."_1"]['ZeiPrc']           = $total_other_zei;
                            $_wkData_tmp[$max_zei_rtu."_1"]['zei_cd']           = $max_zei_cd;
                            $_wkData_tmp[$max_zei_rtu."_1"]['zei_rtu']          = $max_zei_rtu;
                            $_wkData_tmp[$max_zei_rtu."_1"]['zei_kbn']          = 1;
                            $_wkData_tmp[$max_zei_rtu."_1"]['reduced_tax_rate'] = 1;
                            $_wkData_tmp[$max_zei_rtu."_1"]['gojo_flg']         = 1;
                        }
                    }
                }
            }
            $_wkData = $_wkData_tmp;
        }
        //Msi_Sys_Utils::debug( '*** _wkData=>' . Msi_Sys_Utils::dump($_wkData) );
        // Msi_Sys_Utils::debug( '*** sum(totalPrice) => ' . array_sum(array_column($_wkData, 'totalPrice')) );
        // Msi_Sys_Utils::debug( '*** sum(zeiPrc) => ' . array_sum(array_column($_wkData, 'ZeiPrc')) );
        // Msi_Sys_Utils::debug( '*** sum(zeiPrc)+zei_sagaku => ' . (array_sum(array_column($_wkData, 'ZeiPrc'))-$zei_sagaku) );
    }
    /**
     * 互助会（友の会）、後掛金、供物未精算 を調整
     *
     * <AUTHOR> Sugiyama
     * @since   2021/07/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no
     * @param   array        $_wkData
     * @return  void
     */
    public static function _adjEtcSeikyuShohizei02($db, $seikyu_den_no, &$_wkData)
    {
        $seko_no = $db->getOneVal( 'SELECT seko_no FROM seikyu_denpyo WHERE seikyu_den_no=? and delete_flg=0',
                                   array($seikyu_den_no) );
        if ( $seko_no === null ) {
            return;
        }
        $bun_gas_kbn_num = $db->getOneVal( 'SELECT bun_gas_kbn_num FROM seikyu_denpyo WHERE seikyu_den_no=? and delete_flg=0',
                                   array($seikyu_den_no) );
        if($bun_gas_kbn_num == 2){  // 請求分割
            return;
        }
        // cf. outDataFaceSum01() in Pdf1101Controller.ipal_k.php (PDF 請求書)
        // cf. addKeiyakuGaku() in KeigenUtils.linkmore_k.php/KeigenUtils.tojo.php

        // 最大税率
        $max_zei_rtu = max(array_keys($_wkData));

        // 葬儀日取得
        $sougi_ymd = static::getSougiYmd($seko_no);

        // 葬儀日の消費税
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $sougi_ymd);
        $sougi_zei_rtu      = +$taxInfo['zei_rtu'];
        $sougi_zei_hasu_kbn = +$taxInfo['zei_hasu_kbn'];

        //
        // 互助会関連 調整
        //
        $tomo_cose  = 0; // 友の会利用コース
        $atokake    = 0; // 後掛金
        $zei_sagaku = 0; // 旧税率適用差額
        
        $denpyo = DataMapper_JuchuDenpyo::findDenpyo2($db, array('seko_no' => $seko_no)); // 受注伝票取得
        if(count($denpyo) > 0){
            $tomo_cose  = +$denpyo[0]['sougi_keiyaku_prc'] * -1;   // 友の会利用コース
            $atokake    = +$denpyo[0]['sougi_keiyaku_prc'] + $denpyo[0]['sougi_harai_prc']; // 後掛金
                                                            // +$denpyo[0]['sougi_harai_prc'] OK. (not minus)
            $zei_sagaku = +$denpyo[0]['sougi_zei_sagaku_prc']; // 旧税率適用差額
        }
        $tomo_cose_flg = false;
        if($tomo_cose != 0){
            $tomo_cose_flg = true;
        }
        if(!$tomo_cose_flg){
            if($tomo_cose == 0){
                $kaiyakuData = DataMapper_SekoGojokaiMember::find($db, array(
                                        'seko_no' => $seko_no,          // 施行番号
                                        '__raw_1' => 'yoto_kbn IN (5)'  // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                ));
                $harai_gaku = 0;
                foreach ($kaiyakuData as $kaiyaku) {
                    $harai_gaku += $kaiyaku['harai_gaku'];
                }
                if($harai_gaku > 0){
                    $tomo_cose_flg = true;
                }else{
                    // 金額充当
                    $tmpGojoData = DataMapper_SekoGojokaiMember::find($db, array(
                                                                              'seko_no' => $seko_no,  // 施行番号
                                                                              '__raw_1' => 'yoto_kbn IN (3)'         // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                                                              ));
                    if(Msi_Sys_Utils::myCount($tmpGojoData) > 0){
                        $tomo_cose_flg = true;
                    }
                }
            }            
        }
        if ( $tomo_cose_flg ) { // 友の会利用 あり
            $total_gojo_keiyaku  = 0; // 互助会契約額
            $total_gojo_harai    = 0; // 互助会払込金額計
            $total_gojo_zei_past = 0; // 互助会加入時ベースの税額
            $total_gojo_zei_now  = 0; // 現在時点の税額
            $adj_totalPrice      = 0; // totalPrice の調整用
            $adj_ZeiPrc          = 0; // ZeiPrc の調整用
            $total_early_use_cost = 0; // 早期利用費

            // 契約時点の税で調整するため SekoGojokaiMember から再度データ取得している
            $gojoData = DataMapper_SekoGojokaiMember::find($db, array(
                                                                      'seko_no' => $seko_no,  // 施行番号
                                                                      '__raw_1' => 'yoto_kbn IN (1, 2, 3, 5)'         // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                                                      ));
            foreach ($gojoData as $gojo) {
                $keiyaku_gaku = +$gojo['keiyaku_gaku'];
                $harai_gaku   = +$gojo['harai_gaku'];
                $wari_gaku    = +$gojo['wari_gaku'];
                $_atokake     = $keiyaku_gaku - $harai_gaku;
                $kaihi_zan    = $keiyaku_gaku - $harai_gaku - $wari_gaku;
                $total_early_use_cost = $gojo['early_use_cost'] + $gojo['early_use_cost_zei'];

                $rec = array();
                $rec['zei_kbn']          = 2;   // 外税 => 契約金額は外税の為
                $rec['zei_rtu']          = 0;   // 互助会の税率 初期値
                $rec['reduced_tax_rate'] = 1;   // 軽減対象外
                if ( $gojo['zei_cd'] === null ) { $gojo['zei_cd'] = 0; } // 非課税の場合のパッチ                
                $rec['zei_cd']           = +$gojo['zei_cd']; // 税コード
                // 契約額用税率取得
                $zeiGojo = DataMapper_ZeiGojoMst::find($db, array('zei_cd' => $gojo['zei_cd']), false); // false:終了含む
                if ( count($zeiGojo) === 0 ) { continue; } // never

                $tomo_zei_rtu      = +$zeiGojo[0]['zei_rtu'];         // 互助会の税率
                $tomo_zei_hasu_kbn = +$zeiGojo[0]['zei_hasu_kbn'];    // 互助会の端数区分
                $tomo_zei_cd       = +$zeiGojo[0]['zei_cd'];          // 互助会の税CD

                $keiyaku_zei_gaku = App_ClsTaxLib::CalcTax($keiyaku_gaku, 2, $tomo_zei_rtu, $tomo_zei_hasu_kbn); // 2:外税

                $total_gojo_keiyaku  = $keiyaku_gaku; // 互助会契約額
                $total_gojo_harai    = $harai_gaku;   // 互助会払込金額
                $total_gojo_zei_past = $keiyaku_zei_gaku['ZeiPrc']; // 互助会加入時ベースの税額
                
                $keiyaku_zei_now = App_ClsTaxLib::CalcTax($keiyaku_gaku, 2, $sougi_zei_rtu, $sougi_zei_hasu_kbn); // 2:外税
                $total_gojo_zei_now = $keiyaku_zei_now['ZeiPrc'];

                // 残り分(後掛金)でなく契約金で税額を調整する

                // if ( $_atokake <= 0 ) { // 後掛金(支払残高)なし
                //     continue;
                // }
                //  $tomo_zei_gaku = App_ClsTaxLib::CalcTax($_atokake, 2, $tomo_zei_rtu, $tomo_zei_hasu_kbn); // 2:外税

                $tomo_zei_gaku = App_ClsTaxLib::CalcTax($keiyaku_gaku, 2, $tomo_zei_rtu, $tomo_zei_hasu_kbn); // 2:外税

                // コース金額は含まない為コメントアウト
                //if ( !array_key_exists($tomo_zei_rtu, $_wkData) ) {
                //   $_wkData[ $tomo_zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0,
                //                                       'zei_rtu' => $tomo_zei_rtu, 'zei_kbn' => 2,  // 2:外税
                //                                       'zei_cd' => $tomo_zei_cd );
                //}

                //$_wkData[$tomo_zei_rtu]['totalPrice'] += $tomo_zei_gaku['KomiPrc'];
                //$_wkData[$tomo_zei_rtu]['ZeiPrc']     += $tomo_zei_gaku['ZeiPrc'];

                $adj_totalPrice = $tomo_zei_gaku['KomiPrc'];
                $adj_ZeiPrc     = $tomo_zei_gaku['ZeiPrc'];
                // 最大税率で調整
                if($gojo['yoto_kbn'] == 1){         // コース施行
                    $zei_sagaku00 = $total_gojo_zei_now - $total_gojo_zei_past; // 旧税率適用差額
                    $_wkData[$max_zei_rtu]['totalPrice'] -= $total_gojo_keiyaku + $zei_sagaku00 - $total_early_use_cost - $kaihi_zan;
                    $_wkData[$max_zei_rtu]['ZeiPrc']     -= $adj_ZeiPrc + $zei_sagaku00;
                }else if($gojo['yoto_kbn'] == 2){   // プラン施行
                    $_wkData[$max_zei_rtu]['totalPrice'] -= $total_gojo_keiyaku - $total_early_use_cost - $kaihi_zan;
                    $_wkData[$max_zei_rtu]['ZeiPrc']     -= $adj_ZeiPrc;
                }else if($gojo['yoto_kbn'] == 3){   // 金額充当
                    $_wkData[$max_zei_rtu]['totalPrice'] -= $gojo['harai_gaku'];
                }else if($gojo['yoto_kbn'] == 5){   // 解約指図払い
                    $_wkData[$max_zei_rtu]['totalPrice'] -= $gojo['harai_gaku'];
                }
            }
        }

         Msi_Sys_Utils::debug( '*** _wkData=>' . Msi_Sys_Utils::dump($_wkData) );
        // Msi_Sys_Utils::debug( '*** sum(totalPrice) => ' . array_sum(array_column($_wkData, 'totalPrice')) );
        // Msi_Sys_Utils::debug( '*** sum(zeiPrc) => ' . array_sum(array_column($_wkData, 'ZeiPrc')) );
        // Msi_Sys_Utils::debug( '*** sum(zeiPrc)+zei_sagaku => ' . (array_sum(array_column($_wkData, 'ZeiPrc'))-$zei_sagaku) );
    }

    /**
     * 
     * 領収書発行画面用 売上明細伝票から消費税額を計算して返す
     * 税率で対象額をまとめる
     * 
     * ex. array( array( 'taisho_cap' => '非課税金額', 'taisho_gaku' => 10000, 'zei_gaku' => 0 ),
     *            array( 'taisho_cap' => '10％対象額', 'taisho_gaku' => 10000, 'zei_gaku' => 910),
     *            array( 'taisho_cap' => '8％対象額',  'taisho_gaku' => 10000, 'zei_gaku' => 741 ) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no 
     * @param   string       $data_kbn 
     * @param   boolean      $order 
     * @param   string       $dati_bunrui_cd 
     * @param   string       $zei_cd 
     * @return  array()
     */
    public static function getSeikyuShohizeiEasyRyoshu($db, $seikyu_den_no, $data_kbn = 1, $order = false, $dai_bunrui_cd = null, $zei_cd = null, $iaktsu_flg = false, $kyoka_flg = false)
    {
        $select = static::getSeikyuShohizeiRyoshu($db, $seikyu_den_no, $order, $dai_bunrui_cd, $zei_cd, $iaktsu_flg, $kyoka_flg, $data_kbn);

        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...

        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd       = +$rec['zei_cd'];

            //            if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            //            if ( $zei_kbn == 0 ) continue; // 非課税は skip

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0, 'zei_rtu'=>$zei_rtu, 'zei_kbn'=>$zei_kbn,
                                              'zei_cd' => $zei_cd );
            }

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
            $_wkData[ $zei_rtu ]['reduced_tax_rate'] = $rec['reduced_tax_rate'];
        }
        
        if($data_kbn == 1 || $data_kbn == 2){
            if($dai_bunrui_cd == null || $dai_bunrui_cd == '0010' || $dai_bunrui_cd == '0110'){
                // 互助会（友の会）、後掛金、供物未精算 を調整   2019/10/xx mihara
                static::_adjEtcSeikyuShohizei($db, $seikyu_den_no, $_wkData, $data_kbn);
            }
        }

        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ( $_wkData as $wkK => $wkRec ) {
            if ( $wkK == 0 ) {
                $cap1 = '非課税金額';
                $zei_kbn = 0;
            } else {
                if (isset($wkRec['reduced_tax_rate']) && $wkRec['reduced_tax_rate'] == 2) {
                    $cap1 = sprintf("＊%d%%対象額", $wkK);
                } else {
                    $cap1 = sprintf("%d%%対象額", $wkK);
                }
                $zei_kbn = +$wkRec['zei_kbn'];
            }
            $amt1 = +$wkRec['totalPrice'];
            $cap2 = "(内消費税)";
            $amt2 = +$wkRec['ZeiPrc'];
            $_arr = array( 'taisho_cap'  => $cap1, 
                           'taisho_gaku' => $amt1,
                           'zei_gaku'    => $amt2 );
            $_arr['zei_kbn'] = $zei_kbn;
            $_arr['zei_rtu'] = +$wkRec['zei_rtu'];
            $zei_cd = +$wkRec['zei_cd'];
            $_arr['zei_cd']  = $zei_cd;
            $_arr['reduced_tax_rate']  = DataMapper_ZeiMstEasy::getReducedTaxRate($zei_cd);
            $rtnData[] = $_arr;
        }

        // Msi_Sys_Utils::debug( 'rtnData PRE=>' . Msi_Sys_Utils::dump($rtnData) );
        // 出力順に並び替え
        // zei_kbn=0(非課税が先頭), 次に外税,内税の順で、外税,内税内では税率が高い順
        usort( $rtnData, function($a, $b) {
                if ( $a['zei_kbn'] == 0 ) return -1;
                if ( $b['zei_kbn'] == 0 ) return +1;
                return $a['zei_kbn'] > $b['zei_kbn'] ? -1 : 
                    ( $a['zei_rtu'] > $b['zei_rtu'] ? -1 : +1  );
            } );
        // Msi_Sys_Utils::debug( 'rtnData post=>' . Msi_Sys_Utils::dump($rtnData) );

        return $rtnData;
    }
    /**
     * 請求書用 売上明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @version 2019/09/10 MSI Sai 一般合計と値引き合計を追加
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no
     * @return  array()|null
     */
    protected static function getSeikyuShohizei00seikyu($db, $seikyu_den_no) {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(uri_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   ,SUM(uri_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price_i
   ,SUM(COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc) AS price_n
   FROM seikyu_denpyo_msi jdm
  WHERE seikyu_den_no=:seikyu_den_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
   ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'seikyu_den_no'=>$seikyu_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        // patch: 非課税で 税率設定(>0)されている場合は強制的に zei_rtu,zei_cd をゼロにする        
        $rtnSel = array();
        foreach ( $select as $rec ) {
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_cd       = +$rec['zei_cd'];
            if ( $zei_kbn == 0 && $zei_rtu != 0 ) { 
                $rec['zei_rtu'] = 0;
                $rec['zei_cd'] = 0;
            }
            $rtnSel[] = $rec;
        }

        return $rtnSel;
    }
    /**
     * 領収証要 売上明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @version 2019/09/10 MSI Sai 一般合計と値引き合計を追加
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no
     * @return  array()|null
     */
    protected static function getSeikyuShohizeiRyoshu($db, $seikyu_den_no, $order = false, $dai_bunrui_cd = null, $zei_cd = null, $ikatu_flg = false, $kyoka_flg = false, $data_kbn = null) {
        $order_sql = "ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順";
        if($order){
            $order_sql = "ORDER BY price DESC, (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順";
        }
        $dai_bunrui_sql = "";
        if($ikatu_flg && isset($dai_bunrui_cd)){
            // 一括発行
            if($dai_bunrui_cd == '0080' && ($data_kbn == 1 || $data_kbn == 2)){
                // 喪家供花個別発行　k_free1 => 1:個別発行
                $dai_bunrui_sql = "AND jdm.dai_bunrui_cd IN ('0080') AND COALESCE(udm.k_free1,0) IN (1)";
            }else if($data_kbn == 1 || $data_kbn == 2) {
                // 葬儀 OR 法事 領収証個別発行を省く
                $dai_bunrui_sql = "AND COALESCE(udm.k_free1,0) NOT IN (1)";
            }else{
                $dai_bunrui_sql = "AND jdm.dai_bunrui_cd IN ('$dai_bunrui_cd')";
            }
        }else if(isset($dai_bunrui_cd)){
            $dai_bunrui_sql = "AND jdm.dai_bunrui_cd IN ('$dai_bunrui_cd')";
        }
        $zei_cd_sql = "";
        if(isset($zei_cd)){
            $zei_cd_sql = "AND jdm.zei_cd = $zei_cd";
        }
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
     jdm.zei_kbn
    ,jdm.zei_cd
    ,SUM(jdm.uri_prc + COALESCE(jdm.gojokai_nebiki_prc, 0) + jdm.nebiki_prc + jdm.hoshi_prc + COALESCE(jdm.add_cost, 0)) AS price
    ,SUM(jdm.uri_prc + jdm.hoshi_prc + COALESCE(jdm.add_cost, 0)) AS price_i
    ,SUM(COALESCE(jdm.gojokai_nebiki_prc, 0) + jdm.nebiki_prc) AS price_n
 FROM seikyu_denpyo_msi jdm
 LEFT JOIN uriage_denpyo_msi udm
 ON  udm.seko_no    = jdm.seko_no
 AND udm.n_free1    = jdm.n_free1
 AND udm.delete_flg = 0
 WHERE jdm.seikyu_den_no=:seikyu_den_no
 AND jdm.delete_flg=0
 $dai_bunrui_sql
 $zei_cd_sql
 GROUP BY jdm.zei_kbn, jdm.zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
   $order_sql
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'seikyu_den_no'=>$seikyu_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        // patch: 非課税で 税率設定(>0)されている場合は強制的に zei_rtu,zei_cd をゼロにする        
        $rtnSel = array();
        foreach ( $select as $rec ) {
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_cd       = +$rec['zei_cd'];
            if ( $zei_kbn == 0 && $zei_rtu != 0 ) { 
                $rec['zei_rtu'] = 0;
                $rec['zei_cd'] = 0;
            }
            $rtnSel[] = $rec;
        }

        return $rtnSel;
    }
    /**
     * 領収書用 ryosyusho_history_ からその分の消費税額を返す
     * ex. array( array('10％対象額', 10000,  '(内消費税)', 910),
     *            array('8%対象額'  , 10000,  '(内消費税)', 741) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> Sugiyama
     * @since 2020/12/xx
     * @param   Msi_Sys_DB   $db
     * @params  string   $seikyu_den_no
     * @params  integer  $hako_count
     * @return  array()
     */
    public static function getSeikyuShohizeiEasyRyoshuUchiwake($db, $seikyu_den_no, $hako_count, $ikatsu_flg = false, $count = 0)
    {
        $seikyuRec = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $seikyu_den_no));
        if ($seikyuRec === null) {
            throw new Exception(sprintf('請求伝票が存在しません(udn:%s)', $seikyu_den_no));
        }
        $ryoshuRec = DataMapper_RyosyushoHistory::findOne($db, array('uri_den_no' => $seikyu_den_no, 'hako_count' => $hako_count));
        if ($ryoshuRec === null) {
            throw new Exception(sprintf('領収書データが存在しません(udn:%s,cnt:%s)', $seikyu_den_no, $hako_count));
        }
        $select = App_KeigenUtils::getSeikyuShohizei00seikyu($db, $seikyu_den_no);
        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800
        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...
        
        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd       = +$rec['zei_cd'];
            list($zei_rtu00, $zei_hasu_kbn00, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($zei_cd);
            //if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            //if ( $zei_kbn == 0 ) continue; // 非課税は skip
            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額
            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0 );
            }
            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
            $_wkData[ $zei_rtu ]['zei_cd'] = $zei_cd;
            $_wkData[ $zei_rtu ]['reduced_tax_rate'] = $reduced_tax_rate;
        }
        if($ryoshuRec['data_kbn'] == 1 || $ryoshuRec['data_kbn'] == 2){
            // 互助会（友の会）、後掛金、供物未精算 を調整
            if($ikatsu_flg){
                static::_adjEtcSeikyuShohizeiIkatu($db, $seikyu_den_no, $_wkData, $ryoshuRec['data_kbn']);
                $rtnData = static::getSeikyuShohizeiEasyRyoshuUchiwakeIkatu($_wkData,$ryoshuRec, $count);
                return $rtnData;
            }else{
                static::_adjEtcSeikyuShohizei($db, $seikyu_den_no, $_wkData, $ryoshuRec['data_kbn']);
            }
        }
        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ( $_wkData as $wkK => $wkRec ) {
            if ( $wkK == 0 ) {
                $zei_kbn = 0;
                $amt1= $ryoshuRec['genkin_prc_hikazei'] + $ryoshuRec['kogite_prc_hikazei'] + $ryoshuRec['furikomi_prc_hikazei'] + $ryoshuRec['credit_prc_hikazei'] + $ryoshuRec['j_debit_prc_hikazei'];
                $cap1 = '非課税対象額';
                $cap2 = null;
                $amt2 = null;
            } else {
                if ( $wkRec['reduced_tax_rate'] == 2 ) { // 軽減税率
                    $cap1 = sprintf("%d%%軽減対象額", $wkK);
                    $amt1= +$ryoshuRec['genkin_prc_keigen'] + $ryoshuRec['kogite_prc_keigen'] + $ryoshuRec['furikomi_prc_keigen'] + $ryoshuRec['credit_prc_keigen'] + $ryoshuRec['j_debit_prc_keigen'];
                    $cap2 = "(内消費税)";
                    $amt2 = +$ryoshuRec['zei_prc_keigen'];
                } else if($wkK == 3) {
                    $cap1 = sprintf("%d%%対象額", $wkK);
                    $amt1 = +$ryoshuRec['genkin_prc_3'] + $ryoshuRec['kogite_prc_3'] + $ryoshuRec['furikomi_prc_3'] + $ryoshuRec['credit_prc_3'] + $ryoshuRec['j_debit_prc_3'];
                    $cap2 = "(内消費税)";
                    $amt2 = +$ryoshuRec['zei_prc_3'];
                } else if($wkK == 5) {
                    $cap1 = sprintf("%d%%対象額", $wkK);
                    $amt1 = +$ryoshuRec['genkin_prc_5'] + $ryoshuRec['kogite_prc_5'] + $ryoshuRec['furikomi_prc_5'] + $ryoshuRec['credit_prc_5'] + $ryoshuRec['j_debit_prc_5'];
                    $cap2 = "(内消費税)";
                    $amt2 = +$ryoshuRec['zei_prc_5'];
                } else if($wkK == 8) {
                    $cap1 = sprintf("%d%%対象額", $wkK);
                    $amt1 = +$ryoshuRec['genkin_prc_8'] + $ryoshuRec['kogite_prc_8'] + $ryoshuRec['furikomi_prc_8'] + $ryoshuRec['credit_prc_8'] + $ryoshuRec['j_debit_prc_8'];
                    $cap2 = "(内消費税)";
                    $amt2 = +$ryoshuRec['zei_prc_8'];
                } else { // 標準税率
                    $cap1 = sprintf("%d%%対象額", $wkK);
                    $amt1= +$ryoshuRec['genkin_prc'] + $ryoshuRec['kogite_prc'] + $ryoshuRec['furikomi_prc'] + $ryoshuRec['credit_prc'] + $ryoshuRec['j_debit_prc']
                        - ( $ryoshuRec['genkin_prc_keigen'] + $ryoshuRec['kogite_prc_keigen'] + $ryoshuRec['furikomi_prc_keigen'] + $ryoshuRec['credit_prc_keigen'] + $ryoshuRec['j_debit_prc_keigen'] )
                        - ( $ryoshuRec['genkin_prc_hikazei'] + $ryoshuRec['kogite_prc_hikazei'] + $ryoshuRec['furikomi_prc_hikazei'] + $ryoshuRec['credit_prc_hikazei'] + $ryoshuRec['j_debit_prc_hikazei'] )
                        - ( $ryoshuRec['genkin_prc_3'] + $ryoshuRec['kogite_prc_3'] + $ryoshuRec['furikomi_prc_3'] + $ryoshuRec['credit_prc_3'] + $ryoshuRec['j_debit_prc_3'] )
                        - ( $ryoshuRec['genkin_prc_5'] + $ryoshuRec['kogite_prc_5'] + $ryoshuRec['furikomi_prc_5'] + $ryoshuRec['credit_prc_5'] + $ryoshuRec['j_debit_prc_5'] )
                        - ( $ryoshuRec['genkin_prc_8'] + $ryoshuRec['kogite_prc_8'] + $ryoshuRec['furikomi_prc_8'] + $ryoshuRec['credit_prc_8'] + $ryoshuRec['j_debit_prc_8'] );
                    $cap2 = "(内消費税)";
                    $amt2 = +$ryoshuRec['zei_prc_std'];
                }
            }
            $_arr = array($cap1, $amt1, $cap2, $amt2);
            $rtnData[] = $_arr;
        }
        return $rtnData;
    }
    
    public function getSeikyuShohizeiEasyRyoshuUchiwakeIkatu($_wkData, $ryoshuRec, $count){
        $rtnData = array();
        $rtnData2 = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //
        // 内訳金額
        $hikazei = $ryoshuRec['genkin_prc_hikazei'] + $ryoshuRec['kogite_prc_hikazei'] + $ryoshuRec['furikomi_prc_hikazei'] + $ryoshuRec['credit_prc_hikazei'] + $ryoshuRec['j_debit_prc_hikazei'];
        $keigen  = $ryoshuRec['genkin_prc_keigen'] + $ryoshuRec['kogite_prc_keigen'] + $ryoshuRec['furikomi_prc_keigen'] + $ryoshuRec['credit_prc_keigen'] + $ryoshuRec['j_debit_prc_keigen'];
        $prc_3   = $ryoshuRec['genkin_prc_3'] + $ryoshuRec['kogite_prc_3'] + $ryoshuRec['furikomi_prc_3'] + $ryoshuRec['credit_prc_3'] + $ryoshuRec['j_debit_prc_3'];
        $prc_5   = $ryoshuRec['genkin_prc_5'] + $ryoshuRec['kogite_prc_5'] + $ryoshuRec['furikomi_prc_5'] + $ryoshuRec['credit_prc_5'] + $ryoshuRec['j_debit_prc_5'];
        $prc_8   = $ryoshuRec['genkin_prc_8'] + $ryoshuRec['kogite_prc_8'] + $ryoshuRec['furikomi_prc_8'] + $ryoshuRec['credit_prc_8'] + $ryoshuRec['j_debit_prc_8'];
        $prc_std = $ryoshuRec['genkin_prc'] + $ryoshuRec['kogite_prc'] + $ryoshuRec['furikomi_prc'] + $ryoshuRec['credit_prc'] + $ryoshuRec['j_debit_prc']
                   - ($hikazei + $keigen + $prc_3 + $prc_5 + $prc_8);
        // 内訳金額（税）
        $zei_prc_keigen = $ryoshuRec['zei_prc_keigen'];
        $zei_prc_3      = $ryoshuRec['zei_prc_3'];
        $zei_prc_5      = $ryoshuRec['zei_prc_5'];
        $zei_prc_8      = $ryoshuRec['zei_prc_8'];
        $zei_prc_std    = $ryoshuRec['zei_prc_std'];
        $hikazei_g = 0;
        $prc_3_g   = 0;
        $prc_5_g   = 0;
        $prc_8_g   = 0;
        $prc_std_g = 0;
        $zei_3_g   = 0;
        $zei_5_g   = 0;
        $zei_8_g   = 0;
        $zei_std_g = 0;
        // 互助会の金額を取得
        if($count == 0){
            foreach ( $_wkData as $gwkK => $gwkRec ) {
                $wkK_tmp = explode("_",$gwkK);
                if(!isset($gwkRec['gojo_flg'])){
                    continue;
                }
                if($wkK_tmp[0] == 0 && isset($gwkRec['gojo_flg'])){
                    $cap1 = '非課税対象額';
                    $amt1 = $gwkRec['totalPrice'];
                    $cap2 = null;
                    $amt2 = null;
                    $hikazei_g += $gwkRec['totalPrice'];
                }else if($wkK_tmp[0] == 8 && isset($gwkRec['gojo_flg'])){
                    $cap1 = sprintf("%d%%対象額", $wkK_tmp[0]);
                    $amt1 = $gwkRec['totalPrice'];
                    $cap2 = "(内消費税)";
                    $amt2 = $gwkRec['ZeiPrc'];
                    $prc_8_g += $gwkRec['totalPrice'];
                    $zei_8_g += $gwkRec['ZeiPrc'];
                }else if($wkK_tmp[0] == 5 && isset($gwkRec['gojo_flg'])){
                    $cap1 = sprintf("%d%%対象額", $wkK_tmp[0]);
                    $amt1 = $gwkRec['totalPrice'];
                    $cap2 = "(内消費税)";
                    $amt2 = $gwkRec['ZeiPrc'];
                    $prc_5_g += $gwkRec['totalPrice'];
                    $zei_5_g += $gwkRec['ZeiPrc'];
                }else if($wkK_tmp[0] == 3 && isset($gwkRec['gojo_flg'])){
                    $cap1 = sprintf("%d%%対象額", $wkK_tmp[0]);
                    $amt1 = $gwkRec['totalPrice'];
                    $cap2 = "(内消費税)";
                    $amt2 = $gwkRec['ZeiPrc'];
                    $prc_3_g += $gwkRec['totalPrice'];
                    $zei_3_g += $gwkRec['ZeiPrc'];
                }else if(isset($gwkRec['gojo_flg'])){
                    $cap1 = sprintf("%d%%対象額", $wkK_tmp[0]);
                    $amt1 = $gwkRec['totalPrice'];
                    $cap2 = "(内消費税)";
                    $amt2 = $gwkRec['ZeiPrc'];
                    $prc_std_g += $gwkRec['totalPrice'];
                    $zei_std_g += $gwkRec['ZeiPrc'];
                }else{
                    continue;
                }
                $_arr = array($cap1, $amt1, $cap2, $amt2);
                $rtnData2[] = $_arr;
            }
        }
        // 互助会以外の金額を取得
        foreach ( $_wkData as $wkK => $wkRec ) {
            $wkK_tmp = explode("_",$wkK);
            // $_wkData_tmp[$gojo2['kake_zei_rtu']]['gojo_flg']
            if(isset($wkRec['gojo_flg'])){
                continue;
            }
            if($wkK_tmp[0] == 0){
                $cap1 = '非課税対象額';
                $amt1 = $hikazei - $hikazei_g;
                $cap2 = null;
                $amt2 = null;
            }else if($wkRec['reduced_tax_rate'] == 2 && $keigen != 0){
                $cap1 = sprintf("%d%%軽減対象額", $wkK_tmp[0]);
                $amt1 = $keigen;
                $cap2 = "(内消費税)";
                $amt2 = $zei_prc_keigen ;
            }else if($wkK_tmp[0] == 8 && $prc_8 != 0){
                $cap1 = sprintf("%d%%対象額", $wkK_tmp[0]);
                $amt1 = $prc_8 - $prc_8_g;
                $cap2 = "(内消費税)";
                $amt2 = $zei_prc_8 - $zei_8_g;
            }else if($wkK_tmp[0] == 5 && $prc_5 != 0){
                $cap1 = sprintf("%d%%対象額", $wkK_tmp[0]);
                $amt1 = $prc_5 - $prc_5_g;
                $cap2 = "(内消費税)";
                $amt2 = $zei_prc_5 - $zei_5_g;
            }else if($wkK_tmp[0] == 3 && $prc_3 != 0){
                $cap1 = sprintf("%d%%対象額", $wkK_tmp[0]);
                $amt1 = $prc_3 - $prc_3_g;
                $cap2 = "(内消費税)";
                $amt2 = $zei_prc_3 - $zei_3_g;
            }else if($wkK_tmp[0] > 8 && $prc_std != 0){
                $cap1 = sprintf("%d%%対象額", $wkK_tmp[0]);
                $amt1 = $prc_std - $prc_std_g;
                $cap2 = "(内消費税)";
                $amt2 = $zei_prc_std - $zei_std_g;
            }else{
                continue;
            }
            $_arr = array($cap1, $amt1, $cap2, $amt2);
            $rtnData[] = $_arr;
        }
        $rtnData_tmp = array($rtnData, $rtnData2);
        return $rtnData_tmp;
    }
    
    /**
     * 請求書用に請求明細伝票から消費税額を計算して返す(請求伝票番号版)
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no 
     * @return  array()
     */
    public static function getSeikyuShohizeiEasySeikyusyo($db, $seikyu_den_no)
    {
        $data00 = App_KeigenUtils::getSeikyuShohizei4($db, $seikyu_den_no);
        $rtnData = static::_shohizeiEasyFilter3($data00);
        return $rtnData;
    }
    
    /**
     * 請求書用に請求明細伝票から消費税額を計算して返す(請求伝票番号版)
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no 
     * @return  array()
     */
    public static function getSeikyuShohizeiEasySeikyusyo2($db, $seikyu_den_no)
    {
        $data00 = App_KeigenUtils::getSeikyuShohizei5($db, $seikyu_den_no);
        $rtnData = static::_shohizeiEasyFilter4($data00);
        return $rtnData;
    }
    
    /**
     * 請求書用に請求明細伝票から消費税額を計算して返す(請求伝票番号版)
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no 
     * @return  array()
     */
    public static function getTempSeikyuShohizeiEasySeikyusyo2($db, $seikyu_den_no)
    {
        $data00 = App_KeigenUtils::getTempSeikyuShohizei5($db, $seikyu_den_no);
        $rtnData = static::_shohizeiEasyFilter4($data00);
        return $rtnData;
    }
    
    /**
     * 仮請求書用に仮請求明細伝票から消費税額を計算して返す(請求伝票番号版)
     * 非課税行 0 円なら返却データから外し、消費税行の場合はインデントなどの装飾をして返す版
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no 
     * @return  array()
     */
    public static function getTempSeikyuShohizeiEasySeikyusyo($db, $seikyu_den_no)
    {
        $data00 = App_KeigenUtils::getTempSeikyuShohizei4($db, $seikyu_den_no);
        $rtnData = static::_shohizeiEasyFilter3($data00);
        return $rtnData;
    }
    
    /**
     * 請求書用 請求明細伝票の sum から消費税額を計算して返す(請求伝票番号版)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no 
     * @return  array()|null
     */
    public static function getSeikyuShohizei4($db, $seikyu_den_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(uri_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   FROM seikyu_denpyo_msi jdm
  WHERE seikyu_den_no=:seikyu_den_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'seikyu_den_no'=>$seikyu_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub3($select);

        return $rtnData;
    }
    
    /**
     * 請求書用 請求明細伝票の sum から消費税額を計算して返す(請求伝票番号版)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no 
     * @return  array()|null
     */
    public static function getSeikyuShohizei5($db, $seikyu_den_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(uri_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   FROM seikyu_denpyo_msi jdm
  WHERE seikyu_den_no=:seikyu_den_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'seikyu_den_no'=>$seikyu_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub4($select);

        return $rtnData;
    }
    
    /**
     * 請求書用 請求明細伝票の sum から消費税額を計算して返す(請求伝票番号版)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no 
     * @return  array()|null
     */
    public static function getTempSeikyuShohizei5($db, $seikyu_den_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(uri_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   FROM temp_seikyu_denpyo_msi jdm
  WHERE temp_seikyu_den_no=:temp_seikyu_den_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'temp_seikyu_den_no'=>$seikyu_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub4($select);

        return $rtnData;
    }
    
    /**
     * 請求書用 仮請求明細伝票の sum から消費税額を計算して返す(仮請求伝票番号版)
     *
     * <AUTHOR> mihara
     * @since 2019/04/30
     * @version 2019/06/20 MSI tosaka priceにadd_costを追加
     * defaultで0が入るが念のためCOALESCE処理をしておく
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no 
     * @return  array()|null
     */
    public static function getTempSeikyuShohizei4($db, $seikyu_den_no)
    {
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(uri_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   FROM temp_seikyu_denpyo_msi jdm
  WHERE temp_seikyu_den_no=:temp_seikyu_den_no
    AND delete_flg=0
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
 ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'temp_seikyu_den_no'=>$seikyu_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        $rtnData = static::_getSomeShohizeiSub3($select);

        return $rtnData;
    }
    /**
     * 領収書用 ryosyusho_history_ からその分の消費税額を返す
     * ex. array( array('10％対象額', 10000,  '(内消費税)', 910),
     *            array('8%対象額', 10000, '(内消費税)', 741) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> Sugiyama
     * @since 2020/11/xx
     * @param   Msi_Sys_DB   $db
     * @params  string   $seikyu_den_no
     * @return  array()
     */
    public static function getSeikyuZanShohizei($db, $seikyu_den_no)
    {
        $seikyuRec = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $seikyu_den_no));
        if ($seikyuRec === null) {
            throw new Exception(sprintf('請求伝票が存在しません(udn:%s)', $seikyu_den_no));
        }
        // 互助会系金額をまとめる
        $gojokai_prc = + $seikyuRec['sougi_wari_prc'] // 葬儀前納割引額(前納割引額+プレミアム割引+完納後割増サービス)
                        + $seikyuRec['sougi_premium_service_prc']   // 葬儀割増サービス(残一括割引額)
                        + $seikyuRec['sougi_early_use_cost'] + $seikyuRec['sougi_early_use_cost_zei']   // 早期利用費 
                        + $seikyuRec['sougi_meigi_chg_cost'] + $seikyuRec['sougi_meigi_chg_cost_zei']   // 名義変更手数料 
                        + $seikyuRec['etc_harai_prc'];  // 会費充当 
                        + $seikyuRec['sougi_keiyaku_zei'];  // 会費消費税 
                        + $seikyuRec['sougi_keiyaku_prc'] + $seikyuRec['sougi_harai_prc'];  // 会費残
        // 入金系金額をまとめる
        $nyukin_prc = $seikyuRec['nyukin_prc'] + $seikyuRec['uchikin_prc'] + $seikyuRec['cupon_prc'];

        $select = App_KeigenUtils::getSeikyuShohizei02Uri($db, $seikyu_den_no);

        // 内容調整
        $zeiData = array(); 
        foreach ($select as $rec) {
            $onerow = array();
            $price = +$rec['price'];
            $zei_kbn = +$rec['zei_kbn'];
            $zei_rtu = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd = +$rec['zei_cd'];
            list($zei_rtu00, $zei_hasu_kbn00, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($zei_cd);

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            $onerow['totalPrice'] = $totalPrice;
            $onerow['ZeiPrc'] = $tax;
            $onerow['zei_cd'] = $zei_cd;
            $onerow['zei_kbn'] = $zei_kbn;
            $onerow['zei_rtu'] = $zei_rtu;
            $onerow['zei_hasu_kbn'] = $zei_hasu_kbn;
            $zeiData[] = $onerow;
        }
        // 並び替えを行う
        // 金額が大きい順→税区分の大きい順→税コードの大きい順
        $rtn = usort($zeiData, function($a, $b) {
            $val = null;
            if ($a['totalPrice'] == $b['totalPrice']) {
                if ($a['zei_kbn'] == $b['zei_kbn']) {
                    if ($a['zei_cd'] > $b['zei_cd']) {
                        $val = -1;
                    } else {
                        $val = 1;
                    }
                } else if ($a['zei_kbn'] > $b['zei_kbn']) {
                    $val = -1;
                } else {
                    $val = 1;
                }
            } else if ($a['totalPrice'] > $b['totalPrice']) {
                $val = -1;
            } else {
                $val = 1;
            }
            return $val;
        });

        $rtnData = array();   
        if ($gojokai_prc < 0) {
            $juto_prc = $nyukin_prc + ($gojokai_prc * -1);
        } else {
            $juto_prc = $nyukin_prc + $gojokai_prc;
        }
        // 金額大きいものから充当していく
        foreach ($zeiData as $value) {
            $onerow = array();
            $komiPrc = $value['totalPrice'];
            if ($value['totalPrice'] < $juto_prc) { // 税込金額<充当金額の場合は税込金額分充当する
                $juto_prc -= $value['totalPrice'];
                $value['totalPrice'] = 0;
            } else if ($value['totalPrice'] == $juto_prc) { // 税込金額=充当金額の場合は税込金額分充当する
                $juto_prc -= $value['totalPrice'];
                $value['totalPrice'] = 0;
            } else {    // 税込金額>充当金額の場合は充当分差し引く
                $value['totalPrice'] -= $juto_prc;
                $juto_prc = 0;
            }
            // 充当後の税込金額から税額を求める
            if ($value['zei_kbn'] != 0) {
                $TaxPrc = App_ClsTaxLib::CalcTax($value['totalPrice'], 1, $value['zei_rtu'], $value['zei_hasu_kbn']);
                $onerow['zeiPrc'] = $TaxPrc['ZeiPrc']; 
            } else {
                $onerow['zeiPrc'] = 0; 
            }
            $onerow['zei_cd'] = $value['zei_cd'];  // 充当前の税込金額
            $onerow['zei_kbn'] = $value['zei_kbn'];  // 充当前の税込金額
            $onerow['komiPrc'] = $komiPrc;  // 充当前の税込金額
            $onerow['zanPrc'] = $value['totalPrice'];   // 充当後の税込金額
            $rtnData[] = $onerow;
        }

        return $rtnData;
    }    
    /**
     * 請求分割画面用表示形式 請求明細伝票から消費税額を計算して返す(会費充当込)
     * 税率で対象額をまとめる
     * ex. array( array( 'taisho_cap' => '非課税金額', 'zanPrc' => 10000, 'zeiPrc' => 0 ),
     *            array( 'taisho_cap' => '10％対象額', 'zanPrc' => 10000, 'zeiPrc' => 910),
     *            array( 'taisho_cap' => '8％対象額',  'zanPrc' => 10000, 'zeiPrc' => 741 ) )
     *     zanPrcには消費税額も含まれます
     * <AUTHOR> Kobayashi
     * @since 2021/03/12
     * @param   Msi_Sys_DB   $db
     * @param   string       $seikyu_den_no
     * @return  array()
     */
    public static function getSeikyuShohizeibunkatsu($db, $seikyu_den_no)
    {
        $select = static::getSeikyuZanShohizei($db, $seikyu_den_no);

        $rtnData = array();
        foreach ( $select as $wkRec ) {
            list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($wkRec['zei_cd']);
            if ( $zei_rtu == 0 ) {
                $cap = '非課税金額';
            } else {
                if (isset($reduced_tax_rate) && $reduced_tax_rate == 2) {
                    $cap = sprintf("軽減%d%%対象額", $zei_rtu);
                } else {
                    $cap = sprintf("%d%%対象額", $zei_rtu);
                }
            }
            $wkRec['zei_rtu'] = $zei_rtu;
            $wkRec['zei_hasu_kbn'] = $zei_hasu_kbn;
            $wkRec['reduced_tax_rate'] = $reduced_tax_rate;
            $wkRec['taisho_cap']  = $cap;
            $rtnData[] = $wkRec;
        }

        // 出力順に並び替え
        // zei_kbn=0(非課税が先頭), 次に外税,内税の順で、外税,内税内では税率が高い順
        usort( $rtnData, function($a, $b) {
                if ( $a['zei_kbn'] == 0 ) return -1;
                if ( $b['zei_kbn'] == 0 ) return +1;
                return $a['zei_kbn'] > $b['zei_kbn'] ? -1 : 
                    ( $a['zei_rtu'] > $b['zei_rtu'] ? -1 : +1  );
            } );

        return $rtnData;
    }
    /**
     * 領収書用(内金用) ryosyusho_history_ からその分の消費税額を返す
     * ex. array( array('10％対象額', 10000,  '(内消費税)', 910),
     *            array('8%対象額', 10000, '(内消費税)', 741) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> Sugiyama
     * @since 2021/06/xx
     * @param   Msi_Sys_DB   $db
     * @params  string   $uri_den_no
     * @params  integer  $hako_count
     * @params  string   $juchu_den_no
     * @return  array()
     */
    public static function getSeikyuShohizeiEasy4Uc($db, $uri_den_no, $hako_count, $juchu_den_no = null)
    {
        $uridenRec = null;
        if(isset($juchu_den_no)){
            $uridenRec = DataMapper_JuchuDenpyo::findOne($db, array('denpyo_no' => $juchu_den_no));
            if ($uridenRec === null) {
                throw new Exception(sprintf('受注伝票が存在しません(udn:%s)', $juchu_den_no));
            }
            $uri_den_no = $juchu_den_no;
        }else{
            $uridenRec = DataMapper_UriageDenpyo::findOne($db, array('seikyu_no' => $uri_den_no));
            if ($uridenRec === null) {
                throw new Exception(sprintf('売上伝票が存在しません(udn:%s)', $uri_den_no));
            }
        }
        $select = DataMapper_RyosyushoHistory::findUC($db, array('uri_den_no' => $uri_den_no, 'hako_count' => $hako_count));
        if (count($select) == 0) {
            throw new Exception(sprintf('領収書データが存在しません(udn:%s,cnt:%s)', $uri_den_no, $hako_count));
        }
        $ryoshuRec = $select[0];
        if(isset($juchu_den_no)){
            $select = App_KeigenUtils::getSeikyuShohizei00Juchu($db, $uri_den_no);
        }else{
            $select = App_KeigenUtils::getSeikyuShohizei00Uri($db, $uri_den_no);
        }

        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...

        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd       = +$rec['zei_cd'];
            list($zei_rtu00, $zei_hasu_kbn00, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($zei_cd);

            if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            if ( $zei_kbn == 0 ) continue; // 非課税は skip

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0 );
            }

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
            $_wkData[ $zei_rtu ]['zei_cd'] = $zei_cd;
            $_wkData[ $zei_rtu ]['reduced_tax_rate'] = $reduced_tax_rate;
        }

        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ( $_wkData as $wkK => $wkRec ) {
            if ( $wkRec['reduced_tax_rate'] == 2 ) { // 軽減税率
                $cap1 = sprintf("%d%%対象額", $wkK);
                $amt1= +$ryoshuRec['genkin_prc_keigen'] + $ryoshuRec['kogite_prc_keigen'] + $ryoshuRec['furikomi_prc_keigen'] + $ryoshuRec['credit_prc_keigen'] + $ryoshuRec['j_debit_prc_keigen'];
                $cap2 = "(内消費税)";
                $amt2 = +$ryoshuRec['zei_prc_keigen'];
            } else { // 標準税率
                $cap1 = sprintf("%d%%対象額", $wkK);
                $amt1= +$ryoshuRec['genkin_prc'] + $ryoshuRec['kogite_prc'] + $ryoshuRec['furikomi_prc'] + $ryoshuRec['credit_prc'] + $ryoshuRec['j_debit_prc']
                    - ( $ryoshuRec['genkin_prc_keigen'] + $ryoshuRec['kogite_prc_keigen'] + $ryoshuRec['furikomi_prc_keigen'] + $ryoshuRec['credit_prc_keigen'] + $ryoshuRec['j_debit_prc_keigen'] )
                    - ( $ryoshuRec['genkin_prc_hikazei'] + $ryoshuRec['kogite_prc_hikazei'] + $ryoshuRec['furikomi_prc_hikazei'] + $ryoshuRec['credit_prc_hikazei'] + $ryoshuRec['j_debit_prc_hikazei'] );
                $cap2 = "(内消費税)";
                $amt2 = +$ryoshuRec['zei_prc_std'];
            }
            $_arr = array($cap1, $amt1, $cap2, $amt2);
            $rtnData[] = $_arr;
        }
        return $rtnData;
    }
    /**
     * 
     * 領収書発行画面用 売上明細伝票から消費税額を計算して返す
     * 税率で対象額をまとめる
     * 
     * ex. array( array( 'taisho_cap' => '非課税金額', 'taisho_gaku' => 10000, 'zei_gaku' => 0 ),
     *            array( 'taisho_cap' => '10％対象額', 'taisho_gaku' => 10000, 'zei_gaku' => 910),
     *            array( 'taisho_cap' => '8％対象額',  'taisho_gaku' => 10000, 'zei_gaku' => 741 ) )
     *     対象額には消費税額も含まれます
     *
     * <AUTHOR> Sugiyama
     * @since 2022/08/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $uri_den_no 
     * @param   string       $data_kbn 
     * @param   boolean      $order 
     * @param   stirng       $dai_bunrui_cd
     * @return  array()
     */
    public static function getSeikyuShohizeiEasyRyoshuUc($db, $uri_den_no, $data_kbn = 1, $order = false, $dai_bunrui_cd = null)
    {
        $select = static::getSeikyuShohizeiRyoshuUc($db, $uri_den_no, $order, $dai_bunrui_cd);

        // $select
        // ex.
        //  zei_kbn | zei_cd | zei_rtu | zei_hasu_kbn | price
        // ---------+--------+---------+--------------+--------
        //        1 |      5 |      10 |            1 |  15000
        //        2 |      4 |       8 |            1 |  80000
        //        2 |      5 |      10 |            1 | 185000
        //        0 |      5 |      10 |            1 |  10800

        $_wkData = array(); // 税率毎の金額データ   ex. array( 10 => array('price'=>1000, 'ZeiPrc'=>100),...

        // 税率でまとめる
        foreach ( $select as $rec ) {
            $price        = +$rec['price'];
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_hasu_kbn = +$rec['zei_hasu_kbn'];
            $zei_cd       = +$rec['zei_cd'];

            //            if ( $zei_rtu == 0 ) continue; // 税率ゼロは skip
            //            if ( $zei_kbn == 0 ) continue; // 非課税は skip

            $TaxPrc = App_ClsTaxLib::CalcTax($price, $zei_kbn, $zei_rtu, $zei_hasu_kbn);
            // $TaxPrc['ZeiPrc'];    // 消費税額
            // $TaxPrc['NukiPrc'];   // 税抜金額
            // $TaxPrc['KomiPrc'];   // 税込金額

            if ( !array_key_exists($zei_rtu, $_wkData) ) {
                $_wkData[ $zei_rtu ] = array( 'totalPrice' => 0, 'ZeiPrc' => 0, 'zei_rtu'=>$zei_rtu, 'zei_kbn'=>$zei_kbn,
                                              'zei_cd' => $zei_cd );
            }

            $tax = +$TaxPrc['ZeiPrc'];
            if ( $zei_kbn == 2 ) { // 2:外税
                $totalPrice = $price + $tax;
            } else {
                $totalPrice = $price;
            }
            
            $_wkData[ $zei_rtu ]['totalPrice'] += $totalPrice;
            $_wkData[ $zei_rtu ]['ZeiPrc'] += $tax;
            $_wkData[ $zei_rtu ]['reduced_tax_rate'] = $rec['reduced_tax_rate'];
        }
        
        if($data_kbn == 1 || $data_kbn == 2){
            // 互助会（友の会）、後掛金、供物未精算 を調整   2019/10/xx mihara
            static::_adjEtcSeikyuShohizeiUc($db, $uri_den_no, $_wkData);
        }

        $rtnData = array();
        // ex. array( array('10％対象額', 10000,  '(内消費税)', 1000),
        //            array('8%対象額', 10000, '(内消費税)', 800) )
        //            
        foreach ( $_wkData as $wkK => $wkRec ) {
            if ( $wkK == 0 ) {
                $cap1 = '非課税金額';
                $zei_kbn = 0;
            } else {
                if (isset($wkRec['reduced_tax_rate']) && $wkRec['reduced_tax_rate'] == 2) {
                    $cap1 = sprintf("＊%d%%対象額", $wkK);
                } else {
                    $cap1 = sprintf("%d%%対象額", $wkK);
                }
                $zei_kbn = +$wkRec['zei_kbn'];
            }
            $amt1 = +$wkRec['totalPrice'];
            $cap2 = "(内消費税)";
            $amt2 = +$wkRec['ZeiPrc'];
            $_arr = array( 'taisho_cap'  => $cap1, 
                           'taisho_gaku' => $amt1,
                           'zei_gaku'    => $amt2 );
            $_arr['zei_kbn'] = $zei_kbn;
            $_arr['zei_rtu'] = +$wkRec['zei_rtu'];
            $zei_cd = +$wkRec['zei_cd'];
            $_arr['zei_cd']  = $zei_cd;
            $_arr['reduced_tax_rate']  = DataMapper_ZeiMstEasy::getReducedTaxRate($zei_cd);
            $rtnData[] = $_arr;
        }

        // Msi_Sys_Utils::debug( 'rtnData PRE=>' . Msi_Sys_Utils::dump($rtnData) );
        // 出力順に並び替え
        // zei_kbn=0(非課税が先頭), 次に外税,内税の順で、外税,内税内では税率が高い順
        usort( $rtnData, function($a, $b) {
                if ( $a['zei_kbn'] == 0 ) return -1;
                if ( $b['zei_kbn'] == 0 ) return +1;
                return $a['zei_kbn'] > $b['zei_kbn'] ? -1 : 
                    ( $a['zei_rtu'] > $b['zei_rtu'] ? -1 : +1  );
            } );
        // Msi_Sys_Utils::debug( 'rtnData post=>' . Msi_Sys_Utils::dump($rtnData) );

        return $rtnData;
    }
    /**
     * 領収証要 売上明細伝票の sum から消費税額を計算して返す
     *
     * <AUTHOR> Sugiyama
     * @since 2022/08/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $ur_den_no
     * @return  array()|null
     */
    protected static function getSeikyuShohizeiRyoshuUc($db, $ur_den_no, $order = false, $dai_bunrui_cd = null) {
        $order_sql = "ORDER BY (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順";
        if($order){
            $order_sql = "ORDER BY price DESC, (CASE zei_kbn WHEN 2 THEN -1 ELSE zei_kbn END) ASC, zei_rtu DESC -- 非課税(0),外税(2),内税(1)の順";
        }
        $dai_bunrui_sql = "";
        if(isset($dai_bunrui_cd)){
            $dai_bunrui_sql = "AND dai_bunrui_cd IN ('$dai_bunrui_cd')";
        }
        $sql = <<< END_OF_SQL
SELECT
    zei_kbn
   ,T0.zei_cd
   ,zei_rtu
   ,zei_hasu_kbn
   ,reduced_tax_rate
   ,price
   ,price_i
   ,price_n
  FROM
(SELECT
    zei_kbn
   ,zei_cd
   ,SUM(uri_prc + COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price
   ,SUM(uri_prc + hoshi_prc + COALESCE(add_cost, 0)) AS price_i
   ,SUM(COALESCE(gojokai_nebiki_prc, 0) + nebiki_prc) AS price_n
   FROM uriage_denpyo_msi jdm
  WHERE uri_den_no=:uri_den_no
    AND delete_flg=0
    $dai_bunrui_sql
  GROUP BY zei_kbn, zei_cd) T0
  INNER JOIN (SELECT
                 zei_cd
                ,kaisya_cd
                ,COALESCE(zei_rtu, 0) AS zei_rtu
                ,COALESCE(zei_hasu_kbn, 0) AS zei_hasu_kbn
                ,COALESCE(reduced_tax_rate, 1) AS reduced_tax_rate
                ,delete_flg
                FROM zei_mst) T1
    ON T1.zei_cd=T0.zei_cd
   AND T1.kaisya_cd=:kaisya_cd
   AND T1.delete_flg=0
   $order_sql
END_OF_SQL;

        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();

        $select = $db->easySelect( $sql, array( 'uri_den_no'=>$ur_den_no,
                                                'kaisya_cd' => $kaisya_cd ) );

        // patch: 非課税で 税率設定(>0)されている場合は強制的に zei_rtu,zei_cd をゼロにする        
        $rtnSel = array();
        foreach ( $select as $rec ) {
            $zei_kbn      = +$rec['zei_kbn'];
            $zei_rtu      = +$rec['zei_rtu'];
            $zei_cd       = +$rec['zei_cd'];
            if ( $zei_kbn == 0 && $zei_rtu != 0 ) { 
                $rec['zei_rtu'] = 0;
                $rec['zei_cd'] = 0;
            }
            $rtnSel[] = $rec;
        }

        return $rtnSel;
    }
    /**
     * 互助会（友の会）、後掛金、供物未精算 を調整
     *
     * <AUTHOR> Sugiyama
     * @since   2022/08/xx
     * @param   Msi_Sys_DB   $db
     * @param   string       $uri_den_no
     * @param   array        $_wkData
     * @return  void
     */
    public static function _adjEtcSeikyuShohizeiUc($db, $uri_den_no, &$_wkData)
    {
        $seko_no = $db->getOneVal( 'SELECT seko_no FROM uriage_denpyo WHERE uri_den_no=? and delete_flg=0',
                                   array($uri_den_no) );
        if ( $seko_no === null ) {
            return;
        }
        $bun_gas_kbn_num = $db->getOneVal( 'SELECT bun_gas_kbn_num FROM uriage_denpyo WHERE uri_den_no=? and delete_flg=0',
                                   array($uri_den_no) );
        if($bun_gas_kbn_num == 2){  // 請求分割
            return;
        }
        // 最大税率
        $max_zei_rtu = max(array_keys($_wkData));

        // 葬儀日取得
        $sougi_ymd = static::getSougiYmd($seko_no);

        // 葬儀日の消費税
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $sougi_ymd);
        $sougi_zei_rtu      = +$taxInfo['zei_rtu'];
        $sougi_zei_hasu_kbn = +$taxInfo['zei_hasu_kbn'];

        //
        // 互助会関連 調整
        //
        $tomo_cose  = 0; // 友の会利用コース
        $atokake    = 0; // 後掛金
        $zei_sagaku = 0; // 旧税率適用差額
        $uchikin_prc = 0; // 内金
        // 内金取得
        $sql = <<< END_OF_SQL
        SELECT COALESCE(SUM(uchikin_prc),0) AS uchikin_prc
        FROM uriage_denpyo WHERE uri_den_no = :uri_den_no AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelect( $sql, array( 'uri_den_no'=>$uri_den_no));
        if(Msi_Sys_Utils::myCount($select) > 0){
            $uchikin_prc = $select[0]['uchikin_prc'];
        }
        
        $denpyo = DataMapper_JuchuDenpyo::findDenpyo2($db, array('seko_no' => $seko_no)); // 受注伝票取得
        if(count($denpyo) > 0){
            $tomo_cose  = +$denpyo[0]['sougi_keiyaku_prc'] * -1;   // 友の会利用コース
            $atokake    = +$denpyo[0]['sougi_keiyaku_prc'] + $denpyo[0]['sougi_harai_prc']; // 後掛金
            $zei_sagaku = +$denpyo[0]['sougi_zei_sagaku_prc']; // 旧税率適用差額
        }
        $tomo_cose_flg = false;
        if($tomo_cose != 0){
            $tomo_cose_flg = true;
        }
        if(!$tomo_cose_flg){
            if($tomo_cose == 0){
                $kaiyakuData = DataMapper_SekoGojokaiMember::find($db, array(
                                        'seko_no' => $seko_no,          // 施行番号
                                        '__raw_1' => 'yoto_kbn IN (5)'  // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                ));
                $harai_gaku = 0;
                foreach ($kaiyakuData as $kaiyaku) {
                    $harai_gaku += $kaiyaku['harai_gaku'];
                }
                if($harai_gaku > 0){
                    $tomo_cose_flg = true;
                }else{
                    // 金額充当
                    $tmpGojoData = DataMapper_SekoGojokaiMember::find($db, array(
                                                                              'seko_no' => $seko_no,  // 施行番号
                                                                              '__raw_1' => 'yoto_kbn IN (3)'         // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                                                              ));
                    if(Msi_Sys_Utils::myCount($tmpGojoData) > 0){
                        $tomo_cose_flg = true;
                    }
                }
            }
        }
        if ( $tomo_cose_flg ) { // 友の会利用 あり
            $total_gojo_keiyaku  = 0; // 互助会契約額
            $total_gojo_harai    = 0; // 互助会払込金額計
            $total_gojo_zei_past = 0; // 互助会加入時ベースの税額
            $total_gojo_zei_now  = 0; // 現在時点の税額
            $adj_totalPrice      = 0; // totalPrice の調整用
            $adj_ZeiPrc          = 0; // ZeiPrc の調整用
            $wari_gaku           = 0; // 割引金額
            $early_use_cost      = 0; // 早期利用費
            $early_use_cost_zei  = 0; // 早期利用費の消費税
            $meigi_chg_cost      = 0; // 名変手数料
            $meigi_chg_cost_zei  = 0; // 名変手数料の消費税
            $_atokake            = 0;
            $total_zangaku = 0;
            $total_juto    = 0;
            // 契約時点の税で調整するため SekoGojokaiMember から再度データ取得している
            $gojoData = DataMapper_SekoGojokaiMember::find($db, array(
                                                                      'seko_no' => $seko_no,  // 施行番号
                                                                      '__raw_1' => 'yoto_kbn IN (1, 2, 3, 5)'         // 1:コース施行 2:プラン施行 3:金額充当 5:解約指図払い
                                                                      ));
            foreach ($gojoData as $gojo) {
                if($gojo['yoto_kbn'] == 1) {
                    $total_zangaku += $gojo['keiyaku_gaku'] 
                                    - $gojo['harai_gaku'] 
                                    - $gojo['wari_gaku'] 
                                    - $gojo['premium_gaku'] 
                                    - $gojo['kannnou_gaku'] 
                                    - $gojo['waribiki_gaku'] 
                                    + $gojo['kanyu_tax']            // 会費消費税
                                    + $gojo['early_use_cost']       // 早期利用費
                                    + $gojo['early_use_cost_zei']   // 早期利用費消費税
                                    + $gojo['meigi_chg_cost']       // 名義変更手数料
                                    + $gojo['meigi_chg_cost_zei'];  // 名義変更手数料消費税
                }else if($gojo['yoto_kbn'] == 3){
                    $total_juto += ($gojo['harai_gaku'] * -1) + $gojo['meigi_chg_cost'] + $gojo['meigi_chg_cost_zei'];
                }
            }
            // 最大税率で調整
            $_wkData[$max_zei_rtu]['totalPrice'] += $total_zangaku + $total_juto;
        }
    }
}
