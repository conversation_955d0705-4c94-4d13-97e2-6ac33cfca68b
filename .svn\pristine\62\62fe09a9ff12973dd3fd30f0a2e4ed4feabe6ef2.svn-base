/**
 * @fileoverview ライフランド 滞留債権管理
 * @version 2025/05/28 mihara saiken.seikyulist.lifeland.js からコピーして作成. seikyulist => tairyulist
 */
var appsk = appsk || {};
$(function () {

    "use strict";

    var utils = window.msiBbUtils;
    var MIKAKUTEI = '0';
    var KAKUTEI = '1';
    var YUSO_KBN_YUSO = '0';
    var DATA_KBN_SOGI = '1';
    var DATA_KBN_HOUJI = '2';
    var DATA_KBN_TAN = '3';
    var DATA_KBN_BECHU = '4';
    var BUNGAS_NORMAL = '0';
    var BUNGAS_BUN = '2';
    var BUNGAS_GAS = '20';
    var NYUKIN_STATUS_ZAN = '0';
    var ROLE_RYOMUKANRI = 'gyomukanri';
    var ROLE_RYOMUKANRI2 = 'gyomukanri2';
    var ROLE_JOHOSYSTEM = 'johosystem';
    var ROLE_EDIGYOMUKANRI = 'edigyomukanri';
    var ROLE_NYUKINKANRI = 'nyukinkanri';
    var ROLE_SYSMAN = 'sysman';
    var ROLE_MSI = 'msi';
    var IMPORT_KBN = '95';
    
    // select2のvalを設定する
    var _setSelect2Val = function ($el, val) {
        $el.select2("val", val);
    };
    var changeToNum = function (val) {
        var num = parseInt(val, 10);
        if (isFinite(num)) {
            return num;
        } else {
            num = 0;
            return num;
        }
    };

    var AppModel = Backbone.Model.extend({

        defaults: function () {
            return {
                s_oya_bumon: null,
                s_bumon: null,
                s_seko_tanto: null,
                s_seko_tanto_cd: null,
                s_seko_no: null,
                s_shonin: null,
                s_moushi: null,
                s_pay_method_cd: null,
                s_nyukin_st: null,
                s_sekyu_tel: null,
                s_sekyu_nm: null,
                s_souke_nm: null,
                s_k_nm: null,
                s_yuso_kbn: null,
                s_gassan_st: null,
                s_sekyu_st: null,
                s_seikyu_den_no: null,
                s_denpyo_no: null,
                s_kaishu_ymd_from: null,
                s_kaishu_ymd_to: null,
                s_keijo_ymd_from: null,
                s_keijo_ymd_to: null,
                role_nm: null,
                maturity_day: null,
                s_kokyaku_no: null,
                _chkAll: false,
            };
        },

        validation: {
        },

        labels: {
        },

    }); // AppModel

    var vOpt = {

        el: '#my-form-id',

        events: {
            "click #btn_search": "doSearch",
            "click #btn_clear": "doClear",
            "click #btn_kakutei": "doKakutei",
            "click #btn_cancel": "doCancel",
            "click #btn_save": "doSave",
            "click #btn_print": "doPrint",
            "click #btn_msi_print": "doMsiPrint",
            "click #btn_gas": "goGassan",
            "click #btn_bun": "goBunkatsu",
            "click #btn_baddebt": "doBaddebt",
            "click #btn_tairyu": "goTairyu",            
            "click #s_seko_tanto,.s_seko_tanto-ref": "sekoTantoHelper",
            "click .s_seko_no-ref": "sekoNoHelper",
            "click .s_seikyu_den_no-ref": "seikyuDenpyoHelper",
            "click .s_denpyo_no-ref": "juchuDenpyoHelper",
            "change #s_oya_bumon": "oyaBumonChange",
            "select2-opening #s_bumon": function () {
                var orya_bumon_cd = this.model.get("s_oya_bumon");
                var fileredBumons = [];
                _.each(mydata.dataKbns.bumon, function (item) {
                    if (item.oya_cd === orya_bumon_cd) {
                        fileredBumons.push(item);
                    }
                });
                appsk.ko_bumons = fileredBumons;
            },
            "click .chkAllToggle": "toggleChkAll",  // 全選択ボタン
            "click .dlg_s_kokyaku_no": "kokyakuHelper",
        },

        initialize: function () {

            Backbone.Validation.bind(this);
            this.render();
        },

        render: function () {
            // console.log( 'AppView render called.' );

            Backbone.Validation.bind(this, Backbone.Validation.msi_err_setting_std());
            this.listenTo(appsk.resultCol, 'reset', this.addAllResultCol);

            this.stickit();

            // スクロール調整
            this.scrollAdj();
            // 会社(親部門)
            $.msiJqlib.setSelect2Com1(this.$("#s_oya_bumon"), ($.extend({data: mydata.dataKbns.oya_bumon}, $.msiJqlib.setSelect2Default1)));
            // 部門
            appsk.ko_bumons = mydata.dataKbns.bumon;
            $.msiJqlib.setSelect2Com1(this.$("#s_bumon"), ($.extend({data: function () {
                    return {results: appsk.ko_bumons};
                }}, $.msiJqlib.setSelect2Default1)));
            // 承認状況
            $.msiJqlib.setSelect2Com1(this.$("#s_shonin"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.shonin_st)}, $.msiJqlib.setSelect2Default1)));
            // 申込区分
            $.msiJqlib.setSelect2Com1(this.$("#s_moushi"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.moushi_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 支払方法
            $.msiJqlib.setSelect2Com1(this.$("#s_pay_method_cd"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.pay_method_all)}, $.msiJqlib.setSelect2Default1)));
            // 入金状況
            $.msiJqlib.setSelect2Com1(this.$("#s_nyukin_st"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.nyukin_st)}, $.msiJqlib.setSelect2Default1)));
            // 請求書郵送区分
            $.msiJqlib.setSelect2Com1(this.$("#s_yuso"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.yuso_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 合算分割状況
            $.msiJqlib.setSelect2Com1(this.$("#s_bun_gas"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.bun_gas)}, $.msiJqlib.setSelect2Default1)));
            // 請求状況
            $.msiJqlib.setSelect2Com1(this.$("#s_sekyu_st"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.sekyu_st)}, $.msiJqlib.setSelect2Default1)));

            return this;
        },
        // 顧客 ピッカー
        kokyakuHelper: function () {
            // if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない

            var bbv = this;

            var par = {
                // is_click_mode: true, // クリックで選択
                // init_search: 1,
                s_kokyaku_kbn: 0, // 0:個人
                is_easyreg: 1, // 登録用ボタン表示
            };

            msiGlobalObj.kaikokyakudlgOpen(this,
                    par,
                    function (data) { // select
                        // console.log( '@@@ selectKaiKokyaku =>', data );
                        if (!data) {
                            return;
                        }
                        bbv.model.set({s_kokyaku_no: data.kokyaku_no});
                    },
                    function (data) { // clear
                        // console.log( '@@@ clearKaiKokyaku =>', data );
                        bbv.model.set({s_kokyaku_no: null});
                    });
        },
        // check トグル
        toggleChkAll: function() {
            var _chkAll = !this.model.get('_chkAll');
            this.model.set('_chkAll', _chkAll);
            if ( _chkAll ) {
                this.$('.chkAllToggle').addClass('selected');
                this.checkAll();
            } else {
                this.$('.chkAllToggle').removeClass('selected');
                this.uncheckAll();
            }
        },
        // 行全選択
        checkAll: function() {
            this._checkAll(true);
        },
        // 行全選択解除
        uncheckAll: function() {
            this._checkAll(false);
        },
        // 行全設定
        _checkAll: function(isSet) {
            if(isSet){
                _.each(appsk.resultCol.models, function(v, k){
                    var model = v;
                    model.set('selected', '1');
                });
            }else{
                _.each(appsk.resultCol.models, function(v, k){
                    var model = v;
                    model.set('selected', '0');
                });
            }
            this.model.trigger('change');
        },
        oyaBumonChange: function () {
            this.model.set("s_bumon", null);
        },
        // 請求伝票検索ダイアログ	
        seikyuDenpyoHelper: function () {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: '/saiken/seikyudenpyodlg', 
                _myId: '#msi-dialog2',
                data: {
                    'bun_gas_kbns':"'0','2','20'"
                },
                onSelect: function(data) {
//                    console.log(data);
                    bbm.set({
                        s_seikyu_den_no: data.code //請求書№
                    });
                },
                onClear: function() {
                },
                hookSetData: function() {
                    return {
                        init_search: 0,
                    }
                },
            });
        },
        // 受注伝票検索ダイアログ	
        juchuDenpyoHelper: function () {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'juchu.denpyo',
                onSelect: function (data) {
                    bbm.set('s_denpyo_no', data.code);
                },
                onClear: function () {
                    bbm.set('s_denpyo_no', null);
                },
                hookSetData: function () {
                    return {init_search: 0, // XXX
                        no_cond: 0,
                        s_data_kbn: '3', // 単品
                        bun_gas_kbns: "'0','10'",   // 通常・合算元
                    };
                }
            });
        },
        doSave: function(mode) {
            // 制御したいボタンのajax活性処理を無効化
            $('#btn_save, #btn_kakutei, #btn_cancel, #btn_msi_print, #btn_print, #btn_baddebt, #btn_gas, #btn_bun').removeAttr('data-msi-temp-disabled');
            var t = this;
            $.msiJqlib.clearAlert();
            // 選択されている施行のみ処理を行う
            var data = [];
            var seko_nos = [];
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    seko_nos[m.get('seko_no')] = data;
                    data.push(m);
                }
            });
            if (!this.isInputSaveOk(data)) {
                return;
            }
            this.exeSave(data, mode);
        },
        doPrint: function () {
            if (this.doSave("2") === false) {
                return false;
            }
        },
        exePrint: function () {
            // 選択されている施行のみ処理を行う
            var data = [];
            var seikyu_nos = [];
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    seikyu_nos.push(m.get('seikyu_den_no'));
                }
            });
            if (!this.isInputSaveOk()) {
                return;
            }
            // ajax 版
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/saiken/pdf1101/index',
                data: {
                    seikyu_no_arr: seikyu_nos,
                    print_sbt: 3
                }
            });
            return;
        },
        doMsiPrint: function () {
//            if (this.doSave("3") === false) {
//                return false;
//            }
            this.exeMsiPrint();
        },
        exeMsiPrint: function () {
            // 選択されている施行のみ処理を行う
            var data = [];
            var seikyu_nos = [];
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    seikyu_nos.push(m.get('seikyu_den_no'));
                }
            });
            // ajax 版
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/saiken/pdf1101/index',
                data: {
                    seikyu_no_arr: seikyu_nos,
                    ptn: 'shoninmsi'
                }
            });
            return;
        },
        goGassan: function () {
            var seikyu_den_no = null;
            var bun_gas_kbn_num = null;
            var url = null;
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    bun_gas_kbn_num = m.get('bun_gas_kbn_num');
                    seikyu_den_no = m.get('seikyu_den_no');
                }
            });
            switch (bun_gas_kbn_num) {
                case BUNGAS_NORMAL:
                    url = '/saiken/seikyugassan/index/gmsn/' + seikyu_den_no;
                    break;
                case BUNGAS_GAS:
                    url = '/saiken/seikyugassan/index/gssn/' + seikyu_den_no;
                    break;
            }
            this._showNew( url );
            
        },
        goBunkatsu: function () {
            var seko_no = null;
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    seko_no = m.get('seko_no');
                }
            });
            var url = '/saiken/seikyubunkatsu/index/bksn/' + seko_no;
            this._showNew( url );
            
        },
        goGassan: function () {
            var seikyu_den_no = null;
            var bun_gas_kbn_num = null;
            var url = null;
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    bun_gas_kbn_num = m.get('bun_gas_kbn_num');
                    seikyu_den_no = m.get('seikyu_den_no');
                }
            });
            switch (bun_gas_kbn_num) {
                case BUNGAS_NORMAL:
                    url = '/saiken/seikyugassan/index/gmsn/' + seikyu_den_no;
                    break;
                case BUNGAS_GAS:
                    url = '/saiken/seikyugassan/index/gssn/' + seikyu_den_no;
                    break;
            }
            this._showNew( url );
            
        },
        // 滞留債権管理画面 表示  2025/05 mihara
        goTairyu: function () {
            console.log('XXX');
            var seikyu_den_no = null;
            var cnt = 0;
            var url = null;
            var that = this;
            _.each(appsk.resultCol.models, function (m) {
                if ( cnt > 5 ) return;
                if (m.get('selected') === '1') {
                    seikyu_den_no = m.get('seikyu_den_no');
                    url = '/saiken/seikyugassan/index/gmsn/' + seikyu_den_no;
                    that._showNew( url );
                    cnt++;
                }
            });
        },
        // 他画面参照
        _showNew: function(path) {
            var url = $.msiJqlib.baseUrl() + path;
            // location.href = url;
            // window.open( url, '_blank' );
            var that = this;
            var refreshFunc = function() { console.log('refresh searching...');
                                           that.doSearch();
                                         };
            msiLib2.openWinSub( refreshFunc, url );
        },
        // 入力チェック
        isInputSaveOk: function (data) {
            this.clearErr();

            var aMsg = [], line;
            // コレクションチェック
            appsk.resultCol.each(function (m, i) {
                if (m.get('selected') === '1') {
                    var resLine = m.validate();
                    if (resLine) {
                        _.each(resLine, function (v, k) {
                            if ($.inArray(v, aMsg) < 0) {
                                aMsg.push(v);
                            }
                        });
                    } 
                }
            });

            // NG
            if (aMsg.length > 0) {
                // no alert component        msiLib2.showErr( aMsg.join(', ') );
                msiLib2.showErr( aMsg.join(', ') );
                return false;
            }

            // OK
            msiLib2.clearAlert();
            return true;
        },
        exeSave: function(data, mode) {
            var t = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/tairyulist/save',
                data: {
                    dataAppJson: JSON.stringify(app.model.toJSON()),
                    dataSeikyuJson: JSON.stringify(data),
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
                        if (mode == "1") {  // 承認処理
                            t.exeKakutei();
                        } else if (mode == "2") {   // 請求書再発行
                            t.exePrint();
                        } else {
                            // 検索処理を行う
                            t.doSearch();
                        }
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doBaddebt: function() {
            var t = this;
            $.msiJqlib.clearAlert();
            // 選択されている施行のみ処理を行う
            var data = [];
            var seko_nos = [];
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    seko_nos[m.get('seko_no')] = data;
                    data.push(m);
                }
            });
            if (!this.isInputSaveOk(data)) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/tairyulist/baddebt',
                data: {
                    dataAppJson: JSON.stringify(app.model.toJSON()),
                    dataSeikyuJson: JSON.stringify(data),
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
                        // 検索処理を行う
                        t.doSearch();
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doSearch: function () {
            $("#btn_tairyu").prop("disabled", true);  // mihara add 2025/05
            $("#btn_kakutei").prop("disabled", true);
            $("#btn_cancel").prop("disabled", true);
            $("#btn_save").prop("disabled", true);
            $("#btn_msi_print").prop("disabled", true);
            $("#btn_print").prop("disabled", true);
            $("#btn_baddebt").prop("disabled", true);
            var bbm = this.model;
            // 制御したいボタンのajax活性処理を無効化
            $('#btn_tairyu, #btn_save, #btn_kakutei, #btn_cancel, #btn_msi_print, #btn_print, #btn_baddebt, #btn_gas, #btn_bun').removeAttr('data-msi-temp-disabled'); // mihara 2025/05 add btn_tairyu
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/tairyulist/search',
                data: {
                    dataAppJson: JSON.stringify(app.model.toJSON()),
                    s_oya_bumon: bbm.get('s_oya_bumon'),
                    s_bumon: bbm.get('s_bumon'),
                    s_seko_tanto_cd: bbm.get('s_seko_tanto_cd'),
                    s_seko_no: bbm.get('s_seko_no'),
                    s_shonin: bbm.get('s_shonin'),
                    s_moushi: bbm.get('s_moushi'),
                    s_pay_method_cd: bbm.get('s_pay_method_cd'),
                    s_nyukin_st: bbm.get('s_nyukin_st'),
                    s_sekyu_tel: bbm.get('s_sekyu_tel'),
                    s_sekyu_nm: bbm.get('s_sekyu_nm'),
                    s_souke_nm: bbm.get('s_souke_nm'),
                    s_k_nm: bbm.get('s_k_nm'),
                    s_yuso_kbn: bbm.get('s_yuso_kbn'),
                    s_gassan_st: bbm.get('s_gassan_st'),
                    s_kaishu_ymd_from: bbm.get('s_kaishu_ymd_from'),
                    s_kaishu_ymd_to: bbm.get('s_kaishu_ymd_to'),
                    s_keijo_ymd_from: bbm.get('s_keijo_ymd_from'),
                    s_keijo_ymd_to: bbm.get('s_keijo_ymd_to'),
                    s_seikyu_den_no: bbm.get('s_seikyu_den_no'),
                    s_denpyo_no: bbm.get('s_denpyo_no'),
                    s_sekyu_st: bbm.get('s_sekyu_st'),
                    s_kokyaku_no: bbm.get('s_kokyaku_no'),
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        if ( mydata.dataCol.length <= 0 ) { // mihara add 2025/05
                            $('#order .result-list .result_div').empty();
                            $.msiJqlib.showWarn('該当するデータはありません');
                        } else {
                            _resetData(mydata.dataApp, mydata.dataCol);
                        }
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
                // error処理は共通設定を使う
            });
        },
        // クリア
        doClear: function () {
            _resetData(defDataApp);
            // 明細行をクリア
            $('#order .result-list .result_div').empty();
            // ボタンを非活性
            $("#btn_tairyu").prop("disabled", true);  // mihara add 2025/05            
            $("#btn_kakutei").prop("disabled", true);
            $("#btn_cancel").prop("disabled", true);
            $("#btn_save").prop("disabled", true);
            $("#btn_msi_print").prop("disabled", true);
            $("#btn_print").prop("disabled", true);
            $("#btn_baddebt").prop("disabled", true);
            $("#btn_gas").prop("disabled", true);
            $("#btn_bun").prop("disabled", true);
            app.render();
        },
        // 施行担当 pickup
        sekoTantoHelper: function () {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    // console.log( JSON.stringify(data) );
                    bbm.set('s_seko_tanto_cd', data.code);
                    bbm.set('s_seko_tanto', data.name);
                },
                onClear: function () {
                    bbm.set('s_seko_tanto_cd', null);
                    bbm.set('s_seko_tanto', null);
                },
                hookSetData: function () {
                    // return {};
                    return {s_bumon: ''}; // 部門を指定しない場合
                },
            });
        },
        // 施行No pickup
        sekoNoHelper: function () {
            var bbm = this.model;
            msiLib2.celemonyDialog(
                    function (data) {
                        bbm.set('s_seko_no', data.seko_no);
                    });
        },
        doKakutei: function (ev) {
            // 選択されている施行のみ処理を行う
            var data = [];
            var seko_nos = [];
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    seko_nos[m.get('seko_no')] = data;
                    data.push(m);
                }
            });
            if (Object.keys(seko_nos).length > 1) {
                $.msiJqlib.showErr("複数の施行が選択されているため請求書承認ができません。");
                return;
            }
            if (!confirm('請求書承認をします。よろしいですか？')) {
                return;
            }
            if (this.doSave("1") === false) {
                return false;
            }
        },
        exeKakutei: function() {
            var t = this;
            $.msiJqlib.clearAlert();
            // 選択されている施行のみ処理を行う
            var data = [];
            var seko_nos = [];
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    seko_nos[m.get('seko_no')] = data;
                    data.push(m);
                }
            });
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/tairyulist/kakutei',
                data: {
                    dataAppJson: JSON.stringify(app.model.toJSON()),
                    dataSeikyuJson: JSON.stringify(data),
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
                        // 検索処理を行う
                        t.doSearch();
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doCancel: function() {
            var t = this;
            $.msiJqlib.clearAlert();
            // 選択されている施行のみ処理を行う
            var data = [];
            var seko_nos = [];
            var zaimu_rendo_flg = false;
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    seko_nos[m.get('seko_no')] = data;
                    data.push(m);
                    if (!$.msiJqlib.isNullEx2(m.get('zaimu_rendo_denno'))) {
                        zaimu_rendo_flg = true;
                    }
                }
            });
            if (zaimu_rendo_flg) {
                if (!confirm('売上仕訳連携済みです。請求書承認を取消をします。よろしいですか？')) {
                    return;
                }
            } else {
                if (!confirm('請求書承認を取消します。よろしいですか？')) {
                    return;
                }
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/tairyulist/cancel',
                data: {
                    dataAppJson: JSON.stringify(app.model.toJSON()),
                    dataSeikyuJson: JSON.stringify(data),
                },
                type: 'POST',
                success: function(mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
                        // 検索処理を行う
                        t.doSearch();
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        addResultOne: function (msi) {
            var v = new ResultView({model: msi});
            this.$(".list_table").append(v.render().el);
        },
        addAllResultCol: function (collection) {
            var $msi = this.$(".list_table");
            $msi.find('.result_div').remove();
            collection.each(this.addResultOne, this);
        },

        // // スクロールバー表示調整
        // OLD_scrollAdj: function () { // old mihara 2025/05
        //     var $list = this.$('.result-list .list'),
        //             $header = this.$('.result-list .header'),
        //             sc_of,
        //             hh;
        //     // console.log( 'height=>' + [cont_h,src_h,adj_h].join(', ') + ' my_h=>' + my_h );

        //     $list.height('80%');
        // },

        scrollAdj: function () { // new mihara 2025/05
            
            var vh = $('#order').height(),
                $list = this.$('.result-list .list'),
                $header = this.$('.result-list .header'),
                src_h = this.$('.search').outerHeight(true),
                header_h = $header.outerHeight(true),
                adj_h = 160,
                result_part_h = vh - (src_h + adj_h),
                list_h = result_part_h - header_h;
            // console.log( 'height=>', [vh, src_h, header_h, adj_h] );

            this.$('.result-list').height( result_part_h );
            $list.height( list_h );
        },
        
        // エラー表示をクリア
        clearErr: function () {
            this.$el.msiErrClearAll();
        },

        // 入力チェック
        isInputOk: function () {
            this.clearErr();

            var aMsg = [], line;
            // コレクションチェック
            appsk.resultCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                    });
                }
            });

            // NG
            if (aMsg.length > 0) {
                // no alert component        msiLib2.showErr( aMsg.join(', ') );
                return false;
            }

            // OK
            msiLib2.clearAlert();
            return true;
        },

        // 変更可否
        isChanged: function () {
            return false;
        },
        // dbl click 起動画面
        doShowSeikyu: function(seko_no) {
            var url = '/saiken/seikyusyo/shonin/sn/' + seko_no;
            this._showNew( url );
        },
        // 他画面参照
        _showNew: function(path) {
            var url = $.msiJqlib.baseUrl() + path;
            // location.href = url;
            // window.open( url, '_blank' );
            var that = this;
            var refreshFunc = function() { console.log('refresh searching...');
                                           that.doSearch();
                                         };
            msiLib2.openWinSub( refreshFunc, url );
        },
        bindings: {
            '#s_oya_bumon': {
                observe: 's_oya_bumon',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#s_bumon': {
                observe: 's_bumon',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#s_seko_tanto': 's_seko_tanto',
            's_seko_tanto': 's_seko_tanto',
            '#s_seko_no': 's_seko_no',
            '#s_shonin': {
                observe: 's_shonin',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#s_moushi': {
                observe: 's_moushi',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#s_pay_method_cd': {
                observe: 's_pay_method_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#s_nyukin_st': {
                observe: 's_nyukin_st',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#s_sekyu_tel': 's_sekyu_tel',
            '#s_sekyu_nm': 's_sekyu_nm',
            '#s_souke_nm': 's_souke_nm',
            '#s_k_nm': 's_k_nm',
            '#s_yuso': {
                observe: 's_yuso_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#s_bun_gas': {
                observe: 's_gassan_st',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#s_sekyu_st': {
                observe: 's_sekyu_st',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#s_seikyu_den_no': 's_seikyu_den_no',
            '#s_denpyo_no': 's_denpyo_no',
            '#s_kaishu_ymd_from': 's_kaishu_ymd_from',
            '#s_kaishu_ymd_to': 's_kaishu_ymd_to',
            '#s_keijo_ymd_from': 's_keijo_ymd_from',
            '#s_keijo_ymd_to': 's_keijo_ymd_to',
            '#s_kokyaku_no': 's_kokyaku_no',
        },
    };

    // 検索結果モデル
    var ResultModel = Backbone.Model.extend({
        defaults: function () {
            return {
                'row': null,
                'seikyu_den_no': null,
                'seko_no': null,
                'moushi_nm': null,
                'format_nm': null,
                'pay_method_cd': null,
                'pay_method_nm': null,
                'yuso_kbn_nm': null,
                'kaishu_ymd': null,
                'seikyu_hakko_ymd': null,
                'sekyu_nm': null,
                'souke_nm': null,
                'k_nm': null,
                'sekyu_prc': null,
                'nyukin_ymd': null,
                'bumon_nm': null,
                'sekyu_st': null,
                'shonin_st': null,
                'seikyu_print_date': null,
                'bun_gas_st': null,
                'juchu_denpyo_no': null,
                'uri_den_no': null,
                'nyukin_st': null,
                'nyukin_status': null,
                'sekyu_msi': null,
                'nohinsho': null,
                'syorui_tenpu_kbn': '0',
                'sekyu_tel': null,
                'sekyu_addr': null,
                'seikyu_zan': null,
                'nyukin_prc': null,
                'org_nyukin_prc': null,
                'seko_tanto': null,
                'seikyu_ymd': null,
                'keijo_ymd': null,
                'fix_keijo_ymd': null,
                'seikyu_print_tanto': null,
                'seikyu_approval_status': null,
                'selected': '0',
                'yuso_check': '0',
                'msi_check': '0',
                'seikyu_post_kbn': null,
                'data_kbn': null,   // 中身は申込区分
                'denpyo_data_kbn': null,
                'bun_gas_kbn_num': null,
                'back_color': null,
                'text_color': null,
                'bad_debt_loss_prc': null,
                'zaimu_rendo_denno': null,
                'zaimu_rendo_kbn': null,
                'd_import_kbn': null,
                'houjin_flg': null,
            };
        },
        validation: {
            kaishu_ymd: {
                required: function (value) {
                    var seikyu_zan = this.get('seikyu_zan');
                    if (seikyu_zan > 0 && $.msiJqlib.isNullEx2(value)) {
                        return true;
                    }
                },
                customFun: function (value) {
                    return $.msiJqlib.chkYmd(value);
                }
            },
            fix_keijo_ymd: {
                customFun: function (value) {
                    return $.msiJqlib.chkYmd(value);
                }
            },
        },
        labels: {
            kaishu_ymd: '支払期日',
            fix_keijo_ymd: '訂正計上日',
        }
    }); // ResultModel

    // 検索結果コレクション
    var ResultCollection = Backbone.Collection.extend({
        model: ResultModel
    });

    // 検索結果ビュー
    var ResultView = Backbone.View.extend({
        tmpl: _.template($('#tmpl-result').html()),
        events: {
            "click tr": "setSelected",
            // "dblclick tr": "goOther",  // mihara disabled 2025/05
            "click .dlg_date": "setDatePickerFocus",
        },
        bindings: {
            '.row': 'row',
            '.seikyu_den_no': 'seikyu_den_no',
            '.seko_no': 'seko_no',
            '.moushi_nm': 'moushi_nm',
            '.format_nm': 'format_nm',
            '.pay_method_cd': {
                observe: 'pay_method_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '.yuso_kbn_nm': 'yuso_kbn_nm',
            '.kaishu_ymd': 'kaishu_ymd',
            '.seikyu_hakko_ymd': 'seikyu_hakko_ymd',
            '.sekyu_nm': 'sekyu_nm',
            '.souke_nm': 'souke_nm',
            '.k_nm': 'k_nm',
            '.sekyu_prc': {
                observe: 'sekyu_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.nyukin_ymd': 'nyukin_ymd',
            '.bumon_nm': 'bumon_nm',
            '.sekyu_st': 'sekyu_st',
            '.shonin_st': 'shonin_st',
            '.seikyu_print_date': 'seikyu_print_date',
            '.bun_gas_st': 'bun_gas_st',
            '.juchu_denpyo_no': 'juchu_denpyo_no',
            'uri_den_no': 'uri_den_no',
            '.nyukin_st': 'nyukin_st',
            '.sekyu_msi': 'sekyu_msi',
            '.nohinsho': 'nohinsho',
            '.sekyu_tel': 'sekyu_tel',
            '.sekyu_addr': 'sekyu_addr',
            '.seikyu_zan': {
                observe: 'seikyu_zan',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.nyukin_prc': {
                observe: 'nyukin_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.seko_tanto': 'seko_tanto',
            '.seikyu_ymd': 'seikyu_ymd',
            '.keijo_ymd': 'keijo_ymd',
            '.fix_keijo_ymd': 'fix_keijo_ymd',
            '.seikyu_print_tanto': 'seikyu_print_tanto',
            'selected': 'selected',
            ".yuso_check": $.msiJqlib.getCheckBinding('yuso_check'),
            ".msi_check": $.msiJqlib.getCheckBinding('msi_check'),
            ".syorui_tenpu": $.msiJqlib.getCheckBinding('syorui_tenpu_kbn'),
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback({row: '.row'}, "error1"));
            this.listenTo(this.model, 'change:selected', this.setButtonStatus);
            this.render();
        },
        render: function () {
            this.$el.html(this.tmpl(this.model.toJSON()));
//            this.$el.html(this.tmpl({idx: this.model.cid}));
            this.$('.radio_set').buttonset();
            this.$el.addClass("result_div");
            this.stickit();
            this.$('tr').attr('data-seko_no', this.model.get('seko_no'));
            this.$('tr').addClass('seko_no_' + this.model.get('seko_no'));-
            this.$('tr').attr('data-seikyu_den_no', this.model.get('seikyu_den_no'));
            this.$('tr').addClass('seikyu_den_no_' + this.model.get('seikyu_den_no'));
            this.$(".keijo_ymd").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            // 郵送区分が「郵送」じゃなければチェックボックスを非表示にする
            if (this.model.get('seikyu_post_kbn') === YUSO_KBN_YUSO) {
                // 供花供物の場合は非表示にする
                this.$('.lbl_msi_check').show();
                if (this.model.get('data_kbn') === DATA_KBN_BECHU) {
                    this.$('.lbl_yuso_check').hide();
                } else {
                    this.$('.lbl_yuso_check').show();
                }
            } else {
                this.$('.lbl_yuso_check').hide();
                this.$('.lbl_msi_check').hide();
            }
            this.$('tr').find('td.td_yuso_kbn').css('background-color', this.model.get('back_color'));
            this.$('tr').find('td.td_sekyu_msi').css('background-color', this.model.get('back_color'));
            this.$('tr').find('input.yuso_kbn_nm').css('color', this.model.get('text_color'));
            // 支払方法
            if (this.model.get('data_kbn') === DATA_KBN_BECHU) {
                $.msiJqlib.setSelect2Com1(this.$(".pay_method_cd"), {data: $.msiJqlib.objToArray3(mydata.dataKbns.pay_method_kyoka)});
            } else {
                $.msiJqlib.setSelect2Com1(this.$(".pay_method_cd"), {data: $.msiJqlib.objToArray3(mydata.dataKbns.pay_method)});
            }
            // houjin_flg=2のもののみ活性
            if (!$.msiJqlib.isNullEx2(this.model.get('houjin_flg')) && this.model.get('houjin_flg') == '2') {
                this.$(".syorui_tenpu").removeAttr("disabled");
            } else {
                this.$(".syorui_tenpu").attr("disabled","disabled");
            }
            this.$(".pay_method_cd").attr('readonly', 'readonly') // mihara added 2025/05
            this.$(".syorui_tenpu").attr("disabled","disabled");  // mihara added 2025/05
            return this;
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        setButtonStatus: function () {
            var disabled = true;
            var print_disabled = false;
            var baddebt_disabled = false;
            var cancel_disabled = false;
            var gas_disabled = false;
            var selected_cnt = 0;
            var kakutei_kbn = null;
            var bun_gas_kbn_num = null;
            var data_kbn = null;
            var selected = this.model.get('selected');
            var role = appsk.app.model.get('role_nm');
            if (selected === '1') {
                this.$('tr').addClass('row-selected');
            } else {
                this.$('tr').removeClass('row-selected');
            }
            _.each(appsk.resultCol.models, function (m) {
                if (m.get('selected') === '1') {
                    kakutei_kbn = m.get('seikyu_approval_status');
                    bun_gas_kbn_num = m.get('bun_gas_kbn_num');
                    data_kbn = m.get('denpyo_data_kbn');
                    disabled = false;
                    selected_cnt++;
                    // 移行データは承認取消不可
//                    if (!$.msiJqlib.isNullEx2(m.get('d_import_kbn')) && m.get('d_import_kbn') == IMPORT_KBN) {
//                        cancel_disabled = true;
//                    }
                    // アフターの場合かつ売上伝票の財務連動区分が1:連携済の場合は請求承認解除不可とする。
                    if (m.get('zaimu_rendo_kbn') == '1' && m.get('data_kbn') == DATA_KBN_TAN) {
                        cancel_disabled = true;
                    }
                    // 入金状況が入金済の場合はtrue
                    if (!$.msiJqlib.isNullEx2(m.get('nyukin_status')) && m.get('nyukin_status') != NYUKIN_STATUS_ZAN) {
                        baddebt_disabled = true;
//                        cancel_disabled = true;
                    }
                    // 内金を除く入金があればtrue
                    if (m.get('org_nyukin_prc') != 0) {
                        cancel_disabled = true;
                        gas_disabled = true;
                    }
                    // 未承認の場合はtrue
                    if ($.msiJqlib.isNullEx2(kakutei_kbn) || kakutei_kbn == MIKAKUTEI) {
                        print_disabled = true;
                        baddebt_disabled = true;
                        cancel_disabled = true;
                    } else {
                        gas_disabled = true;
                    }
                    // 貸倒損失額が既に存在する場合はtrue
                    if (!$.msiJqlib.isNullEx2(m.get('bad_debt_loss_prc')) && m.get('bad_debt_loss_prc') != 0) {
                        baddebt_disabled = true;
                    }
                    // 権限が業務管理部（本社事務）以外はtrue
                    if (role == ROLE_RYOMUKANRI || role == ROLE_RYOMUKANRI2 || role == ROLE_EDIGYOMUKANRI || role == ROLE_NYUKINKANRI) {
                        baddebt_disabled = false;
                    } else {
                        baddebt_disabled = true;
                    }
                    // 権限設定
                    if (role == ROLE_SYSMAN || role == ROLE_MSI) {
                        cancel_disabled = false;
                    } else {
                        cancel_disabled = true;
                    }
                }
            });
            if (disabled) {
                $("#btn_tairyu").prop("disabled", true);  // mihara add 2025/05
                $("#btn_kakutei").prop("disabled", true);
                $("#btn_cancel").prop("disabled", true);
                $("#btn_save").prop("disabled", true);
                $("#btn_msi_print").prop("disabled", true);
                $("#btn_print").prop("disabled", true);
                $("#btn_gas").prop("disabled", true);
                $("#btn_bun").prop("disabled", true);
                $("#btn_baddebt").prop("disabled", true);
            } else {
                $("#btn_tairyu").prop("disabled", false);  // mihara add 2025/05
                $("#btn_save").prop("disabled", false);
                $("#btn_msi_print").prop("disabled", false);
                // 請求承認されている場合は確定ボタンを非活性
                if (!$.msiJqlib.isNullEx2(kakutei_kbn) && kakutei_kbn == KAKUTEI) {
                    $("#btn_kakutei").prop("disabled", true);
                } else {    
                    $("#btn_kakutei").prop("disabled", false);
                }
                // データ区分・分割合算状況によってボタンの活性非活性を設定
                if (selected_cnt === 1) {
                    if (data_kbn === DATA_KBN_SOGI || data_kbn === DATA_KBN_HOUJI) {
                        gas_disabled = true;
                    } else if (data_kbn === DATA_KBN_BECHU || data_kbn === DATA_KBN_TAN) {
                        if (bun_gas_kbn_num != BUNGAS_NORMAL) {
                            gas_disabled = true;
                        }
                    }
                } else {
                        gas_disabled = true;
                }
                // 請求書発行ボタン活性処理
                if (print_disabled) {
                    $("#btn_print").prop("disabled", true);
                } else {
                    $("#btn_print").prop("disabled", false);
                }
                // 貸倒処理ボタン活性処理
                if (baddebt_disabled) {
                    $("#btn_baddebt").prop("disabled", true);
                } else {
                    $("#btn_baddebt").prop("disabled", false);
                }
                // 承認取消ボタン活性処理
                if (cancel_disabled) {
                    $("#btn_cancel").prop("disabled", true);
                } else {
                    if (selected_cnt === 1 && !cancel_disabled) {
                        $("#btn_cancel").prop("disabled", false);
                    } else {
                        $("#btn_cancel").prop("disabled", true);
                    }
                }
                // 合算ボタン活性処理
                if (gas_disabled) {
                    $("#btn_gas").prop("disabled", true);
                } else {
                    $("#btn_gas").prop("disabled", false);
                }
            }
        },
        setSelected: function (e) {
            var selected = this.model.get('selected');
            var className = e.target.className;
            var maturity_day = changeToNum(appsk.app.model.get('maturity_day'));
            var cur_date = new Date();
            var cur_date2 = new Date();
            // org  var target = className.match('pay_method_cd|fix_keijo_ymd|kaishu_ymd|ui-button-text|syorui_tenpu');
            var target = className.match('pay_method_cd|ui-button-text|syorui_tenpu'); // mihara 2025/05
            // 該当項目のカーソル時のクリックは処理しない
            if (!$.msiJqlib.isNullEx2(target)) {
                return;
            }
            if (selected === '1') {
                this.model.set('selected', '0')
            } else {
                // mihara 2025/05 全てをクリアする.  択一のため
                _.each(appsk.resultCol.models, function(v, k){ 
                    var model = v;
                    model.set('selected', '0');
                });
                this.model.set('selected', '1');
            }
            // mihara comment 2025/05
            // if ($.msiJqlib.isNullEx2(this.model.get('kaishu_ymd'))) {
            //     cur_date.setDate(cur_date.getDate() + maturity_day);
            //     var year = cur_date.getFullYear();
            //     var month = ('0' + (cur_date.getMonth() + 1)).slice(-2);
            //     var day = ('0' + cur_date.getDate()).slice(-2);
            //     this.model.set('kaishu_ymd', year + '/' + month + '/' + day);
            // }
        },
        goOther: function (e) {
            var data_kbn = this.model.get('denpyo_data_kbn');
            var bun_gas_kbn_num = this.model.get('bun_gas_kbn_num');
            var seko_no = this.model.get('seko_no');
            var denpyo_no = this.model.get('juchu_denpyo_no');
            var seikyu_den_no = this.model.get('seikyu_den_no');
            var className = e.target.className;
            var target = className.match('pay_method_cd|fix_keijo_ymd|kaishu_ymd|ui-button-text|syorui_tenpu');
            // 該当項目のカーソル時のダブルクリックは処理しない
            if (!$.msiJqlib.isNullEx2(target)) {
                return;
            }
            // データ区分・分割状況によって画面遷移先が変わる
            var url = null;
            switch (data_kbn) {
                case DATA_KBN_SOGI:
                    if (bun_gas_kbn_num === BUNGAS_NORMAL) {
                        url = '/saiken/seikyusyo/shonin/sn/' + seko_no;
                    } else {
                        url = '/saiken/seikyubunkatsu/index/bksn/' + seko_no;
                    }
                    break;
                case DATA_KBN_HOUJI:
                    if (bun_gas_kbn_num === BUNGAS_NORMAL) {
                        url = '/saiken/seikyusyo/shoninh/sn/' + seko_no;
                    } else {
                        url = '/saiken/seikyubunkatsu/index/bksn/' + seko_no;
                    }
                    break;
                case DATA_KBN_TAN:
                    switch (bun_gas_kbn_num) {
                        case BUNGAS_NORMAL:
                            url = '/juchu/denpyo/order/denpyo_no/' + denpyo_no;
                            break;
                        case BUNGAS_GAS:
                            url = '/saiken/seikyugassan/index/gssn/' + seikyu_den_no;
                            break;
                    }
                    break;
                case DATA_KBN_BECHU:
                    switch (bun_gas_kbn_num) {
                        case BUNGAS_NORMAL:
                            url = '/juchu/bechudenpyo/order2/sn/'+seko_no+'/sekyu_cd/' + denpyo_no;
                            break;
                        case BUNGAS_BUN:
                            url = '/juchu/bechudenpyo/order2/sn/'+seko_no+'/sekyu_cd/' + denpyo_no;
                            break;
                        case BUNGAS_GAS:
                            url = '/saiken/seikyugassan/index/gssn/' + seikyu_den_no;
                            break;
                    }
                    break;
            }
            this._showNew( url );
            
        },
        // 他画面参照
        _showNew: function(path) {
            var url = $.msiJqlib.baseUrl() + path;
            // location.href = url;
            // window.open( url, '_blank' );
            var that = this;
            var refreshFunc = function() { console.log('refresh searching...');
                                           that.doSearch();
                                         };
            msiLib2.openWinSub( refreshFunc, url );
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        },
    }); // ResultView


    // 追加設定があれば設定. (ex. seikyu.sekolist.ex.js)
    if (msiGlobalObj.myAppViewOptExFunc) {
        msiGlobalObj.myAppViewOptExFunc(vOpt);
    }

    var AppView = Backbone.View.extend(vOpt);

    var app, mydata,
            orgDataApp = {},
            defDataApp = {},
            _resetData,
            _setInitData;

    // 初期化処理
    mydata = msiLib2.getJsonFromHtml($('#my-data-init-id'));
    defDataApp = mydata.dataApp;
    appsk.resultCol = new ResultCollection();
    app = new AppView({model: new AppModel});
    appsk.app = app;

    _resetData = function (myApp, dataCol) {
        app.model.set(myApp);
        appsk.resultCol.reset(dataCol);
        orgDataApp = app.model.toJSON(); // not JSON, but Object
        $(document).msiErrClearAll();
        app.model.trigger('change');

        $.msiJqlib.setBtnDisable($("#btn_tairyu, #btn_save, #btn_kakutei, #btn_cancel, #btn_msi_print, #btn_print, #btn_baddebt, #btn_bun, #btn_gas")); // 初期ボタン非活性  mihara 2025/05 add btn_tairyu
        var role = appsk.app.model.get('role_nm');
    };

    _setInitData = function () {
        if (mydata && mydata.dataApp) {
            _resetData(mydata.dataApp, mydata.dataCol);
        }

        // 初期画面で条件を受けて検索実行  2022/09/15 mihara
        if (_.has(mydata.dataApp, '_reload_s_seikyu_den_no')) {
            setTimeout( function() {$('#btn_search').click();}, 300 );
        }
    };

    // msiパーツの有効化 
    msiLib2.msiPrepareParts('#order');

    // 各種イベント設定
    $('#order').on('mouseover',
            '.result-list-sel',
            function () {
                var seikyu_den_no = $(this).attr('data-seikyu_den_no');
                $('.result-list-sel').find('td').removeClass('my-hover');
                $(".seikyu_den_no_" + seikyu_den_no).find('td').addClass('my-hover')
                        .end().find('a').focus();
            })
            .on('mouseout', '.result-list-sel',
                    function () {
                        var seikyu_den_no = $(this).attr('data-seikyu_den_no');
                        $(".seikyu_den_no_" + seikyu_den_no).find('td').removeClass('my-hover');
                    })
            .on('focus', '.result-list-sel',
                    function (e) {
                        var focus_id = $(e.target).attr('id'), seko_no;
                        // console.log( '**** focus id=>' + focus_id );
                        $('.result-list-sel').find('td').removeClass('my-hover');
                        if (focus_id && focus_id.indexOf('a_seikyu_den_no_') === 0) {
                            seikyu_den_no = focus_id.substr(10);
                            $(".seikyu_den_no_" + seikyu_den_no).find('td').addClass('my-hover');
                        }
                    })
            .on('focusout', '.result-list-sel',
                    function (e) {
                        var focus_id = $(e.target).attr('id'), seko_no;
                        // console.log( '**** focusout id=>' + focus_id );
                        if (focus_id && focus_id.indexOf('a_seikyu_den_no_') === 0) {
                            seikyu_den_no = focus_id.substr(10);
                            $(".seikyu_den_no_" + seikyu_den_no).find('td').removeClass('my-hover');
                        }
                    })

    // 上下矢印 移動処理
    var upDownArrowClick = function (e) {
        if (msiLib2.isPickerOpen())
            return; // helper が表示されている場合は処理しない

        var $tgt, seko_no, isUp, $els, i, $e, e_seko_no, bgn, end, step;
        // 38(up arrow) 40(down arrow)
        if (e.keyCode == 38) {
            isUp = true;
        } else if (e.keyCode == 40) {
            isUp = false;
        } else {
            return;
        }

        $tgt = $('tr.result-list-sel :focus');
        if ($tgt.length) {
            seko_no = $tgt.attr('id').substr(10); // a_seko_no_*
            // console.log( 'upDownArrowClick **** seko_no=>' + seko_no );
            if (seko_no) {
                $els = isUp ? $tgt.closest('tr').prevAll() : $tgt.closest('tr').nextAll();
                $els.each(function () {
                    $e = $(this), e_seko_no = $e.attr('data-seko_no');
                    if (e_seko_no != seko_no) {
                        if ($e.find('a')) {
                            // console.log( 'upDownArrowClick e_seko_no=>' + e_seko_no );
                            e.stopImmediatePropagation();
                            $e.find('a').focus().end().trigger('mouseover'); // trigger('focus');
                            return false;
                        }
                    }
                });
            }
        } else { // focus がないとき
            $els = $('tr.result-list-sel');
            if ($els.length) {
                if ($els.length === 1) {
                    $i = 0;
                    $e = $els.eq(i);
                    e_seko_no = $e.attr('data-seko_no');
                    if (e_seko_no && $e.find('a')) {
                        e.stopImmediatePropagation();
                        $e.find('a').focus().end().trigger('mouseover'); // trigger('focus');
                        return false;
                    }
                    return;
                }
                if (isUp) {
                    bgn = $els.length - 1, end = 0, step = -1;
                } else {
                    bgn = 0, end = $els.length - 1, step = 1;
                }
                for (i = bgn; i != end; i = i + step) {
                    $e = $els.eq(i);
                    e_seko_no = $e.attr('data-seko_no');
                    if (e_seko_no && $e.find('a')) {
                        e.stopImmediatePropagation();
                        $e.find('a').focus().end().trigger('mouseover'); // trigger('focus');
                        return false;
                    }
                }
            }
        }
    };

    // ページ遷移前の確認
    $(window).on('beforeunload', function () {
        if (app.isChanged()) {
            return "保存されていないデータがあります.";
        }
    });

    // リサイズ処理
    $(window).on('resize', function () {
        app.render();
    });

    // 広域イベント捕捉
    $(document).on('keydown', upDownArrowClick);

    // 初期フォーカス設定
    // setTimeout( function() {$('#s_number').focus();}, 0 );


    $.msiJqlib.initDone(function () {
        _setInitData();
    }); // 処理完了

    // $('#order').fadeIn('fast'); // ちらつきのごまかし

    // 大域変数に設定. 他の共用プログラムで使う(eg. saiken/seikyusyo/seikyulist)
    msiGlobalObj.myAppView = app;

});
