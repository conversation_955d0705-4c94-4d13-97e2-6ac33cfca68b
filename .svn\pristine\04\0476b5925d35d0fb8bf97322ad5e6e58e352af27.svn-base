<?php

/**
 * DataMapper_SekoKihonInfo
 *
 * 施行基本情報 データマッパークラス
 *
 * @deprecated
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mihara
 * @since      2014/xx/xx
 * @filesource 
 */

/**
 * 施行基本情報 データマッパークラス
 * 
 * @deprecated
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mihara
 * @since      2014/xx/xx
 */
class DataMapper_SekoKihonInfo extends DataMapper_Abstract {

    /**
     * 施行基本情報 取得
     *   cf. Juchu_JuchuCustomerinfo
     *
     * <AUTHOR> Mihara
     * @since      2014/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT *
        FROM (
            SELECT
                k.seko_no           -- 施行番号
                ,k.moushi_kbn       -- 申込区分
                ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd_num=k.moushi_kbn AND code_kbn='0010'
                AND delete_flg=0 limit 1) as moushi_kbn_nm -- 申込区分名
                ,k.sougi_cd         -- 葬儀区分
                ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd=k.sougi_cd AND code_kbn='0020'
                AND delete_flg=0 limit 1) as sougi_cd_nm -- 葬儀区分名
                ,k.daicho_no_eria   -- 台帳番号（エリア）
                ,TRIM(k.daicho_no_mm) AS daicho_no_mm   -- 台帳番号（月）
                ,k.daicho_no_seq    -- 台帳番号（連番）
                ,k.kaiin_code_kbn        -- 会員コード区分
                ,k.kaiin_kbn        -- 会員区分
                ,k.uketuke_tanto_cd -- 受付担当者コード
                ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
                ,k.seko_tanto_cd    -- 施行担当者コード
                ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
                ,k.k_nm             -- 故人名
                ,k.k_last_nm             -- 故人名(性)
                ,k.k_first_nm             -- 故人名(名)
                ,k.m_nm       -- 喪主
                ,k.m_last_nm             -- 喪主名(性)
                ,k.m_first_nm             -- 喪主名(名)
                ,k.mg_addr1   -- 現住所1
                ,k.sougi_ymd  -- 葬儀日
                ,TO_CHAR(k.sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd_ex    -- 葬儀日
                ,(SELECT to_char(nitei_ymd, 'YYYY/MM/DD') FROM seko_nitei  WHERE seko_no = k.seko_no
                AND nitei_kbn=1 AND delete_flg=0 limit 1) as nakijitu   -- 亡日
                ,(SELECT to_char(nitei_ymd, 'YYYY/MM/DD HH24:MI') FROM seko_nitei  WHERE seko_no = k.seko_no
                AND nitei_kbn=1 AND delete_flg=0 limit 1) as nakijitu_ex   -- 亡日
                ,(SELECT to_char(nitei_ymd, 'YYYY/MM/DD') FROM seko_nitei  WHERE seko_no = k.seko_no
                AND nitei_kbn=3 AND delete_flg=0 limit 1) as nyukan   -- 入棺
                ,(SELECT to_char(nitei_ymd, 'YYYY/MM/DD HH24:MI') FROM seko_nitei  WHERE seko_no = k.seko_no
                AND nitei_kbn=3 AND delete_flg=0 limit 1) as nyukan_ex   -- 入棺(時分含)
                ,k.status_kbn -- ステータス
                ,CASE k.status_kbn WHEN 1 THEN '見積中'
                                  WHEN 2 THEN '施行中'
                                  WHEN 3 THEN '請求済'
                                  WHEN 4 THEN '入金済'
                                  WHEN 9 THEN '失注'
                                  ELSE '-' END AS status_kbn_nm  -- ステータス表示名
                ,k.bumon_cd  -- 売上部門コード
                ,k.k_file_nm        -- 故人名添付ファイル
                ,k.k_sex_kbn        -- 性別
                ,k.k_haigu_kbn      -- 配偶者
                ,k.k_knm            -- 故人カナ名
                ,k.k_last_knm             -- 故人カナ名(性)
                ,k.k_first_knm             -- 故人カナ名(名)
                ,k.m_last_knm             -- 喪主カナ名(性)
                ,k.m_first_knm             -- 喪主カナ名(名)
                ,k.k_gengo          -- 生年月日元号
                ,k.k_seinengappi_ymd -- 生年月日
                ,k.k_seinengappi_ymd_y -- 生年月日
                ,k.k_nenrei_man     -- 故人年齢
                ,k.k_nenrei_kyounen -- 享年
                ,k.kg_yubin_no      -- 現住所郵便番号
                ,k.kg_addr1         -- 現住所1
                ,k.kg_tel           -- 現住所TEL
                ,k.kg_addr2         -- 現住所2
                ,k.kk_kinmusaki_kbn -- 勤務先
                ,k.kk_kinmusaki_nm  -- 勤務先名
                ,k.kk_tel           -- 勤務先TEL
                ,k.kk_yakusyoku_nm  -- 役職／職種
                ,k.kk_fax           -- 勤務先FAX
                ,k.souke_nm         -- 葬家
                ,k.souke_knm         -- 葬家カナ
                ,k.keishiki_kbn     -- 葬儀形式
                ,k.syushi_code_kbn        -- 宗旨コード区分
                ,k.syushi_kbn        -- 宗旨区分
                ,k.syuha_kbn        -- 宗派区分
                ,k.syuha_nm         -- 宗派名
                ,k.jyusho_cd        -- 寺院コード
                ,k.jyusho_nm        -- 寺院名
                ,k.biko1            -- メモ（出棺経路・納骨・壇払など）
                ,k.sd_hakko_kbn     -- 診断書発行区分
                ,k.sd_step_kbn      -- 診断書手続
                ,k.sd_yotei_ymd     -- 診断書発行予定時刻
                ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD HH24:MI') AS sd_yotei_ymd-- 診断書発行予定スタンプ
                ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD') AS sd_yotei_date-- 診断書発行予定時日付のみ
                ,TO_CHAR(k.sd_yotei_ymd ,'HH24:MI') AS sd_yotei_time-- 診断書発行予定時刻のみ
                ,k.sd_copy_cnt      -- 診断書コピー枚数
                ,k.kasoba_cd        -- 火葬場コード
                ,k.kasoba_nm -- 火葬場名
                ,k.az_death_cnt     -- 死亡診断書枚数
                ,k.az_inkan_kbn     -- 印鑑
                ,k.az_photo_cnt     -- 御写真枚数
                ,k.az_gojokai_nm    -- 互助会証書名称
                ,TO_CHAR(k.jichu_kakute_ymd, 'YYYY/MM/DD HH24:MI') AS jichu_kakute_ymd  -- 受注確定日
                ,k.free3_kbn    -- 祭壇種別
                ,k.v_free1    -- 施行プランコード
                ,k.v_free3    -- 会員コード
                ,k.v_free4    -- 問い合わせ先
                ,k.v_free5    -- 問い合わせ先TEL
                ,k.v_free6    -- 問い合わせ先FAX
                ,k.v_free9    -- 喪主メールアドレス
                ,k.est_shikijo_cd    -- 見積式場コード
                ,k.seko_shikijo_cd    -- 施行式場コード
                ,k.choi_kouden_cd
                ,k.choi_kyoka_cd
                ,k.choi_shohin_cd
                ,k.choi_shohin_bumon_cd
                ,k.choi_tokuchu_prc
                ,k.inquiry_topic
                ,k.choi_kahi_01
                ,k.choi_kahi_02
                ,k.choi_kahi_03
                ,k.choi_kahi_04
                ,k.choi_kahi_05
                ,k.choi_kahi_06
                ,k.saidan_sbt
                ,k.plan_shikijo_cd
                ,k.plan_syushi_kbn
                ,k.free3_cd
                ,k.free5_cd
                ,k.ts_free1
                ,k.ts_free2
                ,k.v_free19
                ,k.v_free20    -- ワンタイムパスワード
                ,k.v_free21
                ,k.m_zoku_nm
                ,k.m_zoku_nm2
                ,cnm_syushi.kbn_value_lnm AS syushi_nm
            FROM seko_kihon_info k
            LEFT OUTER JOIN tanto_mst t1
                ON k.uketuke_tanto_cd = t1.tanto_cd
                AND t1.delete_flg = 0
            LEFT OUTER JOIN tanto_mst t2
                ON k.seko_tanto_cd = t2.tanto_cd
                AND t2.delete_flg = 0
            LEFT JOIN code_nm_mst cnm_syushi
                ON cnm_syushi.code_kbn = '0240'
                AND cnm_syushi.kbn_value_cd_num = k.syushi_kbn
                AND cnm_syushi.delete_flg = 0
            WHERE  k.delete_flg = 0
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 施行情報を検索する(施行No ダイアログ用)
     * cf. Mref_SekodialogController
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     * @version 2015/02/02 ＰＨＰエラー（場所コードの重複）を修正 Kayo
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForDlg($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT *
        FROM (
            SELECT
                k.seko_no                               -- 施行番号
                ,k.moushi_kbn                           -- 申込区分
                ,moushi.kbn_value_lnm AS moushi_kbn_nm  -- 申込区分名
                ,k.sougi_cd                             -- 葬儀区分
                ,sougicd.kbn_value_lnm AS sougi_cd_nm   -- 葬儀区分名
		,k.daicho_no_eria                       -- 台帳番号（エリア）
		,TRIM(k.daicho_no_mm) AS daicho_no_mm   -- 台帳番号（月）
		,k.daicho_no_seq                        -- 台帳番号（連番）
                ,(k.daicho_no_eria || '-' || TRIM(k.daicho_no_mm) || '-' || k.daicho_no_seq ) AS daicho_no -- 台帳番号
                ,k.kaiin_kbn                        -- 会員区分
                ,kaiin.kbn_value_lnm                -- 会員区分名
                ,k.uketuke_tanto_cd                 -- 受付担当者コード
                ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
                ,k.seko_tanto_cd                    -- 施行担当者コード
                ,t2.tanto_nm AS seko_tanto_nm       -- 施行担当者名
                ,k.k_nm                             -- 故人名
                ,k.k_knm                            -- 故人名カナ
                ,k.kg_tel                           -- 現住所TEL  
                ,k.m_nm                             -- 喪主
                ,k.m_knm                            -- 喪主カナ
                ,k.mg_yubin_no                      -- 喪主郵便番号
                ,k.mg_addr1                         -- 喪主現住所1
                ,k.mg_addr2                         -- 喪主現住所2
                ,k.mg_tel                           -- 喪主TEL
                ,TO_CHAR(k.sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd    -- 葬儀日
                ,CASE WHEN k.moushi_kbn = 2 THEN  TO_CHAR(k.k_death_ymd, 'YYYY/MM/DD') 
                ELSE TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD') 
                END AS nakijitu -- 亡日
                ,TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD HH24:MI') AS nakijitu_ex -- 亡日(時分含)
                ,TO_CHAR(n3.nitei_ymd, 'YYYY/MM/DD') AS nyukan -- 入棺
                ,TO_CHAR(n3.nitei_ymd, 'YYYY/MM/DD HH24:MI') AS nyukan_ex -- 入棺(時分含)
                ,(SELECT TO_CHAR(nitei_ymd, 'YYYY/MM/DD') 
                    FROM seko_nitei_houji  
                    WHERE seko_no = k.seko_no
                        AND nitei_kbn=1 
                        AND delete_flg=0 
                    LIMIT 1) AS houji_ymd   -- 法事日, sougi_ymd に格納される
                ,CASE WHEN k.moushi_kbn = 2 THEN nh2.basho_cd 
                ELSE n11.basho_cd 
                END basho_cd -- 式場CD
                ,CASE WHEN k.moushi_kbn = 2 THEN 
                    CASE WHEN nh2.basho_kbn = 0 THEN '自宅' 
                    ELSE nh2.basho_nm
                    END
                ELSE 
                    CASE WHEN n11.basho_kbn = 0 THEN '自宅' 
                    ELSE n11.basho_nm
                    END
                END basho_nm -- 式場名
                ,(SELECT 
                    (
                        juchu_prc_sum 
                        +juchu_hepn_sum 
                        +juchu_nebk_sum 
                        +hoshi_prc_sum 
                        +out_zei_prc
                        +(sougi_keiyaku_prc + sougi_harai_prc +sougi_wari_prc)
                        +(sougi_keiyaku_zei + sougi_wari_zei)
                        +sougi_early_use_cost
                        +sougi_early_use_cost_zei
                        +sougi_premium_service_prc
                        +sougi_tokuten_prc + n_free9 + n_free10
                        +n_free5 
                    )
                    FROM juchu_denpyo  
                    WHERE seko_no = k.seko_no
                        AND data_kbn IN (1,2) 
                        AND delete_flg = 0 LIMIT 1) AS juchu_prc_sum -- 受注金額合計
                ,(SELECT 
                    (
                        uri_prc_sum 
                        + uri_hepn_sum 
                        + uri_nebk_sum 
                        + hoshi_prc_sum 
                        + out_zei_prc
                        +(sougi_keiyaku_prc + sougi_harai_prc +sougi_wari_prc)
                        +(sougi_keiyaku_zei + sougi_wari_zei)
                        +sougi_early_use_cost
                        +sougi_early_use_cost_zei
                        +sougi_premium_service_prc
                        +sougi_tokuten_prc + n_free9 + n_free10
                        +n_free5 
                    ) 
                    FROM uriage_denpyo
                    WHERE seko_no = k.seko_no
                        AND data_kbn IN (1,2) 
                        AND delete_flg = 0 LIMIT 1) AS uri_prc_sum -- 売上金額合計
                    ,k.status_kbn -- ステータス
                    ,seko_status.kbn_value_lnm AS status_kbn_nm  -- ステータス表示名
                    ,k.souke_nm                 -- 葬家
                    ,k.souke_knm                -- 葬家
                    ,k.keishiki_kbn             -- 葬儀形式
                    ,k.biko1                    -- メモ（出棺経路・納骨・壇払など）
                    ,k.bumon_cd                 -- 売上部門コード
                    ,bm.bumon_lnm  AS bumon_nm  -- 売上部門名
                    ,bm.bumon_lnm               -- 売上正式部門名       
                    ,bm.bumon_snm               -- 売上簡略部門名
                    ,CASE WHEN shi.status_kbn IS NULL THEN 1
                    ELSE shi.status_kbn
                    END AS order_flg -- 発注完了フラグ  0:未発注 1:発注済み
                    ,CASE WHEN shi.status_kbn IS NULL THEN '発注済み'
                    ELSE
                        CASE shi.status_kbn WHEN 0 THEN '未発注'
                        WHEN 1 THEN '発注済み'
                        END
                    END AS order_flg_nm  -- 発注完了表示名
                    ,k.kk_tel                   -- 勤務先TEL		
                    ,k.mg_m_tel                 -- 携帯番号			
                    ,k.mk_tel                   -- 勤務先TEL		
                    ,k.kg_addr1 AS kg_addr1_x   -- 故人現住所1   
                    ,k.kg_addr2 AS kg_addr2_x   -- 故人現住所2  
                    ,k.mg_addr1 AS mg_addr1_x   -- 喪主住所1    
                    ,k.mg_addr2 AS mg_addr2_x   -- 喪主住所2
                    ,sf.import_kbn
                    ,sf.free13_code_cd AS uketsuke_result
                FROM seko_kihon_info k
                LEFT JOIN seko_kihon_all_free sf    -- 施行基本汎用フリー情報
                    ON k.seko_no = sf.seko_no
                    AND 0 = sf.seq_no		
                    AND 0 = sf.delete_flg
                LEFT JOIN tanto_mst t1  -- 受付担当
                    ON k.uketuke_tanto_cd = t1.tanto_cd
                    AND t1.delete_flg = 0
                LEFT JOIN tanto_mst t2  -- 施行担当
                    ON k.seko_tanto_cd = t2.tanto_cd
                    AND t2.delete_flg = 0
                LEFT JOIN code_nm_mst moushi    -- 申込区分
                    ON moushi.kbn_value_cd = k.moushi_cd
                    AND moushi.code_kbn = k.moushi_code_kbn
                    AND moushi.delete_flg = 0
                LEFT JOIN code_nm_mst sougicd   -- 葬儀区分
                    ON sougicd.kbn_value_cd = k.sougi_cd
                    AND sougicd.code_kbn = k.sougi_code_kbn
                    AND sougicd.delete_flg = 0
                LEFT JOIN code_nm_mst kaiin -- 会員区分
                    ON kaiin.kbn_value_cd = k.kaiin_cd
                    AND kaiin.code_kbn = k.kaiin_code_kbn
                    AND kaiin.delete_flg = 0
                LEFT JOIN code_nm_mst seko_status -- ステータス
                    ON seko_status.kbn_value_cd_num = k.status_kbn
                    AND seko_status.code_kbn = '0610'
                    AND seko_status.delete_flg = 0
                LEFT JOIN seko_nitei n1 -- 日程死亡日
                    ON n1.seko_no = k.seko_no
                    AND n1.nitei_kbn = 1
                    AND n1.delete_flg = 0
                LEFT JOIN seko_nitei n3 -- 日程納棺
                    ON n3.seko_no = k.seko_no
                    AND n3.nitei_kbn = 3
                    AND n3.delete_flg = 0
                LEFT JOIN seko_nitei n11 -- 日程葬儀
                    ON n11.seko_no = k.seko_no
                    AND n11.nitei_kbn = 11
                    AND n11.delete_flg = 0
                LEFT JOIN seko_nitei_houji nh2  -- 日程法要
                    ON nh2.seko_no = k.seko_no
                    AND nh2.nitei_kbn = 1
                    AND nh2.delete_flg = 0
                LEFT JOIN bumon_mst bm -- 部門マスタ
                    ON k.bumon_cd = bm.bumon_cd
                    AND bm.delete_flg = 0
                LEFT JOIN
                    (
                        SELECT
                            seko_no
                            ,MIN(status_kbn) AS status_kbn
                        FROM seko_hachu_info
                        WHERE delete_flg = 0
                            AND data_kbn IN(1,2)
                        GROUP BY seko_no
                    ) AS shi 
                    ON k.seko_no=shi.seko_no
                WHERE k.delete_flg = 0
             ) T
            WHERE $whereStr
            $orderBy
            $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 施行情報を検索する(施行No ダイアログ事前相談用)
     * cf. Mref_SekodialogController
     *
     * <AUTHOR> Tosaka
     * @since 2022/xx/xx
     * @version 2015/02/02 ＰＨＰエラー（場所コードの重複）を修正 Kayo
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForDlgJizen($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
                
        WITH my_family_kaiin_2
        AS
            (SELECT *
               ,row_number() over (PARTITION BY kokyaku_no ORDER BY is_hon_kaiin DESC, nyukai_ymd DESC) AS _seq_no
               ,count(*) over (partition by kokyaku_no) AS cnt_kokyaku_kaiin
            FROM
            (SELECT   dm.kaiin_no
                ,dm.kokyaku_no
                ,dm.msi_no
                ,dm.disp_no
                ,dm.zoku_nm
                ,dm.credit_umu_kbn
                ,dm.kaiin_tekiyo_kbn
                ,dm.taikai_ymd
                ,CASE WHEN dm.kokyaku_no=kai.kokyaku_no THEN 1 ELSE 0 END AS is_hon_kaiin -- 本会員(筆頭者)
                ,kai.kaiin_sbt
                ,kai.tenpo_cd
                ,kai.bumon_cd
                ,kai.keisho_status
                ,kai.keisho_ymd
                ,kai.nyukai_ymd
            FROM kaiin_doukyo_mst dm
            INNER JOIN kaiin_mst kai
                ON kai.kaiin_no = dm.kaiin_no
            WHERE (dm.kaiin_tekiyo_kbn = 0 OR dm.kaiin_tekiyo_kbn IS NULL)  -- 9642(会員適用(会員マスタ)) 0:適用、1:退会
                AND dm.delete_flg=0
            ) my_family_kaiin
        )
        SELECT *
        FROM (
            SELECT
                k.seko_no                               -- 施行番号
                ,k.moushi_kbn                           -- 申込区分
                ,moushi.kbn_value_lnm AS moushi_kbn_nm  -- 申込区分名
		,k.daicho_no_eria                       -- 台帳番号（エリア）
		,TRIM(k.daicho_no_mm) AS daicho_no_mm   -- 台帳番号（月）
		,k.daicho_no_seq                        -- 台帳番号（連番）
                ,(k.daicho_no_eria || '-' || TRIM(k.daicho_no_mm) || '-' || k.daicho_no_seq ) AS daicho_no -- 台帳番号
                ,k.kaiin_kbn                        -- 会員区分
                ,kaiin.kbn_value_lnm                -- 会員区分名
                ,k.uketuke_tanto_cd                 -- 受付担当者コード
                ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
                ,k.seko_tanto_cd                    -- 施行担当者コード
                ,t2.tanto_nm AS seko_tanto_nm       -- 施行担当者名
                ,k.k_nm                             -- 故人名
                ,k.k_knm                            -- 故人名カナ
                ,k.kg_tel                           -- 現住所TEL  
                ,k.m_nm                             -- 喪主
                ,k.m_knm                            -- 喪主カナ
                ,k.mg_yubin_no                      -- 喪主郵便番号
                ,k.mg_addr1                         -- 喪主現住所1
                ,k.mg_addr2                         -- 喪主現住所2
                ,k.mg_tel                           -- 喪主TEL
                    ,k.status_kbn -- ステータス
                    ,seko_status.kbn_value_lnm AS status_kbn_nm  -- ステータス表示名
                    ,k.bumon_cd                 -- 売上部門コード
                    ,bm.bumon_lnm  AS bumon_nm  -- 売上部門名
                    ,bm.bumon_lnm               -- 売上正式部門名       
                    ,bm.bumon_snm               -- 売上簡略部門名		
                    ,k.mg_m_tel                 -- 携帯番号			
                    ,k.kg_addr1 AS kg_addr1_x   -- 故人現住所1   
                    ,k.kg_addr2 AS kg_addr2_x   -- 故人現住所2  
                    ,k.mg_addr1 AS mg_addr1_x   -- 喪主住所1    
                    ,k.mg_addr2 AS mg_addr2_x   -- 喪主住所2
                    ,sf.free13_code_cd AS uketsuke_result
                    ,k.k_cif_no
                    ,k.m_cif_no
                    ,k_kaiin.kaiin_no AS k_kaiin_no
                    ,m_kaiin.kaiin_no AS m_kaiin_no
                    ,sf.free14_code_cd AS shokai_kbn
                    ,sf.v_free27 AS gyosya_cd
                    ,TO_CHAR(sf.ts_free1, 'YYYY/MM/DD') AS uketsuke_ymd
                    ,TO_CHAR(sf.ts_free1, 'YYYY/MM/DD') AS uketuke_ymd
                    ,TO_CHAR(sf.ts_free4, 'YYYY/MM/DD') AS jizen_nyukin_ymd
                    ,k_kokyaku.shibo_ymd
                    ,k_kokyaku.jotai_kbn
                    ,cbi.contact_kbn
                    ,cnm_7931.kbn_value_lnm AS contact_kbn_nm
                    ,CASE WHEN sf.free14_code_cd = '99' THEN sf.v_free26 
                    ELSE cnm_9606.kbn_value_lnm END AS shokai_kbn_nm 
                    ,sf.v_free28 AS gyosya_nm
                    ,cnm_9612.kbn_value_lnm AS uketamawari_status_nm
                    ,CASE WHEN SUBSTR(k.seko_no,1,2) = '91' THEN SUBSTR(k.seko_no,1,2)
                    ELSE NULL END AS import_kbn
                FROM seko_kihon_info k
                LEFT JOIN seko_kihon_all_free sf    -- 施行基本汎用フリー情報
                    ON k.seko_no = sf.seko_no
                    AND 0 = sf.seq_no		
                    AND 0 = sf.delete_flg
                LEFT JOIN tanto_mst t1  -- 受付担当
                    ON k.uketuke_tanto_cd = t1.tanto_cd
                    AND t1.delete_flg = 0
                LEFT JOIN tanto_mst t2  -- 施行担当
                    ON k.seko_tanto_cd = t2.tanto_cd
                    AND t2.delete_flg = 0
                LEFT JOIN code_nm_mst moushi    -- 申込区分
                    ON moushi.kbn_value_cd = k.moushi_cd
                    AND moushi.code_kbn = k.moushi_code_kbn
                    AND moushi.delete_flg = 0
                LEFT JOIN code_nm_mst kaiin -- 会員区分
                    ON kaiin.kbn_value_cd = k.kaiin_cd
                    AND kaiin.code_kbn = k.kaiin_code_kbn
                    AND kaiin.delete_flg = 0
                LEFT JOIN code_nm_mst seko_status -- ステータス
                    ON seko_status.kbn_value_cd_num = k.status_kbn
                    AND seko_status.code_kbn = '0610'
                    AND seko_status.delete_flg = 0
                LEFT JOIN bumon_mst bm -- 部門マスタ
                    ON k.bumon_cd = bm.bumon_cd
                    AND bm.delete_flg = 0
              LEFT JOIN my_family_kaiin_2 k_kaiin   -- 会員マスタ(故人)
                    ON k_kaiin.kokyaku_no = k.k_cif_no
                    AND k_kaiin._seq_no = 1
                LEFT JOIN kokyaku_mst k_kokyaku    -- 顧客マスタ(故人)
                    ON k_kokyaku.kokyaku_no = k.k_cif_no
                    AND k_kokyaku.delete_flg = 0
              LEFT JOIN my_family_kaiin_2 m_kaiin   -- 会員マスタ(喪主)
                    ON m_kaiin.kokyaku_no = k.m_cif_no
                    AND m_kaiin._seq_no = 1
                LEFT JOIN customer_base_info cbi    -- 顧客基本情報
                    ON cbi.seko_no = k.seko_no
                    AND cbi.delete_flg = 0
                LEFT JOIN code_nm_mst cnm_7931 -- 問合せ区分
                    ON cnm_7931.kbn_value_cd_num = cbi.contact_kbn
                    AND cnm_7931.code_kbn = '7931'
                    AND cnm_7931.delete_flg = 0
                LEFT JOIN code_nm_mst cnm_9606 -- 紹介項目
                    ON cnm_9606.kbn_value_cd = sf.free14_code_cd
                    AND cnm_9606.code_kbn = '9606'
                    AND cnm_9606.delete_flg = 0
                LEFT JOIN code_nm_mst cnm_9612 -- 承りステータス
                    ON cnm_9612.kbn_value_cd = sf.free15_code_cd
                    AND cnm_9612.code_kbn = '9612'
                    AND cnm_9612.delete_flg = 0
                LEFT JOIN
                    (
                        SELECT
                            seko_no
                            ,MIN(status_kbn) AS status_kbn
                        FROM seko_hachu_info
                        WHERE delete_flg = 0
                            AND data_kbn IN(1,2)
                        GROUP BY seko_no
                    ) AS shi 
                    ON k.seko_no=shi.seko_no
                WHERE k.delete_flg = 0
             ) T
            WHERE $whereStr
            $orderBy
            $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 施行情報を検索する(出庫確認用)
     * cf. Zaiko_ShukolistController
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForDlgShuko($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT T.*
      ,CASE T.shuko_flg WHEN 0 THEN '未出庫'
                        WHEN 1 THEN '一部出庫済み'
                        WHEN 2 THEN '出庫済み'
                        WHEN 9 THEN '対象外'
                        ELSE '?' END AS shuko_flg_nm -- 出荷状況表示名
      ,CASE T.shuko_flg WHEN 0 THEN '×'
                        WHEN 1 THEN '△'
                        WHEN 2 THEN '○'
                        WHEN 9 THEN '－'
                        ELSE '?' END AS shuko_flg_nm2 -- 出荷状況表示名
  FROM (
  SELECT
		 k.seko_no          -- 施行番号
		,k.moushi_kbn       -- 申込区分
		,cm1.kbn_value_lnm AS moushi_kbn_nm 		-- 申込区分名
        ,k.sougi_cd         -- 葬儀区分
		,cm2.kbn_value_lnm AS sougi_cd_nm 			-- 葬儀区分名
		,k.daicho_no_eria   -- 台帳番号（エリア）
		,TRIM(k.daicho_no_mm) AS daicho_no_mm   -- 台帳番号（月）
		,k.daicho_no_seq    -- 台帳番号（連番）
		,(k.daicho_no_eria || '-' || TRIM(k.daicho_no_mm) || '-' || k.daicho_no_seq ) AS daicho_no -- 台帳番号
		,k.kaiin_kbn			-- 会員区分
		,cm3.kbn_value_lnm	-- 会員区分名
		,k.kaiin_sonota		-- 会員区分（その他）
		,k.uketuke_tanto_cd	-- 受付担当者コード
		,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
		,k.seko_tanto_cd    -- 施行担当者コード
		,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
		,k.k_nm				-- 故人名
		,k.k_knm				-- 故人名カナ
        ,k.kg_tel           -- 現住所TEL                    2016/02/05 Mihara add
		,k.m_nm				-- 喪主
		,k.m_knm				-- 喪主カナ
		,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_yubin_no
                           ELSE k.kg_yubin_no END AS mg_yubin_no -- 喪主郵便番号
		,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_addr1
                           ELSE k.kg_addr1 END AS mg_addr1		-- 喪主現住所1
		,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_addr2
                           ELSE k.kg_addr2 END AS mg_addr2		-- 喪主現住所2
		,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_tel
                           ELSE k.kg_tel END AS mg_tel			-- 喪主TEL
		,TO_CHAR(k.sougi_ymd,  'YYYY/MM/DD') as sougi_ymd		-- 葬儀日
		,TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD') as nakijitu		-- 亡日
		,TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD HH24:MI') as nakijitu_ex -- 亡日(時分含)
		,TO_CHAR(n3.nitei_ymd, 'YYYY/MM/DD') as nyukan				-- 入棺
		,TO_CHAR(n3.nitei_ymd, 'YYYY/MM/DD HH24:MI') as nyukan_ex	-- 入棺(時分含)
		,to_char(n1hoji.nitei_ymd, 'YYYY/MM/DD') 	 as houji_ymd  	-- 法事日, sougi_ymd に格納される
		,n2.basho_cd -- 式場CD
		,n2.basho_nm -- 式場名
		,(SELECT juchu_prc_sum FROM juchu_denpyo  WHERE seko_no = k.seko_no
         AND data_kbn=1 AND delete_flg=0 limit 1) as juchu_prc_sum -- 受注金額合計
		,(SELECT uri_prc_sum FROM uriage_denpyo  WHERE seko_no = k.seko_no
         AND data_kbn=1 AND delete_flg=0 limit 1) as uri_prc_sum -- 売上金額合計
		,k.status_kbn -- ステータス
		,CASE k.status_kbn WHEN 1 THEN '見積中'
                          WHEN 2 THEN '施行中'
                          WHEN 3 THEN '請求済'
                          WHEN 4 THEN '入金済'
                          WHEN 9 THEN '失注'
                          ELSE '-' END AS status_kbn_nm  -- ステータス表示名
		,k.souke_nm         -- 葬家
		,k.souke_knm         -- 葬家カナ
		,k.keishiki_kbn     -- 葬儀形式
		,k.biko1            -- メモ（出棺経路・納骨・壇払など）
		,k.bumon_cd         -- 売上部門コード
		,bm.bumon_lnm  AS bumon_nm        -- 売上部門名
		,bm.bumon_lnm       -- 売上正式部門名
		,bm.bumon_snm       -- 売上簡略部門名
       ,k.order_finish_flg AS order_flg -- 発注完了フラグ  0:未発注 1:発注済み
       ,CASE k.order_finish_flg WHEN 0 THEN '未発注'
                                WHEN 1 THEN '発注済み'
                                ELSE '-' END AS order_flg_nm  -- 発注完了表示名
		,ty_kaijyo_cd -- 通夜会場場所コード
		,ty_kaijyo_nm -- 通夜会場場所名称
-- Shuko追加部分
        ,(SELECT CASE WHEN SUM(x.misyuko_suryo) = 0 AND SUM(x.syuko_suryo) <> 0 THEN
                2 -- 入庫完了
            WHEN SUM(x.syuko_suryo) <> 0 THEN
                1 -- 一部入庫
            WHEN SUM(x.misyuko_suryo) <> 0  THEN
                0 -- 入庫なし
            ELSE 
                9   -- 対象外
            END AS shuko_flg
        FROM (SELECT
            CASE WHEN v.shuko_sumi_kbn = 0 THEN -- 0:未出庫, 1:一部出庫, 2:全出庫, 9:強制完了
                1
            ELSE
                0
            END	AS	misyuko_suryo
            ,CASE WHEN v.shuko_sumi_kbn = 1 THEN -- 0:未出庫, 1:一部出庫, 2:全出庫, 9:強制完了
                1
            ELSE
                0
            END	AS	syuko_suryo
            FROM v_nyuko_shuko_msi v
            WHERE k.seko_no= v.seko_no
            AND	set_shohin_kbn = 0) x)  AS  shuko_flg
               
  /*              
  ,CASE WHEN EXISTS( SELECT * FROM juchu_denpyo_msi jdm00
						, juchu_denpyo	jd00
						, shohin_mst	sm00
                      WHERE k.seko_no			=	jd00.seko_no
						AND jd00.denpyo_no		=	jdm00.denpyo_no
						AND jdm00.shohin_cd		=	sm00.shohin_cd
                        AND jd00.delete_flg		=	0
						AND jdm00.delete_flg	=	0
						AND jdm00.juchu_suryo	<>	0				-- 2015/02/08 ADD Kayo
						AND sm00.delete_flg		=	0
						AND k.moushi_kbn		=	jd00.data_kbn	-- 2015/02/08 ADD Kayo
                        AND sm00.zaiko_knri_kbn IN (1) ) OR
             EXISTS( SELECT * FROM seko_kashidasi_bihn skb01
                      WHERE k.seko_no=skb01.seko_no
					  AND	skb01.delete_flg=	0
					  AND	skb01.suryo		<>	0 )
   THEN -- 出荷対象あり
     CASE WHEN NOT EXISTS( SELECT *
                            FROM juchu_denpyo_msi jdm00
                                ,juchu_denpyo jd00
                                ,shohin_mst sm00
                            WHERE k.seko_no=jd00.seko_no
								AND jd00.denpyo_no		=	jdm00.denpyo_no
								AND jdm00.shohin_cd		=	sm00.shohin_cd
								AND jd00.delete_flg		=	0
								AND jdm00.delete_flg	=	0
								AND jdm00.juchu_suryo	<>	0				-- 2015/02/08 ADD Kayo
								AND k.moushi_kbn		=	jd00.data_kbn	-- 2015/02/08 ADD Kayo
								AND sm00.zaiko_knri_kbn IN (1)
								AND jdm00.shuko_status_kbn NOT IN (2,9) ) AND
               NOT EXISTS( SELECT * FROM seko_kashidasi_bihn skb01
                            WHERE k.seko_no=skb01.seko_no
							AND skb01.delete_flg	=	0
							AND skb01.suryo			<>	0
                            AND skb01.shuko_status_kbn NOT IN (2,9) ) THEN 2 -- 出荷済み
          WHEN EXISTS( SELECT * FROM juchu_denpyo_msi jdm00
							, juchu_denpyo jd00
							, shohin_mst sm00
                        WHERE k.seko_no=jd00.seko_no
							AND	jd00.denpyo_no	=	jdm00.denpyo_no
							AND	jdm00.shohin_cd	=	sm00.shohin_cd
							AND jd00.delete_flg	=	0
							AND jdm00.delete_flg=	0
							AND jdm00.juchu_suryo	<>	0				-- 2015/02/08 ADD Kayo
							AND k.moushi_kbn	=	jd00.data_kbn       -- 2015/02/08 ADD Kayo
							AND sm00.zaiko_knri_kbn IN (1)
							AND jdm00.shuko_status_kbn IN (1,2,9) ) OR
               EXISTS( SELECT * FROM seko_kashidasi_bihn skb01
                        WHERE k.seko_no=skb01.seko_no
							AND skb01.delete_flg=0
							AND skb01.suryo	<>	0
							AND skb01.shuko_status_kbn IN (1,2,9) ) THEN 1 -- 一部出荷済み
          ELSE 0 -- 未出庫
     END
   ELSE 9           -- 対象外
   END AS shuko_flg
   */             
  ,CASE WHEN EXISTS( SELECT *
			FROM juchu_denpyo_msi jdm00
				, juchu_denpyo jd00
				, shohin_mst sm00
            WHERE k.seko_no=jd00.seko_no 
			AND jd00.denpyo_no=jdm00.denpyo_no
			AND jdm00.shohin_cd=sm00.shohin_cd
            AND jd00.delete_flg=0 
			AND jdm00.delete_flg=0
			AND sm00.delete_flg=0
            AND sm00.zaiko_knri_kbn IN (1) ) THEN 1
        ELSE 0 END AS shuko_zaiko_ari_flg
  ,CASE WHEN EXISTS( SELECT *
			FROM seko_kashidasi_bihn skb01
            WHERE k.seko_no=skb01.seko_no 
			AND skb01.delete_flg=0
			AND skb01.suryo>0 ) THEN 1
        ELSE
			0
		END					AS shuko_kashi_ari_flg
-- Shuko追加部分 end
  FROM seko_kihon_info k
	LEFT JOIN tanto_mst t1
		ON k.uketuke_tanto_cd = t1.tanto_cd
		AND t1.delete_flg = 0
	LEFT JOIN tanto_mst t2
		ON k.seko_tanto_cd    = t2.tanto_cd
		AND t2.delete_flg = 0
	LEFT JOIN nm_jyusho_mst jm
		ON k.kasoba_cd = jm.jyusho_cd
		AND jm.jyusho_kbn = 3
		AND jm.delete_flg = 0
	LEFT JOIN code_nm_mst cm1
		ON k.moushi_kbn 		=	cm1.kbn_value_cd_num
		AND cm1.code_kbn		=	'0010'
		AND cm1.delete_flg 		=	0
	LEFT JOIN code_nm_mst cm2
		ON  k.sougi_cd 			=	cm2.kbn_value_cd
		AND cm2.code_kbn			=	'0020'
		AND cm2.delete_flg		=	0
	LEFT JOIN code_nm_mst cm3
		ON cm3.kbn_value_cd = k.kaiin_cd
		AND cm3.code_kbn='0030'
		AND cm3.delete_flg=0
	LEFT JOIN seko_nitei n1
		ON n1.seko_no=k.seko_no
		AND n1.nitei_kbn=1
		AND n1.delete_flg=0
	LEFT JOIN seko_nitei n2
		ON n2.seko_no=k.seko_no
		AND n2.nitei_kbn=7
		AND n2.delete_flg=0
	LEFT JOIN seko_nitei n3
		ON n3.seko_no=k.seko_no
		AND n3.nitei_kbn=3
		AND n3.delete_flg=0
	LEFT JOIN bumon_mst bm
		ON k.bumon_cd = bm.bumon_cd
		AND bm.delete_flg=0
	LEFT JOIN seko_nitei_houji n1hoji
		ON  n1hoji.seko_no		=	k.seko_no
		AND n1hoji.nitei_kbn	=	1
		AND n1hoji.delete_flg	=	0
 WHERE k.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 施行情報を検索する(貸出品回収一覧用)
     * cf. Zaiko_NyukolistkashiController
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForDlgNyukoKashi($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT T.*
      ,CASE T.nyuko_flg WHEN 0 THEN '未入庫'
                        WHEN 1 THEN '一部入庫済み'
                        WHEN 2 THEN '入庫済み'
                        WHEN 9 THEN '対象外'
                        ELSE '?' END AS nyuko_flg_nm -- 入庫状況表示名
      ,CASE T.nyuko_flg WHEN 0 THEN '×'
                        WHEN 1 THEN '△'
                        WHEN 2 THEN '○'
                        WHEN 9 THEN '－'
                        ELSE '?' END AS nyuko_flg_nm2 -- 入庫状況表示名
  FROM (
  SELECT
         k.seko_no          -- 施行番号
		,k.moushi_kbn       -- 申込区分
		,cm1.kbn_value_lnm AS moushi_kbn_nm 		-- 申込区分名
        ,k.sougi_cd         -- 葬儀区分
		,cm2.kbn_value_lnm AS sougi_cd_nm 			-- 葬儀区分名
        ,k.daicho_no_eria   -- 台帳番号（エリア）
        ,TRIM(k.daicho_no_mm) AS daicho_no_mm   -- 台帳番号（月）
        ,k.daicho_no_seq    -- 台帳番号（連番）
		,(k.daicho_no_eria || '-' || TRIM(k.daicho_no_mm) || '-' || k.daicho_no_seq ) AS daicho_no -- 台帳番号
		,k.kaiin_kbn        -- 会員区分
		,cm3.kbn_value_lnm	-- 会員区分名
		,k.kaiin_sonota		-- 会員区分（その他）
		,k.uketuke_tanto_cd -- 受付担当者コード
		,t1.tanto_nm		AS uketuke_tanto_nm -- 受付担当者名
		,k.seko_tanto_cd						-- 施行担当者コード
		,t2.tanto_nm		AS seko_tanto_nm    -- 施行担当者名
		,k.k_nm									-- 故人名
		,k.k_knm									-- 故人名カナ
        ,k.kg_tel           -- 現住所TEL                    2016/02/05 Mihara add
		,k.m_nm									-- 喪主
		,k.m_knm									-- 喪主カナ
		,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_yubin_no
                           ELSE k.kg_yubin_no END AS mg_yubin_no	-- 喪主郵便番号
		,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_addr1
                           ELSE k.kg_addr1 END AS mg_addr1			-- 喪主現住所1
		,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_addr2
                           ELSE k.kg_addr2 END AS mg_addr2			-- 喪主現住所2
		,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_tel
                           ELSE k.kg_tel END AS mg_tel				-- 喪主TEL
		,TO_CHAR(k.sougi_ymd, 'YYYY/MM/DD')				as sougi_ymd	-- 葬儀日
		,TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD')			as nakijitu		-- 亡日
		,TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD HH24:MI')	as nakijitu_ex	-- 亡日(時分含)
		,TO_CHAR(n3.nitei_ymd, 'YYYY/MM/DD')			as nyukan		-- 入棺
		,TO_CHAR(n3.nitei_ymd, 'YYYY/MM/DD HH24:MI')	as nyukan_ex	-- 入棺(時分含)
		,to_char(n1hoji.nitei_ymd, 'YYYY/MM/DD') 		as houji_ymd   	-- 法事日, sougi_ymd に格納される
		,n2.basho_cd -- 式場CD
		,n2.basho_nm -- 式場名
		,(SELECT juchu_prc_sum FROM juchu_denpyo  WHERE seko_no = k.seko_no
			AND delete_flg=0 limit 1) as juchu_prc_sum -- 受注金額合計
		,(SELECT uri_prc_sum FROM uriage_denpyo  WHERE seko_no = k.seko_no
			AND delete_flg=0 limit 1) as uri_prc_sum -- 売上金額合計
		,k.status_kbn -- ステータス
		,CASE k.status_kbn WHEN 1 THEN '見積中'
                          WHEN 2 THEN '施行中'
                          WHEN 3 THEN '請求済'
                          WHEN 4 THEN '入金済'
                          WHEN 9 THEN '失注'
                          ELSE '-' END AS status_kbn_nm  -- ステータス表示名
		,k.souke_nm							-- 葬家
		,k.souke_knm							-- 葬家カナ
		,k.keishiki_kbn						-- 葬儀形式
		,k.biko1							-- メモ（出棺経路・納骨・壇払など）
		,k.bumon_cd							-- 売上部門コード
		,bm.bumon_lnm  AS bumon_nm			-- 売上部門名
		,bm.bumon_lnm						-- 売上正式部門名
		,bm.bumon_snm						-- 売上簡略部門名
       ,k.order_finish_flg AS order_flg -- 発注完了フラグ  0:未発注 1:発注済み
       ,CASE k.order_finish_flg WHEN 0 THEN '未発注'
                                WHEN 1 THEN '発注済み'
                                ELSE '-' END AS order_flg_nm  -- 発注完了表示名

		,ty_kaijyo_cd											-- 通夜会場場所コード
		,ty_kaijyo_nm											-- 通夜会場場所名称
		-- Nyuko追加部分
		,(SELECT COUNT(*) 
			FROM nyuko_shuko_msi msi00
			LEFT JOIN nyuko_shuko_denpyo nsd11
				ON nsd11.nyuko_shuko_kbn	=	3 -- 3:貸出品出庫
				AND nsd11.denpyo_no			=	msi00.denpyo_no
				AND nsd11.delete_flg		=	0
				WHERE msi00.delete_flg		=	0
				AND msi00.suryo				>	0
				AND msi00.shuko_sumi_kbn	<>	0 
				AND nsd11.seko_no			=	k.seko_no
								 ) AS kashi_shuko_cnt -- 貸出品出庫明細数
		-- 2015/12/20 UPD Kayo	
		,(SELECT  CASE WHEN x.nyuko_suryo = 0 THEN
				0 -- 入庫なし
			WHEN x.zan_suryo = 0 THEN
				2 -- 入庫完了
			WHEN x.zan_suryo <> 0 THEN
				1 -- 一部入庫
			ELSE
				9   -- 対象外
			END	AS nyuko_flg
			FROM	(SELECT  sum(coalesce(msi00.suryo,0)) 	AS suryo
							,sum(coalesce(msi00.nyuko_suryo,0)) AS nyuko_suryo
							,sum(coalesce(msi00.suryo,0)-coalesce(msi00.nyuko_suryo,0)) AS zan_suryo
					FROM nyuko_shuko_msi msi00
					LEFT JOIN nyuko_shuko_denpyo nsd11
						ON  nsd11.nyuko_shuko_kbn = 3 -- 3:貸出品出庫 2015/12/20 UPD Kayo	
						AND nsd11.denpyo_no	= msi00.denpyo_no
						AND nsd11.delete_flg	= 0
						WHERE msi00.delete_flg	= 0
						AND nsd11.seko_no	= k.seko_no -- 2015/12/20 UPD Kayo
						AND msi00.suryo		> 0) x)		AS	nyuko_flg
  FROM seko_kihon_info k
	LEFT JOIN tanto_mst t1
		ON k.uketuke_tanto_cd = t1.tanto_cd
		AND t1.delete_flg = 0
	LEFT JOIN tanto_mst t2
		ON k.seko_tanto_cd    = t2.tanto_cd
		AND t2.delete_flg = 0
	LEFT JOIN nm_jyusho_mst jm
		ON k.kasoba_cd = jm.jyusho_cd
		AND jm.jyusho_kbn = 3
		AND jm.delete_flg = 0
	LEFT JOIN code_nm_mst cm1
		ON k.moushi_kbn 		=	cm1.kbn_value_cd_num
		AND cm1.code_kbn		=	'0010'
		AND cm1.delete_flg 		=	0
	LEFT JOIN code_nm_mst cm2
		ON  k.sougi_cd 			=	cm2.kbn_value_cd
		AND cm2.code_kbn		=	'0020'
		AND cm2.delete_flg		=	0
	LEFT JOIN code_nm_mst cm3
		ON cm3.kbn_value_cd = k.kaiin_cd
		AND cm3.code_kbn='0030'
		AND cm3.delete_flg=0
	LEFT JOIN seko_nitei n1
		ON n1.seko_no=k.seko_no
		AND n1.nitei_kbn=1
		AND n1.delete_flg=0
	LEFT JOIN seko_nitei n2
		ON n2.seko_no=k.seko_no
		AND n2.nitei_kbn=7
		AND n2.delete_flg=0
	LEFT JOIN seko_nitei n3
		ON n3.seko_no=k.seko_no
		AND n3.nitei_kbn=3
		AND n3.delete_flg=0
	LEFT JOIN bumon_mst bm
		ON k.bumon_cd = bm.bumon_cd
		AND bm.delete_flg=0
	LEFT JOIN seko_nitei_houji n1hoji
		ON  n1hoji.seko_no		=	k.seko_no
		AND n1hoji.nitei_kbn	=	1
		AND n1hoji.delete_flg	=	0
WHERE k.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 施行基本情報 取得
     *
     * <AUTHOR> sai
     * @since      2015/07/13
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find2($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
        SELECT
            k.seko_no           -- 施行番号
            ,k.moushi_kbn       -- 申込区分
       ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd_num=k.moushi_kbn AND code_kbn='0010'
         AND delete_flg=0 limit 1) as moushi_kbn_nm -- 申込区分名
            ,k.sougi_cd         -- 葬儀区分
       ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd=k.sougi_cd AND code_kbn='0020'
         AND delete_flg=0 limit 1) as sougi_cd_nm -- 葬儀区分名
            ,k.daicho_no_eria   -- 台帳番号（エリア）
            ,TRIM(k.daicho_no_mm) AS daicho_no_mm   -- 台帳番号（月）
            ,k.daicho_no_seq    -- 台帳番号（連番）
            ,k.kaiin_kbn        -- 会員区分
            ,k.kaiin_sonota     -- 会員区分その他
            ,k.uketuke_tanto_cd -- 受付担当者コード
            ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
            ,k.seko_tanto_cd    -- 施行担当者コード
            ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
            ,k.k_nm             -- 故人名
            ,k.k_knm             -- 故人名カナ
            ,k.m_nm       -- 喪主
            ,k.m_knm       -- 喪主カナ
            ,k.mg_addr1   -- 現住所1
            ,k.sougi_ymd  -- 葬儀日
            ,(k.daicho_no_eria || '-' || TRIM(k.daicho_no_mm) || '-' || k.daicho_no_seq ) AS daicho_no -- 台帳番号
            ,(SELECT nitei_ymd FROM seko_nitei  WHERE seko_no = k.seko_no
              AND nitei_kbn=1 AND delete_flg=0 limit 1) as nakijitu_ex   -- 亡日
            ,k.status_kbn -- ステータス
            ,k.k_file_nm        -- 故人名添付ファイル
            ,k.k_sex_kbn        -- 性別
            ,k.k_haigu_kbn      -- 配偶者
            ,k.k_knm            -- 故人カナ名
            ,k.k_gengo          -- 生年月日元号
            ,k.k_seinengappi_ymd -- 生年月日
            ,k.k_nenrei_man     -- 故人年齢
            ,k.k_nenrei_kyounen -- 享年
            ,k.kg_yubin_no      -- 現住所郵便番号
            ,k.kg_addr1         -- 現住所1
            ,k.kg_tel           -- 現住所TEL
            ,k.kg_addr2         -- 現住所2
            ,k.kg_setai_kbn     -- 世帯主
            ,k.kj_kbn           -- 住民登録住所の現住所に同じチェックボックス
            ,k.kj_yubin_no      -- 住民登録住所郵便番号
            ,k.kj_addr1         -- 住民登録住所1
            ,k.kj_tel           -- 住民登録住所TEL
            ,k.kj_addr2         -- 住民登録住所2
            ,k.kj_setai_kbn     -- 住民登録住所世帯主
            ,k.kh_kbn           -- 本籍の現住所に同じチェックボックス
            ,k.kh_yubin_no      -- 本籍郵便番号
            ,k.kh_addr1         -- 本籍住所1
            ,k.kh_hito_kbn      -- 筆頭者
            ,k.kh_addr2         -- 本籍住所2
            ,k.kk_kinmusaki_kbn -- 勤務先
            ,k.kk_kinmusaki_nm  -- 勤務先名
            ,k.kk_tel           -- 勤務先TEL
            ,k.kk_yakusyoku_nm  -- 役職／職種
            ,k.kk_fax           -- 勤務先FAX
            ,k.souke_nm         -- 葬家
            ,k.souke_knm         -- 葬家カナ
            ,k.souke_tel        -- 葬家TEL
            ,k.keishiki_kbn     -- 葬儀形式
            ,k.syuha_kbn        -- 宗旨・宗派コード
            ,k.syuha_nm         -- 宗旨・宗派
            ,k.jyusho_cd        -- 寺院コード
            ,k.jyusho_nm        -- 寺院名
            ,jm.jyusho_lknm     -- 寺院名カナ
            ,m_zoku.kbn_value_lnm AS m_zoku_nm -- 続柄名
                
        FROM
            seko_kihon_info k
            LEFT OUTER JOIN tanto_mst t1
            ON  (
                    k.uketuke_tanto_cd = t1.tanto_cd
                AND t1.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t2
            ON  (
                    k.seko_tanto_cd = t2.tanto_cd
                AND t2.delete_flg = 0
                )
            LEFT OUTER JOIN nm_jyusho_mst jm
            ON  (
                    k.jyusho_cd = jm.jyusho_cd
                AND jm.jyusho_kbn = 1
                AND jm.delete_flg = 0
                )
            LEFT JOIN code_nm_mst m_zoku ON 
                m_zoku.code_kbn = '0190' 
            AND k.m_zoku_cd = m_zoku.kbn_value_cd 
            AND m_zoku.delete_flg = 0
        WHERE  k.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 施行情報を検索する(施行データエクスポート用)
     * cf. findForDlg() からコピー(2015/08/26) link_exp_no,link_exp_info の追加
     *
     * <AUTHOR> Mihara
     * @since 2015/08/26
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForDlgSekoExp($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
  SELECT
        k.seko_no           -- 施行番号
       ,k.moushi_kbn       -- 申込区分
       ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd_num=k.moushi_kbn AND code_kbn='0010'
         AND delete_flg=0 limit 1) as moushi_kbn_nm -- 申込区分名
            ,k.sougi_cd         -- 葬儀区分
       ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd=k.sougi_cd AND code_kbn='0020'
         AND delete_flg=0 limit 1) as sougi_cd_nm -- 葬儀区分名
		,k.daicho_no_eria   -- 台帳番号（エリア）
		,TRIM(k.daicho_no_mm) AS daicho_no_mm   -- 台帳番号（月）
		,k.daicho_no_seq    -- 台帳番号（連番）
       ,(k.daicho_no_eria || '-' || TRIM(k.daicho_no_mm) || '-' || k.daicho_no_seq ) AS daicho_no -- 台帳番号
       ,k.kaiin_kbn        -- 会員区分
       ,cm3.kbn_value_lnm -- 会員区分名
       ,k.kaiin_sonota -- 会員区分（その他）
       ,k.uketuke_tanto_cd -- 受付担当者コード
       ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
       ,k.seko_tanto_cd    -- 施行担当者コード
       ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
       ,k.k_nm             -- 故人名
       ,k.k_knm             -- 故人名カナ
       ,k.kg_tel           -- 現住所TEL                    2016/02/05 Mihara add
       ,k.m_nm       -- 喪主
       ,k.m_knm       -- 喪主カナ
       ,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_yubin_no
                           ELSE k.kg_yubin_no END AS mg_yubin_no -- 喪主郵便番号
       ,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_addr1
                           ELSE k.kg_addr1 END AS mg_addr1   -- 喪主現住所1
       ,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_addr2
                           ELSE k.kg_addr2 END AS mg_addr2   -- 喪主現住所2
       ,CASE WHEN mg_kbn=0 OR mg_kbn IS NULL THEN k.mg_tel
                           ELSE k.kg_tel END AS mg_tel       -- 喪主TEL
       ,TO_CHAR(k.sougi_ymd, 'YYYY/MM/DD') as sougi_ymd  -- 葬儀日
       ,TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD') as nakijitu -- 亡日
       ,TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD HH24:MI') as nakijitu_ex -- 亡日(時分含)
       ,TO_CHAR(n3.nitei_ymd, 'YYYY/MM/DD') as nyukan -- 入棺
       ,TO_CHAR(n3.nitei_ymd, 'YYYY/MM/DD HH24:MI') as nyukan_ex -- 入棺(時分含)
       ,(SELECT to_char(nitei_ymd, 'YYYY/MM/DD') FROM seko_nitei_houji  WHERE seko_no = k.seko_no
         AND nitei_kbn=1 AND delete_flg=0 limit 1) as houji_ymd   -- 法事日, sougi_ymd に格納される
       --2015/02/02 DEL Kayo    ,n2.basho_cd -- 式場CD
       --2015/02/02 DEL Kayo    ,n2.basho_nm -- 式場名
       ,CASE 
           WHEN k.moushi_kbn = 2 THEN nh2.basho_cd 
           ELSE n2.basho_cd 
        END basho_cd -- 式場CD
       ,CASE 
           WHEN k.moushi_kbn = 2 THEN 
                CASE WHEN nh2.basho_kbn = 0 THEN '自宅' 
                ELSE nh2.basho_nm
                END
           ELSE 
                CASE WHEN n2.basho_kbn = 0 THEN '自宅' 
                ELSE n2.basho_nm
                END
        END basho_nm -- 式場名
       ,(SELECT juchu_prc_sum FROM juchu_denpyo  WHERE seko_no = k.seko_no
         AND data_kbn=1 AND delete_flg=0 limit 1) as juchu_prc_sum -- 受注金額合計
       ,(SELECT uri_prc_sum FROM uriage_denpyo  WHERE seko_no = k.seko_no
         AND data_kbn=1 AND delete_flg=0 limit 1) as uri_prc_sum -- 売上金額合計
       ,k.status_kbn -- ステータス
       ,CASE k.status_kbn WHEN 1 THEN '見積中'
                          WHEN 2 THEN '施行中'
                          WHEN 3 THEN '請求済'
                          WHEN 4 THEN '入金済'
                          WHEN 9 THEN '失注'
                          ELSE '-' END AS status_kbn_nm  -- ステータス表示名
       ,k.souke_nm         -- 葬家
       ,k.souke_knm         -- 葬家カナ
       ,k.keishiki_kbn     -- 葬儀形式
       ,k.biko1            -- メモ（出棺経路・納骨・壇払など）
       ,k.bumon_cd         -- 売上部門コード
       ,bm.bumon_lnm  AS bumon_nm        -- 売上部門名
       ,bm.bumon_lnm       -- 売上正式部門名       
       ,bm.bumon_snm       -- 売上簡略部門名
       ,k.order_finish_flg AS order_flg -- 発注完了フラグ  0:未発注 1:発注済み
       ,CASE k.order_finish_flg WHEN 0 THEN '未発注'
                                WHEN 1 THEN '発注済み'
                                ELSE '-' END AS order_flg_nm  -- 発注完了表示名
       ,ty_kaijyo_cd -- 通夜会場場所コード
       ,ty_kaijyo_nm -- 通夜会場場所名称
       ,k.link_exp_no
       ,k.link_exp_info
	   ,k.souke_tel 	-- 葬家電話番号		2016/03/21 ADD Kayo
	   ,k.kj_tel 		-- 住民登録電話番号	2016/03/21 ADD Kayo
	   ,k.kk_tel 		-- 勤務先TEL		2016/03/21 ADD Kayo
	   ,k.mg_m_tel		-- 携帯番号			2016/03/21 ADD Kayo
	   ,k.mj_tel		-- 住民登録電話番号 2016/03/21 ADD Kayo
	   ,k.mk_tel		-- 勤務先TEL		2016/03/21 ADD Kayo
	   ,sf.tel_no			-- 現住所電話番号	2016/03/21 ADD Kayo
	   ,sf.mobile_tel		-- 携帯番号			2016/03/21 ADD Kayo
	   ,sf.em_tel			-- 緊急先電話番号	2016/03/21 ADD Kayo
	   ,sf.em_mobile_tel	-- 緊急先携帯番号	2016/03/21 ADD Kayo
	   ,sf.resident_tel		-- 住民登録電話番号	2016/03/21 ADD Kayo
	   ,sf.office_tel		-- 勤務先TEL		2016/03/21 ADD Kayo
	   ,sf.tel_no1			-- 住所電話番号1	2016/03/21 ADD Kayo
	   ,sf.mobile_tel1		-- 携帯番号1		2016/03/21 ADD Kayo
	   ,sf.tel_no2			-- 住所電話番号2	2016/03/21 ADD Kayo
	   ,sf.mobile_tel2		-- 携帯番号2		2016/03/21 ADD Kayo
	   ,sf.tel_no3			-- 住所電話番号3	2016/03/21 ADD Kayo
	   ,sf.mobile_tel3		-- 携帯番号3		2016/03/21 ADD Kayo
	   ,sf.v_free5			-- 連絡者の携帯番号	2016/03/21 ADD Kayo
       ,k.kg_addr1              AS  kg_addr1_x    -- 故人現住所1    2016/06/14 ADD Kayo
       ,k.kg_addr2              AS  kg_addr2_x    -- 故人現住所2    2016/06/14 ADD Kayo
       ,k.mg_addr1              AS  mg_addr1_x    -- 喪主住所1     2016/06/14 ADD Kayo
       ,k.mg_addr2              AS  mg_addr2_x    -- 喪主住所2     2016/06/14 ADD Kayo
       ,k.kh_addr1              -- 故人本籍登録住所1	2016/06/13 ADD Kayo
       ,k.kh_addr2              -- 故人本籍登録住所2	2016/06/13 ADD Kayo
       ,k.mj_addr1              -- 喪主住民登録住所1	2016/06/13 ADD Kayo
       ,k.mj_addr2              -- 喪主住民登録住所2	2016/06/13 ADD Kayo
       ,k.mh_addr1              -- 喪主本籍住民登録住所12016/06/13 ADD Kayo	
       ,k.mh_addr2              -- 喪主本籍住民登録住所22016/06/13 ADD Kayo
       ,sf.addr1                -- 現住所1              2016/06/13 ADD Kayo
       ,sf.addr2                -- 現住所2              2016/06/13 ADD Kayo	
       ,sf.em_addr1             -- 緊急先住所1          2016/06/13 ADD Kayo
       ,sf.em_addr2             -- 緊急先住所2          2016/06/13 ADD Kayo
       ,sf.resident_addr1       -- 住民登録住所1        2016/06/13 ADD Kayo	
       ,sf.resident_addr2       -- 住民登録住所2        2016/06/13 ADD Kayo
       ,sf.family_addr1         -- 住民登録住所1        2016/06/13 ADD Kayo
       ,sf.family_addr2         -- 住民登録住所2        2016/06/13 ADD Kayo   
       ,sf.addr1_1              -- 住所1_1              2016/06/13 ADD Kayo
       ,sf.addr1_2              -- 住所1_2              2016/06/13 ADD Kayo	
       ,sf.addr2_1              -- 住所2_1              2016/06/13 ADD Kayo
       ,sf.addr2_2              -- 住所2_2              2016/06/13 ADD Kayo	
       ,sf.addr3_1              -- 住所3_1              2016/06/13 ADD Kayo	
       ,sf.addr3_2              -- 住所3_2              2016/06/13 ADD Kayo
  FROM seko_kihon_info k
  LEFT JOIN seko_kihon_all_free sf			-- 施行基本汎用フリー情報 2016/03/21 ADD Kayo
	  ON k.seko_no			= sf.seko_no
	  AND 0					= sf.seq_no		
	  AND 0					= sf.delete_flg
  LEFT JOIN tanto_mst t1 
	  ON k.uketuke_tanto_cd = t1.tanto_cd
	  AND t1.delete_flg = 0
  LEFT JOIN tanto_mst t2 
	  ON k.seko_tanto_cd    = t2.tanto_cd
	  AND t2.delete_flg = 0
  LEFT JOIN nm_jyusho_mst jm
	  ON k.kasoba_cd = jm.jyusho_cd
	  AND jm.jyusho_kbn = 3
	  AND jm.delete_flg = 0
  LEFT JOIN code_nm_mst cm3
	  ON cm3.kbn_value_cd = k.kaiin_cd
	  AND cm3.code_kbn='0030'
	  AND cm3.delete_flg=0
  LEFT JOIN seko_nitei n1
	  ON n1.seko_no=k.seko_no
	  AND n1.nitei_kbn=1
	  AND n1.delete_flg=0
  LEFT JOIN seko_nitei n2
	  ON n2.seko_no=k.seko_no
	  AND n2.nitei_kbn=7
	  AND n2.delete_flg=0
  LEFT JOIN seko_nitei n3
	  ON n3.seko_no=k.seko_no
	  AND n3.nitei_kbn=3
	  AND n3.delete_flg=0
  LEFT JOIN seko_nitei_houji nh2
	  ON nh2.seko_no=k.seko_no
	  AND nh2.nitei_kbn=1
	  AND nh2.delete_flg=0
  LEFT JOIN bumon_mst bm 
	  ON k.bumon_cd = bm.bumon_cd
	  AND bm.delete_flg=0
 WHERE k.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 施行情報を検索する(新盆用)
     *
     * <AUTHOR> Matsuyama
     * @since 2017/01/05
     * @version 2017/02/08 MSI Matsuyama 支払方法(新盆)、家名有無を追加
     * @param	Msi_Sys_Db $db
     * @param	array      $keyHash  条件
     * @return	array      該当データがない場合はarray()を返す
     */
    public static function findForNiibon($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
  SELECT
		 k.seko_no			    -- 施行番号
		,k.free4_cd			    -- 葬儀・法事施行
		,k.moushi_kbn			    -- 申込区分
		,k.daicho_no_eria		    -- 台帳番号（エリア）
		,TRIM(k.daicho_no_mm) AS daicho_no_mm   -- 台帳番号（月）
		,k.daicho_no_seq		    -- 台帳番号（連番）
		,(k.daicho_no_eria || '-' || TRIM(k.daicho_no_mm) || '-' || k.daicho_no_seq ) AS daicho_no -- 台帳番号
		,d.denpyo_no			    -- 受注伝票番号
		,u.uri_den_no			    -- 請求番号
		,d.sekyu_cd			    -- 請求先コード
		,u.uri_prc_sum			    -- 売上金額
		,u.uri_prc_sum - u.in_zei_prc  AS zeinuki_prc  -- 売上金額(税抜)
		,u.uri_prc_sum + u.out_zei_prc AS zeikomi_prc  -- 売上金額(税込)
		,d.tax_kbn			    -- 税区分
		,d.zei_cd			    -- 税率
		,k.bumon_cd			    -- 部門コード
		,k.uketuke_tanto_cd		    -- 受注担当者コード
		,k.tanto_cd1 AS hachu_tanto_cd	    -- 発注担当者コード
		,ut.tanto_nm AS uketuke_tanto_nm    -- 受注担当者名
		,ht.tanto_nm AS hachu_tanto_nm	    -- 発注担当者名
		,k.nb_nm			    -- お名前
		,k.nb_knm			    -- お名前カナ
		,k.nb_syawari_kbn		    -- 社割区分
		,CASE WHEN k.nb_syawari_kbn = 1 THEN 
		    'あり'
		 ELSE
		    'なし'
		 END AS syawari			    -- 社割区分値
		,k.nb_souke			    -- 葬家名
		,gm.gazo_img AS nb_kamon_oid	    -- 家紋oid
		,k.nb_kamon_cd			    -- 家紋コード
		,k.nb_kamon_nm			    -- 家紋名
		,k.nb_kamon_color		    -- 家紋入れ文字色区分
		,TO_CHAR(k.nb_kamon_nouki_ymd, 'YYYY/MM/DD') AS nb_kamon_nouki_ymd	-- 納期指定日(提灯発注用)
		,k.nb_map_oid			    -- 地図(OID)
		,k.nb_map_nm			    -- 地図(ファイル名)
		,k.nb_seisan_yotei_kbn		    -- ご精算予定日区分
		,TO_CHAR(k.nb_seisan_yotei_ymd, 'YYYY/MM/DD') AS nb_seisan_yotei_ymd  -- ご精算予定日時
		,TO_CHAR(k.nb_juchu_ymd, 'YYYY/MM/DD') AS nb_juchu_ymd		-- 新盆受注日
		,k.nb_addr			    -- 現住所
		,k.nb_tel			    -- 電話番号
		,k.nb_yubin_no			    -- 郵便番号
		,k.nb_nohin_addr		    -- 納品先住所
		,k.nb_nohin_tel			    -- 連絡先
		,k.nb_nohin_yubin_no		    -- 納品先郵便番号
		,TO_CHAR(k.nb_kazari_ymd, 'YYYY/MM/DD')	AS nb_kazari_ymd        -- お飾り希望日
		,COALESCE(k.nb_kazari_time, '')	AS nb_kazari_time		-- お飾り希望時間
		,TO_CHAR(k.nb_kataduke_ymd, 'YYYY/MM/DD') AS nb_kataduke_ymd	-- お片付け希望日
		,COALESCE(k.nb_kataduke_time, '') AS nb_kataduke_time		-- お片付け希望時間
		,k.nb_biko			    -- 備考
		,k.nb_pay_method_cd		    -- 支払方法(新盆)
		,k.nb_souke_kbn			    -- 家名有無
		,TO_CHAR(u.kaishu_ymd, 'YYYY/MM/DD') AS kaishu_ymd		-- 回収予定日
		,'登録：' || ctt.tanto_nm || '　(' || TO_CHAR(d._cre_ts, 'YYYY/MM/DD HH24:MI:SS') || ')' AS  cre_user
		,CASE WHEN d._cre_ts = d._mod_ts THEN
		     NULL
		 ELSE        
		    '変更：' || utt.tanto_nm || '　(' || TO_CHAR(d._mod_ts, 'YYYY/MM/DD HH24:MI:SS') || ')'
		 END                         AS  mod_user
		,'請求：' || skt.tanto_nm || '　(' || TO_CHAR(u.seikyu_print_date, 'YYYY/MM/DD HH24:MI:SS') || ')' AS  seikyu_user
  FROM seko_kihon_info k
  LEFT JOIN juchu_denpyo d 	-- 新盆受注伝票
    ON  k.seko_no = d.seko_no
    AND d.data_kbn = 8
    AND d.delete_flg = 0
  LEFT JOIN uriage_denpyo u
    ON  d.denpyo_no  = u.denpyo_no
    AND u.data_kbn = 8
    AND u.delete_flg = 0
  LEFT JOIN tanto_mst ut
    ON  k.uketuke_tanto_cd = ut.tanto_cd
    AND ut.delete_flg = 0
  LEFT JOIN tanto_mst ht
    ON  k.tanto_cd1 = ht.tanto_cd
    AND ht.delete_flg = 0
  LEFT JOIN gazo_file_info_mst gm
    ON  k.nb_kamon_cd = gm.gazo_cd
    AND gm.gazo_kbn   = 1		
    AND gm.delete_flg = 0
  LEFT JOIN login_mst cltm
    ON  CASE WHEN length(d._cre_user)-10 > 0 THEN
	    SUBSTR(d._cre_user,10, length(d._cre_user)-9 )
	ELSE
            null
        END         =  cltm.login_cd
    AND 0           =  cltm.delete_flg          
  LEFT JOIN tanto_mst ctt
    ON   cltm.tanto_cd  = ctt.tanto_cd
    AND  0              = ctt.delete_flg
  LEFT JOIN login_mst ultm
    ON  CASE WHEN length(d._mod_user)-10 > 0 THEN
	    SUBSTR(d._mod_user,10, length(d._mod_user)-9 )
        ELSE
            null
        END        =  ultm.login_cd
    AND 0	   =  ultm.delete_flg          
  LEFT JOIN tanto_mst utt
    ON   ultm.tanto_cd  = utt.tanto_cd
    AND  0              = utt.delete_flg
  LEFT JOIN tanto_mst skt
    ON   u.seikyu_print_tanto_cd = skt.tanto_cd
    AND  0                       = skt.delete_flg
 WHERE k.delete_flg = 0 AND k.nb_juchu_ymd IS NOT NULL
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

}
