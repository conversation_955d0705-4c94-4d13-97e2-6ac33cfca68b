<?php
/**
 * Logic_FileTrans_FileTransGenPushFiles
 *
 * ファイル転送生成ファイル(file_trans_gen_file)へファイルを登録する
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/04/07
 * @filesource 
 */

/**
 * ファイル転送生成ファイル(file_trans_gen_file)へファイルを登録する
 * 
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/04/07
 */
class Logic_FileTrans_FileTransGenPushFiles
{
    /**
     * コンストラクタ
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @param      Msi_Sys_Db $db
     * @param      string $ft_type    転送種別    ex. shidashi/denshi/kaikei
     * @param      string $exe_mode   実行モード  ex. urgent/batch
     * @param      array  $param      他パラメタ  とくに ext_cond
     */
    public function __construct( $db, $ft_type, $exe_mode, $param=array() )
    {
        $this->_db  = $db;
        $this->_ft_type  = $ft_type;
        $this->_exe_mode = $exe_mode;
        $this->_param    = $param;
        list($this->_cur_ts, $this->_cur_user) = Logic_FileTrans_Utils::getTsAndUser($db);
        $this->_aFiles = array(); // 未登録ファイル
        $this->_allFiles = array(); // 登録した全ファイル
    }

    /**
     * ファイル転送生成ファイル 登録予約処理
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @param      string  $file_type     ファイルタイプ(論理ファイル名)
     * @param      string  $tmpFilename   登録内容が格納されているファイル
     * @param      boolean $isHeaderLine  ファイル１行目がヘッダなら真
     * @param      boolean $isConvSjis    SJIS-win に変換するなら真
     * @return     void
     */
    public function pushFile( $file_type, $tmpFilename, $isHeaderLine=true, $isConvSjis=false )
    {
        $db = $this->_db;
        $ft_type  = $this->_ft_type;
        $exe_mode = $this->_exe_mode;
        $param    = $this->_param;

        $buf = Msi_Sys_Utils::get_contents($tmpFilename);
        if ( $isConvSjis ) {
            $buf = mb_convert_encoding( $buf, 'SJIS-win' );
        }

        $tmpFile = Msi_Sys_Utils::tempnam();
        $isOk = Msi_Sys_Utils::put_contents($tmpFile, $buf);
        if ( !$isOk ) {
            throw new Msi_Sys_Exception_LogicException("(236db4db)ファイル出力に失敗しました");
        }

        $aFileInfo = array( 'genFileName'    => $tmpFile,
                            'ft_type'        => $ft_type,
                            'file_type'      => $file_type,
                            'exe_mode'       => $exe_mode,
                            'result'         => 'ok',
                            'is_header_flg'  => $isHeaderLine ? 1 : 0,
                            'output_cnt'     => Logic_FileTrans_Utils::getLineCnt($buf) );

        $aFileInfo['ext_cond'] = Logic_FileTrans_Utils::getExtCondJsonStr($param);

        $this->_aFiles[] = $aFileInfo;
    }

    /**
     * ファイル転送生成ファイル 登録予約処理
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @param      string  $file_type     ファイルタイプ(論理ファイル名)
     * @param      string  $buf           ファイル内容
     * @param      boolean $isHeaderLine  ファイル１行目がヘッダなら真
     * @param      boolean $isConvSjis    SJIS-win に変換するなら真
     * @return     void
     */
    public function pushBuf( $file_type, $buf, $isHeaderLine=true, $isConvSjis=false )
    {
        $tmpFilename = Msi_Sys_Utils::tempnam();
        $isOk = Msi_Sys_Utils::put_contents($tmpFilename, $buf);
        if ( !$isOk ) {
            throw new Msi_Sys_Exception_LogicException("(beb1e840)ファイル出力に失敗しました");
        }

        $this->pushFile( $file_type, $tmpFilename, $isHeaderLine, $isConvSjis );
    }

    /**
     * ファイル転送生成ファイル 登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @param      string  $file_type     ファイルタイプ(論理ファイル名)
     * @param      string  $tmpFilename   登録内容が格納されているファイル
     * @param      boolean $isHeaderLine  ファイル１行目がヘッダなら真
     * @param      boolean $isConvSjis    SJIS-win に変換するなら真
     * @return     integer|array|null     生成ファイル番号
     */
    public function genFileWithFile( $file_type, $tmpFilename, $isHeaderLine=true, $isConvSjis=false )
    {
        $this->pushFile( $file_type, $tmpFilename, $isHeaderLine, $isConvSjis );

        $aFileIds = $this->genFileFlush();
        return $aFileIds;
    }

    /**
     * ファイル転送生成ファイル 登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/06/03
     * @param      string  $file_type     ファイルタイプ(論理ファイル名)
     * @param      string  $buf           ファイル内容
     * @param      boolean $isHeaderLine  ファイル１行目がヘッダなら真
     * @param      boolean $isConvSjis    SJIS-win に変換するなら真
     * @return     integer|array|null     生成ファイル番号
     */
    public function genFileWithBuf( $file_type, $buf, $isHeaderLine=true, $isConvSjis=false )
    {
        $this->pushBuf( $file_type, $buf, $isHeaderLine, $isConvSjis );

        $aFileIds = $this->genFileFlush();
        return $aFileIds;
    }

    /**
     * $this->_aFiles で予約したファイルをファイル転送生成ファイル へ書き出す
     *
     * <AUTHOR> Mihara
     * @since      2025/06/03
     * @return     integer|array|null      生成ファイル番号
     */
    public function genFileFlush()
    {
        $len = count($this->_aFiles);
        if ( $len <= 0 ) {
            return null;
        }

        $db = $this->_db;
        $aFileIds = Logic_FileTrans_FileTransGenFile::genFile( $db, $this->_aFiles ); // scalar があり得る

        $this->_allFiles = array_merge( $this->_allFiles, Msi_Sys_Utils::arrayify($aFileIds) );
        $this->_aFiles = array();

        return $aFileIds;
    }

    /**
     * ファイル転送生成ファイル へ書き出す
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @return     array         生成ファイル番号
     */
    public function finish()
    {
        $aFileIds = $this->genFileFlush();
        return $this->_allFiles;
    }

}
