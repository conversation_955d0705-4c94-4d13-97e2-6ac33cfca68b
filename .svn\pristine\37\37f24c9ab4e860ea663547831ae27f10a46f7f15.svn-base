    
<div id = "infomember-tab" class = "tab-contents off">
    <fieldset class="member_group">
        <span id="member_group_set" class="radio_set">
            <label for="member_group_1" class="lbl_print lbl_member_group_1">加入状況</label><input name="member-grp" id="member_group_1" type="radio" value="0"  />
            <label for="member_group_2" class="lbl_print lbl_member_group_2">その他加入確認</label><input class="lbl_member_group_2" name="member-grp" id="member_group_2" type="radio" value="1" />
        </span>
{*        <input type="button" name="btn_gojokai_search" id="btn_gojokai_search" value="コース検索">*}
        <label class="lbl_name gojo_kijun_ymd">試算基準日</label>
        <input name="sougi_ymd" id="sougi_ymd" type="text" class="txt ime-off date_auto_slash to_alpha_num gojo_kijun_ymd" value="" maxlength = "10"/>
        <div class="label dlg_date gojo_kijun_ymd"></div>
    </fieldset>
    <div id="member_1" class="member_area field_end">
        {*        <h3>加入状況</h3>*}
        <div id="member_header" class="option done">
            <table>
                <tr>
                    <td class="lbl_kbn" rowspan="1">区分</td>
                    <td class="lbl_course" rowspan="1">互助会コース</td>
                    <td class="lbl_kanyu_nm" rowspan="1">加入者名</td>
                    <td class="lbl_keiyaku" rowspan="1">契約金額</td>
                    <td class="lbl_harai" rowspan="1">掛込金額</td>
                    <td class="lbl_zan" rowspan="1">掛込残額</td>
                    <td class="lbl_ekimu2" rowspan="1">提供役務2</td>
                    <td class="lbl_wari1" rowspan="1">割引券1</td>
                    <td class="lbl_wari2" rowspan="1">割引券2</td>
                    <td class="lbl_wari3" rowspan="1">割引券3</td>
                    <td rowspan="2">会費消費税率</td>
{*                    <td class="lbl_genkyo" rowspan="1">ステータス</td>*}
                    <td class="lbl_botan1" rowspan="1">募集担当1</td>
                    <td class="lbl_delete_btn" rowspan="2">削除</td>
                </tr>
                <tr>
                    <td rowspan="1">紛失区分</td>
                    <td rowspan="1">加入者番号</td>
                    <td rowspan="1">加入年月日</td>
                    <td class="lbl_yoto" rowspan="1">用途</td>
                    <td rowspan="1">掛込回数</td>
                    <td rowspan="1">会費消費税</td>
                    <td rowspan="1">名変手数料</td>
                    <td rowspan="1">割引額</td>
                    <td rowspan="1">割引額</td>
                    <td rowspan="1">割引額</td>
                    <td rowspan="1">募集担当2</td>
                </tr>
            </table>
        </div>
        <div id="member_dtl_div">
            <table id="member_msi"></table>
        </div>
 {*       <div id="use_cose_header">
            <fieldset class="base_1">
                <div class="lbl_use_cose subtitle option">ご利用コース</div>
                <input type="hidden" class="cls_use_cose" id="use_cose"/>
            </fieldset>
            <fieldset class="member_info">
                <div class="lbl_use_cose subtitle option">金額</div>
                <input name="use_keiyaku_gaku" id="use_keiyaku_gaku" type="text" class="txt" value="" readonly="readonly"/>
            </fieldset>
        </div>
        <div id="experienced_header">
            <fieldset class="base_1">
                <span id="history_set" class="radio_set">
                    <label for="inexperienced" class="lbl_inexperienced ">なし</label><input name="history" id="inexperienced" type="radio" value="2" checked="checked" />
                    <label for="experienced" class="lbl_experienced ">あり</label><input name="history" id="experienced" type="radio" value="1" />
                </span>
                <div class="lbl_history subtitle option">施行履歴</div>
            </fieldset>
            <fieldset class="base_1 exp_consultation">
                <span id="consultation_set" class="radio_set">
                    <label for="unadviced" class="lbl_unadviced">なし</label><input name="consultation" id="unadviced" type="radio" value="2" checked="checked"/>
                    <label for="adviced" class="lbl_adviced">あり</label><input name="consultation" id="adviced" type="radio" value="1"  />
                </span>
                <div class="lbl_consultation subtitle option">事前相談</div>
            </fieldset>
        </div>*}
    </div>
    <div id="member_2" class="member_area field_end">
        {*        <h3>その他加入確認</h3>*}
        <fieldset class="base_1">
            <label>契約先番号</label>
            <input name="keiyaku_no" id="partner_cd" type="text" class="txt" value="" readonly="readonly"/>
            <div class="label dlg_keiyaku_no cursor-pointer"></div>
        </fieldset>
        <fieldset>
            <label>契約先名</label>
            <input name="keiyaku_nm" id="partner_nm" type="text" class="txt" value="" readonly="readonly"/>
        </fieldset>
        <h3>利用者</h3>
        <fieldset class="base_1">
            <label>利用者区分</label>
            <input type="hidden" name="user_kbn" id="user_kbn" class="cls_user_kbn"/>
            <input name="user_kbn_nm" id="user_kbn_nm" type="text" class="txt" value="" maxlength="20"/>
        </fieldset>
        <fieldset class="base_1">
            <label>利用者名(漢字)</label>
            <input name="user_last_nm" id="user_last_nm" type="text" class="txt" value="" maxlength="12"/>
            <input name="user_first_nm" id="user_first_nm" type="text" class="txt" value="" maxlength="12"/>
        </fieldset>
        <fieldset>
            <label>利用者名(カナ)</label>
            <input name="user_last_knm" id="user_last_knm" type="text" class="txt" value="" maxlength="12"/>
            <input name="user_first_knm" id="user_first_knm" type="text" class="txt" value="" maxlength="12"/>
        </fieldset>
        <fieldset class="base_1">
            <label>勤務先</label>
            <input name="user_co_nm" id="user_kinmusaki_nm" type="text" class="txt" value="" maxlength="30"/>
        </fieldset>
        <fieldset>
            <label>所属部署</label>
            <input name="user_dep_nm" id="user_department_nm" type="text" class="txt" value="" maxlength="30"/>
        </fieldset>
        <fieldset>
            <label for="birthday_date" class="lbl_birthday">利用者生年月日</label>
            <input type="hidden" name="birthday_era" id="birthday_era" class="cls_birthday_era"/>
            <input type="hidden" name="birthday_month" id="birthday_month" class="cls_birthday_month"/>
            <input type="hidden" name="birthday_day" id="birthday_day" class="cls_birthday_day"/>
        </fieldset>
        <fieldset>
            <label>(国家公務員省庁名)</label>
            <input type="hidden" name="kyosai_kbn" id="ministries_cd" class="cls_ministries_cd"/>
        </fieldset>
    </div>
</div>
{literal}
    <!-- 互助会確認 加入状況テンプレート -->
    <script type="text/template" id="tmpl-kanyu">
        <!-- tbody -->
        <tr>
        <td class="lbl_kbn" rowspan="1">
        <input type="hidden" class="i_kaiin_info"/>
        </td>
        <td class="lbl_course" rowspan="1">
        <input type="hidden" class="i_cose"/>
        <input type="text" class="txt i_other_cose_nm" value="" maxlength = "60"/>
        </td>
        <td class="lbl_kanyu_nm" rowspan="1">
        <input type="text" class="txt i_member_name" value=""  maxlength = "30"/>
        </td>
        <td class="lbl_keiyaku" rowspan="1">
        <input type="text" class="txt i_deposit to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this))"/>
        </td>
        <td class="lbl_harai" rowspan="1">
        <input type="text" class="txt i_pay to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this))"/>
        </td>
        <td class="lbl_zan" rowspan="1">
        <input type="text" class="txt i_zan_prc to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this))" disabled="disabled"/>
        </td>
        <td class="lbl_ekimu2" rowspan="1">
        <input type="text" class="txt i_ekimu2 to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this))" disabled="disabled"/>
        </td>
        <td class="lbl_wari1" rowspan="1">
        <input type="hidden" class="i_wariken1"/>
        </td>
        <td class="lbl_wari2" rowspan="1">
        <input type="hidden" class="i_wariken2"/>
        </td>
        <td class="lbl_wari3" rowspan="1">
        <input type="hidden" class="i_wariken3"/>
        </td>
        <td rowspan="2">
        <input type="hidden" class="i_zei_cd"/>
        </td>
        <td class="lbl_botan1" rowspan="1">
        <input type="hidden" class="i_botan1"/>
        </td>
        <td class="lbl_delete_btn" rowspan="2">
        <span id="i_member_set" class="radio_set">
        <input name="i_member_delete_<%=idx%>" id="i_member_delete_<%=idx%>" class="i_member_delete" type="checkbox" value="1" /><label for="i_member_delete_<%=idx%>" class="lbl_i_member_delete"></label>
        </span>
        </td>
        </tr>
        <tr>
        <td class="lbl_kbn" rowspan="1">
        <input type="hidden" class="i_hunsitu"/>
        </td>
        <td rowspan="1">
        <input type="text" class="txt i_member_id" value=""  maxlength = "40"/>
        </td>
        <td rowspan="1">
        <input type="text" class="txt i_entry date_auto_slash to_alpha_num" value="" maxlength = "10"/>
        <div class="label dlg_date"></div>
        <div class="label dlg_dummy_date"></div>
        </td>
        <td class="lbl_yoto" rowspan="1">
        <input type="hidden" class="i_usage"/>
        </td>
        <td rowspan="1">
        <input type="text" class="txt i_times to_alpha_num" value="" maxlength = "3"/>
        </td>
        <td rowspan="1">
        <input type="text" class="txt i_kanyu_tax to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this))" disabled="disabled"/>
        </td>
        <td rowspan="1">
        <input type="text" class="txt i_mg_chg_cost to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this))"/>
        </td>
        <td rowspan="1">
        <input type="text" class="txt i_wari1 to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this))"/>
        </td>
        <td rowspan="1">
        <input type="text" class="txt i_wari2 to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this))"/>
        </td>
        <td rowspan="1">
        <input type="text" class="txt i_wari3 to_alpha_num" value="" maxlength = "9" onblur="$.msiJqlib.commaFilterTemp($(this))"/>
        </td>
        <td rowspan="1">
        <input type="hidden" class="i_botan2"/>
        </td>
        </tr>
        <!-- /tbody -->
    </script>
{/literal}