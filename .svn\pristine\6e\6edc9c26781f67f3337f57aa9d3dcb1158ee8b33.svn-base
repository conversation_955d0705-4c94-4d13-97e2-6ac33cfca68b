<?php

/**
 * Saiken_RyoshuController
 *
 * 領収書発行 コントローラクラス
 *
 * @category   App
 * @package    controllers\Saiken
 * <AUTHOR> Mihara
 * @since      2015/06/xx
 * @version    2019/05/15  mihara 軽減税率対応
 * @filesource 
 */

/**
 * 領収書発行 コントローラクラス
 *
 * @category   App
 * @package    controllers\Saiken
 * <AUTHOR> Mihara
 * @since      2015/06/xx
 */
class Saiken_RyoshuController extends Msi_Zend_Controller_Action {
    /**
     * index アクション
     *
     * <AUTHOR> Mihara
     * @since  2015/06/xx
     */
    public function indexAction() {
        $this->_forward('order');
    }
    /**
     * orderアクション
     *
     * <AUTHOR> Mihara
     * @since  2015/06/xx
     */
    public function orderAction() {
        // 権限を再度(?)チェック
        //if (!App_Utils::hasRyoshuHakkoRole()) {
        //    throw new Exception('権限がありません');
        //}
        if (!App_Utils::ryoshushoKbn()) {
            throw new Exception('領収書発行区分が「なし」に設定されています');
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $req = $this->getRequest();
        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }
        if (isset($this->_params['readonly']) && $this->_params['readonly']) {
            $this->_readonly = true;
        } else {
            $this->_readonly = Msi_Sys_Utils::isReadOnlyCtxt();
        }
        $seikyu_den_no = null;
        $uri_den_no = null;
        $juchu_den_no = null;
        if(isset($params['sdn'])){
            $seikyu_den_no = Msi_Sys_Utils::checkVarOrDefault($params['sdn'], 'DIGIT', null);
        }else if(isset ($params['udn'])){
            $uri_den_no    = Msi_Sys_Utils::checkVarOrDefault($params['udn'], 'DIGIT', null);
        }else if(isset ($params['jdn'])){
            $juchu_den_no  = Msi_Sys_Utils::checkVarOrDefault($params['jdn'], 'DIGIT', null);
        }
        // 伝票番号チェック
        if(!isset($seikyu_den_no) && !isset($uri_den_no) && !isset($juchu_den_no)){
            throw new Exception(sprintf("伝票Noが指定されていません"));
        }
        // 請求情報取得
        if(isset($juchu_den_no)){
            $this->doUchikin($db, $req, $uri_den_no, $juchu_den_no);
            return ;
        }else if(isset($uri_den_no)){
            $this->doUchikin($db, $req, $uri_den_no, $juchu_den_no);
            return ;
        }
        $seikyuRec = $this->_getSeikyuRec($seikyu_den_no);
        if ($seikyuRec === null) {
            throw new Exception(sprintf("請求伝票No(%d)の請求データが存在しません", $seikyu_den_no));
        }
        $this->_seikyuRec = $seikyuRec;
        $moushi_kbn    = $seikyuRec['moushi_kbn'];

        // Ajax
        if ($req->isPost()) {
            try {
                $action = $req->getPost('action');
                $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
                $dataCol = Msi_Sys_Utils::json_decode($req->getPost('dataColJson'));
                if ($action == '保存') {
                    $seikyu_den_no_2 = $dataApp['seikyu_den_no'];
                    if ((string) $seikyu_den_no_2 !== (string) $seikyu_den_no) {
                        throw new Exception(sprintf("伝票No(%s<=>%s)が異なります!", $seikyu_den_no, $seikyu_den_no_2));
                    }
                    $col = $dataCol[count($dataCol) - 1];
                    if (!$col || !isset($col['hako_kbn'])) {
                        throw new Exception('入力データが不正です');
                    }
                    $this->_add($col);
                    $msg = '新規発行しました';
                    if($col['hako_kbn'] == '1'){
                        $msg = '再発行しました';
                        // 発行状態更新
                        self::upHakoInfo($db, $col['ryosyu_no'], $col['ryosyu_eda_no'] - 1, 9, "再発行による破棄：");
                    }
                    $seikyuRec = $this->_getSeikyuRec($seikyu_den_no);
                    $this->_seikyuRec = $seikyuRec;
                    $newDataApp = $this->_dataApp();
                    $newDataCol = $this->_dataCol();
                    if($col['hako_kbn'] == '1'){
                        $ryoshuRec = DataMapper_RyosyushoHistory::findOne($db,array(  
                                        'uri_den_no'    => $seikyuRec['seikyu_den_no'], 
                                        '__raw_1'       => 'hako_kbn IN (9)', // 破棄
                                        '__etc_orderby' => array('hako_count DESC')
                                    )
                                );
                        $outputdata = array();
                        $outputdata['seikyu_den_no'] = $seikyu_den_no;
                        $outputdata['ryosyusho_no']  = $ryoshuRec['ryosyusho_no'];
                        $outputdata['seko_no']       = $ryoshuRec['seko_no'];
                        $outputdata['hako_ymd']      = $ryoshuRec['hako_ymdhis'];
                        $outputdata['hako_count']    = $ryoshuRec['hako_count'];
                        $outputdata['bumon_cd']      = $seikyuRec['bumon_cd'];
                        list($temp_file, $filename)  = Saiken_RyoshuPdf::outputPdfSeikyu($db, $outputdata);
                        // 電子帳票システム連携登録
                        //$buf = Msi_Sys_Utils::get_contents($temp_file);
                        //$optData['ryosyu_no'] = $ryoshuRec['ryosyusho_no'];
                        //$optData['eda_no']    = $ryoshuRec['ryosyusho_eda_no'];
                        //$dir_id  = $seikyuRec['seko_no'];
                        //$dir_kbn = 0;
                        //if($seikyuRec['moushi_kbn'] == 3 && isset($seikyuRec['ref_seko_no'])){
                        //    // アフター(関連施行有り）
                        //    $dir_id = $seikyuRec['ref_seko_no'];
                        //}else if($seikyuRec['moushi_kbn'] == 3){
                        //    // アフター(関連施行無し）
                        //    $dir_id = $seikyu_den_no;
                        //    $dir_kbn = 1;
                        //}
                        //Logic_RpSystemRenkei::upsert($db, $buf, $moushi_kbn, $dir_id, $dir_kbn, 40, $ryoshuRec['hako_date_disp2'], 1, $optData);
                    }
                    $db->commit();
                    $data = array(
                        'dataApp' => $newDataApp,
                        'dataCol' => $newDataCol,
                        'status' => 'OK',
                        'msg' => $msg,
                    );
                    // 軽減税率対応 調整 keigen
                    $this->_keigenAdj($data);
                    $data['dlKey'] = array('seikyu_den_no' => $newDataApp['seikyu_den_no'],'hako_count' => $this->_hako_count_saved);
                    Msi_Sys_Utils::info(sprintf('領収書を%s(seikyu_den_no:%s, hako_count:%s)', $msg, $newDataApp['seikyu_den_no'], isset($this->_hako_count_saved) ? $this->_hako_count_saved : 'X' ));
                    Msi_Sys_Utils::outJson($data);
                } else if ($action == '破棄') {
                    $cancelData = array();
                    foreach ($dataCol as $col) {
                        // チェックされたレコード
                        if($col['ryoshu_check'] == '1'){
                            $cancelData[] = $col;
                        }
                    }
                    // 領収証破棄
                    $this->_cancelLast($db, $cancelData);
                    $msg = '破棄しました';
                    $seikyuRec = $this->_getSeikyuRec($seikyu_den_no);
                    $this->_seikyuRec = $seikyuRec;
                    $newDataApp = $this->_dataApp();
                    $newDataCol = $this->_dataCol();
                    $ryoshuRec = DataMapper_RyosyushoHistory::findOne($db,array(  
                                    'uri_den_no'    => $seikyuRec['seikyu_den_no'], 
                                    '__raw_1'       => 'hako_kbn IN (9)', // 破棄
                                    '__etc_orderby' => array('hako_count DESC')
                                )
                            );
                    //$outputdata = array();
                    //$outputdata['seikyu_den_no'] = $seikyu_den_no;
                    //$outputdata['ryosyusho_no']  = $ryoshuRec['ryosyusho_no'];
                    //$outputdata['seko_no']       = $ryoshuRec['seko_no'];
                    //$outputdata['hako_ymd']      = $ryoshuRec['hako_ymdhis'];
                    //$outputdata['hako_count']    = $ryoshuRec['hako_count'];
                    //$outputdata['bumon_cd']      = $seikyuRec['bumon_cd'];
                    //list($temp_file, $filename)  = Saiken_RyoshuPdf::outputPdfSeikyu($db, $outputdata);
                    // 電子帳票システム連携登録
                    //$buf = Msi_Sys_Utils::get_contents($temp_file);
                    //$optData['ryosyu_no'] = $ryoshuRec['ryosyusho_no'];
                    //$optData['eda_no']    = $ryoshuRec['ryosyusho_eda_no'];
                    //$dir_id  = $seikyuRec['seko_no'];
                    //$dir_kbn = 0;
                    //if($seikyuRec['moushi_kbn'] == 3 && isset($seikyuRec['ref_seko_no'])){
                    //    // アフター(関連施行有り）
                    //    $dir_id = $seikyuRec['ref_seko_no'];
                    //}else if($seikyuRec['moushi_kbn'] == 3){
                    //    // アフター(関連施行無し）
                    //    $dir_id = $seikyu_den_no;
                    //    $dir_kbn = 1;
                    //}
                    //Logic_RpSystemRenkei::upsert($db, null, $moushi_kbn, $dir_id, $dir_kbn, 40, $ryoshuRec['hako_date_disp2'], 1, $optData);
                    $db->commit();
                    $data = array(
                        'dataApp' => $newDataApp,
                        'dataCol' => $newDataCol,
                        'status' => 'OK',
                        'msg' => $msg,
                    );
                    // 軽減税率対応 調整 keigen
                    $this->_keigenAdj($data);
                    Msi_Sys_Utils::info(sprintf('領収書を%s(seikyu_den_no:%s, hako_count:%s)', $msg, $newDataApp['seikyu_den_no'], isset($this->_hako_count_saved) ? $this->_hako_count_saved : 'X' ));
                    Msi_Sys_Utils::outJson($data);
                } else if ($action == '再発行') {
                    $msg = "コピーしました。";
                    $reissueData = array();
                    $cnt = count($dataCol);
                    foreach ($dataCol as $key => $col) {
                        // チェックされたレコード
                        if($col['ryoshu_check'] == '1'){
                            $reissueData[] = $col;
                            $dataCol[$key]['reissue_flg'] = false;
                            $dataCol[$cnt-1] = $this->_dataCol2($reissueData);
                        }
                    }
                    $data = array(
                        'dataApp' => $dataApp,
                        'dataCol' => $dataCol,
                        'status'  => 'OK',
                        'msg'     => $msg,
                    );
                    // 軽減税率対応 調整 keigen
                    $this->_keigenAdj($data);
                    Msi_Sys_Utils::outJson($data);
                    return;
                } else if ($action == 'PDF') {
                    $seikyu_den_no_2 = $req->getPost('seikyu_den_no');
                    if ((string) $seikyu_den_no_2 !== (string) $seikyu_den_no) {
                        throw new Exception(sprintf("伝票No(%s<=>%s)が異なります!", $seikyu_den_no, $seikyu_den_no_2));
                    }
                    $hako_count = $req->getPost('hako_count');
                    // ryosyusho_pdf チェック
                    $ryoshuRec = DataMapper_RyosyushoHistory::findOne($db, array('uri_den_no' => $seikyu_den_no, 'hako_count' => $hako_count));
                    if ($ryoshuRec === null) {
                        throw new Exception(sprintf('請求データが存在しません(udn:%s,cnt:%s)', $seikyu_den_no, $hako_count));
                    }
                    if ($ryoshuRec['ryosyusho_pdf']) {
                        throw new Exception('領収書は発行済みです');
                    }
                    $outputdata = array();
                    $outputdata['seikyu_den_no'] = $seikyu_den_no;
                    $outputdata['ryosyusho_no']  = $ryoshuRec['ryosyusho_no'];
                    $outputdata['seko_no']       = $ryoshuRec['seko_no'];
                    $outputdata['hako_ymd']      = $ryoshuRec['hako_ymdhis'];
                    $outputdata['hako_count']    = $ryoshuRec['hako_count'];
                    $outputdata['bumon_cd']      = $seikyuRec['bumon_cd'];
                    list($temp_file, $filename)  = Saiken_RyoshuPdf::outputPdfSeikyu($db, $outputdata);
                    $buf = Msi_Sys_Utils::get_contents($temp_file);
                    // 電子帳票システム連携登録
                    //$optData['ryosyu_no'] = $ryoshuRec['ryosyusho_no'];
                    //$optData['eda_no']    = $ryoshuRec['ryosyusho_eda_no'];
                    //$dir_id  = $seikyuRec['seko_no'];
                    //$dir_kbn = 0;
                    //if($seikyuRec['moushi_kbn'] == 3 && isset($seikyuRec['ref_seko_no'])){
                    //    // アフター(関連施行有り）
                    //    $dir_id = $seikyuRec['ref_seko_no'];
                    //}else if($seikyuRec['moushi_kbn'] == 3){
                    //    // アフター(関連施行無し）
                    //    $dir_id = $seikyu_den_no;
                    //    $dir_kbn = 1;
                    //}
                    //Logic_RpSystemRenkei::upsert($db, $buf, $moushi_kbn, $dir_id, $dir_kbn, 40, $ryoshuRec['hako_date_disp2'], null, $optData);
                    // ryosyusho_pdf へ保存
                    $temp_file_2 = Saiken_RyoshuPdf::pdfReissue($temp_file); // バックアップ用
                    $oid2 = $db->writeBlob($temp_file_2);
                    $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE ryosyusho_history SET ryosyusho_pdf=:ryosyusho_pdf
 WHERE uri_den_no=:seikyu_den_no
   AND hako_count=:hako_count
   AND delete_flg=0
END_OF_SQL
                        , array(
                            'seikyu_den_no' => $seikyu_den_no,
                            'hako_count'    => $ryoshuRec['hako_count'],
                            'ryosyusho_pdf' => $oid2
                        ));
                    $db->commit();
                    Msi_Sys_Utils::info(sprintf('領収書をPDF出力しました(seikyu_den_no:%s, hako_count:%s)', $seikyu_den_no, $ryoshuRec['hako_count']));
                    Msi_Sys_Utils::out2way1($buf, $filename);
                }
            } catch (Exception $e) {
                $err = $e->getMessage();
                Msi_Sys_Utils::err('Saiken_RyoshuController: ' . $err);
                $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
                $errData = array(
                    'status' => 'NG',
                    'msg' => $userMsg,
                );
                Msi_Sys_Utils::outJson($errData);
            }
            return;
        }

        // 初期値
        if (!isset($dataApp)) {
            $dataApp = $this->_dataApp();
            $dataCol = $this->_dataCol();
        }

        // 伝票区分 x005   0420
        // @ $hako_kbn = $dataCol['hako_kbn'];
        // $this->view->hako_kbn = $hako_kbn;
        // 参照専用
        if ($this->_readonly) {
            $this->view->ctxt_readonly = 'my-ctxt-readonly';
        }
        // 初期設定データ
        $data = array(
            'dataApp' => $dataApp,
            'dataCol' => $dataCol,
        );
        // 軽減税率対応 調整 keigen
        $this->_keigenAdj($data);
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
        App_Smarty::pushCssFile(['app/saiken.ryoshu.css']);
        App_Smarty::pushJsFile(['app/saiken.ryoshu.js']);
    }
    /**
     * 
     * 内金領収証の発行金額を加算
     * 
     * @param type $uchiwake
     */
    private function setRyosyuUcKingaku($db, &$uchiwake){
        if(Msi_Sys_Utils::myCount($uchiwake) == 0){
            return;
        }
        $_uchiwakeData = $uchiwake;
        $ryosyuInfo = $this->getRyosyuUcInfo($db, $this->_seikyuRec['uri_den_no_2']);
        if(Msi_Sys_Utils::myCount($ryosyuInfo) == 0){
            return;
        }
        foreach ($_uchiwakeData as $key => $value) {
            $taisho_zan       = $value['taisho_zan'];
            $reduced_tax_rate = $value['reduced_tax_rate'];
            $zei_rtu          = $value['zei_rtu'];
            if(array_key_exists($zei_rtu, $ryosyuInfo[$reduced_tax_rate])){
                $ryoshu_hako_gaku = $ryosyuInfo[$reduced_tax_rate][$zei_rtu];
                $uchiwake[$key]['taisho_zan'] = $taisho_zan - $ryoshu_hako_gaku;
            }else{
                $ryoshu_hako_gaku = $ryosyuInfo[$reduced_tax_rate][99];
                $uchiwake[$key]['taisho_zan'] = $taisho_zan - $ryoshu_hako_gaku;
            }
        }
    }
    /**
     * 内金用領収証情報取得
     * @param type $db
     * @param type $uri_den_no
     */
    private function getRyosyuUcInfo($db, $uri_den_no){
        $result = array();
        $select = $db->easySelOne(<<< END_OF_SQL
        SELECT
             rh.uri_den_no
            ,rh.genkin_prc         + rh.kogite_prc         + rh.furikomi_prc         AS prc
            ,rh.genkin_prc_keigen  + rh.kogite_prc_keigen  + rh.furikomi_prc_keigen  AS prc_keigen
            ,rh.genkin_prc_5       + rh.kogite_prc_5       + rh.furikomi_prc_5       AS prc_5
            ,rh.genkin_prc_3       + rh.kogite_prc_3       + rh.furikomi_prc_3       AS prc_3
            ,rh.genkin_prc_hikazei + rh.kogite_prc_hikazei + rh.furikomi_prc_hikazei AS prc_hikazei
            FROM ryosyusho_history_uc rh
        WHERE rh.uri_den_no = :uri_den_no
        AND rh.delete_flg = 0 
        AND rh.hako_kbn IN (0, 1)
END_OF_SQL
            , array('uri_den_no' => $uri_den_no)
        );
        if(Msi_Sys_Utils::myCount($select) > 0){
            $result[1][0]  = $select['prc_hikazei'];
            $result[1][3]  = $select['prc_3'];
            $result[1][5]  = $select['prc_5'];
            $result[1][99] = $select['prc'];
            $result[0][99] = $select['prc_keigen'];
        }
        return $result;
    }
    
    /**
     * 
     * 内金用領収証発行
     * 
     * @param type $db
     * @param type $req
     * @param type $uri_den_no
     * @param type $juchu_den_no
     * @return type
     * @throws Exception
     */
    private function doUchikin($db, $req, $uri_den_no, $juchu_den_no){
        if(isset($juchu_den_no)){
            $seikyuRec = $this->_getSeikyuRecUcAzukari($juchu_den_no);
            $uri_den_no = $juchu_den_no;
        }else{
            $seikyuRec = $this->_getSeikyuRecUc($uri_den_no);
        }
        if ($seikyuRec === null) {
            throw new Exception(sprintf("売上伝票No(%d)の請求データが存在しません", $uri_den_no));
        }
        //$seikyuRec['nyukin_den_no'] = $nyukin_den_no;
        $this->_seikyuRec = $seikyuRec;
        //$uri_den_no = $seikyuRec['uri_den_no'];
        $moushi_kbn = $seikyuRec['moushi_kbn'];
        // Ajax
        if ($req->isPost()) {
            try {
                $action = $req->getPost('action');
                $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
                $dataCol = Msi_Sys_Utils::json_decode($req->getPost('dataColJson'));
                if ($action == '保存') {
                    $col = $dataCol[count($dataCol) - 1];
                    if (!$col || !isset($col['hako_kbn'])) {
                        throw new Exception('入力データが不正です');
                    }
                    $this->_add($col);
                    $msg = '新規発行しました';
                    if($col['hako_kbn'] == '1'){
                        $msg = '再発行しました';
                        // 発行状態更新
                        self::upHakoInfo($db, $col['ryosyu_no'], $col['ryosyu_eda_no'] - 1, 9, "再発行による破棄：");
                    }else if($col['hako_kbn'] == '0'){
                        if(isset($seikyuRec['status_kbn']) && $seikyuRec['status_kbn'] >= 3){
                            throw new Exception("ステータスが".$seikyuRec['status_kbn_nm']."の為、新規領収書発行は出来ません。");
                        }
                    }
                    // 領収証出力状況更新 1:個別発行済
                    //self::upRyosyuPrtStatus($db, $nyukin_den_no, 1);
                    if(isset($juchu_den_no)){
                        $seikyuRec = $this->_getSeikyuRecUcAzukari($uri_den_no);
                    }else{
                        $seikyuRec = $this->_getSeikyuRecUc($uri_den_no);
                    }
                    //$seikyuRec['nyukin_den_no'] = $nyukin_den_no;
                    $this->_seikyuRec = $seikyuRec;
                    $newDataApp = $this->_dataApp();
                    $newDataCol = $this->_dataCol();
                    if($col['hako_kbn'] == '1'){
                        $ryoshuRecAry = DataMapper_RyosyushoHistory::findUC($db,array(  
                                        'uri_den_no'    => $uri_den_no, 
                                        //'nyukin_den_no' => $seikyuRec['nyukin_den_no'],
                                        '__raw_1'       => 'hako_kbn IN (9)', // 破棄
                                        '__etc_orderby' => array('hako_count DESC')
                                    )
                                );
                        $ryoshuRec = $ryoshuRecAry[0];
                        $outputdata = array();
                        $outputdata['juchu_den_no']  = $juchu_den_no;
                        $outputdata['uri_den_no']    = $uri_den_no;
                        $outputdata['ryosyusho_no']  = $ryoshuRec['ryosyusho_no'];
                        $outputdata['seko_no']       = $ryoshuRec['seko_no'];
                        $outputdata['hako_ymd']      = $ryoshuRec['hako_ymdhis'];
                        $outputdata['hako_count']    = $ryoshuRec['hako_count'];
                        $outputdata['bumon_cd']      = $seikyuRec['bumon_cd'];
                        list($temp_file, $filename)  = Saiken_RyoshuPdf::outputPdf($db, $outputdata);
                        // 電子帳票システム連携登録
                        //$buf = Msi_Sys_Utils::get_contents($temp_file);
                        //$optData['ryosyu_no'] = $ryoshuRec['ryosyusho_no'];
                        //$optData['eda_no']    = $ryoshuRec['ryosyusho_eda_no'];
                        //$dir_id  = $seikyuRec['seko_no'];
                        //$dir_kbn = 0;
                        //if($seikyuRec['moushi_kbn'] == 3 && isset($seikyuRec['ref_seko_no'])){
                        //    // アフター(関連施行有り）
                        //    $dir_id = $seikyuRec['ref_seko_no'];
                        //}else if($seikyuRec['moushi_kbn'] == 3){
                        //    // アフター(関連施行無し）
                        //    $dir_id = $uri_den_no;
                        //    $dir_kbn = 1;
                        //}
                        //Logic_RpSystemRenkei::upsert($db, $buf, $moushi_kbn, $dir_id, $dir_kbn, 40, $ryoshuRec['hako_date_disp2'], 1, $optData);
                    }
                    $db->commit();
                    $data = array(
                        'dataApp' => $newDataApp,
                        'dataCol' => $newDataCol,
                        'status' => 'OK',
                        'msg' => $msg,
                    );
                    // 軽減税率対応 調整 keigen
                    $this->_keigenAdj($data);
                    $data['dlKey'] = array('uri_den_no' => $newDataApp['uri_den_no'],'hako_count' => $this->_hako_count_saved);
                    Msi_Sys_Utils::info(sprintf('領収書を%s(uri_den_no:%s, hako_count:%s)', $msg, $newDataApp['uri_den_no'], isset($this->_hako_count_saved) ? $this->_hako_count_saved : 'X' ));
                    Msi_Sys_Utils::outJson($data);
                } else if ($action == '破棄') {
                    $cancelData = array();
                    foreach ($dataCol as $col) {
                        // チェックされたレコード
                        if($col['ryoshu_check'] == '1'){
                            $cancelData[] = $col;
                        }
                    }
                    // 領収証破棄
                    $this->_cancelLast($db, $cancelData);
                    $msg = '破棄しました';
                    if(isset($juchu_den_no)){
                        $seikyuRec = $this->_getSeikyuRecUcAzukari($juchu_den_no);
                    }else{
                        $seikyuRec = $this->_getSeikyuRecUc($uri_den_no);
                    }
                    $this->_seikyuRec = $seikyuRec;
                    $newDataApp = $this->_dataApp();
                    $newDataCol = $this->_dataCol();
                    $ryoshuRecAry = DataMapper_RyosyushoHistory::findUC($db,array(  
                                    'uri_den_no'    => $uri_den_no, 
                                    '__raw_1'       => 'hako_kbn IN (9)', // 破棄
                                    '__etc_orderby' => array('hako_count DESC')
                                )
                            );
                    $ryoshuRec = $ryoshuRecAry[0];
                    //$outputdata = array();
                    //$outputdata['uri_den_no']    = $uri_den_no;
                    //$outputdata['ryosyusho_no']  = $ryoshuRec['ryosyusho_no'];
                    //$outputdata['seko_no']       = $ryoshuRec['seko_no'];
                    //$outputdata['hako_ymd']      = $ryoshuRec['hako_ymdhis'];
                    //$outputdata['hako_count']    = $ryoshuRec['hako_count'];
                    //$outputdata['bumon_cd']      = $seikyuRec['bumon_cd'];
                    //list($temp_file, $filename)  = Saiken_RyoshuPdf::outputPdf($db, $outputdata);
                    // 電子帳票システム連携登録
                    //$buf = Msi_Sys_Utils::get_contents($temp_file);
                    //$optData['ryosyu_no'] = $ryoshuRec['ryosyusho_no'];
                    //$optData['eda_no']    = $ryoshuRec['ryosyusho_eda_no'];
                    //$dir_id  = $seikyuRec['seko_no'];
                    //$dir_kbn = 0;
                    //if($seikyuRec['moushi_kbn'] == 3 && isset($seikyuRec['ref_seko_no'])){
                    //    // アフター(関連施行有り）
                    //    $dir_id = $seikyuRec['ref_seko_no'];
                    //}else if($seikyuRec['moushi_kbn'] == 3){
                    //    // アフター(関連施行無し）
                    //    $dir_id = $uri_den_no;
                    //    $dir_kbn = 1;
                    //}
                    //Logic_RpSystemRenkei::upsert($db, null, $moushi_kbn, $dir_id, $dir_kbn, 40, $ryoshuRec['hako_date_disp2'], 1, $optData);
                    $db->commit();
                    $data = array(
                        'dataApp' => $newDataApp,
                        'dataCol' => $newDataCol,
                        'status' => 'OK',
                        'msg' => $msg,
                    );
                    // 軽減税率対応 調整 keigen
                    $this->_keigenAdj($data);
                    Msi_Sys_Utils::info(sprintf('領収書を%s(seikyu_den_no:%s, hako_count:%s)', $msg, $newDataApp['uri_den_no'], isset($this->_hako_count_saved) ? $this->_hako_count_saved : 'X' ));
                    Msi_Sys_Utils::outJson($data);
                } else if ($action == '再発行') {
                    $msg = "コピーしました。";
                    $reissueData = array();
                    $cnt = count($dataCol);
                    foreach ($dataCol as $key => $col) {
                        // チェックされたレコード
                        if($col['ryoshu_check'] == '1'){
                            $reissueData[] = $col;
                            $dataCol[$key]['reissue_flg'] = false;
                            $dataCol[$cnt-1] = $this->_dataCol2($reissueData);
                        }
                    }
                    $data = array(
                        'dataApp' => $dataApp,
                        'dataCol' => $dataCol,
                        'status'  => 'OK',
                        'hako_flg' => true,
                        'msg'     => $msg,
                    );
                    // 軽減税率対応 調整 keigen
                    $this->_keigenAdj($data);
                    Msi_Sys_Utils::outJson($data);
                    return;
                } else if ($action == 'PDF') {
                    $hako_count = $req->getPost('hako_count');
                    // ryosyusho_pdf チェック
                    $ryoshuRecAry = DataMapper_RyosyushoHistory::findUC($db, array('uri_den_no' => $uri_den_no, 'hako_count' => $hako_count));
                    $ryoshuRec = $ryoshuRecAry[0];
                    if ($ryoshuRec === null) {
                        throw new Exception(sprintf('請求データが存在しません(udn:%s,cnt:%s)', $uri_den_no, $hako_count));
                    }
                    if ($ryoshuRec['ryosyusho_pdf']) {
                        throw new Exception('領収書は発行済みです');
                    }
                    $outputdata = array();
                    $outputdata['juchu_den_no']  = $juchu_den_no;
                    $outputdata['uri_den_no']    = $uri_den_no;
                    $outputdata['ryosyusho_no']  = $ryoshuRec['ryosyusho_no'];
                    $outputdata['seko_no']       = $ryoshuRec['seko_no'];
                    $outputdata['hako_ymd']      = $ryoshuRec['hako_ymdhis'];
                    $outputdata['hako_count']    = $ryoshuRec['hako_count'];
                    $outputdata['bumon_cd']      = $seikyuRec['bumon_cd'];
                    list($temp_file, $filename)  = Saiken_RyoshuPdf::outputPdf($db, $outputdata);
                    $buf = Msi_Sys_Utils::get_contents($temp_file);
                    // 電子帳票システム連携登録
                    //$optData['ryosyu_no'] = $ryoshuRec['ryosyusho_no'];
                    //$optData['eda_no']    = $ryoshuRec['ryosyusho_eda_no'];
                    //$dir_id  = $seikyuRec['seko_no'];
                    //$dir_kbn = 0;
                    //if($seikyuRec['moushi_kbn'] == 3 && isset($seikyuRec['ref_seko_no'])){
                    //    // アフター(関連施行有り）
                    //    $dir_id = $seikyuRec['ref_seko_no'];
                    //}else if($seikyuRec['moushi_kbn'] == 3){
                    //    // アフター(関連施行無し）
                    //    $dir_id = $uri_den_no;
                    //    $dir_kbn = 1;
                    //}
                    //Logic_RpSystemRenkei::upsert($db, $buf, $moushi_kbn, $dir_id, $dir_kbn, 40, $ryoshuRec['hako_date_disp2'], null, $optData);
                    // ryosyusho_pdf へ保存
                    $temp_file_2 = Saiken_RyoshuPdf::pdfReissue($temp_file); // バックアップ用
                    $oid2 = $db->writeBlob($temp_file_2);
                    $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE ryosyusho_history_uc SET ryosyusho_pdf=:ryosyusho_pdf
 WHERE uri_den_no=:uri_den_no
   AND hako_count=:hako_count
   AND delete_flg=0
END_OF_SQL
                        , array(
                            'uri_den_no'    => $uri_den_no,
                            'hako_count'    => $ryoshuRec['hako_count'],
                            'ryosyusho_pdf' => $oid2
                        ));
                    $db->commit();
                    Msi_Sys_Utils::info(sprintf('領収書をPDF出力しました(uri_den_no:%s, hako_count:%s)', $uri_den_no, $ryoshuRec['hako_count']));
                    Msi_Sys_Utils::out2way1($buf, $filename);
                }
            } catch (Exception $e) {
                $err = $e->getMessage();
                Msi_Sys_Utils::err('Saiken_RyoshuController: ' . $err);
                $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
                $errData = array(
                    'status' => 'NG',
                    'msg' => $userMsg,
                );
                Msi_Sys_Utils::outJson($errData);
            }
            return;
        }

        // 初期値
        if (!isset($dataApp)) {
            $dataApp = $this->_dataApp();
            $dataCol = $this->_dataCol();
        }

        // 参照専用
        if ($this->_readonly) {
            $this->view->ctxt_readonly = 'my-ctxt-readonly';
        }
        // 初期設定データ
        $data = array(
            'dataApp' => $dataApp,
            'dataCol' => $dataCol,
        );
        // 軽減税率対応 調整 keigen
        $this->_keigenAdj($data);
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
        App_Smarty::pushCssFile(['app/saiken.ryoshu.css']);
        App_Smarty::pushJsFile(['app/saiken.ryoshu.js']);
    }
    
    /**
     * 伝票データのヘッダデータを返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx
     */
    protected function _dataApp() {
        $seikyuRec = $this->_seikyuRec;
        //$nyuRec = $this->_nyuRec;
        switch ($seikyuRec['data_kbn']) {
            case '1': $dataKbnStr = '葬儀';
                break;
            case '2': $dataKbnStr = '法事';
                break;
            case '3': $dataKbnStr = '単品';
                break;
            case '4': $dataKbnStr = '別注品';
                break;
            case '6': $dataKbnStr = 'その他';
                break;
            default: $dataKbnStr = $seikyuRec['data_kbn'];
        }
        $seikyu_nm   = $seikyuRec['sekyu_nm'];
        $seikyu_gaku = $seikyuRec['h_seikyu_prc'];
        $nyukin_gaku = $seikyuRec['nyukin_prc'];
        $seikyu_zan  = $seikyuRec['seikyu_zan'];
        $ryoshu_hako_gaku = $seikyuRec['ryoshu_hako_gaku'];
        if(!isset($seikyuRec['seikyu_den_no'])){
            $nyukin_zumi_prc = self::getNyukinZumiPrcUc($seikyuRec['uri_den_no']);
        }else{
            $nyukin_zumi_prc = self::getNyukinZumiPrc($seikyuRec['seikyu_den_no']);
        }
        // 印紙税情報取得
        $inshi_info = $this->getInshiInfo();
        $db = Msi_Sys_DbManager::getMyDb();
        // 8551:印紙税納付税務署(領収証)
        $inshi = $this->getInshiCodeKbn($db, $seikyuRec['bumon_cd']);
        if(Msi_Sys_Utils::myCount($inshi) == 0){
            // 対象の印紙が無い場合、印紙税を０円に上書き
            foreach ($inshi_info as $key => $inshi_value) {
                $inshi_info[$key]['inshi_zei_kingaku'] = "0";
            }
        }
        // Zeiマスタ
        $zeiData = array();
        foreach (DataMapper_ZeiMst::find($db, array(), false) as $rec) {
            $rec11 = Msi_Sys_Utils::remapArrayFlat($rec, <<< END_OF_TXT
                zei_cd zei_rtu zei_hasu_kbn
END_OF_TXT
            );
            $zeiData[] = array_merge($rec11, array(
                'id'    => $rec['zei_cd'],
                'text'  => $rec['zei_rtu'] . '%',
            ));
        }
        $aka_kuro_flg = false;
        // 施行情報取得
        $seko_info = $this->getSekoInfo($db, $seikyuRec['seko_no']);
        // 内金発行額検索用
        $uri_den_no_2 = null;
        if(isset($seikyuRec['uri_den_no_2'])){
            $uri_den_no_2 = $seikyuRec['uri_den_no_2'];
        }
        // 内金新規発行ボタン制御用
        $seikyu_den_no_2 = null;
        if(isset($seikyuRec['seikyu_den_no_2'])){
            $seikyu_den_no_2 = $seikyuRec['seikyu_den_no_2'];
        }
        $status_kbn = null;
        if(isset($seikyuRec['status_kbn'])){
            $status_kbn = $seikyuRec['status_kbn'];
        }
        // 但し書き取得
        $sql = "
            SELECT 
               kbn_value_cd
              ,kbn_value_cd_num
              ,kbn_value_lnm
              ,kbn_value_snm
            FROM  code_nm_mst
            WHERE 
                code_kbn     = '8564'
            AND delete_flg   = 0
            ORDER BY disp_nox
        ";
        $tadashikaki = $db->easySelect($sql);
        
        // 標準税率を取得
        $zei_cd = App_Utils::getZeiCd();
        
        $dataApp = array(
            'seikyu_nm'        => $seikyu_nm,
            'seikyu_tel'       => $seikyuRec['sekyu_tel'],
            'seikyu_addr1'     => $seikyuRec['sekyu_addr1'],
            'seikyu_addr2'     => $seikyuRec['sekyu_addr2'],
            'seikyu_gaku'      => $seikyu_gaku,
            'nyukin_gaku'      => $nyukin_gaku,
            'seikyu_zan'       => $seikyu_zan,
            'ryoshu_hako_gaku' => $ryoshu_hako_gaku,
            'tanto_cd'         => App_Utils::getTantoCd(), // 担当者CD
            'tanto_nm'         => App_Utils::getTantoNm(), // 担当者名
            'seikyu_den_no'    => $seikyuRec['seikyu_den_no'],
            'uri_den_no'       => $seikyuRec['uri_den_no'],
            'uri_den_no_2'     => $uri_den_no_2,
            'seikyu_den_no_2'  => $seikyu_den_no_2,
            'juchu_den_no'     => $seikyuRec['juchu_den_no'],
            'status_kbn'       => $status_kbn,
            'data_kbn'         => $seikyuRec['data_kbn'],
            'nyukin_zumi_prc'  => $nyukin_zumi_prc,
            'pay_method_nm'    => $seikyuRec['pay_method_nm'],
            'pay_method_cd'    => $seikyuRec['pay_method_cd'],
            'inshi_info'       => $inshi_info,
            'zei'              => $zeiData,
            'zei_cd'           => $zei_cd,
            'aka_kuro_flg'     => $aka_kuro_flg,
            'seko_info'        => $seko_info,
            'tadashikaki'      => $tadashikaki,
        );
        return $dataApp;
    }
    /**
     * 
     * @param type $db
     * @param type $seko_no
     * @return type
     */
    private function getSekoInfo($db, $seko_no){
        $select = $db->easySelOne(<<< END_OF_SQL
        SELECT
             TO_CHAR(sn.nitei_ymd, 'YYYY年mm月dd日') AS nitei_ymd
            ,ski.k_nm
        FROM 
            seko_kihon_info ski
        LEFT JOIN seko_nitei sn
        ON  sn.seko_no    = ski.seko_no
        AND sn.nitei_kbn  = 11
        AND sn.delete_flg = 0
        WHERE ski.seko_no  = :seko_no
        AND ski.delete_flg = 0
END_OF_SQL
            , array('seko_no' => $seko_no)
        );
        return $select;
    }
    /**
     * 
     * 印紙税情報取得
     * 
     * @return type
     */
    private function getInshiInfo(){
        $db = Msi_Sys_DbManager::getMyDb();
        $select = $db->easySelect(<<< END_OF_SQL
SELECT 
izm.kingaku_from,
izm.kingaku_to,
izm.inshi_zei_kingaku
FROM inshi_zei_mst izm
WHERE CURRENT_DATE BETWEEN izm.tekiyo_st_date AND izm.tekiyo_ed_date
ORDER BY toroku_no

END_OF_SQL
        );
        return $select;
    }
    /**
     * 
     * 8551:印紙税納付税務署
     * 
     * @param type $db
     * @param type $bumon_cd
     */
    private function getInshiCodeKbn($db, $bumon_cd){
        $select = $db->easySelect(<<< END_OF_SQL
SELECT 
    kbn_value_cd
FROM 
    code_nm_mst
WHERE 
    code_kbn     = '8551'
AND kbn_value_lnm = :bumon_cd
AND delete_flg   = 0
END_OF_SQL
            , array('bumon_cd' => $bumon_cd)
        );
        return $select;        
    }
    /**
     * 明細データを返す
     *
     * <AUTHOR> Mihara
     * @since  2015/06/xx
     * @version 2019/05/17  mihara   軽減税率対応版
     */
    protected function _dataCol() {
        $db = Msi_Sys_DbManager::getMyDb();
        $seikyuRec = $this->_seikyuRec;
        $seikyu_den_no = $seikyuRec['seikyu_den_no'];
        //$nyukin_den_no = $seikyuRec['nyukin_den_no'];
        $uri_den_no = null;
        if(!isset($seikyu_den_no)){
            if(isset($seikyuRec['juchu_den_no'])){
                $uri_den_no = $seikyuRec['juchu_den_no'];
            }else{
                $uri_den_no = $seikyuRec['uri_den_no'];
            }
            $cond = array(
                'uri_den_no'    => $uri_den_no,
                //'nyukin_den_no' => $nyukin_den_no,
                '__etc_orderby' => array(' uri_den_no ASC ',' hako_count ASC '),
            );            
            $arrRyoshu = DataMapper_RyosyushoHistory::findUC($db, $cond);
        }else{
            $cond = array(
                'uri_den_no'    => $seikyu_den_no,
                //'nyukin_den_no' => $nyukin_den_no,
                '__etc_orderby' => array(' uri_den_no ASC ',' hako_count ASC '),
            );
            $arrRyoshu = DataMapper_RyosyushoHistory::find($db, $cond);
        }
        $ryosyu_meigi = self::getRyosyuMeigi($db, $seikyuRec['seko_no']);
        if($seikyuRec['data_kbn'] == "4" && $seikyuRec['bun_gas_kbn_num'] == "2"){
            $ryosyu_meigi = self::getMokegaiRyosyuBunkatsuMeigi($db, $seikyu_den_no, $ryosyu_meigi);
        }else if($seikyuRec['data_kbn'] == "4"){
            $ryosyu_meigi = self::getMokegaiRyosyuMeigi($db, $seikyu_den_no, $uri_den_no, $ryosyu_meigi);
        }
        $dataCol = array();
        foreach ($arrRyoshu as $rec) {
            $genkin   = $rec['genkin_prc']   <= 0 ? null : +$rec['genkin_prc'];
            $kogite   = $rec['kogite_prc']   <= 0 ? null : +$rec['kogite_prc'];
            $furikomi = $rec['furikomi_prc'] <= 0 ? null : +$rec['furikomi_prc'];
            $credit   = $rec['credit_prc']   <= 0 ? null : +$rec['credit_prc'];
            $j_debit  = $rec['j_debit_prc']  <= 0 ? null : +$rec['j_debit_prc'];
            $gokei    = $rec['gokei_prc']    <= 0 ? null : +$rec['gokei_prc'];
            $ryoshusho_no = $rec['ryosyusho_no_sub'];
            $biko = $rec['biko'];
            if(isset($rec['biko2']) && strlen($rec['biko2']) > 0){
                $biko = $rec['biko2'].' '.$biko;
            }
            $_data = array(
                'ryosyu_no'    => $rec['ryosyusho_no'],
                'ryosyu_eda_no'=> $rec['ryosyusho_eda_no'],
                'ryoshusho_no' => $ryoshusho_no,
                'hako_kbn' => $rec['hako_kbn'],
                'hako_kbn_nm' => $rec['hako_kbn_nm'],
                'disp_kbn' => $rec['disp_kbn'], // 再発行文字印刷区分   2016/03/16 ADD Kayo
                'syukin_tanto_cd' => $rec['syukin_tanto_cd'],
                'syukin_tanto_nm' => $rec['syukin_tanto_nm'],
                'jimu_tanto_cd' => $rec['jimu_tanto_cd'],
                'jimu_tanto_nm' => $rec['jimu_tanto_nm'],
                'atena' => $rec['atena'],
                'genkin'   => $genkin,
                'kogite'   => $kogite,
                'furikomi' => $furikomi,
                'credit'   => $credit,
                'j_debit'  => $j_debit,
                'kin_1' => null,
                'kin_2' => null,
                'kin_3' => null,
                'kin_4' => null,
                'way_1' => null,
                'way_2' => null,
                'way_3' => null,
                'way_4' => null,
                'zei_1' => null,
                'zei_2' => null,
                'zei_3' => null,
                'zei_4' => null,
                'gokei' => $gokei,
                'tadashikaki' => $rec['tadashikaki'],
                'tadashikaki_cd' => $rec['tadashikaki_cd'], // 但し書きコード     2016/03/16 ADD Kayo
                'biko' => $biko,
                'hako_date' => $rec['hako_date_disp'],
                'haki_date' => $rec['haki_date_disp'],
                'haki_user' => $rec['haki_user'],
                'is_edit' => false,
                'inshi_prc' => $rec['inshi_zei_prc'],
            );

            $cnt = 0;
            if ($genkin != 0) {
                ++$cnt;
                $_data['kin_' . $cnt] = +$genkin;
                $_data['way_' . $cnt] = 2; // 2:現金
            }
            if ($kogite != 0) {
                ++$cnt;
                $_data['kin_' . $cnt] = +$kogite;
                $_data['way_' . $cnt] = 3; // 3:小切手
            }
            if ($furikomi != 0) {
                ++$cnt;
                $_data['kin_' . $cnt] = +$furikomi;
                $_data['way_' . $cnt] = 1; // 1:振込
            }
            if ($credit != 0) {
                ++$cnt;
                $_data['kin_' . $cnt] = +$credit;
                $_data['way_' . $cnt] = 4; // 4:クレジットカード
            }
            if ($j_debit != 0) {
                ++$cnt;
                $_data['kin_' . $cnt] = +$j_debit;
                $_data['way_' . $cnt] = 5; // 5:ジェイデビットカード
            }
            $dataCol[] = $_data;
        }

        if (!$this->_readonly) {
            $atenaPre = $seikyuRec['sekyu_nm'];
            //if (isset($atena)) {
            //    $atenaPre = $atena;
            //}
            $_data = array(
                'ryoshusho_no' => '(自動)',
                'hako_kbn' => 0, // 伝区 xx30 => 新規(0),変更(1),破棄(9)       
                'hako_kbn_nm' => '新規発行',
                'disp_kbn' => 0, // 再発行文字印刷区分
                'syukin_tanto_cd' => null,
                'syukin_tanto_nm' => null,
                'jimu_tanto_cd' => App_Utils::getTantoCd(),
                'jimu_tanto_nm' => App_Utils::getTantoNm(),
                'atena' => $ryosyu_meigi,
                'genkin' => null,
                'kogite' => null,
                'furikomi' => null,
                'gokei' => null,
                'tadashikaki' => null,
                'tadashikaki_cd' => null, // 但し書きコード
                'biko' => null,
                'hako_date' => null,
                'haki_date' => null,
                'haki_user' => null,
                'is_edit' => true,
            );

            $dataCol[] = $_data;
        }
        return $dataCol;
    }
    /**
     * 明細データを返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx
     */
    protected function _dataCol2($reissueData) {
        $db = Msi_Sys_DbManager::getMyDb();
        $seikyuRec = $this->_seikyuRec;
        $juchu_den_no = null;
        $seikyu_den_no = null;
        if(isset($seikyuRec['seikyu_den_no'])){
            $seikyu_den_no = $seikyuRec['seikyu_den_no'];
        }else if(isset($seikyuRec['juchu_den_no'])){
            $juchu_den_no = $seikyuRec['juchu_den_no'];
        }
        //$nyukin_den_no = $seikyuRec['nyukin_den_no'];
        $dataCol = array();
        foreach ($reissueData as $rec) {
            $gokei = $rec['gokei'] <= 0 ? null : +$rec['gokei'];
            $ryosyusho_eda_no= $rec['ryosyu_eda_no'] + 1;
            $biko = $rec['biko'];
            if(isset($rec['biko2']) && strlen($rec['biko2']) > 0){
                $biko = $rec['biko2'].' '.$biko;
            }
            if(!isset($seikyu_den_no)){
                $reissuePrc = self::reissuePrcUC($db, $rec['ryosyu_no'], $rec['ryosyu_eda_no']);
            }else{
                $reissuePrc = self::reissuePrc($db, $rec['ryosyu_no'], $rec['ryosyu_eda_no']);
            }
            $_data = array(
                'ryosyu_no'         => $rec['ryosyu_no'],
                'ryosyu_eda_no'     => $ryosyusho_eda_no,
                'ryoshusho_no'      => $rec['ryosyu_no']."-".$ryosyusho_eda_no,
                'hako_kbn'          => 1,
                'hako_kbn_nm'       => "再発行",
                'disp_kbn'          => 1, // 再発行文字印刷区分   2016/03/16 ADD Kayo
                'syukin_tanto_cd'   => $rec['syukin_tanto_cd'],
                'syukin_tanto_nm'   => $rec['syukin_tanto_nm'],
                'jimu_tanto_cd'     => App_Utils::getTantoCd(),
                'jimu_tanto_nm'     => App_Utils::getTantoNm(),
                'atena'             => $rec['atena'],
                'genkin'            => null,
                'kogite'            => null,
                'furikomi'          => null,
                'kin_1'             => $reissuePrc['kin_1'],
                'kin_2'             => $reissuePrc['kin_2'],
                'kin_3'             => $reissuePrc['kin_3'],
                'kin_4'             => $reissuePrc['kin_4'],
                'way_1'             => $reissuePrc['way_1'],
                'way_2'             => $reissuePrc['way_2'],
                'way_3'             => $reissuePrc['way_3'],
                'way_4'             => $reissuePrc['way_4'],
                'zei_1'             => $reissuePrc['zei_1'],
                'zei_2'             => $reissuePrc['zei_2'],
                'zei_3'             => $reissuePrc['zei_3'],
                'zei_4'             => $reissuePrc['zei_4'],
                'gokei'             => $gokei,
                'tadashikaki'       => $rec['tadashikaki'],
                'tadashikaki_cd'    => $rec['tadashikaki_cd'], // 但し書きコード     2016/03/16 ADD Kayo
                'biko'              => $biko,
                'hako_date'         => null,
                'haki_date'         => null,
                'haki_user'         => null,
                'is_edit'           => true,
                'ryoshu_check'      => 0,
                'inshi_prc'         => $rec['inshi_prc'],
            );
            $dataCol = $_data;
        }
        return $dataCol;
    }
    /**
     * 請求データを返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx
     * @params string   $seikyu_den_no
     * @return array              請求伝票データ
     */
    protected function _getSeikyuRec($seikyu_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();

        $seikyuRec = $db->easySelOne(<<< END_OF_SQL
    SELECT 
         sd.seikyu_den_no
        ,null               AS uri_den_no
        ,sd.uri_den_no      AS uri_den_no_2
        ,ud1.denpyo_no      AS juchu_den_no
        ,sd.data_kbn
        ,sd.seko_no
        ,sd.seko_no_sub
        ,COALESCE(ud1.ref_seko_no,ud2.ref_seko_no) AS ref_seko_no
	--,sd.uri_prc_sum + sd.uri_nebk_sum + sd.uri_hepn_sum + sd.hoshi_prc_sum  AS seikyu_prc    -- 請求金額
        ,sd.uri_prc_sum
        + sd.uri_nebk_sum
        + sd.uri_hepn_sum
        + sd.hoshi_prc_sum
        + sd.out_zei_prc
        + sd.sougi_keiyaku_prc + sd.sougi_harai_prc
        + sd.sougi_keiyaku_zei
        + sd.sougi_wari_prc
        + sd.sougi_premium_service_prc
        + sd.sougi_meigi_chg_cost + sd.sougi_meigi_chg_cost_zei
        + sd.sougi_early_use_cost + sd.sougi_early_use_cost_zei
        - COALESCE(sd.uchikin_prc,0)
        + sd.etc_harai_prc                                                      AS seikyu_prc    -- 請求金額
	,sd.out_zei_prc                                                         AS zei_prc       -- 消費税額
        ,sd.seikyu_zan + sd.nyukin_prc + sd.uchikin_prc + sd.cupon_prc          AS h_seikyu_prc
        ,CASE WHEN sd.data_kbn IN(1,2) AND sd.bun_gas_kbn_num = 2 THEN seikyu.ryosyusyo_soufu_nm
              WHEN sd.data_kbn IN(1,2)                            THEN sekosaki.ryosyu_soufu_nm
              WHEN sd.data_kbn IN(3)                              THEN seikyu.sekyu_soufu_nm
              WHEN sd.data_kbn IN(4) AND sd.juchusaki_kbn = 2     THEN seikyu.sekyu_soufu_nm
              WHEN sd.data_kbn IN(4)                              THEN seikyu.ryosyusyo_soufu_nm
              ELSE seikyu.ryosyusyo_soufu_nm
         END                                                                    AS sekyu_nm
        ,CASE WHEN sd.data_kbn IN(1,2) AND sd.bun_gas_kbn_num = 2 THEN seikyu.ryosyusyo_soufu_addr1
              WHEN sd.data_kbn IN(1,2)                            THEN sekosaki.ryosyu_soufu_addr1
              WHEN sd.data_kbn IN(3)                              THEN seikyu.soufu_addr1
              WHEN sd.data_kbn IN(4) AND sd.juchusaki_kbn = 2     THEN seikyu.soufu_addr1
              WHEN sd.data_kbn IN(4)                              THEN seikyu.ryosyusyo_soufu_addr1
              ELSE seikyu.ryosyusyo_soufu_addr1
         END                                                                    AS sekyu_addr1
        ,CASE WHEN sd.data_kbn IN(1,2) AND sd.bun_gas_kbn_num = 2 THEN seikyu.ryosyusyo_soufu_addr2
              WHEN sd.data_kbn IN(1,2)                            THEN sekosaki.ryosyu_soufu_addr2
              WHEN sd.data_kbn IN(3)                              THEN seikyu.soufu_addr2
              WHEN sd.data_kbn IN(4) AND sd.juchusaki_kbn = 2     THEN seikyu.soufu_addr2
              WHEN sd.data_kbn IN(4)                              THEN seikyu.ryosyusyo_soufu_addr2
              ELSE seikyu.ryosyusyo_soufu_addr2
         END                                                                    AS sekyu_addr2
        ,CASE WHEN sd.data_kbn IN(1,2) THEN sekosaki.ryosyu_soufu_tel
		      ELSE null
         END                                                                    AS sekyu_tel
        ,sd.nyukin_prc
        ,sd.seikyu_zan
        ,TRIM(sd.pay_method_cd)                                                 AS pay_method_cd     -- 入金方法区分（支払方法区分）
        ,n_kbn.kbn_value_lnm                                                    AS pay_method_nm     -- 入金方法
        ,CASE WHEN sd.data_kbn = 4 AND sd.juchusaki_kbn = 2 THEN sd.data_kbn 
              WHEN sd.data_kbn = 3                          THEN sd.data_kbn 
              ELSE ski.moushi_kbn
         END                                                                    AS moushi_kbn           -- 申込区分
        ,sd.bumon_cd
        ,sd.bun_gas_kbn_num
    FROM seikyu_denpyo sd
    LEFT JOIN code_nm_mst n_kbn
    ON n_kbn.code_kbn = '8526'
    AND n_kbn.kbn_value_cd = TRIM(sd.pay_method_cd)
    AND n_kbn.delete_flg = 0
    LEFT JOIN seko_kihon_info ski
    ON ski.seko_no = sd.seko_no
    AND ski.delete_flg = 0
    LEFT JOIN sekyu_saki_info sekosaki
    ON  sekosaki.sekyu_cd   = ski.sekyu_cd
    AND sekosaki.delete_flg = 0
    LEFT JOIN seikyu_sekyu_saki_info seikyu
    ON  seikyu.seikyu_den_no = sd.seikyu_den_no
    AND seikyu.delete_flg    = 0
    LEFT JOIN uriage_denpyo ud1
    ON  ud1.uri_den_no    = sd.uri_den_no
    AND ud1.delete_flg    = 0
    LEFT JOIN seikyu_denpyo sd1
    ON  sd1.seikyu_den_no = sd.bun_gas_seikyu_den_no
    AND sd1.delete_flg    = 0
    LEFT JOIN uriage_denpyo ud2
    ON  ud2.uri_den_no    = sd1.uri_den_no
    AND ud2.delete_flg    = 0
    WHERE sd.delete_flg=0
    AND sd.seikyu_den_no = :seikyu_den_no
END_OF_SQL
                , array('seikyu_den_no' => $seikyu_den_no));
        if ($seikyuRec === null) {
            throw new Exception(sprintf("請求伝票No(%d)のデータが存在しません", $seikyu_den_no));
        }
        $rec = $seikyuRec;
        $rec['my_seikyu_prc'] = $rec['h_seikyu_prc'];
        $rec['ryoshu_hako_gaku']  = $this->_getRyoshusyoHakkoGaku($seikyu_den_no);
        $rec['ryoshu_hako_gaku'] += $this->_getRyoshusyoUcHakkoGaku($rec['juchu_den_no']);
        return $rec;
    }
    /**
     * 受注データを返す
     *
     * <AUTHOR> Sugiyama
     * @since  2022/04/xx
     * @params string   $juchu_den_no
     * @return array    受注伝票データ
     */
    protected function _getSeikyuRecUcAzukari($juchu_den_no){
        $db = Msi_Sys_DbManager::getMyDb();
        $seikyuRec = $db->easySelOne(<<< END_OF_SQL
    SELECT 
         null               AS uri_den_no
        ,null               AS seikyu_den_no
        ,jd.denpyo_no       AS juchu_den_no
        ,jd.data_kbn
        ,jd.seko_no
        ,jd.seko_no_sub
        ,jd1.ref_seko_no    AS ref_seko_no
        ,null               AS seikyu_prc    -- 請求金額
        ,jd.out_zei_prc                                                         AS zei_prc       -- 消費税額
        ,null               AS h_seikyu_prc
        ,CASE WHEN jd.data_kbn IN(1,2)                            THEN sekosaki.ryosyu_soufu_nm
              WHEN jd.data_kbn IN(3)                              THEN seikyu.sekyu_soufu_nm
              WHEN jd.data_kbn IN(4) AND jd.juchusaki_kbn = 2     THEN seikyu.sekyu_soufu_nm
              WHEN jd.data_kbn IN(4)                              THEN seikyu.ryosyusyo_soufu_nm
              ELSE seikyu.ryosyusyo_soufu_nm
         END                                                                    AS sekyu_nm
        ,CASE WHEN jd.data_kbn IN(1,2)                            THEN sekosaki.ryosyu_soufu_addr1
              WHEN jd.data_kbn IN(3)                              THEN seikyu.soufu_addr1
              WHEN jd.data_kbn IN(4) AND jd.juchusaki_kbn = 2     THEN seikyu.soufu_addr1
              WHEN jd.data_kbn IN(4)                              THEN seikyu.ryosyusyo_soufu_addr1
              ELSE seikyu.ryosyusyo_soufu_addr1
         END                                                                    AS sekyu_addr1
        ,CASE WHEN jd.data_kbn IN(1,2)                            THEN sekosaki.ryosyu_soufu_addr2
              WHEN jd.data_kbn IN(3)                              THEN seikyu.soufu_addr2
              WHEN jd.data_kbn IN(4) AND jd.juchusaki_kbn = 2     THEN seikyu.soufu_addr2
              WHEN jd.data_kbn IN(4)                              THEN seikyu.ryosyusyo_soufu_addr2
              ELSE seikyu.ryosyusyo_soufu_addr2
         END                                                                    AS sekyu_addr2
        ,CASE WHEN jd.data_kbn IN(1,2) THEN sekosaki.ryosyu_soufu_tel
              ELSE null
         END                                                                    AS sekyu_tel
        ,0 AS nyukin_prc
        ,0 AS seikyu_zan
        ,TRIM(jd.pay_method_cd)                                                 AS pay_method_cd     -- 入金方法区分（支払方法区分）
        ,n_kbn.kbn_value_lnm                                                    AS pay_method_nm     -- 入金方法
        ,CASE WHEN jd.data_kbn = 4 AND jd.juchusaki_kbn = 2 THEN jd.data_kbn 
              WHEN jd.data_kbn = 3                          THEN jd.data_kbn 
              ELSE ski.moushi_kbn
         END                                                                    AS moushi_kbn           -- 申込区分
        ,jd.bumon_cd
        ,ski.status_kbn
        ,cnm0610.kbn_value_lnm                                                  AS status_kbn_nm
        ,null                                                                   AS seikyu_den_no_2
    FROM juchu_denpyo jd
    LEFT JOIN code_nm_mst n_kbn
    ON n_kbn.code_kbn = '8526'
    AND n_kbn.kbn_value_cd = TRIM(jd.pay_method_cd)
    AND n_kbn.delete_flg = 0
    LEFT JOIN seko_kihon_info ski
    ON ski.seko_no = jd.seko_no
    AND ski.delete_flg = 0
    LEFT JOIN sekyu_saki_info sekosaki
    ON  sekosaki.sekyu_cd   = ski.sekyu_cd
    AND sekosaki.delete_flg = 0
    LEFT JOIN juchu_sekyu_saki_info seikyu
    ON  seikyu.denpyo_no = jd.denpyo_no
    AND seikyu.delete_flg    = 0
    LEFT JOIN juchu_denpyo jd1
    ON  jd1.denpyo_no    = jd.denpyo_no
    AND jd1.delete_flg    = 0
    LEFT JOIN code_nm_mst cnm0610
    ON  '0610'         = cnm0610.code_kbn
    AND ski.status_kbn = cnm0610.kbn_value_cd_num
    AND 0              = cnm0610.delete_flg
    WHERE jd.delete_flg=0
    AND jd.denpyo_no  = :denpyo_no
END_OF_SQL
                , array('denpyo_no' => $juchu_den_no));
        if ($seikyuRec === null) {
            throw new Exception(sprintf("受注伝票No(%d)のデータが存在しません", $juchu_den_no));
        }
        $rec = $seikyuRec;
        $rec['my_seikyu_prc'] = $rec['seikyu_prc'];
        $rec['ryoshu_hako_gaku'] = $this->_getRyoshusyoUcHakkoGaku($juchu_den_no);
        return $rec;
    }
    /**
     * 売上データを返す
     *
     * <AUTHOR> Sugiyama
     * @since  2022/04/xx
     * @params string   $uri_den_no
     * @return array              請求伝票データ
     */
    protected function _getSeikyuRecUc($uri_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();

        $seikyuRec = $db->easySelOne(<<< END_OF_SQL
    SELECT 
         ud.uri_den_no
        ,null               AS seikyu_den_no
        ,null               AS juchu_den_no
        ,ud.data_kbn
        ,ud.seko_no
        ,ud.seko_no_sub
        ,ud1.ref_seko_no    AS ref_seko_no
        ,ud.uri_prc_sum
        + ud.uri_nebk_sum
        + ud.uri_hepn_sum
        + ud.hoshi_prc_sum
        + ud.out_zei_prc
        + ud.sougi_keiyaku_prc + ud.sougi_harai_prc
        + ud.sougi_keiyaku_zei
        + ud.sougi_wari_prc
        + ud.sougi_premium_service_prc
        + ud.sougi_meigi_chg_cost + ud.sougi_meigi_chg_cost_zei
        + ud.sougi_early_use_cost + ud.sougi_early_use_cost_zei
        --- COALESCE(ud.uchikin_prc,0)
        + ud.etc_harai_prc                                                      AS seikyu_prc    -- 請求金額
        ,ud.out_zei_prc                                                         AS zei_prc       -- 消費税額
        ,ud.seikyu_zan + ud.nyukin_prc + ud.uchikin_prc + ud.cupon_prc          AS h_seikyu_prc
        ,CASE WHEN ud.data_kbn IN(1,2) AND ud.bun_gas_kbn_num = 2 THEN seikyu.ryosyusyo_soufu_nm
              WHEN ud.data_kbn IN(1,2)                            THEN sekosaki.ryosyu_soufu_nm
              WHEN ud.data_kbn IN(3)                              THEN seikyu.sekyu_soufu_nm
              WHEN ud.data_kbn IN(4) AND ud.juchusaki_kbn = 2     THEN seikyu.sekyu_soufu_nm
              WHEN ud.data_kbn IN(4)                              THEN seikyu.ryosyusyo_soufu_nm
              ELSE seikyu.ryosyusyo_soufu_nm
         END                                                                    AS sekyu_nm
        ,CASE WHEN ud.data_kbn IN(1,2) AND ud.bun_gas_kbn_num = 2 THEN seikyu.ryosyusyo_soufu_addr1
              WHEN ud.data_kbn IN(1,2)                            THEN sekosaki.ryosyu_soufu_addr1
              WHEN ud.data_kbn IN(3)                              THEN seikyu.soufu_addr1
              WHEN ud.data_kbn IN(4) AND ud.juchusaki_kbn = 2     THEN seikyu.soufu_addr1
              WHEN ud.data_kbn IN(4)                              THEN seikyu.ryosyusyo_soufu_addr1
              ELSE seikyu.ryosyusyo_soufu_addr1
         END                                                                    AS sekyu_addr1
        ,CASE WHEN ud.data_kbn IN(1,2) AND ud.bun_gas_kbn_num = 2 THEN seikyu.ryosyusyo_soufu_addr2
              WHEN ud.data_kbn IN(1,2)                            THEN sekosaki.ryosyu_soufu_addr2
              WHEN ud.data_kbn IN(3)                              THEN seikyu.soufu_addr2
              WHEN ud.data_kbn IN(4) AND ud.juchusaki_kbn = 2     THEN seikyu.soufu_addr2
              WHEN ud.data_kbn IN(4)                              THEN seikyu.ryosyusyo_soufu_addr2
              ELSE seikyu.ryosyusyo_soufu_addr2
         END                                                                    AS sekyu_addr2
        ,CASE WHEN ud.data_kbn IN(1,2) THEN sekosaki.ryosyu_soufu_tel
              ELSE null
         END                                                                    AS sekyu_tel
        ,ud.nyukin_prc
        ,ud.seikyu_zan
        ,TRIM(ud.pay_method_cd)                                                 AS pay_method_cd     -- 入金方法区分（支払方法区分）
        ,n_kbn.kbn_value_lnm                                                    AS pay_method_nm     -- 入金方法
        ,CASE WHEN ud.data_kbn = 4 AND ud.juchusaki_kbn = 2 THEN ud.data_kbn 
              WHEN ud.data_kbn = 3                          THEN ud.data_kbn 
              ELSE ski.moushi_kbn
         END                                                                    AS moushi_kbn           -- 申込区分
        ,ud.bumon_cd
        ,ski.status_kbn
        ,cnm0610.kbn_value_lnm                                                  AS status_kbn_nm
        ,sd.seikyu_den_no                                                       AS seikyu_den_no_2
    FROM uriage_denpyo ud
    LEFT JOIN code_nm_mst n_kbn
    ON n_kbn.code_kbn = '8526'
    AND n_kbn.kbn_value_cd = TRIM(ud.pay_method_cd)
    AND n_kbn.delete_flg = 0
    LEFT JOIN seko_kihon_info ski
    ON ski.seko_no = ud.seko_no
    AND ski.delete_flg = 0
    LEFT JOIN sekyu_saki_info sekosaki
    ON  sekosaki.sekyu_cd   = ski.sekyu_cd
    AND sekosaki.delete_flg = 0
    LEFT JOIN uriage_sekyu_saki_info seikyu
    ON  seikyu.uri_den_no = ud.uri_den_no
    AND seikyu.delete_flg    = 0
    LEFT JOIN uriage_denpyo ud1
    ON  ud1.uri_den_no    = ud.uri_den_no
    AND ud1.delete_flg    = 0
    LEFT JOIN code_nm_mst cnm0610
    ON  '0610'         = cnm0610.code_kbn
    AND ski.status_kbn = cnm0610.kbn_value_cd_num
    AND 0              = cnm0610.delete_flg
    LEFT JOIN seikyu_denpyo sd
    ON  sd.uri_den_no = ud.uri_den_no
    AND sd.delete_flg = 0
    WHERE ud.delete_flg=0
    AND ud.uri_den_no  = :uri_den_no
END_OF_SQL
                , array('uri_den_no' => $uri_den_no));
        if ($seikyuRec === null) {
            throw new Exception(sprintf("売上伝票No(%d)のデータが存在しません", $uri_den_no));
        }
        $rec = $seikyuRec;
        $rec['my_seikyu_prc'] = $rec['seikyu_prc'];
        $rec['ryoshu_hako_gaku'] = $this->_getRyoshusyoUcHakkoGaku($uri_den_no);
        return $rec;
    }
    /**
     * 入金データを返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx
     * @params string   $seikyu_den_no
     * @return array              入金伝票データ
     */
    protected function _getNyukinRec($nyukin_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $nyuRec = $db->easySelOne(<<< END_OF_SQL
        SELECT 
             nd.denpyo_no
            ,nd.nyu_kbn                                                     -- 入金区分
            ,TO_CHAR(nd.nyukin_ymd, 'YYYY/MM/DD')            AS nyukin_ymd
            ,nd.nyukin_prc
            ,nd.nyukin_prc - 
             COALESCE(SUM(CASE WHEN cnm.code_kbn IS NOT NULL THEN ndm.nyukin_prc ELSE 0 END), 0)
                                                             AS h_nyukin_prc
            ,COALESCE(SUM(CASE WHEN cnm.code_kbn IS NOT NULL THEN ndm.nyukin_prc ELSE 0 END), 0)
                                                             AS chosei_prc  -- 売上値引 / 支払手数料 / 貸倒損失
            ,nd.seikyu_no                                                   -- 請求伝票番号
            ,nd.uri_den_no                                                  -- 売上伝票番号
        FROM nyukin_denpyo nd
        LEFT JOIN nyukin_denpyo_msi ndm
        ON  ndm.denpyo_no  = nd.denpyo_no
        AND ndm.delete_flg = 0
        LEFT JOIN code_nm_mst cnm -- 入金調整額科目
        ON  cnm.code_kbn      = '8581'
        AND cnm.kbn_value_lnm = ndm.kamoku_cd
        AND cnm.delete_flg    = 0
        WHERE nd.denpyo_no = :nyukin_den_no
        AND nd.delete_flg  = 0
        GROUP BY 
             nd.denpyo_no
END_OF_SQL
                , array('nyukin_den_no' => $nyukin_den_no));
        if ($nyuRec === null) {
            throw new Exception(sprintf("入金伝票No(%d)のデータが存在しません", $nyukin_den_no));
        }
        return $nyuRec;
    }

    /**
     * 領収書発行額を返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx
     * @params string   $seikyu_den_no
     * @return array    領収証データ
     */
    protected function _getRyoshusyoHakkoGaku($seikyu_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();

        $val = $db->getOneVal(<<< END_OF_SQL
SELECT SUM(gokei_prc)
  FROM ryosyusho_history
 WHERE delete_flg = 0
   AND hako_kbn NOT IN (9)
   AND uri_den_no = :seikyu_den_no
END_OF_SQL
                , array('seikyu_den_no' => $seikyu_den_no));
        if ($val === null) {
            return 0; // null;
        }

        return $val;
    }
    /**
     * 領収書発行額(内金)を返す
     *
     * <AUTHOR> Sugiyama
     * @since  2022/04/xx
     * @params string   $uri_den_no
     * @return array    領収証(内金)データ
     */
    protected function _getRyoshusyoUcHakkoGaku($uri_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();

        $val = $db->getOneVal(<<< END_OF_SQL
SELECT SUM(gokei_prc)
  FROM ryosyusho_history_uc
 WHERE delete_flg = 0
   AND hako_kbn NOT IN (9)
   AND uri_den_no = :uri_den_no
END_OF_SQL
                , array('uri_den_no' => $uri_den_no));
        if ($val === null) {
            return 0; // null;
        }

        return $val;
    }
    /**
     * データ追加処理
     *
     * <AUTHOR> Sugiyama
     * @since  2025/04/xx
     * @param  array  $data
     * @param  array  $dataCol
     * @return void
     */
    protected function _addAzukari($data) {
        $db = Msi_Sys_DbManager::getMyDb();
        // 画面からの情報取得
        $way   = Msi_Sys_Utils::checkVarOrDefault($data['way_1'], '/^\d+$/', 0); // 種別
        $kin   = Msi_Sys_Utils::checkVarOrDefault($data['kin_1'], 'INT', 0);     // 金額

        $seikyuRec = $this->_seikyuRec;
        $denpyo_no = $seikyuRec['seikyu_den_no'];
        $table_name = "ryosyusho_history";
        $arrRyoshu = array();
        // 領収証情報取得
        $arrRyoshu = DataMapper_RyosyushoHistory::findUC($db, array('uri_den_no' => $seikyuRec['juchu_den_no']));
        $denpyo_no = $seikyuRec['juchu_den_no'];
        $table_name = "ryosyusho_history_uc";
        //}
        $hako_count = count($arrRyoshu);
        $hako_count++;
        
        // 標準税率取得
        $cond['reduced_tax_rate'] = 1;
        $zei_mst = DataMapper_ZeiMst::findOne($db, $cond);
                        
        if ($data['gokei'] <= 0) {
            throw new Exception(sprintf('金額がゼロです'));
        }
        if (strlen($data['tadashikaki_cd']) <= 0) {
            throw new Exception("但し書きコードが指定されていません");
        }
        
        $TaxPrc = App_ClsTaxLib::CalcTax($data['gokei'], 1, $zei_mst['zei_rtu'], $zei_mst['zei_hasu_kbn']); // 1(内税)で計算
        $tax = $TaxPrc['ZeiPrc'];
        
        $genkin   = 0;
        $furikomi = 0;
        $credit   = 0;
        $j_debit  = 0;
        if( $way == 1 ){        // 1:振込
            $furikomi += $kin;
        }else if ( $way == 2 ){ // 2:現金
            $genkin += $kin;
        }else if( $way == 4 ){  // 4:クレジットカード/デビットカード
            $credit += $kin;
        }else if( $way == 5 ){  // 5:ジェイデビットカード
            $j_debit += $kin;
        }
        if(isset($data['ryosyu_no'])){
            $ryosyusho_no = $data['ryosyu_no'];
            $ryosyusho_eda_no = $data['ryosyu_eda_no'];
        }else{
            $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, $table_name, 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
            $ryosyusho_eda_no = 0;
        }
        $history = array();
        $history['seko_no']          = $seikyuRec['seko_no'];
        $history['seko_no_sub']      = $seikyuRec['seko_no_sub'];
        $history['uri_den_no']       = $denpyo_no;
        $history['ryosyusho_no']     = $ryosyusho_no;
        $history['ryosyusho_eda_no'] = $ryosyusho_eda_no;
        $history['ryosyu_no']        = $ryosyusho_no;
        $history['ryosyu_no_sub']    = $ryosyusho_eda_no;
        $history['data_kbn']         = $seikyuRec['data_kbn'];
        $history['hako_count']       = $hako_count;
        $history['hako_kbn']         = $data['hako_kbn'];
        $history['hako_date']        = Msi_Sys_Utils::getDatetimeStd();
        $history['atena']            = $data['atena'];
        $history['disp_kbn']         = $data['disp_kbn'];
        $history['gokei_prc']        = $data['gokei'];
        $history['biko']             = $data['biko'];
        $history['tadashikaki_cd']   = $data['tadashikaki_cd']; // 但し書きコード
        $history['tadashikaki']      = $data['tadashikaki'];
        $history['syukin_tanto_cd']  = $data['syukin_tanto_cd'];
        $history['jimu_tanto_cd']    = $data['jimu_tanto_cd'];
        $history['genkin_prc']       = $genkin;
        $history['furikomi_prc']     = $furikomi;
        $history['credit_prc']       = $credit;
        $history['j_debit_prc']      = $j_debit;
        $history['zei_prc_std']      = $tax;
        $history['inshi_zei_kbn']    = 1;
        $history['inshi_zei_prc']    = $data['inshi_prc'];    // 印紙税;
        // 発行部門分
        $inshi_type_cd = null;
        $inshi_type = DataMapper_CodeNmMst::findOne($db, array('code_kbn'=> '8551', 'kbn_value_snm' => 10, 'kbn_value_lnm' => $seikyuRec['bumon_cd']));
        if(Msi_Sys_Utils::myCount($inshi_type) > 0){
            $inshi_type_cd = $inshi_type['kbn_value_cd'];
        }
        $history['inshi_type_cd']    = $inshi_type_cd;
        list($sql, $param) = DataMapper_Utils::makeInsertSQL($table_name, $history);
        $cnt = $db->easyExecute($sql, $param);
        $this->_hako_count_saved = $hako_count;
    }
    /**
     * データ追加処理 
     * 
     * @param type $data
     */
    protected function _addNotZero($db, $data) {
        $way  = +Msi_Sys_Utils::checkVarOrDefault($data['way_1'], 'INT', 0);
        $kin  = +Msi_Sys_Utils::checkVarOrDefault($data['kin_1'], 'INT', 0);
        // 領収書発行額
        $ryoshu_hako_gaku = $this->_seikyuRec['ryoshu_hako_gaku'];
        if(isset($data['disp_kbn']) && $data['disp_kbn'] == 1){
            // 再発行の場合
            // 再発行分の金額をマイナスする
            $ryoshu_hako_gaku = $ryoshu_hako_gaku - $data['kin_1'];
        }
        // 税別の内訳取得
        $uchiwake = $this->_getMyUchiwake($this->_seikyuRec);
        // 領収証発行額をマイナスする
        if($ryoshu_hako_gaku != 0){
            foreach ($uchiwake as $key => $value){
                if($value['taisho_gaku'] >= $ryoshu_hako_gaku){
                    $uchiwake[$key]['taisho_gaku'] = $value['taisho_gaku']- $ryoshu_hako_gaku;
                    $ryoshu_hako_gaku = 0;
                }else{
                    $uchiwake[$key]['taisho_gaku'] = 0;
                    $ryoshu_hako_gaku = $ryoshu_hako_gaku - $value['taisho_gaku'];
                }
            }
        }
        // 税率毎に按分する
        $_uchiwake = array();
        $i = 0;
        $gokei = $data['gokei'];
        foreach ($uchiwake as $key => $value){
            if($value['taisho_gaku'] >= $gokei){
                $_uchiwake[$i]['taisho_gaku'] = $gokei;
                $_uchiwake[$i]['zei_kbn']     = $value['zei_kbn'];
                $_uchiwake[$i]['zei_rtu']     = $value['zei_rtu'];
                $_uchiwake[$i]['zei_cd']      = $value['zei_cd'];
                $uchiwake[$key]['taisho_gaku'] = $value['taisho_gaku']- $gokei;
                $gokei = 0;
            }else{
                $_uchiwake[$i]['taisho_gaku'] = $value['taisho_gaku'];
                $_uchiwake[$i]['zei_kbn']     = $value['zei_kbn'];
                $_uchiwake[$i]['zei_rtu']     = $value['zei_rtu'];
                $_uchiwake[$i]['zei_cd']      = $value['zei_cd'];
                $gokei = $gokei - $value['taisho_gaku'];
                $uchiwake[$key]['taisho_gaku'] = 0;
            }
            $i++;
        }
        // 内消費税を取得する
        foreach ($_uchiwake as $key => $value){
            $zei_hasu_kbn = 0;
            $zei_mst = DataMapper_ZeiMst::findOne($db, array('zei_cd' => $value['zei_cd']));
            if(Msi_Sys_Utils::myCount($zei_mst) > 0){
                $zei_hasu_kbn = $zei_mst['zei_hasu_kbn'];
                $_uchiwake[$key]['reduced_tax_rate'] = $zei_mst['reduced_tax_rate'];
            }
            $TaxPrc = App_ClsTaxLib::CalcTax($value['taisho_gaku'], 1, $value['zei_rtu'], $zei_hasu_kbn);
            $_uchiwake[$key]['zei_gaku'] = $TaxPrc['ZeiPrc'];
        }
        // 領収方法を判定
        $genkin_prc_hikazei   = 0;
        $kogite_prc_hikazei   = 0;
        $furikomi_prc_hikazei = 0;
        $credit_prc_hikazei   = 0;
        $j_debit_prc_hikazei  = 0;
        $genkin_prc_keigen    = 0;
        $kogite_prc_keigen    = 0;
        $furikomi_prc_keigen  = 0;
        $credit_prc_keigen    = 0;
        $j_debit_prc_keigen   = 0;
        $zei_prc_keigen       = 0;
        $genkin_prc_3         = 0;
        $kogite_prc_3         = 0;
        $furikomi_prc_3       = 0;
        $credit_prc_3         = 0;
        $zei_prc_3            = 0;
        $j_debit_prc_3        = 0;
        $genkin_prc_5         = 0;
        $kogite_prc_5         = 0;
        $furikomi_prc_5       = 0;
        $credit_prc_5         = 0;
        $j_debit_prc_5        = 0;
        $zei_prc_5            = 0;
        $genkin_prc_8         = 0;
        $kogite_prc_8         = 0;
        $furikomi_prc_8       = 0;
        $credit_prc_8         = 0;
        $j_debit_prc_8        = 0;
        $zei_prc_8            = 0;
        $genkin_prc_std       = 0;
        $kogite_prc_std       = 0;
        $furikomi_prc_std     = 0;
        $credit_prc_std       = 0;
        $j_debit_prc_std      = 0;
        $zei_prc_std          = 0;
        foreach ($_uchiwake as $key => $value){
            // 0円はスキップ
            if($value['taisho_gaku'] == 0){
                continue;
            }
            if($value['zei_rtu'] == 0){ // 非課税
                if($way == 2){
                    $genkin_prc_hikazei   += $value['taisho_gaku'];
                }else if($way == 3) {
                    $kogite_prc_hikazei   += $value['taisho_gaku']; 
                }else if( $way == 1 ){
                    $furikomi_prc_hikazei += $value['taisho_gaku'];
                }else if( $way == 4 ){
                    $credit_prc_hikazei   += $value['taisho_gaku'];
                }else if( $way == 5 ){
                    $j_debit_prc_hikazei  += $value['taisho_gaku'];
                }
            } else if ( $value['reduced_tax_rate'] == 2 ) { // 軽減税率
                if($way == 2){ 
                    $genkin_prc_keigen   += $value['taisho_gaku'];
                    $zei_prc_keigen      += $value['zei_gaku'];
                }else if($way == 3) {
                    $kogite_prc_keigen   += $value['taisho_gaku']; 
                    $zei_prc_keigen      += $value['zei_gaku'];
                }else if( $way == 1 ){
                    $furikomi_prc_keigen += $value['taisho_gaku'];
                    $zei_prc_keigen      += $value['zei_gaku'];
                }else if( $way == 4 ){
                    $credit_prc_keigen   += $value['taisho_gaku'];
                    $zei_prc_keigen      += $value['zei_gaku'];
                }else if( $way == 5 ){
                    $j_debit_prc_keigen  += $value['taisho_gaku'];
                    $zei_prc_keigen      += $value['zei_gaku'];
                }
            } else if ( $value['zei_rtu'] == 3 ) {
                if($way == 2){ 
                    $genkin_prc_3   += $value['taisho_gaku'];
                    $zei_prc_3      += $value['zei_gaku'];
                }else if($way == 3) {
                    $kogite_prc_3   += $value['taisho_gaku']; 
                    $zei_prc_3      += $value['zei_gaku'];
                }else if( $way == 1 ){
                    $furikomi_prc_3 += $value['taisho_gaku'];
                    $zei_prc_3      += $value['zei_gaku'];
                }else if( $way == 4 ){
                    $credit_prc_3   += $value['taisho_gaku'];
                    $zei_prc_3      += $value['zei_gaku'];
                }else if( $way == 5 ){
                    $j_debit_prc_3  += $value['taisho_gaku'];
                    $zei_prc_3      += $value['zei_gaku'];
                }
            } else if ( $value['zei_rtu'] == 5 ) {
                if($way == 2){ 
                    $genkin_prc_5   += $value['taisho_gaku'];
                    $zei_prc_5      += $value['zei_gaku'];
                }else if($way == 3) {
                    $kogite_prc_5   += $value['taisho_gaku']; 
                    $zei_prc_5      += $value['zei_gaku'];
                }else if( $way == 1 ){
                    $furikomi_prc_5 += $value['taisho_gaku'];
                    $zei_prc_5      += $value['zei_gaku'];
                }else if( $way == 4 ){
                    $credit_prc_5   += $value['taisho_gaku'];
                    $zei_prc_5      += $value['zei_gaku'];
                }else if( $way == 5 ){
                    $j_debit_prc_5  += $value['taisho_gaku'];
                    $zei_prc_5      += $value['zei_gaku'];
                }
            } else if ( $value['zei_rtu'] == 8 ) {
                if($way == 2){ 
                    $genkin_prc_8   += $value['taisho_gaku'];
                    $zei_prc_8      += $value['zei_gaku'];
                }else if($way == 3) {
                    $kogite_prc_8   += $value['taisho_gaku']; 
                    $zei_prc_8      += $value['zei_gaku'];
                }else if( $way == 1 ){
                    $furikomi_prc_8 += $value['taisho_gaku'];
                    $zei_prc_8      += $value['zei_gaku'];
                }else if( $way == 4 ){
                    $credit_prc_8   += $value['taisho_gaku'];
                    $zei_prc_8      += $value['zei_gaku'];
                }else if( $way == 5 ){
                    $j_debit_prc_8  += $value['taisho_gaku'];
                    $zei_prc_8      += $value['zei_gaku'];
                }
            } else { // 標準税率
                if($way == 2){ 
                    $genkin_prc_std   += $value['taisho_gaku'];
                    $zei_prc_std      += $value['zei_gaku'];
                }else if($way == 3) {
                    $kogite_prc_std   += $value['taisho_gaku']; 
                    $zei_prc_std      += $value['zei_gaku'];
                }else if( $way == 1 ){
                    $furikomi_prc_std += $value['taisho_gaku'];
                    $zei_prc_std      += $value['zei_gaku'];
                }else if( $way == 4 ){
                    $credit_prc_std   += $value['taisho_gaku'];
                    $zei_prc_std      += $value['zei_gaku'];
                }else if( $way == 5 ){
                    $j_debit_prc_std  += $value['taisho_gaku'];
                    $zei_prc_std      += $value['zei_gaku'];
                }
            }
        }
        $seikyuRec = $this->_seikyuRec;
        // 領収証情報取得
        $arrRyoshu = DataMapper_RyosyushoHistory::find($db, array('uri_den_no' => $seikyuRec['seikyu_den_no']));
        $hako_count = count($arrRyoshu);
        $hako_count++;
        // 領収証番号採番
        if(isset($data['ryosyu_no'])){
            $ryosyusho_no = $data['ryosyu_no'];
            $ryosyusho_eda_no = $data['ryosyu_eda_no'];
        }else{
            $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, 'ryosyusho_history', 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());
            $ryosyusho_eda_no = 0;
        }
        $history = array();
        $history['seko_no']              = $seikyuRec['seko_no'];
        $history['seko_no_sub']          = $seikyuRec['seko_no_sub'];
        $history['uri_den_no']           = $seikyuRec['seikyu_den_no'];
        $history['ryosyusho_no']         = $ryosyusho_no;
        $history['ryosyusho_eda_no']     = $ryosyusho_eda_no;
        $history['ryosyu_no']            = $ryosyusho_no;
        $history['ryosyu_no_sub']        = $ryosyusho_eda_no;
        $history['data_kbn']             = $seikyuRec['data_kbn'];
        $history['hako_count']           = $hako_count;
        $history['hako_kbn']             = $data['hako_kbn'];
        $history['hako_date']            = Msi_Sys_Utils::getDatetimeStd();
        $history['atena']                = $data['atena'];
        $history['disp_kbn']             = $data['disp_kbn'];
        $history['gokei_prc']            = $data['gokei'];
        $history['biko']                 = $data['biko'];
        $history['tadashikaki_cd']       = $data['tadashikaki_cd'];
        $history['tadashikaki']          = $data['tadashikaki'];
        $history['syukin_tanto_cd']      = $data['syukin_tanto_cd'];
        $history['jimu_tanto_cd']        = $data['jimu_tanto_cd'];
        $history['genkin_prc']           = $genkin_prc_std + $genkin_prc_keigen + $genkin_prc_hikazei + $genkin_prc_3 + $genkin_prc_5 + $genkin_prc_8;
        $history['kogite_prc']           = $kogite_prc_std + $kogite_prc_keigen + $kogite_prc_hikazei + $kogite_prc_3 + $kogite_prc_5 + $kogite_prc_8;
        $history['furikomi_prc']         = $furikomi_prc_std + $furikomi_prc_keigen + $furikomi_prc_hikazei + $furikomi_prc_3 + $furikomi_prc_5 + $furikomi_prc_8;
        $history['credit_prc']           = $credit_prc_std + $credit_prc_keigen + $credit_prc_hikazei + $credit_prc_3 + $credit_prc_5 + $credit_prc_8;
        $history['j_debit_prc']          = $j_debit_prc_std + $j_debit_prc_keigen + $j_debit_prc_hikazei + $j_debit_prc_3 + $j_debit_prc_5 + $j_debit_prc_8;
        $history['genkin_prc_keigen']    = $genkin_prc_keigen;
        $history['kogite_prc_keigen']    = $kogite_prc_keigen;
        $history['furikomi_prc_keigen']  = $furikomi_prc_keigen;
        $history['credit_prc_keigen']    = $credit_prc_keigen;
        $history['j_debit_prc_keigen']   = $j_debit_prc_keigen;
        $history['genkin_prc_hikazei']   = $genkin_prc_hikazei;
        $history['kogite_prc_hikazei']   = $kogite_prc_hikazei;
        $history['furikomi_prc_hikazei'] = $furikomi_prc_hikazei;
        $history['credit_prc_hikazei']   = $credit_prc_hikazei;
        $history['j_debit_prc_hikazei']  = $j_debit_prc_hikazei;
        $history['genkin_prc_3']         = $genkin_prc_3;
        $history['kogite_prc_3']         = $kogite_prc_3;
        $history['furikomi_prc_3']       = $furikomi_prc_3;
        $history['credit_prc_3']         = $credit_prc_3;
        $history['j_debit_prc_3']        = $j_debit_prc_3;
        $history['genkin_prc_5']         = $genkin_prc_5;
        $history['kogite_prc_5']         = $kogite_prc_5;
        $history['furikomi_prc_5']       = $furikomi_prc_5;
        $history['credit_prc_5']         = $credit_prc_5;
        $history['j_debit_prc_5']        = $j_debit_prc_5;
        $history['genkin_prc_8']         = $genkin_prc_8;
        $history['kogite_prc_8']         = $kogite_prc_8;
        $history['furikomi_prc_8']       = $furikomi_prc_8;
        $history['credit_prc_8']         = $credit_prc_8;
        $history['j_debit_prc_8']        = $j_debit_prc_8;
        $history['zei_prc_std']          = $zei_prc_std;    // 税額標準税率分
        $history['zei_prc_3']            = $zei_prc_3;      // 税額標準税率分
        $history['zei_prc_5']            = $zei_prc_5;      // 税額標準税率分
        $history['zei_prc_8']            = $zei_prc_8;      // 税額標準税率分
        $history['zei_prc_keigen']       = $zei_prc_keigen; // 税額軽減税率分
        $history['inshi_zei_kbn']        = 1;
        $history['inshi_zei_prc']        = $data['inshi_prc'];  // 印紙税;
        // 発行部門分
        $inshi_type_cd = null;
        $inshi_type = DataMapper_CodeNmMst::findOne($db, array('code_kbn'=> '8551', 'kbn_value_snm' => 10, 'kbn_value_lnm' => $seikyuRec['bumon_cd']));
        if(Msi_Sys_Utils::myCount($inshi_type) > 0){
            $inshi_type_cd = $inshi_type['kbn_value_cd'];
        }
        $history['inshi_type_cd']        = $inshi_type_cd;
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('ryosyusho_history', $history);
        $cnt = $db->easyExecute($sql, $param);
        $this->_hako_count_saved = $hako_count;
    }
    /**
     * データ追加処理（税率未入力モード）全額発行では無い場合
     *
     * <AUTHOR> Mihara
     * @since  2015/06/xx
     * @param  array   $data
     * @param  array  $dataCol
     * @return void
     */
    protected function _add($data) {
        $data_kbns = array('1','2','3');
        $seikyuRec = $this->_seikyuRec;
        // 預かり証の場合
        if(!isset($seikyuRec['seikyu_den_no']) && in_array($seikyuRec['data_kbn'], $data_kbns)){
           $this->_addAzukari($data);
           return;
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $genkin   = 0; // +$data['genkin'];
        $kogite   = 0; // +$data['kogite'];
        $furikomi = 0; // +$data['furikomi'];
        $credit   = 0;
        $j_debit  = 0;
        $gokei    = +$data['gokei'];
        $_ryoshu_hako_gaku = $seikyuRec['ryoshu_hako_gaku'];
        if(isset($data['hako_kbn']) && $data['hako_kbn'] != 1){
            if ($_ryoshu_hako_gaku + $gokei > $seikyuRec['my_seikyu_prc']) {
                throw new Exception(sprintf('領収書発行金額(%s)が請求金額(%s)を超えています', Msi_Sys_Utils::filterComma($_ryoshu_hako_gaku + $gokei), Msi_Sys_Utils::filterComma($seikyuRec['my_seikyu_prc'])));
            }
        }
        // 請求残ゼロを示すフラグ
        $this->_isZanZero = $_ryoshu_hako_gaku + $gokei == $seikyuRec['my_seikyu_prc'];
        // 供花供物以外（税率未入力モード）の場合
        if(!$this->_isZanZero && $seikyuRec['data_kbn'] != 4){
            $this->_addNotZero($db, $data, $seikyuRec);
            return;
        }
        $kin_1    = +Msi_Sys_Utils::checkVarOrDefault($data['kin_1'], 'INT', 0);
        $kin_2    = +Msi_Sys_Utils::checkVarOrDefault($data['kin_2'], 'INT', 0);
        $kin_3    = +Msi_Sys_Utils::checkVarOrDefault($data['kin_3'], 'INT', 0);
        $kin_4    = +Msi_Sys_Utils::checkVarOrDefault($data['kin_4'], 'INT', 0);
        $_sum     = $kin_1 + $kin_2 + $kin_3 + $kin_4; // $genkin + $kogite + $furikomi;
        $way_1    = +Msi_Sys_Utils::checkVarOrDefault($data['way_1'], '/^\d+$/', 0);
        $way_2    = +Msi_Sys_Utils::checkVarOrDefault($data['way_2'], '/^\d+$/', 0);
        $way_3    = +Msi_Sys_Utils::checkVarOrDefault($data['way_3'], '/^\d+$/', 0);
        $way_4    = +Msi_Sys_Utils::checkVarOrDefault($data['way_4'], '/^\d+$/', 0);
        $zei_1    = +Msi_Sys_Utils::checkVarOrDefault($data['zei_1'], '/^\d+$/', -99);
        $zei_2    = +Msi_Sys_Utils::checkVarOrDefault($data['zei_2'], '/^\d+$/', -99);
        $zei_3    = +Msi_Sys_Utils::checkVarOrDefault($data['zei_3'], '/^\d+$/', -99);
        $zei_4    = +Msi_Sys_Utils::checkVarOrDefault($data['zei_4'], '/^\d+$/', -99);

        if (strlen($data['gokei']) <= 0) {
            throw new Exception(sprintf('金額が指定されていません'));
        }
        if ($gokei <= 0) {
            throw new Exception(sprintf('金額がゼロです'));
        }
        if ((integer) $gokei !== (integer) $_sum) {
            throw new Exception(sprintf('合計額が正しくありません(%s<=>%s)', $gokei, $_sum));
        }
        if (strlen($data['tadashikaki_cd']) <= 0) {
            throw new Exception("但し書きコードが指定されていません");
        }
        // 税別の内訳
        $uchiwake = $this->_getMyUchiwake($this->_seikyuRec);
        list($zan_hikazei, $zan_std, $zan_keigen, $zan_3, $zan_5, $zan_8) = $this->_getEachZanByType($this->_seikyuRec);
        
        $genkin_prc_keigen    = 0; // 現金軽減税率分
        $kogite_prc_keigen    = 0; // 小切手軽減税率分
        $furikomi_prc_keigen  = 0; // 振込軽減税率分
        $credit_prc_keigen    = 0;
        $j_debit_prc_keigen   = 0;
        $genkin_prc_hikazei   = 0; // 現金非課税分
        $kogite_prc_hikazei   = 0; // 小切手非課税分
        $furikomi_prc_hikazei = 0; // 振込非課税分
        $credit_prc_hikazei   = 0;
        $j_debit_prc_hikazei  = 0;
        $genkin_prc_3         = 0; // 現金標準税率分  テーブル項目にはない
        $kogite_prc_3         = 0; // 小切手標準税率分  テーブル項目にはない
        $furikomi_prc_3       = 0; // 振込標準税率分  テーブル項目にはない
        $credit_prc_3         = 0;
        $j_debit_prc_3        = 0;
        $genkin_prc_5         = 0; // 現金標準税率分  テーブル項目にはない
        $kogite_prc_5         = 0; // 小切手標準税率分  テーブル項目にはない
        $furikomi_prc_5       = 0; // 振込標準税率分  テーブル項目にはない
        $credit_prc_5         = 0;
        $j_debit_prc_5        = 0;
        $genkin_prc_8         = 0; // 現金標準税率分  テーブル項目にはない
        $kogite_prc_8         = 0; // 小切手標準税率分  テーブル項目にはない
        $furikomi_prc_8       = 0; // 振込標準税率分  テーブル項目にはない
        $credit_prc_8         = 0;
        $j_debit_prc_8        = 0;
        $genkin_prc_std       = 0; // 現金標準税率分  テーブル項目にはない
        $kogite_prc_std       = 0; // 小切手標準税率分  テーブル項目にはない
        $furikomi_prc_std     = 0; // 振込標準税率分  テーブル項目にはない
        $credit_prc_std       = 0;
        $j_debit_prc_std      = 0;
        for ( $i=1 ; $i <= 4 ; $i++ ) {
            $kin = ${"kin_$i"};
            if ( $kin == 0 ) continue;
            $way = ${"way_$i"}; // 領収方法
            if ( $way == 2 ) {        // 2:現金
                $genkin   += $kin;
            } else if ( $way == 3 ) { // 3:小切手
                $kogite   += $kin;
            } else if ( $way == 1 ) { // 1:振込
                $furikomi += $kin;
            } else if ( $way == 4 ) { // 4:クレジットカード/デビットカード
                $credit   += $kin;
            } else if ( $way == 5 ) { // 5:ジェイデビットカード
                $j_debit  += $kin;
            } else {
                throw new Exception(sprintf("領収方法不正(%d)", $cnt));
            }
            // 請求残ゼロの場合、税対象毎の配分は後で設定する cf.(*1)
            if ( ! $this->_isZanZero ) { // 請求残ゼロでない場合
                $zei_cd = ${"zei_$i"}; // 税CD
                list($zei_rtu, $zei_hasu_kbn, $reduced_tax_rate) = DataMapper_ZeiMstEasy::getRtuAndEtc($zei_cd);
                if ( $zei_rtu == 0 ) {                  // 非課税
                    if      ( $way == 2 ) { $genkin_prc_hikazei   += $kin; }
                    else if ( $way == 3 ) { $kogite_prc_hikazei   += $kin; }
                    else if ( $way == 1 ) { $furikomi_prc_hikazei += $kin; }
                    else if ( $way == 4 ) { $credit_prc_hikazei   += $kin; }
                    else if ( $way == 5 ) { $j_debit_prc_hikazei  += $kin; }
                } else if ( $reduced_tax_rate == 2 ) {  // 軽減税率
                    if      ( $way == 2 ) { $genkin_prc_keigen   += $kin; }
                    else if ( $way == 3 ) { $kogite_prc_keigen   += $kin; }
                    else if ( $way == 1 ) { $furikomi_prc_keigen += $kin; }
                    else if ( $way == 4 ) { $credit_prc_keigen   += $kin; }
                    else if ( $way == 5 ) { $j_debit_prc_keigen  += $kin; }
                } else if ( $zei_rtu == 3 ) {
                    if      ( $way == 2 ) { $genkin_prc_3   += $kin; }
                    else if ( $way == 3 ) { $kogite_prc_3   += $kin; }
                    else if ( $way == 1 ) { $furikomi_prc_3 += $kin; }
                    else if ( $way == 4 ) { $credit_prc_3   += $kin; }
                    else if ( $way == 5 ) { $j_debit_prc_3  += $kin; }
                } else if ( $zei_rtu == 5 ) {
                    if      ( $way == 2 ) { $genkin_prc_5   += $kin; }
                    else if ( $way == 3 ) { $kogite_prc_5   += $kin; }
                    else if ( $way == 1 ) { $furikomi_prc_5 += $kin; }
                    else if ( $way == 4 ) { $credit_prc_5   += $kin; }
                    else if ( $way == 5 ) { $j_debit_prc_5  += $kin; }
                } else if ( $zei_rtu == 8 ) {
                    if      ( $way == 2 ) { $genkin_prc_8   += $kin; }
                    else if ( $way == 3 ) { $kogite_prc_8   += $kin; }
                    else if ( $way == 1 ) { $furikomi_prc_8 += $kin; }
                    else if ( $way == 4 ) { $credit_prc_8   += $kin; }
                    else if ( $way == 5 ) { $j_debit_prc_8  += $kin; }
                } else { // 標準税率
                    if      ( $way == 2 ) { $genkin_prc_std   += $kin; }
                    else if ( $way == 3 ) { $kogite_prc_std   += $kin; }
                    else if ( $way == 1 ) { $furikomi_prc_std += $kin; }
                    else if ( $way == 4 ) { $credit_prc_std   += $kin; }
                    else if ( $way == 5 ) { $j_debit_prc_std  += $kin; }
                }
            }
        }
        if ( $this->_isZanZero ) { // (*1)請求残ゼロの場合
            // すべて現金扱いで設定してしまう. 領収方法毎の表示がないことを前提としている
            $genkin_prc_std     = $zan_std;
            $genkin_prc_hikazei = $zan_hikazei;
            $genkin_prc_keigen  = $zan_keigen;
            $genkin_prc_3       = $zan_3;
            $genkin_prc_5       = $zan_5;
            $genkin_prc_8       = $zan_8;
        }
        // 税対象毎の残額を超えていないことをチェック
        if($data['hako_kbn'] != '1'){
            if ( ! $this->_isZanZero ) {
                $sum_zan_std = $genkin_prc_std + $kogite_prc_std + $furikomi_prc_std + $credit_prc_std + $j_debit_prc_std;
                if ( $zan_std < $sum_zan_std ) {
                    throw new Exception(sprintf("標準税率分の入金額(%s)が残高(%s)を超えています",
                                                Msi_Sys_Utils::filterComma($sum_zan_std), Msi_Sys_Utils::filterComma($zan_std)));
                }
                $sum_zan_keigen = $genkin_prc_keigen + $kogite_prc_keigen + $furikomi_prc_keigen + $credit_prc_keigen + $j_debit_prc_keigen;
                if ( $zan_keigen < $sum_zan_keigen ) {
                    throw new Exception(sprintf("軽減税率分の入金額(%s)が残高(%s)を超えています",
                                                Msi_Sys_Utils::filterComma($sum_zan_keigen), Msi_Sys_Utils::filterComma($zan_keigen)));
                }
                $sum_zan_hikazei = $genkin_prc_hikazei + $kogite_prc_hikazei + $furikomi_prc_hikazei + $credit_prc_hikazei + $j_debit_prc_hikazei;
                if ( $zan_hikazei < $sum_zan_hikazei ) {
                    throw new Exception(sprintf("非課税分の入金額(%s)が残高(%s)を超えています",
                                                Msi_Sys_Utils::filterComma($sum_zan_hikazei), Msi_Sys_Utils::filterComma($zan_hikazei)));
                }
            }
        }
        // 税対象毎の税額設定
        if ( false ) { 
            // 以下は、過去分を含めた税額合計を請求書税額と一致させるための端数調整をしている
            if ( $this->_isZanZero ) { // 全額支払い済
                $zei_prc_std    = 0; // 税額標準税率分
                $zei_prc_3      = 0; // 税額標準税率分
                $zei_prc_5      = 0; // 税額標準税率分
                $zei_prc_8      = 0; // 税額標準税率分
                $zei_prc_keigen = 0; // 税額軽減税率分
                foreach ( $uchiwake as $uchi00 ) {
                    if ( $uchi00['zei_rtu'] == 0 ) continue;
                    if ( $uchi00['reduced_tax_rate'] == 2 ) {
                        $zei_prc_keigen = +$uchi00['zei_gaku'] - $uchi00['zei_before'];
                    } else {
                        $zei_prc_std = +$uchi00['zei_gaku'] - $uchi00['zei_before'];
                    }
                }
            } else { // 未払いあり
                $zei_prc_std    = 0; // 税額標準税率分
                $zei_prc_3      = 0; // 税額標準税率分
                $zei_prc_5      = 0; // 税額標準税率分
                $zei_prc_8      = 0; // 税額標準税率分
                $zei_prc_keigen = 0; // 税額軽減税率分
                $konkai_std    = +$genkin_prc_std    + $kogite_prc_std    + $furikomi_prc_std    + $credit_prc_std    + $j_debit_prc_std;    // 今回入金 標準税率分
                $konkai_3      = +$genkin_prc_3      + $kogite_prc_3      + $furikomi_prc_3      + $credit_prc_3      + $j_debit_prc_3;    // 今回入金 標準税率分
                $konkai_5      = +$genkin_prc_5      + $kogite_prc_5      + $furikomi_prc_5      + $credit_prc_5      + $j_debit_prc_5;    // 今回入金 標準税率分
                $konkai_8      = +$genkin_prc_8      + $kogite_prc_8     + $furikomi_prc_8      + $credit_prc_8      + $j_debit_prc_8;    // 今回入金 標準税率分
                $konkai_keigen = +$genkin_prc_keigen + $kogite_prc_keigen + $furikomi_prc_keigen + $credit_prc_keigen + $j_debit_prc_keigen; // 今回入金 軽減税率分
                foreach ( $uchiwake as $uchi00 ) {
                    if ( $uchi00['reduced_tax_rate'] == 2 ) {
                        $kinTotal = +$uchi00['taisho_gaku'];
                        $zeiTotal = +$uchi00['zei_gaku'];
                        $konkaiGaku = +$konkai_keigen;
                        $zei_hasu_kbn = DataMapper_ZeiMstEasy::getHasuKbn($uchi00['zei_cd']);
                        $zei_rtu  = +$uchi00['zei_rtu'];
                        $TaxPrc = App_ClsTaxLib::CalcTax($konkaiGaku, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                        $tax = $TaxPrc['ZeiPrc'];
                        $zei_prc_keigen = $tax;
                    } else if($uchi00['zei_rtu'] == 3){
                        $kinTotal = +$uchi00['taisho_gaku'];
                        $zeiTotal = +$uchi00['zei_gaku'];
                        $konkaiGaku = +$konkai_3;
                        $zei_hasu_kbn = DataMapper_ZeiMstEasy::getHasuKbn($uchi00['zei_cd']);
                        $zei_rtu  = +$uchi00['zei_rtu'];
                        $TaxPrc = App_ClsTaxLib::CalcTax($konkaiGaku, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                        $tax = $TaxPrc['ZeiPrc'];
                        $zei_prc_3 = $tax;
                    } else if($uchi00['zei_rtu'] == 5){
                        $kinTotal = +$uchi00['taisho_gaku'];
                        $zeiTotal = +$uchi00['zei_gaku'];
                        $konkaiGaku = +$konkai_5;
                        $zei_hasu_kbn = DataMapper_ZeiMstEasy::getHasuKbn($uchi00['zei_cd']);
                        $zei_rtu  = +$uchi00['zei_rtu'];
                        $TaxPrc = App_ClsTaxLib::CalcTax($konkaiGaku, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                        $tax = $TaxPrc['ZeiPrc'];
                        $zei_prc_5 = $tax;
                    } else if($uchi00['zei_rtu'] == 8){
                        $kinTotal = +$uchi00['taisho_gaku'];
                        $zeiTotal = +$uchi00['zei_gaku'];
                        $konkaiGaku = +$konkai_8;
                        $zei_hasu_kbn = DataMapper_ZeiMstEasy::getHasuKbn($uchi00['zei_cd']);
                        $zei_rtu  = +$uchi00['zei_rtu'];
                        $TaxPrc = App_ClsTaxLib::CalcTax($konkaiGaku, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                        $tax = $TaxPrc['ZeiPrc'];
                        $zei_prc_8 = $tax;
                    } else {
                        $kinTotal = +$uchi00['taisho_gaku'];
                        $zeiTotal = +$uchi00['zei_gaku'];
                        $konkaiGaku = +$konkai_std;
                        $zei_hasu_kbn = DataMapper_ZeiMstEasy::getHasuKbn($uchi00['zei_cd']);
                        $zei_rtu  = +$uchi00['zei_rtu'];
                        $TaxPrc = App_ClsTaxLib::CalcTax($konkaiGaku, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                        $tax = $TaxPrc['ZeiPrc'];
                        $zei_prc_std = $tax;
                    }
                }
            }
        } else {
            // 素朴に内税として設定
            $zei_prc_std    = 0; // 税額標準税率分
            $zei_prc_3      = 0; // 税額標準税率分
            $zei_prc_5      = 0; // 税額標準税率分
            $zei_prc_8      = 0; // 税額標準税率分
            $zei_prc_keigen = 0; // 税額軽減税率分
            $konkai_std     = +$genkin_prc_std    + $kogite_prc_std    + $furikomi_prc_std    + $credit_prc_std    + $j_debit_prc_std;    // 今回入金 標準税率分
            $konkai_3       = +$genkin_prc_3      + $kogite_prc_3      + $furikomi_prc_3      + $credit_prc_3      + $j_debit_prc_3;      // 今回入金 標準税率分
            $konkai_5       = +$genkin_prc_5      + $kogite_prc_5      + $furikomi_prc_5      + $credit_prc_5      + $j_debit_prc_5;      // 今回入金 標準税率分
            $konkai_8       = +$genkin_prc_8      + $kogite_prc_8      + $furikomi_prc_8      + $credit_prc_8      + $j_debit_prc_8;      // 今回入金 標準税率分
            $konkai_keigen  = +$genkin_prc_keigen + $kogite_prc_keigen + $furikomi_prc_keigen + $credit_prc_keigen + $j_debit_prc_keigen; // 今回入金 軽減税率分
            foreach ( $uchiwake as $uchi00 ) {
                if ( $uchi00['zei_rtu'] == 0 ) continue;
                $zei_hasu_kbn = DataMapper_ZeiMstEasy::getHasuKbn($uchi00['zei_cd']);
                $zei_rtu  = +$uchi00['zei_rtu'];
                $zei_gaku = +$uchi00['zei_gaku'];
                if ( $uchi00['reduced_tax_rate'] == 2 ) {
                    $TaxPrc = App_ClsTaxLib::CalcTax($konkai_keigen, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                    $tax = $TaxPrc['ZeiPrc'];
                    $zei_prc_keigen = $tax;
                } else if ( $uchi00['zei_rtu'] == 3 ) {
                    $TaxPrc = App_ClsTaxLib::CalcTax($konkai_3, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                    $tax = $TaxPrc['ZeiPrc'];
                    $zei_prc_3 = $tax;
                } else if ( $uchi00['zei_rtu'] == 5 ) {
                    $TaxPrc = App_ClsTaxLib::CalcTax($konkai_5, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                    $tax = $TaxPrc['ZeiPrc'];
                    $zei_prc_5 = $tax;
                } else if ( $uchi00['zei_rtu'] == 8 ) {
                    $TaxPrc = App_ClsTaxLib::CalcTax($konkai_8, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                    $tax = $TaxPrc['ZeiPrc'];
                    $zei_prc_8 = $tax;
                } else {
                    $TaxPrc = App_ClsTaxLib::CalcTax($konkai_std, 1, $zei_rtu, $zei_hasu_kbn); // 1(内税)で計算
                    $tax = $TaxPrc['ZeiPrc'];
                    $zei_prc_std = $tax;
                }
            }
        }
        $table_name = "ryosyusho_history";
        $seikyu_den_no = $seikyuRec['seikyu_den_no'];
        if(!isset($seikyuRec['seikyu_den_no'])){    // 内金
            $seikyu_den_no = $seikyuRec['uri_den_no'];
            $arrRyoshu = DataMapper_RyosyushoHistory::findUC($db, array('uri_den_no' => $seikyu_den_no));
            $table_name = "ryosyusho_history_uc";
        }else{
            $arrRyoshu = DataMapper_RyosyushoHistory::find($db, array('uri_den_no' => $seikyu_den_no));
        }
        $hako_count    = count($arrRyoshu);
        $ryosyusho_no  = null;
        $ryosyusho_eda_no = 0; // 領収証枝番号
        // 領収証番号発行
        if(isset($data['ryosyu_no']) && strlen($data['ryosyu_no']) > 0){
            $ryosyusho_no = $data['ryosyu_no'];
        }else{
            $ryosyusho_no = App_ClsGetCodeNo::GetCodeNo($db, $table_name, 'ryosyusho_no', Msi_Sys_Utils::getDatetimeStd());        
        }
        // 発行回数取得
        $cond =array(
                '__etc_limit'   => 1,
                '__etc_orderby' => array('hako_count DESC')
        );
        $cond['uri_den_no'] = $seikyu_den_no;
        if(!isset($seikyuRec['seikyu_den_no'])){    // 内金
            $history1Ary = DataMapper_RyosyushoHistory::findUC($db, $cond);
            $history1 = null;
            if(Msi_Sys_Utils::myCount($history1Ary) > 0){
                $history1 = $history1Ary[0];
            }
        }else{
            $history1 = DataMapper_RyosyushoHistory::findOne($db, $cond);
        }
        if(Msi_Sys_Utils::myCount($history1) > 0) {
            $hako_count = $history1['hako_count'] + 1;
        }
        // 領収証枝番号取得
        $cond2 =array(
                '__etc_limit'   => 1,
                '__etc_orderby' => array('ryosyu_no_sub DESC')
        );
        $cond2['uri_den_no']   = $seikyu_den_no;
        $cond2['ryosyusho_no'] = $ryosyusho_no;
        if(!isset($seikyuRec['seikyu_den_no'])){    // 内金
            $history2Ary = DataMapper_RyosyushoHistory::findUC($db, $cond2);
            $history2 = null;
            if(Msi_Sys_Utils::myCount($history2Ary) > 0){
                $history2 = $history2Ary[0];
            }
        }else{
            $history2 = DataMapper_RyosyushoHistory::findOne($db, $cond2);
        }
        if(Msi_Sys_Utils::myCount($history2) > 0) {
            $ryosyusho_eda_no   = $history2['ryosyusho_eda_no'] + 1;
        }
        // 印紙税金額
        $inshi_kbn = 1;
        $inshi_prc = $data['inshi_prc'];    // 印紙税
        $history = array();
        $history['ryosyusho_no']         = $ryosyusho_no;
        $history['ryosyusho_eda_no']     = $ryosyusho_eda_no;
        $history['ryosyu_no']            = $ryosyusho_no;
        $history['ryosyu_no_sub']        = $ryosyusho_eda_no;
        $history['hako_count']           = $hako_count;
        $history['uri_den_no']           = $seikyu_den_no;
        $history['hako_count']           = $hako_count;
        $history['data_kbn']             = $seikyuRec['data_kbn'];
        $history['seko_no']              = $seikyuRec['seko_no'];
        $history['seko_no_sub']          = $seikyuRec['seko_no_sub'];
        $history['hako_kbn']             = $data['hako_kbn'];
        $history['hako_date']            = Msi_Sys_Utils::getDatetimeStd();    // 発行日時
        $history['haki_date']            = null;                    // 破棄日時
        $history['disp_kbn']             = $data['disp_kbn'];       // 再発行文字印刷区分
        $history['atena']                = $data['atena'];
        $history['genkin_prc']           = $genkin;
        $history['kogite_prc']           = $kogite;
        $history['furikomi_prc']         = $furikomi;
        $history['credit_prc']           = $credit;
        $history['j_debit_prc']          = $j_debit;
        $history['gokei_prc']            = $gokei;
        $history['tadashikaki_cd']       = $data['tadashikaki_cd']; // 但し書きコード
        $history['tadashikaki']          = $data['tadashikaki'];
        $history['biko']                 = $data['biko'];
        $history['syukin_tanto_cd']      = $data['syukin_tanto_cd'];
        $history['jimu_tanto_cd']        = $data['jimu_tanto_cd'];
        $history['delete_flg']           = 0;                       // 削除フラグ
        $history['genkin_prc_keigen']    = $genkin_prc_keigen;      // 現金軽減税率分
        $history['kogite_prc_keigen']    = $kogite_prc_keigen;      // 小切手軽減税率分
        $history['furikomi_prc_keigen']  = $furikomi_prc_keigen;    // 振込軽減税率分
        $history['credit_prc_keigen']    = $credit_prc_keigen;
        $history['j_debit_prc_keigen']   = $j_debit_prc_keigen;
        $history['genkin_prc_hikazei']   = $genkin_prc_hikazei;     // 現金非課税分
        $history['kogite_prc_hikazei']   = $kogite_prc_hikazei;     // 小切手非課税分
        $history['furikomi_prc_hikazei'] = $furikomi_prc_hikazei;   // 振込非課税分
        $history['furikomi_prc_hikazei'] = $furikomi_prc_hikazei;
        $history['credit_prc_hikazei']   = $credit_prc_hikazei;
        $history['j_debit_prc_hikazei']  = $j_debit_prc_hikazei;
        $history['genkin_prc_3']         = $genkin_prc_3;     // 現金非課税分
        $history['kogite_prc_3']         = $kogite_prc_3;     // 小切手非課税分
        $history['furikomi_prc_3']       = $furikomi_prc_3;   // 振込非課税分
        $history['furikomi_prc_3']       = $furikomi_prc_3;
        $history['credit_prc_3']         = $credit_prc_3;
        $history['j_debit_prc_3']        = $j_debit_prc_3;
        $history['genkin_prc_5']         = $genkin_prc_5;     // 現金非課税分
        $history['kogite_prc_5']         = $kogite_prc_5;     // 小切手非課税分
        $history['furikomi_prc_5']       = $furikomi_prc_5;   // 振込非課税分
        $history['furikomi_prc_5']       = $furikomi_prc_5;
        $history['credit_prc_5']         = $credit_prc_5;
        $history['j_debit_prc_5']        = $j_debit_prc_5;
        $history['genkin_prc_8']         = $genkin_prc_8;     // 現金非課税分
        $history['kogite_prc_8']         = $kogite_prc_8;     // 小切手非課税分
        $history['furikomi_prc_8']       = $furikomi_prc_8;   // 振込非課税分
        $history['furikomi_prc_8']       = $furikomi_prc_8;
        $history['zei_prc_std']          = $zei_prc_std;            // 税額標準税率分
        $history['zei_prc_3']            = $zei_prc_3;              // 税額標準税率分
        $history['zei_prc_5']            = $zei_prc_5;              // 税額標準税率分
        $history['zei_prc_8']            = $zei_prc_8;              // 税額標準税率分
        $history['zei_prc_keigen']       = $zei_prc_keigen;         // 税額軽減税率分
        $history['inshi_zei_kbn']        = $inshi_kbn;
        $history['inshi_zei_prc']        = $inshi_prc;
        // 発行部門分
        $inshi_type_cd = null;
        $inshi_type = DataMapper_CodeNmMst::findOne($db, array('code_kbn'=> '8551', 'kbn_value_snm' => 10, 'kbn_value_lnm' => $seikyuRec['bumon_cd']));
        if(Msi_Sys_Utils::myCount($inshi_type) > 0){
            $inshi_type_cd = $inshi_type['kbn_value_cd'];
        }
        $history['inshi_type_cd']        = $inshi_type_cd;
        list($sql, $param) = DataMapper_Utils::makeInsertSQL($table_name, $history);
        $cnt = $db->easyExecute($sql, $param);
        $this->_hako_count_saved = $hako_count;
    }
    /**
     * 最終データ破棄処理
     *
     * <AUTHOR> Mihara
     * @since  2015/06/xx
     * @param  array  $data
     * @return void
     */
    protected function _cancelLast($db, $data = array()) {
        $cnt = 0;
        $seikyuRec = $this->_seikyuRec;
        $seikyu_den_no = $seikyuRec['seikyu_den_no'];
        $table_name = "ryosyusho_history";
        if(!isset($seikyuRec['seikyu_den_no']) && !isset($seikyuRec['uri_den_no'])){
            $table_name = "ryosyusho_history_uc";
            $seikyu_den_no = $seikyuRec['juchu_den_no'];
        }else if(!isset($seikyuRec['seikyu_den_no'])){    // 内金
            $table_name = "ryosyusho_history_uc";
            $seikyu_den_no = $seikyuRec['uri_den_no'];
        }
        foreach ($data as $value) {
            if(!isset($seikyuRec['seikyu_den_no'])){    // 内金
                $lastRyoshuAry = DataMapper_RyosyushoHistory::findUC($db, array('ryosyusho_no' => $value['ryosyu_no'], 'ryosyusho_eda_no' => $value['ryosyu_eda_no'],
                            '__raw_1' => 'hako_kbn NOT IN (9)',
                            '__etc_limit' => 1,
                            '__etc_orderby' => array('hako_count DESC')));
                if (Msi_Sys_Utils::myCount($lastRyoshuAry) == 0) {
                    throw new Exception(sprintf('破棄する領収書発行データがありません'));
                }
                $lastRyoshu = $lastRyoshuAry[0];
            }else{
                $lastRyoshu = DataMapper_RyosyushoHistory::findOne($db, array('ryosyusho_no' => $value['ryosyu_no'], 'ryosyusho_eda_no' => $value['ryosyu_eda_no'],
                            '__raw_1' => 'hako_kbn NOT IN (9)',
                            '__etc_limit' => 1,
                            '__etc_orderby' => array('hako_count DESC')));
                if ($lastRyoshu === null) {
                    throw new Exception(sprintf('破棄する領収書発行データがありません'));
                }
            }
            // ログイン担当者
            $tanto_nm = App_Utils::getTantoNm();        
            $biko2 = "破棄：".$tanto_nm;
            $cnt += $db->easyExecute(<<< END_OF_SQL
    UPDATE $table_name
       SET hako_kbn  = 9, 
           haki_date = CURRENT_TIMESTAMP, 
           biko2     = :biko2
     WHERE uri_den_no       = :seikyu_den_no
       AND hako_count       = :hako_count
       AND ryosyusho_no     = :ryosyusho_no
       AND ryosyusho_eda_no = :ryosyusho_eda_no
       AND delete_flg       = 0
END_OF_SQL
            , array(
                'seikyu_den_no'    => $seikyu_den_no,
                'hako_count'       => $lastRyoshu['hako_count'],
                'ryosyusho_no'     => $value['ryosyu_no'],
                'ryosyusho_eda_no' => $value['ryosyu_eda_no'],
                'biko2'            => $biko2,
            ));            
        }
        return $cnt;
    }
    /**
     * 軽減税率対応 調整処理
     *
     * <AUTHOR> Mihara
     * @since  2015/06/xx
     * @params array   $data
     * @return void
     */
    protected function _keigenAdj(&$data) {
        $db = Msi_Sys_DbManager::getMyDb();
        $dataApp = &$data['dataApp'];
        // 税別の内訳
        $uchiwake = $this->_getMyUchiwake($this->_seikyuRec);
        // $dataApp に設定
        $dataApp['_uchiwakeData'] = $uchiwake;
        // 領収方法
        $sql = "
            SELECT 
               kbn_value_cd_num AS id
              ,kbn_value_lnm    AS text
            FROM  code_nm_mst
            WHERE 
                code_kbn     = '8526'
            AND delete_flg   = 0
            ORDER BY disp_nox
        ";
        $optWay = $db->easySelect($sql);
        // 税対象
        $optZei = array();
        foreach ($uchiwake as $rec00) {
            $zei_rtu = +$rec00['zei_rtu'];
            if ($zei_rtu == 0) {
                $text = '非課税';
                $zei_cd = 0;
            } else {
                $text = ($rec00['reduced_tax_rate'] == 2 ? '軽減' : '') . sprintf("%d％", $zei_rtu);
                // $text = sprintf("%d％対象額", $zei_rtu);
                $zei_cd = +$rec00['zei_cd'];
            }
            $_data = array('id' => $zei_cd, 'text' => $text);
            $optZei[] = $_data;
        }
        // 選択肢データ
        $optData = array('opt_zei' => $optZei, 'opt_way' => $optWay);
        // $dataApp に設定
        $dataApp['_optData'] = $optData;
    }

    /**
     * 軽減税率 内訳データ取得
     *
     * <AUTHOR> Mihara
     * @since  2019/05/17
     * @params array   $seikyuRec
     * @return array
     */
    protected function _getMyUchiwake($seikyuRec) {
        $db = Msi_Sys_DbManager::getMyDb();
        $seikyu_den_no = $seikyuRec['seikyu_den_no'];
        $uchiwake = array();
        if(!isset($seikyuRec['seikyu_den_no'])){    // 内金
            $uchiwake = App_KeigenUtils::getSeikyuShohizeiEasyRyoshuUc($db, $seikyuRec['uri_den_no'] ,$seikyuRec['data_kbn']);
        }else if(isset($seikyuRec['data_kbn'])){
            $uchiwake = App_KeigenUtils::getSeikyuShohizeiEasyRyoshu($db, $seikyu_den_no ,$seikyuRec['data_kbn']);
        }
        // 残高の設定
        $this->_getZanGakuByZei_cache = array(); // 一応クリア
        foreach ($uchiwake as $i => $rec) {
            if(isset($seikyuRec['seikyu_den_no'])){
                list($zan, $zei_before) = $this->_getZanGakuByZei($rec);
            }else{
                list($zan, $zei_before) = $this->_getZanGakuByZeiUchi($rec);
            }
            $uchiwake[$i]['taisho_zan'] = $zan;
            $uchiwake[$i]['zei_before'] = $zei_before;
        }
        $this->_getZanGakuByZei_cache = array();
        if(isset($seikyuRec['seikyu_den_no'])){
            // 内金領収証発行金額を加算
            $this->setRyosyuUcKingaku($db, $uchiwake);
        }        
        return $uchiwake;
    }
    protected $_getZanGakuByZei_cache = array();
    /**
     * 税対象タイプ毎の 請求残高 を返す
     *
     * <AUTHOR> Mihara
     * @since  2019/05/17
     * @params array   $seikyuRec
     * @return array   list(非課税分残高, 標準税率分残額, 軽減分残高)
     */
    protected function _getEachZanByType($seikyuRec) {
        $zan_std = 0;
        $zan_keigen = 0;
        $zan_hikazei = 0;
        $zan_3 = 0;
        $zan_5 = 0;
        $zan_8 = 0;
        $uchiwake = $this->_getMyUchiwake($seikyuRec);
        foreach ( $uchiwake as $rec ) {
            $zei_rtu          = +$rec['zei_rtu'];
            $reduced_tax_rate = +$rec['reduced_tax_rate'];
            $taisho_zan       = +$rec['taisho_zan'];
            if ( $zei_rtu == 0 ) {
                $zan_hikazei += $taisho_zan;
            } else if ( $reduced_tax_rate == 2 ) {
                $zan_keigen  += $taisho_zan;
            } else if ( $zei_rtu == 3 ) {
                $zan_3  += $taisho_zan;
            } else if ( $zei_rtu == 5 ) {
                $zan_5  += $taisho_zan;
            } else if ( $zei_rtu == 8 ) {
                $zan_8  += $taisho_zan;
            } else {
                $zan_std += $taisho_zan;
            }
        }
        $rtn = array( $zan_hikazei, $zan_std, $zan_keigen, $zan_3, $zan_5, $zan_8 );
        return $rtn;
    }
    /**
     * 軽減税率対応 ある税CDに対する請求残高を返す
     *
     * <AUTHOR> Mihara
     * @since  2019/05/17
     * @params array   $rec
     * @return array  list($zan, $zei_before)
     */
    protected function _getZanGakuByZei($pRec) {
        // 0:非課税 1:通常, 2:軽減
        $type = +$pRec['zei_rtu'] == 0 ? 0 : (+$pRec['reduced_tax_rate'] == 2 ? 2 : 1);
        $seikyu_den_no = $this->_seikyuRec['seikyu_den_no'];
        if (!array_key_exists($seikyu_den_no, $this->_getZanGakuByZei_cache)) {
            $db = Msi_Sys_DbManager::getMyDb();
            $sumRec = $db->easySelOne(<<< END_OF_SQL
SELECT 
     COALESCE(SUM(genkin_prc), 0)           AS sum_genkin_prc
    ,COALESCE(SUM(kogite_prc), 0)           AS sum_kogite_prc
    ,COALESCE(SUM(furikomi_prc), 0)         AS sum_furikomi_prc
    ,COALESCE(SUM(credit_prc), 0)           AS sum_credit_prc
    ,COALESCE(SUM(j_debit_prc), 0)          AS sum_j_debit_prc
    ,COALESCE(SUM(genkin_prc_std), 0)       AS sum_genkin_prc_std
    ,COALESCE(SUM(kogite_prc_std), 0)       AS sum_kogite_prc_std
    ,COALESCE(SUM(furikomi_prc_std), 0)     AS sum_furikomi_prc_std
    ,COALESCE(SUM(credit_prc_std), 0)       AS sum_credit_prc_std
    ,COALESCE(SUM(j_debit_prc_std), 0)      AS sum_j_debit_prc_std
    ,COALESCE(SUM(genkin_prc_3), 0)         AS sum_genkin_prc_3
    ,COALESCE(SUM(kogite_prc_3), 0)         AS sum_kogite_prc_3
    ,COALESCE(SUM(furikomi_prc_3), 0)       AS sum_furikomi_prc_3
    ,COALESCE(SUM(credit_prc_3), 0)         AS sum_credit_prc_3
    ,COALESCE(SUM(j_debit_prc_3), 0)        AS sum_j_debit_prc_3
    ,COALESCE(SUM(genkin_prc_5), 0)         AS sum_genkin_prc_5
    ,COALESCE(SUM(kogite_prc_5), 0)         AS sum_kogite_prc_5
    ,COALESCE(SUM(furikomi_prc_5), 0)       AS sum_furikomi_prc_5
    ,COALESCE(SUM(credit_prc_5), 0)         AS sum_credit_prc_5
    ,COALESCE(SUM(j_debit_prc_5), 0)        AS sum_j_debit_prc_5
    ,COALESCE(SUM(genkin_prc_8), 0)         AS sum_genkin_prc_8
    ,COALESCE(SUM(kogite_prc_8), 0)         AS sum_kogite_prc_8
    ,COALESCE(SUM(furikomi_prc_8), 0)       AS sum_furikomi_prc_8
    ,COALESCE(SUM(credit_prc_8), 0)         AS sum_credit_prc_8
    ,COALESCE(SUM(j_debit_prc_8), 0)        AS sum_j_debit_prc_8
    ,COALESCE(SUM(genkin_prc_keigen), 0)    AS sum_genkin_prc_keigen
    ,COALESCE(SUM(kogite_prc_keigen), 0)    AS sum_kogite_prc_keigen
    ,COALESCE(SUM(furikomi_prc_keigen), 0)  AS sum_furikomi_prc_keigen
    ,COALESCE(SUM(credit_prc_keigen), 0)    AS sum_credit_prc_keigen
    ,COALESCE(SUM(j_debit_prc_keigen), 0)   AS sum_j_debit_prc_keigen
    ,COALESCE(SUM(genkin_prc_hikazei), 0)   AS sum_genkin_prc_hikazei
    ,COALESCE(SUM(kogite_prc_hikazei), 0)   AS sum_kogite_prc_hikazei
    ,COALESCE(SUM(furikomi_prc_hikazei), 0) AS sum_furikomi_prc_hikazei
    ,COALESCE(SUM(credit_prc_hikazei), 0)   AS sum_credit_prc_hikazei
    ,COALESCE(SUM(j_debit_prc_hikazei), 0)  AS sum_j_debit_prc_hikazei
    ,COALESCE(SUM(zei_prc_std), 0)          AS sum_zei_prc_std
    ,COALESCE(SUM(zei_prc_3), 0)            AS sum_zei_prc_3
    ,COALESCE(SUM(zei_prc_5), 0)            AS sum_zei_prc_5
    ,COALESCE(SUM(zei_prc_8), 0)            AS sum_zei_prc_8
    ,COALESCE(SUM(zei_prc_keigen), 0)       AS sum_zei_prc_keigen
FROM (
SELECT 
     genkin_prc
    ,kogite_prc
    ,furikomi_prc
    ,credit_prc
    ,j_debit_prc
    ,COALESCE(genkin_prc, 0)   -COALESCE(genkin_prc_keigen,0)   -COALESCE(genkin_prc_hikazei, 0)   -COALESCE(genkin_prc_3, 0)   -COALESCE(genkin_prc_5, 0)   -COALESCE(genkin_prc_8, 0)   AS genkin_prc_std
    ,COALESCE(kogite_prc, 0)   -COALESCE(kogite_prc_keigen,0)   -COALESCE(kogite_prc_hikazei, 0)   -COALESCE(kogite_prc_3, 0)   -COALESCE(kogite_prc_5, 0)   -COALESCE(kogite_prc_8, 0)   AS kogite_prc_std
    ,COALESCE(furikomi_prc, 0) -COALESCE(furikomi_prc_keigen,0) -COALESCE(furikomi_prc_hikazei, 0) -COALESCE(furikomi_prc_3, 0) -COALESCE(furikomi_prc_5, 0) -COALESCE(furikomi_prc_8, 0) AS furikomi_prc_std
    ,COALESCE(credit_prc, 0)   -COALESCE(credit_prc_keigen,0)   -COALESCE(credit_prc_hikazei, 0)   -COALESCE(credit_prc_3, 0)   -COALESCE(credit_prc_5, 0)   -COALESCE(credit_prc_8, 0)   AS credit_prc_std
    ,COALESCE(j_debit_prc, 0)  -COALESCE(j_debit_prc_keigen,0)  -COALESCE(j_debit_prc_hikazei, 0)  -COALESCE(j_debit_prc_3, 0)  -COALESCE(j_debit_prc_5, 0)  -COALESCE(j_debit_prc_8, 0)  AS j_debit_prc_std
    ,genkin_prc_3
    ,kogite_prc_3
    ,furikomi_prc_3
    ,credit_prc_3
    ,j_debit_prc_3
    ,genkin_prc_5
    ,kogite_prc_5
    ,furikomi_prc_5
    ,credit_prc_5
    ,j_debit_prc_5
    ,genkin_prc_8
    ,kogite_prc_8
    ,furikomi_prc_8
    ,credit_prc_8
    ,j_debit_prc_8
    ,genkin_prc_keigen
    ,kogite_prc_keigen
    ,furikomi_prc_keigen
    ,credit_prc_keigen
    ,j_debit_prc_keigen
    ,genkin_prc_hikazei
    ,kogite_prc_hikazei
    ,furikomi_prc_hikazei
    ,credit_prc_hikazei
    ,j_debit_prc_hikazei
    ,zei_prc_std
    ,zei_prc_3
    ,zei_prc_5
    ,zei_prc_8
    ,zei_prc_keigen
  FROM ryosyusho_history
 WHERE delete_flg=0
   AND hako_kbn NOT IN (9)
   AND uri_den_no=:seikyu_den_no
)t0
END_OF_SQL
                    , array('seikyu_den_no' => $seikyu_den_no));
            $this->_getZanGakuByZei_cache[$seikyu_den_no] = $sumRec;
        }
        $sumRec = $this->_getZanGakuByZei_cache[$seikyu_den_no];
        if ($sumRec === null) {
            return array(0, 0, 0);
        }
        if ($pRec['zei_rtu'] == 0) { // 0:非課税
            $harai = +$sumRec['sum_genkin_prc_hikazei'] + $sumRec['sum_kogite_prc_hikazei'] + $sumRec['sum_furikomi_prc_hikazei'] + $sumRec['sum_credit_prc_hikazei'] + $sumRec['sum_j_debit_prc_hikazei'];
            $zei_before = 0;
        } else if ($pRec['reduced_tax_rate'] == 2) { // 2:軽減
            $harai = +$sumRec['sum_genkin_prc_keigen'] + $sumRec['sum_kogite_prc_keigen'] + $sumRec['sum_furikomi_prc_keigen'] + $sumRec['sum_credit_prc_keigen'] + $sumRec['sum_j_debit_prc_keigen'];
            $zei_before = +$sumRec['sum_zei_prc_keigen'];
        } else if ($pRec['zei_rtu'] == 3) {
            $harai = +$sumRec['sum_genkin_prc_3'] + $sumRec['sum_kogite_prc_3'] + $sumRec['sum_furikomi_prc_3'] + $sumRec['sum_credit_prc_3'] + $sumRec['sum_j_debit_prc_3'];
            $zei_before = +$sumRec['sum_zei_prc_3'];
        } else if ($pRec['zei_rtu'] == 5) {
            $harai = +$sumRec['sum_genkin_prc_5'] + $sumRec['sum_kogite_prc_5'] + $sumRec['sum_furikomi_prc_5'] + $sumRec['sum_credit_prc_5'] + $sumRec['sum_j_debit_prc_5'];
            $zei_before = +$sumRec['sum_zei_prc_5'];
        } else if ($pRec['zei_rtu'] == 8) {
            $harai = +$sumRec['sum_genkin_prc_8'] + $sumRec['sum_kogite_prc_8'] + $sumRec['sum_furikomi_prc_8'] + $sumRec['sum_credit_prc_8'] + $sumRec['sum_j_debit_prc_8'];
            $zei_before = +$sumRec['sum_zei_prc_8'];
        } else {
            $harai = +$sumRec['sum_genkin_prc_std'] + $sumRec['sum_kogite_prc_std'] + $sumRec['sum_furikomi_prc_std'] + $sumRec['sum_credit_prc_std'] + $sumRec['sum_j_debit_prc_std'];
            $zei_before = +$sumRec['sum_zei_prc_std'];
        }
        $zan = +$pRec['taisho_gaku'] - $harai;
        $rtn = array($zan, $zei_before);
        return $rtn;
    }
    /**
     * 軽減税率対応 ある税CDに対する請求残高を返す
     *
     * <AUTHOR> Mihara
     * @since  2019/05/17
     * @params array   $rec
     * @return array  list($zan, $zei_before)
     */
    protected function _getZanGakuByZeiUchi($pRec) {
        // 0:非課税 1:通常, 2:軽減
        $type = +$pRec['zei_rtu'] == 0 ? 0 : (+$pRec['reduced_tax_rate'] == 2 ? 2 : 1);
        $uri_den_no = $this->_seikyuRec['uri_den_no'];
        if (!array_key_exists($uri_den_no, $this->_getZanGakuByZei_cache)) {
            $db = Msi_Sys_DbManager::getMyDb();
            $sumRec = $db->easySelOne(<<< END_OF_SQL
SELECT 
     COALESCE(SUM(genkin_prc), 0)           AS sum_genkin_prc
    ,COALESCE(SUM(kogite_prc), 0)           AS sum_kogite_prc
    ,COALESCE(SUM(furikomi_prc), 0)         AS sum_furikomi_prc
    ,COALESCE(SUM(credit_prc), 0)           AS sum_credit_prc
    ,COALESCE(SUM(j_debit_prc), 0)          AS sum_j_debit_prc
    ,COALESCE(SUM(genkin_prc_std), 0)       AS sum_genkin_prc_std
    ,COALESCE(SUM(kogite_prc_std), 0)       AS sum_kogite_prc_std
    ,COALESCE(SUM(furikomi_prc_std), 0)     AS sum_furikomi_prc_std
    ,COALESCE(SUM(credit_prc_std), 0)       AS sum_credit_prc_std
    ,COALESCE(SUM(j_debit_prc_std), 0)      AS sum_j_debit_prc_std
    ,COALESCE(SUM(genkin_prc_keigen), 0)    AS sum_genkin_prc_keigen
    ,COALESCE(SUM(kogite_prc_keigen), 0)    AS sum_kogite_prc_keigen
    ,COALESCE(SUM(furikomi_prc_keigen), 0)  AS sum_furikomi_prc_keigen
    ,COALESCE(SUM(credit_prc_keigen), 0)    AS sum_credit_prc_keigen
    ,COALESCE(SUM(j_debit_prc_keigen), 0)   AS sum_j_debit_prc_keigen
    ,COALESCE(SUM(genkin_prc_hikazei), 0)   AS sum_genkin_prc_hikazei
    ,COALESCE(SUM(kogite_prc_hikazei), 0)   AS sum_kogite_prc_hikazei
    ,COALESCE(SUM(furikomi_prc_hikazei), 0) AS sum_furikomi_prc_hikazei
    ,COALESCE(SUM(credit_prc_hikazei), 0)   AS sum_credit_prc_hikazei
    ,COALESCE(SUM(j_debit_prc_hikazei), 0)  AS sum_j_debit_prc_hikazei
    ,COALESCE(SUM(zei_prc_std), 0)          AS sum_zei_prc_std
    ,COALESCE(SUM(zei_prc_keigen), 0)       AS sum_zei_prc_keigen
FROM (
SELECT 
     genkin_prc
    ,kogite_prc
    ,furikomi_prc
    ,credit_prc
    ,j_debit_prc
    ,COALESCE(genkin_prc, 0)   -COALESCE(genkin_prc_keigen,0)   -COALESCE(genkin_prc_hikazei, 0)   AS genkin_prc_std
    ,COALESCE(kogite_prc, 0)   -COALESCE(kogite_prc_keigen,0)   -COALESCE(kogite_prc_hikazei, 0)   AS kogite_prc_std
    ,COALESCE(furikomi_prc, 0) -COALESCE(furikomi_prc_keigen,0) -COALESCE(furikomi_prc_hikazei, 0) AS furikomi_prc_std
    ,COALESCE(credit_prc, 0)   -COALESCE(credit_prc_keigen,0)   -COALESCE(credit_prc_hikazei, 0)   AS credit_prc_std
    ,COALESCE(j_debit_prc, 0)  -COALESCE(j_debit_prc_keigen,0)  -COALESCE(j_debit_prc_hikazei, 0)  AS j_debit_prc_std
    ,genkin_prc_keigen
    ,kogite_prc_keigen
    ,furikomi_prc_keigen
    ,credit_prc_keigen
    ,j_debit_prc_keigen
    ,genkin_prc_hikazei
    ,kogite_prc_hikazei
    ,furikomi_prc_hikazei
    ,credit_prc_hikazei
    ,j_debit_prc_hikazei
    ,zei_prc_std
    ,zei_prc_keigen
  FROM ryosyusho_history_uc
 WHERE delete_flg=0
   AND hako_kbn NOT IN (9)
   AND uri_den_no=:uri_den_no
)t0
END_OF_SQL
                    , array('uri_den_no' => $uri_den_no));
            $this->_getZanGakuByZei_cache[$uri_den_no] = $sumRec;
        }
        $sumRec = $this->_getZanGakuByZei_cache[$uri_den_no];
        if ($sumRec === null) {
            return array(0, 0, 0);
        }
        if ($type == 0) { // 0:非課税
            $harai = +$sumRec['sum_genkin_prc_hikazei'] + $sumRec['sum_kogite_prc_hikazei'] + $sumRec['sum_furikomi_prc_hikazei'] + $sumRec['sum_credit_prc_hikazei'] + $sumRec['sum_j_debit_prc_hikazei'];
            $zei_before = 0;
        } else if ($type == 1) { // 1:通常
            $harai = +$sumRec['sum_genkin_prc_std'] + $sumRec['sum_kogite_prc_std'] + $sumRec['sum_furikomi_prc_std'] + $sumRec['sum_credit_prc_std'] + $sumRec['sum_j_debit_prc_std'];
            $zei_before = +$sumRec['sum_zei_prc_std'];
        } else if ($type == 2) { // 2:軽減
            $harai = +$sumRec['sum_genkin_prc_keigen'] + $sumRec['sum_kogite_prc_keigen'] + $sumRec['sum_furikomi_prc_keigen'] + $sumRec['sum_credit_prc_keigen'] + $sumRec['sum_j_debit_prc_keigen'];
            $zei_before = +$sumRec['sum_zei_prc_keigen'];
        } else {
            $harai = 0; // never
            $zei_before = 0;
        }
        $zan = +$pRec['taisho_gaku'] - $harai;
        $rtn = array($zan, $zei_before);
        return $rtn;
    }
    /**
     * 
     * 領収証名義取得
     * 
     * @param type $db
     * @param type $seko_no
     * @return type
     */
    private function getRyosyuMeigi($db, $seko_no){
        $ryosyu_meigi = "";
        $select = $db->easySelOne(<<< END_OF_SQL
    SELECT 
         ski.seko_no
        ,ssi.ryosyu_meigi
    FROM seko_kihon_info ski
    LEFT JOIN sekyu_saki_info ssi
    ON ssi.sekyu_cd    = ski.sekyu_cd
    AND ssi.delete_flg = 0
    WHERE ski.seko_no  = :seko_no     
END_OF_SQL
                    , array('seko_no' => $seko_no));
        if(Msi_Sys_Utils::myCount($select) > 0) {
            $ryosyu_meigi = $select['ryosyu_meigi'];
        }
        return $ryosyu_meigi;
    }
    /**
     * 
     * 領収証名義(喪家外供花供物)取得
     * 
     * @param type $db
     * @param type $seikyu_den_no
     * @param type $uri_den_no
     * @param type $ryosyu_meigi
     * @return type
     */
    private function getMokegaiRyosyuMeigi($db, $seikyu_den_no, $uri_den_no, $ryosyu_meigi){
        if(isset($uri_den_no)){
            $select = $db->easySelOne(<<< END_OF_SQL
                SELECT
                     juchu.ryosyusyo_meigi
                    ,uriage.juchusaki_kbn
                FROM uriage_denpyo uriage
                INNER JOIN juchu_sekyu_saki_info juchu
                ON  juchu.denpyo_no  = uriage.denpyo_no
                AND juchu.delete_flg = 0
                WHERE 
                    uriage.uri_den_no    = :uri_den_no
                AND uriage.data_kbn      = 4
                AND uriage.juchusaki_kbn = 2
                AND uriage.delete_flg    = 0
END_OF_SQL
                    , array('uri_den_no' => $uri_den_no));
            if(Msi_Sys_Utils::myCount($select) > 0) {
                $ryosyu_meigi = $select['ryosyusyo_meigi'];
            }
        }else{
            $select = $db->easySelOne(<<< END_OF_SQL
                SELECT
                     juchu.ryosyusyo_meigi
                    ,seikyu.juchusaki_kbn
                FROM seikyu_denpyo seikyu
                INNER JOIN uriage_denpyo uriage
                ON  uriage.uri_den_no = seikyu.uri_den_no
                AND uriage.delete_flg = 0
                INNER JOIN juchu_sekyu_saki_info juchu
                ON  juchu.denpyo_no   = uriage.denpyo_no
                AND juchu.delete_flg  = 0
                WHERE 
                    seikyu.seikyu_den_no = :seikyu_den_no
                AND seikyu.data_kbn      = 4
                AND seikyu.juchusaki_kbn = 2
                AND seikyu.delete_flg    = 0
END_OF_SQL
                    , array('seikyu_den_no' => $seikyu_den_no));
            if(Msi_Sys_Utils::myCount($select) > 0) {
                $ryosyu_meigi = $select['ryosyusyo_meigi'];
            }
        }
        return $ryosyu_meigi;
    }
    /**
     * 
     * 領収証名義(喪家外供花供物)取得　分割用
     * 
     * @param type $db
     * @param type $seikyu_den_no
     * @param type $ryosyu_meigi
     * @return type
     */
    private function getMokegaiRyosyuBunkatsuMeigi($db, $seikyu_den_no, $ryosyu_meigi){
        $select = $db->easySelOne(<<< END_OF_SQL
        SELECT 
             sekyu.ryosyusyo_meigi
            ,seikyu.juchusaki_kbn
        FROM seikyu_denpyo seikyu
        INNER JOIN seikyu_sekyu_saki_info sekyu
        ON  sekyu.seikyu_den_no = seikyu.seikyu_den_no
        AND sekyu.delete_flg    = 0
        WHERE 
            seikyu.seikyu_den_no = :seikyu_den_no
        AND seikyu.data_kbn      = 4
        AND seikyu.juchusaki_kbn = 2
        AND seikyu.delete_flg    = 0
END_OF_SQL
                , array('seikyu_den_no' => $seikyu_den_no));
        if(Msi_Sys_Utils::myCount($select) > 0) {
            $ryosyu_meigi = $select['ryosyusyo_meigi'];
        }
        return $ryosyu_meigi;
    }
    /**
     * 
     * 入金済金額を取得
     * 
     * @param type $seikyu_den_no
     * @return type
     */
    private function getNyukinZumiPrc($seikyu_den_no ){
        $db = Msi_Sys_DbManager::getMyDb();
        $nyukin_zumi_prc = 0;
        $select = $db->easySelOne(<<< END_OF_SQL
    SELECT 
        COALESCE(SUM(rh.gokei_prc), 0) AS nyukin_zumi_prc
    FROM ryosyusho_history rh
    WHERE rh.uri_den_no  = :seikyu_den_no
    AND rh.hako_kbn NOT IN (9)
    AND rh.delete_flg    = 0
END_OF_SQL
                    , array(
                        'seikyu_den_no' => $seikyu_den_no,
                ));
        if(Msi_Sys_Utils::myCount($select) > 0) {
            $nyukin_zumi_prc = $select['nyukin_zumi_prc'];
        }
        return $nyukin_zumi_prc;
    }
    /**
     * 
     * 入金済金額を取得
     * 
     * @param type $uri_den_no
     * @return type
     */
    private function getNyukinZumiPrcUc($uri_den_no ){
        $db = Msi_Sys_DbManager::getMyDb();
        $nyukin_zumi_prc = 0;
        $select = $db->easySelOne(<<< END_OF_SQL
    SELECT 
        COALESCE(SUM(rh.gokei_prc), 0) AS nyukin_zumi_prc
    FROM ryosyusho_history_uc rh
    WHERE rh.uri_den_no  = :uri_den_no
    AND rh.hako_kbn NOT IN (9)
    AND rh.delete_flg    = 0
END_OF_SQL
                    , array(
                        'uri_den_no'    => $uri_den_no,
                ));
        if(Msi_Sys_Utils::myCount($select) > 0) {
            $nyukin_zumi_prc = $select['nyukin_zumi_prc'];
        }
        return $nyukin_zumi_prc;
    }
    
    private function reissuePrcAzukari($ryosyuData) {
        $cnt = 0;
        $result['kin_1'] = 0;
        $result['kin_2'] = null;
        $result['kin_3'] = null;
        $result['kin_4'] = null;
        $result['zei_1'] = null;
        $result['zei_2'] = null;
        $result['zei_3'] = null;
        $result['zei_4'] = null;
        $result['way_1'] = null;
        $result['way_2'] = null;
        $result['way_3'] = null;
        $result['way_4'] = null;
        foreach ($ryosyuData as $key => $prc) {
            if($cnt === 5){
                break;
            }
            if($prc != 0){
                if('genkin_prc' === $key){
                    $result['way_1'] = 2;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('genkin_prc_keigen' === $key){
                    $result['way_1'] = 2;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('genkin_prc_hikazei' === $key){
                    $result['way_1'] = 2;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('genkin_prc_8' === $key){
                    $result['way_1'] = 2;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('genkin_prc_5' === $key){
                    $result['way_1'] = 2;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('genkin_prc_3' === $key){
                    $result['way_1'] = 2;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else 
                if('kogite_prc' === $key){
                    $result['way_1'] = 3;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('kogite_prc_keigen' === $key){
                    $result['way_1'] = 3;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('kogite_prc_hikazei' === $key){
                    $result['way_1'] = 3;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('kogite_prc_8' === $key){
                    $result['way_1'] = 3;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('kogite_prc_5' === $key){
                    $result['way_1'] = 3;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('kogite_prc_3' === $key){
                    $result['way_1'] = 3;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else 
                if('furikomi_prc' === $key){
                    $result['way_1'] = 1;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('furikomi_prc_keigen' === $key){
                    $result['way_1'] = 1;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('furikomi_prc_hikazei' === $key){
                    $result['way_1'] = 1;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('furikomi_prc_8' === $key){
                    $result['way_1'] = 1;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('furikomi_prc_5' === $key){
                    $result['way_'] = 1;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('furikomi_prc_3' === $key){
                    $result['way_1'] = 1;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else 
                if('credit_prc' === $key){
                    $result['way_1'] = 4;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('credit_prc_keigen' === $key){
                    $result['way_1'] = 4;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('credit_prc_hikazei' === $key){
                    $result['way_1'] = 4;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('credit_prc_8' === $key){
                    $result['way_1'] = 4;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('credit_prc_5' === $key){
                    $result['way_1'] = 4;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('credit_prc_3' === $key){
                    $result['way_1'] = 4;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else 
                if('j_debit_prc' === $key){
                    $result['way_1'] = 5;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('j_debit_prc_keigen' === $key){
                    $result['way_1'] = 5;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('j_debit_prc_hikazei' === $key){
                    $result['way_1'] = 5;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('j_debit_prc_8' === $key){
                    $result['way_1'] = 5;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('j_debit_prc_5' === $key){
                    $result['way_1'] = 5;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }else if('j_debit_prc_3' === $key){
                    $result['way_1'] = 5;
                    $result['kin_1'] += $prc;
                    $cnt++;
                }
            }
        }
        return $result;
    }
    
    /**
     * 
     * 再発行用金額を取得
     * 
     * @param type $db
     * @param type $ryosyu_no
     * @param type $ryosyu_no_sub
     * @return int
     */
    private function reissuePrc($db, $ryosyu_no, $ryosyu_no_sub) {
        $this->_seikyuRec;
        $result = array(
            'kin_1' => null, 'kin_2' => null, 'kin_3' => null, 'kin_4' => null,
            'way_1' => null, 'way_2' => null, 'way_3' => null, 'way_4' => null,
            'zei_1' => null, 'zei_2' => null, 'zei_3' => null, 'zei_4' => null,
        );
        $cnt = 1;
        $ryosyuData = $db->easySelOne(<<< END_OF_SQL
        SELECT 
             rh.genkin_prc - rh.genkin_prc_keigen - rh.genkin_prc_hikazei - rh.genkin_prc_5 - rh.genkin_prc_3 - rh.genkin_prc_8 AS genkin_prc
            ,rh.genkin_prc_keigen
            ,rh.genkin_prc_hikazei
            ,rh.genkin_prc_5
            ,rh.genkin_prc_3
            ,rh.genkin_prc_8
            ,rh.kogite_prc - rh.kogite_prc_keigen - rh.kogite_prc_hikazei - rh.kogite_prc_5 - rh.kogite_prc_3 - rh.kogite_prc_8   AS kogite_prc
            ,rh.kogite_prc_keigen
            ,rh.kogite_prc_hikazei
            ,rh.kogite_prc_5
            ,rh.kogite_prc_3
            ,rh.kogite_prc_8
            ,rh.furikomi_prc - rh.furikomi_prc_keigen - rh.furikomi_prc_hikazei - rh.furikomi_prc_5 - rh.furikomi_prc_3 - rh.furikomi_prc_8 AS furikomi_prc
            ,rh.furikomi_prc_keigen
            ,rh.furikomi_prc_hikazei
            ,rh.furikomi_prc_5
            ,rh.furikomi_prc_3
            ,rh.furikomi_prc_8
            ,rh.credit_prc - rh.credit_prc_keigen - rh.credit_prc_hikazei - rh.credit_prc_5 - rh.credit_prc_3 - rh.credit_prc_8 AS credit_prc
            ,rh.credit_prc_keigen
            ,rh.credit_prc_hikazei
            ,rh.credit_prc_5
            ,rh.credit_prc_3
            ,rh.credit_prc_8
            ,rh.j_debit_prc - rh.j_debit_prc_keigen - rh.j_debit_prc_hikazei - rh.j_debit_prc_5 - rh.j_debit_prc_3 - rh.j_debit_prc_5 - rh.j_debit_prc_8 AS j_debit_prc
            ,rh.j_debit_prc_keigen
            ,rh.j_debit_prc_hikazei
            ,rh.j_debit_prc_5
            ,rh.j_debit_prc_3
            ,rh.j_debit_prc_8
            ,TO_CHAR(sd.juchu_ymd, 'YYYY/MM/DD') AS juchu_ymd
        FROM ryosyusho_history rh
        LEFT JOIN seikyu_denpyo sd
        ON sd.seikyu_den_no = rh.uri_den_no
        AND sd.delete_flg   = 0
        WHERE rh.ryosyu_no   = :ryosyu_no
        AND rh.ryosyu_no_sub = :ryosyu_no_sub
        AND rh.delete_flg = 0
END_OF_SQL
            , array(
                'ryosyu_no'     => $ryosyu_no,
                'ryosyu_no_sub' => $ryosyu_no_sub,
                )
        );
        if(Msi_Sys_Utils::myCount($ryosyuData) > 0){
            $zei_cd = App_Utils::getZeiCd($ryosyuData['juchu_ymd']);
            $cond = array(
                        'reduced_tax_rate' => 2,
                        '__raw_1' => "'".$ryosyuData['juchu_ymd']."' BETWEEN tekiyo_st_date AND tekiyo_ed_date ",
                    );
            $zei_info = DataMapper_ZeiMst::findOne($db, $cond, false);
            if($this->_seikyuRec['data_kbn'] != 4){
                $result = $this->reissuePrcAzukari($ryosyuData);
                return $result;
            }
            foreach ($ryosyuData as $key => $prc) {
                if($cnt === 5){
                    break;
                }
                if($prc != 0){
                    if('genkin_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else if('genkin_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else if('genkin_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else if('genkin_prc_8' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 3;
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else if('genkin_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else if('genkin_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else 
                    if('kogite_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else if('kogite_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else if('kogite_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else if('kogite_prc_8' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 3;
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else if('kogite_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else if('kogite_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else 
                    if('furikomi_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else if('furikomi_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else if('furikomi_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else if('furikomi_prc_8' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 3;
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else if('furikomi_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else if('furikomi_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else 
                    if('credit_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else if('credit_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else if('credit_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else if('credit_prc_8' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 3;
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else if('credit_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else if('credit_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else 
                    if('j_debit_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }else if('j_debit_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }else if('j_debit_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }else if('j_debit_prc_8' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 3;
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }else if('j_debit_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }else if('j_debit_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }
                }
            }
        }
        return $result;
    }
    /**
     * 
     * 再発行用金額(内金)を取得
     * 
     * @param type $db
     * @param type $ryosyu_no
     * @param type $ryosyu_no_sub
     * @return int
     */
    private function reissuePrcUc($db, $ryosyu_no, $ryosyu_no_sub) {
        $result = array(
            'kin_1' => null, 'kin_2' => null, 'kin_3' => null, 'kin_4' => null,
            'way_1' => null, 'way_2' => null, 'way_3' => null, 'way_4' => null,
            'zei_1' => null, 'zei_2' => null, 'zei_3' => null, 'zei_4' => null,
        );
        $cnt = 1;
        $ryosyuData = $db->easySelOne(<<< END_OF_SQL
        SELECT 
             rh.genkin_prc - rh.genkin_prc_keigen       AS genkin_prc
            ,rh.genkin_prc_keigen
            ,rh.genkin_prc_hikazei
            ,rh.genkin_prc_5
            ,rh.genkin_prc_3
            ,rh.kogite_prc - rh.kogite_prc_keigen       AS kogite_prc
            ,rh.kogite_prc_keigen
            ,rh.kogite_prc_hikazei
            ,rh.kogite_prc_5
            ,rh.kogite_prc_3
            ,rh.furikomi_prc - rh.furikomi_prc_keigen   AS furikomi_prc
            ,rh.furikomi_prc_keigen
            ,rh.furikomi_prc_hikazei
            ,rh.furikomi_prc_5
            ,rh.furikomi_prc_3
            ,rh.credit_prc - rh.credit_prc_keigen       AS credit_prc
            ,rh.credit_prc_keigen
            ,rh.credit_prc_hikazei
            ,rh.credit_prc_5
            ,rh.credit_prc_3
            ,rh.j_debit_prc - rh.j_debit_prc_keigen     AS j_debit_prc
            ,rh.j_debit_prc_keigen
            ,rh.j_debit_prc_hikazei
            ,rh.j_debit_prc_5
            ,rh.j_debit_prc_3
            ,TO_CHAR(COALESCE(ud.juchu_ymd,jd.juchu_ymd), 'YYYY/MM/DD') AS juchu_ymd
        FROM ryosyusho_history_uc rh
        LEFT JOIN uriage_denpyo ud
        ON ud.uri_den_no  = rh.uri_den_no
        AND ud.delete_flg = 0
        LEFT JOIN juchu_denpyo jd
        ON jd.denpyo_no  = rh.uri_den_no
        AND jd.delete_flg = 0
        WHERE rh.ryosyu_no   = :ryosyu_no
        AND rh.ryosyu_no_sub = :ryosyu_no_sub
        AND rh.delete_flg = 0
END_OF_SQL
            , array(
                'ryosyu_no'     => $ryosyu_no,
                'ryosyu_no_sub' => $ryosyu_no_sub,
                )
        );
        if(Msi_Sys_Utils::myCount($ryosyuData) > 0){
            $zei_cd = App_Utils::getZeiCd($ryosyuData['juchu_ymd']);
            $cond = array(
                        'reduced_tax_rate' => 2,
                        '__raw_1' => "'".$ryosyuData['juchu_ymd']."' BETWEEN tekiyo_st_date AND tekiyo_ed_date ",
                    );
            $zei_info = DataMapper_ZeiMst::findOne($db, $cond, false);
            foreach ($ryosyuData as $key => $prc) {
                if($cnt === 5){
                    break;
                }
                if($prc != 0){
                    if('genkin_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else if('genkin_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else if('genkin_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else if('genkin_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else if('genkin_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 2;
                        $cnt++;
                    }else 
                    if('kogite_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else if('kogite_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else if('kogite_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else if('kogite_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else if('kogite_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 3;
                        $cnt++;
                    }else 
                    if('furikomi_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else if('furikomi_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else if('furikomi_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else if('furikomi_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else if('furikomi_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 1;
                        $cnt++;
                    }else 
                    if('credit_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else if('credit_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else if('credit_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else if('credit_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else if('credit_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 4;
                        $cnt++;
                    }else 
                    if('j_debit_prc' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_cd;
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }else if('j_debit_prc_keigen' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = $zei_info['zei_cd'];
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }else if('j_debit_prc_hikazei' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 0;
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }else if('j_debit_prc_5' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 2;
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }else if('j_debit_prc_3' === $key){
                        $result['kin_'.$cnt] = $prc;
                        $result['zei_'.$cnt] = 1;
                        $result['way_'.$cnt] = 5;
                        $cnt++;
                    }
                }
            }
        }
        return $result;
    }
    /**
     * 
     * 発行区分更新
     * 
     * @param type $db
     * @param type $ryosyu_no
     * @param type $ryosyu_no_sub
     * @param type $hako_kbn
     * @param type $msg
     * @throws Exception
     */
    private function upHakoInfo($db, $ryosyu_no, $ryosyu_no_sub, $hako_kbn, $msg = null) {
        $biko2 = null;
        if(!isset($hako_kbn)){
            throw new Exception("発行区分が指定されていません");
        }
        if(isset($msg)){
            $biko2 = $msg.App_Utils::getTantoNm();
            $haki_date = Msi_Sys_Utils::getTimestamp();
        }
        if(!isset($this->_seikyuRec['seikyu_den_no'])){ // 内金
            $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE ryosyusho_history_uc
  SET hako_kbn      = :hako_kbn ,
      biko2         = :biko2 ,
      haki_date     = :haki_date
WHERE ryosyu_no     = :ryosyu_no 
  AND ryosyu_no_sub = :ryosyu_no_sub
  AND delete_flg    = 0
END_OF_SQL
            , array(
                'ryosyu_no'     => $ryosyu_no,
                'ryosyu_no_sub' => $ryosyu_no_sub,
                'hako_kbn'      => $hako_kbn,
                'biko2'         => $biko2,
                'haki_date'     => $haki_date,
            ));            
        }else{
            $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE ryosyusho_history 
  SET hako_kbn      = :hako_kbn ,
      biko2         = :biko2 ,
      haki_date     = :haki_date
WHERE ryosyu_no     = :ryosyu_no 
  AND ryosyu_no_sub = :ryosyu_no_sub
  AND delete_flg    = 0
END_OF_SQL
            , array(
                'ryosyu_no'     => $ryosyu_no,
                'ryosyu_no_sub' => $ryosyu_no_sub,
                'hako_kbn'      => $hako_kbn,
                'biko2'         => $biko2,
                'haki_date'     => $haki_date,
            ));
        }
        return $cnt;
    }
    /**
     * 
     * 領収証出力状況を更新
     * 
     * @param type $db
     * @param type $nyukin_den_no
     * @param type $ryosyu_prt_status 0:未発行、1:個別発行済、2:一括発行済  
     */
    private function upRyosyuPrtStatus($db, $nyukin_den_no, $ryosyu_prt_status) {
        $cnt = 0;
        if(!isset($ryosyu_prt_status)){
            throw new Exception("領収証出力状況が指定されていません");
        }
        $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE nyukin_denpyo 
  SET ryosyu_prt_status = :ryosyu_prt_status
WHERE denpyo_no         = :nyukin_den_no 
  AND delete_flg        = 0
END_OF_SQL
            , array(
                'nyukin_den_no'     => $nyukin_den_no,
                'ryosyu_prt_status' => $ryosyu_prt_status,
            ));
        return $cnt;
    }
}
