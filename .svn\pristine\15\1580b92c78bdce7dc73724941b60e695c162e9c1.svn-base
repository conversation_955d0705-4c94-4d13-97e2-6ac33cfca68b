var appcst = appcst || {};
var appgjk = appgjk || {}; // 互助会ライブラリ
var appsk = appsk || {};// 請求先ライブラリ
var appcif = appcif || {};// CIF検索ダイアログ
var appcifgj = appcifgj || {};// コース検索ダイアログ
var appjh = appjh || {};// 受注内容変更ライブラリ
$(function () {
    "use strict";
    /** 申込区分: 1=>葬儀 */
    var MOUSHI_KBN_SOUGI = '1';
    /** 申込区分: 5=>生前依頼 */
    var MOUSHI_KBN_SEIZEN = '5';
    /** 申込区分: 7=>貸式場 */
    var MOUSHI_KBN_SHIKIJO = '7';
    /** 申込区分: 8=>エンバー */
    var MOUSHI_KBN_EMB = '8';
    /** 申込区分: 9=>搬送 */
    var MOUSHI_KBN_HANSO = '9';
    /** ステータス: 3=>施行金額確定済 */
    var STATUS_SEKO_KAKUTEI = '3';
    /** 有無区分: 0=>なし */
    var UMU_KBN_NO = '0';
    /** 弔意対応供花供物区分: 1=>商品統一 */
    var CHOI_KUMOTU_SHOHIN = '1';
    /** 弔意対応供花供物区分: 2=>金額統一 */
    var CHOI_KUMOTU_PRC = '2';
    /** 弔意対応供花供物区分: 3=>金額特注 */
    var CHOI_KUMOTU_SP = '3';
    /** 弔意対応供花供物区分: 4=>指定なし */
    var CHOI_KUMOTU_NOSHITEI = '4';
    /** 弔意対応供花供物区分: 5=>辞退 */
    var CHOI_KUMOTU_JITAI = '5';
    /** 弔意対応問合せ先区分: 99999=>その他 */
    var SEIKA_INFO_OTHER = '99999';

    /** 形式: 4=>火葬 */
    var KEISHI_KASO = '4';
    /** 日程: 1=>死亡日 */
    var NITEI_SHIBOU = 1;
    /** 日程: 3=>納棺 */
    var NITEI_NOKAN = 3;
    /** 日程: 4=>通夜 */
    var NITEI_TUYA = 4;
    /** 日程: 5=>出棺 */
    var NITEI_SYUKAN = 5;
    /** 日程: 6=>火葬 */
    var NITEI_KASO = 6;
    /** 日程: 10=>納骨 */
    var NITEI_NOKOTU = 10;
    /** 日程: 11=>葬儀 */
    var NITEI_SOGI = 11;
    /** 日程: 12=>創想の儀 */
    var NITEI_SOSO = 12;
    /** 日程: 13=>散会 */
    var NITEI_SANKAI = 13;
    /** 日程: 14=>控室 */
    var NITEI_HIKAE = 14;
    /** 日程: 15=>湯灌 */
    var NITEI_YUKAN = 15;
    /** 日程: 16=>通夜会食 */
    var NITEI_TUYASYOKU = 16;
    /** 日程: 17=>枕経 */
    var NITEI_MAKURA = 17;
    /** 場所区分=>ホール */
    var BASHO_KIND_HALL = '2';
    /** 場所区分=>火葬 */
    var BASHO_KIND_KASO = '3';
    /** 場所区分=>他式場 */
    var BASHO_KIND_OTHER = '15';
    /** 会場区分=>自営 */
    var KAIJYO_KBN_JISYA = '1';
    /** 会場区分=>他営 */
    var KAIJYO_KBN_TAEI = '2';
    /** 会場タイプ=>控室 */
    var KAIJYO_TYPE_HIKAE = '2';
    /** 会場タイプ=>エンバー */
    var KAIJYO_TYPE_EMBALM = '9';
    /** 会員区分=>互助会 */
    var KAIIN_KBN_GOJO = '100';
    /** 会員区分=>こすもす */
    var KAIIN_KBN_K = '200';
    /** 会員区分=>アスカラメイト */
    var KAIIN_KBN_A = '400';
    /** 会員区分=>ファーストステップ */
    var KAIIN_KBN_F = '500';
    /** 会員区分=>企業 */
    var KAIIN_KBN_COM = '700';
    /** 会員情報区分=>OM */
    var KAIIN_INFO_OM = '3';

    var YOTO_COURSE = '1';
    var YOTO_PLAN = '2';
    var YOTO_NO_USE = '4';
    /** 契約種別 */
    var ContractType_GOJO = '1'; // 互助会

    // 画面クラスとモデルのプロパティのオブジェクト
    appcst.pro = {
        // ヘッダー部
        bumon_cd: '#hall_cd', // 売上部門コード
        moushi_cd: '#apply_type', // 申込区分
        sougi_cd: '#funeral_type', // 葬儀区分
        daicho_no_eria: '#code_1', // 台帳番号1
        daicho_no_mm: '#code_2', // 台帳番号2
        daicho_no_seq: '#code_3', // 台帳番号3
        p_info_cd: '#personal_info', // 個人情報保護区分
        kaiin_cd: '#member', // 会員区分
        mitsu_tanto_cd: '#mitsu_tanto', // 見積担当者コード
        mitsu_tanto_nm: '#mitsu_tanto', // 見積担当者名
        seko_tanto_nm: '#staff_2', // 施行担当者コード
        // 受付情報タブ
        ts_free1_date: '#uketuke_date', // 受付日
        ts_free1_time: '#uketuke_time', // 受付時間
        uketuke_tanto_cd: '#uketuke_tanto', // 受付者
        k_haigu_cd: '#input-tab #spouse', // 配偶者コード
        k_height: '#input-tab #height', // 身長
        k_weight: '#input-tab #weight', // 体重
        infection_txt: '#input-tab #infection_txt', // 感染症の病名
        kg_yubin_no: '#input-tab #zip_1', // 故人郵便番号
        kg_addr1: '#input-tab #address_1_1', // 故人住所1
        kg_addr2: '#input-tab #address_1_2', // 故人住所2
        kg_tel: '#input-tab #tel_1', // 故人TEL
        k_nenrei_man: '#input-tab #age', // 故人年齢
        kk_kinmusaki_kbn: '#input-tab #employee', // 故人勤務先区分
        kk_kinmusaki_nm: '#input-tab #company', // 故人勤務先名
        kk_tel: '#input-tab #company_tel', // 故人勤務先TEL
        kk_yakusyoku_nm: '#input-tab #position', // 故人役職名
        kk_fax: '#input-tab #company_fax', // 故人勤務先FAX
        v_free4: '#renraku_name', // 連絡先名
        free2_code_cd: '#renraku_zoku', // 連絡先続柄コード
        renraku_tel: '#renraku_tel', // 連絡先電話番号
        nyudensaki_cd1   : '#nyudensaki_cd1',
        nyudensaki_cd2   : '#nyudensaki_cd2',
        nyudensaki_nm1   : '#nyudensaki_nm1',
        nyudensaki_nm2   : '#nyudensaki_nm2',
        nyudensha_nm     : '#nyudensha_nm',
        nyudensaki_tel1  : '#nyudensaki_tel1',
        nyudensaki_tel2  : '#nyudensaki_tel2',
        keisatsu_nm      : '#keisatsu_nm',
        homonsaki_nm     : '#homonsaki_nm',
        homonsaki_tel1   : '#homonsaki_tel1',
        homonsaki_tel2   : '#homonsaki_tel2',
        homonsaki_zip_no : '#homonsaki_zip_no',
        homonsaki_addr1  : '#homonsaki_addr1',
        homonsaki_addr2  : '#homonsaki_addr2',
        izoku_cif_no     : '#izoku_cif_no',
        izoku_cif_status : '#izoku_cif_status',
        hs_anchi_cd      : '#hs_anchi_cd',
        hs_anchi_nm      : '#hs_anchi_nm',
        tanto_nm9        : '#tanto_nm9', // 接待担当者（通夜）
        tanto_nm10       : '#tanto_nm10', // 接待担当者（葬儀）
        tanto_nm11       : '#tanto_nm11', // ライフサービス（通夜）
        tanto_nm12       : '#tanto_nm12', // ライフサービス（葬儀）
        tanto_nm13       : '#tanto_nm13', //  霊柩車運転
        tanto_nm14       : '#tanto_nm14', // マイクロバス
        // 喪主請求情報タブ
        mg_yubin_no: '#infochief-tab #chief #zip_1',
        mg_addr1: '#infochief-tab #chief #address_1_1',
        m_mail_address: '#infochief-tab #chief #m_mail_address',
        mg_tel: '#infochief-tab #chief #tel_1',
        mg_m_tel: '#infochief-tab #chief #mobile_tel_1',
        mg_addr2: '#infochief-tab #chief #address_1_2',
        mk_kinmusaki_kbn: '#infochief-tab #chief #employee',
        mk_kinmusaki_nm: '#infochief-tab #chief #company',
        mk_tel: '#infochief-tab #chief #company_tel',
        mk_yakusyoku_nm: '#infochief-tab #chief #position',
        mk_fax: '#infochief-tab #chief #company_fax',
        sekyu_first_nm: '#sekyu_first_nm',
        sekyu_last_nm: '#sekyu_last_nm',
        sekyu_first_knm: '#sekyu_first_knm',
        sekyu_last_knm: '#sekyu_last_knm',
        soufu_first_nm: '#soufu_first_nm',
        soufu_last_nm: '#soufu_last_nm',
        soufu_first_knm: '#soufu_first_knm',
        soufu_last_knm: '#soufu_last_knm',
        ryosyu_soufu_first_nm: '#ryosyu_soufu_first_nm',
        ryosyu_soufu_last_nm: '#ryosyu_soufu_last_nm',
        ryosyu_soufu_first_knm: '#ryosyu_soufu_first_knm',
        ryosyu_soufu_last_knm: '#ryosyu_soufu_last_knm',
        sekyu_moshu_kankei: '#bill_relationship_name',
        sekyu_yubin_no: '#zip_4',
        sekyu_addr1: '#address_4_1',
        sekyu_tel: '#tel_4',
        soufu_yubin_no: '#zip_5',
        soufu_addr1: '#address_5_1',
        soufu_tel: '#tel_5',
        ryosyu_soufu_yubin_no: '#zip_6',
        ryosyu_soufu_addr1: '#address_6_1',
        ryosyu_soufu_tel: '#tel_6',
        mobile_tel: '#mobile_tel_2',
        sekyu_addr2: '#address_4_2',
        sekyu_biko1: '#memo',
        kaishu_ymd: '#kaishu_ymd',
        dm_last_nm       : '#dm_last_nm',
        dm_first_nm      : '#dm_first_nm',
        dm_last_knm      : '#dm_last_knm',
        dm_first_knm     : '#dm_first_knm',
        dm_tel           : '#dm_tel',
        dm_m_tel         : '#dm_m_tel',
        dm_yubin_no      : '#dm_yubin_no',
        dm_addr1         : '#dm_addr1',
        dm_addr2         : '#dm_addr2',
        fc_tel: '#fc_tel',
        fc_mobile_tel: '#fc_mobile_tel',
        fc_office_tel: '#fc_office_tel',
        fc_office_fax: '#fc_office_fax',
        // 打合せ事項①タブ
        souke_nm: '#infochief2-tab #family_name',
        souke_knm: '#infochief2-tab #family_name_kana',
        keishiki_cd: '#infochief2-tab #funeral_style',
        syushi_kbn: '#input-tab #syushi_kbn',
        syuha_cd: '#input-tab #syuha_cd',
        jyusho_cd: '#input-tab #temple_cd',
        jyusho_nm: '#input-tab #temple',
        temple_person: '#infochief2-tab #temple_person',
        irai_biko: '#infochief2-tab #irai_biko',
        kaiso_cnt: '#infochief3-tab #kaiso_cnt',
        shinzoku_cnt: '#infochief3-tab #shinzoku_cnt',
        temple_yubin_no : '#infochief2-tab #temple_yubin_no',
        temple_addr1    : '#infochief2-tab #temple_addr1',
        temple_addr2    : '#infochief2-tab #temple_addr2',
        temple_tel2     : '#infochief2-tab #temple_tel2',
        temple_cd2      : '#infochief2-tab #temple_nm2',
        // 打合せ事項②タブ
        discuss_ymd: '.discuss_date, .discuss_time',
        choi_tokuchu_prc: '#choi_tokuchu_prc',
        form_tel: '#infochief3-tab #form_tel',
        form_fax: '#infochief3-tab #form_fax',
        // 日程タブ
        nitei_ymd: '.nitei_date, .nitei_time',
        nitei_date: '.nitei_date',
        nitei_time: '.nitei_time',
        nitei_ed_ymd: '.nitei_date, .nitei_ed_time',
        nitei_ed_time: '.nitei_ed_time',
        basho_nm: '.basho_nm',
        kasouba_zuikou_kbn: '.kasouba_zuikou_kbn',
        nitei_nm: '.nitei_nm',
        // 互助会確認タブ
        kaiin_info_kbn: '.i_kaiin_info',
        kain_no: '.i_member_id',
        apply_no: '.i_apply_no',
        course_snm_cd: '.i_cose .select-container',
        kanyu_nm: '.i_member_name',
        yoto_kbn: '.i_usage .select-container',
        keiyaku_gaku: '.i_deposit',
        plan_convert_gaku: '.i_plan_convert',
        harai_gaku: '.i_pay',
        harai_no: '.i_times',
        zankin: '.zankin',
        wari_gaku: '.i_wari_gaku',
        cose_chg_gaku: '.i_balance',
        early_use_cost_disp: '.i_early',
        meigi_chg_cost_disp: '.i_mg_chg_cost',
        n_free3: '.i_premium_gaku',
        n_free4: '.i_kannnou_gaku',
        n_free5: '.i_ekimu2',
        n_free6: '.i_wari1',
        n_free7: '.i_wari2',
        n_free8: '.i_wari3',
        waribiki_gaku: '.i_waribiki_gaku',
        kanyu_tax: '.i_kanyu_tax',
        kanyu_dt_gen: '.i_entry_era .select-container',
        kanyu_dt: '.i_entry',
        zei_kijyn_ymd: '.i_tax',
        kanyu_dantai_ext: '#other_entry_name',
        riyu_memo: '#change_reason',
        plan_use_prc: '#plan_use_prc',
        plan_change_prc: '#plan_change_prc',
        // その他タブ
        d_free1: '#d_free1',
        d_free2: '#d_free2',
        d_free3: '#d_free3',
        d_free4: '#d_free4',
        d_free5: '#d_free5',
        d_free6: '#d_free6',
        d_free7: '#d_free7',
        d_free8: '#d_free8',
        d_free9: '#d_free9',
        d_free10: '#d_free10',
        d_free11: '#d_free11',
        d_free12: '#d_free12',
        d_free13: '#d_free13',
        d_free14: '#d_free14',
        sd_yotei_ymd: '#date,#time',
        sd_yotei_date: '#date',
        v_free9: '#v_free9',
        v_free10: '#v_free10',
        v_free11: '#v_free11',
        v_free12: '#v_free12',
        v_free13: '#v_free13',
        v_free14: '#v_free14',
        v_free15: '#v_free15',
        v_free16: '#v_free16',
        v_free17: '#v_free17',
        v_free34: '#v_free34',
        todokede_kbn : '#todokede_kbn',
        todokede_nm  : '#todokede_nm',

    };
    // 診断書手続きselect2内容
    var _steps = [
        {id: '1', text: '喪家'},
        {id: '2', text: '当社'}
    ];
    // 死亡診断書select2内容
    var _certificate = [
        {id: '1', text: '1部'},
        {id: '2', text: '2部'},
        {id: '3', text: '3部'},
        {id: '4', text: '4部'},
        {id: '5', text: '5部'}
    ];
    /**
     * validation valid時処理
     * @param {View} view
     * @param {string} attr
     */
    var _valid = function (view, attr) {
        var $el = view.$(appcst.pro[attr]);
        if ($el.length) {
            $el.removeClass('error1');
            $el.attr('title', '');
        }
    };

    /**
     * validation invalid時処理
     * @param {View} view
     * @param {string} attr
     * @param {string} error
     */
    var _invalid = function (view, attr, error) {
        var $el = view.$(appcst.pro[attr]);
        if ($el.length) {
            $el.addClass('error1');
            $el.attr('title', error);
        }
    };

    /**
     * 日付と時刻を結合してタイムスタンプに設定する処理
     * @param {string} niteiDate 日付
     * @param {string} niteiTime 時刻
     * @param {string} pro モデルの属性
     * @param {model} model モデル
     */
    var _setNiteiymd = function (niteiDate, niteiTime, pro, model) {
        var niteiymd = null;
        if (!$.msiJqlib.isNullEx2(niteiDate)) {
            niteiymd = niteiDate;
        }
        if (!$.msiJqlib.isNullEx2(niteiTime)) {
            if (!$.msiJqlib.isNullEx2(niteiymd)) {
                niteiymd += " ";
            }
            niteiymd += niteiTime;
        }
        model.set(pro, niteiymd);
    };
    /**
     * 日付チェック処理
     * @param {string} value 日付
     */
    var _chkYmd = function (value) {
        if (!$.msiJqlib.isNullEx2(value) && !$.msiJqlib.chkDate(value)) {
            return Backbone.Validation.messages.ymd;
        }
    };

    // select2のvalを設定する
    var _setSelect2Val = function ($el, val) {
        $el.select2("val", val);
    };

    // 区分値コード数値設定処理
    var _setKbnCdVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_cd_num);
        }
    };
    // 区分値コード正式名設定処理
    var _setKbnLnmVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.orgData.kbn_value_lnm);
        }
    };
    // 区分値コード略称名設定処理
    var _setKbnSnmVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_snm);
        }
    };
    // 区分値コード区分設定処理
    var _setCodeKbn = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.code_kbn);
        }
    };
    // 親部門設定処理(見積式場用)
    var _setOyaBumon = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.oya_bumon_cd);
        }
    };
    // 商品部門コード設定処理(弔意商品用)
    var _setShohinBumon = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.bumon_cd);
        }
    };
    // 契約内種別設定処理(契約内種別用)
    var _setKeiyakuText = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.text);
        }
    };
    // 契約顧客番号設定処理(契約内種別用)
    var _setKeiyakuNo = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.CustomerNo);
        }
    };
    // 契約顧客番号設定処理(契約内種別用)
    var _setKeiyakuMstText = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.mst_text);
        }
    };
    // 契約種別設定処理(契約内種別用)
    var _setKeiyakuSbt = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.ContractType);
        }
    };
    // 契約内種別設定処理(契約内種別用)
    var _setKeiyakunaiSbt = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.keiyaku_nai_sbt);
        }
    };
    var _setCdText = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.text);
        }
    };
    // 和暦の生年月日チェック処理
    var _validateSeinengappi = function (gengo, value) {
        if ($.msiJqlib.isNullEx2(value) || $.msiJqlib.isNullEx2(gengo)) {
            return '生年月日は必須項目です';
        }
        var seinengappi = $.msiJqlib.warekiToseireki(gengo, value);
        if (!seinengappi) {
            return '生年月日の形式エラーです';
        }
    };
    // 赤字クラスの追加削除処理
    appcst.toggleAkajiClass = function (that, targets) {
        _.each(targets, function (val) {
            var target = that.model.get(val);
            if (!$.msiJqlib.isNullEx2(target) && target < 0) {
                that.$(appcst.pro[val]).addClass('com-akaji');
            } else {
                that.$(appcst.pro[val]).removeClass('com-akaji');
            }
        });
    };
    // 場所区分によるpickup入力フィールドの活性・非活性
    var _setSpotStatus = function (niteiKbn, val, $elPickNm, $elPickDlg, disable) {
        if (!$.msiJqlib.isNullEx2(niteiKbn) && niteiKbn == NITEI_SANKAI) {
            $elPickDlg.addClass("disabled");
        } else if (!$.msiJqlib.isNullEx2(niteiKbn) && niteiKbn == NITEI_NOKOTU) {
            $elPickDlg.addClass("disabled");
        } else {
            $elPickNm.attr("readonly", "readonly");
            // 未設定または自宅
            if ($.msiJqlib.isNullEx2(val) || val === "0") { // 0:自宅
                $elPickNm.attr("disabled", "disabled");
                $elPickDlg.addClass("disabled");
            } else if (val === "9" || disable) {
                $elPickNm.removeAttr("readonly");
                $elPickNm.removeAttr("disabled");
                $elPickDlg.addClass("disabled");
            } else {
                $elPickNm.removeAttr("disabled");
                $elPickDlg.removeClass("disabled");
            }
        }
    };
    // 基本情報葬儀日設定処理
    var _setSogiYmd = function () {
        var nitei6 = appcst.niteiCol.where({nitei_kbn: NITEI_KASO}); // 火葬
        var nitei7 = appcst.niteiCol.where({nitei_kbn: 7}); // 告別式
        var nitei11 = appcst.niteiCol.where({nitei_kbn: 11}); // 葬儀
        var keishiki_cd = appcst.appModel.get('keishiki_cd'); // 葬儀
        var nitei_date6 = null;
        var nitei_date7 = null;
        var nitei_date11 = null;
        if (nitei6.length === 1) {
            nitei_date6 = nitei6[0].get("nitei_date");
        }
        if (nitei7.length === 1) {
            nitei_date7 = nitei7[0].get("nitei_date");
        }
        if (nitei11.length === 1) {
            nitei_date11 = nitei11[0].get("nitei_date");
        }
        if (!$.msiJqlib.isNullEx2(nitei_date7)) {
            appcst.appModel.set('sougi_ymd', nitei_date7);
        } else if (!$.msiJqlib.isNullEx2(nitei_date11)) {
            appcst.appModel.set('sougi_ymd', nitei_date11);
        } else {
            appcst.appModel.set('sougi_ymd', null);
        }
    };
    var changeToNum = function (val) {
        var num = parseInt(val, 10);
        if (isFinite(num)) {
            return num;
        } else {
            num = 0;
            return num;
        }
    };
    appcst.setSpotStatus = _setSpotStatus;
    appcst.setYmd = _setNiteiymd;

    /**
     * @description 画面全体処理
     */
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                // ヘッダー部
                bumon_cd: null, // 売上部門コード
                consult_seko_no: null, // 事前相談施行番号
                jizen_history_no: null, // 事前相談受注伝票番号履歴
                hanso_seko_no: null, // 出動搬送施行番号
                login_bumon_cd: null, // ログイン者の所属部門コード
                shikijo_bumon_cd: null, // 当社以外で設定するときの部門コード
                seko_no: null, // 施行番号
                gojokai_kbn: null, // 互助会区分
                moushi_code_kbn: codeKbns.moushi_code_kbn, // 申込区分コード区分
                moushi_cd: "1", // 申込コード
                moushi_kbn: "1", // 申込区分
                sougi_code_kbn: codeKbns.sougi_code_kbn, // 葬儀区分コード区分
                sougi_cd: "1", // 葬儀コード
                sougi_kbn: "1", // 葬儀区分
                daicho_no_eria: null, // 台帳番号1
                daicho_no_mm: null, // 台帳番号2
                daicho_no_seq: null, // 台帳番号3
                p_info_code_kbn: codeKbns.p_info_code_kbn, // 個人情報保護区分コード区分
                p_info_cd: null, // 個人情報保護コード
                p_info_kbn: null, // 個人情報保護区分
                kaiin_code_kbn: codeKbns.kaiin_code_kbn, // 会員区分コード区分
                kaiin_cd: "600", // 会員コード
                kaiin_kbn: "600", // 会員区分
                n_free7: null, // 契約種別
                mitsu_tanto_cd: null, // 見積担当者コード
                mitsu_tanto_nm: null, // 見積担当者名
                uketuke_tanto_cd: null, // 受付担当者コード
                uketuke_tanto_nm: null, // 受付担当者名
                seko_tanto_cd: null, // 施行担当者コード
                seko_tanto_nm: null, // 施行担当者名
                k_last_nm_readonly: null, // 故人姓(表示用)
                k_first_nm_readonly: null, // 故人名(表示用)
                est_shikijo_cd: null, // 見積式場
                est_oya_bumon_cd: null,  // 見積式場の親部門コード
                seko_shikijo_cd: null, // 施行式場
                status_kbn: '1', // ステータス
                kaiin_sbt_code_cd: '9600', // 会員種別コード free8_code_cd SEKO_KIHON_ALL_FREE
                kaiin_sbt_cd: null, // 会員種別 free8_kbn SEKO_KIHON_ALL_FREE
                free7_code_cd: codeKbns.anketo_soufu_code_kbn, // アンケート送付先区分コード区分
                free7_kbn: null,
                free8_code_cd: codeKbns.hitsugi_check_code_kbn, // 棺確認区分コード区分
                free8_kbn: null,
                free2_code_cd: codeKbns.dm_soufu_code_kbn, // DM送付
                free2_kbn: null,
                // 受付情報タブ
                k_nm: null, // 故人名
                k_last_nm: null, // 故人苗字
                k_first_nm: null, // 故人名前
                k_file_nm: null, // 添付ファイル名OID
                k_file: null, // 添付ファイルOID一時
                k_sex_code_kbn: codeKbns.sex_code_kbn, // 性別コード区分
                k_sex_cd: null, // 性別コード
                k_sex_kbn: null, // 性別区分
                k_haigu_code_kbn: codeKbns.haigu_code_kbn, // 配偶者区分コード区分
                k_haigu_cd: null, // 配偶者コード
                k_haigu_kbn: null, // 配偶者区分
                k_knm: null, // 故人カナ名
                k_last_knm: null, // 故人カナ苗字
                k_first_knm: null, // 故人カナ名前
                k_height: null, // 故人身長
                k_weight: null, // 故人体重
                k_birth_year: null, // 生年月日(年)
                k_wa_year: null, // 生年月日(元号年)
                k_gengo: null, // 故人元号
                k_birth_month: null, // 生年月日(月)
                k_birth_day: null, // 生年月日(日)
                k_seinengappi_ymd: null, // 生年月日(和暦)
                k_seinengappi_ymd_y: null, // 生年月日(西暦)
                k_nenrei_man: null, // 故人年齢
                k_nenrei_kyounen: null, // 行年・享年
                k_cif_no: null, // 故人CIFNo
                k_cif_status: "0", // 故人CIFNoステータス
                pacemaker_code_kbn: codeKbns.umu_code_kbn, // ペースメーカー区分コード区分
                pacemaker_kbn: null, // ペースメーカー区分
                cause_death: null, // 死因
                infection_code_kbn: codeKbns.umu_code_kbn, // 感染症有無区分コード区分
                infection_umu_kbn: null, // 感染症有無区分
                infection_txt: null, // 病名
                kg_yubin_no: null, // 現住所郵便番号
                kg_addr1: null, // 現住所1
                kg_addr2: null, // 現住所2
                kg_tel: null, // 現住所TEL
                kk_kinmusaki_kbn: null, // 勤務先
                kk_kinmusaki_nm: null, // 勤務先名
                kk_tel: null, // 勤務先TEL
                kk_yakusyoku_nm: null, // 役職／職種
                kk_fax: null, // 勤務先FAX
                nyudensaki_cd1  : null, // 入電先1      free1_cd
                nyudensaki_cd2  : null, // 入電先2      free2_cd
                nyudensaki_nm1  : null, // 入電先名1    v_free1   SEKO_KIHON_ALL_FREE
                shokuchi_kbn1   : null, // 諸口区分
                nyudensaki_nm2  : null, // 入電先名2    v_free2   SEKO_KIHON_ALL_FREE
                shokuchi_kbn2   : null, // 諸口区分
                nyudensha_nm    : null, // 入電者       v_free3   SEKO_KIHON_ALL_FREE
                nyudensaki_tel1 : null, // 入電者TEL1   v_free8   SEKO_KIHON_ALL_FREE
                nyudensaki_tel2 : null, // 入電者TEL2   v_free9   SEKO_KIHON_ALL_FREE
                shutsudo_nm11   : null, // 出動者名1-1
                shutsudo_nm12   : null, // 出動者名1-2
                shutsudo_nm13   : null, // 出動者名1-3
                shutsudo_cd11   : null, // 出動者1-1    iso_tanto_cd1
                shutsudo_cd12   : null, // 出動者1-2    iso_tanto_cd2
                shutsudo_cd13   : null, // 出動者1-3    iso_tanto_cd3
                keisatsu_kbn    : null, // 警察扱い区分             free_kbn2   SEKO_KIHON_ALL_FREE
                keisatsu_nm     : null, // 警察名                   v_free11    SEKO_KIHON_ALL_FREE
                homonsaki_nm    : null, // 訪問先名                 v_free10    SEKO_KIHON_ALL_FREE
                homonsaki_tel1  : null, // 訪問先TEL1               tel_no1     SEKO_KIHON_ALL_FREE
                homonsaki_tel2  : null, // 訪問先TEL2               mobile_tel1 SEKO_KIHON_ALL_FREE
                homonsaki_zip_no: null, // 訪問先郵便番号           zip_no1     SEKO_KIHON_ALL_FREE
                homonsaki_addr1 : null, // 訪問先住所1              addr1_1     SEKO_KIHON_ALL_FREE
                homonsaki_addr2 : null, // 訪問先住所2              addr1_2     SEKO_KIHON_ALL_FREE
                izoku_cif_no    : null, // 遺族顧客No.              free4_cd
                izoku_cif_status: "0",  // 遺族顧客No.ステータス    n_free14
                izoku_file_nm   : null, // 添付ファイル名OID        img_free2      
                izoku_file      : null, // 添付ファイルOID一時
                hs_anchi_cd     : null, // 安置場所区分
                hs_anchi_nm     : null, // 安置場所名
                seko_biko1      : null, // 連絡事項
                careful_memo: null, // 注意事項
                free3_code_cd: codeKbns.umu_code_kbn, // お悔やみ掲載区分
                free3_kbn: null,
                // 喪主請求情報タブ
                m_nm: null, // 喪主名
                m_last_nm: null, // 喪主苗字
                m_first_nm: null, // 喪主名前
                m_knm: null, // 喪主名カナ
                m_last_knm: null, // 喪主苗字カナ
                m_first_knm: null, // 喪主名前カナ
                m_file_nm: null, // 喪主添付ファイル
                m_file: null, // 喪主添付ファイルOID一時
                m_zoku_cd: null, // 喪主続柄コード
                m_zoku_kbn: null, // 喪主続柄区分
                m_zoku_nm: null, // 喪主続柄名
                m_zoku_code_kbn: codeKbns.zoku_code_kbn, // 喪主続柄区分コード区分
                m_zoku_cd2: null, // 喪主続柄コード
                m_zoku_kbn2: null, // 喪主続柄区分
                m_zoku_nm2: null, // 喪主続柄区分
                m_zoku_code_kbn2: codeKbns.zoku_code_kbn, // 喪主続柄区分コード区分
                m_birth_year: null, // 生年月日(年)
                m_wa_year: null, // 生年月日(元号年)
                m_gengo: null, // 喪主元号
                m_birth_month: null, // 生年月日(月)
                m_birth_day: null, // 生年月日(日)
                m_seinengappi_ymd: null, // 喪主生年月日
                m_seinengappi_ymd_y: null, // 喪主生年月日(西暦)
                m_nenrei_man: null, // 喪主年齢
                m_cif_no: null, // 喪主CIFNo
                m_cif_status: "0", // 喪主CIFNoステータス
                mg_yubin_no: null, // 喪主現住所郵便番号
                mg_addr1: null, // 喪主現住所1
                m_mail_address: null, // 喪主mailアドレス
                mg_tel: null, // 喪主現住所TEL
                mg_m_tel: null, // 喪主携帯
                mg_addr2: null, // 喪主現住所2
                mk_kinmusaki_kbn: null, // 喪主勤務先
                mk_kinmusaki_nm: null, // 喪主勤務先名
                mk_tel: null, // 喪主勤務先TEL
                mk_yakusyoku_nm: null, // 喪主役職／職種
                mk_fax: null, // 喪主勤務先FAX
                m_sex_code_kbn: codeKbns.sex_code_kbn, // 性別コード区分
                m_sex_kbn: null, // 性別区分
                kaishu_ymd: null, // 回収予定
                s_cif_no: null, // 請求先CIFNo
                s_cif_status: "0", // 請求先CIFNoステータス
                dm_last_nm      : null, // DM送付先（姓）         v_free24
                dm_first_nm     : null, // DM送付先（名）         v_free25
                dm_last_knm     : null, // DM送付先（姓カナ）     v_free26
                dm_first_knm    : null, // DM送付先（名カナ）     v_free27
                dm_tel          : null, // DM送付先TEL                 v_free28
                dm_m_tel        : null, // DM送付先携帯                 v_free29
                dm_yubin_no     : null, // DM送付先郵便番号                 v_free30
                dm_addr1        : null, // DM送付先住所１                 v_free31
                dm_addr2        : null, // DM送付先住所２                 v_free32
                fc_nm: null, // 葬儀委員長名
                fc_last_nm: null, // 葬儀委員長名苗字
                fc_first_nm: null, // 葬儀委員長名名前
                fc_knm: null, // 葬儀委員長カナ
                fc_last_knm: null, // 葬儀委員長カナ苗字
                fc_first_knm: null, // 葬儀委員長カナ名前
                fc_tel: null, // 葬儀委員長電話番号
                fc_mobile_tel: null, // 葬儀委員長携帯番号
                fc_kinmusaki_kbn: null, // 葬儀委員長勤務先区分
                fc_kinmusaki_nm: null, // 葬儀委員長勤務先名
                fc_office_tel: null, // 葬儀委員長勤務先TEL
                fc_office_fax: null, // 葬儀委員長勤務先FAX
                fc_yakusyoku_nm: null, // 葬儀委員長役職名
                // 打合せ事項①タブ
                souke_nm: null, // 葬家
                souke_knm: null, // 葬家カナ
                keishiki_code_kbn: codeKbns.keishiki_code_kbn, // 葬儀形式コード区分
                keishiki_cd: null, // 葬儀形式コード
                keishiki_kbn: null, // 葬儀形式区分
                syushi_code_kbn: codeKbns.syushi_code_kbn, // 宗旨コード区分
                syushi_cd: null, // 宗旨コード
                syushi_kbn: null, // 宗旨区分
                syuha_code_kbn: codeKbns.syuha_code_kbn, // 宗派コード区分
                syuha_cd: null, // 宗派コード
                syuha_kbn: null, // 宗派区分
                syuha_nm: null, // 宗派名
                syuha_knm: null, // 宗派名カナ
                shonanoka_kbn   : null, // 初七日区分 n_free6 SEKO_KIHON_ALL_FREE
                jyusho_cd: null, // 寺院コード
                jyusho_nm: null, // 寺院名
                jyusho_knm: null, // 寺院カナ名
                temple_tel: null, // 寺院tel
                temple_fax: null, // 寺院fax
                temple_yubin_no : null, // 寺院郵便番号     zip_no2 SEKO_KIHON_ALL_FREE
                temple_addr1    : null, // 寺院住所1        addr2_1 SEKO_KIHON_ALL_FREE
                temple_addr2    : null, // 寺院住所2        addr2_2 SEKO_KIHON_ALL_FREE
                temple_tel2     : null, // 寺院TEL2         tel_no2 SEKO_KIHON_ALL_FREE
                temple_cd2      : null, // 紹介寺院CD       free11_code_cd SEKO_KIHON_ALL_FREE
                temple_nm2      : null, // 紹介寺院名       v_free24 SEKO_KIHON_ALL_FREE
                temple2_yubin_no : null, // 紹介寺院郵便番号     表示のみ
                temple2_addr1    : null, // 紹介寺院住所1        表示のみ
                temple2_addr2    : null, // 紹介寺院住所2        表示のみ
                temple2_tel     : null, // 紹介寺院TEL2         表示のみ
                tera_shokai_code_kbn: codeKbns.tera_shokai_code_kbn, // 寺紹介者コード
                tera_shokai_kbn: null, // 寺紹介者区分
                temple_person: null, //導師人数
                irai_biko   : null, // 依頼書備考 biko1 SEKO_KIHON_ALL_FREE
                biko1: null, // メモ
                kaiso_cnt: null, //予想参列者者
                shinzoku_cnt: null, //遺族・親族人数
                kasoba_kbn: null,
                kasoba_cd: null,
                kasoba_nm: null,
                // 打合せ事項②タブ
                choi_kouden_code_kbn: codeKbns.choi_koden_code_kbn, // 弔意香典コード区分
                choi_kouden_cd: null, // 弔意香典コード
                choi_kouden_kbn: null, // 弔意香典区分
                choi_kyoka_code_kbn: codeKbns.choi_kumotu_code_kbn, // 弔意供花供物コード区分
                choi_kyoka_cd: null, // 弔意供花供物コード
                choi_kyoka_kbn: null, // 弔意供花供物区分
                choi_shohin_cd: null, // 弔意供花供物商品
                choi_shohin_bumon_cd: null, // 弔意供花供物商品部門コード
                choi_tokuchu_prc: null, //特注金額
                choi_kahi_01: null, // 生花の可否
                choi_kahi_02: null, // 樒の可否
                choi_kahi_03: null, // 榊の可否
                choi_kahi_04: null, // 花環の可否
                choi_kahi_05: null, // 果物の可否
                choi_kahi_06: null, // 缶詰の可否
                information_code_kbn: codeKbns.seika_form_code_kbn, // 供花問い合わせコード区分
                information_cd: null, // 供花問い合わせコード
                information_kbn: null, // 供花問い合わせ区分
                seika_contact: null, // 連絡先名
                form_tel: null, // 連絡先TEL
                form_fax: null, // 連絡先FAX
                inquiry_topic: null, // 問い合わせ確認
                v_free19: null, //供花供物特記
                // 日程タブ
                tuya_check: null, // 通夜無し
                kaso_check: null, // 火葬無し
                // その他タブ
                sd_hakko_kbn: "0", // 診断書発行区分
                sd_step_kbn: null, // 診断書手続き区分
                sd_yotei_ymd: null, // 診断書発行予定時刻
                sd_yotei_date: null, // 診断書発行予定時刻(日付のみ)
                sd_yotei_time: null, // 診断書発行予定時刻(時刻のみ)
                sd_copy_cnt: null, // 診断書コピー枚数
                az_death_cnt: null, // 死亡診断書枚数
                az_inkan_kbn: "0", // 印鑑
                az_photo_cnt: null, // 御写真枚数
                az_gojokai_nm: null, // 互助会証書名称など
                uchiawase_tanto_nm: null, // 打合せ担当者(人員配置情報)
                reikyu_tanto_nm: null, // 霊柩搬送担当者(人員配置)
                kaso_tanto_nm: null, // 火葬案内担当者(人員配置)
                reikyu_hanso_ymd: null, // 霊柩搬送日時(人員配置)
                todokede_kbn    : null, // 届出役所区分 free12_code_cd  SEKO_KIHON_ALL_FREE
                todokede_nm     : null, // 届出役所名   v_free26        SEKO_KIHON_ALL_FREE
                free9_code_cd: codeKbns.portrat_code_kbn, // アンケート回収区分
                free9_kbn: null,
            };
        },
        validation: {
            kaiin_sbt_cd:{
                required: false,
                maxLength: 5
            },
            k_nm: {
                required: false,
                maxLength: 50
            },
            k_first_nm: {
                required: false,
                maxLength: 20
            },
            k_last_nm: {
                required: false,
                maxLength: 20
            },
            k_knm: {
                required: false,
                maxLength: 50
            },
            k_first_knm: {
                required: false,
                maxLength: 20
            },
            k_last_knm: {
                required: false,
                maxLength: 20
            },
            k_nenrei_man: {
                required: false,
                pattern: 'number'
            },
            k_seinengappi_ymd: "validateSeinengappi",
            k_height: {
                required: false,
                pattern: 'number'
            },
            k_weight: {
                required: false,
                pattern: 'number'
            },
            kg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            kg_tel: {
                required: false,
                pattern: 'tel'
            },
            kk_tel: {
                required: false,
                pattern: 'tel'
            },
            kk_fax: {
                required: false,
                pattern: 'tel'
            },
            nyudensaki_cd1: {
                required: false,
                maxLength: 10
            },
            nyudensaki_cd2: {
                required: false,
                maxLength: 10
            },
            nyudensaki_nm1: {
                required: false,
                maxLength: 60
            },
            nyudensaki_nm2: {
                required: false,
                maxLength: 60
            },
            nyudensha_nm: {
                required: false,
                maxLength: 60
            },
            nyudensaki_tel1: {
                required: false,
                pattern: 'tel'
            },
            nyudensaki_tel2: {
                required: false,
                pattern: 'tel'
            },
            shutsudo_cd11: {
                required: false,
                maxLength: 10
            },
            shutsudo_cd12: {
                required: false,
                maxLength: 10
            },
            shutsudo_cd13: {
                required: false,
                maxLength: 10
            },
            keisatsu_nm: {
                required: false,
                maxLength: 60
            },
            homonsaki_nm: {
                required: false,
                maxLength: 60
            },
            homonsaki_tel1: {
                required: false,
                pattern: 'tel'
            },
            homonsaki_tel2: {
                required: false,
                pattern: 'tel'
            },
            homonsaki_zip_no: {
                required: false,
                pattern: 'zip'
            },
            homonsaki_addr1: {
                required: false,
                maxLength: 30
            },
            homonsaki_addr2: {
                required: false,
                maxLength: 30
            },
            izoku_cif_no: {
                required: false,
                maxLength: 10
            },
            hs_anchi_cd: {
                required: false,
                maxLength: 5
            },
            hs_anchi_nm: {
                required: false,
                maxLength: 30
            },
            seko_biko1: {
                required: false,
                maxLength: 256
            },
            m_seinengappi_ymd: "validateSeinengappiM",
            mg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            m_mail_address: {
                required: false,
                pattern: 'email'
            },
//            mg_tel: {
//                required: false,
//                pattern: 'tel'
//            },
//            mg_m_tel: {
//                required: false,
//                pattern: 'tel'
//            },
            mk_tel: {
                required: false,
                pattern: 'tel'
            },
            mk_fax: {
                required: false,
                pattern: 'tel'
            },
            dm_last_nm: {
                required: false,
                maxLength: 20
            },
            dm_first_nm: {
                required: false,
                maxLength: 20
            },
            dm_last_knm: {
                required: false,
                maxLength: 30
            },
            dm_first_knm: {
                required: false,
                maxLength: 30
            },
            dm_tel: {
                required: false,
                pattern: 'tel'
            },
            dm_m_tel: {
                required: false,
                pattern: 'tel'
            },
            dm_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            fc_tel: {
                required: false,
                pattern: 'tel'
            },
            fc_mobile_tel: {
                required: false,
                pattern: 'tel'
            },
            fc_office_tel: {
                required: false,
                pattern: 'tel'
            },
            fc_office_fax: {
                required: false,
                pattern: 'tel'
            },
//            kaishu_ymd: {
//                required: function () {
//                    if (!$.msiJqlib.isNullEx2(appcst.appModel.get("jichu_kakute_ymd"))) {
//                        return true;
//                    } else {
//                        return false;
//                    }
//                },
//                fn: Backbone.Validation.msi_v_fn.ymd
//            },
            temple_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            temple_addr1: {
                required: false,
                maxLength: 30
            },
            temple_addr2: {
                required: false,
                maxLength: 30
            },
            temple_tel2: {
                required: false,
                pattern: 'tel'
            },
            temple_cd2: {
                required: false,
                maxLength: 5
            },
            temple_nm2: {
                required: false,
                maxLength: 30
            },
            temple_person: {
                required: false,
                pattern: 'number'
            },
            irai_biko: {
                required: false,
                maxLength: 256
            },
            kaiso_cnt: {
                required: false,
                pattern: 'number'
            },
            souke_nm: {
                required: false,
                maxLength: 50
            },
            souke_knm: {
                required: false,
                maxLength: 50
            },
            shinzoku_cnt: {
                required: false,
                pattern: 'number'
            },
            choi_tokuchu_prc: {
                required: false,
                pattern: 'number'
            },
            form_tel: {
                required: false,
                pattern: 'tel'
            },
            form_fax: {
                required: false,
                pattern: 'tel'
            },
            bumon_cd: {
                required: true,
            },
            sd_yotei_ymd: function (val, attr, computed) {
                if ($.msiJqlib.isNullEx2(computed.sd_yotei_date) && !$.msiJqlib.isNullEx2(computed.sd_yotei_time)) {
                    return '日付と時刻の整合性がありません';
                }
            },
            sd_yotei_date: function (value) {
                return _chkYmd(value);
            },
            sd_yotei_time: {
                required: false,
                pattern: 'time'
            },
            uketuke_tanto_cd: {
                required: true,
            },
            todokede_kbn: {
                required: false,
                maxLength: 5
            },
            todokede_nm: {
                required: false,
                maxLength: 60
            }
        },
        labels: {
            k_nm: '故人名',
            k_last_nm: '故人姓',
            k_first_nm: '故人名前',
            k_knm: '故人名カナ',
            k_last_knm: '故人姓カナ',
            k_first_knm: '故人名前カナ',
            k_nenrei_man: '故人年齢',
            k_seinengappi_ymd: '故人生年月日',
            k_height: '故人身長',
            k_weight: '故人体重',
            kg_tel: '故人電話番号',
            kk_tel: '故人勤務先電話番号',
            kk_fax: '故人勤務先FAX番号',
            nyudensaki_cd1  : '入電先1CD',
            nyudensaki_cd2  : '入電先2CD',
            nyudensaki_nm1  : '入電先名1',
            nyudensaki_nm2  : '入電先名2',
            nyudensha_nm    : '入電者',
            nyudensaki_tel1 : '入電者TEL1',
            nyudensaki_tel2 : '入電者TEL2',
            keisatsu_nm     : '警察名',
            homonsaki_nm    : '訪問先名',
            homonsaki_tel1  : '訪問先TEL1',
            homonsaki_tel2  : '訪問先TEL2',
            homonsaki_addr1 : '訪問先住所1',
            homonsaki_addr2 : '訪問先住所2',
            izoku_cif_no    : '遺族顧客No.',
            izoku_cif_status: '遺族顧客No.ステータス ',
            hs_anchi_cd     : '安置場所CD',
            hs_anchi_nm     : '安置場所名',
            seko_biko1      : '連絡事項',
            m_gengo: '喪主生年月日元号',
            mg_yubin_no: '喪主現住所郵便番号',
            m_mail_address: '喪主Mailアドレス',
            mg_tel: '喪主現住所TEL',
            mg_m_tel: '喪主携帯番号',
            mj_yubin_no: '喪主住民登録住所郵便番号',
            mk_tel: '喪主勤務先TEL',
            mk_fax: '喪主勤務先FAX',
            dm_last_nm      : 'DM送付先（姓）',
            dm_first_nm     : 'DM送付先（名）',
            dm_last_knm     : 'DM送付先（姓カナ）',
            dm_first_knm    : 'DM送付先（名カナ）',
            dm_tel          : 'DM送付先TEL1',
            dm_m_tel        : 'DM送付先携帯電話',
            dm_yubin_no     : 'DM送付先郵便番号',
            fc_tel: '葬儀委員長電話番号',
            fc_mobile_tel: '葬儀委員長携帯番号',
            fc_office_tel: '葬儀委員長勤務先TEL',
            fc_office_fax: '葬儀委員長勤務先FAX',
            kaishu_ymd: '回収予定日',
            temple_person: '導師人数',
            irai_biko   : '依頼書備考',
            kaiso_cnt: '予想参列者数',
            shinzoku_cnt: '遺族・親族人数',
            form_tel: '弔意対応連絡先TEL',
            form_fax: '弔意対応連絡先FAX',
            choi_tokuchu_prc: '特注金額',
            bumon_cd: '部門コード',
            uketuke_tanto_cd: '受付者',
            keishiki_cd: '形式',
            souke_nm: '喪家',
            souke_knm: '喪家カナ',
            temple_yubin_no : '菩提寺郵便番号',
            temple_addr1    : '菩提寺住所1',
            temple_addr2    : '菩提寺住所2',
            temple_tel2     : '菩提寺TEL2',
            temple_cd2      : '紹介寺院CD',
            temple_nm2      : '紹介寺院名',
            todokede_kbn    : '届出役所区分',
            todokede_nm     : '届出役所名',
        },
        validateSeinengappi: function (value, attr, computedState) {
            var gengo = computedState.k_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return _validateSeinengappi(gengo, value);
            }
        },
        validateSeinengappiM: function (value, attr, computedState) {
            var gengo = computedState.m_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return _validateSeinengappi(gengo, value);
            }
        }
    }); // AppModel

    var AppViewDef = {
        el: $("#wrapper"),
        events: {
            "click .tab li a": "changeTab",
            "click #btn_kokyaku": "kokyakuHelper",
            "click #btn_gojokai_search": "gojokaiHelper",
//            "click #mitsu_tanto, .label.dlg_mitsu_tanto": "mitsuTanHelper",
//            "click #uketuke_tanto, .label.dlg_uketuke_tanto": "uketukeHelper",
//            "click .shutsudo, .dlg_shutsudo":"shutsudoHelper",
            "click #consult_seko_no, .dlg_consult_seko_no": "consultInfoHelper",
            "click #consult_seko_clear": function () {
                appcst.appModel.set("consult_seko_no", null);
            },
            "click #hanso_seko_no, .dlg_hanso_seko_no": "hansoInfoHelper",
            "click #hanso_seko_clear": function () {
                appcst.appModel.set("hanso_seko_no", null);
            },
            "click #staff_2, .label.dlg_staff2": "sekoHelper",
            "click #btn_anti_yoyaku": "showAntiYoyaku",
            "click #btn_shikijo_yoyaku": "showShikijoYoyaku",
            "change #est_shikijo_cd": "setEstData",
            "change #input-tab #k_last_nm": function (e) {
                this.model.set('k_last_nm_readonly', e.currentTarget.value);
                appcst.huhoInfoModel.set('huho_k_last_nm', e.currentTarget.value);
            },
            "change #input-tab #k_first_nm": function (e) {
                this.model.set('k_first_nm_readonly', e.currentTarget.value);
                appcst.huhoInfoModel.set('huho_k_first_nm', e.currentTarget.value);
            },
            "change #infochief-tab #m_last_nm": function (e) {
                appcst.huhoInfoModel.set('huho_m_last_nm', e.currentTarget.value);
            },
            "change #infochief-tab #m_first_nm": function (e) {
                appcst.huhoInfoModel.set('huho_m_first_nm', e.currentTarget.value);
            },
            'change #infochief-tab #s_chief_relationship': function(e) {
                appcst.huhoInfoModel.set('huho_m_zoku_kbn', e.currentTarget.value);
            },
            'change #infochief-tab #m_mail_address': function(e) {
                appcst.huhoInfoModel.set('m_mail', e.currentTarget.value);
            },
            "click #male": function (e) {
                // 基本タブ 性別（男）設定
                if ($(e.currentTarget).hasClass('k_sex_checked')) {
                    $('#male').removeAttr("checked", "checked");
                    $('#male').prev().removeClass("ui-state-active");
                    $('#male').removeClass("k_sex_checked");
                    $('#female').removeClass("m_sex_checked");
                    this.model.set("k_sex_cd", "0");
                    this.model.set("k_sex_kbn", "0");
                    appcst.huhoInfoModel.set('huho_k_sex_kbn', "0");
                } else {
                    $('#male').addClass("k_sex_checked");
                    $('#female').removeClass("m_sex_checked");
                    this.model.set("k_sex_cd", "1");
                    this.model.set("k_sex_kbn", "1");
                    appcst.huhoInfoModel.set('huho_k_sex_kbn', "1");
                }
            },
            "click #female": function (e) {
                // 基本タブ 性別（女）設定
                if ($(e.currentTarget).hasClass('m_sex_checked')) {
                    $('#female').removeAttr("checked", "checked");
                    $('#female').prev().removeClass("ui-state-active");
                    this.model.set("k_sex_cd", "0");
                    this.model.set("k_sex_kbn", "0");
                    appcst.huhoInfoModel.set('huho_k_sex_kbn', "0");
                    $('#female').removeClass("m_sex_checked");
                    $('#male').removeClass("k_sex_checked");
                } else {
                    $('#female').addClass("m_sex_checked");
                    $('#male').removeClass("k_sex_checked");
                    this.model.set("k_sex_cd", "2");
                    this.model.set("k_sex_kbn", "2");
                    appcst.huhoInfoModel.set('huho_k_sex_kbn', "2");
                }
            },
            "click #m_male": function (e) {
                // 基本タブ 性別（男）設定
                if ($(e.currentTarget).hasClass('k_sex_checked')) {
                    $('#m_male').removeAttr("checked", "checked");
                    $('#m_male').prev().removeClass("ui-state-active");
                    $('#m_male').removeClass("k_sex_checked");
                    $('#m_female').removeClass("m_sex_checked");
                    this.model.set("m_sex_kbn", "0");
                } else {
                    $('#m_male').addClass("k_sex_checked");
                    $('#m_female').removeClass("m_sex_checked");
                    this.model.set("m_sex_kbn", "1");
                }
            },
            "click #m_female": function (e) {
                // 基本タブ 性別（女）設定
                if ($(e.currentTarget).hasClass('m_sex_checked')) {
                    $('#m_female').removeAttr("checked", "checked");
                    $('#m_female').prev().removeClass("ui-state-active");
                    this.model.set("m_sex_kbn", "0");
                    $('#m_female').removeClass("m_sex_checked");
                    $('#m_male').removeClass("k_sex_checked");
                } else {
                    $('#m_female').addClass("m_sex_checked");
                    $('#m_male').removeClass("k_sex_checked");
                    this.model.set("m_sex_kbn", "2");
                }
            },
            "change #input-tab #birthday_era,#input-tab #birthday_month,#input-tab #birthday_day": "calcNereiK",
            "select2-open #input-tab #birthday_era": function () {
                var era = this.model.get('k_birth_year');
                if (!$.msiJqlib.isNullEx2(era)) {
                    return;
                }
                var choices = $('.select2-results').children('li');
                var index = choices.length - 70;
                var choice = $(choices[index]);
                choices.filter(".select2-highlighted").removeClass("select2-highlighted");
                choice = $(choices[index]);
                choice.addClass("select2-highlighted");
                // 自動スクロールさせる
                $('.select2-results').scrollTop(0 + (index * 25));

            },
            "change #infochief-tab #birthday_era,#infochief-tab #birthday_month,#infochief-tab #birthday_day": "calcNereiM",
            "select2-open #infochief-tab #birthday_era": function () {
                var era = this.model.get('m_birth_year');
                if (!$.msiJqlib.isNullEx2(era)) {
                    return;
                }
                var choices = $('.select2-results').children('li');
                var index = choices.length - 70;
                var choice = $(choices[index]);
                choices.filter(".select2-highlighted").removeClass("select2-highlighted");
                choice = $(choices[index]);
                choice.addClass("select2-highlighted");
                // 自動スクロールさせる
                $('.select2-results').scrollTop(0 + (index * 25));

            },
            "click #detail .label.dlg_zip": "zipHelper",
            "click .label.dlg_temple": "nmjyushoHelper",
            "click .label.dlg_temple2": "nmjyushoHelper2",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
            "select2-opening #input-tab #syuha_cd, #infochief2-tab #syuha_cd": function () {
                // 宗派コードを開いたときに宗旨の区分で絞り込んで表示する
                var syushiCd = this.model.get("syushi_cd");
                var orgKbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
                var fileredKbns = [];
                _.each(orgKbns, function (item) {
                    if (item.kbn_value_cd_num === syushiCd) {
                        fileredKbns.push(item);
                    }
                });
                appcst.syuha_kbns = fileredKbns;
            },
            "select2-selecting #input-tab #syushi_cd, #infochief2-tab #syushi_cd": "clearSyuha",
            "select2-clearing #input-tab #syushi_cd, #infochief2-tab #syushi_cd": "clearSyuha",
            "change #infochief2-tab #funeral_style": function (e) {
                _setSogiYmd();
            },
            'change #input-tab #syushi_cd, #infochief2-tab #syushi_cd': function(e) {
                appcst.huhoInfoModel.set('huho_syushi_cd', e.currentTarget.value);
//                appcst.huhoInfoModel.set('funeral_style', e.added.text);
                appcst.appView.setShuhaOther();
            },
            "click #btn_save": "doSave",
            "click #btn_huho_renkei": "doHuhoRenkei",
            "click #btn_print": "doPrint",
            "click #btn_delete": "doDelete",
            "click #btn_cancel": "doCancel",
            "click #btn_consult": "showConsult",
            "click #btn_seko_copy": "doSekoCopy",
            "click #btn_km_copy": "fromKtoMCopy",
            "click #btn_ms_copy": "fromMtoSCopy",
            "click #btn_mdm_copy": "fromMtoDMCopy",
            "click #btn_ss_copy": "fromStoSCopy",
            "click #btn_sr_copy": "fromStoRCopy",
            "change #choi_kumotu": 'setChoiPrcStatus',
            "change #seika_form": 'setSeikaFormInfo',
            "change #bechu_shohin_bumon": function () {
                this.model.set('choi_shohin_cd', null);
            },
            "select2-opening #bechu_shohin": function () {
                // 商品統一の場合は開いたときに適用範囲内かつ商品部門コードで絞り込んでを取得する
                // 見積式場が未設定の場合は該当なしにする
                // 金額統一の場合は開いたときにそのコード名称を使用する
                var tekiyo_ymd = null;
                var choi_kumotu_cd = this.model.get('choi_kyoka_cd');
//                var shohin_bumon_cd = this.model.get('choi_shohin_bumon_cd');
                var est_shikijo_cd = this.model.get('est_shikijo_cd');
                if (!$.msiJqlib.isNullEx2(this.model.get("sougi_ymd"))) {
                    tekiyo_ymd = this.model.get("sougi_ymd");
                } else {
                    tekiyo_ymd = $.msiJqlib.getStdDate();
                }
                var orgKbns = data.bechuShohin;
                var fileredKbns = [];
                if ($.msiJqlib.isNullEx2(est_shikijo_cd)) {
                    $.msiJqlib.showErr('見積式場を選択してください。');
                    appcst.bechu_shohin = fileredKbns;
                    return;
                }
                var bumonData = data.dataKbns.est_shikijo.filter(function (item, index) {
                    return item.id == est_shikijo_cd;
                });
                if (choi_kumotu_cd == CHOI_KUMOTU_PRC) {
                    fileredKbns = $.msiJqlib.objToArray3(data.dataKbns.prc_unity_kbn);
                } else {
                    _.each(orgKbns, function (item) {
                        if (item.hanbai_st_ymd <= tekiyo_ymd && tekiyo_ymd <= item.hanbai_end_ymd) {
                            _.each(bumonData[0].oya_bumons, function (bumon_cd, key) {
                                if (item.bumon_cd == bumon_cd) {
                                    fileredKbns.push(item);
                                }
                            });
                        }
                    });
                }
                appcst.bechu_shohin = fileredKbns;
            },
            "click #possible1": function () {
                // 問合せ区分 1：可 設定
                this.model.set("free2_kbn", "1");
            },
            "click #impossible1": function () {
                // 問合せ区分 0:不可設定
                this.model.set("free2_kbn", "0");
            },
            "change #stamp": function (e) {
                // "印鑑チェックボックス値を設定
                this.setCheckBox(e, "az_inkan_kbn", '#stamp');
            },
            "change #input-tab #syuha_cd, #infochief2-tab #syuha_cd": "setShuhaOther",
            "change #choi_koden": function (e) {
                if (!$.msiJqlib.isNullEx2(e.currentTarget.value) && e.currentTarget.value == '2'
                        && $.msiJqlib.isNullEx2(appcst.huhoInfoModel.get('funeral_gift_use_flag'))) {
                    appcst.huhoInfoModel.set('funeral_gift_use_flag', '0');
                } else if ($.msiJqlib.isNullEx2(appcst.huhoInfoModel.get('funeral_gift_use_flag'))) {
                    appcst.huhoInfoModel.set('funeral_gift_use_flag', null);
                }
            },
            "click #input-tab #nyudensaki_cd1, #input-tab .dlg_nyuden1": "nyudensaki1Helper",
            "click #input-tab #nyudensaki_cd2, #input-tab .dlg_nyuden2": "nyudensaki2Helper",
            "click #btn_irai" : "doIrai",
            "click #btn_nitei": "doNitei",
            "click #btn_shiji": "doSiji",
            "click #btn_azukari": "doAzukari",
            "select2-opening .sel_tanto_cd": function (e) {
                // 対象施行の葬儀日から退職者は表示させないようにする
                var sougi_ymd = this.model.get('sougi_ymd');
                var kijunYmd = $.msiJqlib.getStdDate();
                if (!$.msiJqlib.isNullEx2(sougi_ymd)) {
                    kijunYmd = sougi_ymd;
                }
                var fileredKbns = [];
                _.each(data.dataKbns.tanto_mst, function (item) {
                    if ($.msiJqlib.isNullEx2(item.tekiyo_ed_date)) {
                        fileredKbns.push(item);
                    } else if (item.tekiyo_ed_date >= kijunYmd) {
                        fileredKbns.push(item);
                    }
                });
                appcst.tanto_cds = fileredKbns;
            },
        },
        bindings: {
            '#hall_cd': {
                observe: 'bumon_cd',
                updateView: false
            },
            '#consult_seko_no': 'consult_seko_no',
            '#jizen_denpyo': {
                observe: 'jizen_history_no',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#hanso_seko_no': 'hanso_seko_no',
            '#apply_type': {
                observe: 'moushi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'moushi_kbn');
                    _setCodeKbn($el, this.model, 'moushi_code_kbn');
                    return $el.val();
                }
            },
            '#funeral_type': {
                observe: 'sougi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'sougi_kbn');
                    _setCodeKbn($el, this.model, 'sougi_code_kbn');
                    return $el.val();
                }
            },
            '#personal_info': {
                observe: 'p_info_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'p_info_kbn');
                    _setCodeKbn($el, this.model, 'p_info_code_kbn');
                    return $el.val();
                }
            },
            '#anketo_soufu': {
                observe: 'free7_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#dm_soufu': {
                observe: 'free2_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setCodeKbn($el, this.model, 'free2_code_cd');
                    return $el.val();
                }
            },
            '#hitsugi_check': {
                observe: 'free8_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#member': {
                observe: 'kaiin_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'kaiin_kbn');
                    _setCodeKbn($el, this.model, 'kaiin_code_kbn');
                    return $el.val();
                }
            },
            '#code_1': {
                observe: 'daicho_no_eria',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo($el.val(), this.model.get("daicho_no_mm"), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_2': {
                observe: 'daicho_no_mm',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), $el.val(), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_3': {
                observe: 'daicho_no_seq',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), this.model.get("daicho_no_mm"), $el.val());
                    return $el.val();
                }
            },
            '#mitsu_tanto': {
                observe: 'mitsu_tanto_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'mitsu_tanto_nm');
                    return $el.val();
                }
            },
            '#uketuke_tanto': {
                observe: 'uketuke_tanto_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'uketuke_tanto_nm');
                    return $el.val();
                }
            },
            '#staff_2': {
                observe: 'seko_tanto_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'seko_tanto_nm');
                    this.$("#hd_seko_tanto").text($el.val());
                    return $el.val();
                }
            },
            '#est_shikijo_cd': {
                observe: 'est_shikijo_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setOyaBumon($el, this.model, 'est_oya_bumon_cd');
                    return $el.val();
                }
            },
            '#seko_shikijo_cd': {
                observe: 'seko_shikijo_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            'k_nm': {
                observe: 'k_nm',
                // 葬儀情報(ヘッダー)の故人名バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_k_nm").text($el.val());
                    return $el.val();
                }
            },
            '#k_last_nm': 'k_last_nm',
            '#k_first_nm': 'k_first_nm',
            '#k_last_nm_readonly': 'k_last_nm_readonly',
            '#k_first_nm_readonly': 'k_first_nm_readonly',
            '#input-tab #spouse': {
                observe: 'k_haigu_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'k_haigu_kbn');
                    _setCodeKbn($el, this.model, 'k_haigu_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #k_last_knm': 'k_last_knm',
            '#input-tab #k_first_knm': 'k_first_knm',
            '#input-tab #height': 'k_height',
            '#input-tab #weight': 'k_weight',
            '#input-tab #birthday_era': {
                observe: 'k_birth_year',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnSnmVal($el, this.model, 'k_wa_year');
                    return $el.val();
                }
            },
            '#input-tab #birthday_month': {
                observe: 'k_birth_month',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #birthday_day': {
                observe: 'k_birth_day',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #age': 'k_nenrei_man',
            '#input-tab #k_cif_no': 'k_cif_no',
            '#input-tab #k_cif_status': {
                observe: 'k_cif_status',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #age_at_death': 'k_nenrei_kyounen',
            '#input-tab #pacemaker': {
                observe: 'pacemaker_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setCodeKbn($el, this.model, 'pacemaker_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #cause_death': 'cause_death',
            '#input-tab #infection_umu': {
                observe: 'infection_umu_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setCodeKbn($el, this.model, 'infection_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #infection_txt': 'infection_txt',
            '#input-tab #zip_1': 'kg_yubin_no',
            '#input-tab #address_1_1': 'kg_addr1',
            '#input-tab #tel_1': 'kg_tel',
            '#input-tab #address_1_2': 'kg_addr2',
            '#input-tab #employee': {
                observe: 'kk_kinmusaki_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #company': 'kk_kinmusaki_nm',
            '#input-tab #company_tel': 'kk_tel',
            '#input-tab #company_fax': 'kk_fax',
            '#input-tab #position': 'kk_yakusyoku_nm',
            '#input-tab #nyudensaki_cd1'   : 'nyudensaki_cd1',
            '#input-tab #nyudensaki_cd2'   : 'nyudensaki_cd2',
            '#input-tab #nyudensaki_nm1'   : 'nyudensaki_nm1',
            '#input-tab #nyudensaki_nm2'   : 'nyudensaki_nm2',
            '#input-tab #nyudensha_nm'     : 'nyudensha_nm',
            '#input-tab #nyudensaki_tel1'  : 'nyudensaki_tel1',
            '#input-tab #nyudensaki_tel2'  : 'nyudensaki_tel2',
            '#input-tab #shutsudo_cd11': {
                observe: 'shutsudo_cd11',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'shutsudo_nm11');
                    return $el.val();
                }
            },
            '#input-tab #shutsudo_cd12': {
                observe: 'shutsudo_cd12',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'shutsudo_nm12');
                    return $el.val();
                }
            },
            '#input-tab #shutsudo_cd13': {
                observe: 'shutsudo_cd13',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'shutsudo_nm13');
                    return $el.val();
                }
            },
            '#input-tab #keisatsu_kbn'     : $.msiJqlib.getCheckBinding('keisatsu_kbn'),
            '#input-tab #keisatsu_nm'      : 'keisatsu_nm',
            '#input-tab #homonsaki_nm'     : 'homonsaki_nm',
            '#input-tab #homonsaki_tel1'   : 'homonsaki_tel1',
            '#input-tab #homonsaki_tel2'   : 'homonsaki_tel2',
            '#input-tab #homonsaki_zip_no' : 'homonsaki_zip_no',
            '#input-tab #homonsaki_addr1'  : 'homonsaki_addr1',
            '#input-tab #homonsaki_addr2'  : 'homonsaki_addr2',
            '#input-tab #izoku_cif_no'     : 'izoku_cif_no',
            '#input-tab #izoku_cif_status' : $.msiJqlib.getSelect2Binding('izoku_cif_status'),
            '#input-tab #hs_anchi_cd'      : $.msiJqlib.getSelect2Binding('hs_anchi_cd'),
            '#input-tab #hs_anchi_nm'      : 'hs_anchi_nm',
            '#input-tab #seko_biko1'       : 'seko_biko1',
            '#input-tab #careful_memo': 'careful_memo',
            '#input-tab #kuyami_keisai': {
                observe: 'free3_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setCodeKbn($el, this.model, 'free3_code_cd');
                    return $el.val();
                }
            },
            'm_nm': {
                observe: 'm_nm',
                // 葬儀情報(ヘッダー)の喪主名バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_m_nm").text($el.val());
                    return $el.val();
                }
            },
            '#infochief-tab #chief #m_last_nm': 'm_last_nm',
            '#infochief-tab #chief #m_first_nm': 'm_first_nm',
            '#input-tab #m_last_nm': 'm_last_nm',
            '#input-tab #m_first_nm': 'm_first_nm',
            '#infochief-tab #chief #s_chief_relationship': {
                observe: 'm_zoku_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'm_zoku_kbn');
                    _setKbnLnmVal($el, this.model, 'm_zoku_nm');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #s_chief_relationship2': {
                observe: 'm_zoku_cd2',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'm_zoku_kbn2');
                    _setKbnLnmVal($el, this.model, 'm_zoku_nm2');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #m_last_knm': 'm_last_knm',
            '#infochief-tab #chief #m_first_knm': 'm_first_knm',
            '#input-tab #m_last_knm': 'm_last_knm',
            '#input-tab #m_first_knm': 'm_first_knm',
            '#infochief-tab #chief #birthday_era': {
                observe: 'm_birth_year',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnSnmVal($el, this.model, 'm_wa_year');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #birthday_month': {
                observe: 'm_birth_month',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #birthday_day': {
                observe: 'm_birth_day',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #age': {
                observe: 'm_nenrei_man',
                onGet: function (val, options) {
                    if (!$.msiJqlib.isNullEx2(val)) {
                        return '満' + val + '歳';
                    }
                }
            },
            '#infochief-tab #m_cif_no': 'm_cif_no',
            '#infochief-tab #m_cif_status': {
                observe: 'm_cif_status',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #zip_1': 'mg_yubin_no',
            '#infochief-tab #chief #address_1_1': {
                observe: 'mg_addr1',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                    return $el.val();
                },
                afterUpdate: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                }
            },
            '#infochief-tab #chief #address_1_2': {
                observe: 'mg_addr2',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr1');
                    this.setHeadeMaddr(address, $el.val());
                    return $el.val();
                }
            },
            '#infochief-tab #chief #m_mail_address': 'm_mail_address',
            '#infochief-tab #chief #tel_1': 'mg_tel',
            '#infochief-tab #chief #mobile_tel_1': 'mg_m_tel',
            '#infochief-tab #chief #employee': {
                observe: 'mk_kinmusaki_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #company': 'mk_kinmusaki_nm',
            '#infochief-tab #chief #company_tel': 'mk_tel',
            '#infochief-tab #chief #position': 'mk_yakusyoku_nm',
            '#infochief-tab #chief #company_fax': 'mk_fax',
            '#infochief-tab #kaishu_ymd': 'kaishu_ymd',
            '#infochief-tab #s_cif_no': 's_cif_no',
            '#infochief-tab #s_cif_status': {
                observe: 's_cif_status',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #dm_last_nm'        : 'dm_last_nm',
            '#infochief-tab #dm_first_nm'       : 'dm_first_nm',
            '#infochief-tab #dm_last_knm'       : 'dm_last_knm',
            '#infochief-tab #dm_first_knm'      : 'dm_first_knm',
            '#infochief-tab #dm_tel'            : 'dm_tel',
            '#infochief-tab #dm_m_tel'          : 'dm_m_tel',
            '#infochief-tab #dm_yubin_no'       : 'dm_yubin_no',
            '#infochief-tab #dm_addr1'          : 'dm_addr1',
            '#infochief-tab #dm_addr2'          : 'dm_addr2',
            '#infochief-tab #fc_last_nm': 'fc_last_nm',
            '#infochief-tab #fc_first_nm': 'fc_first_nm',
            '#infochief-tab #fc_last_knm': 'fc_last_knm',
            '#infochief-tab #fc_first_knm': 'fc_first_knm',
            '#infochief-tab #fc_tel': 'fc_tel',
            '#infochief-tab #fc_mobile_tel': 'fc_mobile_tel',
            '#infochief-tab #fc_kinmusaki_kbn': {
                observe: 'fc_kinmusaki_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #fc_kinmusaki_nm': 'fc_kinmusaki_nm',
            '#infochief-tab #fc_office_tel': 'fc_office_tel',
            '#infochief-tab #fc_office_fax': 'fc_office_fax',
            '#infochief-tab #fc_yakusyoku_nm': 'fc_yakusyoku_nm',
            '#infochief2-tab #family_name': 'souke_nm',
            '#infochief2-tab #family_name_kana': 'souke_knm',
            '#infochief2-tab #funeral_style': {
                observe: 'keishiki_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'keishiki_kbn');
                    _setCodeKbn($el, this.model, 'keishiki_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #syushi_cd': {
                observe: 'syushi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syushi_kbn');
                    _setCodeKbn($el, this.model, 'syushi_code_kbn');
                    return $el.val();
                }
            },
            '#infochief2-tab #syushi_cd': {
                observe: 'syushi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syushi_kbn');
                    _setCodeKbn($el, this.model, 'syushi_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #syuha_cd': {
                observe: 'syuha_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syuha_kbn');
                    _setCodeKbn($el, this.model, 'syuha_code_kbn');
                    // 宗派名設定処理
                    var item = $el.select2("data");
                    if ($.msiJqlib.isNullEx2(item)) {
                        this.model.set("syuha_nm", null);
//                        this.model.set("syuha_knm", null);
                    } else {
                        this.model.set("syuha_nm", item.text);
//                        this.model.set("syuha_knm", item.kbn_value_snm);
                    }
                    return $el.val();
                }
            },
            '#infochief2-tab #syuha_cd': {
                observe: 'syuha_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syuha_kbn');
                    _setCodeKbn($el, this.model, 'syuha_code_kbn');
                    // 宗派名設定処理
                    var item = $el.select2("data");
                    if ($.msiJqlib.isNullEx2(item)) {
                        this.model.set("syuha_nm", null);
//                        this.model.set("syuha_knm", null);
                    } else {
                        this.model.set("syuha_nm", item.text);
//                        this.model.set("syuha_knm", item.kbn_value_snm);
                    }
                    return $el.val();
                }
            },
            '#infochief2-tab #syuha_nm_other': 'syuha_nm',
            '#input-tab #syuha_nm_other': 'syuha_nm',
//            '#infochief2-tab #syuha_knm': 'syuha_knm',
            '#infochief2-tab #shonanoka_kbn': $.msiJqlib.getSelect2Binding('shonanoka_kbn'),
            '#infochief2-tab #tera_shokai': {
                observe: 'tera_shokai_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCodeKbn($el, this.model, 'tera_shokai_code_kbn');
                    return $el.val();
                }
            },
            '#infochief2-tab #temple_cd': 'jyusho_cd',
            '#infochief2-tab #temple': {
                observe: 'jyusho_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("jyusho_cd", null);
                        this.model.set("jyusho_knm", null);
                    }
                    return val;
                }
            },
            '#infochief2-tab #temple_knm': 'jyusho_knm',
            '#infochief2-tab #temple_tel': 'temple_tel',
            '#infochief2-tab #temple_fax': 'temple_fax',
            '#infochief2-tab #temple_yubin_no': 'temple_yubin_no',
            '#infochief2-tab #temple_addr1'   : 'temple_addr1',
            '#infochief2-tab #temple_addr2'   : 'temple_addr2',
            '#infochief2-tab #temple_tel2'    : 'temple_tel2',
            '#infochief2-tab #temple_cd2'     : 'temple_cd2',
            '#infochief2-tab #temple_nm2'     : 'temple_nm2',
            '#infochief2-tab #temple2_yubin_no': 'temple2_yubin_no',
            '#infochief2-tab #temple2_addr1'   : 'temple2_addr1',
            '#infochief2-tab #temple2_addr2'   : 'temple2_addr2',
            '#infochief2-tab #temple2_tel'    : 'temple2_tel',
            '#infochief2-tab #temple_person': {
                observe: 'temple_person',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#infochief2-tab #irai_biko': 'irai_biko',
            '#infochief2-tab #memo': 'biko1',
            '#infochief3-tab #kaiso_cnt': {
                observe: 'kaiso_cnt',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#infochief3-tab #shinzoku_cnt': {
                observe: 'shinzoku_cnt',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#tuya_check': $.msiJqlib.getCheckBinding('tuya_check'),
            '#kaso_check': $.msiJqlib.getCheckBinding('kaso_check'),
            '#infochief3-tab #choi_koden': {
                observe: 'choi_kouden_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setKbnCdVal($el, this.model, 'choi_kouden_kbn');
                    _setCodeKbn($el, this.model, 'choi_kouden_code_kbn');
                    return $el.val();
                }
            },
            '#infochief3-tab #choi_kumotu': {
                observe: 'choi_kyoka_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setKbnCdVal($el, this.model, 'choi_kyoka_kbn');
                    _setCodeKbn($el, this.model, 'choi_kyoka_code_kbn');
                    return $el.val();
                }
            },
            '#infochief3-tab #bechu_shohin_bumon': {
                observe: 'choi_shohin_bumon_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief3-tab #bechu_shohin': {
                observe: 'choi_shohin_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setShohinBumon($el, this.model, 'choi_shohin_bumon_cd');
                    return $el.val();
                }
            },
            '#infochief3-tab #choi_tokuchu_prc': {
                observe: 'choi_tokuchu_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#seika_fa': {
                observe: 'choi_kahi_01',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#sikimi_fa': {
                observe: 'choi_kahi_02',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#sakaki_fa': {
                observe: 'choi_kahi_03',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#hanawa_fa': {
                observe: 'choi_kahi_04',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#kudamono_fa': {
                observe: 'choi_kahi_05',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#can_fa': {
                observe: 'choi_kahi_06',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#seika_form': {
                observe: 'information_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setKbnCdVal($el, this.model, 'information_kbn');
                    _setCodeKbn($el, this.model, 'information_code_kbn');
                    return $el.val();
                }
            },
            '#seika_contact': 'seika_contact', // 連絡先名
            '#form_tel': 'form_tel', // 連絡先TEL
            '#form_fax': 'form_fax', // 連絡先FAX
            '#inquiry_topic': 'inquiry_topic',
            '#kyoka_topic': 'v_free19',
            '#infomisc-tab #publish': {
                observe: 'sd_hakko_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #steps': {
                observe: 'sd_step_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #date': {
                observe: 'sd_yotei_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('sd_yotei_time'), 'sd_yotei_ymd', this.model);
                    return $el.val();
                }
            },
            '#infomisc-tab #time': {
                observe: 'sd_yotei_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('sd_yotei_date'), $el.val(), 'sd_yotei_ymd', this.model);
                    return $el.val();
                }
            },
            '#infomisc-tab #copys': {
                observe: 'sd_copy_cnt',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#certificate': {
                observe: 'az_death_cnt',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #portrait': {
                observe: 'az_photo_cnt',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #d_free1': 'd_free1',
            '#infomisc-tab #d_free2': 'd_free2',
            '#infomisc-tab #d_free3': 'd_free3',
            '#infomisc-tab #d_free4': 'd_free4',
            '#infomisc-tab #d_free5': 'd_free5',
            '#infomisc-tab #d_free6': 'd_free6',
            '#infomisc-tab #d_free7': 'd_free7',
            '#infomisc-tab #d_free8': 'd_free8',
            '#infomisc-tab #d_free9': 'd_free9',
            '#infomisc-tab #d_free10': 'd_free10',
            '#infomisc-tab #d_free11': 'd_free11',
            '#infomisc-tab #d_free12': 'd_free12',
            '#infomisc-tab #d_free13': 'd_free13',
            '#infomisc-tab #d_free14': 'd_free14',
            '#infomisc-tab #todokede_kbn': $.msiJqlib.getSelect2Binding('todokede_kbn'),
            '#infomisc-tab #todokede_nm' : 'todokede_nm',
            '#infomisc-tab #v_free9': 'v_free9',
            '#infomisc-tab #v_free10': 'v_free10',
            '#infomisc-tab #v_free11': 'v_free11',
            '#infomisc-tab #v_free12': 'v_free12',
            '#infomisc-tab #v_free13': 'v_free13',
            '#infomisc-tab #v_free14': 'v_free14',
            '#infomisc-tab #v_free15': 'v_free15',
            '#infomisc-tab #v_free16': 'v_free16',
            '#infomisc-tab #v_free17': 'v_free17',
            '#infomisc-tab #v_free34': 'v_free34',
            '#uchiawase_tanto_nm': 'uchiawase_tanto_nm',
            '#after_tanto_nm': 'after_tanto_nm',
            '#infomisc-tab #anketo_kaisyu': {
                observe: 'free9_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setCodeKbn($el, this.model, 'free9_code_cd');
                    return $el.val();
                }
            },
        },
        // 入電先1
        nyudensaki1Helper: function () {
//            if ( msiLib2.isChildWindow() ) {
//                return;
//                msiLib2.showWarn( '子画面からはNoの変更はできません' );
//            }
            var m = this.model; 
            this.$el.msiPickHelper({
                action: '/mref/keiyakusakidlg', 
                _myId: '#msi-dialog',
                width: '94%',
                height: '94%',
                onSelect: function(data) {
                    m.set('shokuchi_kbn1', data.shokuchi_kbn);
                    m.set('nyudensaki_cd1', data.code);
                    m.set('nyudensaki_nm1', data.name);
                },
                onClear: function() {
                    m.set('shokuchi_kbn1', null);
                    m.set('nyudensaki_cd1', null);
                    m.set('nyudensaki_nm1', null);
                },
                hookSetData: function() {
                    return {
                        init_search: 0,
                        s_seko_kbn: 1,    // 1:施行
                        wari_kbn_hide_flg: 1, // 割引区分非表示フラグ 1：非表示
                        //s_est_shikijo_cd: appcst.appModel.get('est_shikijo_cd'),    // 見積式場
                    }
                },
            });
        },
        // 入電先2
        nyudensaki2Helper: function () {
//            if ( msiLib2.isChildWindow() ) {
//                return;
//                msiLib2.showWarn( '子画面からはNoの変更はできません' );
//            }
            var m = this.model; 
            this.$el.msiPickHelper({
                action: '/mref/keiyakusakidlg', 
                _myId: '#msi-dialog',
                width: '94%',
                height: '94%',
                onSelect: function(data) {
                    m.set('shokuchi_kbn2', data.shokuchi_kbn);
                    m.set('nyudensaki_cd2', data.code);
                    m.set('nyudensaki_nm2', data.name);
                },
                onClear: function() {
                    m.set('shokuchi_kbn2', null);
                    m.set('nyudensaki_cd2', null);
                    m.set('nyudensaki_nm2', null);
                },
                hookSetData: function() {
                    return {
                        init_search: 0,
                        s_seko_kbn: 1,    // 1:施行
                        wari_kbn_hide_flg: 1, // 割引区分非表示フラグ 1：非表示
                        //s_est_shikijo_cd: appcst.appModel.get('est_shikijo_cd'),    // 見積式場
                    }
                },
            });
        },
        // 見積式場変更時に変動するデータ調整
        setEstData: function (e) {
            var choi_kyoka_cd = this.model.get('choi_kyoka_cd');
            var choi_shohin_bumon_cd = this.model.get('choi_shohin_bumon_cd');
            var est_shikijo_cd = this.model.get('est_shikijo_cd');
            var est_oya_bumon_cd = null;
            var bumonData = data.dataKbns.est_shikijo.filter(function (item, index) {
                return item.id == est_shikijo_cd;
            });
            var flg = false;
            if (!$.msiJqlib.isNullEx2(est_shikijo_cd)) {
                est_oya_bumon_cd = this.model.get('est_oya_bumon_cd');
                if(!$.msiJqlib.isNullEx2(bumonData) && bumonData.length > 0){
                    _.each(bumonData[0].oya_bumons, function (bumon_cd, key) {
                        if (choi_shohin_bumon_cd == bumon_cd) {
                            flg = true;
                        }
                    });
                }
            }
            // 弔意商品部門が一致するものがなければクリアする
            if (!flg) {
                if (choi_kyoka_cd == CHOI_KUMOTU_SHOHIN || choi_kyoka_cd == CHOI_KUMOTU_SP) {
                    this.model.set('choi_kyoka_cd', null);
                    this.model.set('choi_kyoka_kbn', null);
                    this.model.set('choi_shohin_cd', null);
                    this.model.set('choi_shohin_bumon_cd', null);
                    this.model.set('choi_tokuchu_prc', null);
                }
            }
            // 見積式場設定時に弔意対応の対応先を設定する
            if (!$.msiJqlib.isNullEx2(est_oya_bumon_cd)) {
                this.model.set('information_cd', est_oya_bumon_cd);
                this.model.set('information_kbn', est_oya_bumon_cd);
                this.setSeikaFormInfo();
            }
        },
        // 顧客ピッカー対象顧客
        kokyakuHelper: function(e) {
            // console.log('KOKYAKU 検索');
            var that = this;
            msiGlobalObj.kaikokyakudlg2Open( this, 
                                             ['故人', '遺族', '喪主', '請求先', 'お見舞金対象'], 
                                             { kokyaku_no_p1: this.model.get('k_cif_no'),
                                               kokyaku_no_p2: this.model.get('izoku_cif_no'),
                                               kokyaku_no_p3: this.model.get('m_cif_no'),
                                               kokyaku_no_p4: this.model.get('s_cif_no'),
                                               kokyaku_no_p5: appcst.resultInfoModel.get('om_kokyaku_cd'),
                                               kokyaku_result_p1: this.model.get('k_cif_status'),
                                               kokyaku_result_p2: this.model.get('izoku_cif_status'),
                                               kokyaku_result_p3: this.model.get('m_cif_status'),
                                               kokyaku_result_p4: this.model.get('s_cif_status'),
                                               result_set_3: true, // true:未検索/該当なし/顧客No(3択)  false:該当なし/顧客No(2択)
                                               //
                                               // s_cond_etc01: '0', // 数値型は不可. 文字列型のみ
                                               // s_kokyaku_kbn: 0, // 9617(顧客区分): 0:個人,1:得意先
                                               // s_dantai_cd: '00400110', // 0010000, 00400110:ＪＡ東京みどりＧＬＣ, 00400001: ファミリーライフクラブ　ゴールド
                                               // is_easyreg: 0, // 顧客登録画面リンク表示
                                             },
                                             this._kokyakuHelperOnClose,
                                             this._kokyakuHelperOnSet );
        },
        // ダイアログ close 時ハンドラ ([キャンセル]ボタン押下時には呼ばれない)
        // kokyakudlgData: kokyakudlg のモデルデータ
        _kokyakuHelperOnClose: function( kokyakudlgData ) {
            console.log( '_kokyakuHelperOnClose kokyakudlgData=>', kokyakudlgData );
//            var dumpStr = JSON.stringify(kokyakudlgData, undefined, 2);
//            this.model.set( { dump_kokyaku: dumpStr } );

            if (kokyakudlgData.kokyaku_upd_p1) { // 設定されていれば
                if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_upd_p1)) {
                    this.model.set('k_cif_no', kokyakudlgData.kokyaku_no_p1);
                    this.model.set('k_cif_status', 2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    if (kokyakudlgData.kokyaku_data_p1) {
                        this.model.set('k_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_hyoji_nm);
                        this.model.set('k_last_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_nm1);
                        this.model.set('k_first_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_nm2);
                        appcst.huhoInfoModel.set('huho_k_last_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_nm1);
                        appcst.huhoInfoModel.set('huho_k_first_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_nm2);
                        this.model.set('k_knm', kokyakudlgData.kokyaku_data_p1.kokyaku_hyoji_kana);
                        this.model.set('k_last_knm', kokyakudlgData.kokyaku_data_p1.kokyaku_kana1);
                        this.model.set('k_first_knm', kokyakudlgData.kokyaku_data_p1.kokyaku_kana2);
                        this.model.set('k_sex_kbn', kokyakudlgData.kokyaku_data_p1.sex_kbn);
                        if (kokyakudlgData.kokyaku_data_p1.sex_kbn == 2) {
                            $('#female').attr("checked", "checked");
                            $('#female').prev().addClass("ui-state-active");
                            $('#male').removeAttr("checked", "checked");
                            $('#male').prev().removeClass("ui-state-active");
                            $('#female').addClass("m_sex_checked");
                            $('#male').removeClass("k_sex_checked");
                        } else if (kokyakudlgData.kokyaku_data_p1.sex_kbn == 1) {
                            $('#male').attr("checked", "checked");
                            $('#male').prev().addClass("ui-state-active");
                            $('#female').removeAttr("checked", "checked");
                            $('#female').prev().removeClass("ui-state-active");
                            $('#female').removeClass("m_sex_checked");
                            $('#male').addClass("k_sex_checked");
                        } else {
                            $('#male').removeAttr("checked", "checked");
                            $('#male').prev().removeClass("ui-state-active");
                            $('#female').removeAttr("checked", "checked");
                            $('#female').prev().removeClass("ui-state-active");
                            $('#female').removeClass("m_sex_checked");
                            $('#male').addClass("k_sex_checked");
                        }
                        this.model.set('kg_yubin_no', kokyakudlgData.kokyaku_data_p1.yubin_no);
                        this.model.set('kg_addr1', kokyakudlgData.kokyaku_data_p1.addr1);
                        this.model.set('kg_addr2', kokyakudlgData.kokyaku_data_p1.addr2);
                        this.model.set('kg_tel', kokyakudlgData.kokyaku_data_p1.tel1);
                        this.model.set('kk_kinmusaki_nm', kokyakudlgData.kokyaku_data_p1.tel1);
                        this.model.set('kk_kinmusaki_nm', kokyakudlgData.kokyaku_data_p1.kimsaki_nm);
                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p1.seinengappi_ymd)) {
                            var spSe = kokyakudlgData.kokyaku_data_p1.seinengappi_ymd.split('/');
                            this.model.set('k_birth_year', spSe[0]);
                            this.model.set('k_birth_month', spSe[1]);
                            this.model.set('k_birth_day', spSe[2]);
                            var wareki = $.msiJqlib.arrSeirekiToWareki(kokyakudlgData.kokyaku_data_p1.seinengappi_ymd);
                            if ( wareki[0] ) {
                                var gengo = wareki[0];
                                this.model.set('k_gengo', gengo);
                                this.model.set('k_wa_year', gengo+$.msiJqlib.fillPre(wareki[1]));
                            }
                        }
                        this.calcNereiK();
                    }else{
                        this.model.set('k_cif_no', kokyakudlgData.kokyaku_no_p1);
                        this.model.set('k_cif_status', kokyakudlgData.kokyaku_result_p1); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                        this.model.set('k_nm', null);
                        this.model.set('k_last_nm', null);
                        this.model.set('k_first_nm', null);
                        this.model.set('k_knm', null);
                        this.model.set('k_last_knm', null);
                        this.model.set('k_first_knm', null);
                        $('#male').click();
                        this.model.set('kg_yubin_no', null);
                        this.model.set('kg_addr1', null);
                        this.model.set('kg_addr2', null);
                        this.model.set('kg_tel', null);
                        this.model.set('k_gengo', null);
                        this.model.set('k_wa_year', null);
                        this.model.set('k_birth_year', null);
                        this.model.set('k_birth_month', null);
                        this.model.set('k_birth_day', null);
                        this.model.set('k_seinengappi_ymd', null);
                        this.model.set('k_seinengappi_ymd_y', null);
                        this.model.set('k_nenrei_man', null);
                        this.model.set('kk_kinmusaki_nm', null);
                    }
                } else {
                    this.model.set('k_cif_no', kokyakudlgData.kokyaku_no_p1);
                    this.model.set('k_cif_status', kokyakudlgData.kokyaku_result_p1); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    this.model.set('k_nm', null);
                    this.model.set('k_last_nm', null);
                    this.model.set('k_first_nm', null);
                    this.model.set('k_knm', null);
                    this.model.set('k_last_knm', null);
                    this.model.set('k_first_knm', null);
                    $('#male').click();
                    this.model.set('kg_yubin_no', null);
                    this.model.set('kg_addr1', null);
                    this.model.set('kg_addr2', null);
                    this.model.set('kg_tel', null);
                    this.model.set('k_gengo', null);
                    this.model.set('k_wa_year', null);
                    this.model.set('k_birth_year', null);
                    this.model.set('k_birth_month', null);
                    this.model.set('k_birth_day', null);
                    this.model.set('k_seinengappi_ymd', null);
                    this.model.set('k_seinengappi_ymd_y', null);
                    this.model.set('k_nenrei_man', null);
                    this.model.set('kk_kinmusaki_nm', null);
                }
            }
            if (kokyakudlgData.kokyaku_upd_p2) { // 設定されていれば
                if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_upd_p2)) {
                    this.model.set('izoku_cif_no', kokyakudlgData.kokyaku_no_p2);
                    this.model.set('izoku_cif_status', '2');
                    if (kokyakudlgData.kokyaku_no_p2) {
                        this.model.set('izoku_last_nm', kokyakudlgData.kokyaku_data_p2.kokyaku_nm1);
                        this.model.set('izoku_first_nm', kokyakudlgData.kokyaku_data_p2.kokyaku_nm2);
                        this.model.set('izoku_last_knm', kokyakudlgData.kokyaku_data_p2.kokyaku_kana1);
                        this.model.set('izoku_first_knm', kokyakudlgData.kokyaku_data_p2.kokyaku_kana2);
                        this.model.set('izoku_tel1', kokyakudlgData.kokyaku_data_p2.tel1);
                        this.model.set('izoku_tel2', kokyakudlgData.kokyaku_data_p2.tel2);
                        this.model.set('kaiin_sbt_cd', kokyakudlgData.kokyaku_data_p2.kaiin_sbt);
                    } else {
                        this.model.set('izoku_cif_no', kokyakudlgData.kokyaku_no_p2);
                        this.model.set('izoku_cif_status', kokyakudlgData.kokyaku_result_p2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                        this.model.set('izoku_last_nm', null);
                        this.model.set('izoku_first_nm', null);
                        this.model.set('izoku_last_knm', null);
                        this.model.set('izoku_first_knm', null);
                        this.model.set('izoku_tel1', null);
                        this.model.set('izoku_tel2', null);
                        this.model.set('kaiin_sbt_cd', null);
                    }
                } else {
                    this.model.set('izoku_cif_no', kokyakudlgData.kokyaku_no_p2);
                    this.model.set('izoku_cif_status', kokyakudlgData.kokyaku_result_p2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    this.model.set('izoku_last_nm', null);
                    this.model.set('izoku_first_nm', null);
                    this.model.set('izoku_last_knm', null);
                    this.model.set('izoku_first_knm', null);
                    this.model.set('izoku_tel1', null);
                    this.model.set('izoku_tel2', null);
                    this.model.set('kaiin_sbt_cd', null);
                }
            }
            if (kokyakudlgData.kokyaku_upd_p3) { // 設定されていれば
                $("#m_female").removeAttr('disabled');
                $("#m_male").removeAttr('disabled');
                if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_upd_p3)) {
                    this.model.set('m_cif_no', kokyakudlgData.kokyaku_no_p3);
                    this.model.set('m_cif_status', 2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    if (kokyakudlgData.kokyaku_data_p3) {
                        this.model.set('m_nm', kokyakudlgData.kokyaku_data_p3.kokyaku_hyoji_nm);
                        this.model.set('m_last_nm', kokyakudlgData.kokyaku_data_p3.kokyaku_nm1);
                        this.model.set('m_first_nm', kokyakudlgData.kokyaku_data_p3.kokyaku_nm2);
                        this.model.set('m_knm', kokyakudlgData.kokyaku_data_p3.kokyaku_hyoji_kana);
                        this.model.set('m_last_knm', kokyakudlgData.kokyaku_data_p3.kokyaku_kana1);
                        this.model.set('m_first_knm', kokyakudlgData.kokyaku_data_p3.kokyaku_kana2);
                        this.model.set('m_sex_kbn', kokyakudlgData.kokyaku_data_p3.sex_kbn);
                        if (kokyakudlgData.kokyaku_data_p3.sex_kbn == 2) {
                            $('#m_female').attr("checked", "checked");
                            $('#m_female').prev().addClass("ui-state-active");
                            $('#m_male').removeAttr("checked", "checked");
                            $('#m_male').prev().removeClass("ui-state-active");
                        } else if (kokyakudlgData.kokyaku_data_p3.sex_kbn == 1) {
                            $('#m_male').attr("checked", "checked");
                            $('#m_male').prev().addClass("ui-state-active");
                            $('#m_female').removeAttr("checked", "checked");
                            $('#m_female').prev().removeClass("ui-state-active");
                        } else {
                            $('#m_male').removeAttr("checked", "checked");
                            $('#m_male').prev().removeClass("ui-state-active");
                            $('#m_female').removeAttr("checked", "checked");
                            $('#m_female').prev().removeClass("ui-state-active");
                        }
                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p3.seinengappi_ymd)) {
                            var spSe = kokyakudlgData.kokyaku_data_p3.seinengappi_ymd.split('/');
                            this.model.set('m_birth_year', spSe[0]);
                            this.model.set('m_birth_month', spSe[1]);
                            this.model.set('m_birth_day', spSe[2]);
                            var wareki = $.msiJqlib.arrSeirekiToWareki(kokyakudlgData.kokyaku_data_p3.seinengappi_ymd);
                            if ( wareki[0] ) {
                                var gengo = wareki[0];
                                this.model.set('m_gengo', gengo);
                                this.model.set('m_wa_year', gengo+$.msiJqlib.fillPre(wareki[1]));
                            }
                        }
                        this.model.set('mg_yubin_no', kokyakudlgData.kokyaku_data_p3.yubin_no);
                        this.model.set('mg_addr1', kokyakudlgData.kokyaku_data_p3.addr1);
                        this.model.set('mg_addr2', kokyakudlgData.kokyaku_data_p3.addr2);
                        this.model.set('mg_tel', kokyakudlgData.kokyaku_data_p3.tel1);
                        this.model.set('mg_m_tel', kokyakudlgData.kokyaku_data_p3.tel2);
                        this.model.set('m_mail_address', kokyakudlgData.kokyaku_data_p3.e_mail);
                        this.model.set('mk_kinmusaki_nm', kokyakudlgData.kokyaku_data_p3.kimsaki_nm);
                        this.model.set('mk_tel', kokyakudlgData.kokyaku_data_p3.kimsaki_tel);
                        this.calcNereiM();
                    } else {
                        this.model.set('m_cif_no', kokyakudlgData.kokyaku_no_p3);
                        this.model.set('m_cif_status', kokyakudlgData.kokyaku_result_p3); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                        this.model.set('m_nm', null);
                        this.model.set('m_last_nm', null);
                        this.model.set('m_first_nm', null);
                        this.model.set('m_knm', null);
                        this.model.set('m_last_knm', null);
                        this.model.set('m_first_knm', null);
                        $('#m_male').removeAttr("checked", "checked");
                        $('#m_male').prev().removeClass("ui-state-active");
                        $('#m_female').removeAttr("checked", "checked");
                        $('#m_female').prev().removeClass("ui-state-active");
                        this.model.set('mg_yubin_no', null);
                        this.model.set('mg_addr1', null);
                        this.model.set('mg_addr2', null);
                        this.model.set('mg_tel', null);
                        this.model.set('mg_m_tel', null);
                        this.model.set('m_mail_address', null);
                        this.model.set('m_gengo', null);
                        this.model.set('m_wa_year', null);
                        this.model.set('m_birth_year', null);
                        this.model.set('m_birth_month', null);
                        this.model.set('m_birth_day', null);
                        this.model.set('m_seinengappi_ymd', null);
                        this.model.set('m_seinengappi_ymd_y', null);
                        this.model.set('m_nenrei_man', null);
                        this.model.set('mk_kinmusaki_nm', null);
                        this.model.set('mk_tel', null);
                    }
                } else {
                    this.model.set('m_cif_no', kokyakudlgData.kokyaku_no_p3);
                    this.model.set('m_cif_status', kokyakudlgData.kokyaku_result_p3); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    this.model.set('m_nm', null);
                    this.model.set('m_last_nm', null);
                    this.model.set('m_first_nm', null);
                    this.model.set('m_knm', null);
                    this.model.set('m_last_knm', null);
                    this.model.set('m_first_knm', null);
                    $('#m_male').click();
                    this.model.set('mg_yubin_no', null);
                    this.model.set('mg_addr1', null);
                    this.model.set('mg_addr2', null);
                    this.model.set('mg_tel', null);
                    this.model.set('mg_m_tel', null);
                    this.model.set('m_mail_address', null);
                    this.model.set('m_gengo', null);
                    this.model.set('m_wa_year', null);
                    this.model.set('m_birth_year', null);
                    this.model.set('m_birth_month', null);
                    this.model.set('m_birth_day', null);
                    this.model.set('m_seinengappi_ymd', null);
                    this.model.set('m_seinengappi_ymd_y', null);
                    this.model.set('m_nenrei_man', null);
                    this.model.set('mk_kinmusaki_nm', null);
                    this.model.set('mk_tel', null);
                }
                $("#m_female").attr('disabled','disabled');
                $("#m_male").attr('disabled','disabled');
            }
            if (kokyakudlgData.kokyaku_upd_p4) { // 設定されていれば
                if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_upd_p4)) {
                    this.model.set('s_cif_no', kokyakudlgData.kokyaku_no_p4);
                    this.model.set('s_cif_status', 2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    if (kokyakudlgData.kokyaku_data_p4) {
                        appcst.sekyuModel.set('sekyu_nm', kokyakudlgData.kokyaku_data_p4.kokyaku_hyoji_nm);
                        appcst.sekyuModel.set('sekyu_last_nm', kokyakudlgData.kokyaku_data_p4.kokyaku_nm1);
                        appcst.sekyuModel.set('sekyu_first_nm', kokyakudlgData.kokyaku_data_p4.kokyaku_nm2);
                        appcst.sekyuModel.set('sekyu_knm', kokyakudlgData.kokyaku_data_p4.kokyaku_hyoji_kana);
                        appcst.sekyuModel.set('sekyu_last_knm', kokyakudlgData.kokyaku_data_p4.kokyaku_kana1);
                        appcst.sekyuModel.set('sekyu_first_knm', kokyakudlgData.kokyaku_data_p4.kokyaku_kana2);
                        appcst.sekyuModel.set('sekyu_yubin_no', kokyakudlgData.kokyaku_data_p4.yubin_no);
                        appcst.sekyuModel.set('sekyu_addr1', kokyakudlgData.kokyaku_data_p4.addr1);
                        appcst.sekyuModel.set('sekyu_addr2', kokyakudlgData.kokyaku_data_p4.addr2);
                        appcst.sekyuModel.set('sekyu_tel', kokyakudlgData.kokyaku_data_p4.tel1);
                        appcst.sekyuModel.set('mobile_tel', kokyakudlgData.kokyaku_data_p4.tel2);
                        appcst.sekyuModel.set('soufu_last_nm', kokyakudlgData.kokyaku_data_p4.sekyu_atena1);
                        appcst.sekyuModel.set('soufu_first_nm', kokyakudlgData.kokyaku_data_p4.sekyu_atena2);
                        appcst.sekyuModel.set('soufu_yubin_no', kokyakudlgData.kokyaku_data_p4.sekyu_yubin_no);
                        appcst.sekyuModel.set('soufu_addr1', kokyakudlgData.kokyaku_data_p4.sekyu_addr1);
                        appcst.sekyuModel.set('soufu_addr2', kokyakudlgData.kokyaku_data_p4.sekyu_addr2);
                    } else {
                        this.model.set('s_cif_no', kokyakudlgData.kokyaku_no_p4);
                        this.model.set('s_cif_status', kokyakudlgData.kokyaku_result_p4); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                        appcst.sekyuModel.set('sekyu_nm', null);
                        appcst.sekyuModel.set('sekyu_last_nm', null);
                        appcst.sekyuModel.set('sekyu_first_nm', null);
                        appcst.sekyuModel.set('sekyu_knm', null);
                        appcst.sekyuModel.set('sekyu_last_knm', null);
                        appcst.sekyuModel.set('sekyu_first_knm', null);
                        appcst.sekyuModel.set('sekyu_yubin_no', null);
                        appcst.sekyuModel.set('sekyu_addr1', null);
                        appcst.sekyuModel.set('sekyu_addr2', null);
                        appcst.sekyuModel.set('sekyu_tel', null);
                        appcst.sekyuModel.set('mobile_tel', null);
                        appcst.sekyuModel.set('soufu_last_nm', null);
                        appcst.sekyuModel.set('soufu_first_nm', null);
                        appcst.sekyuModel.set('soufu_yubin_no', null);
                        appcst.sekyuModel.set('soufu_addr1', null);
                        appcst.sekyuModel.set('soufu_addr2', null);
                    }
                } else {
                    this.model.set('s_cif_no', kokyakudlgData.kokyaku_no_p4);
                    this.model.set('s_cif_status', kokyakudlgData.kokyaku_result_p4); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    appcst.sekyuModel.set('sekyu_nm', null);
                    appcst.sekyuModel.set('sekyu_last_nm', null);
                    appcst.sekyuModel.set('sekyu_first_nm', null);
                    appcst.sekyuModel.set('sekyu_knm', null);
                    appcst.sekyuModel.set('sekyu_last_knm', null);
                    appcst.sekyuModel.set('sekyu_first_knm', null);
                    appcst.sekyuModel.set('sekyu_yubin_no', null);
                    appcst.sekyuModel.set('sekyu_addr1', null);
                    appcst.sekyuModel.set('sekyu_addr2', null);
                    appcst.sekyuModel.set('sekyu_tel', null);
                    appcst.sekyuModel.set('mobile_tel', null);
                    appcst.sekyuModel.set('soufu_last_nm', null);
                    appcst.sekyuModel.set('soufu_first_nm', null);
                    appcst.sekyuModel.set('soufu_yubin_no', null);
                    appcst.sekyuModel.set('soufu_addr1', null);
                    appcst.sekyuModel.set('soufu_addr2', null);
                }
            }
            if ( kokyakudlgData.kokyaku_upd_p5 ) { // 設定されていれば
                if ( !$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_no_p5) ) {
                    appcst.resultInfoModel.set('om_kokyaku_cd', kokyakudlgData.kokyaku_no_p5);
                    if ( kokyakudlgData.kokyaku_data_p5) {
                        appcst.resultInfoModel.set('om_kokyaku_nm1', kokyakudlgData.kokyaku_data_p5.kokyaku_nm1);
                        appcst.resultInfoModel.set('om_kokyaku_nm2', kokyakudlgData.kokyaku_data_p5.kokyaku_nm2);
                        appcst.resultInfoModel.set('om_kokyaku_knm1', kokyakudlgData.kokyaku_data_p5.kokyaku_kana1);
                        appcst.resultInfoModel.set('om_kokyaku_knm2', kokyakudlgData.kokyaku_data_p5.kokyaku_kana2);
                    } else {
                        appcst.resultInfoModel.set('om_kokyaku_cd', kokyakudlgData.kokyaku_no_p5);
                        appcst.resultInfoModel.set('om_kokyaku_nm1', null);
                        appcst.resultInfoModel.set('om_kokyaku_nm2', null);
                        appcst.resultInfoModel.set('om_kokyaku_knm1', null);
                        appcst.resultInfoModel.set('om_kokyaku_knm2', null);
                    }
                } else {
                    appcst.resultInfoModel.set('om_kokyaku_cd', kokyakudlgData.kokyaku_no_p5);
                    appcst.resultInfoModel.set('om_kokyaku_nm1', null);
                    appcst.resultInfoModel.set('om_kokyaku_nm2', null);
                    appcst.resultInfoModel.set('om_kokyaku_knm1', null);
                    appcst.resultInfoModel.set('om_kokyaku_knm2', null);
                }
            }
        },
        // データ選択時ハンドラ
        // offset: 順序番号, selectedData, kokyakudlgData: モデルデータ
        _kokyakuHelperOnSet: function( offset, selectedData, kokyakudlgData ) {
            console.log( '_kokyakuHelperOnSet [offset, selectedData, kokyakudlgData]=>', [offset, selectedData, kokyakudlgData] );
        },
        // コース情報 pickup
        gojokaiHelper: function (e) {
            if (msiLib2.isChildWindow()) {
                return;
                msiLib2.showWarn('子画面からはNoの変更はできません');
            }
            var kijunYmd = this.model.get('sougi_ymd');
            if (!$.msiJqlib.isNullEx2(kijunYmd)) {
                if (!kijunYmd.match(/^(\d{4})[\/](\d{2})[\/](\d{2})$/) ) {
                    kijunYmd = $.msiJqlib.getStdDate();
                } 
            } else {
                kijunYmd = $.msiJqlib.getStdDate();
            }
            msiGlobalObj.mockaiindlgOpen( this, { 
                    // s_cust_nm: this.model.get('user_name'), // 氏名. 検索条件の初期値
                    // s_member_no: '00-0-00000-000', // 加入者番号
                    // または個別設定、s_member_no2: '22', s_member_no3: '3', s_member_no4: '44444', s_member_no5: '555',
                    // s_tel_ex: '044-4444-4444', // TEL
                    // s_yubin_no: '123-4567', // 郵便番号
                    // s_addr_ex: '住所',
                    // s2_is_add_sisan_data: 0, // 「契約情報検索」応答形式で返す場合は 0. 通常不要.
                    s2_application_date: kijunYmd, // 利用予定日(契約情報参照で使う)
                    s2_user_name: this.model.get('k_last_nm')+'　'+this.model.get('k_first_nm')
                 }, // 利用者(契約情報参照で使う)
                this.selectMocCustp,   // [決定]時のハンドラ
//                this.clearMocCustp     // [取消]時のハンドラ. 現状、ダイアログに[取消]ボタンは非表示なので使いません
            );
        },
        selectMocCustp: function (data) {
            console.log('@@@ selectMocCustp =>', data);
            if (!data) {
                return;
            }

            // 空いている行を見つける. 
            var emptyOffset = _.reduce( appcst.gojokaiMemberCol.models,
                                        function(ac, m, i) { if ( ac === null && !m.get('kain_no') ) ac = i; return ac; }, null );
            // 施行互助会会員の会員番号を取得する
            var kaiin_no_array = [];
            appcst.gojokaiMemberCol.each(function (m, i) {
                kaiin_no_array.push(m.get('kain_no'));
            });
            var err_flg = false;
            var err_msg = '';
            _.each(data, function (v, k) {
                var course_snm_cd = null;
                _.each(appcst.data.gojokaiCouseMst, function (item, iw) {
                    if (!$.msiJqlib.isNullEx2(course_snm_cd)) {
                        return false;
                    }
                    if (!$.msiJqlib.isNullEx2(iw) && v['CourseName'] === iw) {
                        course_snm_cd = iw;
                    } else if (!$.msiJqlib.isNullEx2(iw) && v['CourseName'].indexOf(iw) === 0) {
                        course_snm_cd = iw;
                    } else if (!$.msiJqlib.isNullEx2(iw) && iw.indexOf(v['CourseName']) === 0) {
                        course_snm_cd = iw;
                    }
                });
                var taxInfo = appcst.data.taxInfoAll;
                var zei_cd = null;
                _.each(taxInfo, function (item) {
                    if (item.zei_rtu == changeToNum(String(v['ContractTaxRate']))) {
                        zei_cd = item.zei_cd;
                    }
                });
                var kain_no = null;
                if (!$.msiJqlib.isNullEx2(v['member_no']) && !$.msiJqlib.isNullEx2(v['member_no_id'])) {
                    var kainNoAry = v['member_no_id'].split('-');
                    kain_no = kainNoAry[0]+'-'+v['member_no'];
                }
                var data = {cif_no: v['CustomerNo'],
                    course_snm_cd: course_snm_cd,
                    kain_no: kain_no,
                    kanyu_nm: v['CustomerName'],
                    kanyu_dt: v['ContractDate'],
                    keiyaku_gaku: changeToNum(String(v['TerminationValue'])),
                    harai_no: changeToNum(String(v['TotalPayNum'])),
                    harai_gaku: changeToNum(String(v['TotalPayValue'])) - changeToNum(String(v['BonusAmount'])) - changeToNum(String(v['BalanceDiscountValue'])) - changeToNum(String(v['PrepaymentDiscountValue'])),
                    wari_gaku: changeToNum(String(v['PrepaymentDiscountValue'])),
                    waribiki_gaku: changeToNum(String(v['BalanceDiscountValue'])),
                    early_use_cost_disp: changeToNum(String(v['EarlyUseCost'])),
                    meigi_chg_cost_disp: changeToNum(String(v['RenameCommission'])),
                    zei_cd: zei_cd,
                    cur_cd: v['ContractCode'],
                    kanyu_tax: changeToNum(String(v['ContractTax'])),
//                    kaiin_info_kbn: '3',  // テスト
                    kaiin_info_kbn: '1',
                    v_free10: v['ContractStatus'],
                    v_free11: v['EarlyUseOriginDate'],
                    v_free12: v['EarlyUseLimitedDate'],
                    v_free13: v['RenameCommissionUsage'],
                    v_free14: v['TargetUser'],
                    v_free15: v['TargetDate'],
                    v_free16: kain_no,
                    v_free17: v['ContractNo'],
                    n_free3: changeToNum(String(v['BonusAmount'])),
                    n_free4: changeToNum(String(v['PremiumMonths'])),
                    zan_gaku: (changeToNum(v['TerminationValue'])-changeToNum(v['TotalPayValue'])-changeToNum(v['PremiumMonths'])),
                };
                if (kaiin_no_array.indexOf(v['ContractNo']) !== -1) {
                    return;
                }
                var m = appcst.gojokaiMemberCol.at(emptyOffset++); // 確保分を超えるとエラー
                if (emptyOffset > 10) {
//                    err_flg = true;
//                    err_msg = '10件を超えての登録はできません。';
                    return;
                }
                m.set(data);
            });
//            if (err_flg) {
//                $.msiJqlib.showErr(err_msg);
//                return;
//            }
        },
        clearMocCustp: function (data) {
            console.log('@@@ clearMocCustp =>', data);
            this._clearCustAll();
            this.$('#mocRes').val(null);
        },
        _clearCustOne: function (offset) {
            this.model.set('ContractNo' + offset, null);
            this.model.set('CustomerName' + offset, null);
            this.model.set('CustomerEtc' + offset, null);
        },
        _clearCustAll: function () {
            var that = this;
            _.each([1, 2, 3, 4], function (cnt) {
                that._clearCustOne(cnt);
            });
        },
        // 見積担当者ヘルパー処理 
        mitsuTanHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'mitsu_tanto_cd': data.code, 'mitsu_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'mitsu_tanto_cd': null, 'mitsu_tanto_nm': null});
                }
            });
        },
        // プルダウン生成処理
        setKeiyakuCif: function () {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/setkeiyakucif',
                data: {
                    seko_no: this.model.get('seko_no'),
                    k_cif_no: this.model.get('k_cif_no'),
                    m_cif_no: this.model.get('m_cif_no'),
                    s_cif_no: this.model.get('s_cif_no')
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        orgKeiyakuSbt = mydata.keiyakuCif;
                    }
                }
            });
        },
        // 故人年齢計算処理
        calcNereiK: function () {
            var era = this.model.get('k_wa_year');
            var seireki = this.model.get('k_birth_year');
            var month = this.model.get('k_birth_month');
            var day = this.model.get('k_birth_day');
            if ($.msiJqlib.isNullEx2(era) || $.msiJqlib.isNullEx2(month) || $.msiJqlib.isNullEx2(day)) {
                return;
            }
            var gengo = era.slice(0, 1);
            var wareki_y = era.slice(1);
            var seinengappi = seireki + '/' + month + '/' + day;
            if (!$.msiJqlib.chkDate(seinengappi)) { // 日付チェック
                seinengappi = null;
            }
            this.model.set('k_gengo', gengo);
            this.model.set('k_seinengappi_ymd', wareki_y + '/' + month + '/' + day);
            this.model.set('k_seinengappi_ymd_y', seinengappi);

            if (seinengappi) {
                var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
                var nakunariymd = null;
                if (nitei.length === 1) {
                    nakunariymd = nitei[0].get("nitei_date");
                    if (!$.msiJqlib.chkDate(nakunariymd)) { // 日付チェック
                        nakunariymd = null;
                    }
                }
                var pram = {
                    nakunariymd: nakunariymd,
                    seinengappi: seinengappi,
                    setData: function (data) { // コールバック
                        this.model.set("k_nenrei_man", data.man);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("k_nenrei_man", null);
            }
        },
        // 喪主年齢計算処理
        calcNereiM: function () {
            var era = this.model.get('m_wa_year');
            var seireki = this.model.get('m_birth_year');
            var month = this.model.get('m_birth_month');
            var day = this.model.get('m_birth_day');
            if ($.msiJqlib.isNullEx2(era) || $.msiJqlib.isNullEx2(month) || $.msiJqlib.isNullEx2(day)) {
                return;
            }
            var gengo = era.slice(0, 1);
            var wareki_y = era.slice(1);
            var seinengappi = seireki + '/' + month + '/' + day;
            if (!$.msiJqlib.chkDate(seinengappi)) { // 日付チェック
                seinengappi = null;
            }
            this.model.set('m_gengo', gengo);
            this.model.set('m_seinengappi_ymd', wareki_y + '/' + month + '/' + day);
            this.model.set('m_seinengappi_ymd_y', seinengappi);
            if (seinengappi) {
                var pram = {
                    nakunariymd: null,
                    seinengappi: seinengappi,
                    setData: function (data) {
                        this.model.set("m_nenrei_man", data.man);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("m_nenrei_man", null);
            }
        },
        // 年齢計算処理
        calcNerei: function (pram) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/calcnenrei',
                data: {
                    nakunariymd: pram.nakunariymd,
                    seinengappi: pram.seinengappi
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        if (_.has(pram, "setData")) {
                            pram.setData.apply(that, [mydata]);
                        }
                    }
                }
            });
        },
        setShuhaOther: function () {
            if (this.model.get("syuha_cd") === '99') { // その他
//                this.model.set("syuha_cd", '');
                this.$("#infochief2-tab #syuha_nm_other").show();
                this.$("#input-tab #syuha_nm_other").show();
//                this.$("#syuha_knm2").show();
//                this.$("#syuha_knm").hide();
            } else {
                this.$("#infochief2-tab #syuha_nm_other").hide();
                this.$("#input-tab #syuha_nm_other").hide();
//                this.$("#syuha_knm2").hide();
//                this.$("#syuha_knm").show();
            }
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        // マイナスのときに赤字クラスを追加する処理
        toggleClass: function (e) {
            appcst.toggleAkajiClass(this, ['shikijo_shiyou_prc', 'tuya_paku_su', 'tuya_paku_su2', 'tuya_shikijo_tanka', 'tuya_shikijo_tanka2', 'tuya_shikijo_prc1', 'tuya_shikijo_prc2', 'n_free1']);
        },
        // 宗派情報をクリアする
        clearSyuha: function (e) {
            if (e.val !== this.model.get("syushi_cd")) {
                this.model.set({'syuha_cd': null
                    , 'syuha_kbn': null
                    , 'syuha_nm': null
//                    , 'syuha_knm': null
//                    , 'jyusho_cd': null
//                    , 'jyusho_nm': null
//                    , 'jyusho_knm': null
//                    , 'temple_tel': null
//                    , 'temple_fax': null
                });
            }
            appcst.appView.setShuhaOther();
        },
        // 葬儀情報(ヘッダー)の台帳番号バインディング処理
        setHeaderDaichoNo: function (d1, d2, d3) {
            this.$("#hd_daicho_no").text(d1 + '-' + d2 + '-' + d3);
        },
        // 葬儀情報(ヘッダー)の喪主バインディング処理
        setHeadeMaddr: function (addr1, addr2) {
            if ($.msiJqlib.isNullEx2(addr1)) {
                addr1 = '';
            }
            if ($.msiJqlib.isNullEx2(addr2)) {
                addr2 = '';
            }
            this.$("#hd_mg_addr").text(addr1 + ' ' + addr2);
        },
        initialize: function () {
            this.listenTo(appcst.niteiCol, 'reset', this.addAllNiteiCol);
            this.listenTo(appcst.sekomemoCol, 'reset', this.addAllSekoMemoCol);
            this.listenTo(appcst.historyCol, 'reset', this.addAllHistoryCol);
            this.listenTo(appcst.appModel, 'change:infection_umu_kbn', this.setInfectionStatus);
            this.listenTo(appcst.appModel, 'change:moushi_cd', this.setLabels);
            this.listenTo(appcst.appModel, 'change:sd_hakko_kbn', this.setSdHakkoDate);
            this.listenTo(appcst.appModel, 'change:nyudensaki_cd1', function (){
                var shokuchi_kbn1 = this.model.get('shokuchi_kbn1');
                if(shokuchi_kbn1 == 1){
                    $("#nyudensaki_nm1").removeAttr('readonly');
                }else{
                    $("#nyudensaki_nm1").attr('readonly','readonly');
                }
            });
            this.listenTo(appcst.appModel, 'change:nyudensaki_cd2', function (){
                var shokuchi_kbn2 = this.model.get('shokuchi_kbn2');
                if(shokuchi_kbn2 == 1){
                    $("#nyudensaki_nm2").removeAttr('readonly');
                }else{
                    $("#nyudensaki_nm2").attr('readonly','readonly');
                }
            });
            this.listenTo(appcst.appModel, 'change:hanso_seko_no', function () {
                // 既に採番済の場合は非表示
                if (!$.msiJqlib.isNullEx2(this.model.get("seko_no"))) {
                    $("#hanso_seko_clear").hide();
                } else {
                    if ($.msiJqlib.isNullEx2(this.model.get("hanso_seko_no"))) {
                        $("#hanso_seko_clear").hide();
                    } else {
                        $("#hanso_seko_clear").show();
                    }
                }
            });
            this.listenTo(appcst.appModel, 'change:consult_seko_no', function () {
                // 既に採番済かつ搬送施行番号がある場合は非表示
                if (!$.msiJqlib.isNullEx2(this.model.get("seko_no")) && !$.msiJqlib.isNullEx2(this.model.get("hanso_seko_no"))) {
                    $("#consult_seko_clear").hide();
                } else {
                    if ($.msiJqlib.isNullEx2(this.model.get("consult_seko_no"))) {
                        $("#consult_seko_clear").hide();
                    } else {
                        $("#consult_seko_clear").show();
                    }
                }
            });
            // 安置場所
            this.listenTo(appcst.appModel, 'change:hs_anchi_cd', function () {
                var m = this.model;
                var hs_anchi_cd = m.get('hs_anchi_cd');
                if($.msiJqlib.isNullEx2(hs_anchi_cd)){
                    $('#hs_anchi_nm').attr('readonly', 'readonly');
                    m.set('hs_anchi_nm', null);
                    return;
                }
                _.each(data.dataKbns.hs_anchi_cd, function (v, k) {
                    var name_input_kbn = v.name_input_kbn;
                    var kbn_value_cd   = v.kbn_value_cd;
                    var kbn_value_lnm  = v.kbn_value_lnm;
                    if(kbn_value_cd === hs_anchi_cd){
                        if(name_input_kbn === '1'){
                            $('#hs_anchi_nm').removeAttr('readonly');
                        }else{
                            $('#hs_anchi_nm').attr('readonly', 'readonly');
                        }
                        // 画面初回表示時には設定しない
                        if(!$.msiJqlib.isNullEx2(m._pending.stickitChange)){
                            m.set('hs_anchi_nm', kbn_value_lnm);
                        }
                    }
                });
            });
            // 届出役所
            this.listenTo(appcst.appModel, 'change:todokede_kbn', function () {
                var m = this.model;
                var hs_anchi_cd = this.model.get('todokede_kbn');
                if($.msiJqlib.isNullEx2(hs_anchi_cd)){
                    $('#todokede_nm').attr('readonly', 'readonly');
                    m.set('todokede_nm', null);
                    return;
                }
                _.each(data.dataKbns.todokede_kbn, function (v, k) {
                    var name_input_kbn = v.name_input_kbn;
                    var kbn_value_cd   = v.kbn_value_cd;
                    var kbn_value_lnm  = v.kbn_value_lnm;
                    if(kbn_value_cd === hs_anchi_cd){
                        if(name_input_kbn === '1'){
                            $('#todokede_nm').removeAttr('readonly');
                        }else{
                            $('#todokede_nm').attr('readonly', 'readonly');
                        }
                        // 画面初回表示時には設定しない
                        if(!$.msiJqlib.isNullEx2(m._pending.stickitChange)){
                            m.set('todokede_nm', kbn_value_lnm);
                        }
                    }
                });
            });
            // 感染症有無区分設定処理
            this.setInfectionStatus();
            // 申込区分切り替え処理
            this.setLabels();
            // 見積式場切り替え処理
            this.setSideMenuStatus();
            // 赤字クラス切り替え処理
            this.toggleClass();
            // その他 診断書 発行予定日時
            this.$("#date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.$("#d_free1").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free2").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free3").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free4").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free5").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free6").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free7").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free8").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free9").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free10").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free11").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free12").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free13").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#d_free14").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));

            Backbone.Validation.bind(this);
            this.render();
        },
        render: function () {
            var m = this.model;
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.stickit();
            this.setSelect2();
            this.$('.radio_set').buttonset();
            return this;
        },
        // select2設定処理
        setSelect2: function () {
            // ヘッダー部
            var moushi_kbn = $.msiJqlib.objToArray3(data.dataKbns.moushi_kbn);
            if (!(data.dataSekoKihon.moushi_cd === MOUSHI_KBN_SEIZEN)) {
                // 事前相談を表示しない 2017/06/26 ADD Otake
                for (var idx = moushi_kbn.length - 1; 0 <= idx; idx--) {
                    if (moushi_kbn[idx].id === MOUSHI_KBN_SEIZEN) {
                        moushi_kbn.splice(idx, 1);
                    }
                }
            }
            // 担当者
            appcst.tanto_cds = data.dataKbns.tanto_mst;
            $.msiJqlib.setSelect2Com1(this.$(".sel_tanto_cd"), ($.extend({data: function () {
                    return {results: appcst.tanto_cds};
                }}, $.msiJqlib.setSelect2Default2)));
            // 申込区分
            $.msiJqlib.setSelect2Com1(this.$("#apply_type"), {data: moushi_kbn});
            // 葬儀区分
            $.msiJqlib.setSelect2Com1(this.$("#funeral_type"), {data: $.msiJqlib.objToArray3(data.dataKbns.sougi_kbn)});
            // 個人情報保護
            $.msiJqlib.setSelect2Com1(this.$("#personal_info"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.p_info)}, $.msiJqlib.setSelect2Default1)));
            // 会員区分
            $.msiJqlib.setSelect2Com1(this.$("#member"), {data: $.msiJqlib.objToArray3(data.dataKbns.kaiin_kbn)});
            // 見積式場
            $.msiJqlib.setSelect2Com1(this.$("#est_shikijo_cd"), ($.extend({data: data.dataKbns.est_shikijo}, $.msiJqlib.setSelect2Default2)));
            $.msiJqlib.setSelect2Com1(this.$("#seko_shikijo_cd"), ($.extend({data: data.dataKbns.est_shikijo}, $.msiJqlib.setSelect2Default1)));
            // 会員種別
            $.msiJqlib.setSelect2Com1(this.$("#kaiin_sbt_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kaiin_sbt_cd)}, $.msiJqlib.setSelect2Default2)));
            // アンケート送付先
            $.msiJqlib.setSelect2Com1(this.$("#anketo_soufu"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.anketo_soufu)}, $.msiJqlib.setSelect2Default1)));
            // DM送付
            $.msiJqlib.setSelect2Com1(this.$("#dm_soufu"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.dm_soufu)}, $.msiJqlib.setSelect2Default1)));
            appcst.jizenDenpyo = data.dataKbns.jizenDenpyo;
            $.msiJqlib.setSelect2Com1(this.$("#jizen_denpyo"), ($.extend({data: function () {
                    return {results: appcst.jizenDenpyo};
                }}, $.msiJqlib.setSelect2Default1)));
            // 受付情報タブ
            // 配偶者
            $.msiJqlib.setSelect2Com1(this.$("#spouse"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.haigu_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #birthday_era"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.gengo)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #birthday_month"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.month)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #birthday_day"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.day)}, $.msiJqlib.setSelect2Default1)));
            // 故人CIFNoステータス
            $.msiJqlib.setSelect2Com1(this.$("#k_cif_status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.cif_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 故人CIFNoステータス
            $.msiJqlib.setSelect2Com1(this.$("#k_cif_status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.cif_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 遺族顧客No.ステータス
            $.msiJqlib.setSelect2Com1(this.$("#izoku_cif_status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.cif_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 安置場所CD
            $.msiJqlib.setSelect2Com1(this.$("#hs_anchi_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.hs_anchi_cd)}, $.msiJqlib.setSelect2Default2)));
            // ペースメーカー有無区分
            $.msiJqlib.setSelect2Com1(this.$("#pacemaker"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.pacemaker_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 感染症有無区分
            $.msiJqlib.setSelect2Com1(this.$("#infection_umu"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.infection_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 勤務先
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #employee"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kinmu_kbn_k)}, $.msiJqlib.setSelect2Default1)));
            // 連絡先続柄
            $.msiJqlib.setSelect2Com1(this.$("#renraku_zoku"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 棺確認
            $.msiJqlib.setSelect2Com1(this.$("#hitsugi_check"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.hitsugi_check)}, $.msiJqlib.setSelect2Default1)));
            // お悔やみ掲載区分
            $.msiJqlib.setSelect2Com1(this.$("#kuyami_keisai"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.pacemaker_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 喪主請求情報タブ
            // 続柄
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #s_chief_relationship"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 喪主様からみた続柄
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #s_chief_relationship2"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #birthday_era"), {data: $.msiJqlib.objToArray3(data.dataKbns.gengo)});
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #birthday_month"), {data: $.msiJqlib.objToArray3(data.dataKbns.month)});
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #birthday_day"), {data: $.msiJqlib.objToArray3(data.dataKbns.day)});
            // 喪主CIFNoステータス
            $.msiJqlib.setSelect2Com1(this.$("#m_cif_status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.cif_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 請求先CIFNoステータス
            $.msiJqlib.setSelect2Com1(this.$("#s_cif_status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.cif_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 勤務先
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #employee"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kinmu_kbn_m)}, $.msiJqlib.setSelect2Default1)));
            // 葬儀委員長勤務先
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #fc_kinmusaki_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kinmu_kbn_m)}, $.msiJqlib.setSelect2Default1)));
            // 打合せ事項①タブ
            // 葬儀形式
            $.msiJqlib.setSelect2Com1(this.$("#funeral_style"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.keishiki_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 宗旨区分
            $.msiJqlib.setSelect2Com1(this.$(" #infochief2-tab #syushi_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.syushi_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$(" #input-tab #syushi_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.syushi_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 宗派区分
            appcst.syuha_kbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
            $.msiJqlib.setSelect2Com1(this.$(" #infochief2-tab #syuha_cd"), ($.extend({data: function () {
                    return {results: appcst.syuha_kbns};
                }}, $.msiJqlib.setSelect2Default2)));
            $.msiJqlib.setSelect2Com1(this.$(" #input-tab #syuha_cd"), ($.extend({data: function () {
                    return {results: appcst.syuha_kbns};
                }}, $.msiJqlib.setSelect2Default2)));
            // 初七日
            $.msiJqlib.setSelect2Com1(this.$("#shonanoka_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.shonanoka_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 寺紹介者
            $.msiJqlib.setSelect2Com1(this.$("#tera_shokai"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.tera_shokai)}, $.msiJqlib.setSelect2Default1)));
            // 打合せ事項②タブ
            // 弔意香典
            $.msiJqlib.setSelect2Com1(this.$("#choi_koden"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.choi_koden_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 弔意供花供物
            $.msiJqlib.setSelect2Com1(this.$("#choi_kumotu"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.choi_kumotu_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 別注商品用部門
            $.msiJqlib.setSelect2Com1(this.$("#bechu_shohin_bumon"), ($.extend({data: data.dataKbns.bechuShohinBumon}, $.msiJqlib.setSelect2Default1)));
            // 契約種別CIF
            appcst.keiyaku_sbt = orgKeiyakuSbt;
            // 別注商品
            if (data.dataSekoKihon.choi_kyoka_cd == CHOI_KUMOTU_PRC) {
                appcst.bechu_shohin = $.msiJqlib.objToArray3(data.dataKbns.prc_unity_kbn);
            } else {
                appcst.bechu_shohin = data.bechuShohin;
            }
            $.msiJqlib.setSelect2Com2(this.$("#bechu_shohin"), ($.extend({data: function () {
                    return {results: appcst.bechu_shohin};
                }}, $.msiJqlib.setSelect2Default1)));
            // 可否区分
            $.msiJqlib.setSelect2Com1(this.$("#seika_fa"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.fa_seika_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#sikimi_fa"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.fa_sikimi_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#sakaki_fa"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.fa_sakaki_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#hanawa_fa"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.fa_hanawa_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#kudamono_fa"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.fa_kudamono_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#can_fa"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.fa_can_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 供花問い合わせ
            $.msiJqlib.setSelect2Com1(this.$("#seika_form"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.seika_form_kbn)}, $.msiJqlib.setSelect2Default1)));
            /** その他タブ*/
            // 診断書発行
            $.msiJqlib.setSelect2Com1(this.$("#publish"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.portrait_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 診断書手続き
            $.msiJqlib.setSelect2Com1(this.$("#steps"), ($.extend({data: _steps}, $.msiJqlib.setSelect2Default1)));
            // 診断書コピー
            $.msiJqlib.setSelect2Com1(this.$("#copys"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.portrait_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 死亡診断書
            $.msiJqlib.setSelect2Com1(this.$("#certificate"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.portrait_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 御写真
            $.msiJqlib.setSelect2Com1(this.$("#portrait"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.portrait_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 届出役所区分
            $.msiJqlib.setSelect2Com1(this.$("#todokede_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.todokede_kbn)}, $.msiJqlib.setSelect2Default2)));
            // アンケート回収
            $.msiJqlib.setSelect2Com1(this.$("#anketo_kaisyu"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.portrait_kbn)}, $.msiJqlib.setSelect2Default1)));
        },
        addNiteiOne: function (nitei) {
            var v = new NiteiView({model: nitei});
            this.$("#infodate-tab #infodate").append(v.render().el);
        },
        addAllNiteiCol: function (collection) {
            var $infodate = this.$("#infodate-tab #infodate");
            $infodate.find('fieldset').remove();
            collection.each(this.addNiteiOne, this);
        },
        addSekoMemoOne: function (memo) {
            var v = new SekoMemoView({model: memo});
            this.$("#memo_msi").append(v.render().el);
        },
        addAllSekoMemoCol: function (collection) {
            var $memoMsi = this.$("#memo_msi");
            $memoMsi.find('tbody').remove();
            collection.each(this.addSekoMemoOne, this);
        },
        addHistoryOne: function (history) {
            var v = new HistoryView({model: history});
            this.$("#history_msi").append(v.render().el);
        },
        addAllHistoryCol: function (collection) {
            var $historyMsi = this.$("#history_msi");
            $historyMsi.find('fieldset').remove();
            collection.each(this.addHistoryOne, this);
        },
        // 感染症有無区分設定処理
        setInfectionStatus: function () {
            var val = this.model.get('infection_umu_kbn');
            if (val == UMU_KBN_NO) {
                this.$("#infection_txt").attr("disabled", "disabled");
                this.model.set('infection_txt', null);
            } else {
                this.$("#infection_txt").removeAttr("disabled");
            }
        },
        // 弔意対応変更時設定処理
        setChoiPrcStatus: function () {
            var val = this.model.get('choi_kyoka_cd');
            this.model.set('choi_shohin_bumon_cd', null);
            this.model.set('choi_shohin_cd', null);
            this.model.set('choi_tokuchu_prc', null);
            // 商品統一のときはプルダウンのみ有効にする
            if (val == CHOI_KUMOTU_SHOHIN) {
                this.$("#bechu_shohin_bumon").removeAttr("disabled");
                this.$("#bechu_shohin").removeAttr("disabled");
                this.$("#choi_tokuchu_prc").attr("disabled", "disabled");
                // 可否を未設定にして編集不可
                this.model.set('choi_kahi_01', null);
                this.model.set('choi_kahi_02', null);
                this.model.set('choi_kahi_03', null);
                this.model.set('choi_kahi_04', null);
                this.model.set('choi_kahi_05', null);
                this.model.set('choi_kahi_06', null);
                this.$("#seika_fa").attr("disabled", "disabled");
                this.$("#sikimi_fa").attr("disabled", "disabled");
                this.$("#sakaki_fa").attr("disabled", "disabled");
                this.$("#hanawa_fa").attr("disabled", "disabled");
                this.$("#kudamono_fa").attr("disabled", "disabled");
                this.$("#can_fa").attr("disabled", "disabled");
            } else if (val == CHOI_KUMOTU_PRC) { // 金額統一のときはプルダウンのみ有効にする
                this.$("#bechu_shohin_bumon").removeAttr("disabled");
                this.$("#bechu_shohin").removeAttr("disabled");
                this.$("#choi_tokuchu_prc").attr("disabled", "disabled");
                // 可否を可にして編集可能にする
                this.model.set('choi_kahi_01', '1');
                this.model.set('choi_kahi_02', '1');
                this.model.set('choi_kahi_03', '1');
                this.model.set('choi_kahi_04', '1');
                this.model.set('choi_kahi_05', '1');
                this.model.set('choi_kahi_06', '1');
                this.$("#seika_fa").removeAttr("disabled");
                this.$("#sikimi_fa").removeAttr("disabled");
                this.$("#sakaki_fa").removeAttr("disabled");
                this.$("#hanawa_fa").removeAttr("disabled");
                this.$("#kudamono_fa").removeAttr("disabled");
                this.$("#can_fa").removeAttr("disabled");
            } else if (val == CHOI_KUMOTU_SP) { // 金額特注のときは金額入力できるようにする
                this.$("#bechu_shohin_bumon").removeAttr("disabled");
                this.$("#bechu_shohin").removeAttr("disabled");
                this.$("#choi_tokuchu_prc").removeAttr("disabled");
                // 可否を未設定にして編集不可
                this.model.set('choi_kahi_01', null);
                this.model.set('choi_kahi_02', null);
                this.model.set('choi_kahi_03', null);
                this.model.set('choi_kahi_04', null);
                this.model.set('choi_kahi_05', null);
                this.model.set('choi_kahi_06', null);
                this.$("#seika_fa").attr("disabled", "disabled");
                this.$("#sikimi_fa").attr("disabled", "disabled");
                this.$("#sakaki_fa").attr("disabled", "disabled");
                this.$("#hanawa_fa").attr("disabled", "disabled");
                this.$("#kudamono_fa").attr("disabled", "disabled");
                this.$("#can_fa").attr("disabled", "disabled");
            } else {
                // それ以外はプルダウンと金額入力は非活性
                this.$("#bechu_shohin_bumon").attr("disabled", "disabled");
                this.$("#bechu_shohin").attr("disabled", "disabled");
                this.$("#choi_tokuchu_prc").attr("disabled", "disabled");
                if (val == CHOI_KUMOTU_JITAI) {
                    // 可否を未設定にして編集不可
                    this.model.set('choi_kahi_01', null);
                    this.model.set('choi_kahi_02', null);
                    this.model.set('choi_kahi_03', null);
                    this.model.set('choi_kahi_04', null);
                    this.model.set('choi_kahi_05', null);
                    this.model.set('choi_kahi_06', null);
                    this.$("#seika_fa").attr("disabled", "disabled");
                    this.$("#sikimi_fa").attr("disabled", "disabled");
                    this.$("#sakaki_fa").attr("disabled", "disabled");
                    this.$("#hanawa_fa").attr("disabled", "disabled");
                    this.$("#kudamono_fa").attr("disabled", "disabled");
                    this.$("#can_fa").attr("disabled", "disabled");
                    // 辞退の場合はHiKARI項目の供花・供物利用をなしに設定する
                    if ($.msiJqlib.isNullEx2(appcst.huhoInfoModel.get('flower_use_flag'))) {
                        appcst.huhoInfoModel.set('flower_use_flag', '0');
                    } else if ($.msiJqlib.isNullEx2(appcst.huhoInfoModel.get('flower_use_flag'))) {
                        appcst.huhoInfoModel.set('flower_use_flag', null);
                    }
                    if ($.msiJqlib.isNullEx2(appcst.huhoInfoModel.get('offerings_use_flag'))) {
                        appcst.huhoInfoModel.set('offerings_use_flag', '0');
                    } else if ($.msiJqlib.isNullEx2(appcst.huhoInfoModel.get('offerings_use_flag'))) {
                        appcst.huhoInfoModel.set('offerings_use_flag', null);
                    }
                } else if (val == CHOI_KUMOTU_NOSHITEI) {
                    // 可否を可にして編集可能にする
                    this.model.set('choi_kahi_01', '1');
                    this.model.set('choi_kahi_02', '1');
                    this.model.set('choi_kahi_03', '1');
                    this.model.set('choi_kahi_04', '1');
                    this.model.set('choi_kahi_05', '1');
                    this.model.set('choi_kahi_06', '1');
                    this.$("#seika_fa").removeAttr("disabled");
                    this.$("#sikimi_fa").removeAttr("disabled");
                    this.$("#sakaki_fa").removeAttr("disabled");
                    this.$("#hanawa_fa").removeAttr("disabled");
                    this.$("#kudamono_fa").removeAttr("disabled");
                    this.$("#can_fa").removeAttr("disabled");
                }
            }
        },
        // 申込区分変更によるラベル切り替え処理
        setLabels: function () {
            var moushi = this.model.get('moushi_kbn');
            // 貸式場・エンバー・搬送のみの特殊処理
            if (moushi == MOUSHI_KBN_SHIKIJO || moushi == MOUSHI_KBN_EMB || moushi == MOUSHI_KBN_HANSO) {
                $('#bill_sekyu .lbl_name').addClass('require');
                $('#bill_sekyu .lbl_address').addClass('require');
                $('#bill_sekyu .lbl_sekyu_tel').addClass('require');
                $('#bill_soufu .lbl_name').addClass('require');
                $('#bill_soufu .lbl_address').addClass('require');
                $('#bill_soufu .lbl_soufu_tel').addClass('require');
                $('#bill_ryosyu_soufu .lbl_name').addClass('require');
                $('#bill_ryosyu_soufu .lbl_address').addClass('require');
                $('#bill_ryosyu_soufu .lbl_ryoshu_tel').addClass('require');
            } else {
                $('#bill_sekyu .lbl_name').removeClass('require');
                $('#bill_sekyu .lbl_address').removeClass('require');
                $('#bill_sekyu .lbl_sekyu_tel').removeClass('require');
                $('#bill_soufu .lbl_name').removeClass('require');
                $('#bill_soufu .lbl_address').removeClass('require');
                $('#bill_soufu .lbl_soufu_tel').removeClass('require');
                $('#bill_ryosyu_soufu .lbl_name').removeClass('require');
                $('#bill_ryosyu_soufu .lbl_address').removeClass('require');
                $('#bill_ryosyu_soufu .lbl_ryoshu_tel').removeClass('require');
            }
        },
        // 見積式場変更によるサイドメニュー切り替え処理
        setSideMenuStatus: function () {
            var sideMenu = data.sideMenuDataCol;
            var moushi_kbn = this.model.get('moushi_kbn');
            if (moushi_kbn != MOUSHI_KBN_EMB && moushi_kbn != MOUSHI_KBN_HANSO) {
                if ($.msiJqlib.isNullEx2(this.model.get('est_shikijo_cd'))) {
                    // お客様情報以外非表示
                    _.each(sideMenu, function (item) {
                        var css = item.css_class;
                        if (css != 'customer') {
                            $("#side ." + css).hide();
                        }
                    });
                } else {
                    _.each(sideMenu, function (item) {
                        var css = item.css_class;
                        $("#side ." + css).show();
                    });
                }
            }
        },
        replaceLabel: function ($tagets, substr, newsubstr) {
            $tagets.each(function () {
                var $taget = $(this);
                var lable = $taget.text().replace(substr, newsubstr);
                $taget.text(lable);
            });
        },
        // チェックボックス切り替え処理
        setCheckBox: function (e, pro, target) {
            var val = $(target + ':checked').val();
            this.model.set(pro, val === "1" ? "1" : "0");
        },
        // タブ切り替え処理
        changeTab: function (e) {
            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.tab li'), $(e.currentTarget));
            var $tabId = $.msiJqlib.getTabId();
            if ($tabId.is('#tab-report-info')) {
                // 報告書タブ設定処理
                appcst.resetReport();
            } else if ($tabId.is('#tab-huho-info')) {
                // 訃報連絡タブ設定処理
                appcst.resethuhoInfo();
            } else if ($tabId.is('#tab-result-info')) {
                // 結果情報
                appcst.resetresultInfo();
            }
            // 互助会確認の場合、加入状況をクリックする
            if ($tabId.is('#tab-kaiin-info')) {
                this.$("#member_group_set #member_group_1").click();
            }
        },
        // 申込区分変更によるラベル切り替え処理
        setSeikaFormInfo: function () {
            var item = data.dataKbns.seika_form_kbn;
            var m = this.model;
            var information_cd = m.get('information_cd');
            var info = null;
            m.set('seika_contact', null);
            m.set('form_tel', null);
            m.set('form_fax', null);
            if (!$.msiJqlib.isNullEx2(information_cd)) {
                // その他の時は直接入力できるようにする
                if (information_cd == SEIKA_INFO_OTHER) {
                    this.$("#seika_contact").removeAttr("disabled");
                    this.$("#form_tel").removeAttr("disabled");
                    this.$("#form_fax").removeAttr("disabled");
                } else {
                    this.$("#seika_contact").attr("disabled", "disabled");
                    this.$("#form_tel").attr("disabled", "disabled");
                    this.$("#form_fax").attr("disabled", "disabled");
                    _.each(item, function (v) {
                        if (information_cd == v.kbn_value_cd) {
                            if (!$.msiJqlib.isNullEx2(v.biko)) {
                                info = v.biko.split(',');
                                m.set('form_tel', info[0]);
                                m.set('form_fax', info[1]);
                            } else {
                                m.set('form_tel', null);
                                m.set('form_fax', null);
                            }
                            m.set('seika_contact', v.kbn_value_snm);
                        }
                    });
                }
            }
        },
        // 診断書発行が未済で発行日の活性非活性を決定する
        setSdHakkoDate: function () {
            var sd_hakko_kbn = this.model.get('sd_hakko_kbn');
            // 済の場合は非活性
            if (!$.msiJqlib.isNullEx2(sd_hakko_kbn) && sd_hakko_kbn == '1') {
                this.model.set('sd_yotei_ymd', null);
                this.model.set('sd_yotei_date', null);
                this.model.set('sd_yotei_time', null);
                $('#infomisc-tab #date').datepicker("option", "disabled", true);
                $('#infomisc-tab #time').timepicker("option", "disabled", true);
                $('#infomisc-tab #date').attr('disabled', 'disabled');
                $('#infomisc-tab #time').attr('disabled', 'disabled');
            } else {
                $('#infomisc-tab #date').removeAttr('disabled');
                $('#infomisc-tab #time').removeAttr('disabled');
                $('#infomisc-tab #date').datepicker("option", "disabled", false);
                $('#infomisc-tab #time').timepicker("option", "disabled", false);
            }
        },
        isInputOk: function () {

            var aMsg = [];
            // 施行基本フリーモデルチェック
            var result = appcst.kfModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    if ($.inArray(v, aMsg) < 0) {
                        aMsg.push(v);
                    }
                });
            }
            // 施行基本モデルチェック
            var result = appcst.appModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 施行日程コレクションチェック
            appcst.niteiCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                    });
                }
            });

            // 請求先情報モデルチェック
            var result = appcst.sekyuModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }

            // 施行互助会会員コレクションチェック
            appcst.gojokaiMemberCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                        ;
                    });
                }
            });

            // 施行互助会情報モデルチェック
            var result = appcst.gojokaiInfoModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 契約先情報モデルチェック
            var result = appcst.sekoKeiyakusakiInfoModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }

            // 施行引継書モデルチェック
            var result = appcst.hikitsugiModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 手配依頼書モデルチェック
            var result = appcst.tehaiIraiModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 式場移動コレクションチェック
            appcst.shikijoIdoCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                        ;
                    });
                }
            });
            // 施行情報打合せ履歴コレクションチェック
            appcst.sekomemoCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                        ;
                    });
                }
            });
            // 結果報告モデル
            var result = appcst.resultInfoModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 施行者カードモデル
            var result = appcst.sekoshaModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // NG
            if (aMsg.length > 0) {
                var errClsNm = ".error1";
                var $li = this.$('.tab');
                if (this.$("#input-tab").find(errClsNm).length) {   // 受付情報
                    $li.find("#tab-input-info a").click();
                } else if (this.$("#infochief-tab").find(errClsNm).length) {    // 喪主請求情報
                    $li.find("#tab-seikyu-info a").click();
                } else if (this.$("#infochief2-tab").find(errClsNm).length) {   // 打合せ事項①
                    $li.find("#tab-input-info2 a").click();
                } else if (this.$("#infochief3-tab").find(errClsNm).length) {   // 打合せ事項②
                    $li.find("#tab-input-info3 a").click();
                } else if (this.$("#infodate-tab").find(errClsNm).length) { // 施行日程
                    $li.find("#tab-seko-info a").click();
                } else if (this.$("#infomember-tab").find(errClsNm).length) {   //会員情報
                    $li.find("#tab-kaiin-info a").click();
                } else if (this.$("#huho-tab").find(errClsNm).length) {   // 訃報連絡
                    $li.find("#tab-huho-info a").click();
                } else if (this.$("#infomisc-tab").find(errClsNm).length) { // その他
                    $li.find("#tab-other-info a").click();
                } else if (this.$("#report-tab").find(errClsNm).length) {   // 報告書
                    $li.find("#tab-report-info a").click();
                }
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }

            // OK
            $.msiJqlib.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },
        doSave: function (mode) {
            if (!this.isInputOk()) {
                return;
            }
            if (!this.kasoCheck()) {
                return;
            }
            if (!this.gojoYotoCheck()) {
                return;
            }
            // 訃報連携済なら常に連携
            // 施設予約の場合はスルー
            var huho_id = appcst.huhoInfoModel.get('huho_id');
            if (!$.msiJqlib.isNullEx2(huho_id) && mode !== 'shikijo') {
                mode = 'huhorenkei';
            }
            if (this.model.get('moushi_kbn') == "5") {
                if (!$.msiJqlib.isNullEx2(this.model.get('copy_moto_seko_no'))) {
                    $.msiJqlib.showWarn('事前相談施行からコピーした施行は事前相談にはできません');
                    return;
                }
            }
            this.checkKokyaku(mode);
        },
        doSaveSub: function (mode) {
            var bumonCd = $('#hall_cd').val();
            var bumonChanged = bumonCd !== appcst.appModel.get("bumon_cd");
            // 見積確定
            if (!$.msiJqlib.isNullEx2(appcst.appModel.get("jichu_kakute_ymd")) && bumonChanged) {
                this.exeCheck(mode);
            } else {
                //                this.shohinCheck(mode);
                this.exeSave(mode);
            }   
        },
        kasoCheck: function () {
            var flg = true;
            var niteiKaso = appcst.niteiCol.where({nitei_kbn: NITEI_KASO});
            var sj_yoyaku_kbn = appcst.tehaiIraiModel.get('sj_yoyaku_kbn'); // 火葬場予約区分
            var sj_yoyaku_no = appcst.tehaiIraiModel.get('sj_yoyaku_no');   // 火葬場予約番号
            var rh_tanto = appcst.tehaiIraiModel.get('rh_tehai_tanto_nm');
            var rh_date = appcst.tehaiIraiModel.get('rh_tehai_date');
            var kaso_check = appcst.appModel.get('kaso_check'); // 火葬なしチェック
            // 火葬なしにチェックがある場合
            if (kaso_check == '1') {
                // 予約番号の有無をチェックする
                if (!$.msiJqlib.isNullEx2(sj_yoyaku_no)) {
                    $.msiJqlib.showErr('斎場の予約番号が入力されています。');
                    flg = false;
                }
                // 霊柩搬送情報の有無をチェックする
                if (!$.msiJqlib.isNullEx2(rh_tanto) || !$.msiJqlib.isNullEx2(rh_date)) {
                    $.msiJqlib.showErr('霊柩搬送情報が入力されています。');
                    flg = false;
                }
            } else {
                // 火葬の日時と場所がある場合のみチェックする
                var kaso_date = null;
                var kaso_basho = null;
                if (niteiKaso.length === 1) {
                    kaso_date = niteiKaso[0].get("nitei_date");
                    kaso_basho = niteiKaso[0].get("basho_cd");
                    if (!$.msiJqlib.isNullEx2(kaso_date) && !$.msiJqlib.isNullEx2(kaso_basho)) {
                        // 予約区分が「要」の場合は予約番号の有無をチェックする
                        if (sj_yoyaku_kbn == '0' && $.msiJqlib.isNullEx2(sj_yoyaku_no)) {
                            $.msiJqlib.showErr('斎場の予約番号が入力されていません。');
                            flg = false;
                        }
                    }
                }
            }

            return flg;
        },
        usePrcCheck: function () {
            var flg = true;
            var kaiin_kbn = appcst.appModel.get('kaiin_kbn');
            // 会員区分が互助会以外はスルーする
            if (kaiin_kbn != KAIIN_KBN_GOJO) {
                return flg;
            }
            var use_keiyaku_gaku = appcst.gojokaiInfoModel.get('use_keiyaku_gaku');
            if ($.msiJqlib.isNullEx2(use_keiyaku_gaku)) {
                return flg;
            }
            var use_prc = 0;
            var yoto_cnt = 0;
            if (appcst.gojokaiMemberCol.length != 0) {
                _.each(appcst.gojokaiMemberCol.models, function (item) {
                    if (!$.msiJqlib.isNullEx2(item.get('yoto_kbn')) && (item.get('yoto_kbn') == YOTO_COURSE || item.get('yoto_kbn') == YOTO_PLAN) && item.get('delete_check') == '0') {
                        use_prc += changeToNum(item.get('keiyaku_gaku'));
                        yoto_cnt++;
                    }
                });
            }
            // ご利用コースの契約金額と一致しなければfalse
            if (yoto_cnt != 0 && use_keiyaku_gaku != use_prc) {
                flg = false;
            }

            return flg;
        },
        gojoYotoCheck: function () {
            var flg = true;
            var kaiin_kbn = appcst.appModel.get('kaiin_kbn');
            // 会員区分が互助会以外はスルーする
//            if (kaiin_kbn != KAIIN_KBN_GOJO) {
//                return flg;
//            }
            var yoto_cnt = 0;
            if (appcst.gojokaiMemberCol.length != 0) {
                _.each(appcst.gojokaiMemberCol.models, function (item) {
                    if (!$.msiJqlib.isNullEx2(item.get('yoto_kbn')) && (item.get('yoto_kbn') == YOTO_COURSE) && item.get('delete_check') == '0') {
                        yoto_cnt++;
                    }
                });
            }
            // コース施行が２件以上あればfalse
//            if (yoto_cnt > 1) {
//                $.msiJqlib.showErr('用途にコース施行が複数件選択されています。');
//                flg = false;
//            }

            return flg;
        },
        checkKokyaku: function (mode) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/checkkokyaku',
                data: {dataAppJson: JSON.stringify(appcst.appModel.toJSON())},
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        that.doSaveSub(mode);
                    } else if (mydata.status === 'INFO') {
                        if (!confirm(mydata.msg)) {
                            return;
                        } else {
                            that.doSaveSub(mode);
                        }
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        exeCheck: function (mode) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/CheckGetujiFixInput',
                data: {dataAppJson: JSON.stringify(appcst.appModel.toJSON())},
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
//                        that.shohinCheck(mode);
                        that.exeSave(mode);
                    } else if (mydata.status === 'INFO') {
                        $.msiJqlib.setProgressing(false);
                        if (!confirm(mydata.msg)) {
                            return;
                        }
                        $.msiJqlib.setProgressing(true);
//                        that.shohinCheck(mode);
                        that.exeSave(mode);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        shohinCheck: function (mode) {
            var that = this;
            // 施行基本情報
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            // 施行互助会加入者イコール
            var sekoGojokaiMemberEq = $.msiJqlib.isEqual(appcst.gojokaiMemberCol.toJSON(), orgDataGojokaiMemberCol);
            // チェンジフラグ設定
            var changeFlg = JSON.stringify({
                gojokaiMemberChangeFlg: !sekoGojokaiMemberEq,
            });
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/shohincheck',
                data: {
                    dataSekoKihonJson: dataSekoKihonJson,
                    changeFlg: changeFlg
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        that.exeSave(mode);
                    } else if (mydata.status === 'NG') {
                        if (!confirm(mydata.msg)) {
                            return;
                        }
                        that.exeSave(mode);
                    }
                }
            });
        },
        exeSave: function (mode) {
            var t = this;
            // 施行基本イコール
            var sekoKihonEq = $.msiJqlib.isEqual(appcst.appModel.toJSON(), orgDataApp);
            // 施行基本汎用フリー情報イコール
            var sekoKihonFreeEq = $.msiJqlib.isEqual(appcst.kfModel.toJSON(), orgDataKihonFree);
            // 施行日程イコール
            var sekoNiteiEq = $.msiJqlib.isEqual(appcst.niteiCol.toJSON(), orgDataNiteiCol);
            // 請求先情報イコール
            var sekyuInfoEq = $.msiJqlib.isEqual(appcst.sekyuModel.toJSON(), orgDataSekyuInfo);
            // 施行互助会情報イコール
            var sekoGojokaiInfoEq = $.msiJqlib.isEqual(appcst.gojokaiInfoModel.toJSON(), orgDataGojokaiInfo);
            // 施行契約先情報イコール
            var sekoKeiyakusakiInfoEq = $.msiJqlib.isEqual(appcst.sekoKeiyakusakiInfoModel.toJSON(), orgDataSekoKeiyakusakiInfo);
            // 施行互助会加入者イコール
            var sekoGojokaiMemberEq = $.msiJqlib.isEqual(appcst.gojokaiMemberCol.toJSON(), orgDataGojokaiMemberCol);
            //  施行引継書イコール
            var hikitsugiEq = $.msiJqlib.isEqual(appcst.hikitsugiModel.toJSON(), appcst.orgHikitsugiModelJson);
            //  手配依頼書イコール
            var tehaiIraiEq = $.msiJqlib.isEqual(appcst.tehaiIraiModel.toJSON(), appcst.orgTehaiIraiModelJson);
            //  式場移動イコール
            var shikijoIdoEq = $.msiJqlib.isEqual(appcst.shikijoIdoCol.toJSON(), appcst.orgShikijoIdoColJson);
            //  施行受付履歴イコール
            var sekoMemoEq = $.msiJqlib.isEqual(appcst.sekomemoCol.toJSON(), orgDataSekoMemoCol);
            //  訃報連絡イコール
            var huhoInfoEq = $.msiJqlib.isEqual(appcst.huhoInfoModel.toJSON(), appcst.orghuhoInfoModelJson);
            // 結果情報イコール
            var resultInfoEq = $.msiJqlib.isEqual(appcst.resultInfoModel.toJSON(), appcst.orgresultInfoModelJson);
            // 施行者カードイコール
            var sekoshaEq = $.msiJqlib.isEqual(appcst.sekoshaModel.toJSON(), appcst.orgsekoshaModelJson);
            var kaiinKbn = appcst.appModel.get("kaiin_kbn");
            var $tabId = $.msiJqlib.getTabId();
            if ($.msiJqlib.isNullEx2(mode)) {
                if (sekoKihonEq && sekoNiteiEq && sekyuInfoEq && sekoGojokaiInfoEq && sekoGojokaiMemberEq
                        && sekoKeiyakusakiInfoEq && hikitsugiEq && tehaiIraiEq && shikijoIdoEq && sekoKihonFreeEq
                        && sekoMemoEq && huhoInfoEq && resultInfoEq && sekoshaEq) {
                    // 保存時のタブが会員情報タブかつその他加入確認タブの場合は変更がなくても印刷可能にする
//                    if (kaiinKbn == KAIIN_KBN_COM) {
//                        if ($tabId.is('#tab-kaiin-info')) {
//                            if (this.$("#member_group_set .lbl_member_group_2").hasClass('ui-state-active')) {
//                                t.doPrint();
//                                return;
//                            }
//                        }
//                    }
                    $.msiJqlib.showInfo('データの変更がありません');
                    return;
                }
            } else {
                if (mode != 'shikijo' && mode != 'huhorenkei') {
                    if (sekoKihonEq && sekoNiteiEq && sekyuInfoEq && sekoGojokaiInfoEq && sekoGojokaiMemberEq
                            && sekoKeiyakusakiInfoEq && hikitsugiEq && tehaiIraiEq && shikijoIdoEq && sekoKihonFreeEq
                            && sekoMemoEq && huhoInfoEq && resultInfoEq && sekoshaEq) {
                        // 保存時のタブが会員情報タブかつその他加入確認タブの場合は変更がなくても印刷可能にする
//                        if (kaiinKbn == KAIIN_KBN_COM) {
//                            if ($tabId.is('#tab-kaiin-info')) {
//                                if (this.$("#member_group_set .lbl_member_group_2").hasClass('ui-state-active')) {
//                                    t.doPrint();
//                                    return;
//                                }
//                            }
//                        }
                        $.msiJqlib.showInfo('データの変更がありません');
                        return;
                    }
                }
            }
            if ($.msiJqlib.isNullEx2(mode)) {
                // 会員区分が互助会の時に金額が一致しなければ警告をだす
                if (!this.usePrcCheck()) {
                    if (!confirm('ご利用コースの契約金額とご利用金額が一致しませんがよろしいでしょうか。')) {
                        return;
                    }
                }
            } else {
                if (mode != 'shikijo' && mode != 'huhorenkei') {
                    // 会員区分が互助会の時に金額が一致しなければ警告をだす
                    if (!this.usePrcCheck()) {
                        if (!confirm('ご利用コースの契約金額とご利用金額が一致しませんがよろしいでしょうか。')) {
                            return;
                        }
                    }
                }
            }
            var huho_flg = false;
            if (mode == 'huhorenkei') {
                huho_flg = true;
            }
            // チェンジフラグ設定
            var changeFlg = JSON.stringify({
                kihonChangeFlg: !sekoKihonEq,
                kihonFreeChangeFlg: !sekoKihonFreeEq,
                niteiChangeFlg: !sekoNiteiEq,
                sekyuInfoChangeFlg: !sekyuInfoEq,
                gojokaiInfoChangeFlg: !sekoGojokaiInfoEq,
                sekoKeiyakusakiInfoChangeFlg: !sekoKeiyakusakiInfoEq,
                gojokaiMemberChangeFlg: !sekoGojokaiMemberEq,
                hikitsugiChangeFlg: !hikitsugiEq,
                tehaiIraiChangeFlg: !tehaiIraiEq,
                shikijoChangeFlg: !shikijoIdoEq,
                sekoMemoChangeFlg: !sekoMemoEq,
                huhoInfoChangeFlg: !huhoInfoEq,
                resultInfoChangeFlg: !resultInfoEq,
                sekoshaChangeFlg: !sekoshaEq,
            });
            var bumonCd = $('#hall_cd').val();
            appcst.appModel.set('bumon_cd', bumonCd);
            // 施行基本情報
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            // 施行基本汎用フリー情報
            var dataSekoKihonFreeJson = JSON.stringify(appcst.kfModel.toJSON());
            // 施行日程情報
            var dataNiteiColJson = JSON.stringify(appcst.niteiCol.toJSON());
            // 請求先情報
            var dataSekyuInfoJson = JSON.stringify(appcst.sekyuModel.toJSON());
            // 施行互助会情報
            var dataGojokaiInfoJson = JSON.stringify(appcst.gojokaiInfoModel.toJSON());
            // 施行契約先情報
            var dataSekoKeiyakusakiInfoJson = JSON.stringify(appcst.sekoKeiyakusakiInfoModel.toJSON());
            // 施行互助会加入者情報
            // 会員番号があるデータに絞込み
            var dataGojokaiMemberDeleteCol = this.gojokaiMemberDeletefilter();
            var dataGojokaiMemberCol = this.gojokaiMemberfilter();
            var dataGojokaiMemberColJson = JSON.stringify(dataGojokaiMemberCol);
            var dataGojokaiMemberDeleteColJson = JSON.stringify(dataGojokaiMemberDeleteCol);

            // 施行引継書データ
            var dataHikitsugiJson = JSON.stringify(appcst.hikitsugiModel.toJSON());
            // 手配依頼書データ
            var dataTehaiIraiJson = JSON.stringify(appcst.tehaiIraiModel.toJSON());
            // 式場移動データ
            var dataShikijoIdoJson = JSON.stringify(appcst.shikijoIdoCol.toJSON());
            // 施行受付履歴
            var dataSekoMemoColJson = JSON.stringify(appcst.sekomemoCol.toJSON());
            // 訃報連絡
            var dataHuhoInfoJson = JSON.stringify(appcst.huhoInfoModel.toJSON());
            // 結果情報
            var dataResultInfoJson = JSON.stringify(appcst.resultInfoModel.toJSON());
            // 結果情報
            var dataSekoshaInfoJson = JSON.stringify(appcst.sekoshaModel.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/customerinfosave',
                data: {
                    controllerName: appcst.controllerName,
                    dataSekoKihonJson: dataSekoKihonJson,
                    oldDataApp: orgDataApp,
                    dataSekoKihonFreeJson: dataSekoKihonFreeJson,
                    dataNiteiColJson: dataNiteiColJson,
                    dataSekyuInfoJson: dataSekyuInfoJson,
                    dataGojokaiInfoJson: dataGojokaiInfoJson,
                    oldDataGojokaiInfo: orgDataGojokaiInfo,
                    dataSekoKeiyakusakiInfoJson: dataSekoKeiyakusakiInfoJson,
                    dataGojokaiMemberColJson: dataGojokaiMemberColJson,
                    dataGojokaiMemberDeleteColJson: dataGojokaiMemberDeleteColJson,
                    dataHikitsugiJson: dataHikitsugiJson,
                    dataTehaiIraiJson: dataTehaiIraiJson,
                    dataShikijoIdoJson: dataShikijoIdoJson,
                    dataSekoMemoColJson: dataSekoMemoColJson,
                    dataHuhoInfoJson: dataHuhoInfoJson,
                    dataResultInfoJson: dataResultInfoJson,
                    dataSekoshaInfoJson: dataSekoshaInfoJson,
                    changeFlg: changeFlg,
                    orgUpdateHistroyContents: JSON.stringify(orgUpdateHistroyContents),
                    sidemenukey: JSON.stringify(sidemenukey),
                    huho_flg: huho_flg,
                    orgModCnts: orgModCnts
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        // メニューの再設定
                        if ((Object.keys(appjh).length) > 0) {
                            appjh.setSide({data: mydata.dataSideMenu});
                        } else {
                            $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        }
                        _resetData(mydata.dataSekoKihon,
                                mydata.dataNiteiCol,
                                mydata.dataSekyuInfo,
                                mydata.dataGojokaiInfo,
                                mydata.dataSekoKeiyakusakiInfo,
                                mydata.dataGojokaiMemberCol,
                                mydata.dataSekoKihonFree,
                                mydata.dataSekoMemoCol,
                                mydata.dataUpdateHistoryCol,
                                mydata.dataUpdateHistoryContents);
                        appcst.resetReport(mydata);
                        appcst.resethuhoInfo(mydata);
                        $("#header .id_number span.number").text(mydata.dataSekoKihon.seko_no);  // 施行番号設定
                        appcst.backupReportData(); // 報告書データ退避
                        appcst.backuphuhoInfoData(); // 訃報連絡データ退避
                        appcst.resetresultInfo(mydata);  // 結果情報
                        appcst.setImgLink(); // 報告書画像リンク設定
                        appcst.setFileLink();
                        orgModCnts = mydata.modCnts
                        $.msiJqlib.showInfo(mydata.msg);

                        var matcheds = location.href.match(/(\/sn\/\d+)/);
                        if (!matcheds) {
                            var m = location.href.match(/\/(\D+)\/(\D+)\/(\D+)\/(\D+)/);
                            var herf;
                            if (m) {
                                herf = $.msiJqlib.baseUrl() + "/" + m[2] + "/" + m[3] + "/" + m[4].replace("/", '') + "/sn/" + mydata.dataSekoKihon.seko_no;
                            } else {
                                herf = location.href + "/sn/" + mydata.dataSekoKihon.seko_no;
                            }
                            location.href = herf;
                        }
                        t.setSideMenuStatus();
//                        t.doPrint();
                        if (!$.msiJqlib.isNullEx2(mode)) {
                            if (mode == 'shikijo') {
                                t.exeShowShikijoYoyaku();
                            } else if (mode == 'huhorenkei') {
                                $('#funeral_site_limit_date').removeAttr('readonly');
                                $('#funeral_site_limit_time').removeAttr('readonly');
                            }
                        }
                        // お仕事メール
                        $("#a_mail1").prop("href", mydata.mail_info1);
                        $("#a_mail2").prop("href", mydata.mail_info2);
                    } else if (mydata.status === 'WARN') {
                        // メニューの再設定
                        // メニューの再設定
                        if ((Object.keys(appjh).length) > 0) {
                            appjh.setSide({data: mydata.dataSideMenu});
                        } else {
                            $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        }
                        _resetData(mydata.dataSekoKihon,
                                mydata.dataNiteiCol,
                                mydata.dataSekyuInfo,
                                mydata.dataGojokaiInfo,
                                mydata.dataSekoKeiyakusakiInfo,
                                mydata.dataGojokaiMemberCol,
                                mydata.dataSekoKihonFree,
                                mydata.dataSekoMemoCol,
                                mydata.dataUpdateHistoryCol,
                                mydata.dataUpdateHistoryContents);
                        appcst.resetReport(mydata);
                        appcst.resethuhoInfo(mydata);
                        $("#header .id_number span.number").text(mydata.dataSekoKihon.seko_no);  // 施行番号設定
                        appcst.backupReportData(); // 報告書データ退避
                        appcst.backuphuhoInfoData(); // 訃報連絡データ退避
                        appcst.setImgLink(); // 報告書画像リンク設定
                        $.msiJqlib.showWarn(mydata.msg);

                        var matcheds = location.href.match(/(\/sn\/\d+)/);
                        if (!matcheds) {
                            var m = location.href.match(/\/(\D+)\/(\D+)\/(\D+)\/(\D+)/);
                            var herf;
                            if (m) {
                                herf = $.msiJqlib.baseUrl() + "/" + m[2] + "/" + m[3] + "/" + m[4].replace("/", '') + "/sn/" + mydata.dataSekoKihon.seko_no;
                            } else {
                                herf = location.href + "/sn/" + mydata.dataSekoKihon.seko_no;
                            }
                            location.href = herf;
                        }
                        t.setSideMenuStatus();
//                        t.doPrint();
                        if (!$.msiJqlib.isNullEx2(mode)) {
                            if (mode == 'shikijo') {
                                t.exeShowShikijoYoyaku();
                            } else if (mode == 'huhorenkei') {
                                $('#funeral_site_limit_date').removeAttr('readonly');
                                $('#funeral_site_limit_time').removeAttr('readonly');
                            }
                        }
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showErr(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                    //console.log('ajax res msg==>' + mydata.msg);
                }
            });
        },
        doPrint: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            var kaiinKbn = appcst.appModel.get("kaiin_kbn");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            // 会員区分が企業のみ
            if (kaiinKbn != KAIIN_KBN_COM) {
                return;
            }
            var pdfUrl = null;
            var $tabId = $.msiJqlib.getTabId();
            // 契約先状況報告書のみ現在は使用
            // 保存時のタブが会員情報タブかつその他加入確認タブの場合に印刷処理
            if ($tabId.is('#tab-kaiin-info')) {
                if (this.$("#member_group_set .lbl_member_group_2").hasClass('ui-state-active')) {
                    var pdfUrl = 'pdf0102';
                }
            }
            if ($.msiJqlib.isNullEx2(pdfUrl)) {
                return;
            }
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/' + pdfUrl,
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
            return;
        },
        doDelete: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('お客様情報を削除します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/customerinfodelete',
                data: {
                    seko_no: sekoNo,
                    controllerName: appcst.controllerName
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        var matcheds = location.href.match(/(.+)(\/sn\/\d+)/);
                        if (matcheds) {
                            location.href = matcheds[1];
                        } else {
                            window.location.reload();
                        }
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        doCancel: function () {
            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }
            window.location.reload();
        },
        showConsult: function () {
            var seko_no = appcst.appModel.get("seko_no");
            if (!seko_no)
                return;
            this._showNew('/juchu/mitsu/consult/sn/' + seko_no);
        },
        _showNew: function (path) {
            var url = $.msiJqlib.baseUrl() + path;
            var refreshFunc = function () {
            };
            msiLib2.openWinSub(refreshFunc, url);
        },
        doSekoCopy: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('施行情報をコピーして新規作成します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/sekocopy',
                data: {
                    seko_no: sekoNo,
                    controllerName: appcst.controllerName
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
                        var herf;
                        if (appcst.controllerName === "juchuhenko") {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/customerinfo/sn/' + mydata.seko_no;
                        } else {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/input/sn/' + mydata.seko_no;
                        }
                        location.href = herf;
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        // コースがあるデータに絞込み
        gojokaiMemberfilter: function () {
            var dataGojokaiMemberCol = appcst.gojokaiMemberCol.filter(function (item) {
                return (!$.msiJqlib.isNullEx2(item.get("course_snm_cd")) || !$.msiJqlib.isNullEx2(item.get("other_cose_nm"))) && item.get("delete_check") == '0';
            });
            return dataGojokaiMemberCol;
        },
        // コースがあるデータに絞込み
        gojokaiMemberDeletefilter: function () {
            var dataGojokaiMemberCol = appcst.gojokaiMemberCol.filter(function (item) {
                return (item.get("delete_check") == '1');
            });
            return dataGojokaiMemberCol;
        },
        // 郵便番号ヘルパー処理
        zipHelper: function (e) {
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            var val = $target.data("zip");
            var zip, addr1;
            var m = this.model;
            if (val === 'k1') { // 故人現住所
                zip = 'kg_yubin_no';
                addr1 = 'kg_addr1';
            } else if (val === 'm1') {// 喪主現住所
                zip = 'mg_yubin_no';
                addr1 = 'mg_addr1';
            } else if (val === 's1') {// 請求先現住所
                zip = 'sekyu_yubin_no';
                addr1 = 'sekyu_addr1';
                m = appcst.sekyuModel;
            } else if (val === 's2') {// 請求送付先現住所
                zip = 'soufu_yubin_no';
                addr1 = 'soufu_addr1';
                m = appcst.sekyuModel;
            } else if (val === 'r1') {// 領収書送付先現住所
                zip = 'ryosyu_soufu_yubin_no';
                addr1 = 'ryosyu_soufu_addr1';
                m = appcst.sekyuModel;
            } else if (val === 'huho_tuya') {// 訃報連絡通夜
                zip = 'huho_tuya_zip';
                addr1 = 'huho_tuya_addr';
                m = appcst.huhoInfoModel;
            } else if (val === 'huho_sougi') {// 訃報連絡葬儀
                zip = 'huho_sougi_zip';
                addr1 = 'huho_sougi_addr';
                m = appcst.huhoInfoModel;
            } else if (val === 'houmon') {    // 訪問先
                zip = 'homonsaki_zip_no';
                addr1 = 'homonsaki_addr1';
            } else if (val === 't1') {      // 菩提寺
                zip = 'temple_yubin_no';
                addr1 = 'temple_addr1';
            } else if (val === 'dm1') {      // DM送付先
                zip = 'dm_yubin_no';
                addr1 = 'dm_addr1';
            }
            this.$el.msiPickHelper({
                action: 'zipno',
                onSelect: function (data) {
                    m.set(zip, data.code);
                    m.set(addr1, data.name);
                },
                onClear: function () {
                    m.set(zip, null);
                    m.set(addr1, null);
                }
            });
        },

        // 受付担当者ヘルパー処理 
        uketukeHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'uketuke_tanto_cd': data.code, 'uketuke_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'uketuke_tanto_cd': null, 'uketuke_tanto_nm': null});
                }
            });
        },
        // 施行担当者ヘルパー処理
        sekoHelper: function () {
            var m = this.model;
            // ステータスが施行金額確定以上は変更不可
            if (m.get('status_kbn') >= STATUS_SEKO_KAKUTEI) {
                return;
            }
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'seko_tanto_cd': data.code, 'seko_tanto_nm': data.name, 'shikijo_bumon_cd': data.common_bumon_cd});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text(data.name);
                },
                onClear: function () {
                    m.set({'seko_tanto_cd': null, 'seko_tanto_nm': null, 'shikijo_bumon_cd': null});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text('');
                }
            });
        },
        // 事前相談施行番号ヘルパー処理
        consultInfoHelper: function () {
            // 搬送施行番号がある場合はダイアログを表示させない
            if (!$.msiJqlib.isNullEx2(appcst.appModel.get('hanso_seko_no'))) {
                return;
            }
            var dataIn = {init_search: 0, mode: 'consult_seko', s_moushi_ctxt: '7988', s_moushi_kbn_ctxt: '5', s_apply: '5'}; // 初回表示時検索する/しない=1/0
            var t = this;
            $.msiJqlib.celemonyDialogOnSelect = function (data) {
                appcst.appModel.set("consult_seko_no", data.seko_no);
                // 施行金額確定済の場合は引継をしない
                if (appcst.appModel.get('status_kbn') < STATUS_SEKO_KAKUTEI) {
                    t.copyConsultInfo();
                }
                $("#consult_seko_clear").show();
            };
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/mref/sekodialog',
                type: 'GET',
                data: dataIn,
                dataType: 'html',
                success: function (html) {
                    // console.log( html );
                    $('#celemony_dialog').remove();
                    $(html).appendTo($('#wrapper')).fadeIn(400);
                }
                // error処理は共通設定を使う
            });
        },
        shutsudoHelper: function (e) {
            var m = this.model;
            var $target = $(e.currentTarget);
            var id = $target.attr('data-tanto');
            var shutsudo_cd = 'shutsudo_cd'+id;
            var shutsudo_nm = 'shutsudo_nm'+id;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set(shutsudo_cd, data.code);
                    m.set(shutsudo_nm, data.name);
                },
                onClear: function () {
                    m.set(shutsudo_cd, null);
                    m.set(shutsudo_nm, null);
                }
            });
        },
        copyConsultInfo: function () {
            var t = this;
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            var dataKihonFreeJson = JSON.stringify(appcst.kfModel.toJSON());
            // 施行互助会加入者情報
            // 会員番号があるデータに絞込み
            var dataGojokaiMemberCol = this.gojokaiMemberfilter();
            var dataGojokaiMemberColJson = JSON.stringify(dataGojokaiMemberCol);
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/consultinfocopy',
                data: {
                    dataSekoKihonJson: dataSekoKihonJson,
                    dataKihonFreeJson: dataKihonFreeJson,
                    dataGojokaiMemberColJson: dataGojokaiMemberColJson,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // 既に施行番号がある場合は互助会のみ
                        if (!$.msiJqlib.isNullEx2(appcst.appModel.get('seko_no'))) {
                            appcst.gojokaiMemberCol.reset(mydata.dataGojokaiMemberCol);
                        } else {
                            appcst.appModel.set(mydata.sekoData);
                            appcst.appView.setImgLink("#input-tab", mydata.sekoData.k_file_nm, null);
                            appcst.kfModel.set(mydata.kihonFreeData);
                            appcst.gojokaiInfoView.model.set(mydata.dataGojokaiInfo);
                            appcst.sekoKeiyakusakiInfoView.model.set(mydata.dataSekoKeiyakusakiInfo);
                            appcst.gojokaiMemberCol.reset(mydata.dataGojokaiMemberCol);
                            appcst.jizenDenpyo = mydata.jizenDenpyo;
                            appcst.appView.calcNereiK();
                            if (mydata.sekoData.k_sex_kbn == 2) {
                                $('#female').attr("checked", "checked");
                                $('#female').prev().addClass("ui-state-active");
                                $('#male').removeAttr("checked", "checked");
                                $('#male').prev().removeClass("ui-state-active");
                                $('#female').addClass("m_sex_checked");
                                $('#male').removeClass("k_sex_checked");
                            } else if (mydata.sekoData.k_sex_kbn == 1) {
                                $('#male').attr("checked", "checked");
                                $('#male').prev().addClass("ui-state-active");
                                $('#female').removeAttr("checked", "checked");
                                $('#female').prev().removeClass("ui-state-active");
                                $('#female').removeClass("m_sex_checked");
                                $('#male').addClass("k_sex_checked");
                            } else {
                                $('#male').removeAttr("checked", "checked");
                                $('#male').prev().removeClass("ui-state-active");
                                $('#female').removeAttr("checked", "checked");
                                $('#female').prev().removeClass("ui-state-active");
                                $('#female').removeClass("m_sex_checked");
                                $('#male').addClass("k_sex_checked");
                            }
                        }
                        $.msiJqlib.showInfo(mydata.msg);
                        setTimeout(function () {
                        }, 10);
                    }
                }
            });
        },
        // 出動依頼情報 pickup
        hansoInfoHelper: function (e) {
            // 既に施行が作成されている場合はダイアログを表示させない
            if (!$.msiJqlib.isNullEx2(appcst.appModel.get('seko_no'))) {
                return;
            }
            if (msiLib2.isChildWindow()) {
                return;
                msiLib2.showWarn('子画面からはNoの変更はできません');
            }
            var t = this;
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: '/juchu/dlgirai2',
                _myId: '#msi-dialog2',
                width: '94%',
                height: '94%',
                onSelect: function (data) {
                    // console.log( 'pickupHansoIrai', data );
                    appcst.appModel.set("hanso_seko_no", data.uketsuke_no);
                    appcst.appModel.set("consult_seko_no", data.sodan_uke_no);
                    t.copyHansoInfo();
                    $("#hanso_seko_clear").show();
                },
                hookSetData: function () {
                    return {
                        no_footer: true,
                        init_search: 0,
                        limit: 10,
                        s_seko_kaisya: seko_kaisya_cd,
//                        s_for_juchu_copy: 1,
                        s_not_iraisya_kbns: "'1'",
                        // s_bumon_kbn: '1,2',
                    }
                },
            });
        },
        copyHansoInfo: function () {
            var t = this;
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            var dataKihonFreeJson = JSON.stringify(appcst.kfModel.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/hansoinfocopy',
                data: {
                    dataSekoKihonJson: dataSekoKihonJson,
                    dataKihonFreeJson: dataKihonFreeJson,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        appcst.appModel.set(mydata.sekoData);
                        appcst.kfModel.set(mydata.kihonFreeData);
                        appcst.appView.calcNereiK();
                        $.msiJqlib.showInfo(mydata.msg);
                        setTimeout(function () {
                        }, 10);
                    }
                }
            });
        },
        nmjyushoHelper2: function (e) {
            var m = this.model;
            var syuha_cd = this.model.get("syuha_cd");
            this.$el.msiPickHelper({
                action: 'nmjyusho',
                kind2: 1,
                mydata: {s_syuha_cd: syuha_cd,is_easyreg_jiin:0},
                onSelect: function (data) {
                    m.set('temple_cd2', data.code);
                    m.set('temple_nm2', data.name);
                    m.set('temple2_yubin_no', data.zip_no);
                    m.set('temple2_addr1', data.addr1_nm);
                    m.set('temple2_addr2', data.addr2_nm);
                    m.set('temple2_tel', data.tel);
                },
                onClear: function () {
                    m.set('temple_cd2', null);
                    m.set('temple_nm2', null);
                    m.set('temple2_yubin_no', null);
                    m.set('temple2_addr1', null);
                    m.set('temple2_addr2', null);
                    m.set('temple2_tel', null);
                }
            });
        },
        // 名所住所ヘルパー処理
        nmjyushoHelper: function (e) {
            var t = this;
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var kind = $target.data("kind2");
            var code = $target.data("code");
            var name = $target.data("name");
            var kname = $target.data("kname");
            var tel = $target.data("tel");
            var fax = $target.data("fax");
            var m = this.model;
            var syuha_cd = null;
            if (kind === 1) {
                syuha_cd = this.model.get("syuha_cd");
            }
            this.$el.msiPickHelper({
                action: 'nmjyusho',
                kind2: kind,
                mydata: {s_syuha_cd: syuha_cd,is_easyreg_jiin:0},
                onSelect: function (data) {
                    m.set(code, data.code);
                    m.set(name, data.name);
                    if (!$.msiJqlib.isNullEx2(kname)) {
                        m.set(kname, data.jyusho_lknm);
                        m.set(fax, data.fax);
                        m.set('temple_yubin_no', data.zip_no);
                        m.set('temple_addr1'   , data.addr1_nm);
                        m.set('temple_addr2'   , data.addr2_nm);
                        m.set('temple_tel2'    , data.tel);
                        if (kind === 1) {
                            if (!$.msiJqlib.isNullEx2(data.syuha_cd)) {
                                m.set('syushi_cd', data.syushi_cd);
                                m.set('syushi_kbn', data.syushi_cd);
                                m.set('syuha_cd', data.syuha_cd);
                                m.set('syuha_kbn', data.syuha_cd);
                                m.set('syuha_nm', data.syuha_nm);
//                                m.set('syuha_knm', data.syuha_kana);
                                t.setShuhaOther();
                            }
                        }
                    }
                },
                onClear: function () {
                    m.set(code, null);
                    m.set(name, null);
                    m.set(kname, null);
                    m.set(tel, null);
                    m.set(fax, null);
                }
            });
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            // disabledの場合はリターン（報告書タブの式場移動用処理）
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            $target.datepicker("show");
        },
        // 故人住所を喪主住所にコピー
        fromKtoMCopy: function () {
            this.model.set('mg_yubin_no', this.model.get('kg_yubin_no'));
            this.model.set('mg_addr1', this.model.get('kg_addr1'));
            this.model.set('mg_addr2', this.model.get('kg_addr2'));
            this.model.set('mg_tel', this.model.get('kg_tel'));
        },
        // 喪主情報を請求情報にコピー
        fromMtoSCopy: function () {
            this.model.set('s_cif_no', this.model.get('m_cif_no'));
            this.model.set('s_cif_status', this.model.get('m_cif_status'));
            appcst.sekyuModel.set('sekyu_last_nm', this.model.get('m_last_nm'));
            appcst.sekyuModel.set('sekyu_first_nm', this.model.get('m_first_nm'));
            appcst.sekyuModel.set('sekyu_nm', this.model.get('m_nm'));
            appcst.sekyuModel.set('sekyu_last_knm', this.model.get('m_last_knm'));
            appcst.sekyuModel.set('sekyu_first_knm', this.model.get('m_first_knm'));
            appcst.sekyuModel.set('sekyu_knm', this.model.get('m_knm'));
            appcst.sekyuModel.set('sekyu_yubin_no', this.model.get('mg_yubin_no'));
            appcst.sekyuModel.set('sekyu_addr1', this.model.get('mg_addr1'));
            appcst.sekyuModel.set('sekyu_addr2', this.model.get('mg_addr2'));
            appcst.sekyuModel.set('sekyu_tel', this.model.get('mg_tel'));
            appcst.sekyuModel.set('mobile_tel', this.model.get('mg_m_tel'));
            appcst.sekyuModel.set('sekyu_file_nm', this.model.get('m_file_nm'));
            appcst.sekyuModel.set('sekyu_file', this.model.get('m_file'));
            appcst.sekyuModel.set('sekyu_moshu_kankei_kbn', '1');
        },
        // 喪主情報をDM送付先情報にコピー
        fromMtoDMCopy: function () {
            this.model.set('dm_last_nm', this.model.get('m_last_nm'));
            this.model.set('dm_first_nm', this.model.get('m_first_nm'));
            this.model.set('dm_last_knm', this.model.get('m_last_knm'));
            this.model.set('dm_first_knm', this.model.get('m_first_knm'));
            this.model.set('dm_yubin_no', this.model.get('mg_yubin_no'));
            this.model.set('dm_addr1', this.model.get('mg_addr1'));
            this.model.set('dm_addr2', this.model.get('mg_addr2'));
            this.model.set('dm_tel', this.model.get('mg_tel'));
            this.model.set('dm_m_tel', this.model.get('mg_m_tel'));
        },
        // 請求先情報を請求送付先情報にコピー
        fromStoSCopy: function () {
            appcst.sekyuModel.set('soufu_last_nm', appcst.sekyuModel.get('sekyu_last_nm'));
            appcst.sekyuModel.set('soufu_first_nm', appcst.sekyuModel.get('sekyu_first_nm'));
            appcst.sekyuModel.set('sekyu_soufu_nm', appcst.sekyuModel.get('sekyu_nm'));
            appcst.sekyuModel.set('soufu_last_knm', appcst.sekyuModel.get('sekyu_last_knm'));
            appcst.sekyuModel.set('soufu_first_knm', appcst.sekyuModel.get('sekyu_first_knm'));
            appcst.sekyuModel.set('sekyu_soufu_knm', appcst.sekyuModel.get('sekyu_knm'));
            appcst.sekyuModel.set('soufu_yubin_no', appcst.sekyuModel.get('sekyu_yubin_no'));
            appcst.sekyuModel.set('soufu_addr1', appcst.sekyuModel.get('sekyu_addr1'));
            appcst.sekyuModel.set('soufu_addr2', appcst.sekyuModel.get('sekyu_addr2'));
            appcst.sekyuModel.set('soufu_tel', appcst.sekyuModel.get('sekyu_tel'));
            appcst.sekyuModel.set('soufu_file_nm', appcst.sekyuModel.get('sekyu_file_nm'));
            appcst.sekyuModel.set('soufu_file', appcst.sekyuModel.get('sekyu_file'));
        },
        // 請求送付先情報を領収証送付先情報にコピー
        fromStoRCopy: function () {
            appcst.sekyuModel.set('ryosyu_soufu_last_nm', appcst.sekyuModel.get('soufu_last_nm'));
            appcst.sekyuModel.set('ryosyu_soufu_first_nm', appcst.sekyuModel.get('soufu_first_nm'));
            appcst.sekyuModel.set('ryosyu_soufu_nm', appcst.sekyuModel.get('sekyu_soufu_nm'));
            if (!$.msiJqlib.isNullEx2(appcst.sekyuModel.get('sekyu_last_nm')) && !$.msiJqlib.isNullEx2(appcst.sekyuModel.get('sekyu_first_nm'))) {
                appcst.sekyuModel.set('ryosyu_meigi', appcst.sekyuModel.get('sekyu_last_nm') + '　' + appcst.sekyuModel.get('sekyu_first_nm'));
            } else if (!$.msiJqlib.isNullEx2(appcst.sekyuModel.get('sekyu_last_nm'))) {
                appcst.sekyuModel.set('ryosyu_meigi', appcst.sekyuModel.get('sekyu_last_nm'));
            } else if (!$.msiJqlib.isNullEx2(appcst.sekyuModel.get('sekyu_first_nm'))) {
                appcst.sekyuModel.set('ryosyu_meigi', appcst.sekyuModel.get('sekyu_first_nm'));
            }
            //appcst.sekyuModel.set('ryosyu_soufu_last_knm', appcst.sekyuModel.get('soufu_last_knm'));
            //appcst.sekyuModel.set('ryosyu_soufu_first_knm', appcst.sekyuModel.get('soufu_first_knm'));
            appcst.sekyuModel.set('ryosyu_soufu_knm', appcst.sekyuModel.get('sekyu_soufu_knm'));
            appcst.sekyuModel.set('ryosyu_soufu_yubin_no', appcst.sekyuModel.get('soufu_yubin_no'));
            appcst.sekyuModel.set('ryosyu_soufu_addr1', appcst.sekyuModel.get('soufu_addr1'));
            appcst.sekyuModel.set('ryosyu_soufu_addr2', appcst.sekyuModel.get('soufu_addr2'));
            appcst.sekyuModel.set('ryosyu_soufu_tel', appcst.sekyuModel.get('soufu_tel'));
            appcst.sekyuModel.set('ryosyu_soufu_file_nm', appcst.sekyuModel.get('soufu_file_nm'));
            appcst.sekyuModel.set('ryosyu_soufu_file', appcst.sekyuModel.get('soufu_file'));
        },
        showAntiYoyaku: function () {
            // 施行番号がなければエラー
            var sekoNo = this.model.get('seko_no');
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                $.msiJqlib.showErr('安置先予約の前に施行登録を行ってください。');
                return;
            }
            // 呼び出し元設定例 parms *は必須
            // p_yoyaku_bunrui:0:搬送 1:葬儀 2:法事
            //* p_seko_no: 施行番号（搬送時の受付番号）
            //* p_seko_no_sub: 搬送時の受付番号枝番
            // p_kaijyo_type: 1:式場 2:控室 3:会食室(お清め室) 4:霊安室 5:対面室 6:保冷庫 9:エンバー室 99:その他
            // p_tgt_day: YYYY-MM-DD
            var t = this;
            var sekoNo = appcst.appModel.get("seko_no");
            var url = $.msiJqlib.baseUrl() + '/cale/yoyaku/cale/p_yoyaku_bunrui/4/p_seko_no/' + sekoNo;
            var refreshFunc = function () {
                // 葬儀・法事の場合
                // 予約情報を取得し再設定する
                // 予約保存時に施行日程（日程開始日、予約場所等）を更新(updateのみ)している）
                console.log("openerCalled");
                t.getCurData();
            };
            msiLib2.openWinSub(refreshFunc, url);
        },
        showShikijoYoyaku: function () {
            var mode = 'shikijo';
            // 施行番号がなければエラー
            var sekoNo = this.model.get('seko_no');
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                $.msiJqlib.showErr('式場予約の前に施行登録を行ってください。');
                return;
            }
            this.doSave(mode);
        },
        // 式場予約画面呼び出し処理
        exeShowShikijoYoyaku: function () {
            // 呼び出し元設定例 parms *は必須
            // p_yoyaku_bunrui:0:搬送 1:葬儀 2:法事
            //* p_seko_no: 施行番号（搬送時の受付番号）
            //* p_seko_no_sub: 搬送時の受付番号枝番
            // p_tgt_day: YYYY-MM-DD
            var t = this;
            var sekoNo = appcst.appModel.get("seko_no");
            var url = $.msiJqlib.baseUrl() + '/cale/yoyaku/cale/p_yoyaku_bunrui/1/p_seko_no/' + sekoNo;
            var refreshFunc = function () {
                // 葬儀・法事の場合
                // 予約情報を取得し再設定する
                // 予約保存時に施行日程（日程開始日、予約場所等）を更新(updateのみ)している）
                console.log("openerCalled");
                t.getCurData();
            };
            msiLib2.openWinSub(refreshFunc, url);
        },
        doHuhoRenkei: function () {
            var mode = 'huhorenkei';
            this.doSave(mode);
        },
        getCurData: function () {
            var am = appcst.appModel;
            var t = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/getcurdata',
                data: {
                    seko_no: appcst.appModel.get("seko_no")
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        am.set('est_shikijo_cd', mydata.dataSekoKihon.est_shikijo_cd);
                        am.set('est_oya_bumon_cd', mydata.dataSekoKihon.est_oya_bumon_cd);
                        am.set('seko_shikijo_cd', mydata.dataSekoKihon.seko_shikijo_cd);
                        am.set('sougi_ymd', mydata.dataSekoKihon.sougi_ymd);
                        appcst.niteiCol.reset(mydata.dataNiteiCol);
                        orgModCnts = mydata.modCnts;
                        t.setEstData();
                    }
                }
            });
        },
        doIrai: function(){
            var sekoNo = appcst.appModel.get("seko_no");
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0182',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
            return;
        },
        doNitei: function(){
            var sekoNo = appcst.appModel.get("seko_no");
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0183',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
            return;
        },
        doSiji: function(){
            var sekoNo = appcst.appModel.get("seko_no");
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0185',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
            return;
        },
        doAzukari: function(){
            var sekoNo = appcst.appModel.get("seko_no");
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0192',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
            return;
        },
    };
    // 全体ビュー
    var AppView = Backbone.View.extend($.extend(true, AppViewDef, $.customerFileup)); // AppView

    /**
     * @description 日程タブ処理
     */
    // 日程タブ明細モデル
    var NiteiModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                nitei_kbn: null, // 日程区分
                ts_based_nm: null, // 日程基本名
                ts_based_nm2: null, // 日程基本名2 
                nitei_ymd: null, // 日程タイムスタンプ
                nitei_date: null, // 日程日付のみ
                nitei_time: null, // 日程時刻のみ
                nitei_ed_ymd: null, // 日程終了
                nitei_ed_time: null, // 日程終了時刻
                spot_cd: null, // 場所区分コード
                basho_kbn: null, // 場所区分
                basho_cd: null, // 場所コード
                basho_nm: null, // 場所名
                sikijo_check: null, // 当社式場未使用チェック
                sikijo_yoyaku_no: null, // 式場予約番号
                nitei_zip: null,
                nitei_addr1: null,
                nitei_addr2: null,
                nitei_tel: null,
                kasouba_zuikou_kbn: null,   // 火葬場随行 free_kbn3 SEKO_KIHON_ALL_FREE
                nitei_nm: null, // 日程名 v_free4
            };
        },
        validation: {
            nitei_ymd: function (val, attr, computed) {
                var keishiki_cd = appcst.appModel.get('keishiki_cd');
                if (keishiki_cd !== KEISHI_KASO && computed.nitei_kbn === 7 && !$.msiJqlib.isNullEx2(appcst.appModel.get("jichu_kakute_ymd")) && $.msiJqlib.isNullEx2(computed.nitei_date)) {
                    return '見積確定後は施行日は必須です';
                } else if (keishiki_cd == KEISHI_KASO && computed.nitei_kbn == NITEI_KASO && !$.msiJqlib.isNullEx2(appcst.appModel.get("jichu_kakute_ymd")) && $.msiJqlib.isNullEx2(computed.nitei_date)) {
                    return '見積確定後は施行日は必須です';
                }
                if ($.msiJqlib.isNullEx2(computed.nitei_date) && !$.msiJqlib.isNullEx2(computed.nitei_time)) {
                    return '日付と時刻の整合性がありません';
                }
            },
            nitei_date: function (value) {
                return _chkYmd(value);
            },
            nitei_time: {
                required: false,
                pattern: 'time'
            },
            nitei_ed_ymd: function (val, attr, computed) {
                if ($.msiJqlib.isNullEx2(computed.nitei_date) && !$.msiJqlib.isNullEx2(computed.nitei_ed_time)) {
                    return '日付と時刻の整合性がありません';
                }
            },
            nitei_ed_time: {
                required: false,
                pattern: 'time'
            },
            basho_nm: {
                required: function () {
                    var require = false;
                    if (this.get('nitei_kbn') === 7) {
                        if (this.get('basho_kbn') === '2') {
                            require = true;
                        }
                    }
                    return require;
                },
                maxLength: 40
            },
            kasouba_zuikou_kbn: {
                required: false,
                maxLength: 2
            },
            nitei_nm: {
                required: true,
                maxLength: 60
            }
        },
        labels: {
            basho_nm: '場所名',
            kasouba_zuikou_kbn: '火葬場随行区分',
            nitei_nm: '日程名',
        }
    }); // NiteiModel

    // 日程コレクション
    var NiteiCollection = Backbone.Collection.extend({
        model: NiteiModel
    });

    // 日程ビュー
    var NiteiView = Backbone.View.extend({
        tagName: 'fieldset',
        //1:亡日 3:入棺 4:通夜 5:出棺 6:火葬 8:法要 9:会食 10:納骨 11:葬儀 12:創想の儀 13:散会 14:控室 15:湯灌 16:通夜後会食 17:枕経
        tmpl1: _.template($('#tmpl-nitei-1').html()),
        tmpl3: _.template($('#tmpl-nitei-1').html()),
        tmpl4: _.template($('#tmpl-nitei-4').html()),
        tmpl5: _.template($('#tmpl-nitei-5').html()),
        tmpl6: _.template($('#tmpl-nitei-6').html()),
        tmpl8: _.template($('#tmpl-nitei-8').html()),
        tmpl9: _.template($('#tmpl-nitei-9').html()),
        tmpl10: _.template($('#tmpl-nitei-10').html()),
        tmpl11: _.template($('#tmpl-nitei-11').html()),
        tmpl12: _.template($('#tmpl-nitei-9').html()),
        tmpl13: _.template($('#tmpl-nitei-10').html()),
        tmpl14: _.template($('#tmpl-nitei-3').html()),
        tmpl15: _.template($('#tmpl-nitei-15').html()),
        tmpl16: _.template($('#tmpl-nitei-3').html()),
        tmpl17: _.template($('#tmpl-nitei-15').html()),
        events: {
            "click .basho_nm, .label.dlg_place": "nmjyushoHelper",
            "change .nitei_spot_cd": function () {
                this.model.set({'basho_cd': null, 'basho_nm': null});
            },
            "change .sikijo_check": function () {
                this.model.set({'spot_cd': null, 'basho_kbn': null, 'basho_cd': null, 'basho_nm': null, 'nitei_date':null, 'nitei_time':null, 'nitei_ed_time':null});
            },
            "select2-opening .nitei_spot_cd": function () {
                var niteiSpotKbns = {};
                // 日程区分
                var niteiKbn = this.model.get("nitei_kbn");
                if (niteiKbn === 1) {// 亡日
                    niteiSpotKbns = data.dataKbns.hs_gyomu_kbn;
                } else if (niteiKbn === 3) {    // 入棺
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 4) { // 通夜
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 5) {    // 出棺
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 8) {    // 法要
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 9) {    // 会席・会食
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 11) {   // 葬儀
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 12) {    // 創想の儀
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 13) {   // 散会
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 14) {    // 控室
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 15) {    // 湯灌
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 16) {    // 通夜後会食
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 17) {    // 枕経
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                }
                var orgKbns = $.msiJqlib.objToArray3(niteiSpotKbns);
                var fileredKbns = [];
                var sikijo_check = this.model.get('sikijo_check');
                _.each(orgKbns, function (item) {
                    if (sikijo_check == '1') {
                        if (item.kbn_value_cd_num !== BASHO_KIND_HALL) {
                            fileredKbns.push(item);
                        }
                    } else {
                        fileredKbns.push(item);
                    }
                });
                appcst.niteiSpotKbns = fileredKbns;
            },
        },
        bindings: {
            '.nitei_kbn_nm': 'nitei_kbn_nm',
            '.nitei_date': {
                observe: 'nitei_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('nitei_time'), 'nitei_ymd', this.model);
                    _setNiteiymd($el.val(), this.model.get('nitei_ed_time'), 'nitei_ed_ymd', this.model);
                    // 死亡日を設定
                    if (this.model.get('nitei_kbn') === 1) {
                    }
                    // 施行基本の葬儀日を設定
                    if (this.model.get('nitei_kbn') === 11) {
                        appcst.appModel.set('sougi_ymd', $el.val());
                        // 葬儀情報(ヘッダー)の葬儀日バインディング処理
                        $("#hd_mg_sogibi").text($el.val());
                    }
                    if (this.model.get('nitei_kbn') === 7 || this.model.get('nitei_kbn') === 11 || this.model.get('nitei_kbn') === 6) {
                        _setSogiYmd();
//                        console.log(appcst.appModel.get('sougi_ymd'));
                    }
                    return $el.val();
                }

            },
            '.nitei_time': {
                observe: 'nitei_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('nitei_date'), $el.val(), 'nitei_ymd', this.model);
                    return $el.val();
                }
            },
            '.nitei_ed_time': {
                observe: 'nitei_ed_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('nitei_date'), $el.val(), 'nitei_ed_ymd', this.model);
                    return $el.val();
                }
            },
            '.nitei_spot_cd': {
                observe: 'spot_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'basho_kbn');
                    return $el.val();
                }
            },
            '.basho_nm': {
                observe: 'basho_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("basho_cd", null);
                    }
                    return val;
                }
            },
            ".sikijo_check": $.msiJqlib.getCheckBinding('sikijo_check'),
            'sikijo_yoyaku_no': 'sikijo_yoyaku_no',
            '.kasouba_zuikou_kbn': $.msiJqlib.getSelect2Binding('kasouba_zuikou_kbn'),
            '.nitei_nm': 'nitei_nm',
        },
        initialize: function () {
            this.listenTo(this.model, 'change:spot_cd', this.setPickupNmStatus);
            this.listenTo(this.model, 'change:basho_nm change:spot_cd', this.setBashoNm);
            this.listenTo(this.model, 'change:sikijo_check', this.setSpotStatus);
            this.listenTo(appcst.appModel, 'change:tuya_check', this.setTuyaStatus);
            this.listenTo(appcst.appModel, 'change:kaso_check', this.setKasoStatus);
            this.listenTo(this.model, 'change:nitei_date', function () {
                if (this.model.get('nitei_kbn') === 1) { // 1:亡日
                    var nakunaribi = this.model.get("nitei_date");
                    if ($.msiJqlib.chkDate(nakunaribi)) { // 日付チェック
                        // 故人年齢計算
                        appcst.appView.calcNereiK();
                    }
                }
            });
            Backbone.Validation.bind(this);
        },
        render: function () {
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            // 場所区分データ
            var niteiSpotKbns = {};
            // 日程区分
            var niteiKbn = this.model.get("nitei_kbn");
            var sikijo_yoyaku_no = this.model.get("sikijo_yoyaku_no");
            var buttonDisabled = null;
            if (!($.msiJqlib.isNullEx2(sikijo_yoyaku_no)) && niteiKbn != NITEI_SYUKAN) {
                buttonDisabled = 'disabled';
            }
            if (niteiKbn === 1) {// 亡日
                this.$el.html(this.tmpl1(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.hs_gyomu_kbn;
            } else if (niteiKbn === 3) {    // 入棺
                this.$el.html(this.tmpl3(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 4) { // 通夜
                this.$el.html(this.tmpl4(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 5) {    // 出棺
                this.$el.html(this.tmpl5(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 6) {    // 火葬
                this.$el.html(this.tmpl6(this.model.toJSON()));
            } else if (niteiKbn === 8) {    // 法要
                this.$el.html(this.tmpl8(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 9) {    // 会席・会食
                this.$el.html(this.tmpl9(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 10) {   // 納骨
                this.$el.html(this.tmpl10(this.model.toJSON()));
            } else if (niteiKbn === 11) {   // 葬儀
                this.$el.html(this.tmpl11(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 12) {    // 創想の儀
                this.$el.html(this.tmpl12(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 13) {   // 散会
                this.$el.html(this.tmpl13(this.model.toJSON()));
            } else if (niteiKbn === 14) {   // 控室
                this.$el.html(this.tmpl14(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 15) {   // 湯灌
                this.$el.html(this.tmpl15(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 16) {   // 通夜後会食
                this.$el.html(this.tmpl16(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 17) {   // 枕経
                this.$el.html(this.tmpl17(this.model.toJSON()));
                this.$('.radio_set').buttonset();
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            }

            this.$(".nitei_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            if (niteiKbn === 1) {
                this.$(".nitei_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            } else {
                this.$(".nitei_time, .nitei_ed_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault2, {minuteGrid: 5, stepMinute: 5}));
            }
            this.stickit();

            // 場所区分
            appcst.niteiSpotKbns = $.msiJqlib.objToArray3(niteiSpotKbns);
            $.msiJqlib.setSelect2Com1(this.$(".nitei_spot_cd"), ($.extend({data: function () {
                    return {results: appcst.niteiSpotKbns};
                }}, $.msiJqlib.setSelect2Default1)));
//            $.msiJqlib.setSelect2Com1(this.$(".nitei_spot_cd"), ($.extend({data: $.msiJqlib.objToArray3(niteiSpotKbns)}, $.msiJqlib.setSelect2Default1)));
            // 火葬場随行
            $.msiJqlib.setSelect2Com1(this.$(".kasouba_zuikou_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.umu_kbn)}, $.msiJqlib.setSelect2Default1)));
            this.setRadioButtonStatus();
            this.setPickupNmStatus();
            this.setTuyaStatus();
            this.setKasoStatus();
            this.setSpotStatus();
            return this;
        },
        setRadioButtonStatus: function () {
            var niteiKbn = this.model.get("nitei_kbn");
            var sikijo_yoyaku_no = this.model.get("sikijo_yoyaku_no");
            var buttonDisabled = 'enable';
            if (!($.msiJqlib.isNullEx2(sikijo_yoyaku_no)) && niteiKbn != NITEI_SYUKAN) {
                buttonDisabled = 'disable';
            }
            if (niteiKbn === 1) {// 亡日
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 3) {    // 入棺
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 4) { // 通夜
                this.$('.radio_set').buttonset(buttonDisabled);
                $('.radio_set_tuya').buttonset(buttonDisabled);
            } else if (niteiKbn === 5) {    // 出棺
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 8) {    // 法要
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 9) {    // 会席・会食
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 11) {   // 葬儀
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 12) {    // 創想の儀
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 14) {    // 控室
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 15) {    // 湯灌
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 16) {    // 通夜後会食
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 17) {    // 枕経
                this.$('.radio_set').buttonset(buttonDisabled);
            }
        },
        setSpotStatus: function () {
            // 施設予約番号があればスルーする
            var niteiKbn = this.model.get("nitei_kbn");
            if (!$.msiJqlib.isNullEx2(this.model.get("sikijo_yoyaku_no")) && niteiKbn != NITEI_SYUKAN) {
                this.$(".nitei_spot_cd").attr("disabled", "disabled");
                this.$(".nitei_date").attr("disabled", "disabled");
                this.$(".nitei_time").attr("disabled", "disabled");
                this.$(".nitei_ed_time").attr("disabled", "disabled");
                this.$(".dlg_date_common").removeClass("dlg_date");
                this.$(".dlg_time_common").removeClass("dlg_time");
                return;
            }
            if (this.model.get('sikijo_check') == '1') {
                if (this.model.get('nitei_kbn') == NITEI_TUYA && appcst.appModel.get('tuya_check') == '1') {
                    this.$(".nitei_spot_cd").attr("disabled", "disabled");
                    this.$(".nitei_date").attr("disabled", "disabled");
                    this.$(".nitei_time").attr("disabled", "disabled");
                    this.$(".nitei_ed_time").attr("disabled", "disabled");
                    this.$(".dlg_date_common").removeClass("dlg_date");
                    this.$(".dlg_time_common").removeClass("dlg_time");
                } else if (this.model.get('nitei_kbn') == NITEI_KASO && appcst.appModel.get('kaso_check') == '1') {
                    this.$(".nitei_spot_cd").attr("disabled", "disabled");
                    this.$(".nitei_date").attr("disabled", "disabled");
                    this.$(".nitei_time").attr("disabled", "disabled");
                    this.$(".dlg_date_common").removeClass("dlg_date");
                    this.$(".dlg_time_common").removeClass("dlg_time");
                } else {
                    this.$(".nitei_spot_cd").removeAttr("disabled");
                    this.$(".nitei_date").removeAttr("disabled");
                    this.$(".nitei_time").removeAttr("disabled");
                    this.$(".nitei_ed_time").removeAttr("disabled");
                    this.$(".dlg_date_common").addClass("dlg_date");
                    this.$(".dlg_time_common").addClass("dlg_time");
                }
            } else {
                if (!$.msiJqlib.isNullEx2(niteiKbn) &&
                        (niteiKbn === NITEI_YUKAN
                                || niteiKbn === NITEI_SHIBOU
                                || niteiKbn === NITEI_NOKAN
                                || niteiKbn === NITEI_SOSO
                                || niteiKbn === NITEI_SYUKAN
                                || niteiKbn === NITEI_MAKURA
                                || niteiKbn === NITEI_KASO
                                || niteiKbn === NITEI_SANKAI
                                )) {
                    if (niteiKbn == NITEI_KASO && appcst.appModel.get('kaso_check') == '1') {
                        this.$(".nitei_spot_cd").attr("disabled", "disabled");
                        this.$(".nitei_date").attr("disabled", "disabled");
                        this.$(".nitei_time").attr("disabled", "disabled");
                        this.$(".dlg_date_common").removeClass("dlg_date");
                        this.$(".dlg_time_common").removeClass("dlg_time");
                    } else {
                        this.$(".nitei_spot_cd").removeAttr("disabled");
                        this.$(".nitei_date").removeAttr("disabled");
                        this.$(".nitei_time").removeAttr("disabled");
                        this.$(".dlg_date_common").addClass("dlg_date");
                        this.$(".dlg_time_common").addClass("dlg_time");
                    }
                } else {
                    this.$(".nitei_spot_cd").attr("disabled", "disabled");
                    this.$(".nitei_date").attr("disabled", "disabled");
                    this.$(".nitei_time").attr("disabled", "disabled");
                    this.$(".nitei_ed_time").attr("disabled", "disabled");
                    this.$(".dlg_date_common").removeClass("dlg_date");
                    this.$(".dlg_time_common").removeClass("dlg_time");
                }
            }
        },
        setTuyaStatus: function () {
            // 施設予約番号があればスルーする
            if (!$.msiJqlib.isNullEx2(this.model.get("sikijo_yoyaku_no"))) {
                return;
            }
            if ((this.model.get('nitei_kbn') == NITEI_TUYA || this.model.get('nitei_kbn') == NITEI_TUYASYOKU) && appcst.appModel.get('tuya_check') == '1') {
                this.model.set({'nitei_date': null, 'nitei_time': null, 'nitei_ed_time': null, 'nitei_ymd': null, 'spot_cd': null
                    , 'basho_kbn': null, 'basho_cd': null, 'basho_nm': null, 'free1_code_cd': null, 'sikijo_check': null});
                this.$(".nitei_date").attr("disabled", "disabled");
                this.$(".nitei_time").attr("disabled", "disabled");
                this.$(".nitei_ed_time").attr("disabled", "disabled");
                this.$(".nitei_spot_cd").attr("disabled", "disabled");
                this.$(".basho_nm").attr("disabled", "disabled");
                this.$(".dlg_date_common").removeClass("dlg_date");
                this.$(".dlg_time_common").removeClass("dlg_time");
                this.$('.radio_set').buttonset().buttonset('disable');
                // 訃報連絡タブも制御する
                $("#huho_tuya_basho_nm").attr("disabled", "disabled");
                $("#tuya_nm").attr("disabled", "disabled");
                $("#huho_tuya_date").attr("disabled", "disabled");
                $("#huho_tuya_time").attr("disabled", "disabled");
                $("#huho_tuya_zip").attr("disabled", "disabled");
                $("#huho_tuya_addr").attr("disabled", "disabled");
                $("#huho_tuya_tel").attr("disabled", "disabled");
                $("#huho-tab .dlg_date_dummy").show();
                $("#huho-tab .dlg_date.date_tuya").hide();
                $("#huho-tab .dlg_time_dummy").show();
                $("#huho-tab .dlg_time.time_tuya").hide();
                appcst.huhoInfoModel.set({'huho_tuya_basho_nm': null, 'tuya_nm': null, 'huho_tuya_date': null, 'huho_tuya_ymd': null, 'huho_tuya_time': null
                    , 'huho_tuya_zip': null, 'huho_tuya_addr': null, 'huho_tuya_tel': null});
            } else if (this.model.get('nitei_kbn') == NITEI_TUYA || this.model.get('nitei_kbn') == NITEI_TUYASYOKU) {
                if (this.model.get('sikijo_check') == '1') {
                    this.$(".nitei_spot_cd").removeAttr("disabled");
                    this.$(".nitei_date").removeAttr("disabled");
                    this.$(".nitei_time").removeAttr("disabled");
                    this.$(".nitei_ed_time").removeAttr("disabled");
                    this.$(".dlg_date_common").addClass("dlg_date");
                    this.$(".dlg_time_common").addClass("dlg_time");
                } else {
                    this.$(".nitei_spot_cd").attr("disabled", "disabled");
                    this.$(".nitei_date").attr("disabled", "disabled");
                    this.$(".nitei_time").attr("disabled", "disabled");
                    this.$(".nitei_ed_time").attr("disabled", "disabled");
                    this.$(".dlg_date_common").removeClass("dlg_date");
                    this.$(".dlg_time_common").removeClass("dlg_time");
                }
                if (!$.msiJqlib.isNullEx2(this.model.get('basho_kbn'))) {
                    this.$(".basho_nm").removeAttr("disabled");
                } else {
                    this.$(".basho_nm").attr("disabled", "disabled");
                }
                this.$('.radio_set').buttonset().buttonset('enable');
                // 訃報連絡タブも制御する
                $("#huho_tuya_basho_nm").removeAttr("disabled");
                $("#tuya_nm").removeAttr("disabled");
                $("#huho_tuya_date").removeAttr("disabled");
                $("#huho_tuya_time").removeAttr("disabled");
                $("#huho_tuya_zip").removeAttr("disabled");
                $("#huho_tuya_addr").removeAttr("disabled");
                $("#huho_tuya_tel").removeAttr("disabled");
                $("#huho-tab .dlg_date_dummy").hide();
                $("#huho-tab .dlg_date.date_tuya").show();
                $("#huho-tab .dlg_time_dummy").hide();
                $("#huho-tab .dlg_time.time_tuya").show();
            }
        },
        setKasoStatus: function () {
            if (this.model.get('nitei_kbn') == NITEI_KASO && appcst.appModel.get('kaso_check') == '1') {
                this.model.set({'nitei_date': null, 'nitei_time': null, 'nitei_ed_time': null, 'nitei_ymd': null, 'spot_cd': null
                    , 'basho_kbn': null, 'basho_cd': null, 'basho_nm': null, 'free1_code_cd': null});
                this.$(".nitei_date").attr("disabled", "disabled");
                this.$(".nitei_time").attr("disabled", "disabled");
                this.$(".nitei_spot_cd").attr("disabled", "disabled");
                this.$(".basho_nm").attr("disabled", "disabled");
                this.$(".dlg_place_common").addClass("disabled");
                this.$(".dlg_date_common").removeClass("dlg_date");
                this.$(".dlg_time_common").removeClass("dlg_time");
                this.$(".dlg_place_common").removeClass("dlg_place");
            } else if (this.model.get('nitei_kbn') == NITEI_KASO) {
                this.$(".nitei_spot_cd").removeAttr("disabled");
                this.$(".nitei_date").removeAttr("disabled");
                this.$(".nitei_time").removeAttr("disabled");
                this.$(".basho_nm").removeAttr("disabled");
                this.$(".dlg_place").removeAttr("disabled");
                this.$(".dlg_date_common").addClass("dlg_date");
                this.$(".dlg_place_common").removeClass("disabled");
                this.$(".dlg_time_common").addClass("dlg_time");
                this.$(".dlg_place_common").addClass("dlg_place");
            }
        },
        // 名所住所ヘルパー処理
        nmjyushoHelper: function (e) {
            // 施設予約番号があればダイアログを表示させない
            var niteiKbn = this.model.get('nitei_kbn');
            var bashoKbn = this.model.get('basho_kbn');
            if (!$.msiJqlib.isNullEx2(this.model.get("sikijo_yoyaku_no")) && niteiKbn != NITEI_SYUKAN) {
                this.$(".nitei_spot_cd").attr("disabled", "disabled");
                return;
            }
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var kind = $target.data("kind2");
            if ($.msiJqlib.isNullEx2(kind)) {
                return;
            }
            var actionNm = 'nmjyusho';
            var action = $target.data("action");
            if (!$.msiJqlib.isNullEx2(action)) {
                actionNm = action;
            }
            var hanso_kbn = null;
            var s_hanso_kbn = $target.data("s_hanso_kbn");
            if (!$.msiJqlib.isNullEx2(s_hanso_kbn)) {
                hanso_kbn = s_hanso_kbn;
            }
            var kaijyo_kbn = null;
            if (bashoKbn == BASHO_KIND_HALL) {
                kaijyo_kbn = KAIJYO_KBN_JISYA;
            } else if (bashoKbn == BASHO_KIND_OTHER) {
                kaijyo_kbn = KAIJYO_KBN_TAEI;
            }
            var kaijyo_type = null;
            if (niteiKbn == NITEI_HIKAE) {
                kaijyo_type = KAIJYO_TYPE_HIKAE;
//            } else if (niteiKbn == NITEI_YUKAN) {
//                kaijyo_type = KAIJYO_TYPE_EMBALM;
            }
            var m = this.model;
            var t = this;
            this.$el.msiPickHelper({
                action: actionNm,
                kind2: kind,
                mydata: {s_hanso_kbn: hanso_kbn, s_kaijyo_kbn: kaijyo_kbn, s_type_kbn: kaijyo_type, is_easyreg_shisetsu: 0, is_easyreg_jiin: 0},
                onSelect: function (data) {
                    m.set("basho_cd", data.code);
                    m.set("basho_nm", data.name);
                    if (kind == BASHO_KIND_KASO) {
                        appcst.appModel.set('kasoba_kbn', kind);
                        appcst.appModel.set('kasoba_cd', data.code);
                        appcst.appModel.set('kasoba_nm', data.name);
                    } else if (niteiKbn == NITEI_TUYA || niteiKbn == NITEI_SOGI) {
                        m.set('nitei_zip', data.zip_no);
                        m.set('nitei_addr1', data.addr1_nm);
                        m.set('nitei_addr2', data.addr2_nm);
                        m.set('nitei_tel', data.tel);
                    }
                },
                onClear: function () {
                    m.set("basho_cd", null);
                    m.set("basho_nm", null);
                    appcst.appModel.set('kasoba_kbn', null);
                    appcst.appModel.set('kasoba_cd', null);
                    appcst.appModel.set('kasoba_nm', null);
                }
            });
        },
        // 場所区分切り替え処理
        setPickupNmStatus: function () {
            // 日程区分 1:死亡日時 3:納棺 4:通夜 5:出棺 6:火葬 8:法要 9:会席・会食 10:納骨 11:葬儀・告別式 12:創想の儀 13:散会 14:控室 15:湯灌 16:通夜後会食 17:枕経
            var niteiKbn = this.model.get("nitei_kbn");
            if ($.inArray(niteiKbn, [1, 3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]) >= 0) {
                // 場所区分
                var bashoKbn = this.model.get('basho_kbn');

                if (((niteiKbn === 3 || niteiKbn === 4 || niteiKbn === 5 || niteiKbn === 8 || niteiKbn === 9 || niteiKbn === 11 || niteiKbn === 12 || niteiKbn === 14 || niteiKbn === 15 || niteiKbn === 16 || niteiKbn === 17) && (bashoKbn == BASHO_KIND_HALL || bashoKbn == BASHO_KIND_OTHER))) {
                    this.$(".place").data("action", 'kaijyo');
                } else {
                    this.$(".place").data("action", null);
                }
                // pickname入力可能区分
                var disable = false;
                _setSpotStatus(niteiKbn, bashoKbn, this.$(".place"), this.$(".dlg_place"), disable);
                if (bashoKbn === '0' || bashoKbn === '9' || disable) { // 0:自宅と9:その他はpickup無し
                    bashoKbn = null;
                }
                if (niteiKbn !== 1 && bashoKbn === "7") { // 7:火葬場（斎場）
                    this.$(".place").data("kind2", "3");
                    this.$(".place").data("s_hanso_kbn", bashoKbn);
                } else {
                    this.$(".place").data("kind2", bashoKbn);
                }
            }
        },
        setBashoNm: function () {
            var t = this;
            var cm = this.model;
            var cur_nitei_kbn = cm.get("nitei_kbn");
            if ($.inArray(cur_nitei_kbn, [1, 3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]) >= 0) { // 4:通夜 5:出棺 11:葬儀 7:告別式
                _.each(appcst.niteiCol.models, function (m) {
                    var niteiKbn = m.get("nitei_kbn");
                    if ($.inArray(niteiKbn, [1, 3, 4, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]) >= 0 && cur_nitei_kbn !== niteiKbn) { // 4:通夜 5:出棺 11:葬儀 7:告別式
                        var orgNitei = t.getOrgNitei(niteiKbn);
                    }
                });
            }
        },
        getOrgNitei: function (kbn) {
            var ret = null;
            _.each(orgDataNiteiCol, function (o) {
                if (o.nitei_kbn === kbn) {
                    ret = o;
                }
            });
            return ret;
        }
    }); // NiteiView
    //
    // 施行基本フリーモデル
    var KihonFreeModel = Backbone.Model.extend({
        defaults: function () {
            return {
                // 受付情報タブ
                ts_free1: null, // 受付日
                ts_free1_date: null, // 受付日(日付のみ)
                ts_free1_time: null, // 受付日(時間のみ)
                renraku_nm: null, // 連絡者お名前
                renraku_zoku_code_cd: codeKbns.zoku_code_kbn, // 連絡者続柄
                renraku_zoku_kbn: null, // 連絡者続柄
                renraku_tel: null, // 連絡者携帯番号
                tanto_cd2: null, // 納棺担当者
                tanto_nm2: null, // 納棺担当者
                tanto_cd3: null, // 通夜担当者
                tanto_nm3: null, // 通夜担当者
                tanto_cd4: null, // 設営担当者
                tanto_nm4: null, // 設営担当者
                tanto_cd5: null, // 司会担当者
                tanto_nm5: null, // 司会担当者
                tanto_cd8: null, // 片付担当者
                tanto_nm8: null, // 片付担当者
                tanto_nm9: null, // 接待担当者（通夜）
                tanto_nm10: null, // 接待担当者（葬儀）
                tanto_nm11: null, // ライフサービス（通夜）
                tanto_nm12: null, // ライフサービス（葬儀）
                tanto_nm13: null, //  霊柩車運転
                tanto_nm14: null, // マイクロバス
                tanto_cd15: null, // 預り印鑑担当者コード
                tanto_nm15: null, // 預り印鑑担当者名
                tanto_cd16: null, // 預り死亡診断書・死亡届担当者コード
                tanto_nm16: null, // 預り死亡診断書・死亡届担当者名
                tanto_cd17: null, // 預り火葬料担当者コード
                tanto_nm17: null, // 預り火葬料担当者名
                tanto_cd18: null, // 預り写真担当者コード
                tanto_nm18: null, // 預り写真担当者名
                tanto_cd19: null, // 預り加入者証担当者コード
                tanto_nm19: null, // 預り加入者証担当者名
                tanto_cd20: null, // 預りその他担当者コード
                tanto_nm20: null, // 預りその他担当者名
                tanto_cd21: null, // 預りその他担当者コード
                tanto_nm21: null, // 預りその他担当者名
                tanto_cd22: null, // 受取印鑑担当者コード
                tanto_nm22: null, // 受取印鑑担当者名
                tanto_cd23: null, // 受取火葬・埋葬許可書担当者コード
                tanto_nm23: null, // 受取火葬・埋葬許可書担当者名
                tanto_cd24: null, // 受取写真原版担当者コード
                tanto_nm24: null, // 受取写真原版担当者名
                tanto_cd25: null, // 受取火葬領収書担当者コード
                tanto_nm25: null, // 受取火葬領収書担当者名
                tanto_cd26: null, // 受取その他担当者コード
                tanto_nm26: null, // 受取その他担当者名
                tanto_cd27: null, // 受取その他担当者コード
                tanto_nm27: null, // 受取その他担当者名

                // 打合せ事項②
                form_answer_kbn: "1", // 問合せ(する・しない)

            };
        },
        validation: {
            ts_free1_date: {
                required: true,
                customFun: function (value) {
                    return _chkYmd(value);
                }
            },
            ts_free1_time: {
                required: false,
                pattern: 'time'
            },
            renraku_tel: {
                required: false,
                pattern: 'tel'
            },
        },
        labels: {
            ts_free1: '受付日',
            ts_free1_date: '受付日',
            ts_free1_time: '受付時間',
            renraku_tel: '連絡者携帯番号',
        },
    });

    // 施行基本フリービュー
    var KihonFreeView = Backbone.View.extend({
        el: $("#customer-tab"),
        events: {
            "click #answeryes": function () {
                // 打合せ事項②タブ 問合せ（する）設定
                this.model.set("form_answer_kbn", "1");
            },
            "click #answerno": function () {
                // 打合せ事項②タブ 問合せ（しない）設定
                this.model.set("form_answer_kbn", "2");
            },
            "click .label.dlg_staff": "tantoHelper",
        },
        bindings: {
            '#uketuke_date': {
                observe: 'ts_free1_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('ts_free1_time'), 'ts_free1', this.model);
                    return $el.val();
                }
            },
            '#uketuke_time': {
                observe: 'ts_free1_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('ts_free1_date'), $el.val(), 'ts_free1', this.model);
                    return $el.val();
                }
            },
            '#renraku_name': 'renraku_nm',
            '#renraku_zoku': {
                observe: 'renraku_zoku_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCodeKbn($el, this.model, 'renraku_zoku_code_cd');
                    return $el.val();
                }
            },
            '#renraku_tel': 'renraku_tel',
            "input[name='form_check']": $.msiJqlib.getRadioBinding('form_answer_kbn'),
            '#nokan_tanto': {
                observe: 'tanto_cd2',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm2');
                    return $el.val();
                }
            },
            '#tuya_tanto': {
                observe: 'tanto_cd3',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm3');
                    return $el.val();
                }
            },
            '#setsuei_tanto': {
                observe: 'tanto_cd4',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm4');
                    return $el.val();
                }
            },
            '#shikai_tanto': {
                observe: 'tanto_cd5',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm5');
                    return $el.val();
                }
            },
            '#kataduke_tanto': {
                observe: 'tanto_cd8',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm8');
                    return $el.val();
                }
            },
            '#tanto_nm9': 'tanto_nm9',
            '#tanto_nm10': 'tanto_nm10',
            '#tanto_nm11': 'tanto_nm11',
            '#tanto_nm12': 'tanto_nm12',
            '#tanto_nm13': 'tanto_nm13',
            '#tanto_nm14': 'tanto_nm14',
            '#tanto15': {
                observe: 'tanto_cd15',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm15');
                    return $el.val();
                }
            },
            '#tanto16': {
                observe: 'tanto_cd16',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm16');
                    return $el.val();
                }
            },
            '#tanto17': {
                observe: 'tanto_cd17',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm17');
                    return $el.val();
                }
            },
            '#tanto18': {
                observe: 'tanto_cd18',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm18');
                    return $el.val();
                }
            },
            '#tanto19': {
                observe: 'tanto_cd19',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm19');
                    return $el.val();
                }
            },
            '#tanto20': {
                observe: 'tanto_cd20',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm20');
                    return $el.val();
                }
            },
            '#tanto21': {
                observe: 'tanto_cd21',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm21');
                    return $el.val();
                }
            },
            '#tanto22': {
                observe: 'tanto_cd22',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm22');
                    return $el.val();
                }
            },
            '#tanto23': {
                observe: 'tanto_cd23',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm23');
                    return $el.val();
                }
            },
            '#tanto24': {
                observe: 'tanto_cd24',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm24');
                    return $el.val();
                }
            },
            '#tanto25': {
                observe: 'tanto_cd25',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm25');
                    return $el.val();
                }
            },
            '#tanto26': {
                observe: 'tanto_cd26',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm26');
                    return $el.val();
                }
            },
            '#tanto27': {
                observe: 'tanto_cd27',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCdText($el, this.model, 'tanto_nm27');
                    return $el.val();
                }
            },
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.render();
        },
        render: function () {
            this.stickit();

            this.$("#uketuke_date, #sibo_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#uketuke_time, #sibo_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));

            // 受付情報タブ
            // 連絡者続柄
            $.msiJqlib.setSelect2Com1(this.$("#renraku_zoku"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));

            return this;
        },
        // 担当者ヘルパー処理 
        tantoHelper: function (e) {
            var m = this.model;
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var code = $target.data("code");
            var name = $target.data("name");
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set(code, data.code);
                    m.set(name, data.name);
                },
                onClear: function () {
                    m.set({code: null, name: null});
                }
            });
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
    });

    // 受付情報タブ 特記事項モデル
    var SekoMemoModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seq_no: null, //　明細番号
                discuss_date: null, // 日付
                discuss_time: null, // 時間
                discuss_ymd: null, // 時間
                tanto_nm: null, // 担当者名
                tanto_cd: null, // 担当者コード
                status_kbn: null, // ステータス
                level_kbn: null, // 重要度
                contents: null, // 内容
            };
        },
        validation: {
            discuss_ymd: function (val, attr, computed) {
                if ($.msiJqlib.isNullEx2(computed.discuss_date) && !$.msiJqlib.isNullEx2(computed.discuss_time)) {
                    return '日付と時刻の整合性がありません';
                }
            },
            discuss_date: function (value) {
                return _chkYmd(value);
            },
            discuss_time: {
                required: false,
                pattern: 'time'
            },
        },
        labels: {
            discuss_date: '打合履歴日付',
            discuss_time: '打合履歴時刻',
        }
    }); // SekoMemoModel

    // 特記事項コレクション
    var SekoMemoCollection = Backbone.Collection.extend({
        model: SekoMemoModel,
        resetLineNo: function () {
            var i, max, m;
            for (i = 0, max = this.length; i < max; i++) {
                m = this.at(i);
                m.set('seq_no', i + 1);
                // console.log( 'seq_no=>' + m.get('seq_no') + ' line_no=>' + m.get('line_no') + ' ' + m.get('msi_biko2') );
            }
        },
    });

    // 特記事項ビュー
    var SekoMemoView = Backbone.View.extend({
        tagName: 'tbody',
        tmpl: _.template($('#tmpl-seko_memo').html()),
        events: {
            "click a.destroy": "clear",
            "click a.add": "add",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
//            "click .tanto_nm, .label.dlg_staff": "tantoHelper",
            "select2-opening .tanto_cd": function (e) {
                // 対象施行の葬儀日から退職者は表示させないようにする
                var sougi_ymd = this.model.get('sougi_ymd');
                var kijunYmd = $.msiJqlib.getStdDate();
                if (!$.msiJqlib.isNullEx2(sougi_ymd)) {
                    kijunYmd = sougi_ymd;
                }
                var fileredKbns = [];
                _.each(data.dataKbns.tanto_mst, function (item) {
                    if ($.msiJqlib.isNullEx2(item.tekiyo_ed_date)) {
                        fileredKbns.push(item);
                    } else if (item.tekiyo_ed_date >= kijunYmd) {
                        fileredKbns.push(item);
                    }
                });
                appcst.tanto_cds = fileredKbns;
            },
        },
        bindings: {
            '.seq_no': 'seq_no',
            '.discuss_date': {
                observe: 'discuss_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('discuss_time'), 'discuss_ymd', this.model);
                    return $el.val();
                }
            },
            '.discuss_time': {
                observe: 'discuss_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('discuss_date'), $el.val(), 'discuss_ymd', this.model);
                    return $el.val();
                }
            },
//            '.tanto_nm': {
//                observe: 'tanto_nm',
//                onSet: function (val, options) {
//                    if ($.msiJqlib.isNullEx2(val)) {
//                        this.model.set('tanto_cd', null);
//                    }
//                    return val;
//                }
//            },
            '.tanto_cd': {
                observe: 'tanto_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '.status': {
                observe: 'status_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '.level': {
                observe: 'level_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '.contents': 'contents',
        },
        initialize: function () {
            Backbone.Validation.bind(this);
            this.render();
        },
        render: function () {
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.$el.html(this.tmpl({idx: this.model.cid}));
            this.stickit();
            this.$(".discuss_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".discuss_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            // select2
            // 担当者
            appcst.tanto_cds = data.dataKbns.tanto_mst;
            $.msiJqlib.setSelect2Com1(this.$(".tanto_cd"), ($.extend({data: function () {
                    return {results: appcst.tanto_cds};
                }}, $.msiJqlib.setSelect2Default2)));
            // ステータス
            $.msiJqlib.setSelect2Com1(this.$(".status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 重要度
            $.msiJqlib.setSelect2Com1(this.$(".level"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.category_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 明細一行目は削除ボタンを非表示にする
            if (this.model.get('seq_no') == '1') {
                this.$(".destroy").hide();
            }
            return this;
        },
        // Create a item. ugly...
        add: function () {

            var off = this.model.get('seq_no');
            var newModel = new SekoMemoModel;

            // console.log( 'add line_no=>' + off + ' shift=>' + isUpside + ' isCopy=>' + isCopy );

            appcst.sekomemoCol.add(newModel, {at: off, silent: true}); // add event を挙げない
            appcst.sekomemoCol.resetLineNo(); // line_no を再設定
            this.addOne(newModel, appcst.sekomemoCol, {at: off});
        },
        clear: function () {
            this.model.destroy();
            this.remove();
            appcst.sekomemoCol.resetLineNo(); // line_no を再設定
        },
        addOne: function (model, list, options) {
            var v = new SekoMemoView({model: model});

            var off;
            if (_.has(options, 'at')) {
                off = options['at'];
            }
            // console.log( 'line_no=>' + line_no + ' of=>' + off + ' addOne *** => ' + JSON.stringify(meisai) );

            if (off === undefined) {
                $("#memo_msi").append(v.render().el);
            } else if (off > 0) {
                $("#memo_msi").find('tbody').eq(off - 1).after(v.render().el);
            } else { // off === 0
                $("#memo_msi").prepend(v.render().el);
            }

        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        },
        // 担当者ヘルパー処理 
        tantoHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'tanto_cd': data.code, 'tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'tanto_cd': null, 'tanto_nm': null});
                }
            });
        },
    }); // SekoMemoView
    //
    // 打合せ事項②タブ 更新履歴モデル
    var HisrtoryModel = Backbone.Model.extend({
        defaults: function () {
            return {
                msi_no: null, //　明細番号
                tanto_nm: null, // 更新担当者
                contents: null, // 更新内容
                update_ymd: null, // 更新日時
            };
        },
        validation: {
        },
        labels: {
        }
    }); // HisrtoryModel

    // 更新履歴コレクション
    var HistoryCollection = Backbone.Collection.extend({
        model: HisrtoryModel
    });

    // 更新履歴ビュー
    var HistoryView = Backbone.View.extend({
        tagName: 'fieldset',
        className: 'history_msi',
        tmpl: _.template($('#tmpl-history').html()),
        events: {
        },
        bindings: {
            '.msi_no': 'seq_no',
            '.tanto_nm': 'tanto_nm',
            '.contents': 'update_contents',
            '.update_ymd': 'update_ts',
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.render();
        },
        render: function () {
            this.$el.html(this.tmpl({idx: this.model.cid}));
            this.stickit();
            return this;
        }
    }); // HistoryView

    msiGlobalObj.markObj.mark('start');
    // データ取得
    try {
        // DBから取得したJsonデータ
        var data = JSON.parse(_.unescape($('#data-customerinfo').text()));
        //console.log(JSON.stringify(mydata.dataSekoDtlCol))
    } catch (e) {
        console.log('JSON error. ' + e);
        $.msiJqlib.showErr('JSON error. ' + e);
    }
    appcst.data = data;
    var codeKbns = data.codeKbns;
    var sidemenukey = data.sidemenukey;
    var seko_kaisya_cd = data.seko_kaisya_cd;
    var orgKeiyakuSbt = data.keiyakuCif;
    var orgModCnts = data.modCnts;
    appcst.area_cd = data.area_cd;
    // コントローラー名
    appcst.controllerName = data.controllerName;
    // 施行メモ初期化
    appcst.sekomemoCol = new SekoMemoCollection();
    // 更新履歴初期化
    appcst.historyCol = new HistoryCollection();
    // 日程タブ初期化
    appcst.niteiCol = new NiteiCollection();
    // 互助会タブ初期化
    appcst.gojokaiInfoModel = new appgjk.GojokaiInfoModel();
    appcst.sekoKeiyakusakiInfoModel = new appgjk.SekoKeiyakusakiInfoModel();
    appcst.gojokaiMemberCol = new appgjk.GojokaiMemberCollection();
    appcst.gojokaiInfoView = new appgjk.GojokaiInfoView({model: appcst.gojokaiInfoModel});
    appcst.sekoKeiyakusakiInfoView = new appgjk.SekoKeiyakusakiInfoView({model: appcst.sekoKeiyakusakiInfoModel});
    // 喪主タブ請求先初期化処理
    appcst.sekyuModel = new appsk.SekyuModel();
    appcst.sekyuView = new appsk.SekyuView({model: appcst.sekyuModel});
    // APP初期化処理
    appcst.kfModel = new KihonFreeModel();
    appcst.kfView = new KihonFreeView({model: appcst.kfModel});
    appcst.appModel = new AppModel();
    appcst.appView = new AppView({model: appcst.appModel});
    //
    var orgDataApp, orgDataNiteiCol, orgDataSekyuInfo, orgDataGojokaiInfo, orgDataGojokaiMemberCol, orgDataKihonFree, orgDataSekoMemoCol;
    var orgUpdateHistroyContents, orgDataSekoKeiyakusakiInfo;
    var _resetData = function (dataSekoKihon, dataNiteiCol, dataSekyuInfo, dataGojokaiInfo, dataSekoKeiyakusakiInfo, dataGojokaiMemberCol, dataKihonFree, dataSekoMemoCol, dataUpdateHistoryCol, dataUpdateHistroyContents) {
        // モデルのデータを設定
        appcst.kfView.model.set(dataKihonFree);
        appcst.appView.model.set(dataSekoKihon);
        appcst.sekyuView.model.set(dataSekyuInfo);
        appcst.gojokaiInfoView.model.set(dataGojokaiInfo);
        appcst.sekoKeiyakusakiInfoView.model.set(dataSekoKeiyakusakiInfo);
        // resetによりAppViewのresetイベントが発火
        appcst.sekomemoCol.reset(dataSekoMemoCol);
        appcst.historyCol.reset(dataUpdateHistoryCol);
        appcst.niteiCol.reset(dataNiteiCol);
        appcst.gojokaiMemberCol.reset(dataGojokaiMemberCol);

        if (appcst.controllerName === "juchuhenko") {
            $("#h_btn_new").hide();
        }
        // 基本・喪主・その他タブ 
        if (dataSekoKihon || dataKihonFree) {
            if (!$.msiJqlib.isNullEx2(dataSekoKihon.jichu_kakute_ymd)) {
                $("#hall_cd").attr("disabled", "disabled");
                $("#est_shikijo_cd").attr("disabled", "disabled");
            } else {
                $("#hall_cd").removeAttr("disabled");
                $("#est_shikijo_cd").removeAttr("disabled");
            }
            // 受注変更の場合、部門コード変更可にする
//            if (appcst.controllerName === "juchuhenko") {
//                $("#hall_cd").removeAttr("disabled");
//            }
            // 基本タブ 性別を設定
            if (dataSekoKihon.k_sex_kbn === "2") {
                $('#female').attr("checked", "checked");
                $('#female').prev().addClass("ui-state-active");
                $('#male').removeAttr("checked", "checked");
                $('#male').prev().removeClass("ui-state-active");
            } else if (dataSekoKihon.k_sex_kbn === "1") {
                $('#male').attr("checked", "checked");
                $('#male').prev().addClass("ui-state-active");
                $('#female').removeAttr("checked", "checked");
                $('#female').prev().removeClass("ui-state-active");
            } else {
                $('#male').removeAttr("checked", "checked");
                $('#male').prev().removeClass("ui-state-active");
                $('#female').removeAttr("checked", "checked");
                $('#female').prev().removeClass("ui-state-active");
            }
            if (dataSekoKihon.m_sex_kbn === "2") {
                $('#m_female').attr("checked", "checked");
                $('#m_female').prev().addClass("ui-state-active");
                $('#m_male').removeAttr("checked", "checked");
                $('#m_male').prev().removeClass("ui-state-active");
            } else if (dataSekoKihon.m_sex_kbn === "1") {
                $('#m_male').attr("checked", "checked");
                $('#m_male').prev().addClass("ui-state-active");
                $('#m_female').removeAttr("checked", "checked");
                $('#m_female').prev().removeClass("ui-state-active");
            } else {
                $('#m_male').removeAttr("checked", "checked");
                $('#m_male').prev().removeClass("ui-state-active");
                $('#m_female').removeAttr("checked", "checked");
                $('#m_female').prev().removeClass("ui-state-active");
            }
            // 基本タブ 問合せのする・しないを設定
            if (dataKihonFree.form_answer_kbn === "2") {
                $('#answerno').click();
            } else {
                $('#answeryes').click();
            }
            // 弔意対応設定処理
            var choi_kyoka_cd = dataSekoKihon.choi_kyoka_cd;
            // 商品統一ときはプルダウンのみ有効にする
            if (choi_kyoka_cd == CHOI_KUMOTU_SHOHIN) {
                $("#bechu_shohin_bumon").removeAttr("disabled");
                $("#bechu_shohin").removeAttr("disabled");
                $("#choi_tokuchu_prc").attr("disabled", "disabled");
                // 可否を編集不可
//                $("#seika_fa").attr("disabled", "disabled");
//                $("#sikimi_fa").attr("disabled", "disabled");
//                $("#sakaki_fa").attr("disabled", "disabled");
//                $("#hanawa_fa").attr("disabled", "disabled");
//                $("#kudamono_fa").attr("disabled", "disabled");
//                $("#can_fa").attr("disabled", "disabled");
            } else if (choi_kyoka_cd == CHOI_KUMOTU_PRC) { // 金額統一のときはプルダウンのみ有効にする
                $("#bechu_shohin_bumon").removeAttr("disabled");
                $("#bechu_shohin").removeAttr("disabled");
                $("#choi_tokuchu_prc").attr("disabled", "disabled");
                // 可否を可にして編集可能にする
//                $("#seika_fa").removeAttr("disabled");
//                $("#sikimi_fa").removeAttr("disabled");
//                $("#sakaki_fa").removeAttr("disabled");
//                $("#hanawa_fa").removeAttr("disabled");
//                $("#kudamono_fa").removeAttr("disabled");
//                $("#can_fa").removeAttr("disabled");
            } else if (choi_kyoka_cd == CHOI_KUMOTU_SP) { // 金額特注のときは金額入力できるようにする
                $("#bechu_shohin_bumon").removeAttr("disabled");
                $("#bechu_shohin").removeAttr("disabled");
                $("#choi_tokuchu_prc").removeAttr("disabled");
                // 可否を編集不可
//                $("#seika_fa").attr("disabled", "disabled");
//                $("#sikimi_fa").attr("disabled", "disabled");
//                $("#sakaki_fa").attr("disabled", "disabled");
//                $("#hanawa_fa").attr("disabled", "disabled");
//                $("#kudamono_fa").attr("disabled", "disabled");
//                $("#can_fa").attr("disabled", "disabled");
            } else {
                $("#bechu_shohin_bumon").attr("disabled", "disabled");
                $("#bechu_shohin").attr("disabled", "disabled");
                $("#choi_tokuchu_prc").attr("disabled", "disabled");
                if (choi_kyoka_cd == CHOI_KUMOTU_JITAI) {
                    // 可否を未設定にして編集不可
//                    $("#seika_fa").attr("disabled", "disabled");
//                    $("#sikimi_fa").attr("disabled", "disabled");
//                    $("#sakaki_fa").attr("disabled", "disabled");
//                    $("#hanawa_fa").attr("disabled", "disabled");
//                    $("#kudamono_fa").attr("disabled", "disabled");
//                    $("#can_fa").attr("disabled", "disabled");
                } else if (choi_kyoka_cd == CHOI_KUMOTU_NOSHITEI) {
                    // 可否を可にして編集可能にする
//                    $("#seika_fa").removeAttr("disabled");
//                    $("#sikimi_fa").removeAttr("disabled");
//                    $("#sakaki_fa").removeAttr("disabled");
//                    $("#hanawa_fa").removeAttr("disabled");
//                    $("#kudamono_fa").removeAttr("disabled");
//                    $("#can_fa").removeAttr("disabled");
                }
            }
            // 弔意対応連絡先がその他の時は直接入力できるようにする
            var information_cd = dataSekoKihon.information_cd;
            if (information_cd == SEIKA_INFO_OTHER) {
                $("#seika_contact").removeAttr("disabled");
                $("#form_tel").removeAttr("disabled");
                $("#form_fax").removeAttr("disabled");
            } else {
                $("#seika_contact").attr("disabled", "disabled");
                $("#form_tel").attr("disabled", "disabled");
                $("#form_fax").attr("disabled", "disabled");
            }
            // 印鑑を設定
            if (dataSekoKihon.az_inkan_kbn === "1" && $('#stamp:checked').val() !== "1") {
                $('#stamp').click();
            }
            if (dataSekoKihon.moushi_cd === MOUSHI_KBN_SEIZEN) {
                $('#apply_type').attr("disabled", "disabled");
                $('#btn_consult').show();
            }
            if (!$.msiJqlib.isNullEx2(data) && !$.msiJqlib.isNullEx2(data.dataShoudanRev) && data.dataShoudanRev.length > 0) {
                $('#btn_seko_copy').show();
            }
            // 宗派その他の場合の処理
            appcst.appView.setShuhaOther();
            // 故人名画像ファイルリンク設定
            appcst.appView.setImgLink("#input-tab", dataSekoKihon.k_file_nm, null);
            // 遺族名画像ファイルリンク設定
            appcst.appView.setImgLink("#input-tab .field_izoku", dataSekoKihon.izoku_file_nm, null);
            // 喪主名画像ファイルリンク設定
            appcst.appView.setImgLink("#infochief-tab #chief", dataSekoKihon.m_file_nm, null);
        }
        if (dataSekyuInfo) {
            // 請求先画像ファイルリンク設定
            appcst.appView.setImgLink("#infochief-tab #bill_sekyu", dataSekyuInfo.sekyu_file_nm, null);
            appcst.appView.setImgLink("#infochief-tab #bill_soufu", dataSekyuInfo.soufu_file_nm, null);
            appcst.appView.setImgLink("#infochief-tab #bill_ryosyu_soufu", dataSekyuInfo.ryosyu_soufu_file_nm, null);
        }
        // 互助会タブ
        if (dataGojokaiInfo) {
            // 施行履歴を設定
            if (dataGojokaiInfo.rireki_kbn === "1") {
                $('#experienced').click();
            } else {
                $('#inexperienced').click();
            }
            // 事前相談有無を設定
            if (dataGojokaiInfo.sodan_kbn === "1") {
                $('#adviced').click();
            } else {
                $('#unadviced').click();
            }
            // 客様加入確認有無を設定
            if (dataGojokaiInfo.kanyu_kakunin_kbn === "1") {
                $('#registered').click();
            } else {
                $('#unregistered').click();
            }
            // 加入団体利用確認有無を設定
            if (dataGojokaiInfo.join_use_kbn === "1") {
                $('#join_used').click();
            } else {
                $('#join_unused').click();
            }
            if (dataGojokaiInfo.plan_use_kbn === '1') {
                $('#plan_agree').click();
            } else {
                $('#plan_disagree').click();
            }
        }
        // 施行金額確定されていたら特定項目を編集不可にする
        if (dataSekoKihon.status_kbn >= STATUS_SEKO_KAKUTEI) {
            // 部門
            $('#hall_cd').attr('disabled', 'disabled');
            // 施行担当者
            $('.dlg_staff2').attr('disabled', 'disabled');
            // 施行情報タブ
            var $form = $('#infodate-tab');
            $form.msiInputReadonly()
                    .msiCalReadonly()
                    .find('.my-readonly-hidden').hide();
            // 会員情報タブ
            var $form = $('#member_1');
            $form.msiInputReadonly()
                    .msiCalReadonly()
                    .find('.my-readonly-hidden').hide();
            var $form = $('#member_2');
            $form.msiInputReadonly()
                    .msiCalReadonly()
                    .find('.my-readonly-hidden').hide();
            $('#btn_gojokai_search').attr('disabled', 'disabled')
        }
//        console.log(str);
        // 部門コードの初期値セット
        if ($.msiJqlib.isNullEx2(appcst.appModel.get('bumon_cd'))) {
            var bumonCd = $('#hall_cd').val();
//            appcst.appModel.set('bumon_cd', appcst.appModel.get('login_bumon_cd'));
            appcst.appModel.set('bumon_cd', bumonCd);
            msiLib2.setHallCd(bumonCd);
        }
        // データを退避する
        orgDataApp = appcst.appModel.toJSON();
        orgDataKihonFree = appcst.kfModel.toJSON();
        orgDataNiteiCol = appcst.niteiCol.toJSON();
        orgDataSekyuInfo = appcst.sekyuModel.toJSON();
        orgDataGojokaiInfo = appcst.gojokaiInfoModel.toJSON();
        orgDataSekoKeiyakusakiInfo = appcst.sekoKeiyakusakiInfoModel.toJSON();
        orgDataGojokaiMemberCol = appcst.gojokaiMemberCol.toJSON();
        orgDataSekoMemoCol = appcst.sekomemoCol.toJSON();
        orgUpdateHistroyContents = dataUpdateHistroyContents
        // 上記プラン選択を非活性
        $("#plan_change_set input[name='plan_kbn']").button({disabled: true});
    };
    msiGlobalObj.markObj.mark('reset');
    _resetData(data.dataSekoKihon, data.dataNiteiCol, data.dataSekyuInfo, data.dataGojokaiInfo, data.dataSekoKeiyakusakiInfo, data.dataGojokaiMemberCol, data.dataSekoKihonFree, data.dataSekoMemoCol, data.dataUpdateHistoryCol, data.dataUpdateHistoryContents);
    msiGlobalObj.markObj.markOutput();
    // 報告書タブ設定処理
    appcst.resetReport(appcst.data);
    // 訃報連絡タブ設定処理
    appcst.resethuhoInfo(appcst.data);
    // 結果情報
    appcst.resetresultInfo(appcst.data);
    appcst.resetsekosha(appcst.data);
    // その他の御写真
//    appcst.filePhoto = fileUpLib.upload({m: appcst.appModel, attr_oid: 'img_free1', attr_fnm: 'v_free1', el: '#file_clip_portrait', imgprv: false});
//    fileUpLib.upload({m:this.model,attr_oid:"pdf_img",attr_fnm:"pdf_file_nm",el:"#file_clip_gazo1",imgprv:!1,filekbn:1})
    // ファイルリンク設定処理
//    appcst.setFileLink = function () {
//        appcst.filePhoto.setlink();
//    };
    // その他のアンケート
    appcst.filePdf = fileUpLib.upload({m: appcst.appModel, attr_oid: 'img_free3', attr_fnm: 'v_free35', el: '#file_clip_anketo', imgprv: false, filekbn:5});
//    fileUpLib.upload({m:this.model,attr_oid:"pdf_img",attr_fnm:"pdf_file_nm",el:"#file_clip_gazo1",imgprv:!1,filekbn:1})
    // ファイルリンク設定処理
    appcst.setFileLink = function () {
//        appcst.filePhoto.setlink();
        appcst.filePdf.setlink();
    };
    // かな自動入力ペア key:名称要素 value[カナ要素, カナモデルプロパティ, モデル]
    var _name_kana_pair = {
        '#input-tab #k_last_nm': ['#input-tab #k_last_knm', 'k_last_knm', appcst.appModel]
        , '#input-tab #k_first_nm': ['#input-tab #k_first_knm', 'k_first_knm', appcst.appModel]
        , '#input-tab #m_last_nm': ['#input-tab #m_last_knm', 'm_last_knm', appcst.appModel]
        , '#input-tab #m_first_nm': ['#input-tab #m_first_knm', 'm_first_knm', appcst.appModel]
        , '#infochief-tab #chief #m_last_nm': ['#infochief-tab #chief #m_last_knm', 'm_last_knm', appcst.appModel]
        , '#infochief-tab #chief #m_first_nm': ['#infochief-tab #chief #m_first_knm', 'm_first_knm', appcst.appModel]
        , '#infochief-tab #bill #sekyu_last_nm': ['#infochief-tab #bill #sekyu_last_knm', 'sekyu_last_knm', appcst.sekyuModel]
        , '#infochief-tab #bill #sekyu_first_nm': ['#infochief-tab #bill #sekyu_first_knm', 'sekyu_first_knm', appcst.sekyuModel]
        , '#infochief-tab #bill #soufu_last_nm': ['#infochief-tab #bill #soufu_last_knm', 'soufu_last_knm', appcst.sekyuModel]
        , '#infochief-tab #bill #soufu_first_nm': ['#infochief-tab #bill #soufu_first_knm', 'soufu_first_knm', appcst.sekyuModel]
        //, '#infochief-tab #bill #ryosyu_soufu_last_nm': ['#infochief-tab #bill #ryosyu_soufu_last_knm', 'ryosyu_soufu_last_knm', appcst.sekyuModel]
        //, '#infochief-tab #bill #ryosyu_soufu_first_nm': ['#infochief-tab #bill #ryosyu_soufu_first_knm', 'ryosyu_soufu_first_knm', appcst.sekyuModel]
        , '#infochief-tab #fc_last_nm': ['#infochief-tab #fc_last_knm', 'fc_last_knm', appcst.appModel]
        , '#infochief-tab #fc_first_nm': ['#infochief-tab #fc_first_knm', 'fc_first_knm', appcst.appModel]
        , '#infochief2-tab #family_name': ['#infochief2-tab #family_name_kana', 'souke_knm', appcst.appModel]
//        , '#input-tab #syuha_nm_other': ['#input-tab #syuha_knm2', 'syuha_knm', appcst.appModel]
        , '#infomember-tab #user_last_nm': ['#infomember-tab #user_last_knm', 'user_last_knm', appcst.sekoKeiyakusakiInfoModel]
        , '#infomember-tab #user_first_nm': ['#infomember-tab #user_first_knm', 'user_first_knm', appcst.sekoKeiyakusakiInfoModel]
        , '#infochief-tab #dm_last_nm': ['#input-tab #dm_last_knm', 'dm_last_knm', appcst.appModel]
        , '#infochief-tab #dm_first_nm': ['#input-tab #dm_first_knm', 'dm_first_knm', appcst.appModel]
    };
    // カナ自動入力設定
    $.msiJqlib.setAutoKanaModel(_name_kana_pair);

    var _zip_addr_pair = {
        '#input-tab #zip_1': ['kg_yubin_no', 'kg_addr1', appcst.appModel]
        , '#input-tab #dm_yubin_no': ['dm_yubin_no', 'dm_addr1', appcst.appModel]
        , '#input-tab #homonsaki_zip_no': ['homonsaki_zip_no', 'homonsaki_addr1', appcst.appModel]
        , '#infochief-tab #zip_1': ['mg_yubin_no', 'mg_addr1', appcst.appModel]
        , '#infochief-tab #zip_4': ['sekyu_yubin_no', 'sekyu_addr1', appcst.sekyuModel]
        , '#infochief-tab #zip_5': ['soufu_yubin_no', 'soufu_addr1', appcst.sekyuModel]
        , '#infochief-tab #zip_6': ['ryosyu_soufu_yubin_no', 'ryosyu_soufu_addr1', appcst.sekyuModel]
        , '#infochief2-tab #temple_yubin_no': ['temple_yubin_no', 'temple_addr1', appcst.appModel]
        , '#huho-tab #huho_tuya_zip': ['huho_tuya_zip', 'huho_tuya_addr', appcst.huhoInfoModel]
        , '#huho-tab #huho_sougi_zip': ['huho_sougi_zip', 'huho_sougi_addr', appcst.huhoInfoModel]
    };

    // 郵便番号による住所1自動入力設定
    $.msiJqlib.setAutoZipToAddrModel(_zip_addr_pair);

    // タブの横幅設定
    var $li = $("#detail .tab li");
    $li.width(100 / $li.length + "%");

    $("#customer-div-wrapper").show();

    // ヘッダー共通処理
    // 新規作成ボタン押下
    $("#header #btn_new").click(function () {
        location.href = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/new';
    });
    // ページ遷移前の確認
    $(window).on('beforeunload', function () {
        if (_isChanged()) {
            return "保存されていないデータがあります.";
        }
    });
    var _isChanged = function () {
        var changed = true;
        // 施行基本イコール
        var sekoKihonEq = $.msiJqlib.isEqual(appcst.appModel.toJSON(), orgDataApp);
        // 施行基本汎用フリー情報イコール
        var sekoKihonFreeEq = $.msiJqlib.isEqual(appcst.kfModel.toJSON(), orgDataKihonFree);
        // 施行日程イコール
        var sekoNiteiEq = $.msiJqlib.isEqual(appcst.niteiCol.toJSON(), orgDataNiteiCol);
        // 請求先情報イコール
        var sekyuInfoEq = $.msiJqlib.isEqual(appcst.sekyuModel.toJSON(), orgDataSekyuInfo);
        // 施行互助会情報イコール
        var sekoGojokaiInfoEq = $.msiJqlib.isEqual(appcst.gojokaiInfoModel.toJSON(), orgDataGojokaiInfo);
        // 施行契約先情報イコール
        var sekoKeiyakusakiInfoEq = $.msiJqlib.isEqual(appcst.sekoKeiyakusakiInfoModel.toJSON(), orgDataSekoKeiyakusakiInfo);
        // 施行互助会加入者イコール
        var sekoGojokaiMemberEq = $.msiJqlib.isEqual(appcst.gojokaiMemberCol.toJSON(), orgDataGojokaiMemberCol);
        //  施行引継書イコール
        var hikitsugiEq = $.msiJqlib.isEqual(appcst.hikitsugiModel.toJSON(), appcst.orgHikitsugiModelJson);
        //  手配依頼書イコール
        var tehaiIraiEq = $.msiJqlib.isEqual(appcst.tehaiIraiModel.toJSON(), appcst.orgTehaiIraiModelJson);
        //  式場移動イコール
        var shikijoIdoEq = $.msiJqlib.isEqual(appcst.shikijoIdoCol.toJSON(), appcst.orgShikijoIdoColJson);
        //  施行受付履歴イコール
        var sekoMemoEq = $.msiJqlib.isEqual(appcst.sekomemoCol.toJSON(), orgDataSekoMemoCol);
        //  訃報連絡イコール
        var huhoInfoEq = $.msiJqlib.isEqual(appcst.huhoInfoModel.toJSON(), appcst.orghuhoInfoModelJson);
        // 結果情報イコール
        var resultInfoEq = $.msiJqlib.isEqual(appcst.resultInfoModel.toJSON(), appcst.orgresultInfoModelJson);
        // 施行者カードイコール
        var sekoshaEq = $.msiJqlib.isEqual(appcst.sekoshaModel.toJSON(), appcst.orgsekoshaModelJson);

        if (sekoKihonEq && sekoNiteiEq && sekyuInfoEq && sekoGojokaiInfoEq && sekoGojokaiMemberEq
                && sekoKeiyakusakiInfoEq && hikitsugiEq && tehaiIraiEq && shikijoIdoEq && sekoKihonFreeEq
                && sekoMemoEq && huhoInfoEq && resultInfoEq && sekoshaEq) {
            changed = false;
        }
        return changed;
    };

});
