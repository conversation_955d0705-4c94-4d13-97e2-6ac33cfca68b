var appcst = appcst || {};
var appgjk = appgjk || {};
$(function () {

    var YOTO_COURSE = '1';
    var YOTO_PLAN = '2';
    var YOTO_SPECIAL = '3';
    var YOTO_NOUSE = '4';
    /** 利用者区分: 3=>その他 */
    var USER_KBN_OTHER = '3';   
    /** 会員情報区分: 1=>当社 */
    var KAIIN_INFO_MAIN = '1';  
    /** 会員情報区分: 2=>他社 */
    var KAIIN_INFO_OTHER = '2';  
    /** 会員情報区分: 3=>OM */
    var KAIIN_INFO_OM = '3';  
    /** ステータス: 3=>施行金額確定済 */
    var STATUS_SEKO_KAKUTEI = '3';
    /** 申込区分: 2=>法事 */
    var MOUSHI_KBN_HOJI = '2';
    /** 申込区分: 5=>事前相談 */
    var MOUSHI_KBN_JIZEN = '5';
    /** 申込区分: 19=>オーダーメイド */
    var MOUSHI_KBN_OM = '19';
    
    var _getGojokaiCoseMst = function (m) {
        var gojokaiCoseMst = null;
        var coseCd = m.get('course_snm_cd');
        if ($.msiJqlib.isNullEx2(coseCd)) {
            return gojokaiCoseMst;
        }
        _.each(appcst.data.gojokaiCouseMst, function (item, iw) {
            if (!$.msiJqlib.isNullEx2(iw) && coseCd === iw) {
                gojokaiCoseMst = item;
            }
        });
        return gojokaiCoseMst;
    };
    // appmodel用
    var _getGojokaiCoseMst2 = function (m) {
        var gojokaiCoseMst = null;
        var coseCd = m.get('use_cose');
        if ($.msiJqlib.isNullEx2(coseCd)) {
            return gojokaiCoseMst;
        }
        _.each(appcst.data.gojokaiCouseMst, function (item, iw) {
            if (!$.msiJqlib.isNullEx2(iw) && coseCd === iw) {
                gojokaiCoseMst = item;
            }
        });
        return gojokaiCoseMst;
    };
    var changeToNum = function (val) {
        var num = parseInt(val, 10);
        if (isFinite(num)) {
            return num;
        } else {
            num = 0;
            return num;
        }
    };
    
    // 区分値コード略称名設定処理
    var _setKbnSnmVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_snm);
        }
    };
    
    // 和暦の生年月日チェック処理
    var _validateSeinengappi = function (gengo, value) {
        if ($.msiJqlib.isNullEx2(value) || $.msiJqlib.isNullEx2(gengo)) {
            return '利用者生年月日が不正です';
        }
        var seinengappi = $.msiJqlib.warekiToseireki(gengo, value);
        if (!seinengappi) {
            return '利用者生年月日の形式エラーです';
        }
    };
    
    /**
     * @description 互助会タブ処理
     */
    // 施行契約先情報モデル
    appgjk.SekoKeiyakusakiInfoModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                partner_cd: null,
                user_kbn: null,
                user_kbn_nm: null,
                user_nm: null,
                user_last_nm: null,
                user_first_nm: null,
                user_knm: null,
                user_last_knm: null,
                user_first_knm: null,
                user_kinmusaki_nm: null,
                user_department_nm: null,
                kigou_no1: null,
                kigou_no2: null,
                ministries_cd: null,
                user_birth_year: null, // 生年月日(年)
                user_wa_year: null, // 生年月日(元号年)
                user_gengo: null, // 故人元号
                user_birth_month: null, // 生年月日(月)
                user_birth_day: null, // 生年月日(日)
                user_seinengappi_ymd: null, // 生年月日(和暦)
                user_seinengappi_ymd_y: null, // 生年月日(西暦)
            };
        },
        validation: {
            user_seinengappi_ymd: "validateSeinengappi",
        },
        labels: {
            user_seinengappi_ymd: '利用者生年月日',
        },
        validateSeinengappi: function (value, attr, computedState) {
            var gengo = computedState.user_gengo;
            var era = computedState.user_wa_year;
            var seireki = computedState.user_birth_year;
            var month = computedState.user_birth_month;
            var day = computedState.user_birth_day;
            var seinengappi = null;
            var wareki_y = null;
            if (!$.msiJqlib.isNullEx2(value)) {
                seinengappi = value;
            } else {
                if (!$.msiJqlib.isNullEx2(era) || !$.msiJqlib.isNullEx2(month) || !$.msiJqlib.isNullEx2(day)) {
                    if (!$.msiJqlib.isNullEx2(era)) {
                        var wareki_y = era.slice(1);
                    }
                    seinengappi = wareki_y + '/' + month + '/' + day;
                }
            }
            if (!$.msiJqlib.isNullEx2(seinengappi)) {
                return _validateSeinengappi(gengo, seinengappi);
            }
        },
    }); // SekoKeiyakusakiInfoModel

    // 施行契約先情報ビュー
    appgjk.SekoKeiyakusakiInfoView = Backbone.View.extend({
        el: $("#infomember-tab"),
        events: {
            "click .dlg_keiyaku_no": "keiyakuHelper",
            "select2-open #birthday_era": function () {
                var era = this.model.get('user_birth_year');
                if (!$.msiJqlib.isNullEx2(era)) {
                    return;
                }
                var choices = $('.select2-results').children('li');
                var index = choices.length - 70;
                var choice = $(choices[index]);
                choices.filter(".select2-highlighted").removeClass("select2-highlighted");
                choice = $(choices[index]);
                choice.addClass("select2-highlighted");
                // 自動スクロールさせる
                $('.select2-results').scrollTop(0 + (index * 25));

            },
            "change #birthday_era,#birthday_month,#birthday_day": "setUserBirth",
        },
        bindings: {
            '#partner_cd': 'partner_cd',
            '#partner_nm': 'partner_nm',
            '#user_kbn': {
                observe: 'user_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
            '#user_kbn_nm': 'user_kbn_nm',
            '#user_last_nm': 'user_last_nm',
            '#user_first_nm': 'user_first_nm',
            '#user_last_knm': 'user_last_knm',
            '#user_first_knm': 'user_first_knm',
            '#user_kinmusaki_nm': 'user_kinmusaki_nm',
            '#user_department_nm': 'user_department_nm',
            '#kigou_no1': 'kigou_no1',
            '#kigou_no2': 'kigou_no2',
            '#ministries_cd': {
                observe: 'ministries_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
            '#birthday_era': {
                observe: 'user_birth_year',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnSnmVal($el, this.model, 'user_wa_year');
                    return $el.val();
                }
            },
            '#birthday_month': {
                observe: 'user_birth_month',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
            '#birthday_day': {
                observe: 'user_birth_day',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.listenTo(this.model, 'change:user_kbn', this.setUserKbnStatus);
            this.render();
        },
        render: function () {
            this.stickit();
            // 利用者区分
            $.msiJqlib.setSelect2Com1(this.$("#user_kbn"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.user_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#ministries_cd"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.kyosai_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#birthday_era"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.gengo)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#birthday_month"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.month)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#birthday_day"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.day)}, $.msiJqlib.setSelect2Default1)));
            this.setUserKbnStatus();
            this.$('#member_1').hide();
            this.$('#member_2').show();
            this.$("#sougi_ymd").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            return this;
        },
        // 故人年齢計算処理
        setUserBirth: function () {
            var era = this.model.get('user_wa_year');
            var seireki = this.model.get('user_birth_year');
            var month = this.model.get('user_birth_month');
            var day = this.model.get('user_birth_day');
            if ($.msiJqlib.isNullEx2(era) || $.msiJqlib.isNullEx2(month) || $.msiJqlib.isNullEx2(day)) {
                this.model.set('user_gengo', null);
                this.model.set('user_seinengappi_ymd', null);
                this.model.set('user_seinengappi_ymd_y', null);
                return;
            }
            var gengo = era.slice(0, 1);
            var wareki_y = era.slice(1);
            var seinengappi = seireki + '/' + month + '/' + day;
            if (!$.msiJqlib.chkDate(seinengappi)) { // 日付チェック
                seinengappi = null;
            }
            this.model.set('user_gengo', gengo);
            this.model.set('user_seinengappi_ymd', wareki_y + '/' + month + '/' + day);
            this.model.set('user_seinengappi_ymd_y', seinengappi);
        },
        // 契約先情報 pickup
        keiyakuHelper: function() {
            // ステータスが施行金額確定以上は変更不可
            if (appcst.appModel.get('status_kbn') >= STATUS_SEKO_KAKUTEI) {
                return;
            }
            var m = this.model; 
            this.$el.msiPickHelper({
                action: '/mref/keiyakusakidlg', 
                _myId: '#msi-dialog',
                width: '94%',
                height: '94%',
                onSelect: function(data) {
//                    console.log(data);
                    m.set('partner_cd', data.code);
                    m.set('partner_nm', data.name);
                },
                onClear: function() {
                    m.set('partner_cd', null);
                    m.set('partner_nm', null);
                },
                hookSetData: function() {
                    return {
                        init_search: 0,
                        s_seko_kbn: 1,    // 1:施行
                        wari_kbn_hide_flg: 1, // 割引区分非表示フラグ 1：非表示
                        //s_est_shikijo_cd: appcst.appModel.get('est_shikijo_cd'),    // 見積式場
                    }
                },
            });
        },
        setUserKbnStatus: function() {
            var user_kbn = this.model.get('user_kbn');
            // 利用者区分がその他の時に非活性
            if (user_kbn == USER_KBN_OTHER) {
                $('#user_kbn_nm').removeAttr("disabled", "disabled");
            } else {
                $('#user_kbn_nm').attr("disabled", "disabled");
                this.model.set('user_kbn_nm',null);
            }
        }
    }); // SekokeiyakusakiInfoView

    /**
     * @description 互助会タブ処理
     */
    // 互助会基本情報モデル
    appgjk.GojokaiInfoModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                rireki_kbn: "0", // 施行履歴有無
                sodan_kbn: "0", // 事前相談有無
                kanyu_kakunin_kbn: "0", // お客様加入確認有無
                join_use_kbn: "0", // 加入団体利用有無
                spec_agent_cd: null, // 特約区分コード
                spec_agent_kbn: null, // 特約区分
                kanyu_dantai_cd: null, // 加入団体コード
                kanyu_dantai_kbn: null, // 加入団体区分
                kanyu_dantai_ext: null, // 加入団体（その他）
                hoyu_kbn: null, // 保有互助会有無
                kazoku_kbn: null, // 同居家族有無
                meigi_kbn: null, // 互助会名義区分
                info_kbn: null, // 名義変更説明有無
                riyu_memo: null, // 名義変更未説明理由
                plan_use_prc: null, // プラン利用金額
                plan_use_cnt: null, // プラン利用口数
                plan_use_kbn: "0", // プラン利用区分
                plan_change_prc: null, // プラン変更差額
                use_cose: null, // ご利用コース
                use_keiyaku_gaku: null, // ご利用コース契約金額
            };
        },
        validation: {
            kanyu_dantai_ext: {
                required: false,
                maxLength: 40
            },
            riyu_memo: {
                required: false,
                maxLength: 60
            },
        },
        labels: {
            kanyu_dantai_ext: '加入団体（その他）',
            riyu_memo: '名義変更未説明理由',
        }
    }); // GojokaiInfoModel

    // 互助会基本情報ビュー
    appgjk.GojokaiInfoView = Backbone.View.extend({
        el: $("#infomember-tab"),
        events: {
            "click #member_group_set #member_group_1": "doButton1Click",
            "click #member_group_set #member_group_2": "doButton2Click",
        },
        bindings: {
            '#spec_agent': {
                observe: 'spec_agent_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'spec_agent_kbn');
                    return $el.val();
                }
            },
            '#other_entry_group': {
                observe: 'kanyu_dantai_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'kanyu_dantai_kbn');
                    return $el.val();
                }
            },
            '#other_entry_name': 'kanyu_dantai_ext',
            '#account': {
                observe: 'meigi_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#change_reason': 'riyu_memo',
            '#plan_use_prc': {
                observe: 'plan_use_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#plan_use_cnt': {
                observe: 'plan_use_cnt',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#plan_change_prc': {
                observe: 'plan_change_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#use_cose': {
                observe: 'use_cose',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
            '#use_keiyaku_gaku': {
                observe: 'use_keiyaku_gaku',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.listenTo(appcst.gojokaiMemberCol, 'reset', this.addAllGojokaiCol);
            this.listenTo(this.model, 'change:kanyu_dantai_cd', this.setKanyuDantaiSonota);
            this.listenTo(this.model, 'change:plan_use_kbn', this.setPlanChange);
            this.listenTo(this.model, 'change:use_cose', this.setKeiyakuGaku);
            this.setKanyuDantaiSonota();
            this.render();
        },
        render: function () {
            this.stickit();
            // 互助会コース名（イニシャル）
            $.msiJqlib.setSelect2Com1(this.$("#use_cose"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.gojokai_cose)}, $.msiJqlib.setSelect2Default1)));
            // 加入団体
            $.msiJqlib.setSelect2Com1(this.$("#other_entry_group"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.kanyu_dantai_kbn)}, $.msiJqlib.setSelect2Default1)));
            // プラン変更を設定
            this.setPlanChange();
            return this;
        },
        setKeiyakuGaku: function () {
            var gojokaiCoseMst = _getGojokaiCoseMst2(this.model);
            if (!$.msiJqlib.isNullEx2(gojokaiCoseMst)) {
                this.model.set("use_keiyaku_gaku", gojokaiCoseMst.gojokai_prc);
            } else {
                this.model.set("use_keiyaku_gaku", null);
            }
        },
        addGojokaiOne: function (gojokai) {
            var v = new GojokaiMemberView({model: gojokai});
            this.$("#member_msi").append(v.render().el);
        },
        addAllGojokaiCol: function (collection) {
            var $memberDtl = this.$("#member_msi");
            $memberDtl.find('tbody').remove();
            collection.each(this.addGojokaiOne, this);
            /* チェックボックスのボタン化 */
            this.$('#member_dtl_div .radio_set').buttonset();
        },
        // 加入団体切り替え処理
        setKanyuDantaiSonota: function () {
            var val = this.model.get('kanyu_dantai_cd');
            // その他
            if (val === "99") {
                this.$("#other_entry_name").removeAttr("disabled");
            } else {
                this.model.set('kanyu_dantai_ext', null);
                this.$("#other_entry_name").attr("disabled", "disabled");
            }
            this.setKaiinsonota();
        },
        // 安心倶楽部の場合、かつ互助会加入情報がない場合、会員その他に「安心倶楽部」文言を表示する
        setKaiinsonota: function () {
            if ($.msiJqlib.isNullEx2(appcst.appModel)) {
                return;
            }
            var val = this.model.get('kanyu_dantai_cd');
            if (val === "06" || val === "07") {
                var data = $.msiJqlib.getSelect2Data(this.$("#other_entry_group"));
                if (!$.msiJqlib.isNullEx2(data)) {
                    appcst.appModel.set('kaiin_sonota', data.text);
                }
            } else {
                appcst.appModel.set('kaiin_sonota', null);
            }
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        setPlanChange: function () {
            if (this.model.get('plan_use_kbn') === '0') {
                this.$("#plan_change_prc").attr("disabled", "disabled");
                this.model.set('plan_change_prc', null);
            } else {
                this.$("#plan_change_prc").removeAttr("disabled");
            }
        },
        // 加入状況ボタンクリック処理
        doButton1Click: function () {
            var moushiKbn = appcst.appModel.get('moushi_kbn');
            this.hideSubTab();
            this.$('#member_1').show();
            if (moushiKbn == MOUSHI_KBN_OM) {
                this.$('#btn_gojokai_search').hide();
            } else {
                this.$('#btn_gojokai_search').show();
            }
        },
        // その他加入確認ボタンクリック処理
        doButton2Click: function () {
            this.hideSubTab();
            this.$('#member_2').show();
            this.$('#btn_gojokai_search').hide();
        },
        // サブタブ非表示処理
        hideSubTab: function () {
            this.$('.member_area').hide();
        }
    }); // GojokaiInfoView

    // 互助会タブ 施行互助会加入者モデル
    var GojokaiMemberModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                cif_no: null,
                kain_no: null, // 会員番号
                gojokai_cose_cd: null, // 互助会コースコード
                course_snm_cd: null, // コース
                gojokai_kbn: null, // 互助会区分
                kaiin_info_kbn: null, // 会員情報区分
                hunsitu_kbn: null, // 紛失区分
                kanyu_nm: null, // 加入者名
                kanyu_dt: null, // 加入年月日
                yoto_kbn: null, // 用途
                keiyaku_gaku: null, // 契約金額
                harai_no: null, // 払込回数
                harai_gaku: null, // 払込金額
                wari_gaku: null, // 割引額
                early_use_cost: null, // 早期利用費
                early_use_cost_disp: null, // 早期利用費
                early_use_cost_zei: null, // 早期利用費消費税
                early_use_cost_zei_cd: null, // 早期利用費消費税コード
                meigi_chg_cost: null, // 名義変更手数料
                meigi_chg_cost_disp: null, // 名義変更手数料
                meigi_chg_cost_zei: null, // 名義変更手数料消費税
                meigi_chg_cost_zei_cd: null, // 名義変更手数料消費税コード
                cur_cd: null, // 現況コード
                v_free10: null, 
                v_free11: null, 
                v_free12: null, 
                v_free13: null, 
                v_free14: null, 
                v_free15: null, 
                v_free16: null, 
                v_free17: null, 
                zei_cd: null, // 消費税コード
                delete_check: '0', // 削除
                shohizei_1: null, // 契約金額に対する消費税
                other_cose_nm: null, // 他社時のコース名
                waribiki_gaku: null, // 残一括割引額
                n_free3: null, // プレミアム割引額
                n_free4: null, // 完納後割増額
                n_free5: null, // 提供役務２
                wariken1: null, // 割引券１
                n_free6: null, // 割引額１
                wariken2: null, // 割引券２
                n_free7: null, // 割引額２
                wariken3: null, // 割引券３
                n_free8: null, // 割引額３
                kanyu_tax: null, // 会費消費税
                zan_gaku: null, // 会費消費税
            };
        },
        validation: {
            kaiin_info_kbn: {
                required: function () {
                    var yoto_kbn = this.get('yoto_kbn');
                    var kaiin_info_kbn = this.get('kaiin_info_kbn');
                    var other_cose_nm = this.get('other_cose_nm');
                    if (!$.msiJqlib.isNullEx2(yoto_kbn) && $.msiJqlib.isNullEx2(kaiin_info_kbn)) {
                        return true;
                    } else if (!$.msiJqlib.isNullEx2(other_cose_nm) && $.msiJqlib.isNullEx2(kaiin_info_kbn)) {
                        return true;
                    } else {
                        return this.hasCoseNm();
                    }
                },
            },
            course_snm_cd: function () {
                var coseCd = this.get('course_snm_cd');
                if ($.msiJqlib.isNullEx2(coseCd)) {
                    return;
                }
                var gojokaiCoseMst = _getGojokaiCoseMst(this);
                if (!$.msiJqlib.isNullEx2(gojokaiCoseMst)) {
                    this.set("gojokai_cose_cd", gojokaiCoseMst.gojokai_cose_cd);
                    return;
                }
                return 'コース番号が不正です。';
            },
            kain_no: {
                required: function () {
                    return this.hasCoseNm();
                },
                maxLength: 40
            },
            kanyu_nm: {
                required: function () {
                    return this.hasCoseNm();
                },
                maxLength: 30
            },
            yoto_kbn: {
                required: function () {
                    return this.hasCoseNm();
                },
                customFun: function () {
                    var dataYotoCourse = appcst.gojokaiMemberCol.where({yoto_kbn: YOTO_COURSE, delete_check: '0'});
                    var dataYotoPlan = appcst.gojokaiMemberCol.where({yoto_kbn: YOTO_PLAN, delete_check: '0'});
                    var dataYotoSpecial = appcst.gojokaiMemberCol.where({yoto_kbn: YOTO_SPECIAL, delete_check: '0'});
                    if (dataYotoCourse.length >= 1 && dataYotoSpecial.length >= 1) {
                        return 'コース施行と特別施行が重複しています';
                    }
                    if (dataYotoCourse.length >= 1 && dataYotoPlan.length >= 1) {
                        return 'コース施行と会費充当が重複しています';
                    }
                    if (dataYotoPlan.length >= 1 && dataYotoSpecial.length >= 1) {
                        return '特別施行と会費充当が重複しています';
                    }
                    return;
                }
            },
            keiyaku_gaku: {
                required: function () {
                    return this.hasCoseNm();
                },
                pattern: 'number',
                max: 9999999999
            },
            harai_gaku: {
                required: function () {
                    return this.hasCoseNm();
                },
                pattern: 'number',
                max: 9999999999
            },
            harai_no: {
                required: function () {
                    return this.hasCoseNm();
                },
                pattern: 'number',
                max: 99999
            },
//            wari_gaku: {
//                required: function () {
//                    return this.hasCoseNm();
//                },
//                pattern: 'number',
//                max: 99999
//            },
//            early_use_cost_disp: {
//                required: function () {
//                    return this.hasCoseNm();
//                },
//                pattern: 'number',
//                max: 9999999999
//            },
            meigi_chg_cost_disp: {
                required: function () {
                    return this.hasCoseNm();
                },
                pattern: 'number',
                max: 9999999999
            },
            kanyu_dt: {
                required: function () {
                    return this.hasCoseNm();
                },
                customFun: function (value) {
                    return $.msiJqlib.chkYmd(value);
                }
            },
            zei_kijyn_ymd: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
//            waribiki_gaku: {
//                required: function () {
//                    return this.hasCoseNm();
//                },
//                pattern: 'number',
//                max: 9999999999
//            },
//            n_free3: {
//                required: function () {
//                    return this.hasCoseNm();
//                },
//                pattern: 'number',
//                max: 9999999999
//            },
//            n_free4: {
//                required: function () {
//                    return this.hasCoseNm();
//                },
//                pattern: 'number',
//                max: 9999999999
//            },
            n_free5: {
                required: function () {
                    return this.chkCouse(YOTO_PLAN);
                },
                pattern: 'number',
                max: 0
            },
            n_free6: {
                required: function () {
                    return this.chkWariken("wariken1");
                },
                pattern: 'number',
                max: 0
            },
            n_free7: {
                required: function () {
                    return this.chkWariken("wariken2");
                },
                pattern: 'number',
                max: 0
            },
            n_free8: {
                required: function () {
                    return this.chkWariken("wariken3");
                },
                pattern: 'number',
                max: 0
            },
            kanyu_tax: {
                required: function () {
                    return this.hasCoseNm();
                },
                pattern: 'number',
                max: 9999999999
            },
//            zan_gaku: {
//                required: function () {
//                    return this.hasCoseNm();
//                },
//                pattern: 'number',
//                max: 9999999999
//            },
        },
        // コースが入力されたら、他の項目も必須入力になる
        hasCoseNm: function () {
            if (!$.msiJqlib.isNullEx2(this.get("kaiin_info_kbn")) && this.get('kaiin_info_kbn') == KAIIN_INFO_OTHER) {
                if ($.msiJqlib.isNullEx2(this.get("other_cose_nm"))) {
                    return false;
                } else {
                    return true;
                }
            } else {
                if ($.msiJqlib.isNullEx2(this.get("course_snm_cd"))) {
                    return false;
                } else {
                    return true;
                }
            }
        },
        chkCouse: function (course) {
            
            var yoto_kbn = this.get("yoto_kbn");
            if (yoto_kbn === course) {
                return true;
            } else {
                return false;
            }
        },
        chkWariken: function (wariken) {
            
            var wariken_cd = this.get(wariken);
            if (!$.msiJqlib.isNullEx2(wariken_cd)) {
                return true;
            } else {
                return false;
            }
        },
        labels: {
            kaiin_info_kbn: '区分',
            course_snm_cd: 'コース',
            apply_no: '申込番号',
            kain_no: '加入者番号',
            kanyu_nm: '加入者名',
            yoto_kbn: '用途',
            keiyaku_gaku: '契約金額',
            harai_gaku: '掛込金額',
            harai_no: '掛込回数',
//            wari_gaku: '割引金額',
            use_prc: '利用金額',
//            early_use_cost_disp: '早期利用費',
            meigi_chg_cost_disp: '名義変更手数料',
            kanyu_dt: '加入年月日',
            zei_kijyn_ymd: '消費税基準日',
//            waribiki_gaku: '残一括割引額',
//            n_free3: 'プレミアム割引額',
//            n_free4: '完納後割増額',
            n_free5: '提供役務２',
            n_free6: '割引額１',
            n_free7: '割引額２',
            n_free8: '割引額３',
            kanyu_tax: '会費消費税',
            zan_gaku: '掛込残額',
        }
    }); // GojokaiMemberModel

    // 施行互助会加入者コレクション
    appgjk.GojokaiMemberCollection = Backbone.Collection.extend({
        model: GojokaiMemberModel
    });

    // 施行互助会加入者ビュー
    var GojokaiMemberView = Backbone.View.extend({
        tagName: 'tbody',
        tmpl: _.template($('#tmpl-kanyu').html()),
        events: {
            "change .i_member_delete": function () {
                // "証書チェックボックス値を設定
                var val = this.$('.i_member_delete:checked').val();
                this.model.set('delete_check', val === "1" ? "1" : "0");
            },
            "select2-opening .i_kaiin_info": function () {
                // 開いた場合は他社のみ表示
                var orgKbns = $.msiJqlib.objToArray3(appcst.data.dataKbns.kaiin_info_kbn);
                var fileredKbns = [];
                _.each(orgKbns, function (item) {
                    if (item.kbn_value_cd_num == KAIIN_INFO_OTHER) {
                        fileredKbns.push(item);
                    }
                });
                appcst.kaiin_info_kbns = fileredKbns;
            },
        },
        bindings: {
            '.i_kaiin_info': {
                observe: 'kaiin_info_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.i_hunsitu': {
                observe: 'hunsitu_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.i_cose': {
                observe: 'course_snm_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.i_member_id': 'kain_no',
            '.i_apply_no': 'apply_no',
            '.i_member_name': 'kanyu_nm',
            '.i_usage': {
                observe: 'yoto_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.i_deposit': {
                observe: 'keiyaku_gaku',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_pay': {
                observe: 'harai_gaku',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_times': {
                observe: 'harai_no',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_wari_gaku': {
                observe: 'wari_gaku',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_use_prc': {
                observe: 'use_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
//            '.i_early': {
//                observe: 'early_use_cost_disp',
//                onSet: 'commaOmit',
//                onGet: 'commaAdd'
//            },
            '.i_mg_chg_cost': {
                observe: 'meigi_chg_cost_disp',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_entry': 'kanyu_dt',
            '.i_cur_cd': {
                observe: 'cur_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.i_zei_cd': {
                observe: 'zei_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.zei_1': {
                observe: 'shohizei_1',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_other_cose_nm': 'other_cose_nm',
            '.i_waribiki_gaku': {
                observe: 'waribiki_gaku',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_premium_gaku': {
                observe: 'n_free3',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_kannnou_gaku': {
                observe: 'n_free4',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_ekimu2': {
                observe: 'n_free5',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_kanyu_tax': {
                observe: 'kanyu_tax',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_zan_prc': {
                observe: 'zan_gaku',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_zan_prc': {
                observe: 'zan_gaku',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_wariken1': {
                observe: 'wariken1',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.i_wariken2': {
                observe: 'wariken2',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.i_wariken3': {
                observe: 'wariken3',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.i_wari1': {
                observe: 'n_free6',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_wari2': {
                observe: 'n_free7',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_wari3': {
                observe: 'n_free8',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '.i_botan1': {
                observe: 'v_free10',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '.i_botan2': {
                observe: 'v_free11',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.listenTo(this.model, 'change:keiyaku_gaku change:harai_gaku change:harai_no change:wari_gaku change:cose_chg_gaku change:early_use_cost change:meigi_chg_cost', this.toggleClass);
            this.listenTo(this.model, 'change:waribiki_gaku change:n_free3 change:n_free4 change:n_free5 change:n_free6 change:n_free7 change:n_free8 change:kanyu_tax', this.toggleClass);
            this.listenTo(this.model, 'change:keiyaku_gaku change:yoto_kbn change:kanyu_dt_gen change:kanyu_dt', this.setShohizei1);
//            this.listenTo(this.model, 'change:early_use_cost_disp', this.setEarlyUseZei);
            this.listenTo(this.model, 'change:meigi_chg_cost_disp', this.setMeigiChgZei);
//            this.listenTo(this.model, 'change:zei_cd change:yoto_kbn', this.setKakeSagakuZei);
            this.listenTo(this.model, 'change:harai_gaku change:wari_gaku', this.setUsePrc);
            this.listenTo(this.model, 'change:course_snm_cd change:yoto_kbn', this.setUseCose);
            this.listenTo(this.model, 'change:kaiin_info_kbn', this.setCoseField);
            this.listenTo(this.model, 'change:keiyaku_gaku change:harai_gaku', this.setZanPrc);
            this.listenTo(this.model, 'change:keiyaku_gaku change:zei_cd', this.setKanyuTax);
            this.listenTo(this.model, 'change:yoto_kbn', this.changeYoto);
            this.listenTo(this.model, 'change:wariken1 change:wariken2 change:wariken3', this.changeWariken);
            this.render();
        },
        render: function () {
            this.$el.html(this.tmpl({idx: this.model.cid}));

            this.$(".i_entry").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.stickit();
            this.toggleClass();
            // 会員情報区分
            appcst.kaiin_info_kbns = $.msiJqlib.objToArray3(appcst.data.dataKbns.kaiin_info_kbn);
            $.msiJqlib.setSelect2Com1(this.$(".i_kaiin_info"), ($.extend({data: function () {
                    return {results: appcst.kaiin_info_kbns};
                }}, $.msiJqlib.setSelect2Default1)));
            // 紛失区分
            $.msiJqlib.setSelect2Com1(this.$(".i_hunsitu"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.hunsitu_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 割引券
            $.msiJqlib.setSelect2Com1(this.$(".i_wariken1"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.wariken_mst)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$(".i_wariken2"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.wariken_mst)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$(".i_wariken3"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.wariken_mst)}, $.msiJqlib.setSelect2Default1)));
            // 募集担当
            $.msiJqlib.setSelect2Com1(this.$(".i_botan1"), ($.extend({data: appcst.data.dataKbns.tanto_mst}, $.msiJqlib.setSelect2Default2)));
            $.msiJqlib.setSelect2Com1(this.$(".i_botan2"), ($.extend({data: appcst.data.dataKbns.tanto_mst}, $.msiJqlib.setSelect2Default2)));
            // 互助会コース名（イニシャル）
            $.msiJqlib.setSelect2Com1(this.$(".i_cose"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.gojokai_cose)}, $.msiJqlib.setSelect2Default1)));
            // 用途
            if (appcst.appModel.get('moushi_kbn') === MOUSHI_KBN_OM) {
                appcst.yoto_kbns = $.msiJqlib.objToArray3(appcst.data.dataKbns.yoto_kbn_OM);
            } else {
                appcst.yoto_kbns = $.msiJqlib.objToArray3(appcst.data.dataKbns.yoto_kbn);
            }
            if (appcst.appModel.get('moushi_kbn') == MOUSHI_KBN_JIZEN) {
                $('.gojo_kijun_ymd').show();
            } else {
                $('.gojo_kijun_ymd').hide();
            }
            if (appcst.appModel.get('moushi_kbn') == MOUSHI_KBN_HOJI) {
                $('.lbl_member_group_2').hide();
            } else {
                $('.lbl_member_group_2').show();
            }MOUSHI_KBN_HOJI
            $.msiJqlib.setSelect2Com1(this.$(".i_usage"), ($.extend({data: function () {
                    return {results: appcst.yoto_kbns};
                }}, $.msiJqlib.setSelect2Default1)));
            // 税コード
            $.msiJqlib.setSelect2Com1(this.$(".i_zei_cd"), ($.extend({data: appcst.data.dataKbns.zei_cd}, $.msiJqlib.setSelect2Default1)));
            // 現況コード
            $.msiJqlib.setSelect2Com1(this.$(".i_cur_cd"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.genkyo_cd)}, $.msiJqlib.setSelect2Default1)));

            this.setShohizei1();
            this.setUsePrc();
            this.setCoseField();
            this.changeYoto();
            this.changeWariken();
            // 名変がNULLの場合 0円を設定
            if ((!$.msiJqlib.isNullEx2(this.model.get('course_snm_cd')) || !$.msiJqlib.isNullEx2(this.model.get('other_cose_nm'))) && $.msiJqlib.isNullEx2(this.model.get('meigi_chg_cost'))) {
                this.model.set('meigi_chg_cost', '0');
            }
            // 早期利用費がNULLの場合 0円を設定
            if ((!$.msiJqlib.isNullEx2(this.model.get('course_snm_cd')) || !$.msiJqlib.isNullEx2(this.model.get('other_cose_nm'))) && $.msiJqlib.isNullEx2(this.model.get('early_use_cost'))) {
                this.model.set('early_use_cost', '0');
            }
            return this;
        },
        // マイナスのときに赤字クラスを追加する処理
        toggleClass: function (e) {
            appcst.toggleAkajiClass(this, ['keiyaku_gaku', 'harai_gaku', 'harai_no', 'wari_gaku', 'cose_chg_gaku', 'early_use_cost', 'meigi_chg_cost', 'use_prc']);
            appcst.toggleAkajiClass(this, ['n_free5', 'n_free6', 'n_free7', 'n_free8']);
        },
        setCoseField: function () {
            // 会員区分がOMの場合は全て非活性
            if (appcst.appModel.get('moushi_kbn') === MOUSHI_KBN_OM) {
                this.$('.i_other_cose_nm').hide();
                this.$('.i_cose').show();
                this.$('.i_kaiin_info').attr('disabled', 'disabled');
                this.$('.i_cose').attr('disabled', 'disabled');
                this.$('.i_member_id').attr('disabled', 'disabled');
                this.$('.i_member_name').attr('disabled', 'disabled');
                this.$('.i_entry').attr('disabled', 'disabled');
                this.$('.i_usage').attr('disabled', 'disabled');
                this.$('.i_deposit').attr('disabled', 'disabled');
                this.$('.i_times').attr('disabled', 'disabled');
                this.$('.i_pay').attr('disabled', 'disabled');
                this.$('.i_wari_gaku').attr('disabled', 'disabled');
                this.$('.i_early').attr('disabled', 'disabled');
                this.$('.i_mg_chg_cost').attr('disabled', 'disabled');
                this.$('.i_cur_cd').attr('disabled', 'disabled');
                this.$('.i_zei_cd').attr('disabled', 'disabled');
                this.$('.i_premium_gaku').attr('disabled', 'disabled');
                this.$('.i_zan_prc').attr('disabled', 'disabled');
                this.$('.i_waribiki_gaku').attr('disabled', 'disabled');
                this.$('.i_kannnou_gaku').attr('disabled', 'disabled');
                this.$('.i_kanyu_tax').attr('disabled', 'disabled');
                this.$('.dlg_date').hide();
                this.$('.dlg_dummy_date').show();
            } else {
                // 区分が他社とそれ以外で入力フィールドを変更する
                if (!$.msiJqlib.isNullEx2(this.model.get('kaiin_info_kbn')) && this.model.get('kaiin_info_kbn') == KAIIN_INFO_MAIN || this.model.get('kaiin_info_kbn') == KAIIN_INFO_OM) {
                    this.$('.i_other_cose_nm').hide();
                    this.$('.i_cose').show();
                    this.$('.i_kaiin_info').attr('disabled', 'disabled');
                    this.$('.i_cose').attr('disabled', 'disabled');
                    this.$('.i_member_id').attr('disabled', 'disabled');
                    this.$('.i_member_name').attr('disabled', 'disabled');
                    this.$('.i_entry').attr('disabled', 'disabled');
                    this.$('.i_deposit').attr('disabled', 'disabled');
//                    this.$('.i_times').attr('disabled', 'disabled');
                    this.$('.i_times').removeAttr('disabled', 'disabled');
//                    this.$('.i_pay').attr('disabled', 'disabled');
                    this.$('.i_pay').removeAttr('disabled', 'disabled');
//                    this.$('.i_wari_gaku').attr('disabled', 'disabled');
                    this.$('.i_wari_gaku').removeAttr('disabled', 'disabled');
//                    this.$('.i_early').attr('disabled', 'disabled');
                    this.$('.i_early').removeAttr('disabled', 'disabled');
//                    this.$('.i_mg_chg_cost').attr('disabled', 'disabled');
                    this.$('.i_mg_chg_cost').removeAttr('disabled', 'disabled');
                    this.$('.i_cur_cd').attr('disabled', 'disabled');
//                    this.$('.i_zei_cd').attr('disabled', 'disabled');
                    this.$('.i_zei_cd').removeAttr('disabled', 'disabled');
//                    this.$('.i_premium_gaku').attr('disabled', 'disabled');
                    this.$('.i_premium_gaku').removeAttr('disabled', 'disabled');
                    this.$('.i_zan_prc').attr('disabled', 'disabled');
//                    this.$('.i_waribiki_gaku').attr('disabled', 'disabled');
                    this.$('.i_waribiki_gaku').removeAttr('disabled', 'disabled');
//                    this.$('.i_kannnou_gaku').attr('disabled', 'disabled');
                    this.$('.i_kannnou_gaku').removeAttr('disabled', 'disabled');
//                    this.$('.i_kanyu_tax').attr('disabled', 'disabled');
                    this.$('.i_kanyu_tax').removeAttr('disabled', 'disabled');
                    this.$('.dlg_date').hide();
                    this.$('.dlg_dummy_date').show();
                } else {
                    this.$('.i_other_cose_nm').show();
                    this.$('.i_cose').hide();
                    this.$('.i_kaiin_info').removeAttr('disabled');
                    this.$('.i_cose').removeAttr('disabled');
                    this.$('.i_member_id').removeAttr('disabled');
                    this.$('.i_member_name').removeAttr('disabled');
                    this.$('.i_entry').removeAttr('disabled');
                    this.$('.i_deposit').removeAttr('disabled');
                    this.$('.i_times').removeAttr('disabled');
                    this.$('.i_pay').removeAttr('disabled');
                    this.$('.i_wari_gaku').removeAttr('disabled');
                    this.$('.i_early').removeAttr('disabled');
                    this.$('.i_mg_chg_cost').removeAttr('disabled');
                    this.$('.i_cur_cd').removeAttr('disabled');
                    this.$('.i_zei_cd').removeAttr('disabled');
                    this.$('.dlg_date').show();
                    this.$('.dlg_dummy_date').hide();
                }
            }
        },
        setKeiyakuGaku: function () {
//            var gojokaiCoseMst = _getGojokaiCoseMst(this.model);
//            if (!$.msiJqlib.isNullEx2(gojokaiCoseMst)) {
//                this.model.set("keiyaku_gaku", gojokaiCoseMst.gojokai_prc);
//                this.model.set("early_use_cost", 0);
//                this.model.set("meigi_chg_cost", 0);
//                this.model.set("manki_no", gojokaiCoseMst.harai_yotei_cnt);
//            } else {
//                this.model.set("keiyaku_gaku", null);
//                this.model.set("early_use_cost", null);
//                this.model.set("meigi_chg_cost", null);
//                this.model.set("manki_no", null);
//            }
        },
        setShohizei1: function () {
            var taxInfo = appcst.data.taxInfoAll;
            var zei = null;
            var yoto_kbn = this.model.get("yoto_kbn");
            var keiyaku_gaku = this.model.get("keiyaku_gaku");
            var kanyu_dt = this.model.get("kanyu_dt");
            if (!$.msiJqlib.isNullEx2(taxInfo) && !$.msiJqlib.isNullEx2(keiyaku_gaku)
                    && !$.msiJqlib.isNullEx2(kanyu_dt)) {
                var dt = $.msiJqlib.chkDate(kanyu_dt);
                if (dt) {
                    var zeirtu = 0;
                    var zei_hasu_kbn = 0;
                    _.each(taxInfo, function (item) {
                        var st = new Date(item.tekiyo_st_date);
                        var ed = new Date(item.tekiyo_ed_date);
                        var kijun = new Date(kanyu_dt);
                        if (st <= kijun && kijun <= ed) {
                            zeirtu = item.zei_rtu;
                            zei_hasu_kbn = item.zei_hasu_kbn;
                        }
                    });
                    if (yoto_kbn !== '12') {
                        zei = $.msiJqlib.round(keiyaku_gaku * zeirtu / 100, zei_hasu_kbn);
                    } else {
                        zei = 0;
                    }
                }
            }
            this.model.set("shohizei_1", zei);
        },
        setEarlyUseZei: function () {
            var taxInfo = appcst.data.taxInfo;
            var zei = null;
            var zei_cd = null;
            var early_use_cost = this.model.get("early_use_cost_disp");
            if (!$.msiJqlib.isNullEx2(taxInfo) && !$.msiJqlib.isNullEx2(early_use_cost)) {
                zei = $.msiJqlib.round(early_use_cost * taxInfo.zei_rtu / 100, taxInfo.zei_hasu_kbn);
                zei_cd = taxInfo.zei_cd;
            }
//            this.model.set("early_use_cost_zei", zei);
            this.model.set("early_use_cost_zei_cd", zei_cd);
        },
        setMeigiChgZei: function () {
            var taxInfo = appcst.data.taxInfo;
            var zei = null;
            var zei_cd = null;
            var meigi_chg_cost = this.model.get("meigi_chg_cost_disp");
            if (!$.msiJqlib.isNullEx2(taxInfo) && !$.msiJqlib.isNullEx2(meigi_chg_cost)) {
                zei = $.msiJqlib.round(meigi_chg_cost * taxInfo.zei_rtu / 100, taxInfo.zei_hasu_kbn);
                zei_cd = taxInfo.zei_cd;
            }
//            this.model.set("meigi_chg_cost_zei", zei);
            this.model.set("meigi_chg_cost_zei_cd", zei_cd);
        },
        setKakeSagakuZei: function () {
            // 用途区分がコース施行以外は差額の税率を0に設定
            var taxInfo = appcst.data.taxInfo;
            var taxInfoAll = appcst.data.taxInfoAll;
            var zei_cd = this.model.get("zei_cd");
            var yoto_kbn = this.model.get("yoto_kbn");
            var sagaku = 0;
            if (yoto_kbn == YOTO_COURSE) {
                var zei_rtu = null;
                _.each(taxInfoAll, function (item) {
                    if (zei_cd == item.zei_cd) {
                        zei_rtu = item.zei_rtu;
                    }
                });
                sagaku = taxInfo.zei_rtu - zei_rtu;   
            }
            this.model.set("kake_zei_rtu", sagaku);
        },
        setUsePrc: function () {
            var harai_gaku = changeToNum(this.model.get('harai_gaku'));
            var wari_gaku = changeToNum(this.model.get('wari_gaku'));
            this.model.set('use_prc', harai_gaku + wari_gaku);
        },
        setUseCose: function () {
            var memberModel = appcst.gojokaiMemberCol.models;
            var yoto_cnt = 0;
            _.each(memberModel, function (item) {
                // 用途区分がコース施行またはプラン施行のときはご利用コースを設定する
                if (item.get('yoto_kbn') == YOTO_COURSE || item.get('yoto_kbn') == YOTO_PLAN) {
                    yoto_cnt++;
                    if ($.msiJqlib.isNullEx2(appcst.gojokaiInfoModel.get('use_cose')) && !$.msiJqlib.isNullEx2(item.get('course_snm_cd'))) {
                        appcst.gojokaiInfoModel.set('use_cose', item.get('course_snm_cd'));
                    }
                }
            });
            // 用途がコース施行とプラン施行以外のみの場合はご利用コースを未設定にする
            if (yoto_cnt == 0) {
                appcst.gojokaiInfoModel.set('use_cose', null);
            }
            
        },
        changeYoto: function () {
            var yoto_kbn = this.model.get("yoto_kbn");
            // 用途区分が会費充当のとき、役務２を入力できる
            if (yoto_kbn === YOTO_PLAN) {
                this.$('.i_ekimu2').removeAttr('disabled');
            } else {
                this.$('.i_ekimu2').attr('disabled', 'disabled');
                this.model.set('n_free5', null);
            }
            if (!$.msiJqlib.isNullEx2(yoto_kbn) && yoto_kbn !== YOTO_COURSE) {
                this.model.set('zei_cd', '0');
                this.$('.i_zei_cd').attr('disabled', 'disabled');
            } else {
                this.$('.i_zei_cd').removeAttr('disabled');
            }
            
        },
        changeWariken: function () {
            this.seteWariken("wariken1", ".i_wari1", "n_free6");
            this.seteWariken("wariken2", ".i_wari2", "n_free7");
            this.seteWariken("wariken3", ".i_wari3", "n_free8");
            
        },
        seteWariken: function (wariken, i_wari, warigaku) {
            var wariken_cd = this.model.get(wariken);
            if (!$.msiJqlib.isNullEx2(wariken_cd)) {
                this.$(i_wari).removeAttr('disabled');
            } else {
                this.$(i_wari).attr('disabled', 'disabled');
                this.model.set(warigaku, null);
            }
            
        },
        setZanPrc: function () {
            var keiyaku_gaku = this.model.get("keiyaku_gaku");
            var harai_gaku = changeToNum(this.model.get('harai_gaku'));
            this.model.set('zan_gaku', keiyaku_gaku - harai_gaku);
        },
        setKanyuTax: function () {
            var keiyaku_gaku = this.model.get("keiyaku_gaku");            
            var taxInfoAll = appcst.data.taxInfoAll;
            var zei_cd = this.model.get("zei_cd");
            var zei_rtu = null;
            _.each(taxInfoAll, function (item) {
                if (zei_cd === item.zei_cd) {
                    zei_rtu = item.zei_rtu;
                }
            });
            this.model.set('kanyu_tax', keiyaku_gaku * zei_rtu / 100);
        },
    }); // GojokaiMemberView
});
