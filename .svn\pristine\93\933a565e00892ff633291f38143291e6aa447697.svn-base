<?php

/**
 * Juchu_JuchuCustomerinfoCom
 *
 * 葬儀と法事のお客様情報共通クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuCustomerinfoCom
 * <AUTHOR> Tosaka
 * @since      2020/02/XX
 * @filesource 
 */

/**
 * 葬儀と法事のお客様情報共通クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuCustomerinfoCom
 * <AUTHOR> Tosaka
 * @since      2020/02/XX
 */
abstract class Juchu_JuchuCustomerinfoCom extends Juchu_JuchuhenkoAbstract {

    /** コード区分: 0010=>申込区分 */
    const CODE_KBN_MOUSHI_KBN = '0010';

    /** コード区分: 0020=>葬儀区分 */
    const CODE_KBN_SOUGI_KBN = '0020';

    /** コード区分: 0030=>会員区分 */
    const CODE_KBN_KAIIN_KBN = '0030';

    /** コード区分: 0070=>配偶者 */
    const CODE_KBN_HAIGU_KBN = '0070';

    /** コード区分: 0080=>性別 */
    const CODE_KBN_SEX_KBN = '0080';

    /** コード区分: 0440=>元号 */
    const CODE_KBN_GENGO = '0440';

    /** コード区分: 0040=>葬儀形式 */
    const CODE_KBN_KEISHIKI_KBN = '0040';

    /** コード区分: 0450=>用途 */
    const CODE_KBN_YOTO_KBN = '0450';

    /** コード区分: 0220=>搬送業務区分 */
    const CODE_KBN_HS_GYOMU_KBN = '0220';

    /** コード区分: 0200=>宗派区分 */
    const CODE_KBN_SYUHA_KBN = '0200';

    /** コード区分: 0240=>宗旨区分 */
    const CODE_KBN_SYUSHI_KBN = '0240';

    /** コード区分: 0190=>続柄区分 */
    const CODE_KBN_ZOKU_KBN = '0190';

    /** コード区分: 0460=>日程区分(葬儀) */
    const CODE_KBN_NITEI_SOUGI = '0460';

    /** コード区分: 0670=>葬儀場所 */
    const CODE_SOGI_BASHO_KBN = '0670';

    /** コード区分: 0990=>申込区分(葬儀) */
    const CODE_KBN_MOUSHI_KBN_S = '0990';

    /** コード区分: 1000=>申込区分(法事) */
    const CODE_KBN_MOUSHI_KBN_H = '1000';

    /** コード区分: 7988=>申込区分(事前相談) */
    const CODE_KBN_MOUSHI_KBN_J = '7988';
    
    /** コード区分: 0600=>安置場所 */
    const HS_ANCHI_CD = '0600';

    /** コード区分: 1190=>個人情報保護 */
    const CODE_P_INFO = '1190';

    /** コード区分: 1370=>写真預かり */
    const CODE_KBN_PORTRAIT = '1370';

    /** コード区分: 1490=>用途法事 */
    const CODE_KBN_YOTO_HOUJI = '1490';

    /** コード区分: 1580=>相談内容 */
    const CODE_KBN_CONTENT1 = '1580';

    /** コード区分: 1590=>確認内容 */
    const CODE_KBN_CONTENT2 = '1590';

    /** コード区分: 1600=>××がない場合 */
    const CODE_KBN_IF_NASHA = '1600';

    /** コード区分: 1610=>互助会コース */
    const CODE_GOJOKAI_COSE = '1610';

    /** コード区分: 1830=>菩提寺紹介者 */
    const CODE_KBN_TERA_SHOKAI = '7782';

    /** コード区分: 2010=>施行場所 */
    const CODE_KBN_SEKO_BASHO = '2010';

    /** コード区分: 0280：有無区分 */
    const CODE_KBN_UMU = '0280';

    /** コード区分: 4030=>戒名 */
    const CODE_KBN_KAINAME1_KBN = '4030';

    /** コード区分: 4430=>お迎え区分 */
    const CODE_KBN_OMUKAE = '4430';

    /** コード区分: 7722=>検索結果区分 */
    const CODE_KBN_CIF_STATUS = '7722';

    /** コード区分: 7768=>勤務先区分(故人) */
    const CODE_KBN_KINMU_K = '7768';

    /** コード区分: 7770=>勤務先区分(喪主) */
    const CODE_KBN_KINMU_M = '7770';

    /** コード区分: 7772=>ペースメーカー区分 */
    const CODE_KBN_PACEMAKER = '7772';

    /** コード区分: 7774=>感染症区分 */
    const CODE_KBN_INFECTION = '7774';

    /** コード区分: 7776=>ステータス区分 */
    const CODE_KBN_STATUS = '7776';

    /** コード区分: 7778=>重要度区分 */
    const CODE_KBN_CATEGORY = '7778';

    /** コード区分: 7784=>弔意香典 */
    const CODE_KBN_CHOI_KODEN = '7784';

    /** コード区分: 7786=>弔意供花供物 */
    const CODE_KBN_CHOI_KUMOTU = '7786';

    /** コード区分: 7802=>会員情報区分 */
    const CODE_KBN_KAIIN_INFO = '7802';

    /** コード区分: 7788=>供花可否区分 */
    const CODE_KBN_FA_SEIKA = '7788';

    /** コード区分: 7790=>樒可否区分 */
    const CODE_KBN_FA_SIKIMI = '7790';

    /** コード区分: 7800=>供花問い合わせ区分 */
    const CODE_KBN_SEIKA_FORM = '7800';

    /** コード区分: 7792=>榊可否区分 */
    const CODE_KBN_FA_SAKAKI = '7792';

    /** コード区分: 7794=>花環可否区分 */
    const CODE_KBN_FA_HANAWA = '7794';

    /** コード区分: 7796=>果物可否区分 */
    const CODE_KBN_FA_KUDAMONO = '7796';

    /** コード区分: 7798=>缶詰可否区分 */
    const CODE_KBN_FA_CAN = '7798';

    /** コード区分: 7824=>利用者区分 */
    const CODE_KBN_USER = '7824';

    /** コード区分: 7833=>共済組合区分 */
    const CODE_KBN_KYOSAI = '7833';

    /** コード区分: 7824=>更新履歴項目区分 */
    const CODE_KBN_CONTENTS = '2540';

    /** コード区分: 7931=>事前問合せ区分 */
    const CODE_KBN_JIZEN_CONTACT = '7931';

    /** コード区分: 7932=>事前問合せ手段区分 */
    const CODE_KBN_JIZEN_CONTACTWAY = '7932';

    /** コード区分: 7933=>事前問合せ経緯区分 */
    const CODE_KBN_JIZEN_CONTACTPROCESS = '7933';

    /** コード区分: 7934=>事前問合せ状況区分 */
    const CODE_KBN_JIZEN_CONTACTSTATUS = '7934';

    /** コード区分: 7935=>事前全体進捗区分 */
    const CODE_KBN_JIZEN_PROGRESS = '7935';

    /** コード区分: 7937=>事前成約状況 */
    const CODE_KBN_JIZEN_SEIYAKU = '7937';

    /** コード区分: 7939=>応対区分 */
    const CODE_KBN_OUTAI = '7939';

    /** コード区分: 7940=>応対方法区分 */
    const CODE_KBN_OUTAI_METHOD = '7940';

    /** コード区分: 8160=>金額統一区分 */
    const CODE_KBN_PRC_UNITY = '8160';

    /** コード区分: 8190=>現況コード */
    const CODE_KBN_GENKYO = '8190';

    /** コード区分: 7880=>互助会区分(値引き) */
    const CODE_KBN_KEIYAKUSAKI_WARI_KBN = '7880';

    /** コード区分: 8572=>用途区分OM */
    const CODE_KBN_YOTO_KBN_OM = '8572';

    /** コード区分: 8576=>故人年齢表記(前) */
    const CODE_KBN_NENREI_BEFORE = '8576';

    /** コード区分: 8577=>故人年齢表記(後) */
    const CODE_KBN_NENREI_AFTER = '8577';

    /** コード区分: 8593=>互助会約款 */
    const CODE_KBN_GOJOKAI_YAKKAN = '8593';
    // 報告書タブ①関連
    /** コード区分: 7884=>通夜時間決定経緯区分 */
    const CODE_KBN_KEII_TSUYA = '7884';

    /** コード区分: 7885=>告別時間決定経緯区分 */
    const CODE_KBN_KEII_KOKUBETSU = '7885';

    /** コード区分: 0730=>車種 */
    const CODE_KBN_CAR_TYPE = '0730';

    /** コード区分: 8559=>車種(喪家希望) */
    const CODE_KBN_CAR_TYPE2 = '8559';

    /** コード区分: 7886=>商品銘柄指定有無区分 */
    const CODE_KBN_SHOHIN_SHITEI = '7886';

    /** コード区分: 7887=>飲食サービス料説明区分 */
    const CODE_KBN_DRINK_SERVICE_EXPLAIN = '7887';

    /** コード区分: 7888=>消費税説明区分 */
    const CODE_KBN_TAX_EXPLAIN = '7888';

    /** コード区分: 7889=>支払期限説明区分 */
    const CODE_KBN_PAYMENT_DEADLINE = '7889';

    /** コード区分: 7890=>支払方法説明区分 */
    const CODE_KBN_PAYMENT_METHOD = '7890';

    /** コード区分: 7891=>記帳箱渡区分 */
    const CODE_KBN_HAND_OVER_BOX = '7891';

    /** コード区分: 7892=>宗教者連絡区分 */
    const CODE_KBN_CONTACT_TERA = '7892';

    /** コード区分: 7893=>寝台車追加料金説明区分 */
    const CODE_KBN_SLLEPER_EXPLAIN = '7893';

    /** コード区分: 7894=>移動時間説明区分 */
    const CODE_KBN_TRANSIT_EXPLAIN = '7894';

    /** コード区分: 7895=>弔辞説明区分 */
    const CODE_KBN_CONDOLENCE = '7895';

    /** コード区分: 7896=>弔辞内容説明区分 */
    const CODE_KBN_CONDOLENCE_EXPLAIN = '7896';

    /** コード区分: 7897=>弔辞打合予定区分 */
    const CODE_KBN_D_MEETING = '7897';

    /** コード区分: 7898=>世話役説明区分 */
    const CODE_KBN_CARE = '7898';

    /** コード区分: 7899=>世話役説明有無区分 */
    const CODE_KBN_CARE_SECTION = '7899';

    /** コード区分: 7900=>世話役説明プラカード区分 */
    const CODE_KBN_CARE_PLACARD = '7900';

    /** コード区分: 7901=>枕元お供え品説明区分 */
    const CODE_KBN_OSONAE = '7901';

    /** コード区分: 7902=>供花注文書記載方法区分 */
    const CODE_KBN_FLOWER_METHOD = '7902';

    /** コード区分: 7903=>供花注文書預かりタイミング */
    const CODE_KBN_FLOWER_TIMING = '7903';

    /** コード区分: 7904=>必要基数説明区分 */
    const CODE_KBN_FLOWER_NUM_EXPLAIN = '7904';

    /** コード区分: 7905=>展示品有無区分 */
    const CODE_KBN_EXIBIT = '7905';

    /** コード区分: 7906=>BGM有無区分 */
    const CODE_KBN_BGM = '7906';

    /** コード区分: 7907=>故人所属団体有無区分 */
    const CODE_KBN_K_GROUP = '7907';

    /** コード区分: 7908=>当日経費宗教者・世話役関係区分 */
    const CODE_KBN_COST_TERA = '7908';

    /** コード区分: 7909=>当日経費火葬場関係区分 */
    const CODE_KBN_COST_KASO = '7909';

    /** コード区分: 7910=>当日経費式場・車両関係区分 */
    const CODE_KBN_COST_HALL_CAR = '7910';

    /** コード区分: 7950=>当日経費その他区分 */
    const CODE_KBN_COST_OTHER = '7950';

    /** コード区分: 7911=>宿泊説明区分 */
    const CODE_KBN_LODGING = '7911';

    /** コード区分: 7912=>宿泊施設説明区分 */
    const CODE_KBN_LODGING_FACILITY = '7912';

    /** コード区分: 7913=>貸寝具説明区分 */
    const CODE_KBN_BEDDING = '7913';

    /** コード区分: 7914=>宿泊金額説明区分 */
    const CODE_KBN_LODGING_COST = '7914';

    /** コード区分: 7915=>提携ホテル説明区分 */
    const CODE_KBN_PARTNER_LODGING = '7915';

    /** コード区分: 7916=>朝食説明区分 */
    const CODE_KBN_BREAKFAST = '7916';

    /** コード区分: 7917=>別室利用希望区分 */
    const CODE_KBN_OTHER_ROOM = '7917';

    /** コード区分: 7918=>御礼渡しタイミング説明区分 */
    const CODE_KBN_THANKING = '7918';

    /** コード区分: 7919=>御礼渡しタイミング(宗教者)区分 */
    const CODE_KBN_THANKING_TERA = '7919';

    /** コード区分: 7920=>御礼渡しタイミング(世話役)区分 */
    const CODE_KBN_THANKING_CARE = '7920';

    /** コード区分: 7921=>挨拶説明(通夜)区分 */
    const CODE_KBN_GREETING_TSUYA = '7921';

    /** コード区分: 7922=>挨拶説明(告別式)区分 */
    const CODE_KBN_GREETING_KOKUBETSU = '7922';

    /** コード区分: 7923=>挨拶説明(献杯)区分 */
    const CODE_KBN_GREETING_KENPAI = '7923';

    /** コード区分: 7924=>挨拶説明(散会)区分 */
    const CODE_KBN_GREETING_SANKAI = '7924';

    /** コード区分: 7925=>挨拶説明(他)区分 */
    const CODE_KBN_GREETING_OTHER = '7925';

    /** コード区分: 7926=>待合室追加可否説明区分 */
    const CODE_KBN_KASO_ROOM_ADDITION = '7926';

    /** コード区分: 7927=>料理設置可能数説明区分 */
    const CODE_KBN_KASO_ROOM_DISH = '7927';

    /** コード区分: 7928=>式場持参品説明区分 */
    const CODE_KBN_BELONGINGS = '7928';

    /** コード区分: 7929=>プロジェクションマッピング説明区分 */
    const CODE_KBN_PROJECTION_M_EXPLAIN = '7929';

    /** コード区分: 7930=>プロジェクションマッピング内容区分 */
    const CODE_KBN_PROJECTION_M_CONTENTS = '7930';
    // 報告書タブ②関連
    /** コード区分: 3640=>霊柩車区分 */
    const CODE_KBN_KIHON_REIKYU = '3640';

    /** コード区分: 7951=>TD処置予定区分 */
    const CODE_KBN_KIHON_TD_SCHEDULE = '7951';

    /** コード区分: 7952=>TD処置時納棺区分 */
    const CODE_KBN_KIHON_TD_NOUKAN = '7952';

    /** コード区分: 7953=>湯灌処置種別区分 */
    const CODE_KBN_KIHON_YUKAN_SCHEDULE = '7953';

    /** コード区分: 7954=>湯灌処置場所区分 */
    const CODE_KBN_KIHON_YUKAN_SCHEDULE2 = '7954';

    /** コード区分: 7955=>湯灌処置時納棺区分 */
    const CODE_KBN_KIHON_YUKAN_NOUKAN = '7955';

    /** コード区分: 7956=>送迎サービス有無区分 */
    const CODE_KBN_KIHON_TRANSPORT = '7956';

    /** コード区分: 7957=>壺彫刻有無区分 */
    const CODE_KBN_KIHON_TSUBO = '7957';

    /** コード区分: 7958=>手配プラカード有無区分 */
    const CODE_KBN_KIHON_PLACARD = '7958';

    /** コード区分: 7959=>金額別返礼有無区分 */
    const CODE_KBN_KIHON_HENREI = '7959';

    /** コード区分: 7960=>TD搬送手配状況区分 */
    const CODE_KBN_TD_TEHAI = '7960';

    /** コード区分: 7961=>湯灌手配状況区分 */
    const CODE_KBN_YUKAN_TEHAI = '7961';

    /** コード区分: 7962=>家族立会有無区分 */
    const CODE_KBN_NOUKAN_TACHIAI = '7962';

    /** コード区分: 7963=>葬送衣装区分 */
    const CODE_KBN_NOUKAN_CLOTHING = '7963';

    /** コード区分: 7964=>葬送衣装掛け方区分 */
    const CODE_KBN_NOUKAN_CLOTHING_NM2 = '7964';

    /** コード区分: 7965=>祭壇設営区分 */
    const CODE_KBN_SETUP_SAIDAN = '7965';

    /** コード区分: 7966=>全館設営区分 */
    const CODE_KBN_SETUP_WHOLE = '7966';

    /** コード区分: 7967=>外式設営区分 */
    const CODE_KBN_SETUP_SOTO = '7967';

    /** コード区分: 7968=>預かり品手配有無区分 */
    const CODE_KBN_KEEPING = '7968';

    /** コード区分: 7969=>埋葬具手配有無区分 */
    const CODE_KBN_BURIAL_ITEM = '7969';

    /** コード区分: 7970=>斎場予約区分 */
    const CODE_KBN_F_HALL_RESERVE = '7970';

    /** コード区分: 7971=>火葬場内案内表示名区分 */
    const CODE_KBN_F_HALL_GUIDE = '7971';

    /** コード区分: 7972=>収骨案内放送名区分 */
    const CODE_KBN_F_HALL_BONE = '7972';

    /** コード区分: 7973=>告別式でのお別れ方法 */
    const CODE_KBN_F_HALL_FAREWELL = '7973';

    /** コード区分: 7974=>外字使用者区分 */
    const CODE_KBN_GAIJI = '7974';

    /** コード区分: 7947=>搬送料金区分 */
    const CODE_KBN_HANSO_RYOKIN_KBN = '7947';

    /** コード区分: 7982=>保冷剤交換状況 */
    const CODE_KBN_HOREIZAI_STATUS_KBN = '7982';

    /** コード区分: 8560=>申込区分(オーダーメイド) */
    const CODE_KBN_MOUSHI_OM = '8560';

    /** コード区分: 8592=>HiKARI問い合わせ先 */
    const CODE_KBN_HIKARI_INFORM = '8592';
    
    /** コード区分: 9600=>会員種別 */
    const CODE_KBN_KAIIN_SBT = '9600';   
    
    /** コード区分: 9603=>入電理由 */
    const CODE_KBN_NYUDEN_RIYU = '9603';   

    /** コード区分: 1820=>初七日区分 */
    const CODE_KBN_SHONANOKA_KBN = '1820';   

    /** コード区分: 9601=>届出役所区分 */
    const CODE_KBN_TODOKEDE_KBN = '9601';   
    
    /** コード区分: 9613=>お見舞金種別 */
    const CODE_KBN_OMIMAI_KBN = '9613';
    
    /** コード区分: 9614=>ステータス(結果情報2) */
    const CODE_KBN_KEKKA_INFO_CD = '9614';
    
    /** コード区分: 9615=>祭壇種別CD */
    const CODE_KBN_SAIDANSHUBETSU_CD = '9615';
    
    /** コード区分: 1980=>預金種別 */
    const CODE_KBN_YOKIN_SBT= '1980';
    /** コード区分: 9606=>紹介項目 */
    const CODE_KBN_9606= '9606';
    /** コード区分: 9611=>葬儀区分(受付結果) */
    const CODE_KBN_9611= '9611';
    /** コード区分: 9612=>ステータス */
    const CODE_KBN_9612= '9612';
    /** コード区分: 9614=>結果情報 */
    const CODE_KBN_9614= '9614';
    /** コード区分: 9646=>事前相談関係分類 */
    const CODE_KBN_9646= '9646';
    /** コード区分: 9647=>相談項目 */
    const CODE_KBN_9647= '9647';
    /** コード区分: 9648=>相談に見えた方 */
    const CODE_KBN_9648= '9648';
    /** コード区分: 9700=>アンケート送付先 */
    const CODE_KBN_9700= '9700';
    /** コード区分: 9701=>棺確認. */
    const CODE_KBN_9701= '9701';
    /** コード区分: 9708=>DM送付 */
    const CODE_KBN_9708= '9708';
    /** コード区分: 9709=>後飾り先*/
    const CODE_KBN_9709= '9709';
    /** コード区分: 9710=>連絡可否*/
    const CODE_KBN_9710= '9710';
    /** コード区分: 9711=>判定*/
    const CODE_KBN_9711= '9711';
    /** コード区分: 9712=>判定除外*/
    const CODE_KBN_9712= '9712';
    /** コード区分: 9713=>内訳*/
    const CODE_KBN_9713= '9713';
    /** コード区分: 9714=>内訳詳細*/
    const CODE_KBN_9714= '9714';
    /** コード区分: 9715=>残口*/
    const CODE_KBN_9715= '9715';
    /** コード区分: 9716=>状況*/
    const CODE_KBN_9716= '9716';
    /** コード区分: 9717=>紛失区分 */
    const CODE_KBN_9717 = '9717';
    /** コード区分: 9851=>割引券 */
    const CODE_KBN_9851 = '9851';
    
    /** 大分類: 0080=>供花供物 */
    const DAIBUNRUI_BECHU = '0080';

    /** 申込区分: 5=>事前相談 */
    const MOUSHI_KBN_JIZEN = '5';

    /** 日程区分: 1=>死亡日 */
    const NITEI_KBN_DEATH = '1';

    /** 日程区分: 4=>通夜 */
    const NITEI_KBN_TUYA = '4';

    /** 日程区分: 5=>出棺 */
    const NITEI_KBN_SYUKKAN = '5';

    /** 日程区分: 6=>火葬 */
    const NITEI_KBN_KASO = '6';

    /** 日程区分: 11=>葬儀 */
    const NITEI_KBN_SOGI = '11';

    /** 日程区分: 1=>法要 */
    const NITEI_KBN_HOYO = '1';

    /** 日程区分: 3=>法要 */
    const NITEI_KBN_HOEN = '3';

    /** 定型区分: 0=>訃報 */
    const TEIKEI_KBN_HUHO = 0;

    /** 場所区分: 6=>警察 */
    const DEATH_BASHO_KBN = '6';
    const DEF_YAKKAN_YMD = '2099/12/31';

    /** 打合せ事項更新履歴対象項目 */
    const UCHIAWASEINFO_ARRAY = array(
        array('table_nm' => 'seko_kihon_all_free', 'column' => 'free_kbn7', 'title' => '問合せに対する回答')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'n_free5', 'title' => '予想参列者数')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'n_free4', 'title' => '(内遺族・親族)')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_kouden_cd', 'title' => '7784')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_kyoka_cd', 'title' => '7786')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_shohin_cd', 'title' => '供花供物商品')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_tokuchu_prc', 'title' => '特注金額')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_kahi_01', 'title' => '7788')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_kahi_02', 'title' => '7790')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_kahi_03', 'title' => '7792')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_kahi_04', 'title' => '7794')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_kahi_05', 'title' => '7796')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'choi_kahi_06', 'title' => '7798')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'information_cd', 'title' => '7800')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'inquiry_topic', 'title' => '問合せ特記')
        , array('table_nm' => 'seko_kihon_info', 'column' => 'v_free19', 'title' => '供花供物特記')
    );

    /** コード区分:一時参照用 */
    protected $_codeKbn = '';

    /** 顧客コード */
    protected $_customerCd = '';

    /** 値引き互助会区分 */
    protected $_nebikiGojokaiKbn = null;

    /**
     * コード名称マスタ取得処理
     * 
     * <AUTHOR> Sai
     * @since      2014/02/13
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return  array コード名称マスタ
     */
    protected function getCodeNameMst() {
        $db = Msi_Sys_DbManager::getMyDb();
        // コード名称マスタを取得する

        $sql = "
        SELECT
            code_kbn            -- コード区分
            ,code_kbn_nm        -- コード区分名
            ,kbn_value_cd       -- 区分値コード
            ,kbn_value_cd_num   -- 区分値コード数値
            ,kbn_value_lnm      -- 区分値正式名
            ,kbn_value_snm      -- 区分値簡略名
            ,biko
            ,name_input_kbn
        FROM
            code_nm_mst
        WHERE
            delete_flg = 0
        AND code_kbn IN (
            ?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?
            )
        ORDER BY
            code_kbn
            ,disp_nox
            ,kbn_value_cd_num
            ,kbn_value_cd
                ";

        $select = $db->easySelect($sql, array(
            self::CODE_KBN_MOUSHI_KBN, self::CODE_KBN_SOUGI_KBN, self::CODE_KBN_KAIIN_KBN, self::CODE_KBN_HAIGU_KBN, self::CODE_KBN_GENGO
            , self::CODE_KBN_KEISHIKI_KBN, self::CODE_KBN_YOTO_KBN, self::CODE_KBN_HS_GYOMU_KBN, self::CODE_KBN_SYUHA_KBN, self::CODE_KBN_SYUSHI_KBN
            , self::CODE_KBN_ZOKU_KBN, self::CODE_SOGI_BASHO_KBN, self::CODE_KBN_MOUSHI_KBN_S, self::CODE_KBN_MOUSHI_KBN_H, self::CODE_P_INFO
            , self::CODE_KBN_YOTO_HOUJI, self::CODE_KBN_CONTENT1, self::CODE_KBN_CONTENT2, self::CODE_KBN_IF_NASHA, self::CODE_GOJOKAI_COSE
            , self::CODE_KBN_TERA_SHOKAI, self::CODE_KBN_SEKO_BASHO, self::CODE_KBN_UMU, self::CODE_KBN_KAINAME1_KBN, self::CODE_KBN_STATUS
            , self::CODE_KBN_CATEGORY, self::CODE_KBN_CHOI_KODEN, self::CODE_KBN_CHOI_KUMOTU, self::CODE_KBN_KAIIN_INFO, self::CODE_KBN_FA_SEIKA
            , self::CODE_KBN_FA_SIKIMI, self::CODE_KBN_FA_SAKAKI, self::CODE_KBN_FA_HANAWA, self::CODE_KBN_FA_KUDAMONO, self::CODE_KBN_FA_CAN
            , self::CODE_KBN_USER, self::CODE_KBN_PACEMAKER, self::CODE_KBN_INFECTION, self::CODE_KBN_KINMU_K, self::CODE_KBN_SEIKA_FORM
            , self::CODE_KBN_PORTRAIT, self::CODE_KBN_JIZEN_CONTACT, self::CODE_KBN_JIZEN_CONTACTWAY, self::CODE_KBN_JIZEN_CONTACTPROCESS, self::CODE_KBN_JIZEN_CONTACTSTATUS
            , self::CODE_KBN_JIZEN_PROGRESS, self::CODE_KBN_JIZEN_SEIYAKU, self::CODE_KBN_OUTAI, self::CODE_KBN_OUTAI_METHOD, self::CODE_KBN_OMUKAE
            , self::CODE_KBN_KEII_TSUYA, self::CODE_KBN_KEII_KOKUBETSU, self::CODE_KBN_CAR_TYPE, self::CODE_KBN_SHOHIN_SHITEI, self::CODE_KBN_DRINK_SERVICE_EXPLAIN
            , self::CODE_KBN_TAX_EXPLAIN, self::CODE_KBN_PAYMENT_DEADLINE, self::CODE_KBN_PAYMENT_METHOD, self::CODE_KBN_HAND_OVER_BOX, self::CODE_KBN_CONTACT_TERA
            , self::CODE_KBN_SLLEPER_EXPLAIN, self::CODE_KBN_TRANSIT_EXPLAIN, self::CODE_KBN_CONDOLENCE, self::CODE_KBN_CONDOLENCE_EXPLAIN, self::CODE_KBN_D_MEETING
            , self::CODE_KBN_CARE, self::CODE_KBN_CARE_SECTION, self::CODE_KBN_CARE_PLACARD, self::CODE_KBN_OSONAE, self::CODE_KBN_FLOWER_METHOD
            , self::CODE_KBN_FLOWER_TIMING, self::CODE_KBN_FLOWER_NUM_EXPLAIN, self::CODE_KBN_EXIBIT, self::CODE_KBN_BGM, self::CODE_KBN_K_GROUP
            , self::CODE_KBN_COST_TERA, self::CODE_KBN_COST_KASO, self::CODE_KBN_COST_HALL_CAR, self::CODE_KBN_LODGING, self::CODE_KBN_LODGING_FACILITY
            , self::CODE_KBN_BEDDING, self::CODE_KBN_LODGING_COST, self::CODE_KBN_PARTNER_LODGING, self::CODE_KBN_BREAKFAST, self::CODE_KBN_OTHER_ROOM
            , self::CODE_KBN_THANKING, self::CODE_KBN_THANKING_TERA, self::CODE_KBN_THANKING_CARE, self::CODE_KBN_GREETING_TSUYA, self::CODE_KBN_GREETING_KOKUBETSU
            , self::CODE_KBN_GREETING_KENPAI, self::CODE_KBN_GREETING_SANKAI, self::CODE_KBN_GREETING_OTHER, self::CODE_KBN_KASO_ROOM_ADDITION, self::CODE_KBN_KASO_ROOM_DISH
            , self::CODE_KBN_BELONGINGS, self::CODE_KBN_PROJECTION_M_EXPLAIN, self::CODE_KBN_PROJECTION_M_CONTENTS, self::CODE_KBN_KIHON_TD_SCHEDULE
            , self::CODE_KBN_KIHON_TD_NOUKAN, self::CODE_KBN_KIHON_YUKAN_SCHEDULE, self::CODE_KBN_KIHON_YUKAN_SCHEDULE2, self::CODE_KBN_KIHON_YUKAN_NOUKAN, self::CODE_KBN_KIHON_TRANSPORT
            , self::CODE_KBN_KIHON_TSUBO, self::CODE_KBN_KIHON_PLACARD, self::CODE_KBN_KIHON_HENREI, self::CODE_KBN_TD_TEHAI, self::CODE_KBN_YUKAN_TEHAI
            , self::CODE_KBN_NOUKAN_TACHIAI, self::CODE_KBN_NOUKAN_CLOTHING, self::CODE_KBN_NOUKAN_CLOTHING_NM2, self::CODE_KBN_SETUP_SAIDAN, self::CODE_KBN_SETUP_WHOLE
            , self::CODE_KBN_SETUP_SOTO, self::CODE_KBN_KEEPING, self::CODE_KBN_BURIAL_ITEM, self::CODE_KBN_F_HALL_RESERVE, self::CODE_KBN_F_HALL_GUIDE
            , self::CODE_KBN_F_HALL_BONE, self::CODE_KBN_F_HALL_FAREWELL, self::CODE_KBN_GAIJI, self::CODE_KBN_HANSO_RYOKIN_KBN, self::CODE_KBN_CIF_STATUS
            , self::CODE_KBN_COST_OTHER, self::CODE_KBN_HOREIZAI_STATUS_KBN,self::CODE_KBN_PRC_UNITY, self::CODE_KBN_KYOSAI,self::CODE_KBN_MOUSHI_KBN_J
            , self::CODE_KBN_GENKYO, self::CODE_KBN_KINMU_M, self::CODE_KBN_CAR_TYPE2, self::CODE_KBN_MOUSHI_OM, self::CODE_KBN_YOTO_KBN_OM
            , self::CODE_KBN_SEX_KBN, self::CODE_KBN_NENREI_BEFORE, self::CODE_KBN_NENREI_AFTER, self::CODE_KBN_HIKARI_INFORM, self::CODE_KBN_NYUDEN_RIYU
            , self::HS_ANCHI_CD, self::CODE_KBN_SHONANOKA_KBN, self::CODE_KBN_TODOKEDE_KBN, self::CODE_KBN_SAIDANSHUBETSU_CD, self::CODE_KBN_KEKKA_INFO_CD
            , self::CODE_KBN_OMIMAI_KBN, self::CODE_KBN_YOKIN_SBT, self::CODE_KBN_KAIIN_SBT, self::CODE_KBN_9611, self::CODE_KBN_9646
            , self::CODE_KBN_9647, self::CODE_KBN_9648, self::CODE_KBN_9606, self::CODE_KBN_9612, self::CODE_KBN_9614
            , self::CODE_KBN_9700, self::CODE_KBN_9701, self::CODE_KBN_9708, self::CODE_KBN_9709, self::CODE_KBN_9710, self::CODE_KBN_9711, self::CODE_KBN_9712
            , self::CODE_KBN_9713, self::CODE_KBN_9714, self::CODE_KBN_9715, self::CODE_KBN_9716, self::CODE_KBN_9717, self::CODE_KBN_9851
        ));
        return $select;
    }

    /**
     *
     * 施行請求先情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/10
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 施行請求先情報
     */
    protected function getSekyuInfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
             si.seko_no         -- 施行番号
            ,si.sekyu_cd        -- 請求先コード
            ,si.sekyu_nm        -- 請求先名
            ,si.sekyu_last_nm   -- 請求先苗字
            ,si.sekyu_first_nm  -- 請求先名前
            ,si.sekyu_knm       -- 請求先名カナ
            ,si.sekyu_last_knm  -- 請求先苗字カナ
            ,si.sekyu_first_knm -- 請求先名前カナ
            ,si.sekyu_file_nm   -- 添付ファイル名
            ,si.moshu_kankei_kbn AS sekyu_moshu_kankei_kbn  -- 喪主との関係
            ,si.moshu_kankei AS sekyu_moshu_kankei          -- 関係
            ,si.yubin_no AS sekyu_yubin_no                  -- 請求先郵便番号
            ,si.addr1 AS sekyu_addr1                        -- 請求先住所1
            ,si.addr2 AS sekyu_addr2                        -- 請求先住所2
            ,si.tel AS sekyu_tel                            -- 請求先電話番号
            ,si.mobile_tel                                  -- 請求先携帯番号
            ,si.fax AS sekyu_fax                            -- 請求先FAX
            ,si.biko1 AS sekyu_biko1                        -- 請求先備考１
            ,br.transfer_bank_cd    AS sekyu_bank_no        -- 請求先銀行口座番号
            ,br.bank_nm || ' ' || br.shiten_nm || ' ' || 
                (CASE WHEN br.yokin_sbt=0 THEN '普' WHEN br.yokin_sbt=1 THEN '当座' ELSE cm1980.kbn_value_lnm END) || br.st_br_koza_no 
                                    AS sekyu_bank_nm        -- 請求先銀行口座名
            ,si.sekyu_soufu_nm  -- 請求送付先名
            ,si.soufu_last_nm   -- 請求送付先苗字
            ,si.soufu_first_nm  -- 請求送付先名前
            ,si.sekyu_soufu_knm -- 請求送付先名カナ
            ,si.soufu_last_knm  -- 請求送付先苗字カナ
            ,si.soufu_first_knm -- 請求送付先名前カナ
            ,si.soufu_yubin_no  -- 請求送付先郵便番号
            ,si.soufu_addr1     -- 請求送付先住所1
            ,si.soufu_addr2     -- 請求送付先住所2
            ,si.soufu_tel       -- 請求送付先電話番号
            ,si.soufu_file_nm   -- 添付ファイル名
            ,si.ryosyu_meigi            -- 領収証名義
            ,si.ryosyu_soufu_nm         -- 領収証送付先名
            ,si.ryosyu_soufu_last_nm    -- 領収証送付先苗字
            ,si.ryosyu_soufu_first_nm   -- 領収証送付先名前
            ,si.ryosyu_soufu_knm        -- 領収証送付先名カナ
            ,si.ryosyu_soufu_last_knm   -- 領収証送付先苗字カナ
            ,si.ryosyu_soufu_first_knm  -- 領収証送付先名前カナ
            ,si.ryosyu_soufu_yubin_no   -- 領収証送付先郵便番号
            ,si.ryosyu_soufu_addr1      -- 領収証送付先住所1
            ,si.ryosyu_soufu_addr2      -- 領収証送付先住所2
            ,si.ryosyu_soufu_tel        -- 領収証送付先電話番号
            ,si.ryosyu_soufu_file_nm    -- 領収証送付先添付ファイル名
        FROM
            sekyu_saki_info si
        LEFT JOIN juchu_denpyo jd           -- 2016/07/24 ADD Kayo
            ON  si.seko_no = jd.seko_no
            AND jd.data_kbn IN (1,2)
            AND 0          = jd.delete_flg 
        LEFT JOIN br_koza_kanri_mst br
        ON  br.transfer_bank_cd = CASE WHEN length(si.v_free3) = 0 THEN NULL ELSE si.v_free3 END
        LEFT JOIN code_nm_mst cm1980
        ON  cm1980.kbn_value_cd_num = br.yokin_sbt
        AND cm1980.code_kbn         = '1980' -- 預金種別(1980)
        WHERE
            si.seko_no = :seko_no
        AND si.sekyu_cd = :sekyu_cd
        AND si.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'sekyu_cd' => $this->_sekyuCd));
        return $select;
    }

    /**
     *
     * 施行互助会情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 施行互助会情報
     */
    protected function getGojokaiInfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
             gi.seko_no             -- 施行番号
            ,gi.rireki_kbn          -- 施行履歴有無
            ,gi.sodan_kbn           -- 事前相談有無
            ,gi.kanyu_kakunin_kbn   -- お客様加入確認有無
            ,gi.join_use_kbn        -- 加入団体利用有無
            ,gi.spec_agent_cd       -- 特約区分コード
            ,gi.spec_agent_kbn      -- 特約区分
            ,gi.kanyu_dantai_cd    -- 加入団体コード
            ,gi.kanyu_dantai_kbn    -- 加入団体区分
            ,gi.kanyu_dantai_ext    -- 加入団体（その他）
            ,gi.hoyu_kbn            -- 保有互助会有無
            ,gi.kazoku_kbn          -- 同居家族有無
            ,gi.meigi_kbn           -- 互助会名義区分
            ,gi.info_kbn            -- 名義変更説明有無
            ,gi.riyu_memo           -- 名義変更未説明理由
            ,gi.plan_use_prc        -- プラン利用金額
            ,gi.plan_use_kbn        -- プラン利用区分
            ,gi.plan_change_prc     -- プラン変更差額
            ,gi.nebiki_gojokai_kbn     -- プラン変更差額
            ,gi.nebiki_yoto_kbn     
            ,gi.nebiki_gojokai_cose_cd AS use_cose     -- 利用コース
        FROM
            seko_gojokai_info gi
        WHERE
            gi.seko_no = :seko_no
        AND gi.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     *
     * 施行基本受付情報を取得する
     *
     * <AUTHOR> Tosaka
     * @since  2020/03/10
     * @return array 更新履歴情報
     */
    protected function getUketsukeInfo() {

        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                sui.seq_no
                ,sui.tanto_cd
                ,tm.tanto_nm
                ,TO_CHAR(sui.discuss_date, 'YYYY/MM/DD') AS discuss_date
                ,to_char(to_timestamp(discuss_time::text, 'HH24:MI'),'HH24:MI') AS discuss_time
                ,sui.status_kbn
                ,sui.level_kbn
                ,sui.contents
            FROM seko_uketsuke_info sui
            LEFT JOIN tanto_mst tm
                ON tm.tanto_cd = sui.tanto_cd
                AND tm.delete_flg = 0
            WHERE sui.seko_no = :seko_no
                AND sui.delete_flg = 0
            ORDER BY sui.seq_no
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        if (count($select) == 0) {
            array_push($select, array('seq_no' => 1));
        }
        return $select;
    }

    /**
     *
     * 更新履歴情報を取得する
     *
     * <AUTHOR> Tosaka
     * @since  2020/03/10
     * @return array 更新履歴情報
     */
    protected function getUpdateHistory() {

        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                sui.seq_no 
                ,tm.tanto_nm
                ,sui.update_contents 
                ,TO_CHAR(sui.update_ts, 'YYYY/MM/DD HH24:MI') AS update_ts
            FROM seko_uchiawase_info sui
            LEFT JOIN tanto_mst tm
                ON tm.tanto_cd = sui.tanto_cd
                AND tm.delete_flg = 0
            WHERE sui.seko_no = :seko_no
                AND sui.delete_flg = 0
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     *
     * 更新履歴対象のデータを取得する
     *
     * <AUTHOR> Tosaka
     * @since  2020/03/10
     * @return array 更新履歴情報
     */
    protected function getUpdateHistoryContents() {

        $dataUpdateHistoryContents = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $contents = self::UCHIAWASEINFO_ARRAY;
        foreach ($contents as $value) {
            $table_nm = $value['table_nm'];
            $column = $value['column'];
            $sql = "
                SELECT $column
                FROM $table_nm
                WHERE seko_no = :seko_no
                    AND delete_flg = 0
                    ";
            $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
            if (count($select) > 0) {
                $dataUpdateHistoryContents[$value['column']] = $select[0][$value['column']];
            } else {
                $dataUpdateHistoryContents[$value['column']] = null;
            }
        }
        return $dataUpdateHistoryContents;
    }

    /**
     *
     * 互助会金額マスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/08/27
     * @return array 互助会金額マスタ
     */
    protected function getGojokaiPrcMst() {

        $dataGojokaiPrcMst = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                grm.price_no            -- 管理No
                ,grm.course_price       -- コース金額
                ,grm.gojokai_kbn        -- 互助会区分
            FROM
                gojokai_price_mst grm
            WHERE
                    CURRENT_DATE BETWEEN grm.tekiyo_st_date AND grm.tekiyo_ed_date
                AND grm.delete_flg = 0
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $dataGojokaiPrcMst[$select[$i]['course_price']] = $select[$i];
            }
        }
        return $dataGojokaiPrcMst;
    }

    /**
     *
     * 互助会金額マスタを取得する(佐野商店カスタマイズ用)
     *
     * <AUTHOR> Sai
     * @since 2014/01/15
     * @return array 互助会金額マスタ
     */
    protected function getGojokaiPrcMst2() {

        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                grm.price_no            -- 管理No
                ,grm.course_price       -- コース金額
                ,grm.gojokai_kbn        -- 互助会区分
                ,grm.course_price_used  -- 互助会コースプラン利用金額
                ,grm.course_priority    -- 互助会コース優位性
                ,grm.gojokai_group_no   -- 互助会グループ番号
            FROM
                gojokai_price_mst grm
            WHERE
                    CURRENT_DATE BETWEEN grm.tekiyo_st_date AND grm.tekiyo_ed_date
                AND grm.delete_flg = 0
            ORDER BY
                course_priority DESC
                ";
        $select = $db->easySelect($sql);
        return $select;
    }

    /**
     * コード名称マスタフィルター処理
     * 
     * <AUTHOR> Sai
     * @since      2014/02/14
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param $dataCodeNameMst コード名称マスタ配列
     * @param $kbn 区分値コード数値
     * @param $key 通常はkbn_value_cd_numをキーにtureの場合はkbn_value_cdをキーに、
     * @return  array コード名称マスタ
     */
    protected function filter($dataCodeNameMst, $kbn) {
        $this->_codeKbn = $kbn;
        $codeNames = array_filter($dataCodeNameMst, function ($item) {
            return $item['code_kbn'] === $this->_codeKbn;
        }
        );
        return $codeNames;
    }

    /**
     *
     * 互助会特典の完納フラグを求める
     *
     * <AUTHOR> Sai
     * @since 2014/06/13
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param array $oneRow 施行互助会加入情報
     * @param array $gojokaiCouseMst 施行互助会マスタ
     * @return int $compFlg 0：未 1：済み
     */
    protected function getTkKanNoFlg($oneRow, $gojokaiCouseMst) {
        // 完納フラグ 0：未 1：済み
        $compFlg = 0;
        // コース変更差額金が存在したら 0:未を返す
        if ($oneRow['cose_chg_gaku'] > 0) {
            return $compFlg;
        }
        $haraiCnt = null; // 支払予定回数
        $kaiinNo = $oneRow['kain_no']; // 会員番号
        $kanyuYmd = $oneRow['kanyu_dt']; // 加入年月日
        // 支払予定回数を取得する
        foreach ($gojokaiCouseMst as $coseOneRow) {
            if (strpos($kaiinNo, $coseOneRow['gojokai_cose_iw']) === 0) {
                $haraiCnt = $coseOneRow['harai_yotei_cnt'];
                break;
            }
        }
        if ($haraiCnt !== null) {
            $diffMonth = $this->diffMonth($this->getZeiKijunYmd(), $kanyuYmd);
            if ($diffMonth >= $haraiCnt) {
                $compFlg = 1;
            }
        }
        return $compFlg;
    }

    /**
     *
     * 日付の月の差を求める
     *
     * <AUTHOR> Sai
     * @since 2014/06/13
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param string $ymd1 日付１
     * @param string $ymd2 日付２
     * @return int $mon $ymd1から$ymd2を引いた月数
     */
    private function diffMonth($ymd1, $ymd2) {
        $ptn = '/^(\d{4,4})(\D)?(\d{1,2})(\D)/';
        $match1 = null;
        $match2 = null;
        if (!preg_match($ptn, $ymd1, $match1)) {
            return null;
        }
        if (!preg_match($ptn, $ymd2, $match2)) {
            return null;
        }
        $y1 = (int) $match1[1];
        $m1 = (int) $match1[3];
        $y2 = (int) $match2[1];
        $m2 = (int) $match2[3];
        $mon = ($y1 - $y2) * 12 + $m1 - $m2;
        return $mon;
    }

    /**
     * 請求先情報保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/03/10
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihon 施行基本情報データ
     * @param array $dataSekyuInfo 請求先情報データ
     * @return int 更新件数
     */
    protected function saveSekyuInfo($db,$dataSekoKihon,$dataSekyuInfo) {
        // 請求番号
        $this->_sekyuCd = $dataSekyuInfo['sekyu_cd'];
        // 請求先共通情報設定
        $sekyuInfo = array();
        if (strlen($dataSekyuInfo['sekyu_last_nm']) > 0 && strlen($dataSekyuInfo['sekyu_first_nm']) > 0) {
            $sekyuInfo['sekyu_nm'] = $dataSekyuInfo['sekyu_last_nm'] . '　' . $dataSekyuInfo['sekyu_first_nm'];
        } else if (strlen($dataSekyuInfo['sekyu_last_nm']) > 0) {
            $sekyuInfo['sekyu_nm'] = $dataSekyuInfo['sekyu_last_nm'];
        } else if (strlen($dataSekyuInfo['sekyu_first_nm']) > 0) {
            $sekyuInfo['sekyu_nm'] = $dataSekyuInfo['sekyu_first_nm'];
        } else {
            $sekyuInfo['sekyu_nm'] = null;
        }
        $sekyuInfo['sekyu_last_nm'] = $dataSekyuInfo['sekyu_last_nm'];
        $sekyuInfo['sekyu_first_nm'] = $dataSekyuInfo['sekyu_first_nm'];
        if (strlen($dataSekyuInfo['sekyu_last_knm']) > 0 && strlen($dataSekyuInfo['sekyu_first_knm']) > 0) {
            $sekyuInfo['sekyu_knm'] = $dataSekyuInfo['sekyu_last_knm'] . '　' . $dataSekyuInfo['sekyu_first_knm'];
        } else if (strlen($dataSekyuInfo['sekyu_last_knm']) > 0) {
            $sekyuInfo['sekyu_knm'] = $dataSekyuInfo['sekyu_last_knm'];
        } else if (strlen($dataSekyuInfo['sekyu_first_knm']) > 0) {
            $sekyuInfo['sekyu_knm'] = $dataSekyuInfo['sekyu_first_knm'];
        } else {
            $sekyuInfo['sekyu_knm'] = null;
        }
        $sekyuInfo['sekyu_last_knm'] = $dataSekyuInfo['sekyu_last_knm'];
        $sekyuInfo['sekyu_first_knm'] = $dataSekyuInfo['sekyu_first_knm'];
        if (isset($dataSekyuInfo['sekyu_file'])) {
            $sekyuInfo['sekyu_file_nm'] = $dataSekyuInfo['sekyu_file'];
        } else if (isset($dataSekyuInfo['sekyu_file_nm'])) {
            $sekyuInfo['sekyu_file_nm'] = $dataSekyuInfo['sekyu_file_nm'];
        } else {
            $sekyuInfo['sekyu_file_nm'] = null;
        }
        $sekyuInfo['moshu_kankei_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekyuInfo['sekyu_moshu_kankei_kbn']);  // 喪主との関係
        $sekyuInfo['moshu_kankei'] = $dataSekyuInfo['sekyu_moshu_kankei'];  // 関係
        $sekyuInfo['yubin_no'] = $dataSekyuInfo['sekyu_yubin_no'];  // 請求先郵便番号
        $sekyuInfo['addr1'] = $dataSekyuInfo['sekyu_addr1'];  // 請求先住所1
        $sekyuInfo['addr2'] = $dataSekyuInfo['sekyu_addr2'];  // 請求先住所2
        $sekyuInfo['tel'] = $dataSekyuInfo['sekyu_tel'];  // 請求先電話番号
        $sekyuInfo['mobile_tel'] = $dataSekyuInfo['mobile_tel'];  // 請求先携帯番号
        $sekyuInfo['fax'] = $dataSekyuInfo['sekyu_fax'];  // 請求先FAX
        $sekyuInfo['biko1'] = $dataSekyuInfo['sekyu_biko1'];  // 請求先備考１
        if (strlen($dataSekyuInfo['soufu_last_nm']) > 0 && strlen($dataSekyuInfo['soufu_first_nm']) > 0) {
            $sekyuInfo['sekyu_soufu_nm'] = $dataSekyuInfo['soufu_last_nm'] . '　' . $dataSekyuInfo['soufu_first_nm'];
        } else if (strlen($dataSekyuInfo['soufu_last_nm']) > 0) {
            $sekyuInfo['sekyu_soufu_nm'] = $dataSekyuInfo['soufu_last_nm'];
        } else if (strlen($dataSekyuInfo['soufu_first_nm']) > 0) {
            $sekyuInfo['sekyu_soufu_nm'] = $dataSekyuInfo['soufu_first_nm'];
        } else {
            $sekyuInfo['sekyu_soufu_nm'] = null;
        }
        $sekyuInfo['soufu_last_nm'] = $dataSekyuInfo['soufu_last_nm'];
        $sekyuInfo['soufu_first_nm'] = $dataSekyuInfo['soufu_first_nm'];
        if (strlen($dataSekyuInfo['soufu_last_knm']) > 0 && strlen($dataSekyuInfo['soufu_first_knm']) > 0) {
            $sekyuInfo['sekyu_soufu_knm'] = $dataSekyuInfo['soufu_last_knm'] . '　' . $dataSekyuInfo['soufu_first_knm'];
        } else if (strlen($dataSekyuInfo['soufu_last_knm']) > 0) {
            $sekyuInfo['sekyu_soufu_knm'] = $dataSekyuInfo['soufu_last_knm'];
        } else if (strlen($dataSekyuInfo['soufu_first_knm']) > 0) {
            $sekyuInfo['sekyu_soufu_knm'] = $dataSekyuInfo['soufu_first_knm'];
        } else {
            $sekyuInfo['sekyu_soufu_knm'] = null;
        }
        $sekyuInfo['soufu_last_knm'] = $dataSekyuInfo['soufu_last_knm'];
        $sekyuInfo['soufu_first_knm'] = $dataSekyuInfo['soufu_first_knm'];
        $sekyuInfo['soufu_yubin_no'] = $dataSekyuInfo['soufu_yubin_no'];
        $sekyuInfo['soufu_addr1'] = $dataSekyuInfo['soufu_addr1'];
        $sekyuInfo['soufu_addr2'] = $dataSekyuInfo['soufu_addr2'];
        $sekyuInfo['soufu_tel'] = $dataSekyuInfo['soufu_tel'];
        if (isset($dataSekyuInfo['soufu_file'])) {
            $sekyuInfo['soufu_file_nm'] = $dataSekyuInfo['soufu_file'];
        } else if (isset($dataSekyuInfo['soufu_file_nm'])) {
            $sekyuInfo['soufu_file_nm'] = $dataSekyuInfo['soufu_file_nm'];
        } else {
            $sekyuInfo['soufu_file_nm'] = null;
        }
        $sekyuInfo['ryosyu_meigi'] = $dataSekyuInfo['ryosyu_meigi'];
        if (strlen($dataSekyuInfo['ryosyu_soufu_last_nm']) > 0 && strlen($dataSekyuInfo['ryosyu_soufu_first_nm']) > 0) {
            $sekyuInfo['ryosyu_soufu_nm'] = $dataSekyuInfo['ryosyu_soufu_last_nm'] . '　' . $dataSekyuInfo['ryosyu_soufu_first_nm'];
        } else if (strlen($dataSekyuInfo['ryosyu_soufu_last_nm']) > 0) {
            $sekyuInfo['ryosyu_soufu_nm'] = $dataSekyuInfo['ryosyu_soufu_last_nm'];
        } else if (strlen($dataSekyuInfo['ryosyu_soufu_first_nm']) > 0) {
            $sekyuInfo['ryosyu_soufu_nm'] = $dataSekyuInfo['ryosyu_soufu_first_nm'];
        } else {
            $sekyuInfo['ryosyu_soufu_nm'] = null;
        }
        $sekyuInfo['ryosyu_soufu_last_nm'] = $dataSekyuInfo['ryosyu_soufu_last_nm'];
        $sekyuInfo['ryosyu_soufu_first_nm'] = $dataSekyuInfo['ryosyu_soufu_first_nm'];
        if (strlen($dataSekyuInfo['ryosyu_soufu_last_knm']) > 0 && strlen($dataSekyuInfo['ryosyu_soufu_first_knm']) > 0) {
            $sekyuInfo['ryosyu_soufu_knm'] = $dataSekyuInfo['ryosyu_soufu_last_knm'] . '　' . $dataSekyuInfo['ryosyu_soufu_first_knm'];
        } else if (strlen($dataSekyuInfo['ryosyu_soufu_last_knm']) > 0) {
            $sekyuInfo['ryosyu_soufu_knm'] = $dataSekyuInfo['ryosyu_soufu_last_knm'];
        } else if (strlen($dataSekyuInfo['ryosyu_soufu_first_knm']) > 0) {
            $sekyuInfo['ryosyu_soufu_knm'] = $dataSekyuInfo['ryosyu_soufu_first_knm'];
        } else {
            $sekyuInfo['ryosyu_soufu_knm'] = null;
        }
        $sekyuInfo['ryosyu_soufu_last_knm'] = $dataSekyuInfo['ryosyu_soufu_last_knm'];
        $sekyuInfo['ryosyu_soufu_first_knm'] = $dataSekyuInfo['ryosyu_soufu_first_knm'];
        $sekyuInfo['ryosyu_soufu_yubin_no'] = $dataSekyuInfo['ryosyu_soufu_yubin_no'];
        $sekyuInfo['ryosyu_soufu_addr1'] = $dataSekyuInfo['ryosyu_soufu_addr1'];
        $sekyuInfo['ryosyu_soufu_addr2'] = $dataSekyuInfo['ryosyu_soufu_addr2'];
        $sekyuInfo['ryosyu_soufu_tel'] = $dataSekyuInfo['ryosyu_soufu_tel'];
        $sekyuInfo['ryosyu_soufu_file_nm'] = $dataSekyuInfo['ryosyu_soufu_file'];
        if (isset($dataSekyuInfo['ryosyu_soufu_file'])) {
            $sekyuInfo['ryosyu_soufu_file_nm'] = $dataSekyuInfo['ryosyu_soufu_file'];
        } else if (isset($dataSekyuInfo['ryosyu_soufu_file_nm'])) {
            $sekyuInfo['ryosyu_soufu_file_nm'] = $dataSekyuInfo['ryosyu_soufu_file_nm'];
        } else {
            $sekyuInfo['ryosyu_soufu_file_nm'] = null;
        }
        // s_cif_no=顧客番号
        $sekyuInfo['v_free1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['s_cif_no']);
        if (empty($this->_sekyuCd)) {
            // 請求番号自動採番
            $this->_sekyuCd = $this->getAutoSekyuNo($db);
            // 請求先登録情報設定
            $sekyuInfo['seko_no'] = $this->_sekoNo;  // 施行番号
            $sekyuInfo['sekyu_cd'] = $this->_sekyuCd;  // 請求先コード
            // 請求先情報登録SQL
            //$sql = $this->makeInsertSQL('sekyu_saki_info', $sekyuInfo);
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("sekyu_saki_info", $sekyuInfo);
        } else {
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['sekyu_cd'] = $this->_sekyuCd;  // 請求先コード
            $where['delete_flg'] = 0;  // 削除フラグ
            // 請求先情報更新SQL
            //$sql = $this->makeUpdateSQL('sekyu_saki_info', $sekyuInfo, $where);
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("sekyu_saki_info", $sekyuInfo, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     *  請求先番号採番処理
     *
     * <AUTHOR> Sai
     * @since 2014/3/10
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @return string 請求先番号
     */
    private function getAutoSekyuNo($db) {
        $kijyunYmd = Msi_Sys_Utils::getDate();
        $sekyuCd = App_ClsGetCodeNo::GetCodeNo($db, 'sekyu_saki_info', 'sekyu_cd', $kijyunYmd);
        return $sekyuCd;
    }

    /**
     * 請求先情報保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/03/10
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihon 施行基本情報データ
     * @param array $dataSekyuInfo 請求先情報データ
     * @return int 更新件数
     */
    protected function saveSekyuSakiInfo($db, $dataSekoKihon,$dataSekyuInfo) {

        $cnt = 0;
        // 請求先共通情報設定
        $sekyuInfo = array();
        // 施行は一行のみなので固定
        $sekyuInfo['seq_no'] = 1;
        $sekyuInfo['data_kbn'] = $this->getDataKbn();
        // s_cif_no=顧客番号
        $sekyuInfo['v_free1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['s_cif_no']);
        if (strlen($dataSekyuInfo['sekyu_last_nm']) > 0) {
            $sekyuInfo['sekyu_nm1'] = $dataSekyuInfo['sekyu_last_nm'];
        } else {
            $sekyuInfo['sekyu_nm1'] = null;
        }
        if (strlen($dataSekyuInfo['sekyu_first_nm']) > 0) {
            $sekyuInfo['sekyu_nm2'] = $dataSekyuInfo['sekyu_first_nm'];
        } else {
            $sekyuInfo['sekyu_nm2'] = null;
        }
        if (strlen($dataSekyuInfo['sekyu_last_knm']) > 0) {
            $sekyuInfo['sekyu_knm1'] = $dataSekyuInfo['sekyu_last_knm'];
        } else {
            $sekyuInfo['sekyu_knm1'] = null;
        }
        if (strlen($dataSekyuInfo['sekyu_first_knm']) > 0) {
            $sekyuInfo['sekyu_knm2'] = $dataSekyuInfo['sekyu_first_knm'];
        } else {
            $sekyuInfo['sekyu_knm2'] = null;
        }
        $sekyuInfo['sekyu_yubin_no'] = $dataSekyuInfo['sekyu_yubin_no'];  // 請求先郵便番号
        $sekyuInfo['sekyu_addr1'] = $dataSekyuInfo['sekyu_addr1'];  // 請求先住所1
        $sekyuInfo['sekyu_addr2'] = $dataSekyuInfo['sekyu_addr2'];  // 請求先住所2
        $sekyuInfo['sekyu_tel'] = $dataSekyuInfo['sekyu_tel'];  // 請求先電話番号
        $sekyuInfo['sekyu_mobile_tel'] = $dataSekyuInfo['mobile_tel'];  // 請求先携帯番号
        if (strlen($dataSekyuInfo['soufu_last_nm']) > 0 && strlen($dataSekyuInfo['soufu_first_nm']) > 0) {
            $sekyuInfo['sekyu_soufu_nm'] = $dataSekyuInfo['soufu_last_nm'] . '　' . $dataSekyuInfo['soufu_first_nm'];
        } else if (strlen($dataSekyuInfo['soufu_last_nm']) > 0) {
            $sekyuInfo['sekyu_soufu_nm'] = $dataSekyuInfo['soufu_last_nm'];
        } else if (strlen($dataSekyuInfo['soufu_first_nm']) > 0) {
            $sekyuInfo['sekyu_soufu_nm'] = $dataSekyuInfo['soufu_first_nm'];
        }
        $sekyuInfo['soufu_yubin_no'] = $dataSekyuInfo['soufu_yubin_no'];
        $sekyuInfo['soufu_addr1'] = $dataSekyuInfo['soufu_addr1'];
        $sekyuInfo['soufu_addr2'] = $dataSekyuInfo['soufu_addr2'];
        $sekyuInfo['soufu_tel'] = $dataSekyuInfo['soufu_tel'];
        $sekyuInfo['ryosyusyo_meigi'] = $dataSekyuInfo['ryosyu_meigi'];
        if (strlen($dataSekyuInfo['ryosyu_soufu_last_nm']) > 0 && strlen($dataSekyuInfo['ryosyu_soufu_first_nm']) > 0) {
            $sekyuInfo['ryosyusyo_soufu_nm'] = $dataSekyuInfo['ryosyu_soufu_last_nm'] . '　' . $dataSekyuInfo['ryosyu_soufu_first_nm'];
        } else if (strlen($dataSekyuInfo['ryosyu_soufu_last_nm']) > 0) {
            $sekyuInfo['ryosyusyo_soufu_nm'] = $dataSekyuInfo['ryosyu_soufu_last_nm'];
        } else if (strlen($dataSekyuInfo['ryosyu_soufu_first_nm']) > 0) {
            $sekyuInfo['ryosyusyo_soufu_nm'] = $dataSekyuInfo['ryosyu_soufu_first_nm'];
        }
        $sekyuInfo['ryosyusyo_soufu_yubin_no'] = $dataSekyuInfo['ryosyu_soufu_yubin_no'];
        $sekyuInfo['ryosyusyo_soufu_addr1'] = $dataSekyuInfo['ryosyu_soufu_addr1'];
        $sekyuInfo['ryosyusyo_soufu_addr2'] = $dataSekyuInfo['ryosyu_soufu_addr2'];

        $denpyo_no = $this->getJuchudenpyoNo();
        $uri_den_no = $this->getUriagedenpyoNo();
        $seikyu_den_no = $this->getSeikyudenpyoNo();
        $uriData = DataMapper_UriageDenpyo::find($db, array('denpyo_no' => $denpyo_no));
        if (isset($denpyo_no)) {
            $juchuSekyu = DataMapper_SekyuSakiInfo::findJuchusekyu($db, array('denpyo_no' => $denpyo_no));
        } else {
            $juchuSekyu = array();
        }
        if (isset($uri_den_no)) {
            $uriageSekyu = DataMapper_SekyuSakiInfo::findUriagesekyu($db, array('uri_den_no' => $uri_den_no));
        } else {
            $uriageSekyu = array();
        }
        if (isset($seikyu_den_no)) {
            $seikyuSekyu = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('seikyu_den_no' => $seikyu_den_no));
        } else {
            $seikyuSekyu = array();
        }

        // 受注請求先情報更新処理
        $except = array();
        $where = array();
        if (count($juchuSekyu) == 0) {
            // 請求先登録情報設定
            $sekyuInfo['seko_no'] = $this->_sekoNo;
            $sekyuInfo['denpyo_no'] = $denpyo_no;
            // 受注請求先情報登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("juchu_sekyu_saki_info", $sekyuInfo, $except);
        } else {
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['denpyo_no'] = $denpyo_no;
            $where['delete_flg'] = 0;  // 削除フラグ
            // 受注請求先情報更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("juchu_sekyu_saki_info", $sekyuInfo, $where, $except);
        }
        $cnt += $db->easyExecute($sql, $param);

        if (count($uriData) > 0) {
            array_push($except, 'denpyo_no');
            // 売上請求先情報更新処理
            if (count($uriageSekyu) == 0) { // 見積確定時に作成されるはずだが念のため
                // 請求先登録情報設定
                $sekyuInfo['seko_no'] = $this->_sekoNo;
                $sekyuInfo['uri_den_no'] = $uri_den_no;
                // 売上請求先情報登録SQL
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("uriage_sekyu_saki_info", $sekyuInfo, $except);
            } else {
                // 条件部
                $where = array();
                $where['seko_no'] = $this->_sekoNo;  // 施行番号
                $where['uri_den_no'] = $uri_den_no;
                $where['delete_flg'] = 0;  // 削除フラグ
                // 売上請求先情報更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_sekyu_saki_info", $sekyuInfo, $where, $except);
            }
            $cnt += $db->easyExecute($sql, $param);
            // 請求請求先情報更新処理
            // 施行金額確定されていた場合のみ(=請求伝票が存在)
            if (isset($uriData[0]['seko_prc_kakute_kbn']) && $uriData[0]['seko_prc_kakute_kbn'] == '1') {
                array_push($except, 'uri_den_no');
                if (count($seikyuSekyu) == 0) { // 施行金額確定時に作成されるはずだが念のため
                    // 請求先登録情報設定
                    $sekyuInfo['seko_no'] = $this->_sekoNo;
                    $sekyuInfo['seikyu_den_no'] = $seikyu_den_no;
                    // 請求請求先情報登録SQL
                    list($sql, $param) = DataMapper_Utils::makeInsertSQL("seikyu_sekyu_saki_info", $sekyuInfo, $except);
                } else {
                    // 条件部
                    $where = array();
                    $where['seko_no'] = $this->_sekoNo;  // 施行番号
                    $where['seikyu_den_no'] = $seikyu_den_no;  // データ区分
                    $where['delete_flg'] = 0;  // 削除フラグ
                    // 請求請求先情報更新SQL
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_sekyu_saki_info", $sekyuInfo, $where, $except);
                }
                $cnt += $db->easyExecute($sql, $param);
            }
        }
        return $cnt;
    }

    /**
     * 施行互助会情報保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @param array $dataGojokaiInfo 施行互助会情報データ
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return int 更新件数
     */
    protected function saveGojokaiInfo($db, $dataGojokaiInfo, $dataGojokaiMemberCol) {
        // 更新対象外項目設定
        $except = array();
        $dataGojokaiInfo['hoyu_kbn'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['hoyu_kbn']);
        $dataGojokaiInfo['kazoku_kbn'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['kazoku_kbn']);
        $dataGojokaiInfo['meigi_kbn'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['meigi_kbn']);
        $dataGojokaiInfo['info_kbn'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['info_kbn']);
        array_push($except, 'gojokai_kbn');
        array_push($except, 'gojokai_group_no');
        array_push($except, 'plan_use_cnt');
        array_push($except, 'use_cose');
        array_push($except, 'use_keiyaku_gaku');
        $this->setNebikiGojokaiInfo($dataGojokaiInfo, $dataGojokaiMemberCol);
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        if (strlen($dataGojokaiInfo['use_cose']) > 0) {
            $couseMstOne = $this->getCouseMstOne($dataGojokaiInfo['use_cose'], $gojokaiCouseMst);
            $dataGojokaiInfo['use_cose'] = $couseMstOne['gojokai_cose_iw'];
            $dataGojokaiInfo['nebiki_gojokai_cose_cd'] = $couseMstOne['gojokai_cose_iw'];
        } else {
            $dataGojokaiInfo['nebiki_gojokai_cose_cd'] = null;
        }
        // 施行互助会情報存在チェック
        $selectGojokaiInfo = $this->getGojokaiInfo();
        if (Msi_Sys_Utils::myCount($selectGojokaiInfo) === 0) {
            $dataGojokaiInfo['seko_no'] = $this->_sekoNo;
            // 施行互助会登録SQL
            //$sql = $this->makeInsertSQL('seko_gojokai_info', $dataGojokaiInfo, $except);
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_gojokai_info", $dataGojokaiInfo, $except);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $dataGojokaiInfo['seko_no'];  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 施行互助会更新SQL
            //$sql = $this->makeUpdateSQL('seko_gojokai_info', $dataGojokaiInfo, $where, $except);
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_gojokai_info", $dataGojokaiInfo, $where, $except);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $dataGojokaiInfo;
    }

    /**
     * 施行故人情報保存処理 
     *
     * <AUTHOR> Tosaka
     * @since 2021/xx/xx
     * @param Msi_Sys_Db $db db
     * @param array $dataKojinInfoCol 施行故人情報データ
     * @return int 更新件数
     */
    protected function saveKojinInfo($db, $dataKojinInfoCol) {

        // 削除→登録する
        $sql = "
            DELETE FROM seko_kojin_info_houji
            WHERE seko_no = :seko_no 
                ";
        $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo));
        // 一行目データは施行基本情報の故人の情報に保存する
        $kihon = array();
        foreach ($dataKojinInfoCol as $oneRow) {
            $except = array();
            array_push($except, 'hk_wa_year');
            $oneRow['seko_no'] = $this->_sekoNo;
            $oneRow['seq_no'] = Msi_Sys_Utils::emptyToNull($oneRow['seq_no']);
            $oneRow['sekohoyo_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['sekohoyo_kbn']);
            $oneRow['hk_last_nm'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_last_nm']);
            $oneRow['hk_first_nm'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_first_nm']);
            $oneRow['hk_last_knm'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_last_knm']);
            $oneRow['hk_first_knm'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_first_knm']);
            $oneRow['hk_kaimyo'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_kaimyo']);
            $oneRow['hk_death_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_death_ymd']);
            $oneRow['hk_cif_no'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_cif_no']);
            $oneRow['hk_sex_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_sex_kbn']);
            $oneRow['hk_gengo'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_gengo']);
            $oneRow['hk_birth_year'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_birth_year']);
            $oneRow['hk_birth_month'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_birth_month']);
            $oneRow['hk_birth_day'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_birth_day']);
            $oneRow['hk_seinengappi_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_seinengappi_ymd']);
            $oneRow['hk_seinengappi_ymd_y'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_seinengappi_ymd_y']);
            $oneRow['hk_nenrei_man'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_nenrei_man']);
            $oneRow['hk_file_oid'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_file_oid']);
            $oneRow['hk_file_nm'] = Msi_Sys_Utils::emptyToNull($oneRow['hk_file_nm']);
            $oneRow['hkg_yubin_no'] = Msi_Sys_Utils::emptyToNull($oneRow['hkg_yubin_no']);
            $oneRow['hkg_addr1'] = Msi_Sys_Utils::emptyToNull($oneRow['hkg_addr1']);
            $oneRow['hkg_addr2'] = Msi_Sys_Utils::emptyToNull($oneRow['hkg_addr2']);
            $oneRow['hkg_tel'] = Msi_Sys_Utils::emptyToNull($oneRow['hkg_tel']);
            if (strlen($oneRow['hk_last_nm']) > 0 && strlen($oneRow['hk_first_nm']) > 0) {
                $oneRow['hk_nm'] = $oneRow['hk_last_nm'] . '　' . $oneRow['hk_first_nm'];
            } else if (strlen($oneRow['hk_last_nm']) > 0) {
                $oneRow['hk_nm'] = $oneRow['hk_last_nm'];
            } else if (strlen($oneRow['hk_first_nm']) > 0) {
                $oneRow['hk_nm'] = $oneRow['hk_first_nm'];
            }
            if (strlen($oneRow['hk_last_knm']) > 0 && strlen($oneRow['hk_first_knm']) > 0) {
                $oneRow['hk_knm'] = $oneRow['hk_last_knm'] . '　' . $oneRow['hk_first_knm'];
            } else if (strlen($oneRow['hk_last_knm']) > 0) {
                $oneRow['hk_knm'] = $oneRow['hk_last_knm'];
            } else if (strlen($oneRow['hk_first_knm']) > 0) {
                $oneRow['hk_knm'] = $oneRow['hk_first_knm'];
            }
            if ($oneRow['seq_no'] == 1) {
                $kihon['k_last_nm'] = $oneRow['hk_last_nm'];
                $kihon['k_first_nm'] = $oneRow['hk_first_nm'];
                $kihon['k_nm'] = $oneRow['hk_nm'];
                $kihon['k_last_knm'] = $oneRow['hk_last_knm'];
                $kihon['k_first_knm'] = $oneRow['hk_first_knm'];
                $kihon['k_knm'] = $oneRow['hk_knm'];
                $kihon['k_sex_kbn'] = $oneRow['hk_sex_kbn'];
                $kihon['k_gengo'] = $oneRow['hk_gengo'];
                $kihon['k_birth_year'] = $oneRow['hk_birth_year'];
                $kihon['k_birth_month'] = $oneRow['hk_birth_month'];
                $kihon['k_birth_day'] = $oneRow['hk_birth_day'];
                $kihon['k_seinengappi_ymd'] = $oneRow['hk_seinengappi_ymd'];
                $kihon['k_seinengappi_ymd_y'] = $oneRow['hk_seinengappi_ymd_y'];
                $kihon['k_nenrei_man'] = $oneRow['hk_nenrei_man'];
                $kihon['kg_yubin_no'] = $oneRow['hkg_yubin_no'];
                $kihon['kg_addr1'] = $oneRow['hkg_addr1'];
                $kihon['kg_addr2'] = $oneRow['hkg_addr2'];
                $kihon['kg_tel'] = $oneRow['hkg_tel'];
            }
            // 施行故人情報登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kojin_info_houji", $oneRow, $except);
            $cnt += $db->easyExecute($sql, $param);
            if (count($kihon) > 0) {
                // 施行基本情報更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihon, array('seko_no' => $this->_sekoNo));
                $cnt += $db->easyExecute($sql, $param);
            }
        }
        return $cnt;
    }

    /**
     * 施行契約先情報保存処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/09/30
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKeiyakusakiInfo 施行契約先情報データ
     * @return int 更新件数
     */
    protected function saveSekoKeiyakusakiInfo($db, $dataSekoKeiyakusakiInfo) {
        // 更新対象外項目設定
        $except = array();
        array_push($except, 'user_wa_year');
        $dataSekoKeiyakusakiInfo['user_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKeiyakusakiInfo['user_kbn']);
        $dataSekoKeiyakusakiInfo['ministries_cd'] = Msi_Sys_Utils::emptyToNull($dataSekoKeiyakusakiInfo['ministries_cd']);
        $dataSekoKeiyakusakiInfo['user_birth_year'] = Msi_Sys_Utils::emptyToNull($dataSekoKeiyakusakiInfo['user_birth_year']);
        $dataSekoKeiyakusakiInfo['user_gengo'] = Msi_Sys_Utils::emptyToNull($dataSekoKeiyakusakiInfo['user_gengo']);
        $dataSekoKeiyakusakiInfo['user_birth_month'] = Msi_Sys_Utils::emptyToNull($dataSekoKeiyakusakiInfo['user_birth_month']);
        $dataSekoKeiyakusakiInfo['user_birth_day'] = Msi_Sys_Utils::emptyToNull($dataSekoKeiyakusakiInfo['user_birth_day']);
        if (strlen($dataSekoKeiyakusakiInfo['user_last_nm']) > 0 && strlen($dataSekoKeiyakusakiInfo['user_first_nm']) > 0) {
            $dataSekoKeiyakusakiInfo['user_nm'] = $dataSekoKeiyakusakiInfo['user_last_nm'] . '　' . $dataSekoKeiyakusakiInfo['user_first_nm'];
        } else if (strlen($dataSekoKeiyakusakiInfo['user_last_nm']) > 0) {
            $dataSekoKeiyakusakiInfo['user_nm'] = $dataSekoKeiyakusakiInfo['user_last_nm'];
        } else if (strlen($dataSekoKeiyakusakiInfo['user_first_nm']) > 0) {
            $dataSekoKeiyakusakiInfo['user_nm'] = $dataSekoKeiyakusakiInfo['user_first_nm'];
        }
        if (strlen($dataSekoKeiyakusakiInfo['user_last_knm']) > 0 && strlen($dataSekoKeiyakusakiInfo['user_first_knm']) > 0) {
            $dataSekoKeiyakusakiInfo['user_knm'] = $dataSekoKeiyakusakiInfo['user_last_knm'] . '　' . $dataSekoKeiyakusakiInfo['user_first_knm'];
        } else if (strlen($dataSekoKeiyakusakiInfo['user_last_knm']) > 0) {
            $dataSekoKeiyakusakiInfo['user_knm'] = $dataSekoKeiyakusakiInfo['user_last_knm'];
        } else if (strlen($dataSekoKeiyakusakiInfo['user_first_knm']) > 0) {
            $dataSekoKeiyakusakiInfo['user_knm'] = $dataSekoKeiyakusakiInfo['user_first_knm'];
        }
        // 施行互助会情報存在チェック
        $selectKeiyakuInfo = $this->getKeiyakusakiInfo();
        // 契約先コードがなければDELETEする
        $cnt = 0;
        if (!isset($dataSekoKeiyakusakiInfo['partner_cd'])) {
            if (Msi_Sys_Utils::myCount($selectKeiyakuInfo) > 0) {
                $sql = "
                    DELETE
                    FROM seko_keiyakusaki_info
                    WHERE seko_no = :seko_no 
                        ";
                $param['seko_no'] = $this->_sekoNo;
                $cnt = $db->easyExecute($sql, $param);
            }
        } else {
            if (Msi_Sys_Utils::myCount($selectKeiyakuInfo) === 0) {
                $dataSekoKeiyakusakiInfo['seko_no'] = $this->_sekoNo;
                // 登録SQL
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_keiyakusaki_info", $dataSekoKeiyakusakiInfo, $except);
            } else {
                array_push($except, 'seko_no');
                // 条件部
                $where['seko_no'] = $dataSekoKeiyakusakiInfo['seko_no'];  // 施行番号
                $where['delete_flg'] = 0;  // 削除フラグ
                // 更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_keiyakusaki_info", $dataSekoKeiyakusakiInfo, $where, $except);
            }
            $cnt = $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     * 施行受付履歴保存処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/04/16
     * @param Msi_Sys_Db $db db
     * @param array $dataUketsukeInfoCol 施行互助会加入者データ
     * @return int 更新件数
     */
    protected function saveUketsukeInfo($db, $dataUketsukeInfoCol) {

        // 削除→登録する
        $sql = "
            DELETE
                FROM
                    seko_uketsuke_info
            WHERE
                    seko_no = :seko_no 
                ";
        $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo));
        foreach ($dataUketsukeInfoCol as $oneRow) {
            $except = array();
            array_push($except, 'tanto_nm');
            array_push($except, 'discuss_ymd');
            $oneRow['seko_no'] = $this->_sekoNo;
            // emptyToNull
            $oneRow['discuss_date'] = Msi_Sys_Utils::emptyToNull($oneRow['discuss_date']);
            $oneRow['discuss_time'] = Msi_Sys_Utils::emptyToNull($oneRow['discuss_time']);
            $oneRow['status_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['status_kbn']);
            $oneRow['level_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['level_kbn']);
            // 施行基本受付履歴登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_uketsuke_info", $oneRow, $except);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     * 施行受付履歴保存処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/04/16
     * @param Msi_Sys_Db $db db
     * @param array $dataUketsukeInfoCol 施行互助会加入者データ
     * @return int 更新件数
     */
    protected function saveUchiawaseInfo($db, $orgUpdateHistroyContents, $newdataUpdateHistoryContents) {

        $contents = self::UCHIAWASEINFO_ARRAY;
        $updateDate = Msi_Sys_Utils::getDatetimeYmdhs();
        $tanto_cd = App_Utils::getTantoCd();
        $cnt = 0;
        foreach ($contents as $value) {
            $oldContent = null;
            $newContent = null;
            $oldValueNm = null;
            $newValueNm = null;
            $insData = array();
            if (strlen($orgUpdateHistroyContents[$value['column']]) > 0) {
                $oldContent = $orgUpdateHistroyContents[$value['column']];
            }
            if (strlen($newdataUpdateHistoryContents[$value['column']]) > 0) {
                $newContent = $newdataUpdateHistoryContents[$value['column']];
            }
            // 変更があったときのみ登録する
            if ($oldContent != $newContent) {
                $seqNo = $this->getUchiawaseInfoMaxSeqNo();
                $insData['seko_no'] = $this->_sekoNo;
                $insData['seq_no'] = $seqNo + 1;
                $insData['update_ts'] = $updateDate;
                $insData['tanto_cd'] = $tanto_cd;
                $codeMst = DataMapper_CodeNmMst::find($db, array('code_kbn' => $value['title']));
                // コード名称の設定があればそれを使用する
                if (count($codeMst) > 0) {
                    if (isset($oldContent)) {
                        $oldKeyIndex = array_search($oldContent, array_column($codeMst, 'kbn_value_cd'));
                        $oldValueNm = $codeMst[$oldKeyIndex]['kbn_value_lnm'];
                    }
                    if (isset($newContent)) {
                        $newKeyIndex = array_search($newContent, array_column($codeMst, 'kbn_value_cd'));
                        $newValueNm = $codeMst[$newKeyIndex]['kbn_value_lnm'];
                    }
                    $insData['update_contents'] = $codeMst[0]['code_kbn_nm'] . '：' . $oldValueNm . '→' . $newValueNm;
                } else {
                    $insData['update_contents'] = $value['title'] . '：' . $oldContent . '→' . $newContent;
                }
                // 打合せ事項更新履歴登録SQL
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_uchiawase_info", $insData);
                $cnt += $db->easyExecute($sql, $param);
            }
        }
        return $cnt;
    }

    /**
     *
     * 打合せ事項更新履歴の連番最大値取得処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/04/17
     * @return string SQL
     */
    protected function getUchiawaseInfoMaxSeqNo() {

        $db = Msi_Sys_DbManager::getMyDb();
        $seq_no = 0;
        $sql = "
        SELECT MAX(seq_no) AS seq_no
        FROM seko_uchiawase_info
        WHERE seko_no = :seko_no
        AND delete_flg = 0
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        if (isset($select[0]['seq_no']) && strlen($select[0]['seq_no']) > 0) {
            $seq_no = $select[0]['seq_no'];
        }
        return $seq_no;
    }

    /**
     *
     * 施行基本論理削除処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/27
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return string SQL
     */
    protected function deleteSekokihon() {
        $sql = "
        UPDATE
            seko_kihon_info
        SET 
            delete_flg = 1
        WHERE
            seko_no = :seko_no
        AND delete_flg = 0
                ";
        return $sql;
    }

    /**
     *
     * 会員区分を求める。
     *
     * <AUTHOR> Sai
     * @since 2014/05/15
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param array $dataGojokaiInfo 施行互助会情報
     * @param array $dataGojokaiMemberCol 施行互助会加入情報
     * @return array[kaiin_cd]        => 会員区分コード
     *          array[kaiin_kbn]       => 会員区分
     */
    protected function calcKaiinKbn($dataGojokaiInfo, $dataGojokaiMemberCol) {
        $kaiin = array();
        $kaiin_cd = null; // 会員区分コード
        $kaiin_kbn = null; // 会員区分
        $isGojokai = false;
        foreach ($dataGojokaiMemberCol as $value) {
            if ($value['yoto_kbn'] !== '4' && $value['yoto_kbn'] !== '12') { // 4：使用しない　12：解約充当
                $isGojokai = true;
                break;
            }
        }
        if ($isGojokai || isset($dataGojokaiInfo['kanyu_dantai_kbn'])) {
            $kaiin_cd = '1'; // 1:互助会/加入団体
            $kaiin_kbn = '1'; // 1:互助会/加入団体
            $kaiin['kaiin_cd'] = $kaiin_cd; // 会員区分コード
            $kaiin['kaiin_kbn'] = $kaiin_kbn; // 会員区分
        }
        return $kaiin;
    }

    /**
     *
     * 伝票の部門コードを更新する
     *
     * <AUTHOR> Sai
     * @since 2014/3/19
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @param string $denpyoNo 伝票番号
     * @param string $bumonCd 部門コード
     * @return int 更新件数
     */
    protected function updateDenpyoBumoncd($db, $denpyoNo, $bumonCd) {
        $oldBumonCd = $this->_selectSekoKihon['bumon_cd'];
        // 受注明細テーブル更新SQL
        $sql1 = <<< END_OF_SQL
UPDATE 
    juchu_denpyo_msi
SET
    bumon_cd = :bumon_cd
WHERE
        denpyo_no = :denpyo_no 
    AND bumon_cd = :old_bumon_cd
    AND delete_flg = 0
END_OF_SQL;
        // 受注テーブル更新SQL
        $sql2 = <<< END_OF_SQL
UPDATE 
    juchu_denpyo
SET
    bumon_cd = :bumon_cd
WHERE
        denpyo_no = :denpyo_no 
    AND delete_flg = 0
END_OF_SQL;
        $cnt = $db->easyExecute($sql1, array('bumon_cd' => $bumonCd, 'old_bumon_cd' => $oldBumonCd, 'denpyo_no' => $denpyoNo));
        $cnt += $db->easyExecute($sql2, array('bumon_cd' => $bumonCd, 'denpyo_no' => $denpyoNo));

        // 売上明細テーブル更新SQL
        $sql3 = <<< END_OF_SQL
UPDATE 
    uriage_denpyo_msi
SET
    bumon_cd = :bumon_cd
WHERE
        uri_den_no = :uri_den_no 
    AND bumon_cd = :old_bumon_cd
    AND delete_flg = 0
END_OF_SQL;
        // 売上テーブル更新SQL
        $sql4 = <<< END_OF_SQL
UPDATE 
    uriage_denpyo
SET
    bumon_cd = :bumon_cd
WHERE
        uri_den_no = :uri_den_no 
    AND delete_flg = 0
END_OF_SQL;

        $uriDenpyoNo = $this->getUriagedenpyoNo();
        if ($uriDenpyoNo) {
            $cnt += $db->easyExecute($sql3, array('bumon_cd' => $bumonCd, 'old_bumon_cd' => $oldBumonCd, 'uri_den_no' => $uriDenpyoNo));
            $cnt += $db->easyExecute($sql4, array('bumon_cd' => $bumonCd, 'uri_den_no' => $uriDenpyoNo));
        }
        return $cnt;
    }

    /**
     *
     * 台帳番号採番処理
     *
     * <AUTHOR> Sai
     * @since 2014/2/18
     * @version 2014/05/02 Juchu_JuchuAbstractより引越し
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param string $sougiymd 葬儀日
     * @return array 台帳番号
     */
    protected function getAutoDaichoNo($sougiymd) {
        $daicho = array();
        $db = Msi_Sys_DbManager::getMyDb();
        // 台帳番号（エリア）
        $area_cd = App_Utils::getSessionData('ctxt_area_cd');
        if (!empty($area_cd)) {
            $daicho['daicho_no_eria'] = substr($area_cd, 0, 2);
        } else {
            $daicho['daicho_no_eria'] = null;
        }
        $mm = App_DateCalc::normMM($sougiymd);
        if (isset($mm)) {
            // 台帳番号（月）
            $daicho['daicho_no_mm'] = $mm;
        } else {
            $select1 = $db->easySelOne("SELECT TO_CHAR(CURRENT_DATE,'MM') AS daicho_no_mm");
            // 台帳番号（月）
            $daicho['daicho_no_mm'] = $select1['daicho_no_mm'];
        }

        $sql = "
            SELECT
                COALESCE(MAX(daicho_no_seq), '0') AS daicho_no_seq
            FROM
                seko_kihon_info
            WHERE
                    daicho_no_eria = :daicho_no_eria
                AND daicho_no_mm = :daicho_no_mm
                AND delete_flg = 0
                ";
        $select2 = $db->easySelOne($sql, array('daicho_no_eria' => $daicho['daicho_no_eria'], 'daicho_no_mm' => $daicho['daicho_no_mm']));
        if (count($select2) > 0) {
            $daichoNoSeqInt = (int) $select2['daicho_no_seq'] + 1;
            $daichoNoSeq = str_pad($daichoNoSeqInt, 3, '0', STR_PAD_LEFT);
            $daicho['daicho_no_seq'] = $daichoNoSeq;
        } else {
            $daicho['daicho_no_seq'] = '0001';
        }
        return $daicho;
    }

    /**
     * 故人名添付ファイル名登録処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/24
     * @param resource $file 故人名添付ファイル
     * @return int 更新件数
     */
    public function writeBlob($file) {
        $db = Msi_Sys_DbManager::getMyDb();
        $oid = $db->writeBlob($file);
        $db->commit();
        $data = array(
            'oid' => $oid,
            'status' => 'OK',
            'msg' => ('更新しました'),
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 画面より削除ボタン押下による施行基本論理削除処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/27
     * @param array $req リクエスト
     */
    public function delete($req) {
        $result = '';
        $msg = '';
        $this->_sekoNo = $req->getPost('seko_no');
        // 施行基本情報を設定する
        $this->setInitParam();
        $db = Msi_Sys_DbManager::getMyDb();
        // 互助会情報で当社のコース施行もしくは金額充当があればエラー
        $gojoData = DataMapper_SekoGojokaiMember::find($db, array('seko_no'=> $this->_sekoNo, 'kaiin_info_kbn' => '1', '__raw1' => "yoto_kbn IN (1,3)"));
        if (count($gojoData) > 0) {
            $data = array(
                'status' => 'NG',
                'msg' => "互助会情報があるため削除できません。互助会情報を削除してください。",
            );
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 施設予約情報があればエラー(安置は除外する)
        $sisetsuYoyaku = DataMapper_SisetsuYoyaku::find2($db, array('yoyaku_seko_no' => $this->_sekoNo, '__raw_bunrui' => "yoyaku_bunrui <> 0"));
        if (count($sisetsuYoyaku) > 0) {
            $data = array(
                'status' => 'NG',
                'msg' => "施設予約情報があるため削除できません。施設予約情報を削除してください。",
            );
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // アフターと紐づけがある場合はエラー
        $juchuDen = DataMapper_JuchuDenpyo::find($db, array('ref_seko_no' => $this->_sekoNo));
        if (count($juchuDen) > 0) {
            $den_nos = implode(',', array_map(function($el) {
                        return $el['denpyo_no'];
                    }, $juchuDen));
            $data = array(
                'status' => 'NG',
                'msg' => sprintf("関連付けされているアフター受注が存在するため削除できません。アフター受注No(%s)", $den_nos),
            );
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $hasDenpyo = $this->hasJuchuDenpyoMsi();
        if (!$hasDenpyo) {
            $sql = $this->deleteSekokihon();
            // 施行基本情報を論理削除する
            $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo));
            if ($cnt > 0) {
                App_Utils::deleteTantoSekoInfo($this->_sekoNo, $this->_moushiKbn);
                App_Utils::setSessionData('seko_no_mitsu', null);
                App_Utils::setSessionData('seko_no', null);
                App_Utils::setSessionData('seko_no_mitsu_houji', null);
                App_Utils::setSessionData('seko_no_houji', null);
                $db->commit();
                $result = 'OK';
                $msg = '削除処理が正常に終了しました。';
            } else {
                $msg = '削除処理が失敗しました。';
            }
        } else {
            $msg = '見積明細が存在するため、削除することができません。';
        }
        $data = array(
            'status' => $result,
            'msg' => $msg,
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 日付を年・月・日にスライスする
     *
     * <AUTHOR> Sai
     * @since 2014/07/08
     * @param string $ymd 日付
     * @return array 年・月・日
     */
    private function sliceYmd($ymd) {
        $match = $y = $m = $d = null;
        if (preg_match('/^(\d{1,4})\D(\d{1,2})\D(\d{1,2}+)/', $ymd, $match)) {
            list( $y, $m, $d ) = array_slice($match, 1, 3);
        }
        return array($y, $m, $d);
    }

    /**
     * 和暦を西暦に変換するする
     *
     * <AUTHOR> Sai
     * @since 2014/07/08
     * @param string $gen 元号
     * @param string $y 年
     * @param string $m 月
     * @param string $d 日
     */
    private function wareki2seireki($gen, $y, $m, $d) {
        $sereki = null;
        if (isset($gen) && isset($y) && isset($m) && isset($d)) {
            $sereki = Msi_Sys_Utils::warekiToSeireki($gen, $y, $m, $d);
        }
        return $sereki;
    }

    /**
     * 施行互助会加入者重複チェック処理 
     *
     * <AUTHOR> Sai
     * @since 2014/07/17
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return boolean 成功可否
     */
    protected function checkGojokaiMember($dataGojokaiMemberCol) {
        $msg = null;
//        if (count($dataGojokaiMemberCol) > 0) {
//            foreach ($dataGojokaiMemberCol as $oneRow1) {
//                $count = 0;
//                $kainNo1 = $oneRow1['kain_no'];
//                foreach ($dataGojokaiMemberCol as $oneRow2) {
//                    $kainNo2 = $oneRow2['kain_no'];
//                    if ($kainNo1 === $kainNo2) {
//                        $count++;
//                    }
//                }
//                if ($count > 1) {
//                    $msg = "加入員番号が重複しているため、更新することができません。";
//                }
//            }
//        }
        return $msg;
    }

    /**
     * 明細データチェック処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @param db $db 
     * @param array $dataSekoKihon 施行基本データ
     * @return boolean 成功可否
     */
    protected function checkShohinBumon($db, $dataSekoKihon) {

        // 明細の存在チェック
        $sql = "
            SELECT m.seko_no
            FROM juchu_denpyo d
            INNER JOIN juchu_denpyo_msi m
                ON m.denpyo_no = d.denpyo_no
                AND m.delete_flg = 0
            WHERE d.seko_no = :seko_no
                AND d.data_kbn IN (1,2,4)   -- 葬儀・法事・供花供物のみ
                AND d.delete_flg = 0
                        ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        // 明細が存在しなければスルー
        if (count($select) == 0) {
            return true;
        }
        // 見積式場が設定されていなければエラー
        if (strlen($dataSekoKihon['est_shikijo_cd']) === 0) {
            $data['status'] = 'NG';
            $data['msg'] = '商品が登録されているため見積式場を未設定にはできません。';
            Msi_Sys_Utils::outJson($data);
            return false;
        }
        // 現在登録可能な商品以外の商品部門コードが存在していたらエラー
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $dataSekoKihon['est_shikijo_cd']));
        $bumonCds = '';
        if ($bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
            //自部門商品＋親部門商品(親部門はさかのぼる)
            $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $dataSekoKihon['est_shikijo_cd'], null);
            foreach ($allOyaBumon as $oneBumon) {
                if ($oneBumon['bumon_shohin_sel_kbn'] === '1') {
                    $bumonCds .= "'" . $oneBumon['ko_bumon_cd'] . "',";
                }
            }
        } else {
            // 親部門商品(親部門はさかのぼる)のみ
            $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $dataSekoKihon['est_shikijo_cd'], null);
            foreach ($allOyaBumon as $oneBumon) {
                if ($oneBumon['bumon_kbn'] === '0') {
                    $bumonCds .= "'" . $oneBumon['bumon_cd'] . "',";
                }
            }
        }
        $bumonWhere = "AND m.shohin_bumon_cd NOT IN (" . trim($bumonCds, ',') . ")";
        $sql = "
            SELECT m.shohin_cd
            FROM juchu_denpyo d
            INNER JOIN juchu_denpyo_msi m
                ON m.denpyo_no = d.denpyo_no
                $bumonWhere
                AND m.delete_flg = 0
            WHERE d.seko_no = :seko_no
                AND d.data_kbn IN (1,2,4)   -- 葬儀・法事・供花供物のみ
                AND d.delete_flg = 0
                        ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        if (count($select) > 0) {
            $data['status'] = 'NG';
            $data['msg'] = '別式場の商品が登録されているため見積式場を変更できません。';
            Msi_Sys_Utils::outJson($data);
            return false;
        }
        return true;
    }

    /**
     * ステータス区分チェック処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @param array $dataSekoKihon 施行基本データ
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return boolean 成功可否
     */
    protected function checkStatusKbn($dataSekoKihon) {

        $msg = null;
        if (!isset($this->_selectSekoKihon['status_kbn'])) {
            return true;
        }
        if ($dataSekoKihon['status_kbn'] != $this->_selectSekoKihon['status_kbn']) {
            $msg = 'ステータスが変更されています。画面を更新してください。';
        }
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return false;
        }
        return true;
    }

    /**
     * oidチェック処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @param array $dataSekoKihonFree 施行基本フリーデータ
     * @return boolean 成功可否
     */
    protected function checkOid($dataSekoKihonFree) {

        $msg = null;
        if ($dataSekoKihonFree['f_free1']) {
            $selectImgPhoto = $this->getOidData($dataSekoKihonFree['f_free1']);
            if (Msi_Sys_Utils::myCount($selectImgPhoto) == 0) {
                $msg = '添付ファイルが変更されています。画面を更新してください。';
            }
        }
        if ($dataSekoKihonFree['f_free2']) {
            $selectImgPhoto = $this->getOidData($dataSekoKihonFree['f_free2']);
            if (Msi_Sys_Utils::myCount($selectImgPhoto) == 0) {
                $msg = '添付ファイルが変更されています。画面を更新してください。';
            }
        }
        if ($dataSekoKihonFree['f_free3']) {
            $selectImgPhoto = $this->getOidData($dataSekoKihonFree['f_free3']);
            if (Msi_Sys_Utils::myCount($selectImgPhoto) == 0) {
                $msg = '添付ファイルが変更されています。画面を更新してください。';
            }
        }
        if ($dataSekoKihonFree['f_free4']) {
            $selectImgPhoto = $this->getOidData($dataSekoKihonFree['f_free4']);
            if (Msi_Sys_Utils::myCount($selectImgPhoto) == 0) {
                $msg = '添付ファイルが変更されています。画面を更新してください。';
            }
        }
        if ($dataSekoKihonFree['f_free5']) {
            $selectImgPhoto = $this->getOidData($dataSekoKihonFree['f_free5']);
            if (Msi_Sys_Utils::myCount($selectImgPhoto) == 0) {
                $msg = '添付ファイルが変更されています。画面を更新してください。';
            }
        }
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return false;
        }
        return true;
    }

    /**
     * 会員区分チェック処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @param array $dataSekoKihon 施行基本データ
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return boolean 成功可否
     */
    protected function checkKaiinKbn($dataSekoKihon, $dataGojokaiMemberCol) {

        $db = Msi_Sys_DbManager::getMyDb();
        $msg = null;
        $simple_yoto_kbn_arr = array();    // 用途区分ごとの配列(件数)
        $yoto_kbn_arr = array();    // 用途区分+互助会コースごとの配列
        $gojo_cnt_arr = array();    // コース施行の互助会コースごとの配列(件数)
        $plan_cnt_arr = array();    // プラン施行の互助会コースごとの配列(件数)
        // 見積式場が設定されていなければチェックしない
        if (strlen($dataSekoKihon['est_shikijo_cd']) === 0) {
            return $msg;
        }
        foreach ($dataGojokaiMemberCol as $one) {
            switch ($dataSekoKihon['kaiin_kbn']) {
//                case static::KAIIN_KBN_GOJO :   // 互助会
//                    // 区分が他社で用途が金額充当OR使用しない以外に設定されているものはエラー
//                    if ($one['kaiin_info_kbn'] == self::KAIIN_INFO_TASYA && ($one['yoto_kbn'] != self::YOTO_KBN_JUTO && $one['yoto_kbn'] != self::YOTO_KBN_NOUSE)) {
//                        $msg = '互助会情報の入力に誤りがあります。会員情報/加入状況の「他社」区分では用途に「金額充当」または「使用しない」を選択してください。';
//                        break;
//                    } else if ($one['kaiin_info_kbn'] == self::KAIIN_INFO_OM && ($one['yoto_kbn'] != self::YOTO_KBN_PLAN && $one['yoto_kbn'] != self::YOTO_KBN_COURSE && $one['yoto_kbn'] != self::YOTO_KBN_NOUSE)) {
//                        $msg = '互助会情報の入力に誤りがあります。会員情報/加入状況の「OM」区分では用途に「コース施行」または「使用しない」を選択してください。';
//                        break;
//                    }
//                    break;
//                case static::KAIIN_KBN_IPAN :   // 一般
//                    // 区分が他社で用途が解約指図払いOR使用しない以外に設定されているものはエラー
//                    if ($one['kaiin_info_kbn'] == self::KAIIN_INFO_TASYA && ($one['yoto_kbn'] != self::YOTO_KBN_KAIYAKU && $one['yoto_kbn'] != self::YOTO_KBN_NOUSE)
//                    ) {
//                        $msg = '互助会情報の入力に誤りがあります。会員情報/加入状況の「他社」区分では用途に「使用しない」を選択してください。';
//                        break;
//                    }
//                    break;
            }
            $key = $one['yoto_kbn'];
            $key2 = $one['gojokai_cose_cd'];
            $yoto_kbn_arr[$key][$one['yoto_kbn'] . '-' . $one['gojokai_cose_cd']] = $one['yoto_kbn'] . '-' . $one['gojokai_cose_cd'];
            if (array_key_exists($key, $simple_yoto_kbn_arr)) {
                $simple_yoto_kbn_arr[$key]['cnt'] ++;
            } else {
                $simple_yoto_kbn_arr[$key]['cnt'] = 1;
            }
            if ($one['yoto_kbn'] == static::YOTO_KBN_COURSE) {
                if (array_key_exists($key2, $gojo_cnt_arr)) {
                    $gojo_cnt_arr[$key2]['cnt'] ++;
                } else {
                    $gojo_cnt_arr[$key2]['cnt'] = 1;
                }
            }
            if ($one['yoto_kbn'] == static::YOTO_KBN_PLAN) {
                if (array_key_exists($key2, $plan_cnt_arr)) {
                    $plan_cnt_arr[$key2]['cnt'] ++;
                } else {
                    $plan_cnt_arr[$key2]['cnt'] = 1;
                }
            }
        }
        if (isset($msg)) {
            return $msg;
        }
        // 会員区分ごとにチェック処理を行う
        switch ($dataSekoKihon['kaiin_kbn']) {
            case static::KAIIN_KBN_GOJO :   // 互助会
//                if (count($dataGojokaiMemberCol) == 0) {
//                    return;
//                }
//                if (isset($yoto_kbn_arr[static::YOTO_KBN_COURSE]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_COURSE]) > 1) {
//                    $msg = '互助会情報の入力に誤りがあります。異なる互助会コースの用途にコース施行を選択する事はできません。';
//                    break;
//                }
                // 見積未確定の場合はスルー
                if (!$this->isMitsuKakutei()) {
                    break;
                }
                if ($dataSekoKihon['moushi_kbn'] != '2') {
                    if (Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_COURSE]) ==0 && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_PLAN]) == 0 && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_JUTO]) == 0) {
                        $msg = '互助会情報の入力に誤りがあります。会員情報/加入状況の用途には「コース施行」を選択してください。';
                        break;
                    }
                }
//                if (isset($yoto_kbn_arr[static::YOTO_KBN_KAIYAKU]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_KAIYAKU]) > 0) {
//                    $msg = '互助会情報の入力に誤りがあります。会員情報/加入状況の用途「解約指図払い」は一般の場合のみ指定できます。';
//                    break;
//                }
                if (count($dataGojokaiMemberCol) == 0) {
                    $msg = '互助会情報が設定されていません。会員情報/加入状況の項目を設定してください。';
                    break;
                }
                if (isset($yoto_kbn_arr[static::YOTO_KBN_COURSE]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_COURSE]) == 0 && isset($yoto_kbn_arr[static::YOTO_KBN_PLAN]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_PLAN]) == 0 && isset($yoto_kbn_arr[static::YOTO_KBN_JUTO]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_JUTO]) == 0 && isset($yoto_kbn_arr[static::YOTO_KBN_NOUSE]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_NOUSE]) == 0) {
                    $msg = '互助会情報が設定されていません。会員情報/加入状況の項目を設定してください。';
                    break;
                }
                break;
            case static::KAIIN_KBN_IPAN :   // 一般
                if ($dataSekoKihon['moushi_kbn'] == '2') {
                    if ((isset($yoto_kbn_arr[static::YOTO_KBN_COURSE]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_COURSE]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_PLAN]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_PLAN]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_JUTO]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_JUTO]) > 0 || (isset($yoto_kbn_arr[static::YOTO_KBN_NOUSE]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_NOUSE]) > 0))) {
                        $msg = '会員区分が互助会以外で互助会利用の入力があります。会員情報/加入状況を確認してください。';
                        break;
                    }
                } else {
                    if ((isset($yoto_kbn_arr[static::YOTO_KBN_COURSE]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_COURSE]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_PLAN]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_PLAN]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_JUTO]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_JUTO]) > 0)) {
                        $msg = '会員区分が互助会以外で互助会利用の入力があります。会員情報/加入状況の用途を確認してください。';
                        break;
                    }
                }
                break;
            case static::KAIIN_KBN_CO :    // 企業
                // 見積未確定の場合はスルー
                if (!$this->isMitsuKakutei() && count($dataGojokaiMemberCol) == 0) {
                    break;
                }
                if ($dataSekoKihon['moushi_kbn'] == '2') {
                    if ((isset($yoto_kbn_arr[static::YOTO_KBN_COURSE]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_COURSE]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_PLAN]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_PLAN]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_JUTO]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_JUTO]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_KAIYAKU]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_KAIYAKU]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_PLAN]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_PLAN]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_JUTO]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_JUTO]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_NOUSE]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_NOUSE]) > 0)) {
                        $msg = '会員区分が互助会以外で互助会利用の入力があります。会員情報/加入状況を確認してください。';
                        break;
                    }
                } else {
                    if ((isset($yoto_kbn_arr[static::YOTO_KBN_COURSE]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_COURSE]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_PLAN]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_PLAN]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_JUTO]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_JUTO]) > 0) || (isset($yoto_kbn_arr[static::YOTO_KBN_KAIYAKU]) && Msi_Sys_Utils::myCount($yoto_kbn_arr[static::YOTO_KBN_KAIYAKU]) > 0)) {
                        $msg = '会員区分が互助会以外で互助会利用の入力があります。会員情報/加入状況の用途を確認してください。';
                        break;
                    }
                }
                break;
        }
        if (isset($msg)) {
            return $msg;
        }
        // コース施行がある場合は互助会コースの件数チェックをする
//        if (count($gojo_cnt_arr) > 0) {
//            foreach ($gojo_cnt_arr as $gojo_cose => $val) {
//                $cdNm = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => self::CODE_GOJOKAI_COSE, 'kbn_value_cd_num' => $gojo_cose));
//                if (isset($cdNm['biko'])) {
//                    $biko_ary = explode(',', $cdNm['biko']);
//                    $gojo_cnt = 1;
//                    if (isset($biko_ary[0]) && strlen($biko_ary[0]) > 0) {
//                        $gojo_cnt = $biko_ary[0];
//                    }
//                    if ($val['cnt'] > $gojo_cnt) {
//                        $msg = sprintf("互助会情報の入力に誤りがあります。同一互助会コースを規定口数以上選択する事はできません。(%s)", $cdNm['kbn_value_cd']);
//                        break;
//                    }
//                } else {
//                    // NULLの場合は同一コースは一件のみ
//                    if ($val['cnt'] > 1) {
//                        $msg = sprintf("互助会情報の入力に誤りがあります。同一互助会コースを複数選択する事はできません。(%s)", $cdNm['kbn_value_cd']);
//                        break;
//                    }
//                }
//            }
//        }
        if (isset($msg)) {
            return $msg;
        }
        // プラン施行がある場合は互助会コースの件数チェックをする
//        if (count($plan_cnt_arr) > 0) {
//            foreach ($plan_cnt_arr as $gojo_cose => $val) {
//                $cdNm = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => self::CODE_GOJOKAI_COSE, 'kbn_value_cd_num' => $gojo_cose));
//                if (isset($cdNm['biko'])) {
//                    $biko_ary = explode(',', $cdNm['biko']);
//                    $plan_cnt = 1;
//                    if (isset($biko_ary[1]) && strlen($biko_ary[1]) > 0) {
//                        $plan_cnt = $biko_ary[1];
//                    }
//                    if ($val['cnt'] > $plan_cnt) {
//                        $msg = sprintf("互助会情報の入力に誤りがあります。同一互助会コースを規定口数以上選択する事はできません。(%s)", $cdNm['kbn_value_cd']);
//                        break;
//                    }
//                } else {
//                    // NULLの場合は同一コースは一件のみ
//                    if ($val['cnt'] > 1) {
//                        $msg = sprintf("互助会情報の入力に誤りがあります。同一互助会コースを複数選択する事はできません。(%s)", $cdNm['kbn_value_cd']);
//                        break;
//                    }
//                }
//            }
//        }
        return $msg;
    }

    /**
     * 加入日チェック処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @param array $dataSekoKihon 施行基本データ
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @param array $dataNiteiCol 施行日程データ
     * @return boolean 成功可否
     */
    protected function checkKanyu($dataSekoKihon, $dataGojokaiMemberCol, $dataNiteiCol) {

        $db = Msi_Sys_DbManager::getMyDb();
        $alertMsg = null;
        $errMsg = null;
        $flg = false;
        $yakkan_ymd = self::DEF_YAKKAN_YMD;
        foreach ($dataGojokaiMemberCol as $one) {
            // 用途区分が「使用しない」があればチェック
            if ($one['yoto_kbn'] != self::YOTO_KBN_NOUSE) {
                // 施行日程の死亡日時の日付と場所区分が入力されてるかチェック
                $keyIndex = array_search(self::NITEI_KBN_DEATH, array_column($dataNiteiCol, 'nitei_kbn'));
                $niteiDeath = $dataNiteiCol[$keyIndex];
                if (!isset($niteiDeath['nitei_ymd']) || strlen($niteiDeath['nitei_ymd']) == 0) {
                    return array($flg, $alertMsg, '施行情報の死亡日時の日付が入力されていません。');
                }
                if (!isset($niteiDeath['basho_kbn']) || strlen($niteiDeath['basho_kbn']) == 0) {
                    return array($flg, $alertMsg, '施行情報の死亡日時の場所区分が入力されていません。');
                }
                // 死亡後加入かチェック
                if ($one['kanyu_dt'] > $niteiDeath['nitei_ymd']) {
                    // 場所区分が警察かどうかチェック
                    if ($niteiDeath['basho_kbn'] == self::DEATH_BASHO_KBN) {
                        return array(true, '死亡後加入ですが、死亡場所が警察となります。', $errMsg);
                    } else {
                        // 加入日が約款規定日以前(加入日＜約款規定日)かチェック
                        $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $dataSekoKihon['bumon_cd']));
                        $cdNm = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => self::CODE_KBN_GOJOKAI_YAKKAN, 'kbn_value_cd' => $bumonData[0]['oya_bumon_cd']));
                        if (Msi_Sys_Utils::myCount($cdNm) > 0) {
                            $yakkan_ymd = $cdNm['kbn_value_snm'];
                        }
                        if ($one['kanyu_dt'] < $yakkan_ymd) {
                            return array(true, '死亡後加入ですが、約款規定日以前となります。', $errMsg);
                        } else {
                            return array(false, $alertMsg, '約款規定日以降の死亡後加入です。');
                        }
                    }
                }
            }
        }
        return array($flg, $alertMsg, $errMsg);
    }

    /**
     * 契約先チェック処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @param db $db 
     * @param array $dataSekoKihon 施行基本データ
     * @param array $dataSekoKeiyakusakiInfo 施行契約先情報データ
     * @return boolean 成功可否
     */
    protected function checkKeiyakusaki($db, $dataSekoKihon, $dataSekoKeiyakusakiInfo) {

        $msg = null;
        // 会員区分ごとにチェック処理を行う
        switch ($dataSekoKihon['kaiin_kbn']) {
            case static::KAIIN_KBN_GOJO :   // 互助会
//                if (isset($dataSekoKeiyakusakiInfo['partner_cd']) && strlen($dataSekoKeiyakusakiInfo['partner_cd']) > 0) {
//                    $msg = '会員情報/その他加入確認の契約先番号が設定されています。';
//                    break;
//                }
                break;
            case static::KAIIN_KBN_IPAN :   // 一般
                if (isset($dataSekoKeiyakusakiInfo['partner_cd']) && strlen($dataSekoKeiyakusakiInfo['partner_cd']) > 0) {
                    $msg = '会員情報/その他加入確認の契約先番号が設定されています。';
                    break;
                }
                break;
            case static::KAIIN_KBN_CO :    // 企業
                // 見積未確定の場合はスルー
                if (!$this->isMitsuKakutei()) {
                    break;
                }
                // 見積式場が設定されていなければチェックしない
                if (strlen($dataSekoKihon['est_shikijo_cd']) === 0) {
                    break;
                }
                if (strlen($dataSekoKeiyakusakiInfo['partner_cd']) == 0) {
                    $msg = '会員情報/その他加入確認の契約先番号が設定されていません。';
                    break;
                }
                // 見積式場の親部門と契約先の会社が一致しているかチェック
                // 契約先が設定されていて見積式場が未設定の場合はエラー
//                if (strlen($dataSekoKeiyakusakiInfo['partner_cd']) > 0 && strlen($dataSekoKihon['est_shikijo_cd']) === 0) {
//                    $msg = '見積式場が設定されていません。';
//                    break;
//                }
//                $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $dataSekoKihon['est_shikijo_cd']));
//                $cdData = DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_KEIYAKUSAKI_WARI_KBN, 'kbn_value_cd_num' => $bumonData[0]['oya_bumon_cd']));
//                if (count($cdData) == 0) {
//                    $msg = 'マスタに割引区分の設定がされていません。';
//                    break;
//                }
//                $matchFlg = false;
//                foreach ($cdData as $value) {
//                    $keiyakusakiData = DataMapper_KeiyakuSaki::findForDlg($db, array('partner_cd' => $dataSekoKeiyakusakiInfo['partner_cd'], 'sougi_wari_kbn' => $value['kbn_value_cd']));
//                    if (count($keiyakusakiData) > 0) {
//                        $matchFlg = true;
//                    }
//                }
//                if (!$matchFlg) {
//                    $msg = '会員情報/その他加入確認の契約先に設定されている会社と見積式場の会社が一致しません。';
//                    break;
//                }
//                break;
        }
        return $msg;
    }

    /**
     *
     * 伝票の担当コードを更新する
     *
     * <AUTHOR> Sai
     * @since 2014/9/2
     * @param Msi_Sys_Db $db db
     * @param string $denpyoNo 伝票番号
     * @param string $bumonCd 部門コード
     * @return int 更新件数
     */
    protected function updateDenpyoDantocd($db, $sekoTantoCd) {
        $cnt = 0;
        $oldSekoTantoCd = $this->_sekoTantoCd;
        if ($sekoTantoCd != $oldSekoTantoCd) {
            $denpyoNo = $this->getJuchudenpyoNo();
            $uriDenpyoNo = $this->getUriagedenpyoNo();
            // 受注テーブル更新SQL
            $sql1 = "
            UPDATE 
                juchu_denpyo
            SET
                tanto_cd = :tanto_cd
            WHERE
                    denpyo_no = :denpyo_no 
                AND delete_flg = 0";
            // 受注テーブル更新SQL
            $sql2 = "
            UPDATE 
                uriage_denpyo
            SET
                tanto_cd = :tanto_cd
            WHERE
                    uri_den_no = :uri_den_no 
                AND delete_flg = 0";
            if (!empty($denpyoNo) && !empty($sekoTantoCd)) {
                $cnt += $db->easyExecute($sql1, array('denpyo_no' => $denpyoNo, 'tanto_cd' => $sekoTantoCd));
            }
            if (!empty($uriDenpyoNo) && !empty($sekoTantoCd)) {
                $cnt += $db->easyExecute($sql2, array('uri_den_no' => $uriDenpyoNo, 'tanto_cd' => $sekoTantoCd));
            }
        }
        return $cnt;
    }

    /**
     * GetCodeNameMst(コード名称マスタを取得）
     * 
     * <AUTHOR> Sai
     * @since      2015/01/16
     * @param   $db DB
     * @param   string $code_kbn
     * @return  array $aryData
     */
    protected function GetCodeNameMst2($db, $code_kbn) {
        // コード名称マスタ
        $select = $db->easySelect(<<< END_OF_SQL
SELECT
    code_kbn			-- コード区分
   ,code_kbn_nm         -- コード区分名
   ,kbn_value_cd		-- 区分値コード
   ,kbn_value_cd_num	-- 区分値コード数値
   ,kbn_value_lnm		-- 区分値正式名
   ,kbn_value_snm		-- 区分値簡略名
   FROM    CODE_NM_MST
   WHERE   delete_flg = 0
   AND     code_kbn    =   :code_kbn	-- コード区分
   ORDER BY kbn_value_cd            
END_OF_SQL
                , array($code_kbn));

        return $select;
    }

    /**
     *
     * 顧客基本情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2015/1/22
     * @return string 顧客コード
     */
    protected function getCustomerCd() {
        $customerCd = '';
        $select = $this->getCustomerBaseInfo();
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $customerCd = $select['customer_cd'];
        }
        return $customerCd;
    }

    /**
     *
     * 顧客基本情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2015/1/22
     * @return array 顧客基本情報
     */
    protected function getCustomerBaseInfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                cbi.*
                ,TO_CHAR(cbi.ap_ymd, 'YYYY/MM/DD') AS ap_date
                ,CASE WHEN cbi.ap_inp_kbn = 0 THEN TO_CHAR(cbi.ap_ymd, 'HH24:MI') 
                ELSE NULL END AS ap_time
                ,TO_CHAR(cbi.consult_ymd, 'YYYY/MM/DD') AS consult_date
                ,CASE WHEN cbi.consult_inp_kbn = 0 THEN TO_CHAR(cbi.consult_ymd, 'HH24:MI') 
                ELSE NULL END AS consult_time
                ,cbi.text_free1 AS nyukai_point
                ,cbi.text_free2 AS minyukai_reason
                ,cbi.text_free3 AS syanai_renrakusaki
                ,ctm.tanto_nm AS consult_tanto_nm
            FROM
                customer_base_info cbi
            LEFT JOIN tanto_mst ctm
                ON ctm.tanto_cd = cbi.consult_tanto_cd
                AND ctm.delete_flg = 0
            WHERE
                    cbi.delete_flg = 0
                AND cbi.seko_no = :seko_no
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     *
     * 施行基本情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/30
     * @return array 施行基本情報
     */
    protected function getSekoKihon() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            k.seko_no           -- 施行番号
            ,k.daicho_no_eria       
            ,TRIM(k.daicho_no_mm) AS daicho_no_mm
            ,k.daicho_no_seq        
            ,k.bumon_cd         -- 部門コード
            ,k.moushi_cd        -- 申込コード
            ,k.moushi_kbn       -- 申込区分
            ,k.moushi_code_kbn  -- 申込区分コード区分
            ,k.main_pt_cd       -- 基本パターンコード
            ,k.main_pt_kbn      -- 基本パターン区分
            ,k.gojokai_kbn      -- 互助会区分
            ,k.sougi_cd         -- 葬儀コード
            ,k.sougi_kbn        -- 葬儀区分
            ,k.sougi_code_kbn   -- 葬儀区分コード区分
            ,k.p_info_cd        -- 個人情報保護コード
            ,k.p_info_kbn       -- 個人情報保護区分
            ,k.p_info_code_kbn       -- 個人情報保護区分コード区分
            ,k.kaiin_cd         -- 会員コード
            ,k.kaiin_kbn        -- 会員区分
            ,k.kaiin_code_kbn        -- 会員区分コード区分
            ,k.uketuke_tanto_cd -- 受付担当者コード
            ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
            ,k.seko_tanto_cd    -- 施行担当者コード
            ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
            ,k.k_nm             -- 故人名
            ,k.k_last_nm             -- 故人苗字
            ,k.k_first_nm             -- 故人名前
            ,k. k_last_nm AS k_last_nm_readonly             -- 故人苗字(表示用)
            ,k. k_first_nm AS k_first_nm_readonly             -- 故人名前(表示用)
            ,k.k_file_nm        -- 故人名添付ファイル
            ,k.k_sex_cd         -- 性別コード
            ,k.k_sex_code_kbn         -- 性別コード区分
            ,k.k_sex_kbn        -- 性別区分
            ,k.k_haigu_cd       -- 配偶者コード
            ,k.k_haigu_kbn      -- 配偶者区分
            ,k.k_haigu_code_kbn -- 配偶者区分コード区分
            ,k.k_knm            -- 故人カナ名
            ,k.k_last_knm             -- 故人苗字カナ
            ,k.k_first_knm             -- 故人名前カナ
            ,k.k_gengo          -- 生年月日元号
            ,k.k_birth_year          -- 誕生年
            ,k.k_birth_month          -- 誕生月
            ,k.k_birth_day          -- 誕生日
            ,k.k_seinengappi_ymd    -- 生年月日
            ,k.k_nenrei_man     -- 故人年齢
            ,k.k_nenrei_kyounen -- 享年
            ,k.k_height -- 故人身長
            ,k.k_weight -- 故人体重
            ,k.v_free2 AS cause_death   -- 死因
            ,k.v_free3 AS infection_txt
            ,k.v_free4 AS seika_contact
            ,k.v_free5 AS form_tel
            ,k.v_free6 AS form_fax
            ,k.kg_yubin_no      -- 現住所郵便番号
            ,k.kg_addr1         -- 現住所1
            ,k.kg_tel           -- 現住所TEL
            ,k.kg_addr2         -- 現住所2
            ,k.kk_kinmusaki_kbn -- 勤務先
            ,k.kk_kinmusaki_nm  -- 勤務先名
            ,k.kk_tel           -- 勤務先TEL
            ,k.kk_yakusyoku_nm  -- 役職／職種
            ,k.kk_fax           -- 勤務先FAX
            ,k.v_free8 AS careful_memo          -- 注意事項
            ,k.m_nm             -- 喪主名
            ,k.m_last_nm             -- 喪主苗字
            ,k.m_first_nm             -- 喪主名前
            ,k.m_knm            -- 喪主名カナ
            ,k.m_last_knm            -- 喪主苗字カナ
            ,k.m_first_knm            -- 喪主名前カナ
            ,k.m_file_nm        -- 喪主添付ファイル
            ,k.m_zoku_nm        -- 喪主続柄名
            ,k.m_zoku_cd        -- 喪主続柄コード
            ,k.m_zoku_kbn       -- 喪主続柄区分
            ,k.m_zoku_nm2       -- 喪主からみた続柄名
            ,k.m_zoku_cd2       -- 喪主からみた続柄コード
            ,k.m_zoku_kbn2      -- 喪主からみた続柄区分
            ,k.m_gengo          -- 喪主生年月日元号
            ,k.m_birth_year          -- 喪主誕生年
            ,k.m_birth_month          -- 喪主誕生月
            ,k.m_birth_day          -- 喪主誕生日
            ,k.m_seinengappi_ymd    -- 喪主生年月日
            ,k.m_nenrei_man     -- 喪主年齢
            ,k.mg_yubin_no      -- 喪主現住所郵便番号
            ,k.mg_addr1         -- 喪主現住所1
            ,k.mg_m_tel         -- 喪主携帯
            ,k.mg_tel           -- 喪主現住所TEL
            ,k.mg_addr2         -- 喪主現住所2
            ,k.mk_kinmusaki_kbn -- 喪主勤務先
            ,k.mk_kinmusaki_nm  -- 喪主勤務先名
            ,k.mk_tel           -- 喪主勤務先TEL
            ,k.mk_yakusyoku_nm  -- 喪主役職／職種
            ,k.mk_fax           -- 喪主勤務先FAX
            ,k.souke_nm         -- 葬家
            ,k.souke_knm        -- 葬家カナ
            ,k.keishiki_code_kbn      -- 葬儀形式コード区分
            ,k.keishiki_cd      -- 葬儀形式コード
            ,k.keishiki_kbn     -- 葬儀形式区分
            ,k.syushi_code_kbn        -- 宗旨コード区分
            ,k.syushi_cd        -- 宗旨コード
            ,k.syushi_kbn       -- 宗旨区分
            ,k.syuha_code_kbn        -- 宗派コード区分
            ,trim(k.syuha_cd) AS syuha_cd        -- 宗派コード
            ,k.syuha_kbn        -- 宗派区分
            ,k.syuha_nm         -- 宗派名
            ,k.syuha_knm        -- 宗派カナ名
            ,skf.n_free6 AS shonanoka_kbn -- 初七日区分
            ,k.jyusho_cd        -- 寺院コード
            ,k.jyusho_nm        -- 寺院名
            ,k.jyusho_knm       -- 寺院カナ名
            ,tera.tel AS temple_tel -- 寺院TEL
            ,tera.fax AS temple_fax -- 寺院FAX
            --,tera.zip_no AS temple_yubin_no -- 寺院郵便番号
            --,tera.addr1_nm AS temple_addr1 -- 寺院住所1
            --,tera.addr2_nm AS temple_addr2 -- 寺院住所2
            ,skf.zip_no2    AS temple_yubin_no  -- 寺院郵便番号
            ,skf.addr2_1    AS temple_addr1     -- 寺院住所1
            ,skf.addr2_2    AS temple_addr2     -- 寺院住所2
            ,skf.tel_no2    AS temple_tel2      -- 寺院TEL
            ,skf.free11_code_cd AS temple_cd2   -- 紹介寺院CD
            ,skf.v_free24       AS temple_nm2   -- 紹介寺院名
            ,temple2.zip_no AS temple2_yubin_no
            ,temple2.addr1_nm AS temple2_addr1
            ,temple2.addr2_nm AS temple2_addr2
            ,temple2.tel AS temple2_tel
            ,k.free1_code_cd AS tera_shokai_code_kbn    -- 寺紹介者コード
            ,k.free1_kbn AS tera_shokai_kbn        -- 寺紹介者区分
            ,k.free4_code_cd AS pacemaker_code_kbn -- ペースメーカー区分コード区分
            ,k.free4_kbn AS pacemaker_kbn   -- ペースメーカー区分
            ,k.free5_code_cd AS infection_code_kbn -- 感染症有無区分コード区分
            ,k.free5_kbn AS infection_umu_kbn   -- 感染症有無区分
            ,k.free6_code_cd AS m_sex_code_kbn -- 喪主性別コード区分
            ,k.free6_kbn AS m_sex_kbn   -- 喪主性別区分
            ,k.free7_code_cd -- アンケート送付先コード区分
            ,k.free7_kbn   -- アンケート送付先区分
            ,k.free8_code_cd -- 棺確認コード区分
            ,k.free8_kbn   -- 棺確認区分
            ,k.free2_code_cd -- DM送付コード区分
            ,k.free2_kbn   -- DM送付コード区分
            ,k.free3_code_cd -- DM送付コード区分
            ,k.free3_kbn   -- DM送付コード区分
            ,k.n_free4 AS shinzoku_cnt        -- 親族人数
            ,k.n_free5 AS kaiso_cnt        -- 予想参列者数
            ,k.n_free8 AS jizen_history_no        -- 事前相談受注伝票履歴番号
            ,k.biko1            -- メモ（出棺経路・納骨・壇払など）
            ,k.sd_hakko_kbn     -- 診断書発行区分
            ,k.sd_step_kbn      -- 診断書手続
            ,k.sd_yotei_ymd     -- 診断書発行予定時刻
            ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD HH24:MI') AS sd_yotei_ymd-- 診断書発行予定スタンプ
            ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD') AS sd_yotei_date-- 診断書発行予定時日付のみ
            ,TO_CHAR(k.sd_yotei_ymd ,'HH24:MI') AS sd_yotei_time-- 診断書発行予定時刻のみ
            ,k.sd_copy_cnt      -- 診断書コピー枚数
            ,k.az_death_cnt     -- 死亡診断書枚数
            ,k.az_inkan_kbn     -- 印鑑
            ,k.az_photo_cnt     -- 御写真枚数
            ,k.az_gojokai_nm    -- 互助会証書名称
            ,k.sekyu_cd         -- 請求先コード
            ,k.jichu_kakute_ymd -- 受注確定日
            ,k.v_free1          -- 御写真ファイル名
            ,k.img_free1        -- 御写真OID
            ,k.v_free34          -- アンケート備考
            ,k.v_free35          -- アンケートファイル名
            ,k.img_free3        -- アンケートファイルOID
            ,TO_CHAR(k.sougi_ymd ,'YYYY/MM/DD') AS sougi_ymd-- 葬儀年月日
            ,k.free2_cd            AS  copy_saki_seko_no    -- コピー先施行番号
            ,ski2.seko_no          AS  copy_moto_seko_no    -- コピー元施行番号
            ,k.k_cif_no
            ,k.k_cif_status
            ,TO_CHAR(k.d_free1 ,'YYYY/MM/DD') AS d_free1-- 印鑑預り日
            ,TO_CHAR(k.d_free2, 'YYYY/MM/DD') AS d_free2
            ,TO_CHAR(k.d_free3, 'YYYY/MM/DD') AS d_free3
            ,TO_CHAR(k.d_free4, 'YYYY/MM/DD') AS d_free4
            ,TO_CHAR(k.d_free5, 'YYYY/MM/DD') AS d_free5
            ,TO_CHAR(k.d_free6, 'YYYY/MM/DD') AS d_free6
            ,TO_CHAR(k.d_free7, 'YYYY/MM/DD') AS d_free7
            ,TO_CHAR(k.d_free8, 'YYYY/MM/DD') AS d_free8
            ,TO_CHAR(k.d_free9, 'YYYY/MM/DD') AS d_free9
            ,TO_CHAR(k.d_free10, 'YYYY/MM/DD') AS d_free10
            ,TO_CHAR(k.d_free11, 'YYYY/MM/DD') AS d_free11
            ,TO_CHAR(k.d_free12, 'YYYY/MM/DD') AS d_free12
            ,TO_CHAR(k.d_free13, 'YYYY/MM/DD') AS d_free13
            ,TO_CHAR(k.d_free14, 'YYYY/MM/DD') AS d_free14
            ,k.m_cif_no
            ,k.m_cif_status
            ,k.s_cif_no
            ,k.s_cif_status
            ,k.fc_nm
            ,k.fc_last_nm
            ,k.fc_first_nm
            ,k.fc_knm
            ,k.fc_last_knm
            ,k.fc_first_knm
            ,k.fc_tel
            ,k.fc_mobile_tel
            ,k.fc_kinmusaki_kbn
            ,k.fc_kinmusaki_nm
            ,k.fc_office_tel
            ,k.fc_office_fax
            ,k.fc_yakusyoku_nm
            ,k.est_shikijo_cd
            ,k.seko_shikijo_cd
            ,k.choi_kouden_code_kbn
            ,k.choi_kouden_cd
            ,k.choi_kouden_kbn
            ,k.choi_kyoka_code_kbn
            ,k.choi_kyoka_cd
            ,k.choi_kyoka_kbn
            ,k.choi_shohin_cd
            ,k.choi_shohin_bumon_cd
            ,k.choi_tokuchu_prc
            ,k.choi_kahi_01
            ,k.choi_kahi_02
            ,k.choi_kahi_03
            ,k.choi_kahi_04
            ,k.choi_kahi_05
            ,k.choi_kahi_06
            ,k.information_code_kbn
            ,k.information_cd
            ,k.information_kbn
            ,k.inquiry_topic
            ,k.status_kbn
            ,bm.kaikei_bumon_cd AS est_oya_bumon_cd
            ,k.free3_cd AS consult_seko_no
            ,k.free5_cd AS hanso_seko_no
            ,k.v_free9
            ,k.v_free10
            ,k.v_free11
            ,k.v_free12
            ,k.v_free13
            ,k.v_free14
            ,k.v_free15
            ,k.v_free16
            ,k.v_free17
            ,k.keiyaku_cif_no
            ,k.keiyaku_cif_status
            ,k.keiyaku_nm     
            ,k.keiyaku_last_nm
            ,k.keiyaku_first_nm
            ,k.keiyaku_knm     
            ,k.keiyaku_last_knm
            ,k.keiyaku_first_knm
            ,k.keiyaku_file_nm  
            ,k.keiyaku_zoku_nm  
            ,k.keiyaku_zoku_cd  
            ,k.keiyaku_zoku_kbn 
            ,k.keiyaku_zoku_nm2 
            ,k.keiyaku_zoku_cd2 
            ,k.keiyaku_zoku_kbn2
            ,k.keiyaku_gengo    
            ,k.keiyaku_birth_year
            ,k.keiyaku_birth_month
            ,k.keiyaku_birth_day  
            ,k.keiyaku_seinengappi_ymd
            ,k.keiyaku_nenrei_man     
            ,k.keiyaku_yubin_no      
            ,k.keiyaku_addr1         
            ,k.keiyaku_m_tel         
            ,k.keiyaku_tel           
            ,k.keiyaku_addr2         
            ,k.keiyaku_kinmusaki_kbn 
            ,k.keiyaku_kinmusaki_nm  
            ,k.keiyaku_kinmusaki_tel           
            ,k.keiyaku_yakusyoku_nm  
            ,k.keiyaku_kinmusaki_fax           
            ,k.m_keiyaku_kankei_kbn           
            ,k.m_keiyaku_kankei     
            ,k.renraku_cif_no
            ,k.renraku_cif_status
            ,k.renraku_nm     
            ,k.renraku_last_nm
            ,k.renraku_first_nm
            ,k.renraku_knm     
            ,k.renraku_last_knm
            ,k.renraku_first_knm      
            ,k.renraku_yubin_no      
            ,k.renraku_addr1         
            ,k.renraku_m_tel         
            ,k.renraku_tel           
            ,k.renraku_addr2           
            ,k.renraku_keiyaku_kankei_kbn           
            ,k.renraku_keiyaku_kankei      
            ,k.m_zoku_code_kbn2
            ,k.k_seinengappi_ymd_y
            ,k.kasoba_kbn
            ,k.kasoba_cd
            ,k.kasoba_nm
            ,k.v_free19
            ,k.n_free7
            ,k.free1_cd         AS nyudensaki_cd1
            ,k.free2_cd         AS nyudensaki_cd2
            ,skf.v_free1        AS nyudensaki_nm1
            ,skf.v_free2        AS nyudensaki_nm2
            ,skf.v_free3        AS nyudensha_nm
            ,skf.v_free8        AS nyudensaki_tel1
            ,skf.v_free9        AS nyudensaki_tel2
            ,keiyaku1.shokuchi_kbn AS shokuchi_kbn1
            ,keiyaku2.shokuchi_kbn AS shokuchi_kbn2
            ,skf.free_kbn2      AS keisatsu_kbn
            ,skf.v_free11       AS keisatsu_nm
            ,skf.v_free10       AS homonsaki_nm
            ,skf.tel_no1        AS homonsaki_tel1
            ,skf.mobile_tel1    AS homonsaki_tel2
            ,skf.zip_no1        AS homonsaki_zip_no
            ,skf.addr1_1        AS homonsaki_addr1
            ,skf.addr1_2        AS homonsaki_addr2
            ,k.iso_tanto_cd1    AS shutsudo_cd11
            ,k.iso_tanto_cd2    AS shutsudo_cd12
            ,k.iso_tanto_cd3    AS shutsudo_cd13
            ,s_tanto11.tanto_nm AS shutsudo_nm11
            ,s_tanto12.tanto_nm AS shutsudo_nm12
            ,s_tanto13.tanto_nm AS shutsudo_nm13
            ,k.free4_cd         AS izoku_cif_no
            ,k.n_free14         AS izoku_cif_status
            ,k.img_free2        AS izoku_file_nm
            ,k.hs_anchi_cd
            ,k.hs_anchi_nm
            ,k.biko1            AS seko_biko1
            ,k.v_free24         AS dm_last_nm
            ,k.v_free25         AS dm_first_nm
            ,k.v_free26         AS dm_last_knm
            ,k.v_free27         AS dm_first_knm
            ,k.v_free28         AS dm_tel
            ,k.v_free29         AS dm_m_tel
            ,k.v_free30         AS dm_yubin_no
            ,k.v_free31         AS dm_addr1
            ,k.v_free32         AS dm_addr2
            ,skf.biko1          AS irai_biko
            ,skf.free12_code_cd AS todokede_kbn
            ,skf.v_free26       AS todokede_nm
            ,skf.free8_code_cd  AS kaiin_sbt_code_cd
            ,skf.free8_kbn      AS kaiin_sbt_cd
            ,k.free9_code_cd    --アンケート回収コード区分
            ,k.free9_kbn        -- アンケート回収区分
        FROM
            seko_kihon_info k
            LEFT JOIN seko_kihon_info ski2          -- コピー元施行番号
                ON ski2.free2_cd   = k.seko_no
                AND ski2.moushi_kbn = 5           -- 申込区分：事前相談
            LEFT JOIN tanto_mst t1
                ON k.uketuke_tanto_cd = t1.tanto_cd
                AND t1.delete_flg = 0
        LEFT JOIN seko_kihon_all_free skf
        ON  skf.seko_no    = k.seko_no
        AND skf.delete_flg = 0
        LEFT JOIN tanto_mst t2
        ON  k.seko_tanto_cd = t2.tanto_cd
        AND t2.delete_flg   = 0
        LEFT JOIN nm_jyusho_mst tera
        ON  tera.jyusho_kbn = 1
        AND tera.jyusho_cd  = k.jyusho_cd
        AND tera.delete_flg = 0
        LEFT JOIN bumon_mst bm
        ON  bm.bumon_cd = k.est_shikijo_cd
        AND bm.delete_flg  = 0
        LEFT JOIN tanto_mst s_tanto11
        ON  s_tanto11.tanto_cd   = k.iso_tanto_cd1
        AND s_tanto11.delete_flg = 0
        LEFT JOIN tanto_mst s_tanto12
        ON  s_tanto12.tanto_cd   = k.iso_tanto_cd2
        AND s_tanto12.delete_flg = 0
        LEFT JOIN tanto_mst s_tanto13
        ON  s_tanto13.tanto_cd   = k.iso_tanto_cd3
        AND s_tanto13.delete_flg = 0
        LEFT JOIN nm_jyusho_mst temple
        ON  temple.jyusho_cd  = skf.free11_code_cd
        AND temple.delete_flg = 0
        LEFT JOIN keiyakusaki_mst keiyaku1
        ON  keiyaku1.partner_cd = k.free1_cd
        AND keiyaku1.delete_flg = 0
        LEFT JOIN keiyakusaki_mst keiyaku2
        ON  keiyaku2.partner_cd = k.free2_cd
        AND keiyaku2.delete_flg = 0
        LEFT JOIN nm_jyusho_mst temple2
        ON temple2.jyusho_kbn = 1
        AND temple2.jyusho_cd = skf.free11_code_cd
        AND temple2.delete_flg = 0
        WHERE
            k.seko_no = :seko_no
        AND k.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
//        if (isset($select['v_free9'])) {
//            $select['m_mail_address'] = $select['v_free9'];
//        }
        if (isset($select['k_birth_year']) && isset($select['k_birth_month']) && isset($select['k_birth_day'])) {
            $select['k_seinengappi_ymd_y'] = $select['k_birth_year'] . '-' . $select['k_birth_month'] . '-' . $select['k_birth_day'];
        }
        if (isset($select['m_birth_year']) && isset($select['m_birth_month']) && isset($select['m_birth_day'])) {
            $select['m_seinengappi_ymd_y'] = $select['m_birth_year'] . '-' . $select['m_birth_month'] . '-' . $select['m_birth_day'];
        }
        return $select;
    }

    /**
     * 値引き互助会情報設定処理 
     *
     * <AUTHOR> Sai
     * @since 2015/06/16
     * @param Msi_Sys_Db $db db
     * @param array $dataGojokaiInfo 施行互助会情報データ
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return int 更新件数
     */
    protected function setNebikiGojokaiInfo(&$dataGojokaiInfo, $dataGojokaiMemberCol) {
        $dataGojokaiInfo['nebiki_gojokai_kbn'] = null;
        $dataGojokaiInfo['nebiki_yoto_kbn'] = null;
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        $gojokai_cnt = 0;
        $couseMstOne = null;
        foreach ($dataGojokaiMemberCol as $oneRow) {
            if ($this->_moushiKbn == static::MOUSHI_KBN_HOUJI && $oneRow['yoto_kbn'] == static::YOTO_KBN_NOUSE) {
                $dataGojokaiInfo['nebiki_yoto_kbn'] = self::NEBIKI_YOTO_PLAN;
            } else if ($oneRow['yoto_kbn'] != static::YOTO_KBN_NOUSE) {
                $gojokai_cnt++;
            }
            if ($oneRow['kaiin_info_kbn'] == self::KAIIN_INFO_OM && $oneRow['yoto_kbn'] === '1' || $oneRow['yoto_kbn'] === '2') {
                $dataGojokaiInfo['nebiki_yoto_kbn'] = self::NEBIKI_YOTO_PLAN;
            } else if ($oneRow['yoto_kbn'] === '1' || $oneRow['yoto_kbn'] === '2') {
                $dataGojokaiInfo['nebiki_yoto_kbn'] = $oneRow['yoto_kbn'];
                $couseMstOne = $this->getCouseMstOne($oneRow['course_snm_cd'], $gojokaiCouseMst);
            }
        }
        if ($gojokai_cnt > 0 && is_null($dataGojokaiInfo['nebiki_yoto_kbn'])) {
            $dataGojokaiInfo['nebiki_yoto_kbn'] = self::NEBIKI_YOTO_PLAN;
        }
        //　会員区分が互助会かつコース施行でもプラン施行も設定されていない場合は値引用途区分にはプラン施行を設定する
        if (isset($dataGojokaiInfo['use_cose']) && strlen($dataGojokaiInfo['use_cose']) > 0) {
            $couseMstOne = $this->getCouseMstOne($dataGojokaiInfo['use_cose'], $gojokaiCouseMst);
        }
        if (!is_null($couseMstOne)) {
            if ($dataGojokaiInfo['nebiki_yoto_kbn'] == '1') {
                $dataGojokaiInfo['nebiki_gojokai_kbn'] = $couseMstOne['gojokai_kbn'];
                $this->_nebikiGojokaiKbn = $dataGojokaiInfo['nebiki_gojokai_kbn'];
            }
        }
        // 申込区分がオーダーメイドかつ互助会未選択時は値引用途区分をプラン施行に設定する
        if ($this->_moushiKbn == static::MOUSHI_KBN_ORDERMADE && !isset($dataGojokaiInfo['nebiki_yoto_kbn']) && !isset($dataGojokaiInfo['nebiki_gojokai_kbn'])) {
            $dataGojokaiInfo['nebiki_yoto_kbn'] = self::NEBIKI_YOTO_PLAN;
        }
    }

    /**
     * 互助会コースマスタワンレコード抽出処理 
     *
     * <AUTHOR> Sai
     * @since 2015/06/16
     * @param Msi_Sys_Db $db db
     * @param array $course_snm_cd 互助会コースコード
     * @param array $gojokaiCouseMst 互助会コースマスタ
     * @return int 更新件数
     */
    private function getCouseMstOne($course_snm_cd, $gojokaiCouseMst) {
        $couseMstOne = null;
        foreach ($gojokaiCouseMst as $coseOneRow) {
            if ($course_snm_cd == $coseOneRow['gojokai_cose_iw']) {
                $couseMstOne = $coseOneRow;
                break;
            }
        }
        return $couseMstOne;
    }

    /**
     * 互助会コースマスタワンレコード抽出処理(互助会区分より)
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @param Msi_Sys_Db $db db
     * @param array $gojokai_kbn 互助会コースコード
     * @param array $gojokaiCouseMst 互助会コースマスタ
     * @return int 更新件数
     */
    private function getCouseMstOneByGojokaiKbn($gojokai_kbn, $gojokaiCouseMst) {
        $couseMstOne = null;
        foreach ($gojokaiCouseMst as $coseOneRow) {
            if ($gojokai_kbn == $coseOneRow['gojokai_kbn']) {
                $couseMstOne = $coseOneRow;
                break;
            }
        }
        return $couseMstOne;
    }

    /**
     * 値引会員区分取得処理 
     *
     * <AUTHOR> Sai
     * @since 2015/06/11
     * @param string $nebiki_group_no 互助会グループ番号
     * @param string $nebiki_join_cnt プラン利用口数
     * @return int 値引会員区分
     */
    private function getNebikiGojokaiKbn($nebiki_group_no, $nebiki_join_cnt) {
        $nebiki_gojokai_kbn = null;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                nmcm.nebiki_gojokai_kbn     -- 顧客コード
            FROM
                nebiki_member_calc_mst nmcm
            WHERE
                    nmcm.delete_flg = 0
                AND nmcm.nebiki_group_no = :nebiki_group_no
                AND nmcm.nebiki_join_cnt = :nebiki_join_cnt
                ";
        $select = $db->easySelOne($sql, array('nebiki_group_no' => $nebiki_group_no, 'nebiki_join_cnt' => $nebiki_join_cnt));
        if (count($select) > 0) {
            $nebiki_gojokai_kbn = $select['nebiki_gojokai_kbn'];
        }
        return $nebiki_gojokai_kbn;
    }

    /**
     *
     * 回収予定日取得
     *
     * <AUTHOR> Sai
     * @since 2016/10/19
     * @return array 
     */
    protected function getUriageKaisu() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            uri_den_no
            ,TO_CHAR(kaishu_ymd,'YYYY/MM/DD') AS kaishu_ymd
        FROM
            uriage_denpyo
        WHERE
            seko_no = :seko_no
        AND delete_flg = 0
        AND seko_no_sub = :seko_no_sub
	AND data_kbn IN (1, 2)
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'seko_no_sub' => $this->_sekoNoSub));
        return $select;
    }

    /**
     * 税情報を取得する
     *
     * <AUTHOR> Matsuyama
     * @since 2016/12/27
     * @return array 税データ
     */
    protected function getZeiData($db) {
        $zeiData = array();
        foreach (DataMapper_ZeiMst::find($db, array(), false) as $rec) {
            $rec11 = Msi_Sys_Utils::remapArrayFlat($rec, <<< END_OF_TXT
zei_cd zei_rtu zei_hasu_kbn
END_OF_TXT
            );
            $zeiData[] = array_merge($rec11, array(
                'id' => $rec['zei_cd'],
                'text' => $rec['zei_rtu'] . '%',
            ));
        }
        return $zeiData;
    }

    /**
     * 西暦＋和暦を取得するを取得する
     *
     * <AUTHOR> Tosaka
     * @since 2020/02/28
     * @return array 税データ
     */
    public static function getSeirekiAndWareki($db) {

        // 現在の西暦を取得する
        $currentYear = Msi_Sys_Utils::getDate(null, 'Y');
        // 元号マスタの初年～現在までを取得する
        $zeiData = array();
        foreach (DataMapper_ZeiMst::find($db, array(), false) as $rec) {
            $rec11 = Msi_Sys_Utils::remapArrayFlat($rec, <<< END_OF_TXT
zei_cd zei_rtu zei_hasu_kbn
END_OF_TXT
            );
            $zeiData[] = array_merge($rec11, array(
                'id' => $rec['zei_cd'],
                'text' => $rec['zei_rtu'] . '%',
            ));
        }
        return $zeiData;
    }

    /**
     * 別注商品取得処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/03/09
     * @return  array 
     */
    protected function getBechuShohin() {
        $db = Msi_Sys_DbManager::getMyDb();
        $kijun_ymd = $this->_selKijunYmd;
        $sql = "
            SELECT
                sm.shohin_cd AS id
                ,'(' || sm.shohin_cd || ')' || sm.shohin_nm AS text
                ,sm.bumon_cd
                ,TO_CHAR(sm.hanbai_st_ymd, 'YYYY/MM/DD') AS hanbai_st_ymd
                ,TO_CHAR(sm.hanbai_end_ymd, 'YYYY/MM/DD') AS hanbai_end_ymd
            FROM shohin_mst sm
            INNER JOIN shohin_bunrui_mst sbm
                ON sbm.shohin_cd = sm.shohin_cd
                AND sbm.bumon_cd = sm.bumon_cd
                AND sbm.dai_bunrui_cd = :dai_bunrui_cd
                AND sbm.delete_flg = 0
            WHERE sm.delete_flg = 0
                AND TO_CHAR(sm.hanbai_st_ymd, 'YYYY/MM/DD') <= '$kijun_ymd'  
                AND TO_CHAR(sm.hanbai_end_ymd,'YYYY/MM/DD') >= '$kijun_ymd'  
            ORDER BY sbm.shohin_cd
        ";
        $select = $db->easySelect($sql, array('dai_bunrui_cd' => self::DAIBUNRUI_BECHU));
        return $select;
    }

    /**
     * 別注商品の部門取得処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/03/09
     * @return  array 
     */
    protected function getBechuShohinBumon() {
        $db = Msi_Sys_DbManager::getMyDb();
        $kijun_ymd = $this->_selKijunYmd;
        $sql = "
            SELECT
                bm.bumon_cd AS id
                ,bm.bumon_lnm AS text
            FROM shohin_mst sm
            INNER JOIN shohin_bunrui_mst sbm
                ON sbm.shohin_cd = sm.shohin_cd
                AND sbm.dai_bunrui_cd = :dai_bunrui_cd
                AND sbm.delete_flg = 0
            INNER JOIN bumon_mst bm
                ON bm.bumon_cd = sm.bumon_cd
                AND bm.delete_flg = 0
            WHERE sm.delete_flg = 0
                AND TO_CHAR(sm.hanbai_st_ymd, 'YYYY/MM/DD') <= '$kijun_ymd'  
                AND TO_CHAR(sm.hanbai_end_ymd,'YYYY/MM/DD') >= '$kijun_ymd'  
            GROUP BY bm.bumon_cd
            ORDER BY bm.bumon_cd
        ";
        $select = $db->easySelect($sql, array('dai_bunrui_cd' => self::DAIBUNRUI_BECHU));
        return $select;
    }

    /**
     * 部門マスタ取得処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/03/09
     * @return  array 
     */
    protected function getCdJizenDenpyo($seko_no) {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
            SELECT T.*
            FROM (
                SELECT 
                    '0' AS id
                    ,'最新' || '(' || TO_CHAR(jd.juchu_prc_sum 
                    + jd.juchu_hepn_sum 
                    + jd.juchu_nebk_sum 
                    + jd.hoshi_prc_sum 
                    + jd.out_zei_prc
                    + jd.sougi_keiyaku_prc + jd.sougi_harai_prc
                    + jd.sougi_keiyaku_zei
                    + jd.sougi_wari_prc
                    + jd.sougi_premium_service_prc
                    + jd.sougi_early_use_cost 
                    + jd.sougi_early_use_cost_zei   -- 早期利用費
                    + jd.sougi_meigi_chg_cost       -- 名義変更手数料
                    + jd.sougi_meigi_chg_cost_zei   -- 名義変更手数料
                    + jd.etc_harai_prc              -- 金額充当
                    ,'fm999,999,999,999') || '円)' AS text
                    ,NULL AS disp_no
                FROM juchu_denpyo jd
                LEFT JOIN bumon_mst est_bm
                    ON est_bm.bumon_cd = jd.est_shikijo_cd
                    AND est_bm.delete_flg = 0
                WHERE jd.delete_flg = 0
                    AND jd.seko_no = :seko_no
                    AND jd.data_kbn = :data_kbn
                UNION ALL
                SELECT 
                    jdh.history_no AS id
                    ,'#' || CAST(jdh.history_no AS VARCHAR) || '(' || TO_CHAR(jdh.juchu_prc_sum 
                    + jdh.juchu_hepn_sum 
                    + jdh.juchu_nebk_sum 
                    + jdh.hoshi_prc_sum 
                    + jdh.out_zei_prc
                    + jdh.sougi_keiyaku_prc + jdh.sougi_harai_prc
                    + jdh.sougi_keiyaku_zei
                    + jdh.sougi_wari_prc
                    + jdh.sougi_premium_service_prc
                    + jdh.sougi_early_use_cost 
                    + jdh.sougi_early_use_cost_zei   -- 早期利用費
                    + jdh.sougi_meigi_chg_cost       -- 名義変更手数料
                    + jdh.sougi_meigi_chg_cost_zei   -- 名義変更手数料
                    + jdh.etc_harai_prc              -- 金額充当
                    ,'fm999,999,999,999') || '円)' AS text
                    ,jdh.history_no AS disp_no
                FROM juchu_denpyo_history jdh
                LEFT JOIN bumon_mst est_bm
                    ON est_bm.bumon_cd = jdh.est_shikijo_cd
                    AND est_bm.delete_flg = 0
                WHERE jdh.delete_flg = 0
                    AND jdh.seko_no = :seko_no
                    AND jdh.data_kbn = :data_kbn
            ) AS T
            ORDER BY T.disp_no DESC
                ";
        $select = $db->easySelect($sql, array(
            'seko_no' => $seko_no,
            'data_kbn' => 1,
        ));
        return $select;
    }
    /**
     * 部門マスタ取得処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/03/09
     * @return  array 
     */
    protected function getCdBumon() {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
            SELECT
                bm.bumon_cd AS id
                ,bm.bumon_lnm AS text
                ,bkm.oya_bumon_cd
            FROM bumon_mst bm
            LEFT JOIN bumon_kaso_mst bkm
                ON bkm.ko_bumon_cd = bm.bumon_cd
                AND bkm.delete_flg = 0
            WHERE bm.bumon_kbn IN (2,3)
                AND bm.delete_flg = 0
            ORDER BY bm.bumon_cd
        ";
        $select = $db->easySelect($sql);
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $estShikijoCd = null;
        if (isset($this->_estShikijoCd)) {
            $estShikijoCd = $this->_estShikijoCd;
        }
        foreach ($select as &$val) {
            $oya_bumons = array();
            $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $val['id']));
            if ($bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
                //自部門商品＋親部門商品(親部門はさかのぼる)
                $oya_bumons = Logic_Hanso_HansoUtils::getShohinOyaBumonCds($estShikijoCd, $db);
            } else {
                // 会計部門と違う場合は会計部門の親部門を取得する
                // 親部門商品(親部門はさかのぼる)のみ
                $aBumonCds = array();
                if (isset($bumonData[0]['kaikei_bumon_cd'])) {
                    $bumonData2 = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $bumonData[0]['kaikei_bumon_cd']));
                    if ($bumonData2[0]['bumon_shohin_sel_kbn'] === '1') {
                        $oya_bumons[] = $bumonData[0]['kaikei_bumon_cd'];
                    }
                    $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $bumonData[0]['kaikei_bumon_cd'], null);
                } else {
                    $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $estShikijoCd, null);
                }
                foreach ($allOyaBumon as $oneBumon) {
                    if ($oneBumon['bumon_kbn'] === '0') {
                        if ($oneBumon['joui_bumon_shohin_sel_kbn'] === '0') { // 上位部門商品選択区分 0:上位部門参照なし  1:上位部門参照あり
                            $oya_bumons[] = $oneBumon['bumon_cd'];
                            break;
                        }
                        if ($oneBumon['bumon_shohin_sel_kbn'] === '1') { // 部門別商品選択区分 0：部門別商品なし　1：部門別商品あり
                            $oya_bumons[] = $oneBumon['bumon_cd'];
                        }
                    }
                }
            }
            $val['oya_bumons'] = $oya_bumons;
        }
        return $select;
    }
    /**
     * 部門マスタ取得処理（取扱店用）
     * 
     * <AUTHOR> Sugiyama
     * @since      2022/07/xx
     * @return  array 
     */
    protected function getCdBumonT() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                 bm.bumon_cd        AS id
                ,bm.bumon_lnm       AS text
                ,bkm.oya_bumon_cd
                ,bm.transfer_bank_cd1
                ,bm.transfer_bank_cd3
            FROM bumon_mst bm
            LEFT JOIN bumon_kaso_mst bkm
            ON  bkm.ko_bumon_cd = bm.bumon_cd
            AND bkm.delete_flg  = 0
            WHERE bm.bumon_kbn = 1
            AND bm.seko_kbn    = 1
            AND bm.delete_flg  = 0
            ORDER BY bm.bumon_cd
        ";
        $select = $db->easySelect($sql);
        return $select;
    }
    /**
     * 部門マスタ取得処理（取扱店用・事前相談用）
     * 
     * <AUTHOR> Sugiyama
     * @since      2022/07/xx
     * @return  array 
     */
    protected function getCdBumonTForJizen() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                 bm.bumon_cd        AS id
                ,bm.bumon_lnm       AS text
                ,bkm.oya_bumon_cd
                ,bm.transfer_bank_cd1
            FROM bumon_mst bm
            LEFT JOIN bumon_kaso_mst bkm
            ON  bkm.ko_bumon_cd = bm.bumon_cd
            AND bkm.delete_flg  = 0
            WHERE bm.bumon_kbn <> 0
            AND bm.haishi_flg    = 0
            AND bm.delete_flg  = 0
            ORDER BY bm.bumon_cd
        ";
        $select = $db->easySelect($sql);
        return $select;
    }
    /**
     * 部門マスタ取得処理（入電店用）
     * 
     * <AUTHOR> Sugiyama
     * @since      2022/07/xx
     * @return  array 
     */
    protected function getCdBumonN() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                 bm.bumon_cd        AS id
                ,bm.bumon_lnm       AS text
                ,bkm.oya_bumon_cd
            FROM bumon_mst bm
            LEFT JOIN bumon_kaso_mst bkm
            ON  bkm.ko_bumon_cd = bm.bumon_cd
            AND bkm.delete_flg  = 0
            WHERE bm.bumon_kbn NOT IN(0)
            AND bm.delete_flg  = 0
            ORDER BY bm.bumon_cd
        ";
        $select = $db->easySelect($sql);
        return $select;
    }
    /**
     * 部門マスタ取得処理(親部門)
     * 
     * <AUTHOR> Tosaka
     * @since      2020/03/09
     * @return  array 
     */
    protected function getCdOyaBumon() {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
            SELECT
                bm.bumon_cd AS id
                ,bm.bumon_lnm AS text
                ,bkm.oya_bumon_cd
            FROM bumon_mst bm
            LEFT JOIN bumon_kaso_mst bkm
                ON bkm.ko_bumon_cd = bm.bumon_cd
                AND bkm.delete_flg = 0
            WHERE bm.bumon_kbn IN (0)
                AND bm.delete_flg = 0
            ORDER BY bm.bumon_cd
        ";
        $select = $db->easySelect($sql);
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        foreach ($select as &$val) {
            $oya_bumons = array();
            $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $val['id']));
            if ($bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
                //自部門商品＋親部門商品(親部門はさかのぼる)
                $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $val['id'], null);
                foreach ($allOyaBumon as $oneBumon) {
                    // 部門別商品選択区分が有かつ上位部門商品選択区分が有
//                    if ($oneBumon['bumon_shohin_sel_kbn'] === '1') {
                    if ($oneBumon['bumon_shohin_sel_kbn'] === '1' && $oneBumon['joui_bumon_shohin_sel_kbn'] === '1') {
                        $oya_bumons[] = $oneBumon['ko_bumon_cd'];
                    }
                }
            } else {
                // 親部門商品(親部門はさかのぼる)のみ
                $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $val['id'], null);
                foreach ($allOyaBumon as $oneBumon) {
                    if ($oneBumon['bumon_kbn'] === '0') {
                        $oya_bumons[] .= $oneBumon['bumon_cd'];
                    }
                }
            }
            $val['oya_bumons'] = $oya_bumons;
        }
        return $select;
    }

    /**
     * 寺院マスタ取得処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/03/09
     * @return  array 
     */
    protected function getCdJiin() {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
            SELECT
                nm.jyusho_cd AS id
                ,nm.jyusho_lnm AS text
            FROM nm_jyusho_mst nm
            WHERE nm.jyusho_kbn = 1
            ORDER BY nm.jyusho_cd
        ";
        $select = $db->easySelect($sql);
        return $select;
    }

    /**
     * 担当マスタ取得処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/03/09
     * @return  array 
     */
    protected function getCdTanto() {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
            SELECT
                tm.tanto_cd AS id
                ,tm.tanto_nm AS text
                ,tm.tekiyo_ed_date
            FROM tanto_mst tm
            WHERE tm.delete_flg = 0
            ORDER BY tm.tanto_cd
        ";
        $select = $db->easySelect($sql);
        return $select;
    }

    /**
     *
     * 消費税コード取得処理
     *
     * <AUTHOR> Tosaka
     * @param Msi_Sys_Db $db db
     * @since 2017/1/17
     * @return array
     */
    protected function getZei_cd() {
        $zeidata = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $zeicd = DataMapper_ZeiMst::find_mst($db, array('reduced_tax_rate' => 1), false);
        foreach ($zeicd as $value) {
            $item['id'] = $value['zei_cd'];
            $item['text'] = $value['zei_rtu'] . "%";
            $zeidata[] = $item;
        }
        return $zeidata;
    }

    /**
     * 事前相談履歴データ取得処理
     *
     * <AUTHOR> Tosaka
     * @since 2016/02/01
     * @return array 
     */
    protected function getConsultHistory() {

        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                rh.history_no
                ,rh.tanto_cd
                ,tm.tanto_nm
                ,rh.renraku_nm
                ,rh.douseki_nm
                ,TO_CHAR(rh.reception_date, 'YYYY/MM/DD') AS reception_date
                ,TO_CHAR(TO_TIMESTAMP(reception_time::text, 'HH24:MI'),'HH24:MI') AS reception_time
                ,rh.reception_kbn
                ,rh.reception_method_kbn
                ,rh.reception_memo
            FROM reception_history rh
            LEFT JOIN tanto_mst tm
                ON tm.tanto_cd = rh.tanto_cd
                AND tm.delete_flg = 0
            WHERE rh.seko_no = :seko_no
                AND rh.delete_flg = 0
            ORDER BY rh.history_no
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        if (count($select) == 0) {
            array_push($select, array('history_no' => 1));
        }
        return $select;
    }

    /**
     *
     * 報告書タブ データを取得する
     *
     * <AUTHOR> Mogi
     * @since  2020/08/06
     * @return array 報告書データ
     */
    protected function getReport() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            si.*
            , TO_CHAR(si.td_syochi_yotei_date, 'YYYY/MM/DD') AS td_syochi_yotei_date
            , TO_CHAR(si.yukan_syochi_yotei_date, 'YYYY/MM/DD') AS yukan_syochi_yotei_date
            , TO_CHAR(si.advance_consult_ymd, 'YYYY/MM/DD HH24:MI') AS advance_consult_ymd
            , TO_CHAR(si.td_mukae_tehai_date, 'YYYY/MM/DD') AS td_mukae_tehai_date
            , TO_CHAR(si.td_mukae_tehai_time, 'HH24:MI') AS td_mukae_tehai_time
            , TO_CHAR(si.td_mukae_syutsodo_st_ts, 'YYYY/MM/DD HH24:MI') AS td_mukae_syutsodo_st_ts
            , TO_CHAR(si.td_okuri_tehai_date, 'YYYY/MM/DD') AS td_okuri_tehai_date
            , TO_CHAR(si.td_okuri_tehai_time, 'HH24:MI') AS td_okuri_tehai_time
            , TO_CHAR(si.td_okuri_syutsodo_st_ts, 'YYYY/MM/DD HH24:MI') AS td_okuri_syutsodo_st_ts
            , TO_CHAR(si.rh_tehai_date, 'YYYY/MM/DD') AS rh_tehai_date
            , TO_CHAR(si.rh_tehai_time, 'HH24:MI') AS rh_tehai_time
            , TO_CHAR(si.rh_syutsodo_st_ts, 'YYYY/MM/DD HH24:MI') AS rh_syutsodo_st_ts
            , TO_CHAR(si.yukan_tehai_date, 'YYYY/MM/DD') AS yukan_tehai_date
            , TO_CHAR(si.yukan_tehai_time, 'HH24:MI') AS yukan_tehai_time
            , TO_CHAR(si.nokan_tehai_date, 'YYYY/MM/DD') AS nokan_tehai_date
            , TO_CHAR(si.nokan_tehai_time, 'HH24:MI') AS nokan_tehai_time
            , TO_CHAR(si.setup_tehai_date, 'YYYY/MM/DD') AS setup_tehai_date
            , TO_CHAR(si.setup_tehai_time, 'HH24:MI') AS setup_tehai_time
            , TO_CHAR(si.az_tehai_date, 'YYYY/MM/DD') AS az_tehai_date
            , TO_CHAR(si.az_tehai_time, 'HH24:MI') AS az_tehai_time
            , TO_CHAR(si.hrz_k1_tehai_date, 'YYYY/MM/DD') AS hrz_k1_tehai_date
            , TO_CHAR(si.hrz_k1_tehai_time, 'HH24:MI') AS hrz_k1_tehai_time
            , TO_CHAR(si.hrz_k2_tehai_date, 'YYYY/MM/DD') AS hrz_k2_tehai_date
            , TO_CHAR(si.hrz_k2_tehai_time, 'HH24:MI') AS hrz_k2_tehai_time
            , TO_CHAR(si.hrz_k3_tehai_date, 'YYYY/MM/DD') AS hrz_k3_tehai_date
            , TO_CHAR(si.hrz_k3_tehai_time, 'HH24:MI') AS hrz_k3_tehai_time
            , TO_CHAR(si.ms_tehai_date, 'YYYY/MM/DD') AS ms_tehai_date
            , TO_CHAR(si.ms_tehai_time, 'HH24:MI') AS ms_tehai_time
			, sn_sougi.basho_nm AS kihon_seko_place -- 葬儀場所名称
			, sn_sougi.basho_cd AS sougi_basho_cd -- 葬儀場所コード
            , TO_CHAR(sn_tsuya.nitei_ymd, 'YYYY/MM/DD HH24:MI ～') AS kihon_tsuya
            , TO_CHAR(sn_sougi.nitei_ymd, 'YYYY/MM/DD HH24:MI ～') || TO_CHAR(sn_sougi.nitei_ed_ymd, 'HH24:MI') AS kihon_sougi
            , sn_kaso.basho_nm || ' 予約時間 ' || TO_CHAR(sn_kaso.nitei_ymd, 'YYYY/MM/DD HH24:MI') AS kihon_kaso_place
            , sn_sougi.spot_cd AS sougi_spot_cd -- 葬儀場所コード
            , sn_sougi.basho_kbn AS sougi_basho_kbn -- 葬儀場所区分
			, sn_sougi.basho_nm AS sougi_basho_nm -- 葬儀場所名称
			, sn_sougi.basho_cd AS sougi_basho_cd -- 葬儀場所コード
            , sn_shukkan.spot_cd AS shukkan_spot_cd -- 出棺場所コード
            , sn_shukkan.basho_kbn AS shukkan_basho_kbn -- 出棺場所区分
			, sn_shukkan.basho_nm AS shukkan_basho_nm -- 出棺場所名称
			, sn_shukkan.basho_cd AS shukkan_basho_cd -- 出棺場所コード
            , sn_kaso.basho_cd AS kaso_basho_cd -- 火葬場所コード
            , sn_kaso.basho_nm AS kaso_basho_nm -- 火葬場所名称
            , cnm_syushi.kbn_value_lnm AS kihon_syushi -- 宗旨名
            , cnm_syuha.kbn_value_lnm AS kihon_syuha   -- 宗派名
            , kihon.n_free5 AS kihon_attend_num     -- 参列人数
            , kihon.n_free4 AS kihon_family_num     -- 親族人数
            , cnm_pacemaker.kbn_value_lnm AS f_hall_pacemaker -- ペースメーカーの有無
            , kihon.k_weight AS k_weight -- 故人体重
            , kihon.k_last_nm AS cif_k_last_nm  -- 故人苗字
            , kihon.k_first_nm AS cif_k_first_nm -- 故人名前
            , kihon.k_last_knm AS cif_k_last_knm -- 故人苗字カナ
            , kihon.k_first_knm AS cif_k_first_knm -- 故人名前カナ
            , kihon.k_birth_year AS cif_k_birth_year -- 故人誕生年
            , kihon.k_birth_month AS cif_k_birth_month -- 故人誕生月
            , kihon.k_birth_day AS cif_k_birth_day -- 故人誕生日
            , kihon.m_last_nm AS cif_m_last_nm -- 喪主苗字
            , kihon.m_first_nm AS cif_m_first_nm -- 喪主名前
            , kihon.m_last_knm AS cif_m_last_knm -- 喪主苗字カナ
            , kihon.m_first_knm AS cif_m_first_knm -- 喪主名前カナ
            , kihon.m_zoku_cd AS cif_m_zoku_cd -- 喪主続柄コード
            , kihon.m_zoku_kbn AS cif_m_zoku_kbn -- 喪主続柄
            , kihon.m_birth_year AS cif_m_birth_year -- 喪主誕生年
            , kihon.m_birth_month AS cif_m_birth_month -- 喪主誕生月
            , kihon.m_birth_day AS cif_m_birth_day -- 喪主誕生日
            , sekyu.sekyu_last_nm AS cif_s_last_nm -- 請求先苗字
            , sekyu.sekyu_first_nm AS cif_s_first_nm -- 請求先名前
            , sekyu.sekyu_last_knm AS cif_s_last_knm -- 請求先苗字カナ
            , sekyu.sekyu_first_knm AS cif_s_first_knm -- 請求先名前カナ
            , kihon.k_cif_no AS cif_k_no -- 故人CIFNo.
            , kihon.k_cif_status AS cif_k_status -- 故人CIFステータス
            , kihon.m_cif_no AS cif_m_no -- 喪主CIFNo.
            , kihon.m_cif_status AS cif_m_status -- 喪主CIFステータス
            , kihon.s_cif_no AS cif_s_no -- 施主CIFNo.
            , kihon.s_cif_status AS cif_s_status -- 施主CIFステータス
            , CASE WHEN cbi.customer_cd IS NULL THEN '0'
                ELSE '1'END AS advance_consult_umu_kbn  -- 事前相談有無
            , TO_CHAR(cbi.consult_ymd, 'YYYY/MM/DD HH24:MI') AS advance_consult_ymd -- 事前相談日時
            , cbi.consult_basho_nm AS advance_consult_basho -- 事前相談場所
            , CASE  sn_shukkan.basho_kbn
               WHEN 1 THEN njm.zip_no
               WHEN 2 THEN km.zip_no
               ELSE '' END AS f_hall_zip    -- 出棺場所 郵便番号
            , CASE  sn_shukkan.basho_kbn
               WHEN 1 THEN njm.addr1_nm
               WHEN 2 THEN km.addr1_nm
               WHEN 15 THEN njm.addr1_nm
               ELSE '' END AS f_hall_address1   -- 出棺場所 住所1
            , CASE  sn_shukkan.basho_kbn
               WHEN 1 THEN njm.addr2_nm
               WHEN 2 THEN km.addr2_nm
               WHEN 15 THEN njm.addr2_nm
               ELSE '' END AS f_hall_address2   -- 出棺場所 住所2
            , CASE  sn_shukkan.basho_kbn
               WHEN 1 THEN njm.tel
               WHEN 2 THEN km.tel
               WHEN 15 THEN njm.tel
               ELSE '' END AS f_hall_tel    -- 出棺場所 電話番号
            , sn_kaso.v_free1 AS kaso_flg   -- 火葬なし
            , CASE si.td_mukae_syutsodo_st
                WHEN 10 THEN cnm_td_mukae.kbn_value_lnm || '(' || cnm_td_mukae.kbn_value_snm || ')'
                WHEN 15 THEN cnm_td_mukae.kbn_value_lnm || '(' || cnm_td_mukae.kbn_value_snm || ')'
                ELSE cnm_td_mukae.kbn_value_lnm || '(' || cnm_td_mukae.kbn_value_snm || ' ' || TO_CHAR(si.td_mukae_syutsodo_st_ts, 'YYYY/MM/DD HH24:MI') || ')'
              END AS td_mukae_syutsodo_st_disp
            , CASE si.td_okuri_syutsodo_st
                WHEN 10 THEN cnm_td_okuri.kbn_value_lnm || '(' || cnm_td_okuri.kbn_value_snm || ')'
                WHEN 15 THEN cnm_td_okuri.kbn_value_lnm || '(' || cnm_td_okuri.kbn_value_snm || ')'
                ELSE cnm_td_okuri.kbn_value_lnm || '(' || cnm_td_okuri.kbn_value_snm || ' ' || TO_CHAR(si.td_okuri_syutsodo_st_ts, 'YYYY/MM/DD HH24:MI') || ')'
              END AS td_okuri_syutsodo_st_disp
            , CASE si.rh_syutsodo_st
                WHEN 10 THEN cnm_reikyu.kbn_value_lnm || '(' || cnm_reikyu.kbn_value_snm || ')'
                WHEN 15 THEN cnm_reikyu.kbn_value_lnm || '(' || cnm_reikyu.kbn_value_snm || ')'
                ELSE cnm_reikyu.kbn_value_lnm || '(' || cnm_reikyu.kbn_value_snm || ' ' || TO_CHAR(si.rh_syutsodo_st_ts, 'YYYY/MM/DD HH24:MI') || ')'
              END AS rh_syutsodo_st_disp
        FROM SEKO_HIKI_TEHAI_INFO si
        LEFT JOIN seko_kihon_info kihon -- 施行基本情報
            ON si.seko_no = kihon.seko_no
            AND kihon.delete_flg = 0
        LEFT JOIN sekyu_saki_info sekyu -- 請求先情報
            ON si.seko_no = sekyu.seko_no
            AND sekyu.sekyu_cd = kihon.sekyu_cd
            AND sekyu.delete_flg = 0
        LEFT JOIN customer_base_info cbi    -- 事前相談情報
            ON si.seko_no = cbi.seko_no
            AND cbi.delete_flg = 0
        LEFT JOIN seko_nitei sn_tsuya   -- 施行日程（通夜）
            ON si.seko_no = sn_tsuya.seko_no
            AND sn_tsuya.delete_flg = 0
            AND sn_tsuya.nitei_kbn = 4
        LEFT JOIN seko_nitei sn_sougi   -- 施行日程（葬儀）
            ON si.seko_no = sn_sougi.seko_no
            AND sn_sougi.delete_flg = 0
            AND sn_sougi.nitei_kbn = 11
        LEFT JOIN seko_nitei sn_kaso   -- 施行日程（火葬）
            ON si.seko_no = sn_kaso.seko_no
            AND sn_kaso.delete_flg = 0        
            AND sn_kaso.nitei_kbn = 6
        LEFT JOIN seko_nitei sn_shukkan -- 施行場所（出棺）
            ON kihon.seko_no = sn_shukkan.seko_no
            AND sn_shukkan.nitei_kbn = 5
            AND sn_shukkan.delete_flg = 0
        LEFT JOIN code_nm_mst cnm_syushi    -- コード名称（宗旨）
            ON cnm_syushi.code_kbn = kihon.syushi_code_kbn
            AND cnm_syushi.kbn_value_cd = kihon.syushi_cd
            AND cnm_syushi.delete_flg = 0
        LEFT JOIN code_nm_mst cnm_syuha -- コード名称（宗派）
            ON cnm_syuha.code_kbn = kihon.syuha_code_kbn
            AND cnm_syuha.kbn_value_cd = kihon.syuha_cd
            AND cnm_syuha.delete_flg = 0
        LEFT JOIN code_nm_mst cnm_pacemaker     -- コード名称（ペースメーカー）
            ON cnm_pacemaker.code_kbn = kihon.free4_code_cd
            AND cnm_pacemaker.kbn_value_cd_num = kihon.free4_kbn
            AND cnm_pacemaker.delete_flg = 0
        LEFT JOIN code_nm_mst cnm_td_mukae     -- コード名称（TD迎えのステータス）
            ON cnm_td_mukae.code_kbn = '7981'
            AND cnm_td_mukae.kbn_value_cd_num = si.td_mukae_syutsodo_st
            AND cnm_td_mukae.delete_flg = 0
        LEFT JOIN code_nm_mst cnm_td_okuri     -- コード名称（TD送りのステータス）
            ON cnm_td_okuri.code_kbn = '7981'
            AND cnm_td_okuri.kbn_value_cd_num = si.td_okuri_syutsodo_st
            AND cnm_td_okuri.delete_flg = 0
        LEFT JOIN code_nm_mst cnm_reikyu     -- コード名称（霊柩搬送のステータス）
            ON cnm_reikyu.code_kbn = '7981'
            AND cnm_reikyu.kbn_value_cd_num = si.rh_syutsodo_st
            AND cnm_reikyu.delete_flg = 0
        LEFT JOIN kaijyo_mst km     -- 会場マスタ※施行日程（出棺）
            ON km.kaijyo_cd = sn_shukkan.basho_cd
            AND km.delete_flg = 0
        LEFT JOIN nm_jyusho_mst njm    -- 名称住所マスタ（寺院）※施行日程（出棺）
            ON njm.jyusho_cd = sn_shukkan.basho_cd
            AND njm.jyusho_kbn = sn_shukkan.basho_kbn -- 寺院
            AND njm.delete_flg = 0
        WHERE si.seko_no = :seko_no
            AND si.delete_flg = 0
        ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));

        if ($select) {
            // 斎場予約の申請者に喪主を初期表示
            if (!$select['sj_yoyaku_last_nm'] && !$select['sj_yoyaku_first_nm'] && !$select['sj_yoyaku_zoku_kbn']) {
                // 喪主の情報が入っていれば表示
                $select['sj_yoyaku_last_nm'] = ( isset($select['cif_m_last_nm'])) ? $select['cif_m_last_nm'] : null;
                $select['sj_yoyaku_first_nm'] = ( isset($select['cif_m_first_nm'])) ? $select['cif_m_first_nm'] : null;
                $select['sj_yoyaku_zoku_kbn'] = ( isset($select['cif_m_zoku_kbn'])) ? $select['cif_m_zoku_kbn'] : null;
            }
            // 棺 総重量の初期表示（故人体重+5kg）
            if (!$select['hitsugi_weight'] && $select['k_weight']) {
                $select['hitsugi_weight'] = $select['k_weight'] + 5;
            }
            // TD送りのお送り先に施行日程（葬儀）情報を初期表示
            if (!$select['td_okuri_pick_up_kbn'] && !$select['td_okuri_pick_up_spot_cd'] && !$select['td_okuri_pick_up_cd'] && !$select['td_okuri_pick_up_nm']) {
                $select['td_okuri_pick_up_kbn'] = ( isset($select['sougi_basho_kbn'])) ? $select['sougi_basho_kbn'] : null;
                $select['td_okuri_pick_up_spot_cd'] = ( isset($select['sougi_spot_cd'])) ? $select['sougi_spot_cd'] : null;
                $select['td_okuri_pick_up_cd'] = ( isset($select['sougi_basho_cd'])) ? $select['sougi_basho_cd'] : null;
                $select['td_okuri_pick_up_nm'] = ( isset($select['sougi_basho_nm'])) ? $select['sougi_basho_nm'] : null;
            }
            // 霊柩搬送のお迎え先に施行日程（出棺）情報を初期表示
            if (!$select['rh_mukae_pick_up_spot_cd'] && !$select['rh_mukae_pick_up_cd'] && !$select['rh_mukae_pick_up_nm']) {
                $select['rh_mukae_pick_up_kbn'] = ( isset($select['shukkan_basho_kbn'])) ? $select['shukkan_basho_kbn'] : null;
                $select['rh_mukae_pick_up_spot_cd'] = ( isset($select['shukkan_spot_cd'])) ? $select['shukkan_spot_cd'] : null;
                $select['rh_mukae_pick_up_cd'] = ( isset($select['shukkan_basho_cd'])) ? $select['shukkan_basho_cd'] : null;
                $select['rh_mukae_pick_up_nm'] = ( isset($select['shukkan_basho_nm'])) ? $select['shukkan_basho_nm'] : null;
            }
            // 霊柩搬送の送り先に施行日程（火葬）情報を初期表示
            if (!$select['rh_okuri_pick_up_cd'] && !$select['rh_okuri_pick_up_nm']) {
                $select['rh_okuri_pick_up_cd'] = ( isset($select['kaso_basho_cd'])) ? $select['kaso_basho_cd'] : null;
                $select['rh_okuri_pick_up_nm'] = ( isset($select['kaso_basho_nm'])) ? $select['kaso_basho_nm'] : null;
            }
            // 出動実績データがある場合は安置場所情報を反映する
            $select = $this->getAnchiData($db, $select);
            // 棺のサイズは選択商品の備考欄を参照する
            $select['hitsugi_size'] = $this->getHitsugiSize($db);
        }

        return $select;
    }

    /**
     *
     * 報告書タブ　選択商品の備考欄から棺のサイズを取得する
     *
     * <AUTHOR> Mogi
     * @since  2020/09/xx
     * @return string 
     */
    private function getHitsugiSize($db) {

        $sql = "
            SELECT jichu_kakute_ymd 
            FROM seko_kihon_info 
            WHERE seko_no = :seko_no 
            AND delete_flg = 0"
        ;
        $jichu_kakute_ymd = $db->getOneVal($sql, array('seko_no' => $this->_sekoNo));

        if ($jichu_kakute_ymd) {
            // 確定済みの場合は売上伝票から取得
            $select_uri = "
        SELECT
            sm.shohin_tkiyo_nm
            --sm.shohin_nm
        FROM uriage_denpyo ud
        LEFT JOIN uriage_denpyo_msi udm
            ON udm.uri_den_no = ud.uri_den_no
            AND udm.dai_bunrui_cd = '0010'
            AND udm.chu_bunrui_cd = '0120'
            AND udm.shohin_kbn = '0530'
            AND udm.delete_flg = 0
        LEFT JOIN shohin_mst sm
            ON sm.bumon_cd = udm.shohin_bumon_cd
            AND sm.shohin_cd = udm.shohin_cd
            AND sm.delete_flg = 0
        WHERE ud.seko_no = :seko_no
            AND ud.data_kbn = 1
            AND ud.delete_flg = 0
        ";
            $hitsugi_size = $db->getOneVal($select_uri, array('seko_no' => $this->_sekoNo));
        } else {
            // 未確定の場合は受注伝票から取得
            $select_juchu = "
        SELECT
            sm.shohin_tkiyo_nm
            --sm.shohin_nm
        FROM juchu_denpyo jd
        LEFT JOIN juchu_denpyo_msi jdm
            ON jdm.denpyo_no = jd.denpyo_no
            AND jdm.dai_bunrui_cd = '0010'
            AND jdm.chu_bunrui_cd = '0120'
            AND jdm.shohin_kbn = '0530'
            AND jdm.delete_flg = 0
        LEFT JOIN shohin_mst sm
            ON sm.bumon_cd = jdm.shohin_bumon_cd
            AND sm.shohin_cd = jdm.shohin_cd
            AND sm.delete_flg = 0
        WHERE jd.seko_no = :seko_no
            AND jd.data_kbn = 1
            AND jd.delete_flg = 0
        ";
            $hitsugi_size = $db->getOneVal($select_juchu, array('seko_no' => $this->_sekoNo));
        }

        return $hitsugi_size;
    }

    /**
     *
     * 報告書タブ　出動実績テーブルから安置情報データを取得する
     *
     * <AUTHOR> Mogi
     * @since  2020/09/02
     * @return array $select
     */
    private function getAnchiData($db, $select) {
        $sql_anchi = "
        SELECT 
            iso_saki_kbn
            , anti_saki_nm
            , anti_yubin_no
            , anti_addr1
            , anti_addr2
            , anti_tel 
        FROM SYUTSUDO_JISEKI
        WHERE unkou_kbn = '2' -- 運行区分が搬送（移送）＝2
            AND hanso_result = '6' -- 搬送結果が安置＝6？のとき
            AND uketsuke_no = :seko_no
            AND delete_flg = 0
        ";
        $select_anchi = $db->easySelOne($sql_anchi, array('seko_no' => $this->_sekoNo));

        if ($select_anchi) {
            $select['anti_sisetu_nm'] = $select_anchi['anti_saki_nm'];
            $select['anti_yubin_no'] = $select_anchi['anti_yubin_no'];
            $select['anti_addr1'] = $select_anchi['anti_addr1'];
            $select['anti_addr2'] = $select_anchi['anti_addr2'];
            $select['anti_tel'] = $select_anchi['anti_tel'];

            // TD迎えのお迎え先に安置場所情報を初期表示
            if (!$select['td_mukae_pick_up_kbn'] && !$select['td_mukae_pick_up_spot_cd'] && !$select['td_mukae_pick_up_cd'] && !$select['td_mukae_pick_up_nm']) {
                $select['td_mukae_pick_up_nm'] = $select_anchi['anti_saki_nm'];
                // 移送先区分を葬儀場所区分に変換する
                $sougibasho_kbn = null;
                switch ($select_anchi['iso_saki_kbn']) {
                    case '1':   // 当社施設⇒02：自営式場
                        $sougibasho_kbn = '02';
                        break;
                    case '2':   // 自宅⇒00：自宅
                        $sougibasho_kbn = '00';
                        break;
                    case '3':   // その他⇒99：その他
                    case '4':   // 不明⇒99：その他
                        $sougibasho_kbn = '99';
                        break;
                    default:
                        $sougibasho_kbn = null;
                        break;
                }
                $select['td_mukae_pick_up_kbn'] = $sougibasho_kbn;
                $select['td_mukae_pick_up_spot_cd'] = $sougibasho_kbn;
            }
        }
        return $select;
    }

    /**
     *
     * 報告書タブ　CIF検索「その他」データを取得する
     *
     * <AUTHOR> Mogi
     * @since  2020/09/02
     * @return array $select
     */
    private function getCifOther($db, $select) {

        $sql_gojokai = "
        SELECT
            sgm.kanyu_nm
            , sgm.kanyu_knm
            , sgm.cif_no
        FROM seko_gojokai_member sgm
        LEFT JOIN seko_kihon_info ski
            ON sgm.seko_no = ski.seko_no
            AND ski.delete_flg = 0
        WHERE sgm.seko_no = :seko_no
            AND sgm.delete_flg = 0
            AND sgm.cif_no NOT IN (ski.k_cif_no, ski.m_cif_no, ski.k_cif_no)
        LIMIT 3
                ";
        $select_gojokai = $db->easySelect($sql_gojokai, array('seko_no' => $this->_sekoNo));

        if ($select_gojokai) {
            $cnt = 1;
            foreach ($select_gojokai as $onerow) {

//                $nm = str_replace('　', ' ', $onerow['kanyu_nm']);
                list($last_nm, $first_nm) = explode('　', $nm);
                list($last_nm_obj, $first_nm_obj) = array('cif_other' . $cnt . '_last_nm', 'cif_other' . $cnt . '_first_nm');
//                $knm = str_replace('　', ' ', $onerow['kanyu_knm']);
                list($last_knm, $first_knm) = explode('　', $knm);
                list($last_knm_obj, $first_knm_obj) = array('cif_other' . $cnt . '_last_knm', 'cif_other' . $cnt . '_first_knm');

                $select[$last_nm_obj] = $last_nm;
                $select[$first_nm_obj] = $first_nm;
                $select[$last_knm_obj] = $last_knm;
                $select[$first_knm_obj] = $first_knm;
                $select['cif_other' . $cnt . '_no'] = $onerow['cif_no'];

                $cnt++;
            }
        }
        return $select;
    }

    /**
     *
     * 報告書タブ　CIF検索「その他」データ保存処理
     *
     * <AUTHOR> Mogi
     * @since  2020/10/14
     * @return cnt
     */
    protected function saveCifOther($db, $dataGojokaiMemberCol, $dataSekyuInfo, $dataSekoKihon) {

        $count = 1;
        $row = array();
        $except = array();
        $k_nm = $dataSekoKihon['k_last_nm'] . $dataSekoKihon['k_first_nm'];
        $m_nm = $dataSekoKihon['m_last_nm'] . $dataSekoKihon['m_first_nm'];
        $s_nm = $dataSekyuInfo['sekyu_last_nm'] . $dataSekyuInfo['sekyu_first_nm'];

        if ($dataGojokaiMemberCol) {
            foreach ($dataGojokaiMemberCol as $oneRow) {
                // 4レコード目以降はbreak
                if ($count === 4) {
                    break;
                }

                // 削除項目にチェックがついている場合はスルー
                if ($oneRow['delete_check'] == '1') {
                    continue;
                }
                // 名前の分割
                $last_nm = null;
                $first_nm = null;
                $last_knm = null;
                $first_knm = null;
                if (isset($oneRow['kanyu_nm'])) {
                    $_nm = str_replace('　', ' ', $oneRow['kanyu_nm']);
                    $nm = explode('　', $_nm);
                    if (isset($nm[0])) {
                        $last_nm = $nm[0];
                    }
                    if (isset($nm[1])) {
                        $first_nm = $nm[1];
                    }
                }
                if (isset($oneRow['kanyu_knm'])) {
                    $_knm = str_replace('　', ' ', $oneRow['kanyu_knm']);
                    $knm = explode('　', $_knm);
                    if (isset($knm[0])) {
                        $last_knm = $knm[0];
                    }
                    if (isset($knm[1])) {
                        $first_knm = $knm[1];
                    }
                }
                // 故人・喪主・施主と名前が一致した場合はスルー
                if (in_array($last_nm . $first_nm, array($k_nm, $m_nm, $s_nm))) {
                    continue;
                }
                $row['cif_ken' . $count . '_last_nm'] = $last_nm; // 氏名（姓）
                $row['cif_ken' . $count . '_first_nm'] = $first_nm; // 氏名（名）
                $row['cif_ken' . $count . '_last_knm'] = $last_knm; // フリガナ（姓）
                $row['cif_ken' . $count . '_first_knm'] = $first_knm; // フリガナ（名）
                $row['cif_ken' . $count . '_kanyu_no'] = $oneRow['kain_no']; // 加入者番号
                if (isset($oneRow['cif_no'])) {
                    $row['cif_ken' . $count . '_cif_no'] = $oneRow['cif_no']; // CIFNo.
                }
                $count++;
            }
        }
        // 互助会会員情報が3レコード以下の場合は、余ったカラムをnullにして更新する。
        if ($count <= 3) {
            for ($i = $count; $i < 4; $i++) {
                $row['cif_ken' . $i . '_last_nm'] = null; // 氏名（姓）
                $row['cif_ken' . $i . '_first_nm'] = null; // 氏名（名）
                $row['cif_ken' . $i . '_last_knm'] = null; // フリガナ（姓）
                $row['cif_ken' . $i . '_first_knm'] = null; // フリガナ（名）
                $row['cif_ken' . $i . '_kanyu_no'] = null; // 加入者番号
                $row['cif_ken' . $i . '_cif_no'] = null; // CIFNo.
            }
        }

        // 施行引継手配情報 存在チェック
        $select = $this->getReport();
        if (!isset($select)) {
            $row['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_hiki_tehai_info", $row, $except);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_hiki_tehai_info", $row, $where, $except);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     * 式場移動情報 データを取得する
     *
     * <AUTHOR> Mogi
     * @since  2020/08/25
     * @return array 式場移動データ
     */
    protected function getShikijoIdo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            sim.seko_no
            , sim.msi_no
            , sim.tanto_cd
            , sim.tanto_nm
            , sim.ido_update_flg
            , TO_CHAR(sim.ido_date, 'YYYY/MM/DD') AS ido_date
            , TO_CHAR(sim.ido_time, 'HH24:MI') AS ido_time
            , sim.syutsodo_st
            , sim.syutsodo_st_ts
            , sim.prc_kbn
            , sim.mukae_pick_up_kbn
            , sim.mukae_pick_up_spot_cd
            , sim.mukae_pick_up_cd
            , sim.mukae_pick_up_nm
            , sim.okuri_pick_up_kbn
            , sim.okuri_pick_up_spot_cd
            , sim.okuri_pick_up_cd
            , sim.okuri_pick_up_nm
            , sim.biko
            , CASE sim.syutsodo_st
                WHEN 10 THEN cnm.kbn_value_lnm || '(' || cnm.kbn_value_snm || ')'
                WHEN 15 THEN cnm.kbn_value_lnm || '(' || cnm.kbn_value_snm || ')'
                ELSE cnm.kbn_value_lnm || '(' || cnm.kbn_value_snm || ' ' || TO_CHAR(sim.syutsodo_st_ts, 'YYYY/MM/DD HH24:MI') || ')'
              END AS syutsodo_st_disp
        FROM SHIKIJO_IDO_MSI sim
        LEFT JOIN seko_kihon_info kihon -- 施行基本情報
            ON sim.seko_no = kihon.seko_no
            AND kihon.delete_flg = 0
        LEFT JOIN code_nm_mst cnm     -- コード名称（TD迎えのステータス）
            ON cnm.code_kbn = '7981'
            AND cnm.kbn_value_cd_num = sim.syutsodo_st
            AND cnm.delete_flg = 0
        WHERE sim.seko_no = :seko_no
            AND sim.delete_flg = 0
        ORDER BY sim.msi_no
        ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));

        // データがない場合でも一行表示
        if (count($select) === 0) {
            array_push($select, array(
                'seko_no' => $this->_sekoNo,
                'msi_no' => 1,
                'tanto_cd' => null,
                'tanto_nm' => null,
                'ido_date' => null,
                'ido_time' => null,
                'prc_kbn' => null,
                'mukae_pick_up_kbn' => null,
                'mukae_pick_up_spot_cd' => null,
                'mukae_pick_up_cd' => null,
                'mukae_pick_up_nm' => null,
                'okuri_pick_up_kbn' => null,
                'okuri_pick_up_spot_cd' => null,
                'okuri_pick_up_cd' => null,
                'okuri_pick_up_nm' => null,
                'biko' => null,
            ));
        }

        return $select;
    }

    /**
     * 式場移動情報 保存処理 
     *
     * <AUTHOR> Mogi
     * @since 2020/08/26
     * @param Msi_Sys_Db $db db
     * @param array $dataShikijoCol 式場移動データ
     * @return int 更新件数
     */
    protected function saveShikijoIdo($db, $dataShikijoCol) {

        // 一旦全てdelete_flg=0にする
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL('shikijo_ido_msi', array('delete_flg' => 0), array('seko_no' => $this->_sekoNo));
        //SQL実行
        $cnt = $db->easyExecute($sql, $param);
        // DBに保存されている現在の式場移動データを取得する
        $olddata = $this->getShikijoIdo();
        // 1:変更チェックで保存更新用データを抽出
        $NewDataArray = array();
        $new_data_id = null;
        $old_data_id = null;
        $new_msi_no_ary = array();
        foreach ($dataShikijoCol as $newrow) {
            $new_msi_no_ary[] = $newrow['msi_no'];
            $new_data_id = $newrow['msi_no'] . '_' . $newrow['tanto_cd'] . '_' . $newrow['tanto_nm'] . '_' . $newrow['ido_date'] . '_' . $newrow['ido_time'] . '_' . $newrow['prc_kbn']
                    . '_' . $newrow['mukae_pick_up_kbn'] . '_' . $newrow['mukae_pick_up_spot_cd'] . '_' . $newrow['mukae_pick_up_cd'] . '_' . $newrow['mukae_pick_up_nm']
                    . '_' . $newrow['okuri_pick_up_kbn'] . '_' . $newrow['okuri_pick_up_spot_cd'] . '_' . $newrow['okuri_pick_up_cd'] . '_' . $newrow['okuri_pick_up_nm'] . '_' . $newrow['biko'];
            $flg = 0;

            foreach ($olddata as $oldrow) {
                $old_data_id = $oldrow['msi_no'] . '_' . $oldrow['tanto_cd'] . '_' . $oldrow['tanto_nm'] . '_' . $oldrow['ido_date'] . '_' . $oldrow['ido_time'] . '_' . $oldrow['prc_kbn']
                        . '_' . $oldrow['mukae_pick_up_kbn'] . '_' . $oldrow['mukae_pick_up_spot_cd'] . '_' . $oldrow['mukae_pick_up_cd'] . '_' . $oldrow['mukae_pick_up_nm']
                        . '_' . $oldrow['okuri_pick_up_kbn'] . '_' . $oldrow['okuri_pick_up_spot_cd'] . '_' . $oldrow['okuri_pick_up_cd'] . '_' . $oldrow['okuri_pick_up_nm'] . '_' . $oldrow['biko'];
                // 変更前と後で全てが一致する場合
                if ($new_data_id == $old_data_id) {
                    $flg = 0;
                    break;
                }
                $flg = 1;
            }
            if ($flg == 1) {
                $NewDataArray[] = $newrow;
            }
        }
        // 2:削除データの更新
        foreach ($olddata as $oldrow) {
            if (!in_array($oldrow['msi_no'], $new_msi_no_ary)) { // 新しいデータに無い明細番号のレコードは削除する
                //削除対象コードなので削除SQL作成
                //条件部セット
                $where['seko_no'] = $this->_sekoNo;
                $where['msi_no'] = $oldrow['msi_no'];
                $data = array();
                //無効データに変換
                $data['delete_flg'] = 1;

                //更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL('shikijo_ido_msi', $data, $where);
                //SQL実行
                $cnt = $db->easyExecute($sql, $param);
            }
        }
        // 3:更新データの登録・更新
        foreach ($NewDataArray as $oneRow) {
            $keyHash = array(
                'seko_no' => $this->_sekoNo,
                'msi_no' => $oneRow['msi_no']
            );

            // 空文字データはnullに置き換える
            $oneRow = Msi_Sys_Utils::emptyToNullArr($oneRow);
            // 一行目のみ、人員配置で登録されるデータのため 該当項目を更新対象から排除する
            if ($oneRow['msi_no'] == '1') {
                unset($oneRow['tanto_cd']);
                unset($oneRow['tanto_nm']);
                unset($oneRow['ido_date']);
                unset($oneRow['ido_time']);
            }
            // キーデータは除外
            unset($oneRow['seko_no']);
            unset($oneRow['msi_no']);
            // 更新対象外のデータ
            unset($oneRow['syutsodo_st_disp']);
            unset($oneRow['ido_update_flg']);
            unset($oneRow['syutsodo_st']);
            unset($oneRow['syutsodo_st_ts']);
            $dataHash = $oneRow;
            // 式場移動明細登録・更新
            DataMapper_Utils::upsert($db, 'shikijo_ido_msi', $keyHash, $dataHash);
        }
        return;
    }

    /**
     * 施行引継書 保存処理 
     *
     * <AUTHOR> Mogi
     * @since  2020/08/07
     * @param Msi_Sys_Db $db db
     * @param array $dataHikitsugi 施行引継書データ
     * @return int 更新件数
     */
    protected function saveHikitsugi($db, $dataHikitsugi) {
        // 施行引継書項目のみを更新対象とする。
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataHikitsugi, 'keihu_etc keihu_f_name info_entry kazokukosei_kkn_kbn kk_tsuya_time kk_kokubetu_time kk_tsuya_kokubetu_kkn_kbn kk_reikyusya reikyusya_sk_kibo kk_reikyusya_kkn_kbn 
                                                              henrei_bland_st_um_kbn henrei_bland henrei_st_reason inryo_bland_st_um_kbn inryo_bland inryo_st_reason other1_bland_cat other1_bland_st_um_kbn 
                                                              other1_bland other1_st_reason other2_bland_cat other2_bland_st_um_kbn other2_bland other2_st_reason other3_bland_cat other3_bland_st_um_kbn other3_bland
                                                              other3_st_reason bland_st_kkn_kbn cif_ken_kkn_kbn insyoku_srv_exp_kbn insyoku_srv_exp_kkn_kbn zei_exp_kbn zei_exp_kkn_kbn shiharai_kigen_exp_kbn
                                                              shiharai_houhou_exp_kbn shiharai_exp_kkn_kbn kichobako_pass_kbn kichobako_pass_kkn_kbn syukyo_renraku_kbn syukyo_renraku_f_name syukyo_renraku_kkn_kbn
                                                              shindai_add_exp_kbn idotime_exp_kbn idotime_exp_memo idotime_exp_kkn_kbn choji_exp_kbn choji_meo_exp_kbn choji1_nm choji1_last_nm choji1_first_nm
                                                              choji1_kankei choji1_renraku choji1_presen_date_kbn choji1_presen_date choji2_nm choji2_last_nm choji2_first_nm choji2_kankei choji2_renraku choji2_presen_date_kbn
                                                              choji2_presen_date choji_exp_kkn_kbn osewa_exp_kbn osewa_zentai_ninzu osewa_uketuke_ninzu osewa_kaikei_ninzu osewa_cloak_ninzu osewa_henrei_ninzu osewa_other1_memo
                                                              osewa_other1_ninzu osewa_other2_memo osewa_other2_ninzu osewa_kbn_um_kbn osewa_kbn_nm osewa_placard_um_kbn osewa_placard_cnt osewa_exp_kkn_kbn 
                                                              mmosonae_timing_exp_kbn mmosonae_exp_memo mmosonae_timing_exp_kkn_kbn kyoka_cm_houhou_exp_kbn kyoka_cm_timing kyoka_cm_timing_other kyoka_cm_kisu_exp_kbn
                                                              kyoka_exp_kisu kyoka_exp_kkn_kbn tenji_um_kbn tenji_memo bgm_um_kbn bgm_memo kojin_syozoku_um_kbn kojin_syozoku_nm kojin_syumi_memo tenji_exp_kkn_kbn
                                                              ss_exp_kbn ss_orei_exp_um_kbn ss_kuruma_exp_um_kbn ss_ozen_exp_um_kbn ss_sunshi_exp_um_kbn ss_other_exp_um_kbn ss_other_exp_memo kasoba_exp_kbn 
                                                              kasoba_kaso_exp_um_kbn kasoba_baiten_exp_um_kbn kasoba_machiai_exp_um_kbn kasoba_other_memo other_exp_kbn other_memo shikijo_exp_kbn shikijo_anchi_exp_um_kbn
                                                              shikijo_shikijo_exp_um_kbn shikijo_shingu_exp_um_kbn shikijo_taxi_exp_um_kbn shikijo_other_exp_um_kbn shikijo_other_memo tojitsu_keihi_exp_kkn_kbn
                                                              syukuhaku_exp_kbn syukuhaku_yotei_cnt shisetsu_exp_kbn shingu_exp_kbn kingaku_exp_kbn hotel_exp_kbn chosyoku_exp_kbn beshitu_kibo_setumei_um_kbn 
                                                              syukuhaku_exp_kkn_kbn bagpass_exp_kbn bagpass_bag_type bagpass_bag_other_memo timing_syukyo_exp_kbn timing_syukyo_memo timing_osewa_exp_kbn timing_osewa_memo
                                                              oreiwatashi_exp_kkn_kbn aisatu_tsuya_exp_kbn aisatu_tsuya_timing aisatu_tsuya_nm aisatu_kokubetu_exp_kbn aisatu_kokubetu_timing aisatu_kokubetu_nm 
                                                              aisatu_kenpai_exp_kbn aisatu_kenpai_nm aisatu_sankai_exp_kbn aisatu_sankai_nm aisatu_other_exp_kbn aisatu_other_memo aisatu_other_timing aisatu_other_nm
                                                              aisatu_exp_kkn_kbn kaso_waiting_roomadd_exp_kbn kaso_waiting_roomadd_exp_memo kaso_waiting_ryoricnt_exp_kbn kaso_waiting_ryoricnt_exp_memo
                                                              kaso_waiting_room_exp_kkn_kbn ryori_tsuya_syukyo_cnt ryori_tsuya_syukyo_memo ryori_tsuya_izoku_cnt ryori_tsuya_izoku_memo ryori_tsuya_osewa_cnt
                                                              ryori_tsuya_osewa_memo ryori_tsuya_sanretu_cnt ryori_tsuya_sanretu_memo ryori_tsuya_other_kbn_nm ryori_tsuya_other_cnt ryori_tsuya_other_memo ryori_tsuya_memo
                                                              ryori_sougi_syukyo_cnt ryori_sougi_syukyo_memo ryori_sougi_kagezen_cnt ryori_sougi_kagezen_memo ryori_sougi_izoku_cnt ryori_sougi_izoku_memo ryori_sougi_osewa_cnt
                                                              ryori_sougi_osewa_memo ryori_sougi_other_kbn_nm ryori_sougi_other_cnt ryori_sougi_other_memo ryori_sougi_memo ryori_kkn_kbn sj_exp_kbn sj_presen_exp_um_kbn
                                                              sj_mitsu_exp_um_kbn sj_note_exp_um_kbn sj_ichizen_exp_um_kbn sj_makuradango_exp_um_kbn sj_shukuhakubihin_exp_um_kbn sj_tenji_exp_um_kbn sj_fukuso_exp_um_kbn
                                                              sj_bgm_exp_um_kbn sj_other_exp_um_kbn sj_exp_kkn_kbn pm_exp_kbn pm_exp_kkn_kbn pm_contents_kbn pm_contents_kkn_kbn other_hiki_memo other_hiki_kkn_kbn
                                                              bagpass_ofuse bagpass_okuruma bagpass_ozen bagpass_sunshi bagpass_saishi bagpass_muji sj_other_memo keihu_file_name'
        );
        // emptyToNull
        // 空文字データはnullに置き換える
        $oneRow = Msi_Sys_Utils::emptyToNullArr($oneRow);

        // 更新対象外
        $except = array();

        // 施行引継手配情報 存在チェック
        $select = $this->getReport();
        if (!isset($select)) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_hiki_tehai_info", $oneRow, $except);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_hiki_tehai_info", $oneRow, $where, $except);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 手配依頼者 保存処理 
     *
     * <AUTHOR> Mogi
     * @since  2020/08/07
     * @param Msi_Sys_Db $db db
     * @param array $dataTehaiIrai 手配依頼書データ
     * @return int 更新件数
     */
    protected function saveTehaiIrai($db, $dataTehaiIrai) {
        // 手配依頼書項目のみ更新対象とする。
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataTehaiIrai, 'reikyusya_kbn reikyusya_tanto_cd reikyusya_tanto_nm anti_sisetu_nm anti_basho_nm anti_saki_nm anti_yubin_no anti_addr1 anti_addr2 anti_tel td_syochi_yotei_kbn
                                                                td_syochi_yotei_date td_syochi_nokan_kbn yukan_syochi_sbt_kbn yukan_syochi_basho_kbn yukan_syochi_yotei_date yukan_syochi_nokan_kbn sougi_prc 
                                                                mitsu_doukou_tanto_cd mitsu_doukou_tanto_nm sougei_service_umu_kbn tubo_chokoku_umu_kbn tehai_placard_umu_kbn advance_consult_umu_kbn advance_consult_ymd
                                                                advance_consult_basho kingakubetsu_henrei_umu_kbn file_storage_basho_nm yakusho_process_basho_nm sougi_tatekae_prc 
                                                                td_mukae_prc_kbn td_mukae_pick_up_kbn td_mukae_pick_up_cd
                                                                td_mukae_pick_up_nm td_mukae_biko 
                                                                td_okuri_prc_kbn td_okuri_pick_up_kbn td_okuri_pick_up_cd td_okuri_pick_up_nm td_okuri_biko td_hanso_tehai_status_kbn
                                                                rh_prc_kbn rh_mukae_pick_up_kbn rh_mukae_pick_up_cd
                                                                rh_mukae_pick_up_nm rh_okuri_pick_up_kbn rh_okuri_pick_up_cd rh_okuri_pick_up_nm rh_okuri_biko 
                                                                yukan_tehai_status_kbn nokan_basho_cd nokan_basho_nm nokan_yubin_no
                                                                nokan_addr1 nokan_addr2 nokan_tel family_tachiai_umu_kbn nokan_tanto_cd1 nokan_tanto_nm1 nokan_tanto_cd2 nokan_tanto_nm2 hitsusgi_nm funeral_dress_kbn
                                                                funeral_dress_nm funeral_dress_way_kbn nokan_tehai_status_kbn saidan_setup_kbn
                                                                zenkan_setup_kbn zenkan_setup_f_name other_setup_kbn other_setup_f_name setup_tehai_status_kbn az_tehai_umu_kbn 
                                                                az_basho_cd az_basho_nm az_yubin_no az_addr1 az_addr2 az_biko az_kaiin_id_kbn az_certificate_kbn az_stamp_kbn az_photo_kbn az_clothes_kbn
                                                                az_exhibit_kbn az_bgm_kbn az_bgm_memo hrz_k1_tehai_status hrz_k1_basho_cd
                                                                hrz_k1_basho_nm hrz_k1_yubin_no hrz_k1_addr1 hrz_k1_addr2 hrz_k1_basyo_tel hrz_k1_biko 
                                                                hrz_k2_tehai_status hrz_k2_basho_cd hrz_k2_basho_nm hrz_k2_yubin_no hrz_k2_addr1 hrz_k2_addr2 hrz_k2_basyo_tel hrz_k2_biko 
                                                                hrz_k3_tehai_status hrz_k3_basho_cd hrz_k3_basho_nm hrz_k3_yubin_no hrz_k3_addr1 hrz_k3_addr2
                                                                hrz_k3_basyo_tel hrz_k3_biko ms_tehai_umu_kbn ms_basho_cd ms_basho_nm ms_yubin_no ms_addr1
                                                                ms_addr2 ms_tel ms_daichujo_kbn ms_gosyaku_kbn ms_rokusyaku_kbn ms_ihai_cnt ms_shirakiihai_kbn ms_toba_kbn ms_toba_syaku ms_toba_cnt ms_rokkakubou_kbn 
                                                                ms_noihai_kbn ms_nanahon_kbn ms_jyusanhon_kbn ms_bohyo_kbn ms_reiji_kbn ms_biko
                                                                tehai_biko sj_yoyaku_kbn sj_yoyaku_no sj_yoyaku_nm sj_yoyaku_last_nm sj_yoyaku_first_nm sj_yoyaku_zoku_kbn sj_kasoba_annai_disp_kbn sj_kasoba_annai_disp_sitei
                                                                sj_syukotsu_annai_hoso_kbn owakare_method_kbn hitsugi_size hitsugi_weight gaiji_umu_kbn gaiji_user_kbn1 gaiji_user_cd1 gaiji_user_kbn2 gaiji_user_cd2
                                                                gaiji_user_kbn3 gaiji_user_cd3 gaiji_user_kbn4 gaiji_user_cd4 td_mukae_pick_up_spot_cd td_okuri_pick_up_spot_cd rh_mukae_pick_up_spot_cd rh_okuri_pick_up_spot_cd
                                                                nokan_spot_cd az_spot_cd hrz_k1_spot_cd hrz_k2_spot_cd hrz_k3_spot_cd ms_spot_cd td_syochi_yotei_cd td_mukae_o_pick_up_kbn td_mukae_o_pick_up_spot_cd  
                                                                td_okuri_m_pick_up_kbn td_okuri_m_pick_up_spot_cd zenkan_setup_file_name other_setup_file_name'
        );
        // emptyToNull   
        // 空文字データはnullに置き換える
        $oneRow = Msi_Sys_Utils::emptyToNullArr($oneRow);

        // 更新対象外
        $except = array();

        // 施行引継手配情報 存在チェック
        $select = $this->getReport();
        if (!isset($select)) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_hiki_tehai_info", $oneRow, $except);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_hiki_tehai_info", $oneRow, $where, $except);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     * ワンタイムノンス発行処理
     *
     * <AUTHOR> Sai
     * @since  2020/02/18
     * @param string $expire_sec 有効期限秒数
     * @param string $proc_max_cnt 実行可能回数
     * @return array Nonce配列情報（Nonce,有効期限）
     */
    protected function genOtNonceInfo($expire_sec = 60 * 60 * 24 * 30, $proc_max_cnt = 9999) {
        $attr['expire_sec'] = $expire_sec;
        $attr['proc_max_cnt'] = $proc_max_cnt;
        $nonce = App_OtUserUtilsAbst::genOtUser(Msi_Sys_Utils::getUserCd(), $attr);
        $expire_ts = null;
        if (strlen($nonce) > 0) {
            $sysDb = Msi_Sys_DbManager::getDb('systr');
            $otUserRec = DataMapper_SOtUser::findOne($sysDb, array('user_nonce' => $nonce));
            if (Msi_Sys_Utils::myCount($otUserRec) > 0) {
                $expire_ts = $otUserRec['expire_ts'];
            }
        }
        $ret['nonce'] = $nonce;
        $ret['expire_ts'] = $expire_ts;
        return $ret;
    }

    /**
     * 契約Cif情報生成処理
     * 
     * <AUTHOR> Tosaka
     * @since      2020/03/09
     * @return  array 
     */
    protected function getKeiyakuCif() {

        $db = Msi_Sys_DbManager::getMyDb();
        if (isset($this->_sekoNo)) {
            $kihonInfo = DataMapper_SekoKihon::find2($this->_sekoNo);
            if ($this->_moushiKbn === static::MOUSHI_KBN_HOUJI) {
                $kojinInfo = DataMapper_SekoKojinInfoHouji::find($db, array('seko_no' => $this->_sekoNo));
                $kihonInfo['dataKojinInfo'] = $kojinInfo;
            }
//            $data = $this->getKeiyakuCifData($kihonInfo);
        } else {
            $data = array(array('id' => '', 'text' => ''));
        }
        return $data;
    }

    /**
     *
     * CIFの契約情報取得処理
     *
     * <AUTHOR> Tosaka
     * @since 2021/04/xx
     * @return array 施行契約先情報
     */
    public static function getKeiyakuCifData($kihonInfo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $data = array();
        if (isset($kihonInfo['seko_no'])) {
            if (isset($kihonInfo['k_cif_no'])) {
                list($errMsg, $aResult, $resData) = Logic_HakuApi_LogicCifList::search(array('CustomerNo' => $kihonInfo['k_cif_no']));
                if (strlen($errMsg) === 0) {
                    foreach ($aResult as $value) {
                        foreach ($value['ContractInfos'] as $one) {
                            if ($one['Available'] == '1') {
                                // 有効かつ契約種別にヒットしたもののみ(一般以外)
                                $cdNm = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8573', 'kbn_value_cd_num' => $one['ContractType']));
                                if (count($cdNm) > 0) {
                                    $rec = array();
                                    $rec['id'] = $one['ContractNo'];
                                    $rec['CustomerNo'] = $value['CustomerNo'];
                                    $rec['kaiin_kbn'] = $cdNm[0]['biko'];
                                    $rec['ContractType'] = $one['ContractType'];
                                    // テキストに表示する文言を取得する
                                    $InternalType = Msi_Sys_Utils::emptyToNull(str_replace(array(" ", "　"), "", $one['InternalType']));
                                    if (isset($InternalType)) {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], 'kbn_value_lnm' => $one['InternalType']));
                                    } else {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], '__raw_kbn_lnm' => "T.kbn_value_lnm IS NULL"));
                                    }
                                    if (count($cdNm2) > 0) {
                                        $rec['text'] = $value['NameF'] . $value['NameG'] . ' ' . $cdNm2[0]['kbn_value_snm'];
                                        $rec['keiyaku_nai_sbt'] = $cdNm2[0]['kbn_value_cd'];
                                        $rec['mst_text'] = $cdNm2[0]['kbn_value_snm'];
                                    } else {
                                        $rec['text'] = 'エラー';
                                    }
                                    $data[$one['ContractNo']] = $rec;
                                }
                            }
                        }
                    }
                }
            }
            if (isset($kihonInfo['m_cif_no'])) {
                list($errMsg, $aResult, $resData) = Logic_HakuApi_LogicCifList::search(array('CustomerNo' => $kihonInfo['m_cif_no']));
                if (strlen($errMsg) === 0) {
                    foreach ($aResult as $value) {
                        foreach ($value['ContractInfos'] as $one) {
                            if ($one['Available'] == '1') {
                                // 有効かつ契約種別にヒットしたもののみ(一般以外)
                                $cdNm = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8573', 'kbn_value_cd_num' => $one['ContractType']));
                                if (count($cdNm) > 0) {
                                    $rec = array();
                                    $rec['id'] = $one['ContractNo'];
                                    $rec['CustomerNo'] = $value['CustomerNo'];
                                    $rec['kaiin_kbn'] = $cdNm[0]['biko'];
                                    $rec['ContractType'] = $one['ContractType'];
                                    // テキストに表示する文言を取得する
                                    $InternalType = Msi_Sys_Utils::emptyToNull(str_replace(array(" ", "　"), "", $one['InternalType']));
                                    if (isset($InternalType)) {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], 'kbn_value_lnm' => $one['InternalType']));
                                    } else {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], '__raw_kbn_lnm' => "T.kbn_value_lnm IS NULL"));
                                    }
                                    if (count($cdNm2) > 0) {
                                        $rec['text'] = $value['NameF'] . $value['NameG'] . ' ' . $cdNm2[0]['kbn_value_snm'];
                                        $rec['keiyaku_nai_sbt'] = $cdNm2[0]['kbn_value_cd'];
                                        $rec['mst_text'] = $cdNm2[0]['kbn_value_snm'];
                                    } else {
                                        $rec['text'] = 'エラー';
                                    }
                                    $data[$one['ContractNo']] = $rec;
                                }
                            }
                        }
                    }
                }
            }
            if (isset($kihonInfo['s_cif_no'])) {
                list($errMsg, $aResult, $resData) = Logic_HakuApi_LogicCifList::search(array('CustomerNo' => $kihonInfo['s_cif_no']));
                if (strlen($errMsg) === 0) {
                    foreach ($aResult as $value) {
                        foreach ($value['ContractInfos'] as $one) {
                            if ($one['Available'] == '1') {
                                // 有効かつ契約種別にヒットしたもののみ(一般以外)
                                $cdNm = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8573', 'kbn_value_cd_num' => $one['ContractType']));
                                if (count($cdNm) > 0) {
                                    $rec = array();
                                    $rec['id'] = $one['ContractNo'];
                                    $rec['CustomerNo'] = $value['CustomerNo'];
                                    $rec['kaiin_kbn'] = $cdNm[0]['biko'];
                                    $rec['ContractType'] = $one['ContractType'];
                                    // テキストに表示する文言を取得する
                                    $InternalType = Msi_Sys_Utils::emptyToNull(str_replace(array(" ", "　"), "", $one['InternalType']));
                                    if (isset($InternalType)) {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], 'kbn_value_lnm' => $one['InternalType']));
                                    } else {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], '__raw_kbn_lnm' => "T.kbn_value_lnm IS NULL"));
                                    }
                                    if (count($cdNm2) > 0) {
                                        $rec['text'] = $value['NameF'] . $value['NameG'] . ' ' . $cdNm2[0]['kbn_value_snm'];
                                        $rec['keiyaku_nai_sbt'] = $cdNm2[0]['kbn_value_cd'];
                                        $rec['mst_text'] = $cdNm2[0]['kbn_value_snm'];
                                    } else {
                                        $rec['text'] = 'エラー';
                                    }
                                    $data[$one['ContractNo']] = $rec;
                                }
                            }
                        }
                    }
                }
            }
            if (isset($kihonInfo['keiyaku_cif_no'])) {
                list($errMsg, $aResult, $resData) = Logic_HakuApi_LogicCifList::search(array('CustomerNo' => $kihonInfo['keiyaku_cif_no']));
                if (strlen($errMsg) === 0) {
                    foreach ($aResult as $value) {
                        foreach ($value['ContractInfos'] as $one) {
                            if ($one['Available'] == '1') {
                                // 有効かつ契約種別にヒットしたもののみ(一般以外)
                                $cdNm = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8573', 'kbn_value_cd_num' => $one['ContractType']));
                                if (count($cdNm) > 0) {
                                    $rec = array();
                                    $rec['id'] = $one['ContractNo'];
                                    $rec['CustomerNo'] = $value['CustomerNo'];
                                    $rec['kaiin_kbn'] = $cdNm[0]['biko'];
                                    $rec['ContractType'] = $one['ContractType'];
                                    // テキストに表示する文言を取得する
                                    $InternalType = Msi_Sys_Utils::emptyToNull(str_replace(array(" ", "　"), "", $one['InternalType']));
                                    if (isset($InternalType)) {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], 'kbn_value_lnm' => $one['InternalType']));
                                    } else {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], '__raw_kbn_lnm' => "T.kbn_value_lnm IS NULL"));
                                    }
                                    if (count($cdNm2) > 0) {
                                        $rec['text'] = $value['NameF'] . $value['NameG'] . ' ' . $cdNm2[0]['kbn_value_snm'];
                                        $rec['keiyaku_nai_sbt'] = $cdNm2[0]['kbn_value_cd'];
                                        $rec['mst_text'] = $cdNm2[0]['kbn_value_snm'];
                                    } else {
                                        $rec['text'] = 'エラー';
                                    }
                                    $data[$one['ContractNo']] = $rec;
                                }
                            }
                        }
                    }
                }
            }
            if (isset($kihonInfo['renraku_cif_no'])) {
                list($errMsg, $aResult, $resData) = Logic_HakuApi_LogicCifList::search(array('CustomerNo' => $kihonInfo['renraku_cif_no']));
                if (strlen($errMsg) === 0) {
                    foreach ($aResult as $value) {
                        foreach ($value['ContractInfos'] as $one) {
                            if ($one['Available'] == '1') {
                                // 有効かつ契約種別にヒットしたもののみ(一般以外)
                                $cdNm = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8573', 'kbn_value_cd_num' => $one['ContractType']));
                                if (count($cdNm) > 0) {
                                    $rec = array();
                                    $rec['id'] = $one['ContractNo'];
                                    $rec['CustomerNo'] = $value['CustomerNo'];
                                    $rec['kaiin_kbn'] = $cdNm[0]['biko'];
                                    $rec['ContractType'] = $one['ContractType'];
                                    // テキストに表示する文言を取得する
                                    $InternalType = Msi_Sys_Utils::emptyToNull(str_replace(array(" ", "　"), "", $one['InternalType']));
                                    if (isset($InternalType)) {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], 'kbn_value_lnm' => $one['InternalType']));
                                    } else {
                                        $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], '__raw_kbn_lnm' => "T.kbn_value_lnm IS NULL"));
                                    }
                                    if (count($cdNm2) > 0) {
                                        $rec['text'] = $value['NameF'] . $value['NameG'] . ' ' . $cdNm2[0]['kbn_value_snm'];
                                        $rec['keiyaku_nai_sbt'] = $cdNm2[0]['kbn_value_cd'];
                                        $rec['mst_text'] = $cdNm2[0]['kbn_value_snm'];
                                    } else {
                                        $rec['text'] = 'エラー';
                                    }
                                    $data[$one['ContractNo']] = $rec;
                                }
                            }
                        }
                    }
                }
            }
            if (isset($kihonInfo['dataKojinInfo'])) {
                foreach ($kihonInfo['dataKojinInfo'] as $val) {
                    if (isset($val['hk_cif_no'])) {
                        list($errMsg, $aResult, $resData) = Logic_HakuApi_LogicCifList::search(array('CustomerNo' => $val['hk_cif_no']));
                        if (strlen($errMsg) === 0) {
                            foreach ($aResult as $value) {
                                foreach ($value['ContractInfos'] as $one) {
                                    if ($one['Available'] == '1') {
                                        // 有効かつ契約種別にヒットしたもののみ(一般以外)
                                        $cdNm = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8573', 'kbn_value_cd_num' => $one['ContractType']));
                                        if (count($cdNm) > 0) {
                                            $rec = array();
                                            $rec['id'] = $one['ContractNo'];
                                            $rec['CustomerNo'] = $value['CustomerNo'];
                                            $rec['kaiin_kbn'] = $cdNm[0]['biko'];
                                            $rec['ContractType'] = $one['ContractType'];
                                            // テキストに表示する文言を取得する
                                            $InternalType = Msi_Sys_Utils::emptyToNull(str_replace(array(" ", "　"), "", $one['InternalType']));
                                            if (isset($InternalType)) {
                                                $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], 'kbn_value_lnm' => $one['InternalType']));
                                            } else {
                                                $cdNm2 = DataMapper_CodeNmMst::find($db, array('code_kbn' => '8575', 'kbn_value_cd_num' => $one['ContractType'], '__raw_kbn_lnm' => "T.kbn_value_lnm IS NULL"));
                                            }
                                            if (count($cdNm2) > 0) {
                                                $rec['text'] = $value['NameF'] . $value['NameG'] . ' ' . $cdNm2[0]['kbn_value_snm'];
                                                $rec['keiyaku_nai_sbt'] = $cdNm2[0]['kbn_value_cd'];
                                                $rec['mst_text'] = $cdNm2[0]['kbn_value_snm'];
                                            } else {
                                                $rec['text'] = 'エラー';
                                            }
                                            $data[$one['ContractNo']] = $rec;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (count($data) == 0) {
            $_data = array(array('id' => '', 'text' => ''));
        } else {
            // キー降りなおし
            $_data = array_values($data);
        }
        return $_data;
    }

    /**
     * 現況コード取得処理
     *
     * <AUTHOR> Tosaka
     * @since  2020/06/12
     */
    protected function getAbledGenkyoCd() {

        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
            SELECT cnm.*
            FROM code_nm_mst cnm
            WHERE cnm.code_kbn = :code_kbn
                AND cnm.biko LIKE '%利用可能%'
            ORDER BY cnm.disp_nox
        ";
        $select = $db->easySelect($sql, array('code_kbn' => self::CODE_KBN_GENKYO));
        return $select;
    }

    /**
     * 訃報連絡情報取得処理
     *
     * <AUTHOR> Tosaka
     * @since  2021/xx/xx
     */
    protected function getHuhoInfo() {

        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
            SELECT hri.seko_no 
                ,hri.greeting
                ,hri.name_setting_before
                ,hri.name_setting_after
                ,ski.keishiki_kbn AS huho_keishiki_kbn
                ,hri.schedule_name1_1 AS tuya_nm
                ,hri.schedule_name1_2 AS nitei_other_nm1
                ,hri.youtube_live_flag
                ,hri.telegram_use_flag
                ,hri.flower_use_flag
                ,hri.offerings_use_flag
                ,hri.funeral_gift_use_flag
                ,hri.guide_title
                ,hri.chief_mourner_title
                ,hri.title1
                ,hri.name1
                ,hri.title2
                ,hri.name2
                ,hri.remarks
                ,hri.funeral_style
                ,hri.schedule_name2_1 AS sougi_nm
                ,hri.schedule_name2_2 AS nitei_other_nm2
                ,hri.hall_name1 AS huho_tuya_basho_nm
                ,hri.hall_postal_code1 AS huho_tuya_zip
                ,hri.hall_address1 AS huho_tuya_addr
                ,hri.hall_phone_number1 AS huho_tuya_tel
                ,hri.hall_name2 AS huho_sougi_basho_nm
                ,hri.hall_postal_code2 AS huho_sougi_zip
                ,hri.hall_address2 AS huho_sougi_addr
                ,hri.hall_phone_number2 AS huho_sougi_tel
                ,TO_CHAR(hri.schedule_time1_1 ,'YYYY/MM/DD HH24:MI') AS huho_tuya_ymd
                ,TO_CHAR(hri.schedule_time1_1 ,'YYYY/MM/DD') AS huho_tuya_date
                ,TO_CHAR(hri.schedule_time1_1 ,'HH24:MI') AS huho_tuya_time
                ,TO_CHAR(hri.schedule_time1_2 ,'YYYY/MM/DD HH24:MI') AS nitei_other_ymd1
                ,TO_CHAR(hri.schedule_time1_2 ,'YYYY/MM/DD') AS nitei_other_date1
                ,TO_CHAR(hri.schedule_time1_2 ,'HH24:MI') AS nitei_other_time1
                ,TO_CHAR(hri.schedule_time2_1 ,'YYYY/MM/DD HH24:MI') AS huho_sougi_ymd
                ,TO_CHAR(hri.schedule_time2_1 ,'YYYY/MM/DD') AS huho_sougi_date
                ,TO_CHAR(hri.schedule_time2_1 ,'HH24:MI') AS huho_sougi_time
                ,TO_CHAR(hri.schedule_time2_2 ,'YYYY/MM/DD HH24:MI') AS nitei_other_ymd2
                ,TO_CHAR(hri.schedule_time2_2 ,'YYYY/MM/DD') AS nitei_other_date2
                ,TO_CHAR(hri.schedule_time2_2 ,'HH24:MI') AS nitei_other_time2
                ,TO_CHAR(hri.funeral_site_limit ,'YYYY/MM/DD HH24:MI') AS funeral_site_limit
                ,TO_CHAR(hri.funeral_site_limit ,'YYYY/MM/DD') AS funeral_site_limit_date
                ,TO_CHAR(hri.funeral_site_limit ,'HH24:MI') AS funeral_site_limit_time
                ,TO_CHAR(hri.chief_mourner_login_limit ,'YYYY/MM/DD HH24:MI') AS chief_mourner_login_limit
                ,TO_CHAR(hri.chief_mourner_login_limit ,'YYYY/MM/DD') AS chief_mourner_login_limit_date
                ,TO_CHAR(hri.chief_mourner_login_limit ,'HH24:MI') AS chief_mourner_login_limit_time
                ,hri.huho_id
                ,hri.login_id
                ,hri.password
                ,hri.information
                ,hri.information_bumon
                ,hri.information_tel
                ,hri.teikei_kbn
                ,hri.teikei_cd
                ,ski.k_last_nm AS huho_k_last_nm
                ,ski.k_first_nm AS huho_k_first_nm
                ,ski.m_last_nm AS huho_m_last_nm
                ,ski.m_first_nm AS huho_m_first_nm
                ,ski.m_zoku_kbn AS huho_m_zoku_kbn
                ,ski.syushi_cd AS huho_syushi_cd
                ,ski.k_nenrei_man AS huho_k_nenrei
                ,ski.k_sex_kbn AS huho_k_sex_kbn
                ,ski.v_free9 AS m_mail
            FROM huho_renraku_info hri
            LEFT JOIN seko_kihon_info ski
                ON ski.seko_no = hri.seko_no
            WHERE hri.seko_no = :seko_no
                AND hri.delete_flg = 0
        ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }
    /**
     * 結果情報取得処理
     *
     * <AUTHOR> Sugiyama
     * @since  2022/08/xx
     * @param Msi_Sys_Db $db db
     */
    protected function getResultInfo($db) {
        $sql = "
            SELECT 
                 skf.free_kbn4          AS uketsukekeka_cd
                ,cnm9611.kbn_value_cd   AS uketsukekeka_kbn
                ,skf.free9_code_cd      AS saidanshubetsu_code_cd
                ,skf.free9_kbn          AS saidanshubetsu_cd
                ,cnm9615.kbn_value_cd   AS saidanshubetsu_kbn
                ,cnm9615.kbn_value_lnm  AS saidanshubetsu_nm
                ,skf.n_free3            AS saidan_kin
                ,skf.ts_free2           AS shupatsu_ymd
                ,TO_CHAR(skf.ts_free2 ,'YYYY/MM/DD') AS shupatsu_date
                ,TO_CHAR(skf.ts_free2 ,'HH24:MI')    AS shupatsu_time
                ,skf.ts_free3           AS kisha_ymd
                ,TO_CHAR(skf.ts_free3 ,'YYYY/MM/DD') AS kisha_date
                ,TO_CHAR(skf.ts_free3 ,'HH24:MI')    AS kisha_time
                ,skf.n_free4            AS hanso_kyori
                ,skf.v_free15           AS shupatsuchi
                ,skf.v_free16           AS mokutekichi
                ,skf.v_free17           AS keiyuchi
                ,skf.free_kbn8          AS om_status_cd
                ,cnm9614.kbn_value_cd   AS om_status_kbn
                ,TRIM(skf.free1_cd)     AS om_kokyaku_cd
                ,kokyaku.kokyaku_nm1    AS om_kokyaku_nm1
                ,kokyaku.kokyaku_nm2    AS om_kokyaku_nm2
                ,kokyaku.kokyaku_kana1  AS om_kokyaku_knm1
                ,kokyaku.kokyaku_kana2  AS om_kokyaku_knm2
                ,TO_CHAR(skf.d_free2 ,'YYYY/MM/DD') AS om_shiharai_ymd
                ,skf.v_free19           AS om_shubetsu_cd
                ,skf.v_free21           AS om_ginko_nm
                ,skf.v_free22           AS om_ginkoshiten_nm
                ,skf.free_kbn1          AS om_kozashubetsu_cd
                ,skf.v_free20           AS om_koza_no
                ,skf.v_free23           AS om_koza_nm
                ,skf.n_free5            AS om_kin
                ,skf.v_free18           AS om_biko
            FROM 
                seko_kihon_all_free skf
            LEFT JOIN kokyaku_mst kokyaku
            ON  kokyaku.kokyaku_no = TRIM(skf.free1_cd)
            AND kokyaku.delete_flg = 0
            LEFT JOIN code_nm_mst cnm9611
            ON  cnm9611.code_kbn         = '9611'
            AND cnm9611.kbn_value_cd_num = skf.free_kbn4
            AND cnm9611.delete_flg       = 0
            LEFT JOIN code_nm_mst cnm9614
            ON  cnm9614.code_kbn         = '9614'
            AND cnm9614.kbn_value_cd_num = skf.free_kbn8
            AND cnm9614.delete_flg       = 0
            LEFT JOIN code_nm_mst cnm9615
            ON  cnm9615.code_kbn         = '9615'
            AND cnm9615.kbn_value_cd_num = skf.free9_kbn
            AND cnm9615.delete_flg       = 0
            WHERE 
                skf.seko_no    = :seko_no
            AND skf.delete_flg = 0
        ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }
    /**
     * 施行者カード情報取得処理
     *
     * <AUTHOR> sai.tk
     * @since  2025/04/xx
     * @param Msi_Sys_Db $db db
     */
    protected function getSekoshaInfo($db) {
        $sql = "
            SELECT
                TO_CHAR(nd.nyukin_ymd ,'YYYY/MM/DD')  AS seisan_date
                , skf.free3_kbn  AS kigyokeiyaku_cd
                , skf.free4_kbn  AS saidanumu_cd
                , skf.free5_kbn  AS csumu_cd
                , skf.bumon_cd1  AS csbusho_cd
                , skf.tanto_nm28  AS cs_tanto_nm
                , skf.free6_kbn  AS atokazari_cd
                , skf.v_free30  AS atokazari_addr
                , skf.free7_kbn  AS tekohai_cd
                , skf.free8_kbn  AS renrakukahi_cd
                , skf.v_free31  AS riyu_shosai
                , skf.v_free37  AS customer_nm
                , skf.v_free32  AS homonsaki_addr
                , skf.v_free38  AS customer_tel
                , skf.free_kbn1  AS zokugara_cd
                , skf.v_free33  AS mikomi_shosai
                , skf.free9_kbn  AS hojiriyo_cd
                , skf.free10_kbn  AS shobonriyo_cd
                , skf.v_free34  AS sekobu_biko
                , TO_CHAR(skf.ts_free4 ,'YYYY/MM/DD')  AS eigyouketuke_date
                , skf.bumon_cd2  AS eigyobusho_cd
                , skf.tanto_cd29  AS eigyotanto_cd
                , skf.free11_kbn  AS hantei1_cd
                , skf.free12_kbn  AS hantei2_cd
                , skf.free13_kbn  AS uchiwake1_cd
                , skf.free14_kbn  AS uchiwake2_cd
                , skf.free15_kbn  AS zanguchi_cd
                , skf.n_free1  AS sedai_24man_kuchi
                , skf.n_free2  AS sedai_12man_kuchi
                , skf.n_free3  AS sedai_kensu
                , skf.n_free5  AS sedai_kuchisu
                , skf.n_free7  AS sonota_24man_kuchi
                , skf.n_free8  AS sonota_12man_kuchi
                , skf.n_free9  AS sonota_kensu
                , skf.n_free10  AS sonota_kuchisu
                , COALESCE(skf.n_free1, 0) + COALESCE(skf.n_free7, 0)  AS sum_24man_kuchi
                , COALESCE(skf.n_free2, 0) + COALESCE(skf.n_free8, 0)  AS sum_12man_kuchi
                , COALESCE(skf.n_free3, 0) + COALESCE(skf.n_free9, 0)  AS sum_kensu
                , COALESCE(skf.n_free5, 0) + COALESCE(skf.n_free10, 0)  AS sum_kuchisu
                , skf.v_free35  AS mikanyu_riyu
                , skf.v_free36  AS eigyo_biko
                , skf.free1_kbn  AS eigyo_status_cd
            FROM 
                seko_kihon_all_free skf
            LEFT JOIN 
                (SELECT seko_no, MAX(nyukin_ymd) AS nyukin_ymd FROM 
                    nyukin_denpyo WHERE data_kbn = 1 AND delete_flg = 0
                    GROUP BY seko_no
                ) nd
                ON nd.seko_no = skf.seko_no
            WHERE 
                skf.seko_no    = :seko_no
            AND skf.delete_flg = 0
        ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }
    /**
     * 訃報連絡情報保存処理
     *
     * <AUTHOR> Tosaka
     * @since  2021/xx/xx
     * @param Msi_Sys_Db $db db
     * @param array $dataHuhoInfo 訃報連絡情報データ
     * @return int 更新件数
     */
    protected function saveHuhoInfo($db, $dataHuhoInfo) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataHuhoInfo, 'greeting, name_setting_before, name_setting_after, funeral_style, tuya_nm, nitei_other_nm1
                , youtube_live_flag, telegram_use_flag, flower_use_flag, offerings_use_flag, funeral_gift_use_flag
                , guide_title, chief_mourner_title, title1, name1, title2, name2, remarks, sougi_nm, nitei_other_nm2
                , huho_tuya_basho_nm, huho_tuya_zip, huho_tuya_addr, huho_tuya_tel, huho_sougi_basho_nm, huho_sougi_zip, huho_sougi_addr, huho_sougi_tel
                , huho_tuya_ymd, nitei_other_ymd1, huho_sougi_ymd, nitei_other_ymd2, funeral_site_limit, chief_mourner_login_limit, login_id, password
                , information information_bumon information_tel teikei_kbn teikei_cd'
                        , array(
                    'tuya_nm' => 'schedule_name1_1', 'nitei_other_nm1' => 'schedule_name1_2', 'sougi_nm' => 'schedule_name2_1', 'nitei_other_nm2' => 'schedule_name2_2'
                    , 'huho_tuya_basho_nm' => 'hall_name1', 'huho_tuya_zip' => 'hall_postal_code1', 'huho_tuya_addr' => 'hall_address1', 'huho_tuya_tel' => 'hall_phone_number1'
                    , 'huho_sougi_basho_nm' => 'hall_name2', 'huho_sougi_zip' => 'hall_postal_code2', 'huho_sougi_addr' => 'hall_address2', 'huho_sougi_tel' => 'hall_phone_number2'
                    , 'huho_tuya_ymd' => 'schedule_time1_1', 'nitei_other_ymd1' => 'schedule_time1_2', 'huho_sougi_ymd' => 'schedule_time2_1', 'nitei_other_ymd2' => 'schedule_time2_2'
                        )
        );
        $oneRow = Msi_Sys_Utils::emptyToNullArr($oneRow);
        $except = array();
        // 存在チェック
        $select = $this->getHuhoInfo();
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("huho_renraku_info", $oneRow);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("huho_renraku_info", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }
    /**
     * 結果情報保存処理
     *
     * <AUTHOR> Sugiyama
     * @since  2022/08/xx
     * @param Msi_Sys_Db $db db
     * @param array $dataResultInfo 結果情報データ
     * @return int 更新件数
     */
    public function saveResultInfo($db, $dataResultInfo) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat(
                $dataResultInfo, 
                'uketsukekeka_cd saidanshubetsu_code_cd saidanshubetsu_cd saidan_kin shupatsu_ymd kisha_ymd hanso_kyori shupatsuchi mokutekichi keiyuchi '.
                'om_status_cd om_kokyaku_cd om_shiharai_ymd om_shubetsu_cd om_ginko_nm om_ginkoshiten_nm om_kozashubetsu_cd om_koza_no om_koza_nm om_kin om_biko'
                , array(
                    'uketsukekeka_cd'   => 'free_kbn4',
                    'saidanshubetsu_code_cd' => 'free9_code_cd',
                    'saidanshubetsu_cd' => 'free9_kbn',
                    'saidan_kin'        => 'n_free3',
                    'shupatsu_ymd'      => 'ts_free2',
                    'kisha_ymd'         => 'ts_free3',
                    'hanso_kyori'       => 'n_free4',
                    'shupatsuchi'       => 'v_free15',
                    'mokutekichi'       => 'v_free16',
                    'keiyuchi'          => 'v_free17',
                    'om_status_cd'      => 'free_kbn8',
                    'om_kokyaku_cd'     => 'free1_cd',
                    'om_shiharai_ymd'   => 'd_free2',
                    'om_shubetsu_cd'    => 'v_free19',
                    'om_ginko_nm'       => 'v_free21',
                    'om_ginkoshiten_nm' => 'v_free22',
                    'om_kozashubetsu_cd'=> 'free_kbn1',
                    'om_koza_no'        => 'v_free20',
                    'om_koza_nm'        => 'v_free23',
                    'om_kin'            => 'n_free5',
                    'om_biko'           => 'v_free18',
                )
        );
        $oneRow = Msi_Sys_Utils::emptyToNullArr($oneRow);
        $except = array();
        // 存在チェック
        $select = $this->getResultInfo($db);
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kihon_all_free", $oneRow);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_all_free", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }
    /**
     * 施行者カード情報保存処理
     *
     * <AUTHOR> Sai.tk
     * @since  2025/04/xx
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoshaInfo 施行者カード情報データ
     * @return int 更新件数
     */
    public function saveSekoshaInfo($db, $dataSekoshaInfo) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat(
                $dataSekoshaInfo, 
                'kigyokeiyaku_cd saidanumu_cd csumu_cd csbusho_cd cs_tanto_nm atokazari_cd atokazari_addr tekohai_cd renrakukahi_cd riyu_shosai '.
                'customer_nm homonsaki_addr customer_tel zokugara_cd mikomi_shosai hojiriyo_cd shobonriyo_cd sekobu_biko eigyouketuke_date '.
                'eigyobusho_cd eigyotanto_cd hantei1_cd hantei2_cd uchiwake1_cd uchiwake2_cd zanguchi_cd sedai_24man_kuchi sedai_12man_kuchi '.
                'sedai_kensu sedai_kuchisu sonota_24man_kuchi sonota_12man_kuchi sonota_kensu sonota_kuchisu mikanyu_riyu eigyo_biko eigyo_status_cd'
                , array(
                    'kigyokeiyaku_cd' => 'free3_kbn'
                    , 'saidanumu_cd' => 'free4_kbn'
                    , 'csumu_cd' => 'free5_kbn'
                    , 'csbusho_cd' => 'bumon_cd1'
                    , 'cs_tanto_nm' => 'tanto_nm28'
                    , 'atokazari_cd' => 'free6_kbn'
                    , 'atokazari_addr' => 'v_free30'
                    , 'tekohai_cd' => 'free7_kbn'
                    , 'renrakukahi_cd' => 'free8_kbn' //　使用されている可能性あり
                    , 'riyu_shosai' => 'v_free31'
                    , 'customer_nm' => 'v_free37'
                    , 'homonsaki_addr' => 'v_free32'
                    , 'customer_tel' => 'v_free38'
                    , 'zokugara_cd' => 'free_kbn1' //　使用されている可能性あり
                    , 'mikomi_shosai' => 'v_free33'
                    , 'hojiriyo_cd' => 'free9_kbn' //　使用されている可能性あり
                    , 'shobonriyo_cd' => 'free10_kbn'
                    , 'sekobu_biko' => 'v_free34'
                    , 'eigyouketuke_date' => 'ts_free4'
                    , 'eigyobusho_cd' => 'bumon_cd2'
                    , 'eigyotanto_cd' => 'tanto_cd29'
                    , 'hantei1_cd' => 'free11_kbn'
                    , 'hantei2_cd' => 'free12_kbn'
                    , 'uchiwake1_cd' => 'free13_kbn'
                    , 'uchiwake2_cd' => 'free14_kbn'
                    , 'zanguchi_cd' => 'free15_kbn'
                    , 'sedai_24man_kuchi' => 'n_free1'
                    , 'sedai_12man_kuchi' => 'n_free2'
                    , 'sedai_kensu' => 'n_free3' //　使用されている可能性あり
                    , 'sedai_kuchisu' => 'n_free5' //　使用されている可能性あり
                    , 'sonota_24man_kuchi' => 'n_free7'
                    , 'sonota_12man_kuchi' => 'n_free8'
                    , 'sonota_kensu' => 'n_free9'
                    , 'sonota_kuchisu' => 'n_free10'
                    , 'mikanyu_riyu' => 'v_free35'
                    , 'eigyo_biko' => 'v_free36'
                    , 'eigyo_status_cd' => 'free1_kbn'
                )
        );
        $oneRow = Msi_Sys_Utils::emptyToNullArr($oneRow);
        $except = array();
        // 存在チェック
        $select = $this->getSekoshaInfo($db);
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kihon_all_free", $oneRow);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_all_free", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }
    /**
     * Hikari連携処理
     *
     * <AUTHOR> Tosaka
     * @since 2021/xx/xx
     * @return 
     */
    public function huhorenkei($db, $seko_no) {
        // 必須チェック処理
        $msg = Logic_HakuApi_LogicHikariApiUpdate::checkData($db, $seko_no);
        if (strlen($msg) > 0) {
            return $data = array('status' => 'NG', 'msg' => $msg . "は必須項目です。");
        }
        $renkeiDate = Msi_Sys_Utils::getDate(null, 'Y/m/d H:i');
        // 連携商品保存処理
        Logic_HakuApi_LogicHikariApiUpdate::saveRenkeiShohin($db, $seko_no, $renkeiDate);
        $apiData = DataMapper_HakuApi_HuhoRenkei::getApiUpdateData($db, $seko_no);
        // 必須チェック
        $msg = DataMapper_HakuApi_HuhoRenkei::checckApiUpdateData($apiData);
        if (strlen($msg) > 0) {
            return $data = array('status' => 'NG', 'msg' => $msg . "は必須項目です。");
        }
        // 既に訃報IDが採番されいたら更新なければ新規
        if (isset($apiData['id']) && strlen($apiData['id']) > 0) {
            list($errMsg, $aResult, $resData) = Logic_HakuApi_LogicHikariApiUpdate::update($apiData);
            $firstFlg = false;
        } else {
            list($errMsg, $aResult, $resData) = Logic_HakuApi_LogicHikariApiUpdate::create($apiData);
            $firstFlg = true;
        }
        // 配列の場合がある
        if (is_array($errMsg)) {
            $errMsg = implode($errMsg);
        }
        if (strlen($errMsg) > 0) {
            return $data = array('status' => 'NG', 'msg' => 'HiKARI連携：' . $errMsg);
        }
        // 施行基本保存処理
        Logic_HakuApi_LogicHikariApiUpdate::saveHuho($db, $seko_no, $aResult, $apiData, $firstFlg);
        $db->commit();
        $huhoData = Logic_HakuApi_LogicHikariApiUpdate::getHuho($db, $seko_no);
        $data['status'] = 'OK';
        $data['msg'] = '更新しました。HiKARI連携をしました。';
        // 訃報情報を再取得
        $data['huhoData'] = $huhoData;
        return $data;
    }

    /**
     *
     * 定型文データをマスタより取得する
     *
     * <AUTHOR> Sai
     * @since 2014/05/27
     * @param string $teikeiKbn 定型文区分
     * @param boolean $convert $記号の置換するか
     * @return array 定型文データ 
     */
    protected function getTeikeiDataForCdNm() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            oi.teikei_kbn -- 定型文区分
            ,oi.oshirase_cd AS id -- お知らせ状文言コード
            ,oi.title_nm AS text -- タイトル名
        FROM
            oshirase_orei_mst oi
        WHERE
            CURRENT_DATE BETWEEN oi.tekiyo_st_date AND oi.tekiyo_ed_date
        AND oi.teikei_kbn = :teikei_kbn
        AND oi.delete_flg = 0
        ORDER BY
            oi.disp_no
                ";
        $select = $db->easySelect($sql, array('teikei_kbn' => self::TEIKEI_KBN_HUHO));
        return $select;
    }
    
    protected function getBrKozaKanriMst($db){
        $sql = "
        SELECT 
             koza.transfer_bank_cd
            ,koza.bank_nm || ' ' || koza.shiten_nm || ' ' ||
                (CASE WHEN koza.yokin_sbt=0 THEN '普' WHEN koza.yokin_sbt=1 THEN '当座' ELSE null END) || koza.st_br_koza_no AS transfer_bank_info
        FROM br_koza_kanri_mst koza
        ";
        $select = $db->easySelect($sql);
        return $select;
    }
    
    /**
     *
     * OIDデータを取得をする
     *
     * <AUTHOR> Sai
     * @param string $oid 
     * @since 2021/11/xx
     * @return array $oidデータ
     */
    protected function getOidData($oid) {
        $db = Msi_Sys_DbManager::getMyDb();
        $select = $db->easySelect('select * from pg_largeobject where loid = :oid', array('oid' => $oid));
        return $select;
    }
    
    /**
     *
     * mod_cnt取得処理
     *
     * <AUTHOR> Tosaka
     * @param  
     * @since 2022/11/xx
     * @return array $array
     */
    protected function getModCnts() {
        $db = Msi_Sys_DbManager::getMyDb();
        $array = array();
        if (!isset($this->_sekoNo)) {
            return $array;
        }
        $sql = "
            SELECT ski._mod_cnt AS kihon_mod_cnt
		,skaf._mod_cnt AS kihon_free_mod_cnt
		,jssi._mod_cnt AS sekyu_mod_cnt
		,sn._mod_cnt AS nitei_mod_cnt
            FROM seko_kihon_info ski
            LEFT JOIN seko_kihon_all_free skaf
		ON skaf.seko_no = ski.seko_no
		AND skaf.delete_flg = 0
            LEFT JOIN juchu_sekyu_saki_info jssi
		ON jssi.data_kbn = :data_kbn
		AND jssi.seko_no = ski.seko_no
		AND jssi.delete_flg = 0
            LEFT JOIN seko_nitei sn
		ON sn.seko_no = ski.seko_no
		AND sn.delete_flg = 0
            WHERE ski.seko_no = :seko_no
            LIMIT 1
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDatakbn()));
        if (count($select) > 0) {
            $array = $select[0];
        }
        return $array;
    }
    
    /**
     *
     * 受注伝票設定処理
     *
     * <AUTHOR> Tosaka
     * @param  
     * @since 2022/11/xx
     * @return array $array
     */
    protected function updateJuchuDenpyo($db,$dataSekoKihon) {
        
        $denpyoNo = $this->getJuchudenpyoNo();
        // 事前相談受注伝票番号を取得する
        $sql = "
        SELECT denpyo_no
        FROM juchu_denpyo
        WHERE seko_no = :seko_no
        AND delete_flg	= 0
	AND data_kbn = 1
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $dataSekoKihon['consult_seko_no']));
        $jizenDenpyoNo = $select['denpyo_no'];
        $jizenHistoryNo = $dataSekoKihon['jizen_history_no'];
        // 現在の伝票を履歴に作成
        // 受注伝票履歴登録SQL
        $sql1 = "
            SELECT *,now() AS history_dt
            FROM juchu_denpyo
            WHERE denpyo_no = :denpyo_no
                AND delete_flg = 0
                ";
        // 受注伝票明細履歴登録SQL
        $sql2 = "
            SELECT *
            FROM juchu_denpyo_msi
            WHERE denpyo_no = :denpyo_no
                AND delete_flg = 0
                ";
        $select1 = $db->easySelect($sql1, array('denpyo_no' => $denpyoNo));
        $select2 = $db->easySelect($sql2, array('denpyo_no' => $denpyoNo));
        $except = array("_req_id", "_cre_user", "_cre_ts", "_mod_user", "_mod_ts", "_mod_cnt", "convert_kbn");
        $history_no = DataMapper_Pdf0113::getDenpyoRirekiNo($db, $denpyoNo);
        $history_no += 1;
        require_once 'Pdf0113Controller.bellmony.php';
        $params = array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn(), 'moushi_kbn' => $this->_moushiKbn, 'printKbn' => 1, 'preview' => 'save');
        $buf = Juchu_Pdf0113Controller::indexAction($params);
        $temp_file = Msi_Sys_Utils::tempnam();
        Msi_Sys_Utils::put_contents($temp_file, $buf);
        $oid = $db->writeBlob($temp_file);
        foreach ($select1 as $data) {
            // 受注伝票履歴登録SQL
            $data['history_no'] = $history_no;
            $data['f_free1'] = $oid;
            $data['f_name1'] = $this->_sekoNo.'-'.$history_no.$this->_selectSekoKihon['souke_nm'].'家見積書';
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo_history', $data, $except);
            $cnt += $db->easyExecute($sql, $param);
        }
        foreach ($select2 as $data) {
            // 受注伝票明細履歴登録SQL
            $data['history_no'] = $history_no;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo_msi_history', $data, $except);
            $cnt += $db->easyExecute($sql, $param);
        }
        // 履歴から現在の伝票にコピー
        // 受注伝票削除SQL
        $sql = "
            DELETE FROM juchu_denpyo
            WHERE denpyo_no = :denpyo_no 
                ";
        $cnt += $db->easyExecute($sql, array('denpyo_no' => $denpyoNo));
        // 受注伝票明細削除SQL
        $sql = "
            DELETE FROM juchu_denpyo_msi
            WHERE denpyo_no = :denpyo_no 
                ";
        $cnt += $db->easyExecute($sql, array('denpyo_no' => $denpyoNo));
        // 施行発注情報削除SQL
        $sql = "
            DELETE FROM seko_hachu_info
            WHERE seko_no = :seko_no 
                AND data_kbn = :data_kbn 
                ";
        $cnt += $db->easyExecute($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn()));
        // history_noが0の場合はjuchu_denpyoテーブルから取得する
        if ($jizenHistoryNo == '0') {
            $denpyoTable = 'juchu_denpyo';
            $msiTable = 'juchu_denpyo_msi';
            $denpyoWhere = '';
            $msiWhere = '';
        } else {
            $denpyoTable = 'juchu_denpyo_history';
            $msiTable = 'juchu_denpyo_msi_history';
            $denpyoWhere = 'AND history_no = '.$jizenHistoryNo;
            $msiWhere = 'AND m.history_no = '.$jizenHistoryNo;
        }
        // 受注伝票登録SQL
        $sql1 = "
            SELECT *
            FROM $denpyoTable
            WHERE denpyo_no = :denpyo_no
                AND delete_flg = 0
                $denpyoWhere
                ";
        // 受注伝票明歴登録SQL
        $sql2 = "
            SELECT m.*,sbm.hachu_kbn
            FROM $msiTable m
            LEFT JOIN shohin_bunrui_mst sbm
                ON sbm.shohin_cd = m.shohin_cd
                AND sbm.bumon_cd = m.shohin_bumon_cd
                AND sbm.dai_bunrui_cd = m.dai_bunrui_cd
                AND sbm.chu_bunrui_cd = m.chu_bunrui_cd
                AND sbm.shohin_kbn = m.shohin_kbn
                AND sbm.delete_flg = 0
            WHERE m.denpyo_no = :denpyo_no
                AND m.delete_flg = 0
                $msiWhere
            ORDER BY m.msi_no
                ";
        $select1 = $db->easySelect($sql1, array('denpyo_no' => $jizenDenpyoNo));
        $select2 = $db->easySelect($sql2, array('denpyo_no' => $jizenDenpyoNo));
        $except = array("history_no", "history_dt", "_req_id", "_cre_user", "_cre_ts", "_mod_user", "_mod_ts", "_mod_cnt", "convert_kbn", "f_free1", "f_name1");
        $except2 = array("hachu_kbn","history_no", "history_dt", "_req_id", "_cre_user", "_cre_ts", "_mod_user", "_mod_ts", "_mod_cnt", "convert_kbn", "f_free1", "f_name1");
        $history_no = DataMapper_Pdf0113::getDenpyoRirekiNo($db, $denpyoNo);
        $history_no += 1;
        foreach ($select1 as $data) {
            // 受注伝票登録SQL
            $data['denpyo_no'] = $denpyoNo;
            $data['seko_no'] = $this->_sekoNo;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo', $data, $except);
            $cnt += $db->easyExecute($sql, $param);
            // 施行基本情報更新
            $where = array('seko_no' => $this->_sekoNo);
            $kihon = array();
            $selectKihon = DataMapper_SekoKihonInfo::find($db, array('seko_no' => $this->_sekoNo));
            if (count($selectKihon) > 0) {
                $kaiinKbn = DataMapper_CodeNmMst::find($db, array('code_kbn' => $selectKihon[0]['kaiin_code_kbn'], 'kbn_value_cd' => $data['v_free3']));
                $syushiKbn = DataMapper_CodeNmMst::find($db, array('code_kbn' => $selectKihon[0]['syushi_code_kbn'], 'kbn_value_cd_num' => $data['syushi_kbn']));
            }
            if (count($kaiinKbn) > 0 && isset($kaiinKbn[0])) {
                $kihon['kaiin_kbn'] = $kaiinKbn[0]['kbn_value_cd_num'];      // 会員コード
            }
            if (count($syushiKbn) > 0 && isset($syushiKbn[0])) {
                $kihon['syushi_cd'] = $syushiKbn[0]['kbn_value_cd'];    // 宗旨コード
            }
            $kihon['kaiin_cd'] = $data['v_free3'];                 // 会員区分
            $kihon['main_pt_cd'] = $data['main_pt_cd'];               // 基本パターン区分
            $kihon['main_pt_kbn'] = $data['main_pt_kbn'];               // 基本パターン区分
            $kihon['saidan_sbt'] = $data['saidan_sbt'];                 // 祭壇種別
            $kihon['plan_syushi_kbn'] = $data['syushi_kbn'];                // 宗旨区分
            $kihon['gojokai_kbn'] = $data['gojokai_kbn'];               // 互助会区分
            $kihon['seko_plan_cd'] = $data['seko_plan_cd'];                   // 施行プランコード
            $kihon['plan_shikijo_cd'] = $data['plan_shikijo_cd'];                   // プラン用式場コード
            $kihon['est_shikijo_cd'] = $data['est_shikijo_cd'];                   // 見積式場コード
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihon, $where);
            $cnt += $db->easyExecute($sql, $param);
            $dataApp['denpyo_no'] = $data['denpyo_no'];
        }
        foreach ($select2 as $data) {
            // 受注伝票明細登録SQL
            $data['denpyo_no'] = $denpyoNo;
            $data['seko_no'] = $this->_sekoNo;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo_msi', $data, $except2);
            $cnt += $db->easyExecute($sql, $param);
        }
        // 施行発注管理情報を保存する
        $dataDelCol = array();
        $cnt += $this->saveHachuInfo($db, $dataApp, $select2, $dataDelCol);
        return;
    }

}
