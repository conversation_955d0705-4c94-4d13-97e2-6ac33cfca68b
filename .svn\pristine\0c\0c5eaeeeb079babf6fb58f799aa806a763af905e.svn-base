<?php

/**
 * DataMapper_GojokaiCalc
 *
 * 互助会金額計算 データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Sato
 * @since      2014/05/16
 * @filesource 
 */

/**
 * 互助会金額計算 データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Tosaka
 * @since      2020/03/25
 */
class DataMapper_GojokaiCalc extends DataMapper_Abstract {

    // 用途区分：コース施行
    const YOTO_COURSE = '1';
    // 用途区分：プラン施行
    const YOTO_PLAN = '2';
    // 用途区分：金額充当
    const YOTO_JUTO = '3';
    // 用途区分：使用しない
    const YOTO_NO_USE = '4';
    // 用途区分：解約指図払い
    const YOTO_KAIYAKU = '5';
    // 申込区分：オーダーメイド
    const MOUSHI_KBN_ORDERMADE = '19';
    
    /**
     * 互助会関連の金額を取得
     *
     * <AUTHOR> Sato
     * @since      2014/05/16
     * @param      Msi_Sys_Db $db
     * @param      type $seko_no 施行番号
     * @return     array      互助会関連の金額
     */
    public static function getGojokaiKingaku($db, $seko_no) {
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT
             goj.yoto_kbn
            ,goj.keiyaku_gaku                                                   -- 契約金額
            ,goj.harai_gaku                                                     -- 入金金額
            ,goj.wari_gaku                                                      -- 割引額
            ,goj.wari_gaku_tax                                                  -- 割引額消費税
            ,goj.early_use_cost                                                 -- 早期利用費
            ,goj.early_use_cost_zei                                             -- 早期利用費消費税
            ,goj.waribiki_gaku
            ,COALESCE(goj.warimashi_gaku, 0)            AS warimashi_gaku       -- 割増金額
            ,COALESCE(goj.n_free3, 0)                   AS premium_gaku
            ,COALESCE(goj.n_free4, 0)                   AS kannnou_gaku
            ,goj.kanyu_tax
            ,COALESCE(goj.zei_kijyn_ymd, goj.kanyu_dt)  AS zei_kijyn_ymd
            ,TO_CHAR(COALESCE(kih.sougi_ymd, now()), 'YYYY-MM-DD') AS cur_ymd
            ,COALESCE(goj.meigi_chg_cost, 0)            AS meigi_chg_cost
            ,COALESCE(goj.meigi_chg_cost_zei, 0)        AS meigi_chg_cost_zei
            ,COALESCE(zei.zei_cd,0)                     AS zei_cd  
            ,COALESCE(zei.zei_rtu,0)                    AS zei_rtu
            ,COALESCE(zei2.zei_cd,0)                    AS sougi_zei_cd  
            ,COALESCE(zei2.zei_rtu,0)                   AS sougi_zei_rtu
            ,COALESCE(goj.riyoken,0)                    AS riyoken
            ,COALESCE(goj.point,0)                      AS point
            ,kih.moushi_kbn
        FROM seko_gojokai_member goj
        INNER JOIN seko_kihon_info kih 
            ON goj.seko_no = kih.seko_no 
            AND kih.delete_flg = 0
        LEFT JOIN zei_gojo_mst zei 
            ON goj.zei_cd = zei.zei_cd
            AND zei.delete_flg = 0
        LEFT JOIN zei_mst zei2 
            ON TO_CHAR(COALESCE(kih.sougi_ymd, now()), 'YYYY-MM-DD') BETWEEN TO_CHAR(zei2.tekiyo_st_date,'yyyy/mm/dd') AND TO_CHAR(zei2.tekiyo_ed_date,'yyyy/mm/dd') 
            AND zei2.reduced_tax_rate = 1
            AND zei2.delete_flg = 0
        WHERE goj.seko_no = :seko_no 
            AND goj.delete_flg = 0
END_OF_SQL
                , array('seko_no' => $seko_no));

        if (count($select) == 0) {
            return NULL;
        }

        $ret_ary['keiyaku_gaku_sou'] = 0;
        $ret_ary['harai_gaku_sou'] = 0;
        $ret_ary['wari_gaku'] = 0;
        $ret_ary['wari_sum'] = 0;
        $ret_ary['wari_sum_tax'] = 0;
        $ret_ary['keiyaku_gaku_hen'] = 0;
        $ret_ary['keiyaku_gaku_dan'] = 0;
        $ret_ary['mg_chg_cost'] = 0; // 名義変更手数料
        $ret_ary['mg_chg_cost_zei'] = 0; // 名義変更手数料
        $ret_ary['early_use_cost'] = 0;
        $ret_ary['early_use_cost_zei'] = 0;
        $ret_ary['zan_gaku'] = 0;
        $ret_ary['zan_gaku_zei'] = 0;
        $ret_ary['keiyaku_gaku_zei'] = 0;
        $ret_ary['gojokai_juto'] = 0;
        $ret_ary['kannou_gaku'] = 0;
        $ret_ary['warimashi_gaku'] = 0;
        $ret_ary['riyoken'] = 0;
        $ret_ary['point'] = 0;
        
        foreach ($select as $row) {
            // オーダーメイドの場合はスルーする
            if ($row['moushi_kbn'] === static::MOUSHI_KBN_ORDERMADE) {
                continue;
            }
            switch ($row['yoto_kbn']) {
                case 1: // コース施行
                case 2: // プラン施行	
                    $ret_ary['keiyaku_gaku_sou'] += $row['keiyaku_gaku'];
                    $ret_ary['harai_gaku_sou'] += $row['harai_gaku'];
                    $ret_ary['wari_gaku'] += $row['waribiki_gaku'];
                    $ret_ary['wari_sum'] += $row['wari_gaku'];
                    $ret_ary['wari_sum_tax'] += $row['wari_gaku_tax'];
                    $ret_ary['early_use_cost'] += $row['early_use_cost'];
                    $ret_ary['early_use_cost_zei'] += $row['early_use_cost_zei'];
                    $ret_ary['mg_chg_cost'] += $row['meigi_chg_cost'];
                    $ret_ary['mg_chg_cost_zei'] += $row['meigi_chg_cost_zei'];
                    $ret_ary['keiyaku_gaku_zei'] += $row['kanyu_tax'];
                    $ret_ary['kannou_gaku'] += $row['kannnou_gaku'];
                    $ret_ary['riyoken'] += $row['riyoken'];
                    $ret_ary['point'] += $row['point'];
                    $ret_ary['warimashi_gaku'] += $row['warimashi_gaku'];
                    break;
                case 3: // 金額充当
                case 5: // 解約指図払い
                    $ret_ary['gojokai_juto'] += $row['harai_gaku'] + $row['wari_gaku'];
                    $ret_ary['mg_chg_cost'] += $row['meigi_chg_cost'];
                    $ret_ary['mg_chg_cost_zei'] += $row['meigi_chg_cost_zei'];
                    $ret_ary['riyoken'] += $row['riyoken'];
                    $ret_ary['point'] += $row['point'];
                    break;
            }

            if ($row['yoto_kbn'] == self::YOTO_COURSE || $row['yoto_kbn'] == self::YOTO_PLAN) { // コース施行とプラン施行のみ計算する
                $zan_gaku = $row['keiyaku_gaku']-$row['harai_gaku']-$row['wari_gaku']-$row['waribiki_gaku']-$row['warimashi_gaku'];
                $ret_ary['zan_gaku'] += $zan_gaku;
                $ret_ary['zan_gaku_zei'] = 0;
            }
        }
        return $ret_ary;
    }

    /**
     * 互助会情報を取得
     *
     * <AUTHOR> Sai
     * @since      2014/08/28
     * @param      Msi_Sys_Db $db
     * @param      type $seko_no 施行番号
     * @return     array 互助会情報
     */
    public static function getGojokaiInfo($db, $seko_no) {
        $select = $db->easySelOne("
        SELECT 
            COALESCE(plan_change_prc, 0) AS pcp 
        FROM seko_gojokai_info 
        WHERE seko_no = :seko_no 
            AND delete_flg = 0
        "
                , array('seko_no' => $seko_no));
        return $select;
    }
    /**
     * 互助会関連の金額を取得（消費税コード別）を取得 入金伝票作成用 
     *
     * <AUTHOR> Tosaka
     * @since   2020/03/25
     * @param   Msi_Sys_Db $db
     * @param   type $seko_no 施行番号
     * @param   string  $seko_no_sub 施行番号（枝番）
     * @return  array      互助会関連の金額
     */
    public static function getGojokaiKingaku_zei($db, $seko_no) {
        $select = $db->easySelect(<<< END_OF_SQL
	SELECT
            goj.seko_no AS seko_no                                  -- 施行番号
            ,TO_CHAR(COALESCE(ski.sougi_ymd, now()), 'YYYY/MM/DD') cur_ymd
            ,goj.yoto_kbn AS yoto_kbn                               -- 用途区分
            ,COALESCE(zei.zei_cd,0) AS zei_cd                       -- 消費税コード
            ,COALESCE(zei.zei_rtu,0) AS zei_rtu                     -- 消費税率
            ,COALESCE(zei2.zei_cd,0) AS zei_cd2                     -- 消費税コード
            ,COALESCE(zei2.zei_rtu,0) AS zei_rtu2                   -- 消費税率
            ,SUM(goj.keiyaku_gaku) AS keiyaku_gaku                  -- 契約金額
            ,SUM(goj.harai_gaku) AS harai_gaku                      -- 払込金額
            ,SUM(goj.wari_gaku) AS wari_gaku                        -- 前納割引額（割引額）
            ,SUM(goj.wari_gaku_tax) AS wari_gaku_tax                -- 割引額消費税
            ,SUM(goj.early_use_cost) AS early_use_cost              -- 早期利用費
            ,SUM(goj.early_use_cost_zei) AS early_use_cost_zei      -- 早期利用費消費税
            ,SUM(COALESCE(goj.meigi_chg_cost, 0)) AS meigi_chg_cost -- 名義変更手数料
            ,SUM(COALESCE(goj.meigi_chg_cost_zei, 0)) AS meigi_chg_cost_zei -- 名義変更手数料消費税
            ,SUM(COALESCE(goj.warimashi_gaku, 0)) AS warimashi_gaku -- 割増金額
            ,SUM(COALESCE(goj.n_free3, 0)) AS premium_gaku -- プレミアム割引額
            ,SUM(COALESCE(goj.n_free4, 0)) AS kannnou_gaku -- 完納後割増額
            ,SUM(COALESCE(goj.kanyu_tax, 0)) AS kanyu_tax -- 会費消費税
            ,SUM(COALESCE(goj.riyoken,0)) AS riyoken -- 利用券
            ,SUM(COALESCE(goj.point,0)) AS point -- ポイント
            ,SUM(COALESCE(goj.n_free3,0))   AS n_free3 -- 全金額一括支払割引額(税抜金額)
            ,SUM(COALESCE(goj.n_free4,0))   AS n_free4 -- 全金額一括支払割引額(消費税額)
            ,ski.moushi_kbn
        FROM seko_gojokai_member goj
        INNER JOIN seko_kihon_info ski
            ON goj.seko_no = ski.seko_no
            AND 0 = ski.delete_flg
        LEFT JOIN zei_gojo_mst zei 
            ON goj.zei_cd = zei.zei_cd
            AND zei.delete_flg = 0
        LEFT JOIN zei_mst zei2 
            ON TO_CHAR(COALESCE(ski.sougi_ymd, now()), 'YYYY/MM/DD') BETWEEN TO_CHAR(zei2.tekiyo_st_date,'yyyy/mm/dd') AND TO_CHAR(zei2.tekiyo_ed_date,'yyyy/mm/dd') 
            AND zei2.reduced_tax_rate = 1
            AND zei2.delete_flg = 0
	WHERE goj.delete_flg = 0         
            AND	goj.seko_no = :seko_no			
        GROUP BY goj.seko_no
            ,goj.yoto_kbn
            ,zei.zei_cd
            ,ski.sougi_ymd
            ,zei.zei_rtu
            ,goj.kanyu_dt
            ,zei2.zei_cd
            ,zei2.zei_rtu
            ,ski.moushi_kbn
	ORDER BY goj.seko_no
            ,goj.yoto_kbn
            ,zei.zei_cd			
END_OF_SQL
                , array(
            'seko_no' => $seko_no,
        ));
        $retgojokai['sougi_keiyaku_prc'] = 0;           // 葬儀契約金額
        $retgojokai['sougi_keiyaku_zei'] = 0;           // 葬儀契約金額消費税 
        $retgojokai['sougi_harai_prc'] = 0;             // 葬儀払込金額
        $retgojokai['sougi_wari_prc'] = 0;              // 葬儀前納割引額
        $retgojokai['sougi_wari_zei'] = 0;              // 葬儀前納割引額
        $retgojokai['sougi_premium_service_prc'] = 0;   // 葬儀割増サービス
        $retgojokai['sougi_meigi_chg_cost'] = 0;        // 葬儀名義変更手数料
        $retgojokai['sougi_meigi_chg_cost_zei'] = 0;    // 葬儀名義変更手数料消費税
        $retgojokai['sougi_early_use_cost'] = 0;        // 葬儀早期利用費
        $retgojokai['sougi_early_use_cost_zei'] = 0;    // 葬儀早期利用費消費税 
        $retgojokai['sougi_zei_sagaku_prc'] = 0;        // 葬儀掛金消費税差額
        $retgojokai['etc_keiyaku_prc'] = 0;             // 壇払等の契約金額	
        $retgojokai['etc_keiyaku_zei'] = 0;             // 壇払等の契約金額消費税	
        $retgojokai['etc_harai_prc'] = 0;               // 壇払等の払込金額	
        $retgojokai['etc_wari_prc'] = 0;                // 壇払等の前納割引額
        $retgojokai['etc_early_use_cost'] = 0;          // 壇払等の早期利用費
        $retgojokai['etc_early_use_cost_zei'] = 0;      // 壇払等早期利用費消費税 
        $retgojokai['etc_zei_sagaku_prc'] = 0;          // 壇払等の掛金消費税差額
        $retgojokai['etc_zei_cd'] = 0;                  // 壇払等の消費税コード
        $retgojokai['etc_meigi_chg_cost'] = 0;          // 壇払等の名義変更手数料
        $retgojokai['sougi_tokuten_prc'] = 0;           // 完納充当額
        $retgojokai['n_free6'] = 0;                     // 全金額一括支払割引額(税抜金額)
        $retgojokai['n_free7'] = 0;                     // 全金額一括支払割引額(消費税額)
        $retgojokai['n_free9'] = 0;                     // 利用券
        $retgojokai['n_free10'] = 0;                    // ポイント
        $retgojokai['n_free5'] = 0;                     // 完納充当未納額
        // 申込区分がオーダーメイドの場合はスルーする
        foreach ($select as $one) {
            if ($one['moushi_kbn'] === static::MOUSHI_KBN_ORDERMADE) {
                continue;
            }
            if ($one['yoto_kbn'] == self::YOTO_JUTO || $one['yoto_kbn'] == self::YOTO_KAIYAKU) {
                $retgojokai['sougi_tokuten_prc'] += $one['keiyaku_gaku'];               // 契約金額
                $retgojokai['etc_harai_prc'] += $one['harai_gaku'];                     // 壇払等の払込金額	
                $retgojokai['sougi_meigi_chg_cost'] += $one['meigi_chg_cost'];          // 葬儀名義変更手数料
                $retgojokai['sougi_meigi_chg_cost_zei'] += $one['meigi_chg_cost_zei'];  // 葬儀名義変更手数料消費税
                $retgojokai['n_free6'] += $one['n_free3'];                              // 全金額一括支払割引額(税抜金額)
                $retgojokai['n_free7'] += $one['n_free4'];                              // 全金額一括支払割引額(消費税額)
                $retgojokai['n_free9'] += $one['riyoken'];                              // 利用券
                $retgojokai['n_free10'] += $one['point'];                               // ポイント
                $retgojokai['n_free5'] += $one['keiyaku_gaku'] - ($one['harai_gaku'] + $one['wari_gaku']) ;// 完納充当未納額
            } else if ($one['yoto_kbn'] != self::YOTO_NO_USE) {   // 使用しないはなにも設定しない
                $retgojokai['sougi_keiyaku_prc'] += $one['keiyaku_gaku'];               // 葬儀契約金額
                $retgojokai['sougi_keiyaku_zei'] += $one['kanyu_tax'];                  // 葬儀契約金額消費税	
                $retgojokai['sougi_harai_prc'] += $one['harai_gaku'];                   // 葬儀払込金額	
                $retgojokai['sougi_wari_prc'] += $one['wari_gaku']; // 葬儀前納割引額(前納割引額+プレミアム割引+完納後割増サービス)
                $retgojokai['sougi_wari_zei'] += $one['wari_gaku_tax']; // 葬儀前納割引額(前納割引額+プレミアム割引+完納後割増サービス)
                $retgojokai['sougi_premium_service_prc'] += $one['warimashi_gaku'];     // 割増金額
                $retgojokai['sougi_early_use_cost'] += $one['early_use_cost'];          // 葬儀早期利用費
                $retgojokai['sougi_early_use_cost_zei'] += $one['early_use_cost_zei'];  // 葬儀早期利用費消費税 
                $retgojokai['sougi_meigi_chg_cost'] += $one['meigi_chg_cost'];          // 葬儀名義変更手数料
                $retgojokai['sougi_meigi_chg_cost_zei'] += $one['meigi_chg_cost_zei'];  // 葬儀名義変更手数料消費税
                $retgojokai['n_free6'] += $one['n_free3'];                              // 全金額一括支払割引額(税抜金額)
                $retgojokai['n_free7'] += $one['n_free4'];                              // 全金額一括支払割引額(消費税額)
                $retgojokai['n_free9'] += $one['riyoken'];                              // 利用券
                $retgojokai['n_free10'] += $one['point'];                               // ポイント
            }
            if(!isset($retgojokai['sougi_zei_cd'])) {
                $retgojokai['sougi_zei_cd'] = $one['zei_cd'];                           // 葬儀消費税コード
            }
        }
        return $retgojokai;
    }

}
