/**
 * @fileoverview 入金消込リスト 印刷指示画面
 */
var app = app || {};
$(function () {
    "use strict";

    // 画面クラスとモデルのプロパティのオブジェクト 
    app.pro = {
        kaisya_cd: '#kaisya_cd', // 会社
        torikomi_ymd: '#torikomi_ymd', // 取込日
    };

    var AppModel = Backbone.Model.extend({

        defaults: function () {
            return {
                kaisya_cd: null, // 会社
                torikomi_ymd: null, // 取込日
            };
        },

        validation: {
            kaisya_cd: {//会社
                required: true
            },
            torikomi_ymd: {// 取込日
                required: true,
                pattern: 'ymd'
            },
        },

        labels: {
            kaisya_cd: '会社',
            torikomi_ymd: '取込日',
        }

    }); // AppModel

    var AppView = Backbone.View.extend({
        el: document, // '#my-form-id', 
        events: {
            "click #btn_print": "doPrint",
        },
        bindings: {
            '#kaisya_cd': $.msiJqlib.getSelect2Binding('kaisya_cd'), // 会社
            '#torikomi_ymd': 'torikomi_ymd', // 取込日
        },

        initialize: function () {
            Backbone.Validation.bind(this);
            this.render();
        },

        render: function () {

            this.stickit();
            return this;
        },

        isInputOk: function () {
            this.clearErr();

            var aMsg = [];
            var result = this.model.validate();
            if (result) {
                _.each(result, function (v) {
                    aMsg.push(v);
                    // console.log( '*** err ' + k + ': ' + v );
                });
            }

            // NG
            if (aMsg.length > 0) {
                msiLib2.showErr(aMsg.join(', '));
                return false;
            }

            // OK
            msiLib2.clearAlert();
            return true;
        },
        //エラークラスクリア
        clearErr: function () {
            this.$el.msiErrClearAll();
        },

        // 印刷ボタン押下時
        doPrint: function (ev) {
            if (!this.isInputOk()) {
                return;
            }
            this.clearErr();

            var appObj = this.model.toJSON();

            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/saiken/pdf1204/index/',
                data: {
                    dataAppJson: JSON.stringify(appObj)
                }
            });
        },

    }); // AppView

    var appModel = new AppModel();
    var appView = new AppView({model: appModel});

    var _resetData = function (data) {
        // select2の設定
        $.msiJqlib.setSelect2Com1($("#kaisya_cd"),
                ($.extend({data: data.dataKaisya}, $.msiJqlib.setSelect2Default1)));
        //データクリア
        appModel.clear();
        //デフォルト属性設定
        appModel.set(data.dataApp);
//        appModel.set(new AppModel().toJSON());
        //エラークラスクリア
        $(document).msiErrClearAll({errCls: 'error1'});
    };

    var _setInitData = function () {
        var mydata = msiLib2.getJsonFromHtml($('#my-data-init-id'));
        _resetData(mydata);
    };

    $.msiJqlib.initDone(function () {
        _setInitData();
    }); // 処理完了

    // ラジオボタン・チェックボックスのボタン化
    $('.radio_set, .checkbox_set').buttonset();
    $('#order').fadeIn('fast'); // ちらつきのごまかし
});
