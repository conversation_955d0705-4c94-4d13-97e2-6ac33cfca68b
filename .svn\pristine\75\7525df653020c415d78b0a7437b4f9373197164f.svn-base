<?php

/**
 * DataMapper_SekyuSakiInfo
 *
 * 請求先情報 データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mihara
 * @since      2013/xx/xx
 * @filesource 
 */

/**
 * 請求先情報 データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mihara
 * @since      2013/xx/xx
 */
class DataMapper_SekyuSakiInfo extends DataMapper_Abstract {

    /**
     * データ 取得
     *
     * <AUTHOR> Mihara
     * @since      2013/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no, T.sekyu_cd ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
SELECT m.*
        ,COALESCE (m.sekyu_last_nm,'') || '　' || COALESCE (m.sekyu_first_nm,'') AS sekyu_nm_ex
        ,COALESCE (m.sekyu_last_nm,'') || COALESCE (m.sekyu_first_nm,'') AS sekyu_nm_filter
        ,COALESCE (m.sekyu_last_knm,'') || '　' || COALESCE (m.sekyu_first_knm,'') AS sekyu_knm_ex
  FROM sekyu_saki_info m
 WHERE m.delete_flg=0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }
    
    /**
     * データ 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findJuchusekyu($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no, T.denpyo_no, T.seq_no';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT T.*
        FROM (
            SELECT jssi.* 
                ,CASE WHEN CHAR_LENGTH(jssi.sekyu_nm1) > 0 AND  CHAR_LENGTH(jssi.sekyu_nm2) > 0 THEN jssi.sekyu_nm1 || ' ' || sekyu_nm2
                WHEN CHAR_LENGTH(jssi.sekyu_nm1) > 0 THEN jssi.sekyu_nm1
                WHEN CHAR_LENGTH(jssi.sekyu_nm2) > 0 THEN jssi.sekyu_nm2
                ELSE NULL END AS sekyu_nm_ex
                ,CASE WHEN CHAR_LENGTH(jssi.sekyu_knm1) > 0 AND  CHAR_LENGTH(jssi.sekyu_knm2) > 0 THEN jssi.sekyu_knm1 || ' ' || sekyu_knm2
                WHEN CHAR_LENGTH(jssi.sekyu_knm1) > 0 THEN jssi.sekyu_knm1
                WHEN CHAR_LENGTH(jssi.sekyu_knm2) > 0 THEN jssi.sekyu_knm2
                ELSE NULL END AS sekyu_knm_ex
                ,TO_CHAR(jssi.kaishu_ymd, 'YYYY/MM/DD') AS kaishu_ymd
                ,TO_CHAR(jssi.sekyu_seinengappi_ymd_y, 'YYYY/MM/DD') AS sekyu_seinengappi_ymd_y
                ,pay_cnm.kbn_value_lnm AS pay_nm
                ,CASE WHEN CHAR_LENGTH(jssi.sekyu_soufu_nm) > 0 AND  CHAR_LENGTH(jssi.v_free2) > 0 THEN jssi.sekyu_soufu_nm || ' ' || jssi.v_free2
                WHEN CHAR_LENGTH(jssi.sekyu_soufu_nm) > 0 THEN jssi.sekyu_soufu_nm
                WHEN CHAR_LENGTH(jssi.v_free2) > 0 THEN jssi.v_free2
                ELSE NULL END AS sekyu_soufu_nm_ex
            FROM juchu_sekyu_saki_info jssi
            LEFT JOIN code_nm_mst pay_cnm
                ON pay_cnm.code_kbn = '7812'
                AND pay_cnm.kbn_value_cd_num = jssi.pay_kbn
                AND pay_cnm.delete_flg = 0
            WHERE jssi.delete_flg = 0
        ) T
        WHERE 
        $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);

        return $select;
    }
    
    /**
     * データ 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForJuchusekyuDlg($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no, T.sekyu_cd';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT T.*
        FROM (
            SELECT jssi.* 
                ,CASE WHEN CHAR_LENGTH(jssi.sekyu_last_nm) > 0 AND  CHAR_LENGTH(jssi.sekyu_first_nm) > 0 THEN jssi.sekyu_last_nm || ' ' || sekyu_first_nm
                WHEN CHAR_LENGTH(jssi.sekyu_last_nm) > 0 THEN jssi.sekyu_last_nm
                WHEN CHAR_LENGTH(jssi.sekyu_first_nm) > 0 THEN jssi.sekyu_first_nm
                ELSE NULL END AS sekyu_nm_ex
                ,CASE WHEN CHAR_LENGTH(jssi.sekyu_last_knm) > 0 AND  CHAR_LENGTH(jssi.sekyu_first_knm) > 0 THEN jssi.sekyu_last_knm || ' ' || sekyu_first_knm
                WHEN CHAR_LENGTH(jssi.sekyu_last_knm) > 0 THEN jssi.sekyu_first_knm
                WHEN CHAR_LENGTH(jssi.sekyu_first_knm) > 0 THEN jssi.sekyu_first_knm
                ELSE NULL END AS sekyu_knm_ex
                ,CASE WHEN CHAR_LENGTH(jssi.sekyu_soufu_nm) > 0 AND  CHAR_LENGTH(jssi.v_free2) > 0 THEN jssi.sekyu_soufu_nm || ' ' || jssi.v_free2
                WHEN CHAR_LENGTH(jssi.sekyu_soufu_nm) > 0 THEN jssi.sekyu_soufu_nm
                WHEN CHAR_LENGTH(jssi.v_free2) > 0 THEN jssi.v_free2
                ELSE NULL END AS sekyu_soufu_nm_ex
                ,COALESCE (jssi.sekyu_last_nm,'') || COALESCE (jssi.sekyu_first_nm,'') AS sekyu_nm_filter
            FROM sekyu_saki_info jssi
            WHERE jssi.delete_flg = 0
        ) T
        WHERE 
        $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);

        return $select;
    }
    
    /**
     * データ 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findUriagesekyu($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no, T.uri_den_no, T.seq_no';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT T.*
        FROM (
            SELECT ussi.* 
                ,TO_CHAR(ussi.kaishu_ymd, 'YYYY/MM/DD') AS kaishu_ymd
            FROM uriage_sekyu_saki_info ussi
            WHERE ussi.delete_flg = 0
        ) T
        WHERE 
        $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);

        return $select;
    }
    
    /**
     * データ 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findSeikyusekyu($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no, T.seikyu_den_no, T.seq_no';
        }

        $select = $db->easySelect(<<< END_OF_SQL
        SELECT T.*
        FROM (
            SELECT sssi.* 
                ,TO_CHAR(sssi.kaishu_ymd, 'YYYY/MM/DD') AS kaishu_ymd
                ,TO_CHAR(sssi.sekyu_seinengappi_ymd_y, 'YYYY/MM/DD') AS sekyu_seinengappi_ymd_y
            FROM seikyu_sekyu_saki_info sssi
            WHERE sssi.delete_flg = 0
        ) T
        WHERE 
        $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);

        return $select;
    }
    /**
     * 
     * 領収証発行情報
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findRyosyuHako($db, $keyHash=array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no';
        }
        $select = $db->easySelOne( <<< END_OF_SQL
    SELECT 
        * 
    FROM (
            SELECT 
                ski.seko_no
               ,ski.bumon_cd
               ,ski.sekyu_cd
               ,ssi.ryosyu_soufu_yubin_no
               ,COALESCE (ssi.ryosyu_soufu_addr1,'') AS ryosyu_soufu_addr1
               ,COALESCE (ssi.ryosyu_soufu_addr2,'') AS ryosyu_soufu_addr2
               ,COALESCE (ssi.ryosyu_soufu_addr1,'') || COALESCE (ssi.ryosyu_soufu_addr2,'') AS ryosyu_soufu_addr
               ,ssi.ryosyu_meigi
               ,ssi.ryosyu_soufu_nm
               ,ssi.ryosyu_soufu_last_nm
               ,ssi.ryosyu_soufu_first_nm
               ,bm_oya.zip_no                        AS kaisya_zip_no
               ,bm_oya.addr1_nm                      AS kaisya_addr1
               ,bm_oya.addr2_nm                      AS kaisya_addr2
               ,bm_oya.tel                           AS kaisya_tel
               ,bm_oya.f_free1                       AS kaisya_logo
            FROM seko_kihon_info ski
            LEFT JOIN sekyu_saki_info ssi
            ON ssi.sekyu_cd = ski.sekyu_cd
            AND ssi.delete_flg = 0
            LEFT JOIN bumon_kaso_mst bkm
            ON ski.bumon_cd = bkm.ko_bumon_cd
            AND bkm.delete_flg = 0
            LEFT JOIN bumon_mst bm_oya
            ON bkm.oya_bumon_cd = bm_oya.bumon_cd
            AND bm_oya.delete_flg = 0
    ) T
    WHERE 
    $whereStr
    $orderBy
    $tailClause
END_OF_SQL
            , $param );
        return $select;
    }
    /**
     * 
     * 領収証発行情報
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findRyosyuHako2($db, $keyHash=array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no';
        }
        $select = $db->easySelOne( <<< END_OF_SQL
    SELECT 
        * 
    FROM (
            SELECT 
                ski.seko_no
               ,ski.bumon_cd
               ,ski.sekyu_cd
               ,ssi.ryosyu_soufu_yubin_no
               ,COALESCE (ssi.ryosyu_soufu_addr1,'') AS ryosyu_soufu_addr1
               ,COALESCE (ssi.ryosyu_soufu_addr2,'') AS ryosyu_soufu_addr2
               ,COALESCE (ssi.ryosyu_soufu_addr1,'') || COALESCE (ssi.ryosyu_soufu_addr2,'') AS ryosyu_soufu_addr
               ,ssi.ryosyu_meigi
               ,ssi.ryosyu_soufu_nm
               ,ssi.ryosyu_soufu_last_nm
               ,ssi.ryosyu_soufu_first_nm
               ,bm_oya.zip_no                        AS kaisya_zip_no
               ,bm_oya.addr1_nm                      AS kaisya_addr1
               ,bm_oya.addr2_nm                      AS kaisya_addr2
               ,bm_oya.tel                           AS kaisya_tel
               ,bm_oya.f_free1                       AS kaisya_logo
               ,seikyu.ryosyusyo_soufu_yubin_no                                     AS seikyu_ryosyu_soufu_yubin_no -- 領収証送付先郵便番号
               ,seikyu.ryosyusyo_soufu_addr1                                        AS seikyu_ryosyu_soufu_addr1    -- 領収証送付先住所1
               ,seikyu.ryosyusyo_soufu_addr2                                        AS seikyu_ryosyu_soufu_addr2    -- 領収証送付先住所2
               ,CONCAT(seikyu.ryosyusyo_soufu_addr1,seikyu.ryosyusyo_soufu_addr2)   AS seikyu_ryosyu_soufu_addr     -- 領収証送付先住所
               ,seikyu.ryosyusyo_soufu_nm                                           AS seikyu_ryosyu_soufu_nm       -- 領収証送付先名
               ,seikyu.seikyu_den_no
            FROM seko_kihon_info ski
            LEFT JOIN sekyu_saki_info ssi
            ON ssi.sekyu_cd = ski.sekyu_cd
            AND ssi.delete_flg = 0
            LEFT JOIN seikyu_sekyu_saki_info seikyu
            ON  seikyu.seikyu_den_no = :seikyu_den_no
            AND seikyu.seko_no       = ski.seko_no
            AND seikyu.delete_flg    = 0
            LEFT JOIN bumon_kaso_mst bkm
            ON ski.bumon_cd = bkm.ko_bumon_cd
            AND bkm.delete_flg = 0
            LEFT JOIN bumon_mst bm_oya
            ON bkm.oya_bumon_cd = bm_oya.bumon_cd
            AND bm_oya.delete_flg = 0
    ) T
    WHERE 
    $whereStr
    $orderBy
    $tailClause
END_OF_SQL
            , $param );
        return $select;
    }
    /**
     * 
     * 領収証発行情報（アフター用）
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findRyosyuHakoAfter($db, $keyHash=array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no';
        }
        $select = $db->easySelOne( <<< END_OF_SQL
    SELECT 
        * 
    FROM (
        SELECT 
             ssi.ryosyu_soufu_yubin_no
            ,COALESCE (ssi.ryosyu_soufu_addr1,'')                                           AS ryosyu_soufu_addr1
            ,COALESCE (ssi.ryosyu_soufu_addr2,'')                                           AS ryosyu_soufu_addr2
            ,COALESCE (ssi.ryosyu_soufu_addr1,'') || COALESCE (ssi.ryosyu_soufu_addr2,'')   AS ryosyu_soufu_addr
            ,seikyu.ryosyusyo_soufu_yubin_no                                                AS seikyu_ryosyu_soufu_yubin_no -- 領収証送付先郵便番号
            ,seikyu.ryosyusyo_soufu_addr1                                                   AS seikyu_ryosyu_soufu_addr1    -- 領収証送付先住所1
            ,seikyu.ryosyusyo_soufu_addr2                                                   AS seikyu_ryosyu_soufu_addr2    -- 領収証送付先住所2
            ,CONCAT(seikyu.ryosyusyo_soufu_addr1,seikyu.ryosyusyo_soufu_addr2)              AS seikyu_ryosyu_soufu_addr     -- 領収証送付先住所
            ,seikyu.ryosyusyo_soufu_nm                                                      AS seikyu_ryosyu_soufu_nm       -- 領収証送付先名
            ,seikyu.seikyu_den_no
            ,seikyu.seko_no
            ,sd.bumon_cd
        FROM seikyu_sekyu_saki_info seikyu
        LEFT JOIN sekyu_saki_info ssi
        ON  ssi.sekyu_cd = seikyu.sekyu_cd
        AND ssi.delete_flg = 0
        LEFT JOIN seikyu_denpyo sd
        ON  sd.seikyu_den_no = seikyu.seikyu_den_no
        AND sd.delete_flg    = 0
    ) T
    WHERE 
    $whereStr
    $orderBy
    $tailClause
END_OF_SQL
            , $param );
        return $select;
    }
    /**
     * データ 登録・更新
     *
     * <AUTHOR> Mihara
     * @since      2014/xx/xx
     * @version 2014/01/18 請求先コードのみで検索するように修正 Kayo
     * @param      Msi_Sys_Db $db
     * @param      array      $data
     * @param      string     $kijyunYmd 基準日(YYYY/MM/DD形式)  新規登録の場合必要. nullの場合は現在日
     * @param      boolean    $isCheckUnchange  更新時、データ変更がない場合は更新しない
     * @return     array      登録データ
     */
    public static function upsert($db, $data, $kijyunYmd = null, $isCheckUnchange = true) {
        $isAdd = false; // 新規登録:真, 更新:偽
        $sekyu_cd = null;
        if (isset($data['sekyu_cd'])) {
            $sekyu_cd = Msi_Sys_Utils::emptyToNull($data['sekyu_cd']);
        }
        $seko_no = $data['seko_no'];
        if ($sekyu_cd !== null) { // 更新
            $sekyuRec = DataMapper_SekyuSakiInfo::findOne($db, array('sekyu_cd' => $sekyu_cd));
            // 'seko_no'=>$seko_no) );
            if ($sekyuRec === null) {
                throw new Msi_Sys_Exception_InputException("請求先 ($sekyu_cd) のデータが存在しません");
            }
            if ($isCheckUnchange) {
                // 施行基本に登録されている請求先の場合
                $sekoKihon = DataMapper_SekoKihon::find($db, array('sekyu_cd' => $sekyu_cd));
                if (count($sekoKihon) > 0) {
                    return $data;
                }
                $isSame = Msi_Sys_Utils::isSameRec($data, $sekyuRec);
                if ($isSame) { // データ変更がない場合は更新しない
                    return $data;
                }
            }
            unset($data['seko_no']);
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL2('sekyu_saki_info', $data, array('sekyu_cd'));
            // 2016/05/04 DEL Kayo , 'seko_no') );
        } else { // 新規登録
            $isAdd = true;
            if ($kijyunYmd === null) {
                $kijyunYmd = Msi_Sys_Utils::getDate(); // 現在日
            }
            $sekyu_cd = App_ClsGetCodeNo::GetCodeNo($db, 'sekyu_saki_info', 'sekyu_cd', $kijyunYmd);
            $data['sekyu_cd'] = $sekyu_cd;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('sekyu_saki_info', $data);
        }

        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        $cnt = $db->easyExecute($sql, $param);

        return $data;
    }

    /**
     * 受注請求先データ 登録・更新
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $hdrData
     * @param      array      $data
     * @param      array      $delData
     * @return     array      登録データ
     */
    public static function juchusekyuUpsert($db, $hdrData, $data, $delData) {
        
        // 削除→登録処理を行う
        $db->easyExecute( <<< END_OF_SQL
        DELETE FROM juchu_sekyu_saki_info
        WHERE denpyo_no = :denpyo_no
END_OF_SQL
        , array('denpyo_no' => $hdrData['denpyo_no']) );
        $maxSeqNo = self::getMaxSeqNo('juchu_sekyu_saki_info', 'denpyo_no', $hdrData['denpyo_no']);
        foreach ($data as $onerow) {
            $juchuSeikyuSaki = static::_filterJuchuSekyuSaki($onerow);
            $juchuSeikyuSaki['denpyo_no'] = $hdrData['denpyo_no'];
            $juchuSeikyuSaki['seko_no'] = $hdrData['seko_no'];
            $juchuSeikyuSaki['data_kbn'] = $hdrData['data_kbn'];
            $juchuSeikyuSaki['seq_no'] = ++$maxSeqNo;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_sekyu_saki_info', $juchuSeikyuSaki);
            $db->easyExecute($sql, $param);
        }
        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        return $data;
    }
    
    /**
     * 売上請求先データ 登録・更新
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $hdrData
     * @param      array      $data
     * @param      array      $delData
     * @return     array      登録データ
     */
    public static function uriagesekyuUpsert($db, $hdrData, $data, $delData) {
        
          // 売上伝票データを取得する
        $select = DataMapper_UriageDenpyo2::find($db, array('denpyo_no' => $hdrData['denpyo_no']));
        $uriData = $select[0];
        // 削除→登録処理を行う
        $db->easyExecute( <<< END_OF_SQL
        DELETE FROM uriage_sekyu_saki_info
        WHERE uri_den_no = :uri_den_no
END_OF_SQL
        , array('uri_den_no' => $uriData['seikyu_no']) );
        $maxSeqNo = self::getMaxSeqNo('uriage_sekyu_saki_info', 'uri_den_no', $uriData['seikyu_no']);
        foreach ($data as $onerow) {
            $uriageSeikyuSaki = static::_filterUriageSekyuSaki($onerow);
            $uriageSeikyuSaki['uri_den_no'] = $uriData['seikyu_no'];
            $uriageSeikyuSaki['seko_no'] = $hdrData['seko_no'];
            $uriageSeikyuSaki['data_kbn'] = $hdrData['data_kbn'];
            $uriageSeikyuSaki['seq_no'] = ++$maxSeqNo;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('uriage_sekyu_saki_info', $uriageSeikyuSaki);
            $db->easyExecute($sql, $param);
        }
        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        return $data;
    }
    
    /**
     * 請求請求先データ 登録・更新
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $hdrData
     * @param      array      $data
     * @param      array      $delData
     * @return     array      登録データ
     */
    public static function seikyusekyuUpsert($db, $hdrData, $data, $delData) {
        
        // 請求伝票データを取得する
        $uriSelect = DataMapper_UriageDenpyo2::find($db, array('denpyo_no' => $hdrData['denpyo_no']));
        $sekyuSelect = DataMapper_SeikyuDenpyo::find($db, array('uri_den_no' => $uriSelect[0]['seikyu_no']));
        $sekyuData = $sekyuSelect[0];
        // 削除→登録処理を行う
        $db->easyExecute( <<< END_OF_SQL
        DELETE FROM seikyu_sekyu_saki_info
        WHERE seikyu_den_no = :seikyu_den_no
END_OF_SQL
        , array('seikyu_den_no' => $sekyuData['seikyu_den_no']) );
        // 削除データがあれば削除する
        if (count($delData) > 0) {
            foreach ($delData as $delone) {
                $delrow = array();
                $where = array();
                $delrow['delete_flg'] = 1;
                $where['seikyu_den_no'] = $sekyuData['seikyu_den_no'];
                $where['seq_no'] = $delone['seq_no'];
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_sekyu_saki_info', $delrow, $where);
                $db->easyExecute($sql, $param);
                // 請求分割されている場合は分割先の伝票を論理削除し、請求先情報は物理削除を行う
                if ($sekyuData['bun_gas_kbn_num'] == '1') {
                    $bunWhere = array();
                    $delSaki['delete_flg'] = 1;
                    $bunWhere['seikyu_den_no'] = $delone['seikyu_den_no'];
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo', $delSaki, $bunWhere);
                    $db->easyExecute($sql, $param);
                    list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seikyu_denpyo_msi', $delSaki, $bunWhere);
                    $db->easyExecute($sql, $param);
                    $db->easyExecute( <<< END_OF_SQL
                    DELETE FROM seikyu_sekyu_saki_info
                    WHERE seikyu_den_no = :seikyu_den_no
END_OF_SQL
                    , array('seikyu_den_no' => $delone['seikyu_den_no']) );
                }
            }
        }
        $maxSeqNo = self::getMaxSeqNo('seikyu_sekyu_saki_info', 'seikyu_den_no', $sekyuData['seikyu_den_no']);
        foreach ($data as $onerow) {
            $seikyuSeikyuSaki = static::_filterSeikyuSekyuSaki($onerow);
            $seikyuSeikyuSaki['seikyu_den_no'] = $sekyuData['seikyu_den_no'];
            $seikyuSeikyuSaki['seko_no'] = $hdrData['seko_no'];
            $seikyuSeikyuSaki['data_kbn'] = $hdrData['data_kbn'];
            $seikyuSeikyuSaki['seq_no'] = ++$maxSeqNo;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('seikyu_sekyu_saki_info', $seikyuSeikyuSaki);
            $db->easyExecute($sql, $param);
        }
        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        return $data;
    }
    
    /**
     * 受注→売上請求先データ 登録処理
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      string      $uri_den_no
     * @param      array      $data
     * @return     array      登録データ
     */
    public static function uriageFromJuchuSekyuUpsert($db, $uri_den_no, $data) {
        
        // 売上伝票データを取得する
        $select = DataMapper_UriageDenpyo2::find($db, array('seikyu_no' => $uri_den_no));
        $uriData = $select[0];
        foreach ($data as $onerow) {
            $uriageSeikyuSaki = static::_filterUriageSekyuSaki($onerow);
            // 登録処理
            $uriageSeikyuSaki['uri_den_no'] = $uriData['seikyu_no'];
            $uriageSeikyuSaki['seko_no'] = $uriData['seko_no'];
            $uriageSeikyuSaki['data_kbn'] = $uriData['data_kbn'];
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('uriage_sekyu_saki_info', $uriageSeikyuSaki);
            $db->easyExecute($sql, $param);
        }
        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        return $data;
    }
    
    /**
     * 売上→請求請求先データ 登録処理
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      string      $seikyu_den_no
     * @param      array      $data
     * @return     array      登録データ
     */
    public static function seikyuFromUriageSekyuUpsert($db, $seikyu_den_no, $data) {
        
        // 請求伝票データを取得する
        $select = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $seikyu_den_no));
        $seikyuData = $select[0];
        foreach ($data as $onerow) {
            $seikyuSekyuSaki = static::_filterSeikyuSekyuSaki($onerow);
            // 登録処理
            $seikyuSekyuSaki['seikyu_den_no'] = $seikyuData['seikyu_den_no'];
            $seikyuSekyuSaki['seko_no'] = $seikyuData['seko_no'];
            $seikyuSekyuSaki['data_kbn'] = $seikyuData['data_kbn'];
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('seikyu_sekyu_saki_info', $seikyuSekyuSaki);
            $db->easyExecute($sql, $param);
        }
        // Msi_Sys_Utils::debug( '** SQL=>' . $sql . ' param=>' . Msi_Sys_Utils::dump($param) );
        return $data;
    }
    
    /**
     * 連番最大値取得処理
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      string      $table_nm
     * @param      string      $column_nm
     * @param      string      $column_value
     * @return     string      最大値連番
     */
    Private function getMaxSeqNo($table_nm, $column_nm, $column_value) {
        
        $maxMsiNo = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT COALESCE(MAX(seq_no),0) AS seq_no
        FROM $table_nm
        WHERE $column_nm = :$column_nm
                ";
        $select = $db->easySelOne($sql, array($column_nm => $column_value));
        if (count($select) > 0) {
            $maxMsiNo = $select['seq_no'];
        }
        return (int)$maxMsiNo;
    }
    
    /**
     * 受注請求先情報 項目フィルタ
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      array      $juchuSekyuSaki
     * @return     array
     */
    protected static function _filterJuchuSekyuSaki($juchuSekyuSaki)
    {

        $newSeikyuSaki = Msi_Sys_Utils::remapArrayFlat($juchuSekyuSaki, <<< END_OF_TXT
        denpyo_no seq_no seko_no data_kbn pay_kbn houjin_kbn houjin_sbt soufu_kbn
        sekyu_prc sekyu_cd sekyu_nm sekyu_knm sekyu_nm1 sekyu_nm2 sekyu_knm1 sekyu_knm2 sekyu_yubin_no
        sekyu_addr1 sekyu_addr2 sekyu_tel sekyu_mobile_tel sekyu_soufu_nm soufu_yubin_no
        soufu_addr1 soufu_addr2 soufu_tel rs_print_kbn rs_soufu_kbn ryosyu_prc ryosyusyo_meigi
        ryosyusyo_soufu_nm ryosyusyo_soufu_yubin_no ryosyusyo_soufu_addr1 ryosyusyo_soufu_addr2
        kaishu_ymd syorui_tenpu_kbn delete_flg sekyu_fax sekyu_biko1 sekyu_biko2 d_import_kbn
        sekyu_sex_kbn sekyu_gengo sekyu_birth_year sekyu_birth_month sekyu_birth_day sekyu_seinengappi_ymd sekyu_seinengappi_ymd_y
        v_free1 v_free2 v_free3 v_free4 v_free5
        free_kbn1 free_kbn2 free_kbn3 free_kbn4 free_kbn5
        n_free1 n_free2 n_free3 n_free4 n_free5
        d_free1 d_free2 d_free3
END_OF_TXT
                                                       , array());
        // emptyToNull
        $newSeikyuSaki = Msi_Sys_Utils::emptyToNullArr($newSeikyuSaki);
        return $newSeikyuSaki;
    }
    
    /**
     * 売上請求先情報 項目フィルタ
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      array      $uriageSekyuSaki
     * @return     array
     */
    protected static function _filterUriageSekyuSaki($uriageSekyuSaki)
    {

        $newSeikyuSaki = Msi_Sys_Utils::remapArrayFlat($uriageSekyuSaki, <<< END_OF_TXT
        uri_den_no seq_no seko_no data_kbn pay_kbn houjin_kbn houjin_sbt soufu_kbn
        sekyu_prc sekyu_cd sekyu_nm sekyu_knm sekyu_nm1 sekyu_nm2 sekyu_knm1 sekyu_knm2 sekyu_yubin_no
        sekyu_addr1 sekyu_addr2 sekyu_tel sekyu_mobile_tel sekyu_soufu_nm
        soufu_yubin_no soufu_addr1 soufu_addr2 soufu_tel rs_print_kbn rs_soufu_kbn
        ryosyu_prc ryosyusyo_meigi ryosyusyo_soufu_nm ryosyusyo_soufu_yubin_no
        ryosyusyo_soufu_addr1 ryosyusyo_soufu_addr2 kaishu_ymd syorui_tenpu_kbn
        bun_gas_uri_den_nobun_gas_seq_no delete_flg sekyu_fax sekyu_biko1 sekyu_biko2 d_import_kbn
        sekyu_sex_kbn sekyu_gengo sekyu_birth_year sekyu_birth_month sekyu_birth_day sekyu_seinengappi_ymd sekyu_seinengappi_ymd_y
        v_free1 v_free2 v_free3 v_free4 v_free5
        free_kbn1 free_kbn2 free_kbn3 free_kbn4 free_kbn5
        n_free1 n_free2 n_free3 n_free4 n_free5
        d_free1 d_free2 d_free3
END_OF_TXT
                                                       , array());
        // emptyToNull
        $newSeikyuSaki = Msi_Sys_Utils::emptyToNullArr($newSeikyuSaki);
        return $newSeikyuSaki;
    }
    
    /**
     * 請求請求先情報 項目フィルタ
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      array      $seikyuSekyuSaki
     * @return     array
     */
    protected static function _filterSeikyuSekyuSaki($seikyuSekyuSaki)
    {

        $newSeikyuSaki = Msi_Sys_Utils::remapArrayFlat($seikyuSekyuSaki, <<< END_OF_TXT
        seikyu_den_no seq_no seko_no data_kbn pay_kbn houjin_kbn houjin_sbt soufu_kbn
        sekyu_prc sekyu_cd sekyu_nm sekyu_knm sekyu_nm1 sekyu_nm2 sekyu_knm1 sekyu_knm2 sekyu_yubin_no
        sekyu_addr1 sekyu_addr2 sekyu_tel sekyu_mobile_tel sekyu_soufu_nm soufu_yubin_no
        soufu_addr1 soufu_addr2 soufu_tel rs_print_kbn rs_soufu_kbn ryosyu_prc ryosyusyo_meigi
        ryosyusyo_soufu_nm ryosyusyo_soufu_yubin_no ryosyusyo_soufu_addr1 ryosyusyo_soufu_addr2
        kaishu_ymd syorui_tenpu_kbn bun_gas_seikyu_den_no bun_gas_seq_no delete_flg sekyu_fax sekyu_biko1 sekyu_biko2 d_import_kbn
        sekyu_sex_kbn sekyu_gengo sekyu_birth_year sekyu_birth_month sekyu_birth_day sekyu_seinengappi_ymd sekyu_seinengappi_ymd_y
        v_free1 v_free2 v_free3 v_free4 v_free5
        free_kbn1 free_kbn2 free_kbn3 free_kbn4 free_kbn5
        n_free1 n_free2 n_free3 n_free4 n_free5
        d_free1 d_free2 d_free3
END_OF_TXT
                                                       , array());
        // emptyToNull
        $newSeikyuSaki = Msi_Sys_Utils::emptyToNullArr($newSeikyuSaki);
        return $newSeikyuSaki;
    }

}
