<?php

/**
 * Mr<PERSON>_TantodlgController
 *
 * 担当者 検索ダイアログ
 *
 * @category   App
 * @package    controllers\Mref
 * <AUTHOR> Mihara
 * @since      2014/xx/xx
 * @filesource 
 */

/**
 * 担当者 検索ダイアログ
 *
 * @category   App
 * @package    controllers\Mref
 * <AUTHOR> Mihara
 * @since      2014/xx/xx
 */
class Mref_TantodlgController extends Mref_AbstractdlgController {
    /**
     * index(default) アクション
     *
     * <AUTHOR> Sugiyama
     * @since 2025/03/xx
     */
    public function indexAction() {
        $this->_indexPreHook();
        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }

        if (isset($params['no_cond']) && !!$params['no_cond']) {
            $this->view->is_no_cond = true;
        }

        if (isset($params['no_footer']) && !!$params['no_footer']) {
            $this->view->is_no_footer = true;
        }
        // 部門非活性パラメータ
        if (isset($params['bumon_disabled']) && !!$params['bumon_disabled']) {
            $this->view->is_bumon_disabled = true;
        }

        if (isset($params['init_search']) && !!$params['init_search']) {
            $this->_params['init_search_flg'] = 1; // 2017/05/31 ADD Kobayashi 初期検索フラグ
            $this->listAction();
        }

        App_Smarty::pushCssFile(['app/mref.dialog.css']);
        App_Smarty::pushJsFile(['app/mref.dialog.js']); // cf. jqlib.js:onInitFunc

        $this->_setScriptActionIndex();
    }

    /**
     * indexAction の初期処理
     *
     * <AUTHOR> Mihara
     * @since  2020/12/04
     * @return void
     */
	protected function _indexPreHook()
    {
        $this->_params = $params = Msi_Sys_Utils::webInputs();

        // 部門の初期設定
        // init_s_kaisya_cd: 'myKaisya' => 自社配下で bumon_kbn が該当する部門
        // init_s_kaisya_cd: <seko_kaisya_cd> => <seko_kaisya_cd>配下で bumon_kbn が該当する部門
        if ( Msi_Sys_Utils::arrIsSet($params, 'spe_bumon_kind2') && Msi_Sys_Utils::arrIsSet($params, 'init_s_kaisya_cd') ) {
            $s_bumon_cd = Msi_Sys_Utils::easyGetVar($params, 's_bumon_cd');
            if ( strlen($s_bumon_cd) > 0) { // 明示的に s_bumon_cd が設定されている場合は何もしない
                return;
            }
            $bumon_kind2 = $params['spe_bumon_kind2'];
            $init_s_kaisya_cd = $params['init_s_kaisya_cd'];
            if ( $init_s_kaisya_cd == 'myKaisya' ) {
                $seko_kaisya_cd = Logic_Hanso_HansoUtils::guessMyKaisyaBumonCd();
            } else {
                $seko_kaisya_cd = $init_s_kaisya_cd;
            }
            if ( $seko_kaisya_cd ) {
                $_cond = array( 'bumon_kbn' => DataMapper_Utils::condOneOf('bumon_kbn', $bumon_kind2) );
                $bumon_cd = App_HakuzenUtils::getCondBumonCd( $seko_kaisya_cd, $_cond );
                if ( $bumon_cd ) {
                    $this->_params['s_bumon_cd'] = $bumon_cd;
                }
            }
        }
    }

    /**
     * 検索処理
     *
     * @return array($data, $hash)
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     */
    protected function _doSearch() {
        $params = $this->_params;
        $data = array();
        $hash = array();

        $db = Msi_Sys_DbManager::getMyDb();

        $code = Msi_Sys_Utils::easyGetVar($params, 's_code');
        $name = Msi_Sys_Utils::easyGetVar($params, 's_name');
        $kana = Msi_Sys_Utils::easyGetVar($params, 's_kana');
        $bumon_cd = Msi_Sys_Utils::easyGetVar($params, 's_bumon_cd');
        $syokusyu_kbn = Msi_Sys_Utils::easyGetVar($params, 's_syokusyu_kbn');
        $common_bumon_check = Msi_Sys_Utils::easyGetVar($params, 'common_bumon_check', null, false); // boolean
        $bumon_bumon_kbn = Msi_Sys_Utils::easyGetVar($params, 's_bumon_bumon_kbn'); // mihara added 2020/12/04
        $mode = Msi_Sys_Utils::easyGetVar($params, 'mode');
        $this->_params['limit']  = $limit  = Msi_Sys_Utils::easyGetVar($params, 'limit', 'DIGIT', 100);
        $this->_params['offset'] = $offset = Msi_Sys_Utils::easyGetVar($params, 'offset', 'DIGIT', 0);
        $this->_params['no_date_check'] = $no_date_check = Msi_Sys_Utils::easyGetVar($params, 'no_date_check', 'DIGIT', 0);
        $cond = array(
                      '__etc_limit'   => $limit +1,
                      '__etc_offset'  => $offset,
                      );

        $isBumonAccCheck = App_Utils::isBumonAccCheck(); // 部門権限チェック可否
        // cf. Mref_SekodialogAbstController
        // 2016/09/xx 複数会社対応 -- BEGIN --
        $is_bumon_special_done = false;
        if ($isBumonAccCheck) {
            $kaisya_cd = App_Utils::getSessionData('ctxt_kaisya_cd');
            if (strlen($bumon_cd) > 0) {
                if (App_Utils::isBumonInKaisya($bumon_cd, $kaisya_cd) && App_Utils::isAllowBumon($bumon_cd)) {
                    $cond['__x5'] = DataMapper_BumonEx::findDesCond($db, $bumon_cd);
                    $is_bumon_special_done = true;
                }
            }
            if (!$is_bumon_special_done) {
                $_bumonCds = array();
                $allBumons = App_Utils::getMyAllowedBumons();
                foreach ($allBumons as $_cd) {
                    if (App_Utils::isBumonInKaisya($_cd, $kaisya_cd)) {
                        $_bumonCds[] = $_cd;
                    }
                }
                if (count($_bumonCds) > 0) {
                    $cond['__x5'] = DataMapper_Utils::condOneOf('bumon_cd', implode(',', $_bumonCds));
                } else {
                    $cond['__raw_5'] = '1=2';
                }
                $is_bumon_special_done = true;
            }
        } // 2016/09/xx 複数会社対応 -- END --

        if (strlen($code) > 0) {
            $cond['tanto_cd'] = array('~', $code);
            $this->view->s_code = $code;
        }
        if (strlen($name) > 0) {
            $cond['__x1'] = array('x', "(tanto_nm ~ :x1_1)",
                array('x1_1' => $name));
            $this->view->s_name = $name;
        }
        if (strlen($kana) > 0) {
            $cond['__x2'] = array('x', "(tanto_knm ~ :x2_1)",
                array('x2_1' => $kana));
            $this->view->s_kana = $kana;
        }
        // 予算入力区分を参照する場合 2016/04/09 ADD Kayo
        if (isset($params['s_yosan_inp'])) {
            $cond['__x3'] = array('x', "yosan_inp_kbn = :x3_1", array('x3_1' => $params['s_yosan_inp']));
        }

        if (!$is_bumon_special_done && strlen($bumon_cd) > 0) {
            // $cond['bumon_cd'] = $bumon_cd;
            $cond['bumon_cd'] = DataMapper_BumonEx::findDesCond($db, $bumon_cd);
        }
        if (isset($syokusyu_kbn) && strlen($syokusyu_kbn) > 0) {
            $cond['__x6'] = array('x', "syokusyu_kbn = :x6_1", array('x6_1' => $syokusyu_kbn));
        }

        if ( strlen($bumon_bumon_kbn) > 0 ) { // mihara added 2020/12/04
            $cond['__x7'] = DataMapper_Utils::condOneOf('bumon_kbn', $bumon_bumon_kbn);
        }
        $bumon_cd = App_Utils::getCtxtHallWithKaisya();
        $bumon_ref = App_Utils2::getBumonRef($mode, $bumon_cd, $db);
        if(isset($bumon_ref) && Msi_Sys_Utils::myCount($bumon_ref) > 0){
            // 参照権限部門を条件に追加
            $cond['__x20'] = DataMapper_Utils::condOneOf('bumon_cd', implode(',', $bumon_ref));
        }else if(isset($bumon_ref)){
            // 参照権限部門が無い場合は無し
            $this->view->list_data = array();
            $this->view->my_msg = '該当するデータはありません';
            $this->_setScriptVar();
            return;
        }
        $select = DataMapper_TantoMst::find($db, $cond, !$no_date_check);
        if ( count($select) >= ($limit+1) ) {
            $isMoreData = true;
        }
        $count = 0;
        $lastBumonCd = null;
        foreach ($select as $rec) {
            if ( (string)$lastBumonCd === (string)$rec['bumon_cd'] ) {
                continue;
            }
            $count++;
            if ( $count > $limit ) {
                // $isMoreData = true;
                break;
            }
            $myid = $rec['myid'] = $rec['tanto_cd'];
            $rec['code'] = $rec['tanto_cd'];
            $rec['name'] = $rec['tanto_nm'];
            $rec['kana'] = $rec['tanto_knm'];
            $rec['bumon'] = $rec['bumon_cd'] . ' ' . $rec['bumon_snm'];
            // 所属部門の共通部門が必要な場合はtrueにする
            if ($common_bumon_check) {
                $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $rec['bumon_cd']));
                $comBumonCd = App_Utils2::getCommonBumonCd($bumonData[0]['bumon_cd']);
                $rec['common_bumon_cd'] = $comBumonCd;
            }
            $rec['oddEven'] = $count % 2 ? 'odd' : 'even';
            // 所属部門の親部門の配下の共通部門があれば設定する
            $data[] = $rec;
            $hash[$myid] = $rec;
        }

        // Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($data) );

        return array($data, $hash, $isMoreData);
    }

    /**
     * テンプレート変数設定
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     * @return void
     */
    protected function _setScriptVar() {
        $this->view->dlg_title = '担当者検索';
        if ( Msi_Sys_Utils::arrIsSet($this->_params, 'mytitle') ) {
            $this->view->dlg_title = $this->_params['mytitle'];
        }
        if ( Msi_Sys_Utils::arrIsSet($this->_params, 'spe_bumon_kind2') ) { // mihara added 2020/12/04
            // $this->view->bumon_kind2 = $this->_params['spe_bumon_kind2']; // mihara 2021/02/25 #4324
            $this->view->dlg_tpl_search = 'search-tanto-bk2.tpl';
        } else {
            $this->view->dlg_tpl_search = 'search-tanto.tpl';
        }
        $this->view->dlg_tpl_head = 'head-tanto.tpl';
        $this->view->dlg_tpl_list = 'list-tanto.tpl';
        $this->view->l1_name = '氏名';
        $this->view->l1_bumon = '部門';
        $this->view->l2_name = '氏名';
        $this->view->l2_bumon = '部門';
        $this->view->l2_syokusyu = '職種'; // 2016/04/23 ADD Kayo
        // ひらがな対応 2018/05/25 ADD Kayo
        $getCstmKey = Msi_Sys_Utils::getCstmKeyArr();
        foreach ($getCstmKey as $rec) {
            if ($rec == 'hiragana') {
                $this->view->l1_kana = 'ひらがな';
                $this->view->l2_kana = 'ひらがな';
                break;
            }
        }


        foreach (Msi_Sys_Utils::strArrayify_qw('s_code s_name s_kana s_bumon_cd s_syokusyu_kbn') as $k) {
            @ $this->view->$k = $this->_params[$k];
        }
    }

}
