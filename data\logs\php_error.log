[22-Apr-2025 20:59:59 Asia/Tokyo] The pgsql driver is not currently installed
[22-Apr-2025 20:59:59 Asia/Tokyo] PHP Fatal error:  Uncaught Zend_Db_Adapter_Exception: The pgsql driver is not currently installed in C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Pdo\Abstract.php:112
Stack trace:
#0 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Pdo\Pgsql.php(87): Zend_Db_Adapter_Pdo_Abstract->_connect()
#1 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Abstract.php(460): Zend_Db_Adapter_Pdo_Pgsql->_connect()
#2 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Pdo\Abstract.php(238): Zend_Db_Adapter_Abstract->query('INSERT INTO "s_...', Array)
#3 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Abstract.php(576): Zend_Db_Adapter_Pdo_Abstract->query('INSERT INTO "s_...', Array)
#4 C:\dev\Fdn.keigen\library\Zend\Log\Writer\Db.php(145): Zend_Db_Adapter_Abstract->insert('s_log_std', Array)
#5 C:\dev\Fdn.keigen\library\Zend\Log\Writer\Abstract.php(85): Zend_Log_Writer_Db->_write(Array)
#6 C:\dev\Fdn.keigen\library\Zend\Log.php(439): Zend_Log_Writer_Abstract->write(Array)
#7 C:\dev\Fdn.keigen\library\Msi\Sys\Log\Standard.php(79): Zend_Log->log('The pgsql drive...', 3, Array)
 in C:\dev\Fdn.keigen\library\Zend\Controller\Plugin\Broker.php on line 336
[22-Apr-2025 21:00:05 Asia/Tokyo] The pgsql driver is not currently installed
[22-Apr-2025 21:00:05 Asia/Tokyo] PHP Fatal error:  Uncaught Zend_Db_Adapter_Exception: The pgsql driver is not currently installed in C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Pdo\Abstract.php:112
Stack trace:
#0 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Pdo\Pgsql.php(87): Zend_Db_Adapter_Pdo_Abstract->_connect()
#1 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Abstract.php(460): Zend_Db_Adapter_Pdo_Pgsql->_connect()
#2 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Pdo\Abstract.php(238): Zend_Db_Adapter_Abstract->query('INSERT INTO "s_...', Array)
#3 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Abstract.php(576): Zend_Db_Adapter_Pdo_Abstract->query('INSERT INTO "s_...', Array)
#4 C:\dev\Fdn.keigen\library\Zend\Log\Writer\Db.php(145): Zend_Db_Adapter_Abstract->insert('s_log_std', Array)
#5 C:\dev\Fdn.keigen\library\Zend\Log\Writer\Abstract.php(85): Zend_Log_Writer_Db->_write(Array)
#6 C:\dev\Fdn.keigen\library\Zend\Log.php(439): Zend_Log_Writer_Abstract->write(Array)
#7 C:\dev\Fdn.keigen\library\Msi\Sys\Log\Standard.php(79): Zend_Log->log('The pgsql drive...', 3, Array)
 in C:\dev\Fdn.keigen\library\Zend\Controller\Plugin\Broker.php on line 336
[22-Apr-2025 21:00:27 Asia/Tokyo] The pgsql driver is not currently installed
[22-Apr-2025 21:00:27 Asia/Tokyo] PHP Fatal error:  Uncaught Zend_Db_Adapter_Exception: The pgsql driver is not currently installed in C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Pdo\Abstract.php:112
Stack trace:
#0 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Pdo\Pgsql.php(87): Zend_Db_Adapter_Pdo_Abstract->_connect()
#1 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Abstract.php(460): Zend_Db_Adapter_Pdo_Pgsql->_connect()
#2 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Pdo\Abstract.php(238): Zend_Db_Adapter_Abstract->query('INSERT INTO "s_...', Array)
#3 C:\dev\Fdn.keigen\library\Zend\Db\Adapter\Abstract.php(576): Zend_Db_Adapter_Pdo_Abstract->query('INSERT INTO "s_...', Array)
#4 C:\dev\Fdn.keigen\library\Zend\Log\Writer\Db.php(145): Zend_Db_Adapter_Abstract->insert('s_log_std', Array)
#5 C:\dev\Fdn.keigen\library\Zend\Log\Writer\Abstract.php(85): Zend_Log_Writer_Db->_write(Array)
#6 C:\dev\Fdn.keigen\library\Zend\Log.php(439): Zend_Log_Writer_Abstract->write(Array)
#7 C:\dev\Fdn.keigen\library\Msi\Sys\Log\Standard.php(79): Zend_Log->log('The pgsql drive...', 3, Array)
 in C:\dev\Fdn.keigen\library\Zend\Controller\Plugin\Broker.php on line 336
[30-Apr-2025 14:26:26 Asia/Tokyo] PHP Parse error:  syntax error, unexpected '$oneRow' (T_VARIABLE) in C:\dev\Fdn.keigen\application\modules\juchu\controllers\Juchu\JuchuCustomerinfo.sano_k.php on line 3026
[30-Apr-2025 14:26:35 Asia/Tokyo] PHP Parse error:  syntax error, unexpected '$oneRow' (T_VARIABLE) in C:\dev\Fdn.keigen\application\modules\juchu\controllers\Juchu\JuchuCustomerinfo.sano_k.php on line 3026
[22-May-2025 14:05:13 Asia/Tokyo] PHP Warning:  count(): Parameter must be an array or an object that implements Countable in C:\dev\Fdn.keigen\library\Msi\Sys\Utils.php on line 6361
[22-May-2025 14:05:13 Asia/Tokyo] PHP Stack trace:
[22-May-2025 14:05:13 Asia/Tokyo] PHP   1. {main}() C:\dev\Fdn.keigen\public_dev\index.php:0
[22-May-2025 14:05:13 Asia/Tokyo] PHP   2. require() C:\dev\Fdn.keigen\public_dev\index.php:2
[22-May-2025 14:05:13 Asia/Tokyo] PHP   3. Zend_Application->run() C:\dev\Fdn.keigen\application\public_index.php:56
[22-May-2025 14:05:13 Asia/Tokyo] PHP   4. Bootstrap->run() C:\dev\Fdn.keigen\library\Zend\Application.php:366
[22-May-2025 14:05:13 Asia/Tokyo] PHP   5. Zend_Controller_Front->dispatch() C:\dev\Fdn.keigen\library\Zend\Application\Bootstrap\Bootstrap.php:97
[22-May-2025 14:05:13 Asia/Tokyo] PHP   6. Msi_Zend_Controller_DispatcherMsi->dispatch() C:\dev\Fdn.keigen\library\Zend\Controller\Front.php:954
[22-May-2025 14:05:13 Asia/Tokyo] PHP   7. Hachu_PdfController->dispatch() C:\dev\Fdn.keigen\library\Msi\Zend\Controller\DispatcherMsi.php:211
[22-May-2025 14:05:13 Asia/Tokyo] PHP   8. Hachu_PdfController->indexAction() C:\dev\Fdn.keigen\library\Zend\Controller\Action.php:516
[22-May-2025 14:05:13 Asia/Tokyo] PHP   9. App_HachuLib::getReportName() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:184
[22-May-2025 14:05:13 Asia/Tokyo] PHP  10. Msi_Sys_Utils::myCount() C:\dev\Fdn.keigen\application\library\App\HachuLib.sano_k.php:40
[22-May-2025 14:05:13 Asia/Tokyo] PHP  11. count() C:\dev\Fdn.keigen\library\Msi\Sys\Utils.php:6361
[22-May-2025 14:05:13 Asia/Tokyo] PHP Warning:  "continue" targeting switch is equivalent to "break". Did you mean to use "continue 2"? in C:\dev\Fdn.keigen\library\tcpdf\tcpdf.php on line 17792
[22-May-2025 14:05:13 Asia/Tokyo] PHP Stack trace:
[22-May-2025 14:05:13 Asia/Tokyo] PHP   1. {main}() C:\dev\Fdn.keigen\public_dev\index.php:0
[22-May-2025 14:05:13 Asia/Tokyo] PHP   2. require() C:\dev\Fdn.keigen\public_dev\index.php:2
[22-May-2025 14:05:13 Asia/Tokyo] PHP   3. Zend_Application->run() C:\dev\Fdn.keigen\application\public_index.php:56
[22-May-2025 14:05:13 Asia/Tokyo] PHP   4. Bootstrap->run() C:\dev\Fdn.keigen\library\Zend\Application.php:366
[22-May-2025 14:05:13 Asia/Tokyo] PHP   5. Zend_Controller_Front->dispatch() C:\dev\Fdn.keigen\library\Zend\Application\Bootstrap\Bootstrap.php:97
[22-May-2025 14:05:13 Asia/Tokyo] PHP   6. Msi_Zend_Controller_DispatcherMsi->dispatch() C:\dev\Fdn.keigen\library\Zend\Controller\Front.php:954
[22-May-2025 14:05:13 Asia/Tokyo] PHP   7. Hachu_PdfController->dispatch() C:\dev\Fdn.keigen\library\Msi\Zend\Controller\DispatcherMsi.php:211
[22-May-2025 14:05:13 Asia/Tokyo] PHP   8. Hachu_PdfController->indexAction() C:\dev\Fdn.keigen\library\Zend\Controller\Action.php:516
[22-May-2025 14:05:13 Asia/Tokyo] PHP   9. App_Pdf->__construct() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:184
[22-May-2025 14:05:13 Asia/Tokyo] PHP  10. spl_autoload_call() C:\dev\Fdn.keigen\application\library\App\Pdf.sano.php:55
[22-May-2025 14:05:13 Asia/Tokyo] PHP  11. Zend_Loader_Autoloader::autoload() C:\dev\Fdn.keigen\application\library\App\Pdf.sano.php:55
[22-May-2025 14:05:13 Asia/Tokyo] PHP  12. Zend_Loader_Autoloader->_autoload() C:\dev\Fdn.keigen\library\Zend\Loader\Autoloader.php:124
[22-May-2025 14:05:13 Asia/Tokyo] PHP  13. Zend_Loader::loadClass() C:\dev\Fdn.keigen\library\Zend\Loader\Autoloader.php:479
[22-May-2025 14:05:13 Asia/Tokyo] PHP  14. Zend_Loader::loadFile() C:\dev\Fdn.keigen\library\Zend\Loader.php:82
[22-May-2025 14:05:13 Asia/Tokyo] PHP  15. include_once() C:\dev\Fdn.keigen\library\Zend\Loader.php:134
[22-May-2025 14:05:13 Asia/Tokyo] PHP Warning:  count(): Parameter must be an array or an object that implements Countable in C:\dev\Fdn.keigen\library\Msi\Sys\Utils.php on line 6361
[22-May-2025 14:05:13 Asia/Tokyo] PHP Stack trace:
[22-May-2025 14:05:13 Asia/Tokyo] PHP   1. {main}() C:\dev\Fdn.keigen\public_dev\index.php:0
[22-May-2025 14:05:13 Asia/Tokyo] PHP   2. require() C:\dev\Fdn.keigen\public_dev\index.php:2
[22-May-2025 14:05:13 Asia/Tokyo] PHP   3. Zend_Application->run() C:\dev\Fdn.keigen\application\public_index.php:56
[22-May-2025 14:05:13 Asia/Tokyo] PHP   4. Bootstrap->run() C:\dev\Fdn.keigen\library\Zend\Application.php:366
[22-May-2025 14:05:13 Asia/Tokyo] PHP   5. Zend_Controller_Front->dispatch() C:\dev\Fdn.keigen\library\Zend\Application\Bootstrap\Bootstrap.php:97
[22-May-2025 14:05:13 Asia/Tokyo] PHP   6. Msi_Zend_Controller_DispatcherMsi->dispatch() C:\dev\Fdn.keigen\library\Zend\Controller\Front.php:954
[22-May-2025 14:05:13 Asia/Tokyo] PHP   7. Hachu_PdfController->dispatch() C:\dev\Fdn.keigen\library\Msi\Zend\Controller\DispatcherMsi.php:211
[22-May-2025 14:05:13 Asia/Tokyo] PHP   8. Hachu_PdfController->indexAction() C:\dev\Fdn.keigen\library\Zend\Controller\Action.php:516
[22-May-2025 14:05:13 Asia/Tokyo] PHP   9. Hachu_PdfController->outData() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:186
[22-May-2025 14:05:13 Asia/Tokyo] PHP  10. Hachu_PdfController->outData0209() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:354
[22-May-2025 14:05:13 Asia/Tokyo] PHP  11. App_Pdf->hachu_cmn_out() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:3099
[22-May-2025 14:05:13 Asia/Tokyo] PHP  12. App_Pdf->hachu_title_out() C:\dev\Fdn.keigen\application\library\App\Pdf.sano.php:744
[22-May-2025 14:05:13 Asia/Tokyo] PHP  13. Msi_Sys_Utils::myCount() C:\dev\Fdn.keigen\application\library\App\Pdf.sano.php:887
[22-May-2025 14:05:13 Asia/Tokyo] PHP  14. count() C:\dev\Fdn.keigen\library\Msi\Sys\Utils.php:6361
[22-May-2025 17:12:29 Asia/Tokyo] PHP Warning:  count(): Parameter must be an array or an object that implements Countable in C:\dev\Fdn.keigen\library\Msi\Sys\Utils.php on line 6361
[22-May-2025 17:12:29 Asia/Tokyo] PHP Stack trace:
[22-May-2025 17:12:29 Asia/Tokyo] PHP   1. {main}() C:\dev\Fdn.keigen\public_dev\index.php:0
[22-May-2025 17:12:29 Asia/Tokyo] PHP   2. require() C:\dev\Fdn.keigen\public_dev\index.php:2
[22-May-2025 17:12:29 Asia/Tokyo] PHP   3. Zend_Application->run() C:\dev\Fdn.keigen\application\public_index.php:56
[22-May-2025 17:12:29 Asia/Tokyo] PHP   4. Bootstrap->run() C:\dev\Fdn.keigen\library\Zend\Application.php:366
[22-May-2025 17:12:29 Asia/Tokyo] PHP   5. Zend_Controller_Front->dispatch() C:\dev\Fdn.keigen\library\Zend\Application\Bootstrap\Bootstrap.php:97
[22-May-2025 17:12:29 Asia/Tokyo] PHP   6. Msi_Zend_Controller_DispatcherMsi->dispatch() C:\dev\Fdn.keigen\library\Zend\Controller\Front.php:954
[22-May-2025 17:12:29 Asia/Tokyo] PHP   7. Hachu_PdfController->dispatch() C:\dev\Fdn.keigen\library\Msi\Zend\Controller\DispatcherMsi.php:211
[22-May-2025 17:12:29 Asia/Tokyo] PHP   8. Hachu_PdfController->indexAction() C:\dev\Fdn.keigen\library\Zend\Controller\Action.php:516
[22-May-2025 17:12:29 Asia/Tokyo] PHP   9. App_HachuLib::getReportName() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:184
[22-May-2025 17:12:29 Asia/Tokyo] PHP  10. Msi_Sys_Utils::myCount() C:\dev\Fdn.keigen\application\library\App\HachuLib.sano_k.php:40
[22-May-2025 17:12:29 Asia/Tokyo] PHP  11. count() C:\dev\Fdn.keigen\library\Msi\Sys\Utils.php:6361
[22-May-2025 17:12:29 Asia/Tokyo] PHP Warning:  "continue" targeting switch is equivalent to "break". Did you mean to use "continue 2"? in C:\dev\Fdn.keigen\library\tcpdf\tcpdf.php on line 17792
[22-May-2025 17:12:29 Asia/Tokyo] PHP Stack trace:
[22-May-2025 17:12:29 Asia/Tokyo] PHP   1. {main}() C:\dev\Fdn.keigen\public_dev\index.php:0
[22-May-2025 17:12:29 Asia/Tokyo] PHP   2. require() C:\dev\Fdn.keigen\public_dev\index.php:2
[22-May-2025 17:12:29 Asia/Tokyo] PHP   3. Zend_Application->run() C:\dev\Fdn.keigen\application\public_index.php:56
[22-May-2025 17:12:29 Asia/Tokyo] PHP   4. Bootstrap->run() C:\dev\Fdn.keigen\library\Zend\Application.php:366
[22-May-2025 17:12:29 Asia/Tokyo] PHP   5. Zend_Controller_Front->dispatch() C:\dev\Fdn.keigen\library\Zend\Application\Bootstrap\Bootstrap.php:97
[22-May-2025 17:12:29 Asia/Tokyo] PHP   6. Msi_Zend_Controller_DispatcherMsi->dispatch() C:\dev\Fdn.keigen\library\Zend\Controller\Front.php:954
[22-May-2025 17:12:29 Asia/Tokyo] PHP   7. Hachu_PdfController->dispatch() C:\dev\Fdn.keigen\library\Msi\Zend\Controller\DispatcherMsi.php:211
[22-May-2025 17:12:29 Asia/Tokyo] PHP   8. Hachu_PdfController->indexAction() C:\dev\Fdn.keigen\library\Zend\Controller\Action.php:516
[22-May-2025 17:12:29 Asia/Tokyo] PHP   9. App_Pdf->__construct() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:184
[22-May-2025 17:12:29 Asia/Tokyo] PHP  10. spl_autoload_call() C:\dev\Fdn.keigen\application\library\App\Pdf.sano.php:55
[22-May-2025 17:12:29 Asia/Tokyo] PHP  11. Zend_Loader_Autoloader::autoload() C:\dev\Fdn.keigen\application\library\App\Pdf.sano.php:55
[22-May-2025 17:12:29 Asia/Tokyo] PHP  12. Zend_Loader_Autoloader->_autoload() C:\dev\Fdn.keigen\library\Zend\Loader\Autoloader.php:124
[22-May-2025 17:12:29 Asia/Tokyo] PHP  13. Zend_Loader::loadClass() C:\dev\Fdn.keigen\library\Zend\Loader\Autoloader.php:479
[22-May-2025 17:12:29 Asia/Tokyo] PHP  14. Zend_Loader::loadFile() C:\dev\Fdn.keigen\library\Zend\Loader.php:82
[22-May-2025 17:12:29 Asia/Tokyo] PHP  15. include_once() C:\dev\Fdn.keigen\library\Zend\Loader.php:134
[22-May-2025 17:12:29 Asia/Tokyo] PHP Warning:  count(): Parameter must be an array or an object that implements Countable in C:\dev\Fdn.keigen\library\Msi\Sys\Utils.php on line 6361
[22-May-2025 17:12:29 Asia/Tokyo] PHP Stack trace:
[22-May-2025 17:12:29 Asia/Tokyo] PHP   1. {main}() C:\dev\Fdn.keigen\public_dev\index.php:0
[22-May-2025 17:12:29 Asia/Tokyo] PHP   2. require() C:\dev\Fdn.keigen\public_dev\index.php:2
[22-May-2025 17:12:29 Asia/Tokyo] PHP   3. Zend_Application->run() C:\dev\Fdn.keigen\application\public_index.php:56
[22-May-2025 17:12:29 Asia/Tokyo] PHP   4. Bootstrap->run() C:\dev\Fdn.keigen\library\Zend\Application.php:366
[22-May-2025 17:12:29 Asia/Tokyo] PHP   5. Zend_Controller_Front->dispatch() C:\dev\Fdn.keigen\library\Zend\Application\Bootstrap\Bootstrap.php:97
[22-May-2025 17:12:29 Asia/Tokyo] PHP   6. Msi_Zend_Controller_DispatcherMsi->dispatch() C:\dev\Fdn.keigen\library\Zend\Controller\Front.php:954
[22-May-2025 17:12:29 Asia/Tokyo] PHP   7. Hachu_PdfController->dispatch() C:\dev\Fdn.keigen\library\Msi\Zend\Controller\DispatcherMsi.php:211
[22-May-2025 17:12:29 Asia/Tokyo] PHP   8. Hachu_PdfController->indexAction() C:\dev\Fdn.keigen\library\Zend\Controller\Action.php:516
[22-May-2025 17:12:29 Asia/Tokyo] PHP   9. Hachu_PdfController->outData() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:186
[22-May-2025 17:12:29 Asia/Tokyo] PHP  10. Hachu_PdfController->outData0203_sub() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:308
[22-May-2025 17:12:29 Asia/Tokyo] PHP  11. App_Pdf->hachu_cmn_out() C:\dev\Fdn.keigen\application\modules\hachu\controllers\PdfController.sano.php:741
[22-May-2025 17:12:29 Asia/Tokyo] PHP  12. App_Pdf->hachu_title_out() C:\dev\Fdn.keigen\application\library\App\Pdf.sano.php:744
[22-May-2025 17:12:29 Asia/Tokyo] PHP  13. Msi_Sys_Utils::myCount() C:\dev\Fdn.keigen\application\library\App\Pdf.sano.php:887
[22-May-2025 17:12:29 Asia/Tokyo] PHP  14. count() C:\dev\Fdn.keigen\library\Msi\Sys\Utils.php:6361
[05-Jun-2025 11:49:09 Asia/Tokyo] PHP Fatal error:  Uncaught Msi_Sys_Exception_DbException: 無効なトランザクション状態です。 in C:\dev\Fdn.keigen\library\Msi\Sys\Db.php:432
Stack trace:
#0 C:\dev\Fdn.keigen\library\Msi\Sys\Db.php(744): Msi_Sys_Db->_error('Msi_Sys_Db::eas...', '[Msi_Sys_Db::ea...', '25P02')
#1 C:\dev\Fdn.keigen\application\models\DataMapper\LoginMst.php(54): Msi_Sys_Db->easySelect('SELECT *\n  FROM...', Array)
#2 C:\dev\Fdn.keigen\application\models\DataMapper\Abstract.php(227): DataMapper_LoginMst::find(Object(Msi_Sys_Db), Array)
#3 C:\dev\Fdn.keigen\application\library\App\AccCtrlAdapterAbst.php(112): DataMapper_Abstract::findOne(Object(Msi_Sys_Db), Array)
#4 C:\dev\Fdn.keigen\application\library\App\Utils.sano_k.php(522): App_AccCtrlAdapterAbst::callWithTantoCd('99999', 'canAccessSekoDa...', Array)
#5 C:\dev\Fdn.keigen\application\library\App\Utils.sano_k.php(909): App_Utils::canAccessSekoData(NULL, NULL)
#6 C:\dev\Fdn.keigen\application\library\App\Utils.sano_k.php(2663): App_Utils::getDefaultSekoNo('std')
#7 C:\dev\Fdn.keig in C:\dev\Fdn.keigen\library\Zend\Controller\Plugin\Broker.php on line 336
