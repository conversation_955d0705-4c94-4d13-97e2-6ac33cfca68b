<?php

/**
 * PDF 領収書
 *  cf. df1101Controller.php
 *
 * @category   App
 * @package    controllers\Saiken
 * <AUTHOR> Mihara
 * @since      2015/06/xx
 * @version    2019/05/xx mihara 軽減税率対応
 * @filesource 
 */

/**
 * PDF 領収書
 *
 * @category   App
 * @package    controllers\Saiken
 * <AUTHOR> Mihara
 * @since      2015/06/xx
 */
class Saiken_RyoshuPdf {
    /**
     * 領収書PDFファイルを返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx
     * @return array(PDFファイル名, 論理名)   PDFファイル名(生成した一時ファイル名)
     */
    public static function outputPdf($db, $data, $ikatsu_flg = false, $count = 0) {
        // 本番用
        $base_pdf1 = __DIR__ . '/../pdf_tmpl/ryoshu01.pdf';
        $base_pdf2 = __DIR__ . '/../pdf_tmpl/ryoshu01_note.pdf';
        // テスト用
        $base_pdf_ary[] = $base_pdf1;
        $base_pdf_ary[] = $base_pdf2;
        $title = '領収証'.$data['ryosyusho_no'].'_'.$data['hako_ymd'];
        $pdfObj = new App_Pdf($title);
        // 社判表示フラグ
        $syaban_flg = false;
        $kaisya_logo_flg = false;
        if (isset($data['mode'])) {
            $mode = $data['mode'];
        } else {
            $mode = 'normal';
        }
        foreach ($base_pdf_ary as $key => $base_pdf) {
            $pdfObj->addSourcePage($base_pdf);
            $pdfObj->set_default_minus_font_color('red');
            $pdfObj->set_default_minus_chartype('t');
            // デフォルトフォントサイズ
            $pdfObj->set_default_font_size(11);
            // 折線
            $pdfObj->write_line(array('x1' => 52, 'y1' => 307, 'x2' => 540, 'y2' => 307, 'width' => 0.1));
            $ryoshuRec_uc = DataMapper_RyosyushoHistory::findUC($db, array('uri_den_no' => $data['uri_den_no'], 'hako_count' => $data['hako_count']));
            $ryoshuRec = null;
            if(Msi_Sys_Utils::myCount($ryoshuRec_uc) > 0){
                $ryoshuRec = $ryoshuRec_uc[0];
            }
            $sekyuRec = DataMapper_SekyuSakiInfo::findRyosyuHako($db, array('seko_no' => $data['seko_no']));
            if($key === 0){
                if($count == 0){
                    // 送付先
                    // 供花・施行代金からの入金時は領収証送付先情報は表示しない
                    if(isset($sekyuRec) && $mode != 'daikinprint'){
                        // 窓あき封筒宛名
                        $seikyuSekyuInfo = DataMapper_SekyuSakiInfo::findUriagesekyu($db, array("uri_den_no" => $data['uri_den_no']));
                        $seikyuJuchuInfo = DataMapper_SekyuSakiInfo::findJuchusekyu($db, array("denpyo_no" => $data['juchu_den_no']));
                        if(Msi_Sys_Utils::myCount($seikyuSekyuInfo) > 0){
                            $pdfObj->write_string(array('x' => 85, 'y' => 34, 'width' => 222, 'height' => 9, 'font_size' => 10), '〒'.$seikyuSekyuInfo[0]['ryosyusyo_soufu_yubin_no']); // 郵便番号
                            $pdfObj->write_string(array('x' => 85, 'y' => 46, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['ryosyusyo_soufu_addr1']); // 住所1
                            $pdfObj->write_string(array('x' => 85, 'y' => 58, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['ryosyusyo_soufu_addr2']); // 住所2
                            $pdfObj->write_string(array('x' => 85, 'y' => 80, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['ryosyusyo_soufu_nm'].'　様'); // 請求送付先名
                        }else if(Msi_Sys_Utils::myCount($seikyuJuchuInfo) > 0){
                            $pdfObj->write_string(array('x' => 85, 'y' => 34, 'width' => 222, 'height' => 9, 'font_size' => 10), '〒'.$seikyuJuchuInfo[0]['ryosyusyo_soufu_yubin_no']); // 郵便番号
                            $pdfObj->write_string(array('x' => 85, 'y' => 46, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuJuchuInfo[0]['ryosyusyo_soufu_addr1']); // 住所1
                            $pdfObj->write_string(array('x' => 85, 'y' => 58, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuJuchuInfo[0]['ryosyusyo_soufu_addr2']); // 住所2
                            $pdfObj->write_string(array('x' => 85, 'y' => 80, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuJuchuInfo[0]['ryosyusyo_soufu_nm'].'　様'); // 請求送付先名
                        }
                    }
                }
            }
            // 内消費税
            $zei_prc = $ryoshuRec['zei_prc_std'] + $ryoshuRec['zei_prc_keigen'] + $ryoshuRec['zei_prc_5'] + $ryoshuRec['zei_prc_3'];
            // 領収証名義
            $ryosyu_meigi = "";
            if(isset($ryoshuRec['atena'])) {
                $ryosyu_meigi = $ryoshuRec['atena']."　様";
            }
            $pdfObj->write_string(array('x' => 415, 'y' => 341, 'width' => 114, 'height' => 15, 'align' => 'C', 'font_size' => 9), $ryoshuRec['ryosyu_no'].'-'.$ryoshuRec['ryosyu_no_sub']);  // 領収証番号
            $pdfObj->write_date(  array('x' => 415, 'y' => 356, 'width' => 114, 'height' => 15, 'align' => 'C', 'font_size' => 9), $ryoshuRec['hako_date_disp'], "Y年n月j日", "AUTO");         // 発行日
            $pdfObj->write_date(  array('x' =>  78, 'y' => 464, 'width' => 114, 'height' => 15, 'align' => 'L', 'font_size' =>10), $ryoshuRec['nyukin_date_disp'], "Y年n月j日", "gG", "AUTO"); // 発行日(入金日)
            $pdfObj->write_string(array('x' =>  80, 'y' => 385, 'width' => 225, 'height' => 15, 'align' => 'C', 'font_size' =>15), $ryosyu_meigi);  // 領収証名義
            $pdfObj->write_string(array('x' => 130, 'y' => 421, 'width' => 180, 'height' => 15, 'align' => 'C', 'font_size' => 14), static::_filterKingakuHyphen($ryoshuRec['gokei_prc']));  // 領収金額
            //$pdfObj->write_string(array('x' => 130, 'y' => 441, 'width' => 180, 'height' => 15, 'align' => 'R', 'font_size' => 9), "(内消費税)".static::_filterKingakuHyphen($zei_prc));  // 領収金額
            $pdfObj->write_string(array('x' =>  80, 'y' => 600, 'width' => 215, 'height' => 15), '但し、');
            if($ryoshuRec['pay_kbn'] == "4"){
                $pdfObj->write_string(array('x' => 115, 'y' => 600, 'width' => 215, 'height' => 15), 'クレジットカード利用');
                $pdfObj->write_strings(array('x' =>  80, 'y' => 615, 'width' => 230, 'height' => 48), $ryoshuRec['tadashikaki']);
            }else if($ryoshuRec['pay_kbn'] == "5"){
                $pdfObj->write_strings(array('x' =>  80, 'y' => 615, 'width' => 230, 'height' => 48), $ryoshuRec['tadashikaki']);
            }else{
                $tadashikaki = null;
                if(isset($ryoshuRec['tadashikaki']) && strlen($ryoshuRec['tadashikaki']) > 0){
                    $tadashikaki = $ryoshuRec['tadashikaki'];
                }
                $pdfObj->write_strings(array('x' =>  80, 'y' => 615, 'width' => 230, 'height' => 48), $tadashikaki);
            }
            if($key === 0){
                // 印紙表示チェック
                $inshiflg = self::inshiDispCheck($ryoshuRec);
                if($inshiflg){
                    // 「8551:印紙税納付税務署」取得
                    $inshi_type = DataMapper_CodeNmMst::findOne($db, array('code_kbn'=> '8551', 'kbn_value_cd' => $ryoshuRec['inshi_type_cd']));
                    if(Msi_Sys_Utils::myCount($inshi_type) > 0){
                        // 印紙画像取得
                        $gazo = DataMapper_GazoFileInfoMst::findOne($db, array('gazo_kbn' => 8, 'gazo_cd' => $inshi_type['kbn_value_cd_num']));
                        if(Msi_Sys_Utils::myCount($gazo) > 0){
                            $img = $db->readBlobCont($gazo['gazo_img']);
                            $pdfObj->write_image(array('x' => 400, 'y' => 395, 'width' => 52.734, 'height' => 57.331), $img);
                        }
                    }
                }
            }
            // 会社情報の表示
            $bumon_info = DataMapper_Bumon::findOne($db, array('bumon_cd' => $data['bumon_cd']));
            $touroku_number = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '6852', 'kbn_value_cd' => $data['bumon_cd']));
            $touroku_no = "";
            if(Msi_Sys_Utils::myCount($touroku_number) > 0){
                $touroku_no = $touroku_number['kbn_value_snm'];
            }
            if(Msi_Sys_Utils::myCount($bumon_info) > 0){
                $bumon_logo = $db->readBlobCont($bumon_info['f_free1']); // 部門ロゴ
                $pdfObj->write_image(array('x' => 355, 'y' => 620, 'width' => 182, 'height' => 50, 'font_size' => 12), $bumon_logo);
            }
            // オプション情報「10006」を取得
            $option = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '9750', 'kbn_value_cd' => '10006'));
            if(Msi_Sys_Utils::myCount($option) > 0 && $option['kbn_value_cd_num'] == 0){ // オプションが 0:出力しないの場合
                $pdfObj->write_string(array('x' =>355, 'y' => 678, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $touroku_no);
            }else{
                $pdfObj->write_string(array('x' =>355, 'y' => 678, 'width' => 500, 'height' => 11, 'font_size' => 9), $bumon_info['char_free2']);
                $pdfObj->write_string(array('x' =>355, 'y' => 689, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $touroku_no);
            }
            $syaban_flg = false;
            if($key === 1){
                // QRコード
                $qr_txt = $ryoshuRec['nyukin_den_no'];
                $qr_txt = mb_convert_encoding( $qr_txt, 'SJIS-win', 'UTF-8' );
                $qrOpt = array( 'style' => array('border'=>false) );
                $pdfObj->qrcode_out($qr_txt, 50, 705, 80, $qrOpt);                
            }
            // オプション情報「10008」を取得
            $option = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '9750', 'kbn_value_cd' => '10008'));
            if((Msi_Sys_Utils::myCount($option) > 0 && $option['kbn_value_cd_num'] == 0) // オプションが 0：通常制御の場合
                        || (isset($data['uchiwake_flg']) && $data['uchiwake_flg'])){     // 供花供物の場合
                // 内訳表示
                static::_outZeiUchiwakeFromRyoshu(null, $data['uri_den_no'], $data['hako_count'], $pdfObj, 110, $data['juchu_den_no']);
            }
            // 文言追記
            self::addText1($pdfObj, $key, $ryoshuRec, $ikatsu_flg);
            // 請求情報
            self::addSekyuInfo($pdfObj, $db, $ryoshuRec, true);
        }
        $buf = $pdfObj->fileOutBuf();
        $temp_file = Msi_Sys_Utils::tempnam();
        Msi_Sys_Utils::put_contents($temp_file, $buf);
        return array($temp_file, $title . '.pdf');
    }
    /**
     * 領収書PDFファイルを返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx
     * @return array(PDFファイル名, 論理名)   PDFファイル名(生成した一時ファイル名)
     */
    public static function outputPdfSeikyu($db, $data, $ikatsu_flg = false, $count = 0) {
        // 本番用
        $base_pdf1 = __DIR__ . '/../pdf_tmpl/ryoshu01.pdf';
        $base_pdf2 = __DIR__ . '/../pdf_tmpl/ryoshu01_note.pdf';
        $base_pdf_ary[] = $base_pdf1;
        $base_pdf_ary[] = $base_pdf2;
        $title = '領収書'.$data['ryosyusho_no'].'_'.$data['hako_ymd'];
        $pdfObj = new App_Pdf($title);
        // 社判表示フラグ
        $syaban_flg = false;
        $kaisya_logo_flg = false;
        if (isset($data['mode'])) {
            $mode = $data['mode'];
        } else {
            $mode = 'normal';
        }
        foreach ($base_pdf_ary as $key => $base_pdf) {
            $pdfObj->addSourcePage($base_pdf);
            $pdfObj->set_default_minus_font_color('red');
            $pdfObj->set_default_minus_chartype('t');
            // デフォルトフォントサイズ
            $pdfObj->set_default_font_size(11);
            // 折線
            $pdfObj->write_line(array('x1' => 52, 'y1' => 307, 'x2' => 540, 'y2' => 307, 'width' => 0.1));
            $ryoshuRec = DataMapper_RyosyushoHistory::findOne($db, array('uri_den_no' => $data['seikyu_den_no'], 'hako_count' => $data['hako_count']));
            $sekyuRec = DataMapper_SekyuSakiInfo::findRyosyuHako2($db, array('seko_no' => $data['seko_no'], 'seikyu_den_no' => $data['seikyu_den_no']));
            if($key === 0){
                if(isset($ryoshuRec['data_kbn']) && $ryoshuRec['data_kbn'] == 3){
                    $sekyuRec = DataMapper_SekyuSakiInfo::findRyosyuHakoAfter($db, array('seko_no' => $data['seko_no'], 'seikyu_den_no' => $data['seikyu_den_no']));
                }
                // 一括発行の時は送付先は出力しない
                if(!$ikatsu_flg || ($ikatsu_flg && $count == 0)){
                    if($count == 0){
                        // 供花・施行代金からの入金時は領収証送付先情報は表示しない
                        if(isset($data['ryosyu_check']) && $data['ryosyu_check'] == 1 && $mode != 'daikinprint'){
                            // 窓あき封筒宛名
                            $seikyuSekyuInfo = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array("seikyu_den_no" => $data['seikyu_den_no']));
                            $pdfObj->write_string(array('x' => 85, 'y' => 34, 'width' => 222, 'height' => 9, 'font_size' => 10), '〒'.$seikyuSekyuInfo[0]['ryosyusyo_soufu_yubin_no']); // 郵便番号
                            $pdfObj->write_string(array('x' => 85, 'y' => 46, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['ryosyusyo_soufu_addr1']); // 住所1
                            $pdfObj->write_string(array('x' => 85, 'y' => 58, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['ryosyusyo_soufu_addr2']); // 住所2
                            $pdfObj->write_string(array('x' => 85, 'y' => 80, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['ryosyusyo_soufu_nm'].'　様'); // 請求送付先名
                        }else{
                            // 送付先
                            if(isset($sekyuRec) && $mode != 'daikinprint'){
                                // 窓あき封筒宛名
                                $seikyuSekyuInfo = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array("seikyu_den_no" => $data['seikyu_den_no']));
                                $pdfObj->write_string(array('x' => 85, 'y' => 34, 'width' => 222, 'height' => 9, 'font_size' => 10), '〒'.$seikyuSekyuInfo[0]['ryosyusyo_soufu_yubin_no']); // 郵便番号
                                $pdfObj->write_string(array('x' => 85, 'y' => 46, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['ryosyusyo_soufu_addr1']); // 住所1
                                $pdfObj->write_string(array('x' => 85, 'y' => 58, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['ryosyusyo_soufu_addr2']); // 住所2
                                $pdfObj->write_string(array('x' => 85, 'y' => 80, 'width' => 222, 'height' => 9, 'font_size' => 10), $seikyuSekyuInfo[0]['ryosyusyo_soufu_nm'].'　様'); // 請求送付先名
                            }
                        }                        
                    }
                }
            }
            // 内消費税
            $zei_prc = $ryoshuRec['zei_prc_std'] + $ryoshuRec['zei_prc_keigen'] + $ryoshuRec['zei_prc_5'] + $ryoshuRec['zei_prc_3'];
            // 領収証名義
            $ryosyu_meigi = "";
            if(isset($data['ryosyu_meigi'])) {
                $ryosyu_meigi = $data['ryosyu_meigi']."　様";
            }else{
                $ryosyu_meigi = $ryoshuRec['atena']."　様";
            }
            $pdfObj->write_string(array('x' => 415, 'y' => 341, 'width' => 114, 'height' => 15, 'align' => 'C', 'font_size' => 9), $ryoshuRec['ryosyu_no'].'-'.$ryoshuRec['ryosyu_no_sub']);  // 領収証番号
            $pdfObj->write_date(  array('x' => 415, 'y' => 356, 'width' => 114, 'height' => 15, 'align' => 'C', 'font_size' => 9), $ryoshuRec['hako_date_disp'], "Y年n月j日", "AUTO");         // 発行日
            $pdfObj->write_date(  array('x' =>  78, 'y' => 464, 'width' => 114, 'height' => 15, 'align' => 'L', 'font_size' =>10), $ryoshuRec['nyukin_date_disp'], "Y年n月j日", "gG", "AUTO"); // 発行日(入金日)
            $pdfObj->write_string(array('x' =>  80, 'y' => 385, 'width' => 225, 'height' => 15, 'align' => 'C', 'font_size' =>15), $ryosyu_meigi);  // 領収証名義
            $pdfObj->write_string(array('x' => 130, 'y' => 421, 'width' => 180, 'height' => 15, 'align' => 'C', 'font_size' => 14), static::_filterKingakuHyphen($ryoshuRec['gokei_prc']));  // 領収金額
            //$pdfObj->write_string(array('x' => 130, 'y' => 441, 'width' => 180, 'height' => 15, 'align' => 'R', 'font_size' => 9), "(内消費税)".static::_filterKingakuHyphen($zei_prc));  // 領収金額
            $pdfObj->write_string(array('x' =>  80, 'y' => 600, 'width' => 215, 'height' => 15), '但し、');
            if($ryoshuRec['pay_kbn'] == "4"){
                $pdfObj->write_string(array('x' => 115, 'y' => 600, 'width' => 215, 'height' => 15), 'クレジットカード利用');
                $pdfObj->write_strings(array('x' =>  80, 'y' => 615, 'width' => 230, 'height' => 72), $ryoshuRec['tadashikaki']);
            }else if($ryoshuRec['pay_kbn'] == "5"){
                $pdfObj->write_strings(array('x' =>  80, 'y' => 615, 'width' => 230, 'height' => 72), $ryoshuRec['tadashikaki']);
            }else{
                $tadashikaki = null;
                if(isset($ryoshuRec['tadashikaki']) && strlen($ryoshuRec['tadashikaki']) > 0){
                    $tadashikaki = $ryoshuRec['tadashikaki'];
                }
                $pdfObj->write_strings(array('x' =>  80, 'y' => 615, 'width' => 230, 'height' => 72), $tadashikaki);
            }
            // 「8551:印紙税納付税務署」取得
            $inshi_type = DataMapper_CodeNmMst::findOne($db, array('code_kbn'=> '8551', 'kbn_value_cd' => $ryoshuRec['inshi_type_cd']));
            if(Msi_Sys_Utils::myCount($inshi_type) > 0){
                // 備考取得
                $biko = explode(',',$inshi_type['biko']);
                // 承認番号取得
                $syonin_no = end($biko);
                $pdfObj->write_string(array('x' =>466, 'y' => 320, 'width' => 60, 'height' => 11, 'align' => 'C', 'font_size' => 9), $syonin_no);                
            }
            if($key === 0){
                // 印紙表示チェック
                $inshiflg = self::inshiDispCheck($ryoshuRec);
                if($inshiflg){
                    if(Msi_Sys_Utils::myCount($inshi_type) > 0){
                        // 印紙画像取得
                        $gazo = DataMapper_GazoFileInfoMst::findOne($db, array('gazo_kbn' => 8, 'gazo_cd' => $inshi_type['kbn_value_cd_num']));
                        if(Msi_Sys_Utils::myCount($gazo) > 0){
                            $img = $db->readBlobCont($gazo['gazo_img']);
                            $pdfObj->write_image(array('x' => 400, 'y' => 395, 'width' => 52.734, 'height' => 57.331), $img);
                        }
                    }
                }
            }
            // 会社情報の表示
            $bumon_info = array();
            if($ryoshuRec['inshi_type_cd'] == '1' || $ryoshuRec['inshi_type_cd'] == '2'){
                $bumon_gojo = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '9754', 'kbn_value_cd' => $data['bumon_cd']));
                if(Msi_Sys_Utils::myCount($bumon_gojo) > 0){
                    $bumon_info = DataMapper_Bumon::findOne($db, array('bumon_cd' => $bumon_gojo['kbn_value_lnm']));
                }
            }else{
                $bumon_info = DataMapper_Bumon::findOne($db, array('bumon_cd' => $data['bumon_cd']));
            }
            $touroku_number = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '6852', 'kbn_value_cd' => $data['bumon_cd']));
            $touroku_no = "";
            if(Msi_Sys_Utils::myCount($touroku_number) > 0){
                $touroku_no = $touroku_number['kbn_value_snm'];
            }
            // オプション情報「10006」を取得
            $option = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '9750', 'kbn_value_cd' => '10006'));
            if(Msi_Sys_Utils::myCount($bumon_info) > 0){
                if(isset($bumon_info['f_free1']) && strlen($bumon_info['f_free1']) > 0){
                    $bumon_logo = $db->readBlobCont($bumon_info['f_free1']); // 部門ロゴ
                    $pdfObj->write_image(array('x' => 355, 'y' => 620, 'width' => 182, 'height' => 50, 'font_size' => 12), $bumon_logo);
                }
                if(Msi_Sys_Utils::myCount($option) > 0 && $option['kbn_value_cd_num'] == 1){ // オプションが 1:出力するの場合
                    $pdfObj->write_string(array('x' =>355, 'y' => 678, 'width' => 500, 'height' => 11, 'font_size' => 9), $bumon_info['char_free2']);
                }
            }
            if(Msi_Sys_Utils::myCount($option) > 0 && $option['kbn_value_cd_num'] == 0){ // オプションが 0:出力しないの場合
                $pdfObj->write_string(array('x' =>355, 'y' => 678, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $touroku_no);
            }else{
                $pdfObj->write_string(array('x' =>355, 'y' => 689, 'width' => 500, 'height' => 11, 'font_size' => 9), '登録番号　' . $touroku_no);
            }
            $syaban_flg = false;
            if($key === 1){
                // QRコード
                $qr_txt = $ryoshuRec['nyukin_den_no'];
                $qr_txt = mb_convert_encoding( $qr_txt, 'SJIS-win', 'UTF-8' );
                $qrOpt = array( 'style' => array('border'=>false) );
                $pdfObj->qrcode_out($qr_txt, 50, 705, 80, $qrOpt);                
            }
            // オプション情報「10008」を取得
            $option = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '9750', 'kbn_value_cd' => '10008'));
            if($ryoshuRec['data_kbn'] == 4){
                // 内訳表示
                static::_outZeiUchiwakeFromRyoshu($data['seikyu_den_no'], null,  $data['hako_count'], $pdfObj, 110);
            }else if((Msi_Sys_Utils::myCount($option) > 0 && $option['kbn_value_cd_num'] == 0)  // オプションが 0：通常制御の場合
                        || (isset($data['uchiwake_flg']) && $data['uchiwake_flg'])){            // 供花供物の場合
                // 内訳表示
                static::_outZeiUchiwakeFromRyoshu($data['seikyu_den_no'], null,  $data['hako_count'], $pdfObj, 110);
            }
            // 文言追記
            self::addText2($pdfObj, $key, $ryoshuRec, $ikatsu_flg, $count);
            // 請求情報
            self::addSekyuInfo($pdfObj, $db, $ryoshuRec);
        }
        $buf = $pdfObj->fileOutBuf();
        $temp_file = Msi_Sys_Utils::tempnam();
        Msi_Sys_Utils::put_contents($temp_file, $buf);
        return array($temp_file, $title . '.pdf');
    }
    /**
     * 請求情報
     * 
     * @param type $pdfObj
     * @param type $ryoshuRec
     */
    private function addSekyuInfo($pdfObj, $db, $ryoshuRec, $flg = false){
        $sekyu_den_no = $ryoshuRec['uri_den_no'];
        $seko_no      = $ryoshuRec['seko_no'];
        $data_kbn     = $ryoshuRec['data_kbn'];
        $moushi_kbn   = null;
        if($data_kbn != '3'){
            $moushi_kbn = App_ClsSekoKanri::getMoushiKbn($db, $seko_no);
        }
        
        // 日程情報取得
        $code_kbn  = '0670';
        $sekoNitei = array();
        $sougi_ymd = "";
        // ご利用日取得
        if($data_kbn == '1' || ($moushi_kbn == '1' && $data_kbn == '4')){
            // 葬儀、供花は葬儀日
            $sekoKihon = DataMapper_SekoKihon::findOne($db, array('seko_no' =>  $seko_no));
            if(Msi_Sys_Utils::myCount($sekoKihon) > 0){
                $sougi_ymd = $sekoKihon['sougi_ymd_ex'];
            }
            // 葬儀、供花は葬儀告別式場所
            $nitei_kbn = 11;
            $sekoNitei = DataMapper_SekoNitei::findOne($db, array('seko_no' =>  $seko_no, 'nitei_kbn' => $nitei_kbn));
        }else if($data_kbn == '2' || ($moushi_kbn == '2' && $data_kbn == '4')){
            // 法事、供花は施行日
            $nitei_kbn = 0;
            $sekoNitei = DataMapper_SekoNiteiHouji::findOne($db, array('seko_no' =>  $seko_no, 'nitei_kbn' => $nitei_kbn));
            if(Msi_Sys_Utils::myCount($sekoNitei) > 0){
                $sougi_ymd = $sekoNitei['nitei_time_date'];
            }
            // 法事、供花は法要場所
            $nitei_kbn = 1;
            $sekoNitei = DataMapper_SekoNiteiHouji::findOne($db, array('seko_no' =>  $seko_no, 'nitei_kbn' => $nitei_kbn));
            $code_kbn  = '0960';
        }else if($data_kbn == '3'){
            // アフターは納品日
            $seikyu = DataMapper_SeikyuDenpyo::findOne($db, array('seikyu_den_no' => $sekyu_den_no));
            if(Msi_Sys_Utils::myCount($seikyu) > 0){
                $sougi_ymd = $seikyu['nonyu_ymd'];
            }
        }
        // ご利用場所取得
        if(Msi_Sys_Utils::myCount($sekoNitei) > 0){
            $basho_nm  = $sekoNitei['basho_nm'];
            $basho_kbn = $sekoNitei['basho_kbn'];
            if($basho_kbn == 0){
                // 自宅
                $codeNm = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => $code_kbn, 'kbn_value_cd_num' => $basho_kbn));
                if(Msi_Sys_Utils::myCount($codeNm) > 0){
                    $basho_nm = $codeNm['kbn_value_lnm'];
                }
            }
        }
        $y = 492;
        $add = 14;
        if($flg){
            // 預かり証モード
            $pdfObj->write_string(array('x' => 360, 'y' => $y, 'width' => 100, 'height' => 9), '受付番号');
            $pdfObj->write_string(array('x' => 425, 'y' => $y, 'width' => 145, 'height' => 9), $seko_no);
            $y += $add;
            $pdfObj->write_string(array('x' => 360, 'y' => $y, 'width' => 100, 'height' => 9), 'ご利用日');
            $pdfObj->write_string(array('x' => 425, 'y' => $y, 'width' => 145, 'height' => 9), $sougi_ymd);
            $y += $add;
            $pdfObj->write_string(array('x' => 360, 'y' => $y, 'width' => 100, 'height' => 9), 'ご利用場所');
            $pdfObj->write_string(array('x' => 425, 'y' => $y, 'width' => 145, 'height' => 9), $basho_nm);
        }else{
            $pdfObj->write_string(array('x' => 360, 'y' => $y, 'width' => 100, 'height' => 9), '請求番号');
            $pdfObj->write_string(array('x' => 425, 'y' => $y, 'width' => 145, 'height' => 9), $sekyu_den_no);
            $y += $add;
            $pdfObj->write_string(array('x' => 360, 'y' => $y, 'width' => 100, 'height' => 9), 'ご利用日');
            $pdfObj->write_string(array('x' => 425, 'y' => $y, 'width' => 145, 'height' => 9), $sougi_ymd);
            $y += $add;
            if($data_kbn != 3){
                $pdfObj->write_string(array('x' => 360, 'y' => $y, 'width' => 100, 'height' => 9), 'ご利用場所');
                $pdfObj->write_string(array('x' => 425, 'y' => $y, 'width' => 145, 'height' => 9), $basho_nm);
                $y += $add;
                $pdfObj->write_string(array('x' => 360, 'y' => $y, 'width' => 100, 'height' => 9), '受付番号');
                $pdfObj->write_string(array('x' => 425, 'y' => $y, 'width' => 145, 'height' => 9), $seko_no);
                $y += $add;
            }
        }
    }
    /**
     * 預かり証文言追加
     * 
     * @param type $pdfObj
     * @param type $ryoshuRec
     * @param type $ikatsu_flg
     */
    private function addText1($pdfObj, $key, $ryoshuRec, $ikatsu_flg){
        // 追加文言
        // 再発行文字印刷区分
        $sai_hako = "";
        if(!$ikatsu_flg){
            if(isset($ryoshuRec['disp_kbn']) && $ryoshuRec['disp_kbn'] == '1'){
                $sai_hako = "【再発行】";
            }
        }
        $pdfObj->write_string(array('x' => 10, 'y' => 344.5, 'width' => 227, 'height' => 9, 'font_size' => 17.5, 'align' => 'R'), $sai_hako.'預　か　り　証');
        if($key == 0){
            $pdfObj->write_string(array('x' => 305, 'y' => 160, 'width' => 260, 'height' => 9, 'font_size' => 14, 'align' => 'C'), '預かり証のご案内');
            $pdfObj->write_string(array('x' => 305, 'y' => 185, 'width' => 260, 'height' => 9, 'font_size' => 9), 'このたびは、下記の代金をご入金頂き誠に有難うございます。');
            $pdfObj->write_string(array('x' => 305, 'y' => 195, 'width' => 260, 'height' => 9, 'font_size' => 9), '預かり証をお送りさせていただきますので、 宜しくご査収の程');
            $pdfObj->write_string(array('x' => 305, 'y' => 205, 'width' => 260, 'height' => 9, 'font_size' => 9), 'お願い申し上げます。');
            $pdfObj->write_string(array('x' => 305, 'y' => 215, 'width' => 260, 'height' => 9, 'font_size' => 9), '尚、ご不明な点などございましたら、ご遠慮なく');
            $pdfObj->write_string(array('x' => 305, 'y' => 225, 'width' => 260, 'height' => 9, 'font_size' => 9), 'お申し付けくださいませ。');
        }
        if($ryoshuRec['furikomi_prc'] != 0){
            $pdfObj->write_string(array('x' => 78, 'y' => 574, 'width' => 250, 'height' => 11, 'font_size' =>10), "銀行振込にて上記金額をお預りいたしました。");
        }else if($ryoshuRec['credit_prc'] != 0){
            $pdfObj->write_string(array('x' => 78, 'y' => 574, 'width' => 250, 'height' => 11, 'font_size' =>10), "クレジットカード支払いにて上記金額をお預りいたしました。");
        }else{
            $pdfObj->write_string(array('x' => 78, 'y' => 574, 'width' => 250, 'height' => 11, 'font_size' =>10), "上記金額をお預りいたしました。");
        }
        $pdfObj->write_string(array('x' => 78, 'y' => 585, 'width' => 250, 'height' => 11, 'font_size' => 9), "※金額訂正された預かり証は無効です");
        $pdfObj->write_string(array('x' => 78, 'y' => 686, 'width' => 228, 'height' => 11, 'font_size' => 8), "※領収書の人名用漢字はシステムの関係上いくつかの旧字体が");
        $pdfObj->write_string(array('x' => 78, 'y' => 696, 'width' => 228, 'height' => 11, 'font_size' => 8), "　常用漢字表記となります。ご容赦下さい。");
    }
    /**
     * 領収証文言追加
     * 
     * @param type $pdfObj
     * @param type $ryoshuRec   領収証情報
     * @param type $ikatsu_flg  一括発行
     * @param type $count       一括発行カウント
     */
    private function addText2($pdfObj, $key, $ryoshuRec, $ikatsu_flg, $count){
        // 追加文言
        // 再発行文字印刷区分
        $sai_hako = "";
        if(!$ikatsu_flg){
            if(isset($ryoshuRec['disp_kbn']) && $ryoshuRec['disp_kbn'] == '1'){
                $sai_hako = "【再発行】";
            }
        }
        $pdfObj->write_string(array('x' => 10, 'y' => 344.5, 'width' => 227, 'height' => 9, 'font_size' => 17.5, 'align' => 'R'), $sai_hako.'領　収　書');
        if($key == 0 && $count == 0){
            $pdfObj->write_string(array('x' => 305, 'y' => 160, 'width' => 260, 'height' => 9, 'font_size' => 14, 'align' => 'C'), '領収書のご案内');
            $pdfObj->write_string(array('x' => 305, 'y' => 185, 'width' => 260, 'height' => 9, 'font_size' => 9), 'このたびは、下記の代金をご入金頂き誠に有難うございます。');
            $pdfObj->write_string(array('x' => 305, 'y' => 195, 'width' => 260, 'height' => 9, 'font_size' => 9), '領収書をお送りさせていただきますので、 宜しくご査収の程');
            $pdfObj->write_string(array('x' => 305, 'y' => 205, 'width' => 260, 'height' => 9, 'font_size' => 9), 'お願い申し上げます。');
            $pdfObj->write_string(array('x' => 305, 'y' => 215, 'width' => 260, 'height' => 9, 'font_size' => 9), '尚、ご不明な点などございましたら、ご遠慮なく');
            $pdfObj->write_string(array('x' => 305, 'y' => 225, 'width' => 260, 'height' => 9, 'font_size' => 9), 'お申し付けくださいませ。');
        }
        if($ryoshuRec['furikomi_prc'] != 0){
            $pdfObj->write_string(array('x' => 78, 'y' => 574, 'width' => 250, 'height' => 11, 'font_size' =>10), "銀行振込にて上記金額を領収いたしました。");
        }else if($ryoshuRec['credit_prc'] != 0){
            $pdfObj->write_string(array('x' => 78, 'y' => 574, 'width' => 250, 'height' => 11, 'font_size' =>10), "クレジットカード支払いにて上記金額を領収いたしました。");
        }else{
            $pdfObj->write_string(array('x' => 78, 'y' => 574, 'width' => 250, 'height' => 11, 'font_size' =>10), "上記金額を領収いたしました。");
        }
        $pdfObj->write_string(array('x' => 78, 'y' => 585, 'width' => 250, 'height' => 11, 'font_size' => 9), "※金額訂正された領収書は無効です");
        $pdfObj->write_string(array('x' => 78, 'y' => 686, 'width' => 228, 'height' => 11, 'font_size' => 8), "※領収書の人名用漢字はシステムの関係上いくつかの旧字体が");
        $pdfObj->write_string(array('x' => 78, 'y' => 696, 'width' => 228, 'height' => 11, 'font_size' => 8), "　常用漢字表記となります。ご容赦下さい。");
    }
    /**
     * 
     * 請求伝票のインポート区分を取得
     * 
     * @param type $db
     * @param type $seikyu_den_no
     * @return type
     */
    private function getImportKbn($db, $seikyu_den_no){
        $select = $db->easySelOne( <<< END_OF_SQL
    SELECT 
      d_import_kbn
    FROM seikyu_denpyo sd
    WHERE sd.seikyu_den_no = :seikyu_den_no
END_OF_SQL
            , array('seikyu_den_no' => $seikyu_den_no) 
        );
        return $select['d_import_kbn'];
    }
    /**
     * 領収書PDFファイルに(再発行)上書きした PDF を返す
     *
     * <AUTHOR> Mihara
     * @since  2015/06/xx
     * @params string $filename
     * @return string  PDFファイル名(生成した一時ファイル名)
     */
    public static function pdfReissue($filename) {
        $pdfObj = new App_Pdf('dummy');
        $pdfObj->addSourcePage($filename);

        foreach (array(0, 407) as $y_d) {
            /*
              $pdfObj->write_string( array('x'=>450, 'y'=>128+$y_d, 'width'=>75, 'height'=>15, 'font_size'=> 13, 'align'=>'R'),
              '(再発行)' );
             */
            $pdfObj->set_default_color('gray');
            $pdfObj->write_string(array('x' => 200, 'y' => 150 + $y_d, 'width' => 200, 'height' => 100, 'font_size' => 80, 'align' => 'C'), '(無効)');
        }

        $buf = $pdfObj->fileOutBuf();
        $temp_file = Msi_Sys_Utils::tempnam();
        Msi_Sys_Utils::put_contents($temp_file, $buf);

        return $temp_file;
    }

    /**
     * 内訳金額データを表示用に変換して返す
     *
     * <AUTHOR> Mihara
     * @since  2015/06/xx
     * @params integer   $num
     * @return string    表示用文字列
     */
    protected static function _filterKingakuUchi($num) {
        $yen = mb_convert_encoding(pack("H*", "FFE5"), "UTF-8", "UTF-16"); // 00A5:yen, FFE5:full-width yen
        $str = sprintf('%s%s-', $yen, Msi_Sys_Utils::filterComma($num));
        // $str = Msi_Sys_Utils::filterComma($num);
        return $str;
    }

    /**
     * 合計金額データを表示用に変換して返す
     *
     * <AUTHOR> Mihara
     * @since  2015/06/xx
     * @params integer   $num
     * @return string    表示用文字列
     */
    protected static function _filterKingakuSum($num) {
        $yen = mb_convert_encoding(pack("H*", "FFE5"), "UTF-8", "UTF-16"); // 00A5:yen, FFE5:full-width yen
        $trail = mb_convert_encoding(pack("H*", "FF0D"), "UTF-8", "UTF-16"); // 002D:hyphen, FF0D:full-width hyphen
        $str = sprintf('　　　　　　　　　　%s%s%s', $yen, $num, $trail);
        $str = mb_convert_kana($str, "A", 'UTF-8'); // A:半角英数字を全角英数字に変換する
        $str = mb_substr($str, -10, 10, 'UTF-8'); // 末尾10文字を取得
        return $str;
    }

    static $_cacheUchiwake = array(); // 消費税金額内訳キャッシュ

    /**
     * 消費税金額の内訳を出力する
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @params string   $seikyu_den_no
     * @params string   $uri_den_no
     * @params integer  $hako_count
     * @param App_Pdf   $pdfObj
     * @param float     $y_d    y座標移動量
     * @params boolean  $ikatsu_flg
     * @params integer  $count
     * @params string   $juchu_den_no
     * @return void
     */
    protected static function _outZeiUchiwakeFromRyoshu($seikyu_den_no ,$uri_den_no, $hako_count, $pdfObj, $y_d, $juchu_den_no = null)
    {
        $db = Msi_Sys_DbManager::getMyDb();
        if(isset($juchu_den_no)){
            $key = sprintf("%s-%s", $juchu_den_no, $hako_count);
        }else if(isset($seikyu_den_no)){
            $key = sprintf("%s-%s", $seikyu_den_no, $hako_count);
        }else{
            $key = sprintf("%s-%s", $uri_den_no, $hako_count);
        }
        // 表示位置
        $optAttr = array( 'left'   => 100,
                          'top'    => 335 + $y_d,
                          'width'  => 210, // 200, // 140,
                          'height' => 10,
                          'fontSize' => 9);
        if ( !array_key_exists($key, static::$_cacheUchiwake) ) { 
            if(isset($seikyu_den_no)){
                $aUchiwake = App_KeigenUtils::getSeikyuShohizeiEasyRyoshuUchiwake($db, $seikyu_den_no, $hako_count);
                if(Msi_Sys_Utils::myCount($aUchiwake) > 0){
                    static::$_cacheUchiwake[ $key ] = $aUchiwake;
                }
            }else{
                static::$_cacheUchiwake[ $key ] = App_KeigenUtils::getSeikyuShohizeiEasy4Uc($db, $uri_den_no, $hako_count, $juchu_den_no);
            }
        }
        $aUchiwake = static::$_cacheUchiwake[ $key ];

        App_KeigenPdfUtils::outZeiUchiwakeRyosyu($pdfObj, $aUchiwake, $optAttr);
    }

    /**
     * 消費税金額の内訳を出力する
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @param App_Pdf  $pdfObj
     * @param float    $y_d    y座標移動量
     * @return void
     */
    protected static function _outZeiUchiwake($uridenRec, $pdfObj, $y_d)
    {
        $db = Msi_Sys_DbManager::getMyDb();
        // $seko_no     = $uridenRec['seko_no'];
        // $seko_no_sub = $uridenRec['seko_no_sub'];
        // $data_kbn    = $uridenRec['data_kbn'];
        $uri_den_no = $uridenRec['uri_den_no'];

        // $key = sprintf("%s-%s-%s", $seko_no, $seko_no_sub, $data_kbn);
        $key = sprintf("%s", $uri_den_no);
        if ( !array_key_exists($key, static::$_cacheUchiwake) ) {
            static::$_cacheUchiwake[ $key ] = App_KeigenUtils::getSeikyuShohizeiEasy2($db, $uri_den_no);
        }
        $aUchiwake = static::$_cacheUchiwake[ $key ];

        $optAttr = array( 'left'   => 95,
                          'top'    => 335 + $y_d,
                          'width'  => 200, // 200, // 140,
                          'height' => 10 );

        App_KeigenPdfUtils::outZeiUchiwake02($pdfObj, $aUchiwake, $optAttr);

        // $pdfObj->test_line_out(600, 1000);  // mesh for DEV
    }

    static $_sougi_ymd = null; // 消費税基準日
    static $_is_keigen_ctxt = false; // 軽減税率対応形式で出力

    /**
     * 軽減税率対象の可否を設定する
     * 葬儀や法事は、消費税基準日(sougi_ymd)を設定する
     *   juchu_*_history からの出力時でも現在の sougi_ymd を使うので注意
     * 単品、別注品は常に可で設定する
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @param $uridenRec
     * @return void
     */
    protected static function _prepKeigen($uridenRec)
    {
        $db = Msi_Sys_DbManager::getMyDb();
        $seko_no = $uridenRec['seko_no'];
        $data_kbn = $uridenRec['data_kbn'];

        if ( $data_kbn == 3 || $data_kbn == 4 ) { // // 単品(3),別注品(4)は sougi_ymd を気にしない
            static::_forceKeigenAppliedCtxt();
            return;
        }

        $sougi_ymd = $db->getOneVal( <<< END_OF_SQL
SELECT TO_CHAR(sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd
  FROM seko_kihon_info
 WHERE seko_no=?
   AND delete_flg=0 
END_OF_SQL
                                     , array( $seko_no ) );

        static::$_sougi_ymd = $sougi_ymd; // null もあり得る
    }

    /**
     * 強制的に軽減税率対象とする
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @param $boolean
     * @return void
     */
    protected static function _forceKeigenAppliedCtxt($boolean=true)
    {
        static::$_is_keigen_ctxt = $boolean;
    }

    /**
     * 消費税基準日(sougi_ymd)が軽減税率対象となる場合に真を返す
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @return boolean
     */
    protected static function _isKeigenAppliedCtxt()
    {
        if ( static::$_is_keigen_ctxt ) {
            return true;
        }

        $sougi_ymd = static::$_sougi_ymd;
        if ( $sougi_ymd == null ) {
            return false;
        }

        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen();
        if ( $keigenBgnYmd <= $sougi_ymd ) {
            return true;
        }
        return false;
    }
    /**
     * 合計金額データを表示用に変換して返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx   RyoshuPdfからコピー
     * @params integer   $num
     * @return string    表示用文字列
     */
    protected static function _filterKingakuHyphen($num)
    {
        $yen   = mb_convert_encoding(pack("H*", "FFE5"), "UTF-8", "UTF-16"); // 00A5:yen, FFE5:full-width yen
        $trail = mb_convert_encoding(pack("H*", "FF0D"), "UTF-8", "UTF-16"); // 002D:hyphen, FF0D:full-width hyphen
        $str = sprintf('%s%s%s', $yen, Msi_Sys_Utils::filterComma($num), $trail);
        return $str;
    }
    /**
     * 
     * 印紙表示可否
     * 
     * @param type $ryoshuRec
     * @return boolean
     */
    private function inshiDispCheck($ryoshuRec) {
        $credit_prc  = $ryoshuRec["credit_prc"];
        $j_debit_prc = $ryoshuRec["j_debit_prc"];
        $credit_prc_keigen   = $ryoshuRec["credit_prc_keigen"];
        $j_debit_prc_keigen  = $ryoshuRec["j_debit_prc_keigen"];
        $credit_prc_hikazei  = $ryoshuRec["credit_prc_hikazei"];
        $j_debit_prc_hikazei = $ryoshuRec["j_debit_prc_hikazei"];
        $credit_prc_3  = $ryoshuRec["credit_prc_3"];
        $j_debit_prc_3 = $ryoshuRec["j_debit_prc_3"];
        $credit_prc_5  = $ryoshuRec["credit_prc_5"];
        $j_debit_prc_5 = $ryoshuRec["j_debit_prc_5"];
        if($credit_prc > 0){
            return false;
        }else if($j_debit_prc > 0) {
            return false;
        }else if($credit_prc_keigen > 0) {
            return false;
        }else if($j_debit_prc_keigen > 0) {
            return false;
        }else if($credit_prc_hikazei > 0) {
            return false;
        }else if($j_debit_prc_hikazei > 0) {
            return false;
        }else if($credit_prc_3 > 0) {
            return false;
        }else if($j_debit_prc_3 > 0) {
            return false;
        }else if($credit_prc_5 > 0) {
            return false;
        }else if($j_debit_prc_5 > 0) {
            return false;
        }
        // 印紙税額
        if($ryoshuRec['inshi_zei_prc'] <= 0){
            return false;
        }
        return true;
    }
    /**
     * 
     * 印紙税情報取得
     * 
     * @return type
     */
    private function getInshiInfo($db, $ryoshu_prc){
        $select = $db->easySelect(<<< END_OF_SQL
SELECT 
izm.kingaku_from,
izm.kingaku_to,
izm.inshi_zei_kingaku
FROM inshi_zei_mst izm
WHERE CURRENT_DATE BETWEEN izm.tekiyo_st_date AND izm.tekiyo_ed_date
AND :ryoshu_prc BETWEEN izm.kingaku_from AND izm.kingaku_to
ORDER BY toroku_no

END_OF_SQL
            ,array('ryoshu_prc' => $ryoshu_prc)
        );
        return $select;
    }
    /**
     * 
     * 8551:印紙税納付税務署
     * 
     * @param type $db
     * @param type $bumon_cd
     */
    private function getInshiCodeKbn($db, $bumon_cd){
        $select = $db->easySelect(<<< END_OF_SQL
SELECT 
    kbn_value_cd
FROM 
    code_nm_mst
WHERE 
    code_kbn     = '8551'
AND kbn_value_cd = :bumon_cd
AND delete_flg   = 0
END_OF_SQL
            , array('bumon_cd' => $bumon_cd)
        );
        return $select;        
    }    
}
