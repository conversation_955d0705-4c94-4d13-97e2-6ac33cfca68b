<?php
  /**
   * DataMapper_UnMatchKeshikomi
   *
   * 入金アンマッチ消込 データマッパークラス
   *
   * @deprecated
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Kino
   * @since      2021/02/XX
   * @filesource 
   */

  /**
   * 入金アンマッチ消込 データマッパークラス
   * 
   * @deprecated
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Kino
   * @since      2021/02/XX
   */
class DataMapper_UnMatchKeshikomi extends DataMapper_Abstract
{
    /**
     * 振込入金消込情報を検索する
     *
     * <AUTHOR> Kino
     * @since  2021/02/XX
     * @param  Msi_Sys_Db $db
     * @param  array      $keyHash  条件
     * @param  boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return array      該当データがない場合はarray()を返す
     */
    public static function findFurikomiInfo($db, $keyHash=array(), $isDateEffective=false, $bumon_cd=null)
    {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.history_no, T.msi_no ';
        }

        if ( $isDateEffective ) {
            $dateWhere = " AND T.upload_date <= CURRENT_DATE AND T.upload_date >= CURRENT_DATE ";
        } else {
            $dateWhere = '';
        }
        $bumonWhere1 = '';
        $bumonWhere2 = '';
        if (isset($bumon_cd)) {
            $bumonWhere1 = "AND nd.bumon_cd = '".$bumon_cd."'";
            $bumonWhere2 = "AND nd2.bumon_cd != '".$bumon_cd."'";
        }

        $select = $db->easySelect( <<< END_OF_SQL
        SELECT *
        FROM (
            SELECT
                fpi.history_no                                        -- 取込履歴番号
                , fpi.msi_no                                            -- 取込明細番号
                , fpi.file_type                                         -- ファイル種別
                , CASE WHEN fpi.file_type = 1
                     THEN '銀行振込'
                     WHEN fpi.file_type = 2
                     THEN 'コンビニ入金'
                END AS nyukin_houhou                                  -- 入金方法
                , fpi.kaisya_cd                                         -- 会社コード
                , bm.bumon_lnm AS kaisya_nm                             -- 会社名
                , TO_CHAR(fpi.kanjyo_date, 'YYYY/MM/DD') AS kanjyo_date -- 入金日
                , fpi.upload_date                                       -- 取込日
                , fpi.auto_payment_flg                                  -- 自動消込フラグ
                , fpi.payment_kbn                                       -- 消込区分
                , fpi.kouza_no                                          -- 口座番号
                , fpi.furikomi_nm                                       -- 振込依頼人名
                , fpi.nyukin_prc - COALESCE(SUM(ndm2.nyukin_prc),0) AS nyukin_prc   -- 入金額
                , fpi.furikomi_chg_cost                                 -- 振込手数料
                , fpi.kariuke_prc                                       -- 仮受金
                , SUM(ndm.nyukin_prc) AS nyukin_zumi_prc                -- 入金済金額
                , CASE WHEN fpi.payment_kbn = 9 THEN 0
                  ELSE (fpi.nyukin_prc + COALESCE(fpi.furikomi_chg_cost,0)) - COALESCE(SUM(ndm.nyukin_prc),0) - COALESCE(SUM(ndm2.nyukin_prc),0) END AS diff_prc      -- 差額
                , CASE WHEN fpi.payment_kbn = 9 THEN 0
                  ELSE (fpi.nyukin_prc + COALESCE(fpi.furikomi_chg_cost,0)) - COALESCE(SUM(ndm.nyukin_prc),0) - COALESCE(SUM(ndm2.nyukin_prc),0) END AS diff_prc_org  -- 差額
                , CASE WHEN fpi.payment_kbn != 9 AND fpi.nyukin_prc - SUM(ndm.nyukin_prc) > 0
                     THEN 8
                     ELSE fpi.payment_kbn
                END pattern
                ,fpi._mod_cnt AS mod_cnt
            FROM furikomi_payment_info fpi
            LEFT JOIN furikomi_payment_info_msi fpm
                ON fpm.history_no = fpi.history_no
                AND fpm.msi_no = fpi.msi_no
                AND fpm.delete_flg = 0
            LEFT JOIN nyukin_denpyo nd
                ON nd.denpyo_no = fpm.nyukin_den_no
                AND nd.delete_flg = 0
                $bumonWhere1
            LEFT JOIN nyukin_denpyo_msi ndm
                ON ndm.denpyo_no = nd.denpyo_no
                AND ndm.delete_flg = 0
            LEFT JOIN nyukin_denpyo nd2
                ON nd2.denpyo_no = fpm.nyukin_den_no
                AND nd2.delete_flg = 0
                $bumonWhere2
            LEFT JOIN nyukin_denpyo_msi ndm2
                ON ndm2.denpyo_no = nd2.denpyo_no
                AND ndm2.delete_flg = 0
            LEFT JOIN bumon_mst bm
                ON bm.bumon_cd = fpi.kaisya_cd
                AND bm.delete_flg = 0
            WHERE fpi.delete_flg = 0  
            GROUP BY fpi.history_no -- 取込履歴番号
                , fpi.msi_no                                           -- 取込明細番号
                , fpi.file_type                                        -- ファイル種別
                , fpi.kaisya_cd                                        -- 会社コード
                , bm.bumon_lnm                                        -- 会社名
                , fpi.kanjyo_date                                      -- 入金日
                , fpi.upload_date                                      -- 取込日
                , fpi.auto_payment_flg                                 -- 自動消込フラグ
                , fpi.payment_kbn                                      -- 消込区分
                , fpi.kouza_no                                         -- 口座番号
                , fpi.furikomi_nm                                      -- 振込依頼人名
                , fpi.nyukin_prc                                       -- 入金額
                , fpi.furikomi_chg_cost                                -- 振込手数料
                , fpi.kariuke_prc
        ) T
        WHERE 
        $whereStr 
        $dateWhere
        $orderBy
        $tailClause
END_OF_SQL
          , $param );
        return $select;
    }
    
    /**
     * 請求伝票を検索する
     *
     * <AUTHOR> Kino
     * @since  2021/02/XX
     * @param  Msi_Sys_Db $db
     * @param  array      $keyHash  条件
     * @param  boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return array      該当データがない場合はarray()を返す
     */
    public static function findSeikyuDenpyo($db, $keyHash=array(), $isDateEffective=false)
    {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seikyu_den_no ';
        }

        if ( $isDateEffective ) {
            $dateWhere = " AND T.keijo_ymd <= CURRENT_DATE AND T.keijo_ymd >= CURRENT_DATE ";
        } else {
            $dateWhere = '';
        }
        
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
 SELECT
          sd.seko_no
        , sd.seko_no_sub
        , sd.seikyu_den_no
        , sd.kaisya_cd
        , sd.bumon_cd
        , TO_CHAR(sd.seikyu_ymd, 'YYYY/MM/DD') AS seikyu_ymd
        , sd.br_koza_no
        , COALESCE(sssi.sekyu_knm1,'') || COALESCE(sssi.sekyu_knm2,'') AS sekyu_knm
        , sssi.sekyu_tel  
        ,sd.uri_prc_sum
            + sd.uri_nebk_sum	  
            + sd.uri_hepn_sum
            + sd.hoshi_prc_sum
            + sd.out_zei_prc 
            - sd.sougi_keiyaku_prc
            + (sd.sougi_keiyaku_prc + sd.sougi_harai_prc + sd.sougi_wari_prc)
            - sd.sougi_zei_sagaku_prc
            + sd.sougi_meigi_chg_cost + sd.sougi_meigi_chg_cost_zei
            + sd.sougi_early_use_cost + sd.sougi_early_use_cost_zei
            + sd.etc_harai_prc
            AS  seikyu_zei_prc
        , sd.nyukin_prc 
            + sd.uchikin_prc
            + sd.cupon_prc
            + sd.kouden_uchikin_prc
            AS  nyukin_prc
        , sd.seikyu_zan
        , sd.seikyu_zan AS seikyu_zan_org
        , fpm.history_no  
        , sd.data_kbn
        , cdn.kbn_value_lnm AS seikyu_kbn -- 請区名
        , fpm.msi_no
        ,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
          translate(sd.sekyu_knm
         ,'ｱｲｳｴｵｶｷｸｹｺｻｼｽｾｿﾀﾁﾂﾃﾄﾅﾆﾇﾈﾉﾊﾋﾌﾍﾎﾏﾐﾑﾒﾓﾔﾕﾖﾗﾘﾙﾚﾛﾜｦﾝｧｨｩｪｫｯｬｭｮﾜｰー－（）ﾞﾟ。、「」' 
         ,'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲンアイウエオツヤユヨワ---()゛゜｡､｢｣')
         , ' ', '')
         , '　', '')
         , 'カ゛', 'ガ')
         , 'キ゛', 'ギ')
         , 'ク゛', 'グ')
         , 'ケ゛', 'ゲ')
         , 'コ゛', 'ゴ')
         , 'サ゛', 'ザ')
         , 'シ゛', 'ジ')
         , 'ス゛', 'ズ')
         , 'セ゛', 'ゼ')
         , 'ソ゛', 'ゾ')
         , 'タ゛', 'ダ')
         , 'チ゛', 'ヂ')
         , 'ツ゛', 'ヅ')
         , 'テ゛', 'デ')
         , 'ト゛', 'ド')
         , 'ハ゛', 'バ')
         , 'ヒ゛', 'ビ')
         , 'フ゛', 'ブ')
         , 'ヘ゛', 'ベ')
         , 'ホ゛', 'ボ')
         , 'ハ゜', 'パ')
         , 'ヒ゜', 'ピ')
         , 'フ゜', 'プ')
         , 'ヘ゜', 'ペ')
         , 'ホ゜', 'ポ')
         , 'ウ゛', 'ヴ')
         , '(カイジヨウ', '')
         , 'カイジヨウ)', '')
         , '(キヨウサイレン', '')
         , 'キヨウサイレン)', '')
         , '(ギヨキヨウ', '')
         , 'ギヨキヨウ)', '')
         , '(キヨウサイ', '')
         , 'キヨウサイ)', '')
         , '(キヨウクミ', '')
         , 'キヨウクミ)', '')
         , '(ギヨレン', '')
         , 'ギヨレン)', '')
         , '(ケイザイレン', '')
         , 'ケイザイレン)', '')
         , '(ケンボ', '')
         , 'ケンボ)', '')
         , '(コクキヨウレン', '')
         , 'コクキヨウレン)', '')
         , '(コクホレン', '')
         , 'コクホレン)', '')
         , '(コウネン', '')
         , 'コウネン)', '')
         , '(シヨクハンキヨヴ', '')
         , 'シヨクハンキヨヴ)', '')
         , '(ジユウクミ', '')
         , 'ジユウクミ)', '')
         , '(シヨクアン', '')
         , 'シヨクアン)', '')
         , '(シヤキヨウ', '')
         , 'シヤキヨウ)', '')
         , '(セイキヨウ', '')
         , 'セイキヨウ)', '')
         , '(セイメイ', '')
         , 'セイメイ)', '')
         , '(トクヨウ', '')
         , 'トクヨウ)', '')
         , '(ノウキヨウレン', '')
         , 'ノウキヨウレン)', '')
         , '(ユウクミ', '')
         , 'ユウクミ)', '')
         , '(ロウクミ', '')
         , 'ロウクミ)', '')
         , '(チドク', '')
         , 'チドク)', '')
         , '(カサイ', '')
         , 'カサイ)', '')
         , '(コクホ', '')
         , 'コクホ)', '')
         , '(カンリ', '')
         , 'カンリ)', '')
         , '(チユウ', '')
         , 'チユウ)', '')
         , '(ロウム', '')
         , 'ロウム)', '')
         , '(トクヒ', '')
         , 'トクヒ)', '')
         , '(シヤホ', '')
         , 'シヤホ)', '')
         , '(シユウ', '')
         , 'シユウ)', '')
         , '(シユツ', '')
         , 'シユツ)', '')
         , '(シホウ', '')
         , 'シホウ)', '')
         , '(ザイ', '')
         , 'ザイ)', '')
         , '(ギヨ', '')
         , 'ギヨ)', '')
         , '(ケン', '')
         , 'ケン)', '')
         , '(シヤ', '')
         , 'シヤ)', '')
         , '(シツ', '')
         , 'シツ)', '')
         , '(ガク', '')
         , 'ガク)', '')
         , '(ゼイ', '')
         , 'ゼイ)', '')
         , '(ダイ', '')
         , 'ダイ)', '')
         , '(ドク', '')
         , 'ドク)', '')
         , '(ノウ', '')
         , 'ノウ)', '')
         , '(フク', '')
         , 'フク)', '')
         , '(ベン', '')
         , 'ベン)', '')
         , '(ホゴ', '')
         , 'ホゴ)', '')
         , '(モク', '')
         , 'モク)', '')
         , '(エイ', '')
         , 'エイ)', '')
         , '(レン', '')
         , 'レン)', '')
         , '(イ', '')
         , 'イ)', '')
         , '(ド', '')
         , 'ド)', '')
         , '(シ', '')
         , 'シ)', '')
         , '(メ', '')
         , 'メ)', '')
         , '(ユ', '')
         , 'ユ)', '')
         , '(ソ', '')
         , 'ソ)', '')
         , '(カ', '')
         , 'カ)', '')
         , '(', '')
         , ')', '') AS rpl_sekyu_knm
  FROM seikyu_denpyo sd
  LEFT JOIN seikyu_sekyu_saki_info sssi
    ON sssi.seikyu_den_no = sd.seikyu_den_no
    AND sssi.delete_flg = 0
  LEFT JOIN furikomi_payment_info_msi fpm
    ON fpm.seikyu_no = sd.seikyu_den_no
   AND fpm.delete_flg = 0
  LEFT JOIN code_nm_mst cdn
    ON code_kbn = '8320'
   AND cdn.kbn_value_cd_num = sd.data_kbn
   AND cdn.delete_flg = 0
 WHERE sd.delete_flg = 0
   AND sd.bun_gas_kbn_num IN (0,2,20)   -- 通常・分割先・合算先
) T
 WHERE $whereStr 
 $dateWhere
 $orderBy
 $tailClause
END_OF_SQL
          , $param );
        return $select;
    }
    
    
    /**
     * 請求伝票を検索する(請求用)
     * 消込区分を考慮した版
     *
     * <AUTHOR> Tosaka
     * @since  2021/12/XX
     * @param  Msi_Sys_Db $db
     * @param  array      $keyHash  条件
     * @param  boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return array      該当データがない場合はarray()を返す
     */
    public static function findSeikyuDenpyoForSeikyu($db, $keyHash=array(), $payment_kbn=null, $isDateEffective=false)
    {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seikyu_den_no ';
        }

        if ( $isDateEffective ) {
            $dateWhere = " AND T.keijo_ymd <= CURRENT_DATE AND T.keijo_ymd >= CURRENT_DATE ";
        } else {
            $dateWhere = '';
        }
        if (isset($payment_kbn)) {
            $paymentWhere = " AND fpi.payment_kbn = ".$payment_kbn;
        } else {
            $paymentWhere = '';
        }
        
        
        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM (
 SELECT
          sd.seko_no
        , sd.seko_no_sub
        , sd.seikyu_den_no
        , sd.kaisya_cd
        , sd.bumon_cd
        , TO_CHAR(sd.seikyu_ymd, 'YYYY/MM/DD') AS seikyu_ymd
        , sd.br_koza_no
        , COALESCE(sssi.sekyu_knm1,'') || COALESCE(sssi.sekyu_knm2,'') AS sekyu_knm
        , sssi.sekyu_tel  
        ,sd.uri_prc_sum
            + sd.uri_nebk_sum	  
            + sd.uri_hepn_sum
            + sd.hoshi_prc_sum
            + sd.out_zei_prc 
            - sd.sougi_keiyaku_prc
            + (sd.sougi_keiyaku_prc + sd.sougi_harai_prc + sd.sougi_wari_prc)
            - sd.sougi_zei_sagaku_prc
            + sd.sougi_meigi_chg_cost + sd.sougi_meigi_chg_cost_zei
            + sd.sougi_early_use_cost + sd.sougi_early_use_cost_zei
            + sd.etc_harai_prc
            AS  seikyu_zei_prc
        , sd.nyukin_prc 
            + sd.uchikin_prc
            + sd.cupon_prc
            + sd.kouden_uchikin_prc
            AS  nyukin_prc
        , sd.seikyu_zan
        , sd.seikyu_zan AS seikyu_zan_org
        , fpm2.history_no  
        , sd.data_kbn
        , cdn.kbn_value_lnm AS seikyu_kbn -- 請区名
        , fpm2.msi_no
        ,REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
         REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(
          translate(sd.sekyu_knm
         ,'ｱｲｳｴｵｶｷｸｹｺｻｼｽｾｿﾀﾁﾂﾃﾄﾅﾆﾇﾈﾉﾊﾋﾌﾍﾎﾏﾐﾑﾒﾓﾔﾕﾖﾗﾘﾙﾚﾛﾜｦﾝｧｨｩｪｫｯｬｭｮﾜｰー－（）ﾞﾟ。、「」' 
         ,'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲンアイウエオツヤユヨワ---()゛゜｡､｢｣')
         , ' ', '')
         , '　', '')
         , 'カ゛', 'ガ')
         , 'キ゛', 'ギ')
         , 'ク゛', 'グ')
         , 'ケ゛', 'ゲ')
         , 'コ゛', 'ゴ')
         , 'サ゛', 'ザ')
         , 'シ゛', 'ジ')
         , 'ス゛', 'ズ')
         , 'セ゛', 'ゼ')
         , 'ソ゛', 'ゾ')
         , 'タ゛', 'ダ')
         , 'チ゛', 'ヂ')
         , 'ツ゛', 'ヅ')
         , 'テ゛', 'デ')
         , 'ト゛', 'ド')
         , 'ハ゛', 'バ')
         , 'ヒ゛', 'ビ')
         , 'フ゛', 'ブ')
         , 'ヘ゛', 'ベ')
         , 'ホ゛', 'ボ')
         , 'ハ゜', 'パ')
         , 'ヒ゜', 'ピ')
         , 'フ゜', 'プ')
         , 'ヘ゜', 'ペ')
         , 'ホ゜', 'ポ')
         , 'ウ゛', 'ヴ')
         , '(カイジヨウ', '')
         , 'カイジヨウ)', '')
         , '(キヨウサイレン', '')
         , 'キヨウサイレン)', '')
         , '(ギヨキヨウ', '')
         , 'ギヨキヨウ)', '')
         , '(キヨウサイ', '')
         , 'キヨウサイ)', '')
         , '(キヨウクミ', '')
         , 'キヨウクミ)', '')
         , '(ギヨレン', '')
         , 'ギヨレン)', '')
         , '(ケイザイレン', '')
         , 'ケイザイレン)', '')
         , '(ケンボ', '')
         , 'ケンボ)', '')
         , '(コクキヨウレン', '')
         , 'コクキヨウレン)', '')
         , '(コクホレン', '')
         , 'コクホレン)', '')
         , '(コウネン', '')
         , 'コウネン)', '')
         , '(シヨクハンキヨヴ', '')
         , 'シヨクハンキヨヴ)', '')
         , '(ジユウクミ', '')
         , 'ジユウクミ)', '')
         , '(シヨクアン', '')
         , 'シヨクアン)', '')
         , '(シヤキヨウ', '')
         , 'シヤキヨウ)', '')
         , '(セイキヨウ', '')
         , 'セイキヨウ)', '')
         , '(セイメイ', '')
         , 'セイメイ)', '')
         , '(トクヨウ', '')
         , 'トクヨウ)', '')
         , '(ノウキヨウレン', '')
         , 'ノウキヨウレン)', '')
         , '(ユウクミ', '')
         , 'ユウクミ)', '')
         , '(ロウクミ', '')
         , 'ロウクミ)', '')
         , '(チドク', '')
         , 'チドク)', '')
         , '(カサイ', '')
         , 'カサイ)', '')
         , '(コクホ', '')
         , 'コクホ)', '')
         , '(カンリ', '')
         , 'カンリ)', '')
         , '(チユウ', '')
         , 'チユウ)', '')
         , '(ロウム', '')
         , 'ロウム)', '')
         , '(トクヒ', '')
         , 'トクヒ)', '')
         , '(シヤホ', '')
         , 'シヤホ)', '')
         , '(シユウ', '')
         , 'シユウ)', '')
         , '(シユツ', '')
         , 'シユツ)', '')
         , '(シホウ', '')
         , 'シホウ)', '')
         , '(ザイ', '')
         , 'ザイ)', '')
         , '(ギヨ', '')
         , 'ギヨ)', '')
         , '(ケン', '')
         , 'ケン)', '')
         , '(シヤ', '')
         , 'シヤ)', '')
         , '(シツ', '')
         , 'シツ)', '')
         , '(ガク', '')
         , 'ガク)', '')
         , '(ゼイ', '')
         , 'ゼイ)', '')
         , '(ダイ', '')
         , 'ダイ)', '')
         , '(ドク', '')
         , 'ドク)', '')
         , '(ノウ', '')
         , 'ノウ)', '')
         , '(フク', '')
         , 'フク)', '')
         , '(ベン', '')
         , 'ベン)', '')
         , '(ホゴ', '')
         , 'ホゴ)', '')
         , '(モク', '')
         , 'モク)', '')
         , '(エイ', '')
         , 'エイ)', '')
         , '(レン', '')
         , 'レン)', '')
         , '(イ', '')
         , 'イ)', '')
         , '(ド', '')
         , 'ド)', '')
         , '(シ', '')
         , 'シ)', '')
         , '(メ', '')
         , 'メ)', '')
         , '(ユ', '')
         , 'ユ)', '')
         , '(ソ', '')
         , 'ソ)', '')
         , '(カ', '')
         , 'カ)', '')
         , '(', '')
         , ')', '') AS rpl_sekyu_knm
  FROM seikyu_denpyo sd
  LEFT JOIN seikyu_sekyu_saki_info sssi
    ON sssi.seikyu_den_no = sd.seikyu_den_no
    AND sssi.delete_flg = 0
  LEFT JOIN(SELECT fpm.seikyu_no,fpm.history_no,fpm.msi_no FROM furikomi_payment_info_msi fpm
    INNER JOIN furikomi_payment_info fpi
        ON fpi.history_no = fpm.history_no
        AND fpi.msi_no = fpm.msi_no
        $paymentWhere
        AND fpi.delete_flg = 0
    WHERE fpm.delete_flg = 0) fpm2
    ON fpm2.seikyu_no = sd.seikyu_den_no
  LEFT JOIN code_nm_mst cdn
    ON code_kbn = '8320'
   AND cdn.kbn_value_cd_num = sd.data_kbn
   AND cdn.delete_flg = 0
 WHERE sd.delete_flg = 0
   AND sd.bun_gas_kbn_num IN (0,2,20)   -- 通常・分割先・合算先
) T
 WHERE $whereStr 
 $dateWhere
 $orderBy
 $tailClause
END_OF_SQL
          , $param );
        return $select;
    }
}
