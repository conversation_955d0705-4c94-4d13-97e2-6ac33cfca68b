<?php

/**
 * Logic_SyukeiTblUpdateSub
 *
 * 各種集計テーブル作成、更新処理（サブ関数:会社毎に違う計算式を作成）
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Kayo
 * @version    2020/06/24 Sugiyama  seikyu_zanテーブル更新処理を削除
 * @version    2020/06/24 Sugiyama  UrikakeZanMakeの処理を削除
 * @since      2020/03/25
 * @filesource 
 */

/**
 * 各種集計テーブル作成、更新処理(サブ関数)
 * 
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Kayo
 * @since      2016/08/25
 */
class Logic_SyukeiTblUpdateSub {
    
    const STATUS_SEIKYU_KAKUTEI = '4';
    const STATUS_SEIKYU_NYUKIN = '5';

    /**
     * 受注伝票と売上伝票の請求先情報を更新処理
     *
     * <AUTHOR> Tosaka
     * @since   2020/03/25
     * @param   Msi_Sys_Db $db	データベース
     * @param   string  $denpyo_no 受注伝票№
     * @param   string  $data_kbn 1：葬儀 2：法事 3：単品 4：別注品
     * @param   string  $kaisya_cd 会社コード
     * @param   string  $soukiriyozeikbn 早期利用費消費税区分 0：外税計算 1:内税計算
     * @param   string  $addDay 基準日からXX日後
     * @param   string  $banHolidayKbn 銀行休日区分 0:前日 1:翌日
     * @param   string  $kakekin_tax_kbn 掛金消費税計算区分 0：計算なし 1：計算あり
     * @param   string  $gojyo_tax_keian_kbn 互助会消費税計算区分 0：加入日時点 1：施行日時点
     * @param   string  $maturity_kbn 支払期日計算区分 0：自動計算 1：入力
     * @return     bool   true：成功 false:該当なし
     */
    public static function SeikyuInfoUpdate($db, $denpyo_no, $data_kbn, $kaisya_cd, $soukiriyozeikbn
    , $addDay, $banHolidayKbn, $kakekin_tax_kbn, $gojyo_tax_keian_kbn, $maturity_kbn, $seko_no = null) {
        // 受注伝票を取得する
        $jchuden = DataMapper_JuchuDenpyo::findDenpyo($db, array(
                    'delete_flg' => 0,
                    'data_kbn' => $data_kbn,
                    'denpyo_no' => $denpyo_no
        ));
        //受注伝票が存在しないのはありえないのでエラー	
        if (count($jchuden) <= 0) {
            return false;
        }

        // 葬儀日を取得 
        $seko_kihon = $db->easySelect(<<< END_OF_SQL
        SELECT TO_CHAR(sougi_ymd,'YYYY/MM/DD') AS sougi_ymd -- 葬儀日
        FROM seko_kihon_info
        WHERE delete_flg = 0
            AND seko_no = :seko_no
END_OF_SQL
                , array('seko_no' => $jchuden[0]['seko_no']));

        // 支払期日を算出する(銀行休日を判断して支払期日を算出する)　
        $Maturity = null;
        if (count($seko_kihon) > 0) {
            if (strlen($seko_kihon[0]['sougi_ymd']) > 0) {
                $Maturity = App_DateCalc::GetMaturity($seko_kihon[0]['sougi_ymd'], $addDay, $banHolidayKbn);
            }
        }
        if ($jchuden[0]['data_kbn'] === '3' || $jchuden[0]['data_kbn'] === '4' || $jchuden[0]['data_kbn'] === '8') { // 1：葬儀 2：法事 3：単品 4：別注品 8:新盆
            if ($maturity_kbn == 0) {
//                $denpyo = array();
//                $denpyo['kaishu_ymd'] = $Maturity; // 回収予定日  
//                //受注伝票の回収予定日を更新する。
//                $where = array();
//                $where['delete_flg'] = 0;
//                $where['data_kbn'] = $jchuden[0]['data_kbn'];
//                $where['denpyo_no'] = $denpyo_no;
//                list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL('juchu_denpyo', $denpyo, $where);
//                $cnt = $db->easyExecute($sqlUp, $param);
//                // 売上伝票の回収予定日を更新する
//                $where = array();
//                $where['delete_flg'] = 0;
//                $where['data_kbn'] = $jchuden[0]['data_kbn'];
//                $where['denpyo_no'] = $denpyo_no;
//                list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL('uriage_denpyo', $denpyo, $where);
//                $cnt = $db->easyExecute($sqlUp, $param);
            } else {
                // 設定されていない場合更新
                if (strlen($jchuden[0]['kaishu_ymd']) <= 0) {
//                    $denpyo = array();
//                    $denpyo['kaishu_ymd'] = $Maturity; // 回収予定日  
//                    //受注伝票の回収予定日を更新する。
//                    $where = array();
//                    $where['delete_flg'] = 0;
//                    $where['data_kbn'] = $jchuden[0]['data_kbn'];
//                    $where['denpyo_no'] = $denpyo_no;
//                    list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL('juchu_denpyo', $denpyo, $where);
//                    $cnt = $db->easyExecute($sqlUp, $param);
//                    // 売上伝票の回収予定日を更新する
//                    $where = array();
//                    $where['delete_flg'] = 0;
//                    $where['data_kbn'] = $jchuden[0]['data_kbn'];
//                    $where['denpyo_no'] = $denpyo_no;
//                    list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL('uriage_denpyo', $denpyo, $where);
//                    $cnt = $db->easyExecute($sqlUp, $param);
                }
            }
        }
        // データ種別が葬儀の場合
        if ($jchuden[0]['data_kbn'] === '1' || $jchuden[0]['data_kbn'] === '2') { // 1：葬儀 2：法事 3：単品 4：別注品
            $seko_no = $jchuden[0]['seko_no'];
            $seko_no_sub = $jchuden[0]['seko_no_sub'];
            $denpyo = array();
            // 設定されていない場合更新
            if (strlen($jchuden[0]['kaishu_ymd']) <= 0) {
//                $denpyo['kaishu_ymd'] = $Maturity; // 回収予定日  
            }
            $denpyo['sougi_keiyaku_prc'] = 0;           // 葬儀契約金額
            $denpyo['sougi_keiyaku_zei'] = 0;           // 葬儀契約金額消費税 
            $denpyo['sougi_harai_prc'] = 0;             // 葬儀払込金額（入金金額）
            $denpyo['sougi_wari_prc'] = 0;              // 葬儀前納割引額（割引金額）
            $denpyo['sougi_wari_zei'] = 0;              // 割引金額消費税
            $denpyo['sougi_premium_service_prc'] = 0;   // 葬儀サービス割増金額（割増金額）
            $denpyo['sougi_cose_chg_prc'] = 0;          // 葬儀コース変更差額金
            $denpyo['sougi_meigi_chg_cost'] = 0;        // 葬儀名義変更手数料
            $denpyo['sougi_meigi_chg_cost_zei'] = 0;    // 葬儀名義変更手数料消費税
            $denpyo['sougi_early_use_cost'] = 0;        // 葬儀早期利用費
            $denpyo['sougi_early_use_cost_zei'] = 0;    // 葬儀早期利用費消費税 
            $denpyo['sougi_zei_sagaku_prc'] = 0;        // 葬儀掛金消費税差額
            $denpyo['sougi_zei_cd'] = 0;                // 葬儀消費税コード
            $denpyo['etc_keiyaku_prc'] = 0;             // 壇払等の契約金額	
            $denpyo['etc_keiyaku_zei'] = 0;             // 壇払等の契約金額消費税	
            $denpyo['etc_harai_prc'] = 0;               // 壇払等の払込金額	
            $denpyo['etc_wari_prc'] = 0;                // 壇払等の前納割引額
            $denpyo['etc_cose_chg_prc'] = 0;            // 壇払等のコース変更差額金
            $denpyo['etc_early_use_cost'] = 0;          // 壇払等の早期利用費
            $denpyo['etc_early_use_cost_zei'] = 0;      // 壇払等早期利用費消費税 
            $denpyo['etc_zei_sagaku_prc'] = 0;          // 壇払等の掛金消費税差額
            $denpyo['etc_zei_cd'] = 0;                  // 壇払等の消費税コード
            $denpyo['sougi_tokuten_prc'] = 0;           // 完納充当額
            $denpyo['n_free9'] = 0;                     // 利用券
            $denpyo['n_free10'] = 0;                    // ポイント
            $denpyo['n_free5'] = 0;                     // 完納充当未納額
            $gojokai = DataMapper_GojokaiCalc::getGojokaiKingaku_zei($db, $seko_no);
            $kijyun_ymd = Msi_Sys_Utils::getDate();
            if ($gojyo_tax_keian_kbn == 1) { // 互助会消費税計算区分 0：加入日時点 1：施行日時点
                if (count($seko_kihon) > 0) {
                    if (strlen($seko_kihon[0]['sougi_ymd']) > 0) {
                        $kijyun_ymd = $seko_kihon[0]['sougi_ymd'];
                    }
                }
            }
            $denpyo['sougi_keiyaku_prc'] = $gojokai['sougi_keiyaku_prc'];             // 葬儀契約金額
            $denpyo['sougi_keiyaku_zei'] = $gojokai['sougi_keiyaku_zei'];
            $denpyo['sougi_harai_prc'] = $gojokai['sougi_harai_prc'] * -1;            // 葬儀払込金額
            $denpyo['sougi_wari_prc'] = $gojokai['sougi_wari_prc'] * -1;              // 葬儀前納割引額（割引金額）
            $denpyo['sougi_wari_zei'] = $gojokai['sougi_wari_zei'] * -1;              // 割引金額消費税
            $denpyo['sougi_premium_service_prc'] = $gojokai['sougi_premium_service_prc'] * -1; // 葬儀サービス割増金額（割増金額）
            $denpyo['sougi_meigi_chg_cost'] = $gojokai['sougi_meigi_chg_cost'];       // 名義変更手数料
            $denpyo['sougi_meigi_chg_cost_zei'] = $gojokai['sougi_meigi_chg_cost_zei']; // 名義変更手数料消費税
            $denpyo['sougi_early_use_cost'] = $gojokai['sougi_early_use_cost'];       // 葬儀早期利用費
            $denpyo['sougi_early_use_cost_zei'] = $gojokai['sougi_early_use_cost_zei']; // 葬儀早期利用費消費税 
            $denpyo['sougi_zei_sagaku_prc'] = $gojokai['sougi_zei_sagaku_prc'];       // 葬儀消費税差額
            $denpyo['sougi_zei_cd'] = $gojokai['sougi_zei_cd'];                       // 葬儀消費税コード
            $denpyo['etc_keiyaku_prc'] = $gojokai['etc_keiyaku_prc'];                 // 壇払等の契約金額	
            $denpyo['etc_harai_prc'] = $gojokai['etc_harai_prc'] * -1;                // 壇払等の払込金額	
            $denpyo['etc_wari_prc'] = $gojokai['etc_wari_prc'] * -1;                  // 壇払等の前納割引額
            $denpyo['etc_early_use_cost'] = $gojokai['etc_early_use_cost'];           // 壇払等の早期利用費
            $denpyo['etc_early_use_cost_zei'] = $gojokai['etc_early_use_cost_zei'];   // 壇払等早期利用費消費税 
            $denpyo['etc_zei_cd'] = $gojokai['etc_zei_cd'];                           // 壇払等の消費税コード
            $denpyo['etc_keiyaku_zei'] = $gojokai['etc_keiyaku_zei'];                 // 壇払等の契約金額消費税 
            $denpyo['sougi_tokuten_prc'] = $gojokai['sougi_tokuten_prc'] * -1;        // 完納充当額
            $denpyo['n_free6'] = $gojokai['n_free6'];                                 // 全金額一括支払割引額(税抜金額)
            $denpyo['n_free7'] = $gojokai['n_free7'];                                 // 全金額一括支払割引額(消費税額)
            $denpyo['n_free9'] = $gojokai['n_free9'] * -1;                            // 利用券
            $denpyo['n_free10'] = $gojokai['n_free10'] * -1;                          // ポイント
            $denpyo['n_free5'] = $gojokai['n_free5'];                                 // 完納充当未納額
            $seikyuRec = App_Utils::getSekoSekyuInfo($seko_no);
            //請求先情報が存在する場合	
            if (Msi_Sys_Utils::myCount($seikyuRec) > 0) {
                $denpyo['sekyu_cd'] = $seikyuRec['sekyu_cd'];       // 請求先コード	
                $denpyo['sekyu_nm'] = $seikyuRec['sekyu_nm'];       // 請求先名		
                $denpyo['sekyu_knm'] = $seikyuRec['sekyu_knm'];     // 請求先名カナ		
                $denpyo['sekyu_soufu_nm'] = $seikyuRec['sekyu_nm']; // 請求書送付先名
                $denpyo['sekyu_yubin_no'] = $seikyuRec['yubin_no']; // 請求先郵便番号   
                $denpyo['sekyu_addr1'] = $seikyuRec['addr1'];       // 請求先住所1 
                $denpyo['sekyu_addr2'] = $seikyuRec['addr2'];       // 請求先住所2
                $denpyo['sekyu_tel'] = $seikyuRec['tel'];           // 請求先電話番号
                $denpyo['sekyu_fax'] = $seikyuRec['fax'];           // 請求先FAX		
            }
            //受注伝票の請求先情報を更新する。
            $where = array();
            $where['delete_flg'] = 0;
            $where['data_kbn'] = $jchuden[0]['data_kbn'];
            $where['denpyo_no'] = $denpyo_no;
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL('juchu_denpyo', $denpyo, $where);
            $cnt = $db->easyExecute($sqlUp, $param);
        }
        // 売上伝票を取得する
        $uriden = DataMapper_UriageDenpyo::findDenpyo3($db, array(
                    'delete_flg' => 0,
                    'data_kbn' => $jchuden[0]['data_kbn'],
                    'denpyo_no' => $denpyo_no
        ));
        //売上伝票が存在しないのはありえないのでエラー	
        if (count($uriden) <= 0) {
            return false;
        }

        if ($jchuden[0]['data_kbn'] === '1' || $jchuden[0]['data_kbn'] === '2') { // 1：葬儀 2：法事 3：単品 4：別注品
            $cunt = Logic_SyukeiTblUpdateSub::SeikyuInfoUpdateSub($db, $denpyo_no, $uriden, $denpyo, $soukiriyozeikbn);
        }
        return $uriden[0];
    }

    /**
     * 受注伝票と売上伝票の請求先情報を更新処理(サブ)
     *
     * <AUTHOR> Kayo
     * @since   2014/06/27
     * @param   Msi_Sys_Db $db	データベース
     * @param   string $denpyo_no 受注伝票番号
     * @param   array  $uriden 売上伝票レコード
     * @param   array  $denpyo 売上伝票レコード
     * @param   string  $soukiriyozeikbn 早期利用費消費税区分 0：外税計算 1:内税計算
     * @return  bool   true：成功 false:該当なし
     */
    public static function SeikyuInfoUpdateSub($db, $denpyo_no, $uriden, $denpyo, $soukiriyozeikbn) {
        if ($uriden[0]['data_kbn'] == '1' || $uriden[0]['data_kbn'] == '2') { // 1：葬儀 2：法事 3：単品 4：別注品
            //売上伝票の請求先情報を更新する。
            // 請求残高
            $denpyo["seikyu_zan"] = $uriden[0]["uri_prc_sum"] 
                    + $uriden[0]["uri_hepn_sum"] 
                    + $uriden[0]["uri_nebk_sum"] 
                    + $uriden[0]["hoshi_prc_sum"] 
                    + $uriden[0]["out_zei_prc"]
                    + $denpyo['sougi_keiyaku_prc']+ $denpyo['sougi_harai_prc']
                    + $denpyo['sougi_keiyaku_zei']
                    + $denpyo['sougi_wari_prc']
                    + $denpyo['sougi_wari_zei']
                    + $denpyo['sougi_premium_service_prc']
                    + $denpyo['sougi_early_use_cost'] + $denpyo['sougi_early_use_cost_zei']   // 早期利用費 
                    + $denpyo['sougi_meigi_chg_cost'] + $denpyo['sougi_meigi_chg_cost_zei']   // 名義変更手数料 
                    + $denpyo['sougi_tokuten_prc']  // 会費充当 
                    - ($uriden[0]["nyukin_prc"] + $uriden[0]["uchikin_prc"] + $uriden[0]['cupon_prc'])
                    + $uriden[0]["n_free9"] + $uriden[0]["n_free10"] + $uriden[0]["n_free5"]
                    ; // 請求残高

            $where = array();
            $where['delete_flg'] = 0;
            $where['data_kbn'] = $uriden[0]['data_kbn'];
            $where['denpyo_no'] = $denpyo_no;
            // Msi_Sys_Utils::debug( ' denpyo=>' . Msi_Sys_Utils::dump($denpyo) );
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL('uriage_denpyo', $denpyo, $where);
            $cnt = $db->easyExecute($sqlUp, $param);
        }
        return $cnt;
    }

    /**
     *
     * 入金伝票から売上伝票の入金金額、請求残高を修正して更新する
     *
     * <AUTHOR> Kayo
     * @since   2016/8/25
     * @param   Msi_Sys_Db $db
     * @param   string  $seikyu_no 請求書番号
     * @param   string  $soukiriyozeikbn 早期利用費消費税区分 0：外税計算 1:内税計算
     * @return void
     */
    public static function updNyukinPrc($db, $seikyu_no, $soukiriyozeikbn = 0) {
        // 先に売上伝票をクリア
        $cnt = $db->easyExecute(<<< END_OF_SQL
        UPDATE uriage_denpyo
        SET  nyukin_prc = 0 -- 入金金額
            ,uchikin_prc = 0 -- 内金金額
            ,cupon_prc = 0 -- クーポン金額
            ,kouden_uchikin_prc = 0 -- 香典内金
            ,seikyu_zan = 0 -- 請求残高
        WHERE delete_flg = 0
            AND uri_den_no = :seikyu_no
END_OF_SQL
                , array('seikyu_no' => $seikyu_no));

        // 入金伝票SELECT(内金以外)
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            COALESCE(SUM(nyukin_prc), 0) AS nyukin_prc 
        FROM nyukin_denpyo nd 
        WHERE seikyu_no = :seikyu_no
            AND nyu_kbn NOT IN (4,5,88)
            AND delete_flg  = 0
END_OF_SQL
                , array('seikyu_no' => $seikyu_no));
        // 入金伝票SELECT(内金)
        $select2 = $db->easySelect(<<< END_OF_SQL
            SELECT 
                COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
            FROM nyukin_denpyo d
            INNER JOIN nyukin_denpyo_msi dm
                ON dm.denpyo_no = d.denpyo_no
                AND dm.denpyo_kbn <> 5
                AND dm.delete_flg = 0
            WHERE d.uri_den_no = :uri_den_no
                AND d.nyu_kbn     = 88
                AND d.delete_flg  = 0
END_OF_SQL
                , array('uri_den_no' => $seikyu_no));
        // 入金伝票SELECT(クーポン)
        $select3 = $db->easySelect(<<< END_OF_SQL
            SELECT 
                COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
            FROM nyukin_denpyo d
            INNER JOIN nyukin_denpyo_msi dm
                ON dm.denpyo_no = d.denpyo_no
                AND dm.denpyo_kbn = 5
                AND dm.delete_flg = 0
            WHERE d.uri_den_no = :uri_den_no
                AND d.nyu_kbn     = 88
                AND d.delete_flg  = 0
END_OF_SQL
                , array('uri_den_no' => $seikyu_no));

        // 入金伝票SELECT(香典内金)
        $select4 = $db->easySelect(<<< END_OF_SQL
            SELECT 
                COALESCE(sum(d.nyukin_prc),0) AS nyukin_prc 
            FROM nyukin_denpyo d
            WHERE d.uri_den_no = :uri_den_no
                AND d.nyu_kbn     = 5
                AND d.delete_flg  = 0
END_OF_SQL
                , array('uri_den_no' => $seikyu_no));

        if (count($select) === 0 && count($select2) === 0 && count($select3) === 0 && count($select4) === 0) {
            return;
        }
        $nyukin_prc = $select[0]['nyukin_prc'];
        $uchikin_prc = $select2[0]['nyukin_prc'];
        $cupon_prc = $select3[0]['nyukin_prc'];
        $kouden_prc = $select4[0]['nyukin_prc'];

        // 売上伝票を更新
        $cnt = $db->easyExecute(<<< END_OF_SQL
        UPDATE uriage_denpyo
        SET nyukin_prc = :nyukin_prc    -- 入金金額
            ,uchikin_prc = :uchikin_prc    -- 入金金額
            ,cupon_prc = :cupon_prc    -- クーポン利用額
            ,kouden_uchikin_prc = :kouden_uchikin_prc    -- 香典内金
            ,seikyu_zan = (
                  COALESCE(uri_prc_sum,0)
                + COALESCE(uri_nebk_sum,0) 
                + COALESCE(uri_hepn_sum,0)
                + COALESCE(hoshi_prc_sum,0)
                + COALESCE(out_zei_prc,0)
                + COALESCE(sougi_keiyaku_zei,0)  -- 契約金額
                + COALESCE(sougi_keiyaku_prc,0) 
                + COALESCE(sougi_premium_service_prc,0) 
                + COALESCE(sougi_harai_prc,0)
                + COALESCE(sougi_early_use_cost,0) + COALESCE(sougi_early_use_cost_zei,0)   -- 早期利用費
                + COALESCE(sougi_meigi_chg_cost,0) + COALESCE(sougi_meigi_chg_cost_zei,0)   -- 名義変更手数料
                + COALESCE(etc_harai_prc,0)                                     -- 金額充当
                ) - :nyukin_prc 
                  - :uchikin_prc            
                  - :cupon_prc            
                  - :kouden_uchikin_prc            -- 請求残高
        WHERE delete_flg = 0
            AND uri_den_no = :seikyu_no
END_OF_SQL
                , array('seikyu_no' => $seikyu_no
            , 'nyukin_prc' => $nyukin_prc
            , 'uchikin_prc' => $uchikin_prc
            , 'cupon_prc' => $cupon_prc
            , 'kouden_uchikin_prc' => $kouden_prc));

        return;
    }
    /**
     *
     * 入金伝票から請求伝票の入金金額、請求残高を修正して更新する
     *
     * <AUTHOR> Sugiyama
     * @since   2020/06/24
     * @param   Msi_Sys_Db $db
     * @param   string  $seikyu_no 請求書番号
     * @param   string  $seikyu_den_no 請求伝票番号
     * @param   string  $soukiriyozeikbn 早期利用費消費税区分 0：外税計算 1:内税計算
     * @return void
     */
    public static function updNyukinPrcSeikyu($db, $seikyu_no, $seikyu_den_no, $soukiriyozeikbn = 0) {
        // 先に請求伝票をクリア
        $cnt = $db->easyExecute(<<< END_OF_SQL
        UPDATE seikyu_denpyo
        SET  nyukin_prc = 0 -- 入金金額
            ,uchikin_prc = 0 -- 内金金額
            ,seikyu_zan = 0 -- 請求残高
        WHERE delete_flg = 0
        AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                , array('seikyu_den_no' => $seikyu_den_no));

        // 入金伝票SELECT(内金以外)
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT COALESCE(sum(nyukin_prc),0) AS nyukin_prc 
        FROM nyukin_denpyo
        WHERE delete_flg = 0
            AND nyu_kbn != 88
            AND seikyu_no = :seikyu_no
END_OF_SQL
                , array('seikyu_no' => $seikyu_den_no));
        // 入金伝票SELECT(内金)
        $select2 = $db->easySelect(<<< END_OF_SQL
        SELECT COALESCE(sum(nyukin_prc),0) AS nyukin_prc 
        FROM nyukin_denpyo
        WHERE delete_flg = 0
            AND nyu_kbn = 88
            AND seikyu_no = :seikyu_no
END_OF_SQL
                , array('seikyu_no' => $seikyu_den_no));

        if (count($select) === 0 && count($select2) === 0) {
            return;
        }
        $nyukin_prc = $select[0]['nyukin_prc'];
        $uchikin_prc = $select2[0]['nyukin_prc'];

        // 請求伝票を更新
        $cnt = $db->easyExecute(<<< END_OF_SQL
        UPDATE seikyu_denpyo
        SET nyukin_prc = :nyukin_prc    -- 入金金額
            ,uchikin_prc = :uchikin_prc    -- 内金金額
            ,seikyu_zan = (
                      COALESCE(uri_prc_sum,0)
                    + COALESCE(uri_nebk_sum,0)
                    + COALESCE(uri_hepn_sum,0)
                    + COALESCE(hoshi_prc_sum,0)
                    + COALESCE(out_zei_prc,0)
                    + COALESCE(sougi_keiyaku_prc,0) + COALESCE(sougi_harai_prc,0)
                    + COALESCE(sougi_keiyaku_zei,0)
                    + COALESCE(sougi_wari_prc,0) 
                    + COALESCE(sougi_premium_service_prc,0) 
                    + COALESCE(sougi_early_use_cost,0) + COALESCE(sougi_early_use_cost_zei,0)   -- 早期利用費
                    + COALESCE(sougi_meigi_chg_cost,0) + COALESCE(sougi_meigi_chg_cost_zei,0)   -- 名義変更手数料
                    + COALESCE(etc_harai_prc,0)                                     -- 金額充当
                    ) - :nyukin_prc 
                      - :uchikin_prc            -- 請求残高
        WHERE delete_flg = 0
        AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
            , array('seikyu_den_no' => $seikyu_den_no
                  , 'nyukin_prc'    => $nyukin_prc
                  , 'uchikin_prc'    => $uchikin_prc)
            );
        return;
    }
    /**
     *
     * 入金伝票から請求伝票の入金金額、請求残高を修正して更新する
     *
     * <AUTHOR> Sugiyama
     * @since   2021/02/xx
     * @param   Msi_Sys_Db $db
     * @param   string  $seikyu_den_no 請求伝票番号
     * @return void
     */
    public static function updNyukinPrcSeikyu2($db, $seikyu_den_no) {
        $cnt = 0;
        // 請求伝票のデータ区分を取得
        $seikyu = $db->easySelect(<<< END_OF_SQL
            SELECT 
                 sd.data_kbn
                ,sd.seko_no
                ,sd.status_kbn
                ,sd.seikyu_approval_status
                ,sd.bun_gas_kbn_num
                ,sd.bun_gas_seikyu_den_no
                ,sd.uri_den_no
                ,sd.cupon_prc
                ,sd.uchikin_prc
                ,sd.nyukin_prc
                ,sd.seikyu_zan
                ,sd.d_import_kbn
            FROM seikyu_denpyo sd
            WHERE sd.seikyu_den_no = :seikyu_den_no
            AND   sd.delete_flg    = 0
END_OF_SQL
            , array('seikyu_den_no' => $seikyu_den_no));
        // 請求伝票存在確認
        if(Msi_Sys_Utils::myCount($seikyu) > 0){
            /*
             * ■該当請求伝票の更新
             */
            // 入金金額を取得
            // 入金伝票SELECT(内金以外)
            $nyukin = $db->easySelect(<<< END_OF_SQL
                SELECT 
                    COALESCE(SUM(nyukin_prc), 0) AS nyukin_prc 
                FROM nyukin_denpyo nd 
                WHERE seikyu_no = :seikyu_no
                AND nyu_kbn NOT IN (4,5,88)
                AND delete_flg  = 0
END_OF_SQL
                , array('seikyu_no' => $seikyu_den_no));
            // 入金伝票SELECT(内金)
            // 分割OR合算されていれば元から売上伝票を空文字にする
            if (isset($seikyu[0]['bun_gas_seikyu_den_no'])) {
                $motoData = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $seikyu[0]['bun_gas_seikyu_den_no']));
                $uri_den_no = '';
            } else {
                $uri_den_no = $seikyu[0]['uri_den_no'];
            }
            $nyukin_uchi = $db->easySelect(<<< END_OF_SQL
                SELECT 
                    COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
                FROM nyukin_denpyo d
                INNER JOIN nyukin_denpyo_msi dm
                ON dm.denpyo_no = d.denpyo_no
                AND dm.denpyo_kbn <> 5
                AND dm.delete_flg = 0
                WHERE d.uri_den_no = :uri_den_no
                AND d.nyu_kbn     = 88
                AND d.delete_flg  = 0
END_OF_SQL
                , array('uri_den_no' => $uri_den_no));
            if($seikyu[0]['data_kbn'] == 3 && $seikyu[0]['bun_gas_kbn_num'] == 0 && $seikyu[0]['status_kbn'] == 3 && $seikyu[0]['seikyu_approval_status'] ==  0){
                // アフター請求伝票未承認(内金)
                $nyukin_uchi = $db->easySelect(<<< END_OF_SQL
                    SELECT 
                        COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
                    FROM nyukin_denpyo d
                    INNER JOIN nyukin_denpyo_msi dm
                    ON dm.denpyo_no = d.denpyo_no
                    AND dm.denpyo_kbn <> 5
                    AND dm.delete_flg = 0
                    WHERE d.seikyu_no = :seikyu_no
                    AND d.nyu_kbn     = 88
                    AND d.delete_flg  = 0
END_OF_SQL
                , array('seikyu_no' => $seikyu_den_no));
            }else if($seikyu[0]['data_kbn'] == 3 && $seikyu[0]['bun_gas_kbn_num'] == 0 && $seikyu[0]['seikyu_approval_status'] ==  1){
                // アフター請求伝票承認済み(内金)
                $nyukin_uchi = $db->easySelect(<<< END_OF_SQL
                    SELECT 
                        COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
                    FROM nyukin_denpyo d
                    INNER JOIN nyukin_denpyo_msi dm
                    ON dm.denpyo_no = d.denpyo_no
                    AND dm.denpyo_kbn <> 5
                    AND dm.delete_flg = 0
                    WHERE d.seikyu_no = :seikyu_no
                    AND d.nyu_kbn     = 88
                    AND d.delete_flg  = 0
END_OF_SQL
                , array('seikyu_no' => $seikyu_den_no));
            }
            if($seikyu[0]['data_kbn'] == 3 && $seikyu[0]['bun_gas_kbn_num'] == 0 && $seikyu[0]['status_kbn'] == 3 && $seikyu[0]['seikyu_approval_status'] ==  0){
                // 入金伝票SELECT(クーポン)
                $nyukin_cupon = $db->easySelect(<<< END_OF_SQL
                    SELECT 
                        COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
                    FROM nyukin_denpyo d
                    INNER JOIN nyukin_denpyo_msi dm
                    ON dm.denpyo_no = d.denpyo_no
                    AND dm.denpyo_kbn = 5
                    AND dm.delete_flg = 0
                    WHERE d.seikyu_no = :seikyu_no
                    AND d.nyu_kbn     = 88
                    AND d.delete_flg  = 0
END_OF_SQL
                    , array('seikyu_no' => $seikyu_den_no));
            }else if($seikyu[0]['data_kbn'] == 3 && $seikyu[0]['bun_gas_kbn_num'] == 0 && $seikyu[0]['seikyu_approval_status'] ==  1){
                // 入金伝票SELECT(クーポン)
                $nyukin_cupon = $db->easySelect(<<< END_OF_SQL
                    SELECT 
                        COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
                    FROM nyukin_denpyo d
                    INNER JOIN nyukin_denpyo_msi dm
                    ON dm.denpyo_no = d.denpyo_no
                    AND dm.denpyo_kbn = 5
                    AND dm.delete_flg = 0
                    WHERE d.seikyu_no = :seikyu_no
                    AND d.delete_flg  = 0
END_OF_SQL
                    , array('seikyu_no' => $seikyu_den_no));
                // 入金伝票SELECT(クーポン内金以外)
                $nyukin_cupon2 = $db->easySelect(<<< END_OF_SQL
                    SELECT 
                        COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
                    FROM nyukin_denpyo d
                    INNER JOIN nyukin_denpyo_msi dm
                    ON dm.denpyo_no = d.denpyo_no
                    AND dm.denpyo_kbn = 5
                    AND dm.delete_flg = 0
                    WHERE d.seikyu_no = :seikyu_no
                    AND d.nyu_kbn    <> 88
                    AND d.delete_flg  = 0
END_OF_SQL
                    , array('seikyu_no' => $seikyu_den_no));
                $nyukin[0]['nyukin_prc'] = $nyukin[0]['nyukin_prc'] - $nyukin_cupon2[0]['nyukin_prc'];
            }else{
                // 入金伝票SELECT(クーポン)
                $nyukin_cupon = $db->easySelect(<<< END_OF_SQL
                    SELECT 
                        COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
                    FROM nyukin_denpyo d
                    INNER JOIN nyukin_denpyo_msi dm
                    ON dm.denpyo_no = d.denpyo_no
                    AND dm.denpyo_kbn = 5
                    AND dm.delete_flg = 0
                    WHERE d.uri_den_no = :uri_den_no
                    AND d.nyu_kbn     = 88
                    AND d.delete_flg  = 0
END_OF_SQL
                    , array('uri_den_no' => $uri_den_no));
            }
            // 入金伝票SELECT(貸倒損失額)
            $nyukin_baddebt = $db->easySelect(<<< END_OF_SQL
                SELECT 
                    COALESCE(sum(nyukin_prc),0) AS nyukin_prc 
                FROM nyukin_denpyo
                WHERE seikyu_no = :seikyu_no
                AND nyu_kbn     = 4
                AND delete_flg  = 0
END_OF_SQL
                , array('seikyu_no' => $seikyu_den_no));
            // 入金伝票SELECT(香典内金)
            $nyukin_kouden = $db->easySelect(<<< END_OF_SQL
                SELECT 
                    COALESCE(sum(nyukin_prc),0) AS nyukin_prc 
                FROM nyukin_denpyo
                WHERE uri_den_no = :uri_den_no
                AND nyu_kbn     = 5
                AND delete_flg  = 0
END_OF_SQL
                , array('uri_den_no' => $uri_den_no));
            // 入金金額に入金額を加算
            // 請求残高から入金額を減算
            $cnt += $db->easyExecute(<<< END_OF_SQL
                UPDATE seikyu_denpyo
                SET  nyukin_prc  = :nyukin_prc    -- 入金金額
                    ,uchikin_prc = :uchikin_prc   -- 内金金額
                    ,kouden_uchikin_prc = :kouden_uchikin_prc   -- 香典内金金額
                    ,bad_debt_loss_prc = :bad_debt_loss_prc   -- 貸倒損失額
                    ,cupon_prc = :cupon_prc   -- クーポン利用額
                    ,seikyu_zan = (
                              COALESCE(uri_prc_sum,0)
                            + COALESCE(uri_nebk_sum,0)
                            + COALESCE(uri_hepn_sum,0)
                            + COALESCE(hoshi_prc_sum,0)
                            + COALESCE(out_zei_prc,0)
                            + COALESCE(sougi_keiyaku_prc,0) + COALESCE(sougi_harai_prc,0)
                            + COALESCE(sougi_keiyaku_zei,0)
                            + COALESCE(sougi_wari_prc,0) + COALESCE(sougi_wari_zei,0)
                            + COALESCE(sougi_premium_service_prc,0) 
                            + COALESCE(sougi_early_use_cost,0) + COALESCE(sougi_early_use_cost_zei,0)   -- 早期利用費
                            + COALESCE(sougi_meigi_chg_cost,0) + COALESCE(sougi_meigi_chg_cost_zei,0)   -- 名義変更手数料
                            + COALESCE(sougi_tokuten_prc,0)         -- 金額充当
                            + COALESCE(n_free9,0)
                            + COALESCE(n_free10,0)
                            + COALESCE(n_free5,0)
                    )   - :nyukin_prc
                        - :uchikin_prc                       
                        - :kouden_uchikin_prc                
                        - :bad_debt_loss_prc            
                        - :cupon_prc      
                WHERE delete_flg = 0
                AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                , array('seikyu_den_no' => $seikyu_den_no
                      , 'nyukin_prc'    => $nyukin[0]['nyukin_prc']
                      , 'uchikin_prc'   => $nyukin_uchi[0]['nyukin_prc']
                      , 'cupon_prc'   => $nyukin_cupon[0]['nyukin_prc']
                      , 'bad_debt_loss_prc'   => $nyukin_baddebt[0]['nyukin_prc']
                      , 'kouden_uchikin_prc'   => $nyukin_kouden[0]['nyukin_prc']
                    )
                );
            // 請求伝票取得
            $seikyu2 = $db->easySelect(<<< END_OF_SQL
                SELECT 
                     sd.status_kbn             -- ステータス
                    ,sd.seikyu_zan             -- 請求残高
                    ,sd.seikyu_approval_status -- 請求書承認状況
                    ,sd.br_koza_no
                    ,sd.bumon_cd
                FROM seikyu_denpyo sd
                WHERE sd.seikyu_den_no = :seikyu_den_no
                AND   sd.delete_flg    = 0
END_OF_SQL
                , array('seikyu_den_no' => $seikyu_den_no));
            if($seikyu2[0]['status_kbn'] != 5 && $seikyu2[0]['seikyu_zan'] == 0){
                // ・該当請求伝票のステータスが"5:入金済"以外 かつ 請求残高が0円になった場合
                // 　ステータスを"5:入金済"、入金状況を"1:入金済"で更新する。
                $cnt += $db->easyExecute(<<< END_OF_SQL
                    UPDATE seikyu_denpyo
                    SET  status_kbn    = 5 -- 5:入金済
                        ,nyukin_status = 1 -- 1:入金済
                    WHERE delete_flg = 0
                    AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                    , array('seikyu_den_no' => $seikyu_den_no)
                );         
                if($seikyu2[0]['seikyu_approval_status'] == 0){
                    // 請求書承認状況が 0:未承認の場合は3:施行金額確定済に戻す
                    $cnt += $db->easyExecute(<<< END_OF_SQL
                        UPDATE seikyu_denpyo
                        SET  status_kbn  = 3 -- 3:施行金額確定済
                        WHERE delete_flg = 0
                        AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                        , array('seikyu_den_no' => $seikyu_den_no)
                    );       
                }
            }else if($seikyu2[0]['seikyu_zan'] != 0){
                // 請求残高がプラスの場合
                    // 　入金状況を0:残額有に戻す。
                    if($seikyu2[0]['seikyu_zan'] > 0){
                        $cnt = $db->easyExecute(<<< END_OF_SQL
                            UPDATE seikyu_denpyo
                            SET  nyukin_status = 0 -- 0:残額有
                            WHERE delete_flg = 0
                            AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                            , array('seikyu_den_no' => $seikyu_den_no)
                        );
                        // 部門データ取得
                        $select = DataMapper_BumonEx::find($db, array('bumon_cd' => $seikyu2[0]['bumon_cd']));
                        $bumonData = $select[0];
                        // 仮想口座管理マスタの存在チェック 
                        $select = DataMapper_BrKozaKanriMst::find($db, array('bumon_cd' => $bumonData['oya_bumon_cd']));
                        if (count($select) > 0) {                 
                            $koza_kanri_mst = $select[0];
                            $mstData = DataMapper_VirtualKouzaMst::findVKM($db, array('kouza_no' => $seikyu2[0]['br_koza_no']
                                    ,'bumon_cd' => $koza_kanri_mst['bumon_cd']
                                    ,'bank_cd' => $koza_kanri_mst['bank_cd']
                                    ,'shiten_cd' => $koza_kanri_mst['shiten_cd']
                                    ,'yokin_sbt' => $koza_kanri_mst['yokin_sbt']));
                            if (count($mstData) > 0 && $mstData[0]['wari_status_kbn'] == '0') {
                                // 口座が開放中であれば割当にする
                                $cnt += Logic_BrKozaUpdate::BrKozaNoKanriMstUpdate($db, $seikyu_den_no, $seikyu2[0]['br_koza_no'], 1);
                            }
                        }      
                    } else if ($seikyu2[0]['seikyu_zan'] < 0) {
                        $cnt = $db->easyExecute(<<< END_OF_SQL
                            UPDATE seikyu_denpyo
                            SET  nyukin_status = 1 -- 1:入金済
                            WHERE delete_flg = 0
                            AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                            , array('seikyu_den_no' => $seikyu_den_no)
                        );
                    }
                if($seikyu2[0]['seikyu_approval_status'] == 0){
                    // 請求書承認状況が 0:未承認の場合は3:施行金額確定済に戻す
                    $cnt += $db->easyExecute(<<< END_OF_SQL
                        UPDATE seikyu_denpyo
                        SET  status_kbn  = 3 -- 3:施行金額確定済
                        WHERE delete_flg = 0
                        AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                        , array('seikyu_den_no' => $seikyu_den_no)
                    );
                }else if($seikyu2[0]['seikyu_approval_status'] == 1){
                    // 請求書承認状況が 1:承認済の場合は4:請求確定に戻す
                    $cnt += $db->easyExecute(<<< END_OF_SQL
                        UPDATE seikyu_denpyo
                        SET  status_kbn  = 4 -- 4:請求確定
                        WHERE delete_flg = 0
                        AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                        , array('seikyu_den_no' => $seikyu_den_no)
                    );
                }
            }
            if($seikyu[0]['data_kbn'] == 1 || $seikyu[0]['data_kbn'] == 2){
                /**
                 * ■該当請求伝票のデータ区分が葬儀OR法事の場合の売上伝票、施行基本情報の更新
                 */
                // 該当請求伝票のデータ区分が葬儀の場合
                // 売上伝票取得 該当請求伝票の施行番号かつデータ区分が葬儀OR法事　または　（データ区分が供花供物　かつ　受注先区分が喪家）
                $seko_no = $seikyu[0]['seko_no'];
                $uriage = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                             ud.uri_den_no      -- 売上伝票番号
                            ,ud.seko_no         -- 施行番号
                            ,ud.data_kbn        -- データ区分
                            ,ud.juchusaki_kbn   -- 受注先区分
                            ,COALESCE(ud.uri_prc_sum,0)
                            + COALESCE(ud.uri_nebk_sum,0) 
                            + COALESCE(ud.uri_hepn_sum,0)
                            + COALESCE(ud.hoshi_prc_sum,0)
                            + COALESCE(ud.out_zei_prc,0)
                            + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                            + COALESCE(ud.sougi_keiyaku_zei,0) 
                            + COALESCE(ud.sougi_wari_prc,0) + COALESCE(sougi_wari_zei,0)
                            + COALESCE(ud.sougi_premium_service_prc,0)
                            + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                            + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                            + COALESCE(ud.sougi_tokuten_prc,0)          -- 金額充当
                            - COALESCE(ud.uchikin_prc,0)            -- 内金
                            + COALESCE(ud.n_free9,0)
                            + COALESCE(ud.n_free10,0)
                            + COALESCE(ud.n_free5,0)
                              AS uri_prc        -- 売上金額
                        FROM uriage_denpyo ud           -- 売上伝票
                        WHERE ud.delete_flg  = 0
                        AND  ud.seko_no  = :seko_no                                         -- 該当請求伝票の施行番号
                        AND (ud.data_kbn = :data_kbn OR (ud.data_kbn = 4 AND ud.juchusaki_kbn = 1)) -- データ区分が葬儀　または　（データ区分が供花供物　かつ　受注先区分が喪家）
                        ORDER BY ud.uri_prc_sum DESC, ud.uri_den_no
END_OF_SQL
                    , array('seko_no' => $seko_no, 'data_kbn' => $seikyu[0]['data_kbn']));
                // 売上伝票存在確認
                if(Msi_Sys_Utils::myCount($uriage) > 0){
                    /*
                     * 売上伝票更新処理
                     */
                    // ・検索した売上伝票の売上金額の多い順(同額の場合は売上伝票Noの昇順)で順番に次の処理を行う。
                    $nyukin_prc = $nyukin[0]['nyukin_prc']; // 共通
                    $baddebtloss_prc = $nyukin_baddebt[0]['nyukin_prc'];
                    // 対象が分割先の場合
                    // 分割先請求伝票から入金金額を取得
                    if(isset($seikyu[0]['bun_gas_kbn_num']) && $seikyu[0]['bun_gas_kbn_num'] == 2) {
                        $nyukin2 = $db->easySelect(<<< END_OF_SQL
                            SELECT 
                                COALESCE(SUM(nyukin_prc), 0) AS nyukin_prc 
                            FROM seikyu_denpyo 
                            WHERE bun_gas_seikyu_den_no = :bun_gas_seikyu_den_no 
                            AND delete_flg = 0;
END_OF_SQL
                            , array('bun_gas_seikyu_den_no' => $seikyu[0]['bun_gas_seikyu_den_no']));
                        $nyukin_prc = $nyukin2[0]['nyukin_prc'];
                    }
                    foreach ($uriage as $uri) {
                        $nyukin_prc_tmp = 0;
                        $baddebtloss_prc_tmp = 0;
                        if($nyukin_prc >= $uri['uri_prc']){
                            $nyukin_prc_tmp = $uri['uri_prc'];
                            $nyukin_prc = $nyukin_prc - $uri['uri_prc'];
                        } else {
                            $nyukin_prc_tmp = $nyukin_prc;
                            $nyukin_prc = 0;
                        }
                        if($baddebtloss_prc >= $uri['uri_prc']){
                            $baddebtloss_prc_tmp = $uri['uri_prc'];
                            $baddebtloss_prc = $baddebtloss_prc - $uri['uri_prc'];
                        }else{
                            $baddebtloss_prc_tmp = $baddebtloss_prc;
                            $baddebtloss_prc = 0;
                        }
                        // 入金金額に入金額を設定
                        // 請求残高から入金額を減算
                        $cnt += $db->easyExecute(<<< END_OF_SQL
                            UPDATE uriage_denpyo
                            SET  nyukin_prc = :nyukin_prc    -- 入金金額
                                ,bad_debt_loss_prc = :bad_debt_loss_prc    
                                ,seikyu_zan = (
                                          COALESCE(uri_prc_sum,0)
                                        + COALESCE(uri_nebk_sum,0)
                                        + COALESCE(uri_hepn_sum,0)
                                        + COALESCE(hoshi_prc_sum,0)
                                        + COALESCE(out_zei_prc,0)
                                        + COALESCE(sougi_keiyaku_prc,0) + COALESCE(sougi_harai_prc,0)
                                        + COALESCE(sougi_keiyaku_zei,0)
                                        + COALESCE(sougi_wari_prc,0) + COALESCE(sougi_wari_zei,0)
                                        + COALESCE(sougi_premium_service_prc,0) 
                                        + COALESCE(sougi_early_use_cost,0) + COALESCE(sougi_early_use_cost_zei,0)   -- 早期利用費
                                        + COALESCE(sougi_meigi_chg_cost,0) + COALESCE(sougi_meigi_chg_cost_zei,0)   -- 名義変更手数料
                                        + COALESCE(sougi_tokuten_prc,0)         -- 金額充当
                                        + COALESCE(n_free9,0)
                                        + COALESCE(n_free10,0)
                                        + COALESCE(n_free5,0)
                                )   - :nyukin_prc     
                                    - :bad_debt_loss_prc
                                - COALESCE(uchikin_prc,0)
                                - COALESCE(kouden_uchikin_prc,0)
                                - COALESCE(cupon_prc,0)-- 請求残高
                            WHERE delete_flg = 0
                            AND uri_den_no = :uri_den_no
END_OF_SQL
                            , array('uri_den_no' => $uri['uri_den_no']
                                  , 'nyukin_prc' => $nyukin_prc_tmp
                                  , 'bad_debt_loss_prc' => $baddebtloss_prc_tmp)
                        );
                    }
                    // 売上伝票取得
                    $uriage2 = $db->easySelect(<<< END_OF_SQL
                            SELECT 
                                 ud.status_kbn             -- ステータス
                                ,ud.seikyu_zan             -- 請求残高
                                ,ud.uri_den_no             -- 売上伝票番号
                            FROM uriage_denpyo ud          -- 売上伝票
                            WHERE ud.delete_flg  = 0
                            AND  ud.seko_no = :seko_no                                          -- 該当請求伝票の施行番号
                            AND (ud.data_kbn = :data_kbn OR (ud.data_kbn = 4 AND ud.juchusaki_kbn = 1)) -- データ区分が葬儀　または　（データ区分が供花供物　かつ　受注先区分が喪家）
                            ORDER BY ud.uri_prc_sum, ud.uri_den_no
END_OF_SQL
                        , array('seko_no' => $seko_no, 'data_kbn' => $seikyu[0]['data_kbn']));
                    foreach ($uriage2 as $uri2) {
                        if($uri2['status_kbn'] != 5 && $uri2['seikyu_zan'] == 0){
                            // ・検索した売上伝票のステータスが"5:入金済"以外 かつ 請求残高が0円になった場合
                            // 　ステータスを"5:入金済"で更新する。
                            $cnt += $db->easyExecute(<<< END_OF_SQL
                                UPDATE uriage_denpyo
                                SET  status_kbn  = 5 -- 5:入金済
                                WHERE delete_flg = 0
                                AND uri_den_no = :uri_den_no
END_OF_SQL
                                , array('uri_den_no' => $uri2['uri_den_no'])
                            );
                            if($seikyu2[0]['seikyu_approval_status'] == 0){
                                // 請求書承認状況が 0:未承認の場合は3:施行金額確定済戻す
                                $cnt += $db->easyExecute(<<< END_OF_SQL
                                    UPDATE uriage_denpyo
                                    SET  status_kbn  = 3 -- 3:施行金額確定済戻す
                                    WHERE delete_flg = 0
                                    AND uri_den_no = :uri_den_no
END_OF_SQL
                                    , array('uri_den_no' => $uri2['uri_den_no'])
                                );
                            }
                        }else if($uri2['status_kbn'] == 5 && $uri2['seikyu_zan'] != 0) {
                            // ・検索した売上伝票のステータスが"5:入金済" かつ 請求残高が0円ではない場合
                            // 請求書承認状況に関わらず3:施行金額確定済戻す
                            $cnt += $db->easyExecute(<<< END_OF_SQL
                                UPDATE uriage_denpyo
                                SET  status_kbn  = 3 -- 3:施行金額確定済戻す
                                WHERE delete_flg = 0
                                AND uri_den_no = :uri_den_no
END_OF_SQL
                                , array('uri_den_no' => $uri2['uri_den_no'])
                            );
                        }
                    }
                    // 売上伝票取得
                    $uriage3 = $db->easySelect(<<< END_OF_SQL
                            SELECT 
                                 ud.status_kbn             -- ステータス
                                ,ud.data_kbn               -- データ区分
                                ,ud.seko_prc_kakute_kbn    -- 施行金額確定区分
                            FROM uriage_denpyo ud          -- 売上伝票
                            WHERE ud.delete_flg  = 0
                            AND  ud.seko_no = :seko_no                                          -- 該当請求伝票の施行番号
                            AND (ud.data_kbn = :data_kbn OR (ud.data_kbn = 4 AND ud.juchusaki_kbn = 1)) -- データ区分が葬儀　または　（データ区分が供花供物　かつ　受注先区分が喪家）
                            ORDER BY ud.uri_prc_sum, ud.uri_den_no
END_OF_SQL
                        , array('seko_no' => $seko_no, 'data_kbn' => $seikyu[0]['data_kbn']));
                    // 売上伝票のステータス確認
                    $status_kbn = false;
                    foreach ($uriage3 as $uri3) {
                        if($uri3['status_kbn'] == 5){
                            $status_kbn = true;
                        }else{
                            $status_kbn = false; // 検索した売上伝票のステータスが一つでも"5:入金済"以外
                            break;
                        }
                    }
                    // 葬儀OR法事のデータ区分が含まれているか
                    $uri_data_kbn = null;
                    $seko_prc_kakute_kbn = null;
                    foreach ($uriage3 as $uri3_2) {
                        if($uri3_2['data_kbn'] == 1 || $uri3_2['data_kbn'] == 2){
                            $uri_data_kbn = $uri3_2['data_kbn'];
                            $seko_prc_kakute_kbn = $uri3_2['seko_prc_kakute_kbn'];
                        }
                    }
                    if($status_kbn){
                        // ・検索した売上伝票のステータスが全て"5:入金済"となった場合
                        // 　施行基本情報のステータスを"5:入金済"で更新する。
                        $cnt += $db->easyExecute(<<< END_OF_SQL
                            UPDATE seko_kihon_info
                            SET  status_kbn  = 5 -- 5:入金済
                            WHERE delete_flg = 0
                            AND seko_no      = :seko_no
END_OF_SQL
                            , array('seko_no' => $seko_no)
                        );
                    }else{
                        // 施行基本情報取得
                        $sekoKihon = $db->easySelect(<<< END_OF_SQL
                            SELECT 
                                status_kbn
                            FROM seko_kihon_info
                            WHERE delete_flg = 0
                            AND seko_no      = :seko_no
END_OF_SQL
                        , array('seko_no' => $seko_no));
                        if($sekoKihon[0]['status_kbn'] == 5){
                            // ・施行基本情報のステータスが"5:入金済" かつ 検索した売上伝票のステータスが全て"5:入金済"ではない場合
                            if($seikyu2[0]['seikyu_approval_status'] == 1){
                                // 　該当請求伝票の請求書承認状況が"1:承認済"の場合
                                // 　施行基本情報のステータスを"4:請求確定"に戻す。
                                $cnt += $db->easyExecute(<<< END_OF_SQL
                                    UPDATE seko_kihon_info
                                    SET  status_kbn  = 4 -- 4:請求確定
                                    WHERE delete_flg = 0
                                    AND seko_no      = :seko_no
END_OF_SQL
                                    , array('seko_no' => $seko_no)
                                );
                            }else if($seikyu2[0]['seikyu_approval_status'] == 0 && ($uri_data_kbn == 1 || $uri_data_kbn == 2) && $seko_prc_kakute_kbn == 1){
                                // 　該当請求伝票の請求書承認状況が"0:未承認"の場合
                                // 　かつ 検索した売上伝票のデータ区分が葬儀OR法事の施行金額確定区分が"1:確定済"の場合
                                // 　施行基本情報のステータスを"3:施行金額確定済"に戻す。
                                $cnt += $db->easyExecute(<<< END_OF_SQL
                                    UPDATE seko_kihon_info
                                    SET  status_kbn  = 3 -- 3:施行金額確定済
                                    WHERE delete_flg = 0
                                    AND seko_no      = :seko_no
END_OF_SQL
                                    , array('seko_no' => $seko_no)
                                );
                            }else if($seikyu2[0]['seikyu_approval_status'] == 0 && ($uri_data_kbn == 1 || $uri_data_kbn == 2) && $seko_prc_kakute_kbn == 0){
                                // 　該当請求伝票の請求書承認状況が"0:未承認"の場合
                                // 　かつ 検索した売上伝票のデータ区分が葬儀OR法事の施行金額確定区分が"0:未確定"の場合
                                // 　施行基本情報のステータスを"2:見積確定済"に戻す。
                                $cnt += $db->easyExecute(<<< END_OF_SQL
                                    UPDATE seko_kihon_info
                                    SET  status_kbn  = 2 -- 2:見積確定済
                                    WHERE delete_flg = 0
                                    AND seko_no      = :seko_no
END_OF_SQL
                                    , array('seko_no' => $seko_no)
                                );
                            }
                        }
                    }
                }else{
                    throw new Msi_Sys_Exception_InputException("売上伝票が存在しません");
                }
            }else if($seikyu[0]['data_kbn'] == 4){
                /*
                 * ■該当請求伝票のデータ区分が供花供物の場合の売上伝票、施行基本情報の更新
                 */
                // ・次の条件で対象の売上伝票を検索する
                // 　該当請求伝票の分割合算区分が"0:通常"の場合は、
                //   　該当の売上伝票No.に一致する売上伝票
                $seko_no = $seikyu[0]['seko_no'];
                $uriage = array();
                if($seikyu[0]['bun_gas_kbn_num'] == 0){
                    $uriage = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                             ud.uri_den_no      -- 売上伝票番号
                            ,ud.seko_no         -- 施行番号
                            ,ud.data_kbn        -- データ区分
                            ,ud.juchusaki_kbn   -- 受注先区分
                            ,COALESCE(ud.uri_prc_sum,0)
                            + COALESCE(ud.uri_nebk_sum,0) 
                            + COALESCE(ud.uri_hepn_sum,0)
                            + COALESCE(ud.hoshi_prc_sum,0)
                            + COALESCE(ud.out_zei_prc,0)
                            + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                            + COALESCE(ud.sougi_keiyaku_zei,0) 
                            + COALESCE(ud.sougi_wari_prc,0) + COALESCE(ud.sougi_wari_zei,0)
                            + COALESCE(ud.sougi_premium_service_prc,0)
                            + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                            + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                            + COALESCE(ud.sougi_tokuten_prc,0)          -- 金額充当            
                            + COALESCE(ud.n_free9,0)
                            + COALESCE(ud.n_free10,0)
                            + COALESCE(ud.n_free5,0)
                              AS uri_prc                            -- 売上金額
                        FROM uriage_denpyo ud   -- 売上伝票
                        WHERE ud.delete_flg = 0
                        AND  ud.uri_den_no  = :uri_den_no   -- 該当請求伝票の施行番号
                        ORDER BY ud.uri_prc_sum DESC, ud.uri_den_no
END_OF_SQL
                        , array('uri_den_no' => $seikyu[0]['uri_den_no']));
                }else if($seikyu[0]['bun_gas_kbn_num'] == 1){
                    // 分割元
                    $uriage = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                             ud.uri_den_no      -- 売上伝票番号
                            ,ud.seko_no         -- 施行番号
                            ,ud.data_kbn        -- データ区分
                            ,ud.juchusaki_kbn   -- 受注先区分
                            ,COALESCE(ud.uri_prc_sum,0)
                            + COALESCE(ud.uri_nebk_sum,0) 
                            + COALESCE(ud.uri_hepn_sum,0)
                            + COALESCE(ud.hoshi_prc_sum,0)
                            + COALESCE(ud.out_zei_prc,0)
                            + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                            + COALESCE(ud.sougi_keiyaku_zei,0) 
                            + COALESCE(ud.sougi_wari_prc,0) + COALESCE(ud.sougi_wari_zei,0)
                            + COALESCE(ud.sougi_premium_service_prc,0)
                            + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                            + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                            + COALESCE(ud.sougi_tokuten_prc,0)          -- 金額充当    
                            + COALESCE(ud.n_free9,0)
                            + COALESCE(ud.n_free10,0)
                            + COALESCE(ud.n_free5,0)
                              AS uri_prc                            -- 売上金額
                        FROM uriage_denpyo ud   -- 売上伝票
                        WHERE ud.delete_flg = 0
                        AND  ud.uri_den_no  = :uri_den_no   -- 該当請求伝票の施行番号
                        ORDER BY ud.uri_prc_sum DESC, ud.uri_den_no
END_OF_SQL
                        , array('uri_den_no' => $seikyu[0]['uri_den_no']));
                }else if($seikyu[0]['bun_gas_kbn_num'] == 2){
                    // 　該当請求伝票の分割合算区分が"2:分割先"の場合は、
                    //   　分割合算請求伝票№の請求伝票Noに一致する請求伝票(分割元)の売上伝票No.に一致する売上伝票
                    $seikyu2 = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                            uri_den_no 
                        FROM seikyu_denpyo 
                        WHERE delete_flg  = 0
                        AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                        , array('seikyu_den_no' => $seikyu[0]['bun_gas_seikyu_den_no']));
                    $uriage = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                             ud.uri_den_no      -- 売上伝票番号
                            ,ud.seko_no         -- 施行番号
                            ,ud.data_kbn        -- データ区分
                            ,ud.juchusaki_kbn   -- 受注先区分
                            ,COALESCE(ud.uri_prc_sum,0)
                            + COALESCE(ud.uri_nebk_sum,0) 
                            + COALESCE(ud.uri_hepn_sum,0)
                            + COALESCE(ud.hoshi_prc_sum,0)
                            + COALESCE(ud.out_zei_prc,0)
                            + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                            + COALESCE(ud.sougi_keiyaku_zei,0)
                            + COALESCE(ud.sougi_wari_prc,0) + COALESCE(ud.sougi_wari_zei,0)
                            + COALESCE(ud.sougi_premium_service_prc,0) 
                            + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                            + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                            + COALESCE(ud.sougi_tokuten_prc,0)      -- 金額充当    
                            + COALESCE(ud.n_free9,0)
                            + COALESCE(ud.n_free10,0)
                            + COALESCE(ud.n_free5,0)
                              AS uri_prc                            -- 売上金額
                        FROM uriage_denpyo ud   -- 売上伝票
                        WHERE ud.delete_flg = 0
                        AND  ud.uri_den_no  = :uri_den_no   -- 該当請求伝票の施行番号
                        ORDER BY ud.uri_prc_sum DESC, ud.uri_den_no
END_OF_SQL
                        , array('uri_den_no' => $seikyu2[0]['uri_den_no']));
                }else if($seikyu[0]['bun_gas_kbn_num'] == 10){
                    // 合算元の場合は合算先から遡って売上伝票を取得する
                    $seikyu2 = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                            seikyu_den_no 
                        FROM seikyu_denpyo 
                        WHERE delete_flg  = 0
                        AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                        , array('seikyu_den_no' => $seikyu[0]['bun_gas_seikyu_den_no']));
                    $uriage = $db->easySelect(<<< END_OF_SQL
                            SELECT 
                                 ud.uri_den_no      -- 売上伝票番号
                                ,ud.seko_no         -- 施行番号
                                ,ud.data_kbn        -- データ区分
                                ,ud.juchusaki_kbn   -- 受注先区分
                                ,COALESCE(ud.uri_prc_sum,0)
                                + COALESCE(ud.uri_nebk_sum,0) 
                                + COALESCE(ud.uri_hepn_sum,0)
                                + COALESCE(ud.hoshi_prc_sum,0)
                                + COALESCE(ud.out_zei_prc,0)
                                + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                                + COALESCE(ud.sougi_keiyaku_zei,0) 
                                + COALESCE(ud.sougi_wari_prc,0) + COALESCE(ud.sougi_wari_zei,0)
                                + COALESCE(ud.sougi_premium_service_prc,0)
                                + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                                + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                                + COALESCE(ud.sougi_tokuten_prc,0)      -- 金額充当
                                + COALESCE(ud.n_free9,0)
                                + COALESCE(ud.n_free10,0)
                                + COALESCE(ud.n_free5,0)
                                  AS uri_prc                            -- 売上金額
                            FROM seikyu_denpyo sd   -- 請求伝票
                            INNER JOIN uriage_denpyo ud
                            ON ud.uri_den_no = sd.uri_den_no
                            AND ud.delete_flg = 0
                            WHERE sd.delete_flg  = 0
                            AND bun_gas_seikyu_den_no = :bun_gas_seikyu_den_no
END_OF_SQL
                            , array('bun_gas_seikyu_den_no' => $seikyu2[0]['seikyu_den_no']));
                }else if($seikyu[0]['bun_gas_kbn_num'] == 20){
                    // 該当請求伝票の分割合算区分が"20:合算先"の場合は、
                    // 該当の請求伝票Noが分割合算請求伝票№に一致する請求伝票(合算元)を検索し、
                    // さらにその請求伝票(合算元)の売上伝票No.に一致する売上伝票　※複数件該当する場合がある
                    // 移行データの場合は自身の請求伝票Noの頭二桁を0にしてtext_free5で検索する
                    if (isset($seikyu[0]['d_import_kbn']) && $seikyu[0]['d_import_kbn'] == '95') {
                        $sub_seikyu_den_no = '00'.substr($seikyu_den_no, 2);
                        $uriage = $db->easySelect(<<< END_OF_SQL
                            SELECT 
                                 ud.uri_den_no      -- 売上伝票番号
                                ,ud.seko_no         -- 施行番号
                                ,ud.data_kbn        -- データ区分
                                ,ud.juchusaki_kbn   -- 受注先区分
                                ,COALESCE(ud.uri_prc_sum,0)
                                + COALESCE(ud.uri_nebk_sum,0) 
                                + COALESCE(ud.uri_hepn_sum,0)
                                + COALESCE(ud.hoshi_prc_sum,0)
                                + COALESCE(ud.out_zei_prc,0)
                                + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                                + COALESCE(ud.sougi_keiyaku_zei,0) 
                                + COALESCE(ud.sougi_wari_prc,0) + COALESCE(ud.sougi_wari_zei,0)
                                + COALESCE(ud.sougi_premium_service_prc,0)     
                                + COALESCE(ud.sougi_harai_prc,0)
                                + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                                + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                                + COALESCE(ud.sougi_tokuten_prc,0)      -- 金額充当
                                + COALESCE(ud.n_free9,0)
                                + COALESCE(ud.n_free10,0)
                                + COALESCE(ud.n_free5,0)
                                  AS uri_prc                            -- 売上金額
                            FROM uriage_denpyo ud
                            WHERE ud.delete_flg  = 0
                            AND ud.text_free5 = :seikyu_den_no
END_OF_SQL
                            , array('seikyu_den_no' => $sub_seikyu_den_no));
                    } else {
                        $uriage = $db->easySelect(<<< END_OF_SQL
                            SELECT 
                                 ud.uri_den_no      -- 売上伝票番号
                                ,ud.seko_no         -- 施行番号
                                ,ud.data_kbn        -- データ区分
                                ,ud.juchusaki_kbn   -- 受注先区分
                                ,COALESCE(ud.uri_prc_sum,0)
                                + COALESCE(ud.uri_nebk_sum,0) 
                                + COALESCE(ud.uri_hepn_sum,0)
                                + COALESCE(ud.hoshi_prc_sum,0)
                                + COALESCE(ud.out_zei_prc,0)
                                + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                                + COALESCE(ud.sougi_keiyaku_zei,0) 
                                + COALESCE(ud.sougi_wari_prc,0) + COALESCE(ud.sougi_wari_zei,0)
                                + COALESCE(ud.sougi_premium_service_prc,0)
                                + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                                + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                                + COALESCE(ud.sougi_tokuten_prc,0)      -- 金額充当
                                + COALESCE(ud.n_free9,0)
                                + COALESCE(ud.n_free10,0)
                                + COALESCE(ud.n_free5,0)
                                  AS uri_prc                            -- 売上金額
                            FROM seikyu_denpyo sd   -- 請求伝票
                            INNER JOIN uriage_denpyo ud
                            ON ud.uri_den_no = sd.uri_den_no
                            AND ud.delete_flg = 0
                            WHERE sd.delete_flg  = 0
                            AND bun_gas_seikyu_den_no = :bun_gas_seikyu_den_no
END_OF_SQL
                            , array('bun_gas_seikyu_den_no' => $seikyu_den_no));
                    }
                }
                // 売上伝票存在確認
                if(Msi_Sys_Utils::myCount($uriage) > 0){
                    //・検索した売上伝票の売上金額の多い順(同額の場合は売上伝票Noの昇順)で順番に次の処理を行う。
                    //　 入金金額に入金額を加算
                    // 　請求残高から入金額を減算
                    $nyukin_prc = $nyukin[0]['nyukin_prc']; // 共通
                    $baddebtloss_prc = $nyukin_baddebt[0]['nyukin_prc']; // 共通
                    if($seikyu[0]['bun_gas_kbn_num'] == 2){
                        // 2:分割先の場合に分割先請求伝票の入金金額を取得
                        $bun_gas_seikyu_den_no = $seikyu[0]['bun_gas_seikyu_den_no'];
                        $seikyu3 = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                             COALESCE(SUM(sd.nyukin_prc),0) AS nyukin_prc
                            ,MIN(sd.seikyu_approval_status) AS seikyu_approval_status
                        FROM seikyu_denpyo sd   -- 請求伝票
                        WHERE sd.delete_flg  = 0
                        AND bun_gas_seikyu_den_no = :bun_gas_seikyu_den_no
END_OF_SQL
                            , array('bun_gas_seikyu_den_no' => $bun_gas_seikyu_den_no));
                        if(Msi_Sys_Utils::myCount($seikyu3) > 0){
                            // 入金金額に入金額を設定
                            // 請求残高から入金額を減算
                            $cnt += $db->easyExecute(<<< END_OF_SQL
                                UPDATE uriage_denpyo
                                SET  nyukin_prc = :nyukin_prc    -- 入金金額
                                    ,seikyu_zan = (
                                              COALESCE(uri_prc_sum,0)
                                            + COALESCE(uri_nebk_sum,0)
                                            + COALESCE(uri_hepn_sum,0)
                                            + COALESCE(hoshi_prc_sum,0)
                                            + COALESCE(out_zei_prc,0)
                                            + COALESCE(sougi_keiyaku_prc,0) + COALESCE(sougi_harai_prc,0)
                                            + COALESCE(sougi_keiyaku_zei,0)
                                            + COALESCE(sougi_wari_prc,0) + COALESCE(sougi_wari_zei,0)
                                            + COALESCE(sougi_premium_service_prc,0) 
                                            + COALESCE(sougi_early_use_cost,0) + COALESCE(sougi_early_use_cost_zei,0)   -- 早期利用費
                                            + COALESCE(sougi_meigi_chg_cost,0) + COALESCE(sougi_meigi_chg_cost_zei,0)   -- 名義変更手数料
                                            + COALESCE(sougi_tokuten_prc,0)     -- 金額充当
                                            + COALESCE(n_free9,0)
                                            + COALESCE(n_free10,0)
                                            + COALESCE(n_free5,0)
                                    )   - :nyukin_prc                           -- 請求残高
                                        - COALESCE(uchikin_prc,0)               -- 内金
                                        - COALESCE(kouden_uchikin_prc,0)      
                                        - COALESCE(cupon_prc,0)      
                                WHERE delete_flg = 0
                            AND uri_den_no = :uri_den_no
END_OF_SQL
                                , array('uri_den_no' => $uri_den_no
                                      , 'nyukin_prc' => $seikyu3[0]['nyukin_prc']
                                      )
                            );
                        }
                    }else{
                        foreach ($uriage as $uri) {
                            $nyukin_prc_tmp = 0;
                            $baddebtloss_prc_tmp = 0;
                            if($nyukin_prc >= $uri['uri_prc']){
                                $nyukin_prc_tmp = $uri['uri_prc'];
                                $nyukin_prc = $nyukin_prc - $uri['uri_prc'];
                            }else{
                                $nyukin_prc_tmp = $nyukin_prc;
                                $nyukin_prc = 0;
                            }
                            if($baddebtloss_prc >= $uri['uri_prc']){
                                $baddebtloss_prc_tmp = $uri['uri_prc'];
                                $baddebtloss_prc = $baddebtloss_prc - $uri['uri_prc'];
                            }else{
                                $baddebtloss_prc_tmp = $baddebtloss_prc;
                                $baddebtloss_prc = 0;
                            }
                            // 入金金額に入金額を設定
                            // 請求残高から入金額を減算
                            $cnt += $db->easyExecute(<<< END_OF_SQL
                                UPDATE uriage_denpyo
                                SET  nyukin_prc = :nyukin_prc    -- 入金金額
                                    ,bad_debt_loss_prc = :bad_debt_loss_prc    
                                    ,seikyu_zan = (
                                              COALESCE(uri_prc_sum,0)
                                            + COALESCE(uri_nebk_sum,0)
                                            + COALESCE(uri_hepn_sum,0)
                                            + COALESCE(hoshi_prc_sum,0)
                                            + COALESCE(out_zei_prc,0)
                                            + COALESCE(sougi_keiyaku_prc,0) + COALESCE(sougi_harai_prc,0)
                                            + COALESCE(sougi_keiyaku_zei,0)  
                                            + COALESCE(sougi_wari_prc,0) + COALESCE(sougi_wari_zei,0)
                                            + COALESCE(sougi_premium_service_prc,0)
                                            + COALESCE(sougi_early_use_cost,0) + COALESCE(sougi_early_use_cost_zei,0)   -- 早期利用費
                                            + COALESCE(sougi_meigi_chg_cost,0) + COALESCE(sougi_meigi_chg_cost_zei,0)   -- 名義変更手数料
                                            + COALESCE(sougi_tokuten_prc,0)     -- 金額充当
                                            + COALESCE(n_free9,0)
                                            + COALESCE(n_free10,0)
                                            + COALESCE(n_free5,0)
                                    )   - :nyukin_prc                           -- 請求残高
                                        - :bad_debt_loss_prc
                                        - COALESCE(uchikin_prc,0)               -- 内金
                                        - COALESCE(kouden_uchikin_prc,0)      
                                        - COALESCE(cupon_prc,0)      
                                WHERE delete_flg = 0
                            AND uri_den_no = :uri_den_no
END_OF_SQL
                                , array('uri_den_no' => $uri['uri_den_no']
                                      , 'nyukin_prc' => $nyukin_prc_tmp
                                      , 'bad_debt_loss_prc' => $baddebtloss_prc_tmp)
                            );
                        }
                    }
                    // 売上伝票取得
                    $uriage2 = $db->easySelect(<<< END_OF_SQL
                            SELECT 
                                 ud.status_kbn             -- ステータス
                                ,ud.seikyu_zan             -- 請求残高
                                ,ud.uri_den_no             -- 売上伝票番号
                            FROM uriage_denpyo ud          -- 売上伝票
                            WHERE ud.delete_flg  = 0
                            AND ud.seko_no = :seko_no                                          -- 該当請求伝票の施行番号
                            AND ud.data_kbn =4
                            ORDER BY ud.uri_prc_sum, ud.uri_den_no
END_OF_SQL
                        , array('seko_no' => $seko_no));
                    foreach ($uriage2 as $uri2) {
                        if($uri2['status_kbn'] != 5 && $uri2['seikyu_zan'] == 0){
                            // ・検索した売上伝票のステータスが"5:入金済"以外 かつ 請求残高が0円になった場合
                            // 　ステータスを"5:入金済"で更新する。
                            $cnt += $db->easyExecute(<<< END_OF_SQL
                                UPDATE uriage_denpyo
                                SET  status_kbn    = 5 -- 5:入金済
                                WHERE delete_flg = 0
                                AND uri_den_no = :uri_den_no
END_OF_SQL
                                , array('uri_den_no' => $uri2['uri_den_no'])
                            );
                            if($seikyu[0]['bun_gas_kbn_num'] == 2){
                                if($seikyu3[0]['seikyu_approval_status'] == 0){
                                    // 請求書承認状況が 0:未承認の場合は3:施行金額確定済戻す
                                    $cnt += $db->easyExecute(<<< END_OF_SQL
                                        UPDATE uriage_denpyo
                                        SET  status_kbn  = 3 -- 3:施行金額確定済戻す
                                        WHERE delete_flg = 0
                                        AND uri_den_no = :uri_den_no
END_OF_SQL
                                        , array('uri_den_no' => $uri2['uri_den_no'])
                                    );
                                }
                            }else{
                                if($seikyu2[0]['seikyu_approval_status'] == 0){
                                    // 請求書承認状況が 0:未承認の場合は3:施行金額確定済戻す
                                    $cnt += $db->easyExecute(<<< END_OF_SQL
                                        UPDATE uriage_denpyo
                                        SET  status_kbn  = 3 -- 3:施行金額確定済戻す
                                        WHERE delete_flg = 0
                                        AND uri_den_no = :uri_den_no
END_OF_SQL
                                        , array('uri_den_no' => $uri2['uri_den_no'])
                                    );
                                }
                            }
                        }else if($uri2['status_kbn'] == 5 && $uri2['seikyu_zan'] != 0) {
                            // ・検索した売上伝票のステータスが"5:入金済" かつ 請求残高が0円ではない場合
                            if($seikyu2[0]['seikyu_approval_status'] == 0){
                                // 請求書承認状況が 0:未承認の場合は3:施行金額確定済戻す
                                $cnt += $db->easyExecute(<<< END_OF_SQL
                                    UPDATE uriage_denpyo
                                    SET  status_kbn  = 3 -- 3:施行金額確定済戻す
                                    WHERE delete_flg = 0
                                    AND uri_den_no = :uri_den_no
END_OF_SQL
                                    , array('uri_den_no' => $uri2['uri_den_no'])
                                );
                            }else if($seikyu2[0]['seikyu_approval_status'] == 1){
                                // 請求書承認状況が 1:承認済の場合は4:請求確定に戻す
                                $cnt += $db->easyExecute(<<< END_OF_SQL
                                    UPDATE uriage_denpyo
                                    SET  status_kbn  = 4 -- 4:請求確定に戻す
                                    WHERE delete_flg = 0
                                    AND uri_den_no = :uri_den_no
END_OF_SQL
                                    , array('uri_den_no' => $uri2['uri_den_no'])
                                );
                            }
                        }
                    }
                }else{
                    throw new Msi_Sys_Exception_InputException("売上伝票が存在しません");
                }
            }else if($seikyu[0]['data_kbn'] == 3){
                /*
                 * ■該当請求伝票のデータ区分が供花供物の場合の売上伝票、施行基本情報の更新
                 */
                // ・次の条件で対象の売上伝票を検索する
                // 　該当請求伝票の分割合算区分が"0:通常"の場合は、
                //   　該当の売上伝票No.に一致する売上伝票
                $seko_no = $seikyu[0]['seko_no'];
                $uriage = array();
                if($seikyu[0]['bun_gas_kbn_num'] == 0){
                    $uriage = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                             ud.uri_den_no      -- 売上伝票番号
                            ,ud.seko_no         -- 施行番号
                            ,ud.data_kbn        -- データ区分
                            ,ud.juchusaki_kbn   -- 受注先区分
                            ,COALESCE(ud.uri_prc_sum,0)
                            + COALESCE(ud.uri_nebk_sum,0) 
                            + COALESCE(ud.uri_hepn_sum,0)
                            + COALESCE(ud.hoshi_prc_sum,0)
                            + COALESCE(ud.out_zei_prc,0)
                            + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                            + COALESCE(ud.sougi_keiyaku_zei,0) 
                            + COALESCE(ud.sougi_wari_prc,0) + COALESCE(ud.sougi_wari_zei,0)
                            + COALESCE(ud.sougi_premium_service_prc,0)
                            + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                            + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                            + COALESCE(ud.sougi_tokuten_prc,0)      -- 金額充当
                            + COALESCE(ud.n_free9,0)
                            + COALESCE(ud.n_free10,0)
                            + COALESCE(ud.n_free5,0)
                              AS uri_prc                            -- 売上金額
                        FROM uriage_denpyo ud   -- 売上伝票
                        WHERE ud.delete_flg = 0
                        AND  ud.uri_den_no  = :uri_den_no   -- 該当請求伝票の施行番号
                        ORDER BY ud.uri_prc_sum DESC, ud.uri_den_no
END_OF_SQL
                        , array('uri_den_no' => $seikyu[0]['uri_den_no']));
                }else if($seikyu[0]['bun_gas_kbn_num'] == 2){
                    // 　該当請求伝票の分割合算区分が"2:分割先"の場合は、
                    //   　分割合算請求伝票№の請求伝票Noに一致する請求伝票(分割元)の売上伝票No.に一致する売上伝票
                    $seikyu2 = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                            uri_den_no 
                        FROM seikyu_denpyo 
                        WHERE delete_flg  = 0
                        AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                        , array('seikyu_den_no' => $seikyu[0]['bun_gas_seikyu_den_no']));
                    $uriage = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                             ud.uri_den_no      -- 売上伝票番号
                            ,ud.seko_no         -- 施行番号
                            ,ud.data_kbn        -- データ区分
                            ,ud.juchusaki_kbn   -- 受注先区分
                            ,COALESCE(ud.uri_prc_sum,0)
                            + COALESCE(ud.uri_nebk_sum,0) 
                            + COALESCE(ud.uri_hepn_sum,0)
                            + COALESCE(ud.hoshi_prc_sum,0)
                            + COALESCE(ud.out_zei_prc,0)
                            + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                            + COALESCE(ud.sougi_keiyaku_zei,0)    
                            + COALESCE(ud.sougi_wari_prc,0) + COALESCE(ud.sougi_wari_zei,0)
                            + COALESCE(ud.sougi_premium_service_prc,0) 
                            + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                            + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                            + COALESCE(ud.sougi_tokuten_prc,0)      -- 金額充当
                            + COALESCE(ud.n_free9,0)
                            + COALESCE(ud.n_free10,0)
                            + COALESCE(ud.n_free5,0)
                              AS uri_prc                            -- 売上金額
                        FROM uriage_denpyo ud   -- 売上伝票
                        WHERE ud.delete_flg = 0
                        AND  ud.uri_den_no  = :uri_den_no   -- 該当請求伝票の施行番号
                        ORDER BY ud.uri_prc_sum DESC, ud.uri_den_no
END_OF_SQL
                        , array('uri_den_no' => $seikyu2[0]['uri_den_no']));
                }else if($seikyu[0]['bun_gas_kbn_num'] == 20){
                    //   該当請求伝票の分割合算区分が"20:合算先"の場合は、
                    // 　　該当の請求伝票Noが分割合算請求伝票№に一致する請求伝票(合算元)を検索し、
                    // 　　さらにその請求伝票(合算元)の売上伝票No.に一致する売上伝票　※複数件該当する場合がある
                    $uriage = $db->easySelect(<<< END_OF_SQL
                        SELECT 
                             ud.uri_den_no      -- 売上伝票番号
                            ,ud.seko_no         -- 施行番号
                            ,ud.data_kbn        -- データ区分
                            ,ud.juchusaki_kbn   -- 受注先区分
                            ,COALESCE(ud.uri_prc_sum,0)
                            + COALESCE(ud.uri_nebk_sum,0) 
                            + COALESCE(ud.uri_hepn_sum,0)
                            + COALESCE(ud.hoshi_prc_sum,0)
                            + COALESCE(ud.out_zei_prc,0)
                            + COALESCE(ud.sougi_keiyaku_prc,0) + COALESCE(ud.sougi_harai_prc,0)
                            + COALESCE(ud.sougi_keiyaku_zei,0) 
                            + COALESCE(ud.sougi_wari_prc,0) + COALESCE(ud.sougi_wari_zei,0)
                            + COALESCE(ud.sougi_premium_service_prc,0)
                            + COALESCE(ud.sougi_early_use_cost,0) + COALESCE(ud.sougi_early_use_cost_zei,0) -- 早期利用費
                            + COALESCE(ud.sougi_meigi_chg_cost,0) + COALESCE(ud.sougi_meigi_chg_cost_zei,0) -- 名義変更手数料
                            + COALESCE(ud.sougi_tokuten_prc,0)      -- 金額充当   
                            + COALESCE(ud.n_free9,0)
                            + COALESCE(ud.n_free10,0)
                            + COALESCE(ud.n_free5,0)
                              AS uri_prc                            -- 売上金額
                        FROM seikyu_denpyo sd   -- 請求伝票
                        INNER JOIN uriage_denpyo ud
                        ON ud.uri_den_no = sd.uri_den_no
                        AND ud.delete_flg = 0
                        WHERE sd.delete_flg  = 0
                        AND bun_gas_seikyu_den_no = :bun_gas_seikyu_den_no
END_OF_SQL
                        , array('bun_gas_seikyu_den_no' => $seikyu_den_no));
                }
                // 売上伝票存在確認
                if(Msi_Sys_Utils::myCount($uriage) > 0){
                    //・検索した売上伝票の売上金額の多い順(同額の場合は売上伝票Noの昇順)で順番に次の処理を行う。
                    //　 入金金額に入金額を加算
                    // 　請求残高から入金額を減算
                    $nyukin_prc = $nyukin[0]['nyukin_prc'];        // 入金
                    $baddebtloss_prc = $nyukin_baddebt[0]['nyukin_prc']; // 共通
                    foreach ($uriage as $uri) {
                        $nyukin_prc_tmp  = 0;
                        $baddebtloss_prc_tmp = 0;
                        if($nyukin_prc >= $uri['uri_prc']){
                            $nyukin_prc_tmp = $uri['uri_prc'];
                            $nyukin_prc = $nyukin_prc - $uri['uri_prc'];
                        }else{
                            $nyukin_prc_tmp = $nyukin_prc;
                            $nyukin_prc = 0;
                        }
                        if($baddebtloss_prc >= $uri['uri_prc']){
                            $baddebtloss_prc_tmp = $uri['uri_prc'];
                            $baddebtloss_prc = $baddebtloss_prc - $uri['uri_prc'];
                        }else{
                            $baddebtloss_prc_tmp = $baddebtloss_prc;
                            $baddebtloss_prc = 0;
                        }
                        // 入金金額に入金額を設定
                        // 請求残高から入金額を減算
                        if($seikyu[0]['data_kbn'] == 3 && $seikyu[0]['bun_gas_kbn_num'] == 0 && ($seikyu[0]['status_kbn'] == 3 || $seikyu[0]['status_kbn'] == 5)&& $seikyu[0]['seikyu_approval_status'] ==  0){
                            // アフター請求伝票未承認
                            $cnt += $db->easyExecute(<<< END_OF_SQL
                                UPDATE uriage_denpyo
                                SET  nyukin_prc  = :nyukin_prc    -- 入金金額
                                    ,bad_debt_loss_prc = :bad_debt_loss_prc    
                                    ,seikyu_zan = (
                                              COALESCE(uri_prc_sum,0)
                                            + COALESCE(uri_nebk_sum,0)
                                            + COALESCE(uri_hepn_sum,0)
                                            + COALESCE(hoshi_prc_sum,0)
                                            + COALESCE(out_zei_prc,0)
                                            + COALESCE(sougi_keiyaku_prc,0) + COALESCE(sougi_harai_prc,0)
                                            + COALESCE(sougi_keiyaku_zei,0)
                                            + COALESCE(sougi_wari_prc,0) + COALESCE(sougi_wari_zei,0)
                                            + COALESCE(sougi_premium_service_prc,0) 
                                            + COALESCE(sougi_early_use_cost,0) + COALESCE(sougi_early_use_cost_zei,0)   -- 早期利用費
                                            + COALESCE(sougi_meigi_chg_cost,0) + COALESCE(sougi_meigi_chg_cost_zei,0)   -- 名義変更手数料
                                            + COALESCE(sougi_tokuten_prc,0)     -- 金額充当
                                            + COALESCE(n_free9,0)
                                            + COALESCE(n_free10,0)
                                            + COALESCE(n_free5,0)
                                    )   - :nyukin_prc                           -- 請求残高
                                        - :bad_debt_loss_prc
                                        - :uchikin_prc
                                        - COALESCE(kouden_uchikin_prc,0)
                                        - COALESCE(cupon_prc,0)
                                    ,uchikin_prc = :uchikin_prc    
                                WHERE delete_flg = 0
                                AND uri_den_no = :uri_den_no
END_OF_SQL
                                , array('uri_den_no'  => $uri['uri_den_no']
                                      , 'nyukin_prc'  => $nyukin_prc_tmp
                                      , 'uchikin_prc' => $nyukin_uchi[0]['nyukin_prc']
                                      , 'bad_debt_loss_prc' => $baddebtloss_prc_tmp)
                            );
                        }else{
                            $cnt += $db->easyExecute(<<< END_OF_SQL
                                UPDATE uriage_denpyo
                                SET  nyukin_prc  = :nyukin_prc    -- 入金金額
                                    ,bad_debt_loss_prc = :bad_debt_loss_prc    
                                    ,seikyu_zan = (
                                              COALESCE(uri_prc_sum,0)
                                            + COALESCE(uri_nebk_sum,0)
                                            + COALESCE(uri_hepn_sum,0)
                                            + COALESCE(hoshi_prc_sum,0)
                                            + COALESCE(out_zei_prc,0)
                                            + COALESCE(sougi_keiyaku_prc,0) + COALESCE(sougi_harai_prc,0)
                                            + COALESCE(sougi_keiyaku_zei,0)
                                            + COALESCE(sougi_wari_prc,0) + COALESCE(sougi_wari_zei,0)
                                            + COALESCE(sougi_premium_service_prc,0) 
                                            + COALESCE(sougi_early_use_cost,0) + COALESCE(sougi_early_use_cost_zei,0)   -- 早期利用費
                                            + COALESCE(sougi_meigi_chg_cost,0) + COALESCE(sougi_meigi_chg_cost_zei,0)   -- 名義変更手数料
                                            + COALESCE(sougi_tokuten_prc,0)     -- 金額充当
                                            + COALESCE(n_free9,0)
                                            + COALESCE(n_free10,0)
                                            + COALESCE(n_free5,0)
                                    )   - :nyukin_prc                           -- 請求残高
                                        - :bad_debt_loss_prc
                                        - COALESCE(uchikin_prc,0)
                                        - COALESCE(kouden_uchikin_prc,0)
                                        - COALESCE(cupon_prc,0)
                                WHERE delete_flg = 0
                                AND uri_den_no = :uri_den_no
END_OF_SQL
                                , array('uri_den_no'  => $uri['uri_den_no']
                                      , 'nyukin_prc'  => $nyukin_prc_tmp
                                      , 'bad_debt_loss_prc' => $baddebtloss_prc_tmp)
                            );
                        }
                    }
                    
                    // 売上伝票取得
                    $uriage2 = $db->easySelect(<<< END_OF_SQL
                            SELECT 
                                 ud.status_kbn             -- ステータス
                                ,ud.seikyu_zan             -- 請求残高
                                ,ud.uri_den_no             -- 売上伝票番号
                            FROM uriage_denpyo ud          -- 売上伝票
                            WHERE ud.delete_flg  = 0
                            AND ud.seko_no = :seko_no                                          -- 該当請求伝票の施行番号
                            AND ud.data_kbn =4
                            ORDER BY ud.uri_prc_sum, ud.uri_den_no
END_OF_SQL
                        , array('seko_no' => $seko_no));
                    foreach ($uriage2 as $uri2) {
                        if($uri2['status_kbn'] != 5 && $uri2['seikyu_zan'] == 0){
                            // ・検索した売上伝票のステータスが"5:入金済"以外 かつ 請求残高が0円になった場合
                            // 　ステータスを"5:入金済"で更新する。
                            $cnt += $db->easyExecute(<<< END_OF_SQL
                                UPDATE uriage_denpyo
                                SET  status_kbn    = 5 -- 5:入金済
                                WHERE delete_flg = 0
                                AND uri_den_no = :uri_den_no
END_OF_SQL
                                , array('uri_den_no' => $uri2['uri_den_no'])
                            );
                            if($seikyu2[0]['seikyu_approval_status'] == 0){
                                // 請求書承認状況が 0:未承認の場合は3:施行金額確定済戻す
                                $cnt += $db->easyExecute(<<< END_OF_SQL
                                    UPDATE uriage_denpyo
                                    SET  status_kbn  = 3 -- 3:施行金額確定済戻す
                                    WHERE delete_flg = 0
                                    AND uri_den_no = :uri_den_no
END_OF_SQL
                                    , array('uri_den_no' => $uri2['uri_den_no'])
                                );
                            }
                        }else if($uri2['status_kbn'] == 5 && $uri2['seikyu_zan'] != 0) {
                            // ・検索した売上伝票のステータスが"5:入金済" かつ 請求残高が0円ではない場合
                            if($seikyu2[0]['seikyu_approval_status'] == 0){
                                // 請求書承認状況が 0:未承認の場合は3:施行金額確定済戻す
                                $cnt += $db->easyExecute(<<< END_OF_SQL
                                    UPDATE uriage_denpyo
                                    SET  status_kbn  = 3 -- 3:施行金額確定済戻す
                                    WHERE delete_flg = 0
                                    AND uri_den_no = :uri_den_no
END_OF_SQL
                                    , array('uri_den_no' => $uri2['uri_den_no'])
                                );
                            }else if($seikyu2[0]['seikyu_approval_status'] == 1){
                                // 請求書承認状況が 1:承認済の場合は4:請求確定に戻す
                                $cnt += $db->easyExecute(<<< END_OF_SQL
                                    UPDATE uriage_denpyo
                                    SET  status_kbn  = 4 -- 4:請求確定に戻す
                                    WHERE delete_flg = 0
                                    AND uri_den_no = :uri_den_no
END_OF_SQL
                                    , array('uri_den_no' => $uri2['uri_den_no'])
                                );
                            }
                        }
                    }
                }else{
                    throw new Msi_Sys_Exception_InputException("売上伝票が存在しません");
                }
            }
        }else{
            throw new Msi_Sys_Exception_InputException("請求伝票が存在しません");
        }
    }
    /**
     *
     * 入金伝票から請求伝票の入金金額、請求残高を修正して更新する
     *
     * <AUTHOR> Sugiyama
     * @since   2021/02/xx
     * @param   Msi_Sys_Db $db
     * @param   string  $seikyu_den_no 請求伝票番号
     * @return void
     */
    public static function updNyukinPrcSeikyuHanbai($db, $seikyu_den_no) {
        $cnt = 0;
        // 請求伝票のデータ区分を取得
        $seikyu = $db->easySelect(<<< END_OF_SQL
            SELECT 
                 sd.data_kbn
                ,sd.seko_no
                ,sd.bun_gas_kbn_num
                ,sd.bun_gas_seikyu_den_no
                ,sd.uri_den_no
            FROM seikyu_denpyo sd
            WHERE sd.seikyu_den_no = :seikyu_den_no
            AND   sd.delete_flg    = 0
END_OF_SQL
            , array('seikyu_den_no' => $seikyu_den_no));
        // 請求伝票存在確認
        if(Msi_Sys_Utils::myCount($seikyu) > 0){
            $nyukin = $db->easySelect(<<< END_OF_SQL
                SELECT 
                    COALESCE(SUM(nyukin_prc), 0) AS nyukin_prc 
                FROM nyukin_denpyo nd 
                WHERE seikyu_no = :seikyu_no
                AND delete_flg  = 0
END_OF_SQL
                , array('seikyu_no' => $seikyu_den_no));
            // 入金金額に入金額を加算
            // 請求残高から入金額を減算
            $cnt += $db->easyExecute(<<< END_OF_SQL
                UPDATE seikyu_denpyo
                SET  nyukin_prc = :nyukin_prc   -- 入金金額
                    ,seikyu_zan = 0             -- 請求残高
                WHERE delete_flg = 0
                AND seikyu_den_no = :seikyu_den_no
END_OF_SQL
                , array(
                        'seikyu_den_no' => $seikyu_den_no
                      , 'nyukin_prc'    => $nyukin[0]['nyukin_prc']
                    )
                );
            // 請求残高から入金額を減算
            $cnt += $db->easyExecute(<<< END_OF_SQL
                UPDATE uriage_denpyo
                SET  nyukin_prc = :nyukin_prc   -- 入金金額
                    ,seikyu_zan = 0             -- 請求残高
                WHERE delete_flg = 0
                AND uri_den_no = :uri_den_no
END_OF_SQL
                , array(
                        'uri_den_no' => $seikyu[0]['uri_den_no']
                      , 'nyukin_prc'    => $nyukin[0]['nyukin_prc']
                    )
                );
        }else{
            throw new Msi_Sys_Exception_InputException("請求伝票が存在しません");
        }
    }
    /**
     * 請求残高　作成、更新処理
     *
     * <AUTHOR> Kayo
     * @since   2016/08/29
     * @param   Msi_Sys_Db $db	データベース
     * @param   array  $uriden 売上伝票レコード
     * @param   string  $kaisya_cd 会社コード
     * @param   string  $soukiriyozeikbn 早期利用費消費税区分 0：外税計算 1:内税計算
     * @param   string  $kakekin_tax_kbn 掛金消費税計算区分
     * @param   string  $gojyo_tax_keian_kbn 互助会消費税計算区分
     * @return  bool   true：成功 false:該当なし
     */
    public static function SeikyuZanMake($db, $uriden, $kaisya_cd, $soukiriyozeikbn, $kakekin_tax_kbn, $gojyo_tax_keian_kbn) {
        $seikyu_no = $uriden['seikyu_no'];       // 請求書№

        $zanData = array();
        $zanData['bumon_cd'] = $uriden['bumon_cd'];             //部門コード
        $zanData['sekyu_cd'] = $uriden['sekyu_cd'];             //請求先コード
        $zanData['moushi_kbn'] = $uriden['data_kbn'];           // 申込区分	1:葬儀 2:法事 3:単品 4:別注品
        $zanData['seko_no'] = $uriden['seko_no'];               // 施行番号	99999999999
        $zanData['seikyu_count'] = $uriden['sekkyu_kaisu'];     // 請求回数
        $zanData['seikyu_prc'] = $uriden['seikyu_prc'];         // 請求金額
        $zanData['zei_prc'] = $uriden['out_zei_prc'];           // 消費税額
        $zanData['nyukin_prc'] = 0;                             // 入金金額
        $zanData['seikyu_zan'] = 0;                             // 請求残高
//        $zanData['kaisyu_yotei_ymd'] = $uriden['kaishu_ymd'];   // 回収予定日
        $zanData['last_nyukin_ymd'] = null;      //最終入金日
        // 入金伝票を取得する
        $nyukinden = DataMapper_NyukinDenpyo::findsum($db, array(
                    'seikyu_no' => $seikyu_no,
        ));
        //入金伝票が存在する場合	
        foreach ($nyukinden as $nyukin_rec) {
            if ($nyukin_rec['nyu_kbn'] == '99') {
                // 互助会入金の場合は、請求金額からマイナス
                $zanData['seikyu_prc'] = $zanData['seikyu_prc'] - $nyukin_rec['nyukin_prc'];    //入金金額
            } else {
                $zanData['nyukin_prc'] = $zanData['nyukin_prc'] + $nyukin_rec['nyukin_prc'];    //入金金額
                $zanData['last_nyukin_ymd'] = $nyukin_rec['nyukin_ymd']; //最終入金日
            }
        }
        if ($uriden['data_kbn'] == 1 || $uriden['data_kbn'] == 2) {
            // 消費税差額等の計算をしない
            $gojokai = DataMapper_GojokaiCalc::getGojokaiKingaku_zei($db, $uriden['seko_no']);
            $kijyun_ymd = Msi_Sys_Utils::getDate();
            if ($gojyo_tax_keian_kbn == 1) { // 互助会消費税計算区分 0：加入日時点 1：施行日時点
                if (strlen($uriden['keijo_ymd']) > 0) {
                    $kijyun_ymd = $uriden['keijo_ymd'];
                }
            }
            $zanData['seikyu_prc'] += $gojokai['sougi_keiyaku_prc']+$gojokai['sougi_harai_prc'];        
            $zanData['seikyu_prc'] += $gojokai['sougi_keiyaku_zei'];        
            $zanData['seikyu_prc'] += $gojokai['sougi_meigi_chg_cost'] + $gojokai['sougi_meigi_chg_cost_zei'];   // 名義変更手数料
            $zanData['seikyu_prc'] += $gojokai['sougi_early_use_cost'] + $gojokai['sougi_early_use_cost_zei'];   // 早期利用費
            // 会費残額を加算
            $zanData['seikyu_prc'] += $gojokai['sougi_wari_prc'] + $gojokai['etc_harai_prc'] + $gojokai['sougi_premium_service_prc'];
            if ($gojyo_tax_keian_kbn == 0) { // 互助会消費税計算区分 0：加入日時点 1：施行日時点
                $kijyun_ymd = $gojokai['zei_kijyn_ymd'];
            }
            if ($kakekin_tax_kbn == 1 && isset($gojokai['sougi_keiyaku_prc'])) { // 掛金消費税計算区分 0：計算なし 1：計算あり
                if (isset($gojokai['sougi_keiyaku_zei'])) {
                    $zanData['zei_prc'] += $gojokai['sougi_keiyaku_zei'];
                }
            }
        }
        // 請求金額の消費税額を再度計算しなおす。
        $zanData['seikyu_zan'] = $zanData['seikyu_prc'] + $zanData['zei_prc'] - $zanData['nyukin_prc']; // 請求残高
        if ($uriden['data_kbn'] == 1 || $uriden['data_kbn'] == 2) { // 申込区分	1:葬儀 2:法事 3:単品 4:別注品)
            $kihonInfo = DataMapper_SekoKihonInfo::find($db, array('seko_no' => $zanData['seko_no']));
            // ステータス区分が4のときのみ
            if ($zanData['seikyu_zan'] == 0 
                    && ($kihonInfo[0]['status_kbn'] == self::STATUS_SEIKYU_KAKUTEI || $kihonInfo[0]['status_kbn'] == self::STATUS_SEIKYU_NYUKIN)) {
                // 施行基本情報のステータスを変更 2015/03/02 ADD Kayo
                $db->easyExecute(<<< END_OF_SQL
                UPDATE seko_kihon_info
                SET status_kbn = 4  -- 4:入金済み
                WHERE delete_flg = 0
                    AND seko_no = :seko_no
END_OF_SQL
                        , array('seko_no' => $zanData['seko_no']));
            } else {
                // 残高有の場合
                if ($kihonInfo[0]['status_kbn'] == self::STATUS_SEIKYU_NYUKIN) {
                    // 入金済み→請求済みに戻す
                    // 施行基本情報のステータスを変更
                    $db->easyExecute(<<< END_OF_SQL
                        UPDATE seko_kihon_info
                        SET status_kbn = 3  -- 3：請求済み 
                        WHERE delete_flg = 0
                            AND seko_no = :seko_no
END_OF_SQL
                            , array('seko_no' => $zanData['seko_no']));
                }
            }
        }
        return true;
    }

    /**
     * 売掛残高　作成、更新処理
     *
     * <AUTHOR> Kayo
     * @since      2016/08/29
     * @param      Msi_Sys_Db $db	データベース
     * @param      array  $uriden 売上伝票レコード
     * @return     bool   true：成功 false:該当なし
     */
    public static function UrikakeZanMake($db, $uriden) {
        return true;
    }
    /**
     *
     * 仮請求伝票の請求残高を修正して更新する
     *
     * <AUTHOR> Sugiyama
     * @since   2021/02/xx
     * @param   Msi_Sys_Db $db
     * @param   string  $seikyu_den_no 請求伝票番号
     * @return void
     */
    public static function updTempSeikyu($db, $seikyu_den_no) {
        $cnt = 0;
        // 仮請求伝票のデータ区分を取得
        $seikyu = $db->easySelect(<<< END_OF_SQL
            SELECT 
                 sd.data_kbn
                ,sd.seko_no
                ,sd.status_kbn
                ,sd.seikyu_approval_status
                ,sd.bun_gas_kbn_num
                ,sd.bun_gas_seikyu_den_no
                ,sd.uri_den_no
                ,sd.cupon_prc
                ,sd.uchikin_prc
                ,sd.nyukin_prc
                ,sd.seikyu_zan
            FROM temp_seikyu_denpyo sd
            WHERE sd.temp_seikyu_den_no = :seikyu_den_no
            AND   sd.delete_flg    = 0
END_OF_SQL
            , array('seikyu_den_no' => $seikyu_den_no));
        // 請求伝票存在確認
        if(Msi_Sys_Utils::myCount($seikyu) > 0){
            /*
             * ■該当請求伝票の更新
             */
            // 入金伝票SELECT(内金)
            // 分割OR合算されていれば元から売上伝票を取得する
            if (isset($seikyu[0]['bun_gas_seikyu_den_no'])) {
                $motoData = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $seikyu[0]['bun_gas_seikyu_den_no']));
                $uri_den_no = $motoData[0]['uri_den_no'];
            } else {
                $uri_den_no = $seikyu[0]['uri_den_no'];
            }
            $nyukin_uchi = $db->easySelect(<<< END_OF_SQL
                SELECT 
                    COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
                FROM nyukin_denpyo d
                INNER JOIN nyukin_denpyo_msi dm
                ON dm.denpyo_no = d.denpyo_no
                AND dm.denpyo_kbn <> 5
                AND dm.delete_flg = 0
                WHERE d.uri_den_no = :uri_den_no
                AND d.nyu_kbn     = 88
                AND d.delete_flg  = 0
END_OF_SQL
                , array('uri_den_no' => $uri_den_no));
                // 入金伝票SELECT(クーポン)
                $nyukin_cupon = $db->easySelect(<<< END_OF_SQL
                    SELECT 
                        COALESCE(sum(dm.nyukin_prc),0) AS nyukin_prc 
                    FROM nyukin_denpyo d
                    INNER JOIN nyukin_denpyo_msi dm
                    ON dm.denpyo_no = d.denpyo_no
                    AND dm.denpyo_kbn = 5
                    AND dm.delete_flg = 0
                    WHERE d.uri_den_no = :uri_den_no
                    AND d.nyu_kbn     = 88
                    AND d.delete_flg  = 0
END_OF_SQL
                    , array('uri_den_no' => $uri_den_no));
            // 入金伝票SELECT(貸倒損失額)
            $nyukin_baddebt = $db->easySelect(<<< END_OF_SQL
                SELECT 
                    COALESCE(sum(nyukin_prc),0) AS nyukin_prc 
                FROM nyukin_denpyo
                WHERE seikyu_no = :seikyu_no
                AND nyu_kbn     = 4
                AND delete_flg  = 0
END_OF_SQL
                , array('seikyu_no' => $seikyu_den_no));
            // 入金伝票SELECT(香典内金)
            $nyukin_kouden = $db->easySelect(<<< END_OF_SQL
                SELECT 
                    COALESCE(sum(nyukin_prc),0) AS nyukin_prc 
                FROM nyukin_denpyo
                WHERE uri_den_no = :uri_den_no
                AND nyu_kbn     = 5
                AND delete_flg  = 0
END_OF_SQL
                , array('uri_den_no' => $uri_den_no));
            // 入金金額に入金額を加算
            // 請求残高から入金額を減算
            $cnt += $db->easyExecute(<<< END_OF_SQL
                UPDATE temp_seikyu_denpyo
                SET  uchikin_prc = :uchikin_prc   -- 内金金額
                    ,kouden_uchikin_prc = :kouden_uchikin_prc   -- 香典内金金額
                    ,bad_debt_loss_prc = :bad_debt_loss_prc   -- 貸倒損失額
                    ,cupon_prc = :cupon_prc   -- クーポン利用額
                    ,seikyu_zan = (
                              COALESCE(uri_prc_sum,0)
                            + COALESCE(uri_nebk_sum,0)
                            + COALESCE(uri_hepn_sum,0)
                            + COALESCE(hoshi_prc_sum,0)
                            + COALESCE(out_zei_prc,0)
                            + COALESCE(sougi_keiyaku_prc,0) + COALESCE(sougi_harai_prc,0)  
                            + COALESCE(sougi_keiyaku_zei,0)
                            + COALESCE(sougi_wari_prc,0) + COALESCE(sougi_wari_zei,0)
                            + COALESCE(sougi_premium_service_prc,0)
                            + COALESCE(sougi_early_use_cost,0) + COALESCE(sougi_early_use_cost_zei,0)   -- 早期利用費
                            + COALESCE(sougi_meigi_chg_cost,0) + COALESCE(sougi_meigi_chg_cost_zei,0)   -- 名義変更手数料
                            + COALESCE(sougi_tokuten_prc,0)         -- 金額充当
                            + COALESCE(n_free9,0)
                            + COALESCE(n_free10,0)
                            + COALESCE(n_free5,0)
                    )   - :uchikin_prc                       
                        - :kouden_uchikin_prc                
                        - :bad_debt_loss_prc            
                        - :cupon_prc      
                WHERE delete_flg = 0
                AND temp_seikyu_den_no = :seikyu_den_no
END_OF_SQL
                , array('seikyu_den_no' => $seikyu_den_no
                      , 'uchikin_prc'   => $nyukin_uchi[0]['nyukin_prc']
                      , 'cupon_prc'   => $nyukin_cupon[0]['nyukin_prc']
                      , 'bad_debt_loss_prc'   => $nyukin_baddebt[0]['nyukin_prc']
                      , 'kouden_uchikin_prc'   => $nyukin_kouden[0]['nyukin_prc']
                    )
                );
        }else{
            throw new Msi_Sys_Exception_InputException("請求伝票が存在しません");
        }
    }

}
