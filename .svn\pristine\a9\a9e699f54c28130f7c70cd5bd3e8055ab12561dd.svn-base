<?php

/**
 * DataMapper_UriageDenpyo
 *
 * 売上伝票 データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Tosaka
 * @since      2020/08/XX
 * @filesource 
 */

/**
 * 売上伝票 データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Tosaka
 * @since      2020/08/XX
 */
class DataMapper_UriageDenpyo extends DataMapper_Abstract {

    /**
     * 売上伝票 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/08/XX
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash = array(), $isDateEffective = true) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_no,T.disp_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; 
            $dateWhere2 = ''; 
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }
        // 会社情報を取得   2016/04/04 ADD Kayo
        if (!App_Utils::isFukusuKaisyaKanri()) {
            $kaisyainfo = $db->easySelOne(<<< END_OF_SQL
                SELECT nyukin_den_auto
                FROM kaisya_info
END_OF_SQL
            );
        } else {
            $kaisyacd = App_Utils::getCtxtKaisyaEasy(); // 2016/12/25 ADD Kayo
            $kaisyainfo = $db->easySelOne(<<< END_OF_SQL
                SELECT nyukin_den_auto
                FROM kaisya_info
                WHERE delete_flg = 0
                AND   kaisya_cd = :kaisya_cd    
END_OF_SQL
                    , array('kaisya_cd' => $kaisyacd));
        }
        $nyukin_den_auto = 0;
        if (count($kaisyainfo) > 0) {
            $nyukin_den_auto = $kaisyainfo['nyukin_den_auto'];   // 互助会入金伝票自動作成 0：作成する 1:作成しない
        }
        $select = $db->easySelect(<<< END_OF_SQL
            SELECT *
            FROM (
                SELECT 
                    u.uri_den_no AS seikyu_no   -- 売上伝票№
                    ,u.uri_den_no   
                    ,u.denpyo_no    -- 受注伝票№		
                    ,0 AS disp_no   -- 表示順		
                    ,TO_CHAR(u.juchu_ymd,'YYYY/MM/DD') AS juchu_ymd -- 売上日
                    ,TO_CHAR(u.juchu_ymd,'YYYYMM') AS juchu_ym      -- 売上年月
                    ,u.data_kbn -- データ区分
                    ,c.kbn_value_lnm AS seikyu_kbn_nm   -- 請求区分名			
                    ,u.seko_no      -- 施行番号
                    ,u.seko_no_sub  -- 施行番号（枝番）
                    ,u.kaisya_cd    -- 会社コード
                    ,u.bumon_cd     -- 売上部門コード
                    ,b.bumon_lnm    -- 売上部門名
                    ,b.bumon_snm    -- 売上部門名（簡略）
                    ,u.tanto_cd     -- 担当者コード
                    ,tt.tanto_nm    -- 担当者名		
                    ,u.gojokai_cose_cd  -- 互助会コースコード
                    ,u.seko_plan_cd -- 施行プランコード
                    ,TO_CHAR(u.seikyu_ymd,'YYYY/MM/DD') AS seikyu_ymd   -- 請求日
                    ,TO_CHAR(COALESCE(u.seikyu_ymd,u.juchu_ymd),'YYYY/MM/DD') AS seikyu_ymdx    -- 請求日
                    ,TO_CHAR(u.seikyu_ymd,'YYYYMM') AS seikyu_ym    -- 請求年月
                    ,TO_CHAR(u.zen_seikyu_ymd,'YYYY/MM/DD') AS zen_seikyu_ymd   -- 前回請求日
                    ,u.sekkyu_kaisu -- 請求回数
                    ,u.kaishu_kbn   -- 回収区分
                    ,TO_CHAR(u.kaishu_ymd,'YYYY/MM/DD') AS kaishu_ymd   -- 回収予定日
                    ,u.nyukin_prc       -- 入金金額
                    ,u.seikyu_zan       -- 請求残高	
                    ,u.uchikin_prc    -- 内金
                    ,u.cupon_prc    -- クーポン金額
                    ,u.sekyu_cd         -- 請求先コード
                    ,u.sekyu_nm         -- 請求先名
                    ,u.sekyu_knm        -- 請求先名カナ
                    ,u.sekyu_soufu_nm   -- 請求書送付先名
                    ,u.sekyu_yubin_no   -- 請求先郵便番号
                    ,COALESCE(u.sekyu_addr1,'') || COALESCE(u.sekyu_addr2,'') AS seikyu_addr    -- 請求先住所
                    ,u.sekyu_tel        -- 請求先電話番号
                    ,u.sekyu_fax        -- 請求先FAX
                    ,u.uri_prc_sum      -- 売上金額合計
                    ,u.genka_prc_sum    -- 原価金額合計
                    ,u.uri_hepn_sum     -- 売上返品合計
                    ,u.uri_nebk_sum     -- 売上値引合計
                    ,u.hoshi_prc_sum    -- 奉仕料合計
                    ,u.uri_prc_sum 
			+ u.uri_nebk_sum 
			+ u.uri_hepn_sum 
			+ u.hoshi_prc_sum
                        + u.sougi_wari_prc         -- 割増金額    
                        + u.sougi_meigi_chg_cost
                        + u.sougi_early_use_cost
                        + (u.sougi_keiyaku_prc + u.sougi_harai_prc) -- 会費残額を加算 
                        + u.etc_early_use_cost AS seikyu_prc    -- 請求金額		
                    ,u.szei_katax_taisho_prc    -- 外税課税対象額
                    ,u.uzei_katax_taisho_prc    -- 内税課税対象額
                    ,u.hitax_katax_taisho_prc   -- 非税課税対象額
                    ,u.tax_code_kbn -- 税区分コード区分
                    ,u.tax_cd       -- 税区分コード
                    ,u.tax_kbn      -- 税区分
                    ,u.zei_cd       -- 消費税コード
                    ,u.out_zei_prc  -- 外税消費税額
                    ,u.in_zei_prc   -- 内税消費税額
                    ,u.out_zei_prc 
                        + u.in_zei_prc AS zei_prc   -- 消費税額 	
                    ,u.uri_prc_sum
                    + u.uri_nebk_sum	  
                    + u.uri_hepn_sum
                    + u.hoshi_prc_sum
                    + u.out_zei_prc 
                    + u.sougi_keiyaku_prc + u.sougi_harai_prc
                    + u.sougi_keiyaku_zei
                    + u.sougi_wari_prc
                    + u.sougi_premium_service_prc 
                    + u.sougi_meigi_chg_cost + u.sougi_meigi_chg_cost_zei
                    + u.sougi_early_use_cost + u.sougi_early_use_cost_zei
                    + u.etc_harai_prc
                    AS seikyu_zei_prc    -- 請求金額（消費税込み） 		
                    ,u.arari_prc    -- 粗利益額
                    ,u.denpyo_biko1 -- 伝票備考１
                    ,u.denpyo_biko2 -- 伝票備考２
                    ,u.shonin_dt1   -- 承認１日時
                    ,u.shonin_dt2   -- 承認２日時
                    ,u.sougi_keiyaku_prc    -- 葬儀契約金額
                    ,u.sougi_keiyaku_zei    -- 葬儀契約消費税	
                    ,u.sougi_harai_prc      -- 葬儀払込金額
                    ,u.sougi_wari_prc       -- 葬儀前納割引額
                    ,u.sougi_wari_zei       -- 葬儀前納割引額税
                    ,u.sougi_premium_service_prc       -- 葬儀サービス割増金額
                    ,u.sougi_cose_chg_prc   -- 葬儀コース変更差額金
                    ,u.sougi_meigi_chg_cost -- 名義変更手数料
                    ,u.sougi_early_use_cost -- 葬儀早期利用費
                    ,u.sougi_early_use_cost_zei -- 葬儀早期利用費消費税        
                    ,u.sougi_zei_sagaku_prc     -- 葬儀掛金消費税差額
                    ,u.sougi_zei_cd     -- 葬儀消費税コード
                    ,u.etc_keiyaku_prc  -- 壇払等の契約金額
                    ,u.etc_keiyaku_zei  -- 壇払等の契約消費税	
                    ,u.etc_harai_prc    -- 壇払等の払込金額
                    ,u.etc_wari_prc     -- 壇払等の前納割引額
                    ,u.etc_cose_chg_prc -- 壇払等のコース変更差額金
                    ,u.etc_early_use_cost   -- 壇払等の早期利用費
                    ,u.etc_early_use_cost_zei   -- 壇払等の早期利用費消費税        
                    ,u.etc_zei_sagaku_prc   -- 壇払等の掛金消費税差額
                    ,u.etc_zei_cd   -- 壇払等の消費税コード
                    ,u.delete_flg   -- 削除フラグ
                    ,um.nafuda_nm   -- 名札		
                    ,u.v_free1  
                    ,u.seko_prc_kakute_kbn
                    ,u.d_import_kbn
                    ,u.kouden_uchikin_prc
                    ,u.ref_seko_no
                    ,u.k_free5
                    ,u.zaimu_rendo_denno
                    ,u.n_free7
                    ,u.n_free8
                    ,u.n_free9
                    ,u.n_free10
                FROM uriage_denpyo u			
                LEFT JOIN
                    (SELECT uri_den_no
			,string_agg(nafuda_nm, '、') AS nafuda_nm
                    FROM uriage_denpyo_msi
                    WHERE delete_flg = 0
			AND nafuda_nm IS NOT NULL
			AND trim(nafuda_nm) <> ''
                    GROUP BY uri_den_no
                    ) AS um 
                    ON u.uri_den_no = um.uri_den_no
                LEFT JOIN tanto_mst tt
                    ON tt.delete_flg = 0
                    AND u.tanto_cd = tt.tanto_cd   
                    $dateWhere2
                LEFT JOIN bumon_mst b
                    ON b.delete_flg = 0
                    AND u.bumon_cd = b.bumon_cd   
                    $dateWhere3
                LEFT JOIN code_nm_mst c
                    ON 0 = c.delete_flg
                    AND	'0920' = c.code_kbn
                    AND	u.data_kbn = c.kbn_value_cd_num			
                WHERE u.delete_flg = 0   
                    $dateWhere1
            ) T
            WHERE 
                $whereStr
            $orderBy
            $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 売上伝票明細 取得
     *
     * <AUTHOR> Kayo
     * @since      2014/06/15
     * @param      Msi_Sys_Db $db
     * @param      string  $denpyo_no
     * @return     array   該当データがない場合はarray()を返す
     */
    public static function findDenpyoMsi($db, $denpyo_no) {

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT m.*
                ,sm.nm_input_kbn AS nm_input_kbn
                ,sm.tnk_chg_kbn AS tnk_chg_kbn
                ,sm.tani_ovr_kbn AS tani_ovr_kbn
            FROM uriage_denpyo_msi m
            LEFT JOIN shohin_mst sm
                ON m.shohin_cd = sm.shohin_cd
                AND m.kaisya_cd = sm.kaisya_cd    
                AND m.shohin_bumon_cd = sm.bumon_cd
                AND sm.delete_flg=0
            WHERE m.delete_flg=0
                AND m.denpyo_no = :denpyo_no
            ORDER BY disp_no
END_OF_SQL
                , array('denpyo_no' => $denpyo_no));

        return $select;
    }

    /**
     * 売上伝票(ヘッダーのみ) 取得2
     *
     * <AUTHOR> Kayo
     * @since      2014/07/27
     * @version    2014/09/06 売上計上日を追加 Kayo
     * @version 2014/09/13 売上計上日の処理を追加 Kayo
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findDenpyo2($db, $keyHash = array(), $isDateEffective = false) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.denpyo_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; 
            $dateWhere2 = ''; 
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT *
            FROM (
                SELECT	
                    h.uri_den_no AS seikyu_no   -- 売上伝票№
                    ,h.uri_den_no AS uri_den_no	-- 売上伝票№
                    ,h.denpyo_no    -- 受注伝票№
                    ,TO_CHAR(h.juchu_ymd,'YYYY/MM/DD') AS juchu_ymd -- 売上日
                    ,TO_CHAR(h.juchu_ymd,'YYYYMM') AS juchu_ym      -- 売上年月
                    ,TO_CHAR(h.keijo_ymd,'YYYY/MM/DD') AS keijo_ymd -- 売上計上日		
                    ,TO_CHAR(h.keijo_ymd,'YYYY/MM') AS keijo_ym     -- 売上計上年月		
                    ,h.data_kbn     -- データ区分
                    ,h.seko_no      -- 施行番号
                    ,h.seko_no_sub  -- 施行番号（枝番）
                    ,h.bumon_cd     -- 売上部門コード
                    ,b.bumon_lnm    -- 売上部門名
                    ,b.bumon_snm    -- 売上部門名（簡略）
                    ,h.tanto_cd     -- 担当者コード
                    ,tt.tanto_nm    -- 担当者名		
                    ,h.gojokai_cose_cd  -- 互助会コースコード
                    ,h.seko_plan_cd -- 施行プランコード
                    ,TO_CHAR(h.seikyu_ymd,'YYYY/MM/DD') AS seikyu_ymd   -- 請求日
                    ,TO_CHAR(h.seikyu_ymd,'YYYYMM') AS seikyu_ym        -- 請求年月
                    ,TO_CHAR(h.zen_seikyu_ymd,'YYYY/MM/DD') AS zen_seikyu_ymd   -- 前回請求日
                    ,h.pay_method_cd    -- 支払方法コード
                    ,h.juchusaki_kbn    -- 受注先区分		
                    ,h.sekkyu_kaisu     -- 請求回数
                    ,h.kaishu_kbn       -- 回収区分
                    ,TO_CHAR(h.kaishu_ymd,'YYYY/MM/DD') AS kaishu_ymd   -- 回収予定日
                    ,h.nyukin_prc   -- 入金金額
                    ,h.uchikin_prc   -- 内金金額
                    ,h.cupon_prc   -- クーポン金額
                    ,h.seikyu_zan   -- 請求残高
                    ,h.sekyu_cd     -- 請求先コード
                    ,h.sekyu_nm     -- 請求先名
                    ,h.sekyu_knm    -- 請求先名カナ
                    ,h.sekyu_soufu_nm   -- 請求書送付先名
                    ,h.sekyu_yubin_no   -- 請求先郵便番号
                    ,h.sekyu_addr1  -- 請求先住所1
                    ,h.sekyu_addr2  -- 請求先住所2
                    ,COALESCE(h.sekyu_addr1,'') || COALESCE(h.sekyu_addr2,'') AS seikyu_addr    -- 請求先住所
                    ,h.sekyu_tel    -- 請求先電話番号
                    ,h.sekyu_fax    -- 請求先FAX
                    ,h.nonyu_cd     -- 納入先コード
                    ,h.nonyu_nm     -- 納入先名
                    ,h.nonyu_knm    -- 納入先名カナ
                    ,h.nonyu_yubin_no   -- 納入先郵便番号
                    ,h.nonyu_addr1  -- 納入先住所1
                    ,h.nonyu_addr2  -- 納入先住所2
                    ,h.nonyu_tel    -- 納入先電話番号
                    ,h.nonyu_fax    -- 納入先FAX
                    ,h.nonyu_dt     -- 納入予定日
                    ,h.uri_prc_sum  -- 売上金額合計
                    ,h.genka_prc_sum    -- 原価金額合計
                    ,h.uri_hepn_sum -- 売上返品合計
                    ,h.uri_nebk_sum -- 売上値引合計
                    ,h.hoshi_prc_sum    -- 奉仕料合計
                    ,h.uri_prc_sum
			+ h.uri_nebk_sum
			+ h.uri_hepn_sum
			+ h.hoshi_prc_sum AS seikyu_prc -- 請求金額		
                    ,h.szei_katax_taisho_prc    -- 外税課税対象額
                    ,h.uzei_katax_taisho_prc    -- 内税課税対象額
                    ,h.hitax_katax_taisho_prc   -- 非税課税対象額
                    ,h.tax_code_kbn -- 税区分コード区分
                    ,h.tax_cd   -- 税区分コード
                    ,h.tax_kbn  -- 税区分
                    ,h.zei_cd   -- 消費税コード
                    ,h.out_zei_prc  -- 外税消費税額
                    ,h.in_zei_prc   -- 内税消費税額
                    ,h.out_zei_prc 
                        + h.in_zei_prc AS zei_prc   -- 消費税額 	
                    ,h.uri_prc_sum
                    + h.uri_nebk_sum	  
                    + h.uri_hepn_sum
                    + h.hoshi_prc_sum
                    + h.out_zei_prc 
                    + h.sougi_keiyaku_prc + h.sougi_harai_prc
                    + h.sougi_keiyaku_zei
                    + h.sougi_wari_prc
                    + h.sougi_premium_service_prc
                    + h.sougi_meigi_chg_cost + h.sougi_meigi_chg_cost_zei
                    + h.sougi_early_use_cost + h.sougi_early_use_cost_zei
                    + h.etc_harai_prc
                    AS seikyu_zei_prc    -- 請求金額（消費税込み） 	
                    ,h.arari_prc    -- 粗利益額
                    ,h.denpyo_biko1 -- 伝票備考１
                    ,h.denpyo_biko2 -- 伝票備考２
                    ,h.inkan_img1   -- 承認１印鑑イメージ
                    ,h.inkan_img2   -- 承認２印鑑イメージ
                    ,h.shonin_dt1   -- 承認１日時
                    ,h.shonin_dt2   -- 承認２日時
                    ,h.status_kbn   -- ステータス
                    ,h.sougi_keiyaku_prc    -- 葬儀契約金額
                    ,h.sougi_keiyaku_zei    -- 葬儀契約消費税	
                    ,h.sougi_harai_prc  -- 葬儀払込金額
                    ,h.sougi_wari_prc   -- 葬儀前納割引額
                    ,h.sougi_premium_service_prc   -- 葬儀サービス割増金額
                    ,h.sougi_cose_chg_prc   -- 葬儀コース変更差額金
                    ,h.sougi_meigi_chg_cost -- 名義変更手数料
                    ,h.sougi_early_use_cost -- 葬儀早期利用費
                    ,h.sougi_early_use_cost_zei -- 葬儀早期利用費消費税        
                    ,h.sougi_zei_sagaku_prc     -- 葬儀掛金消費税差額
                    ,h.sougi_zei_cd     -- 葬儀消費税コード
                    ,h.etc_keiyaku_prc  -- 壇払等の契約金額
                    ,h.etc_keiyaku_zei	-- 壇払等の契約消費税	
                    ,h.etc_harai_prc    -- 壇払等の払込金額
                    ,h.etc_wari_prc     -- 壇払等の前納割引額
                    ,h.etc_cose_chg_prc -- 壇払等のコース変更差額金
                    ,h.etc_early_use_cost       -- 壇払等の早期利用費
                    ,h.etc_early_use_cost_zei   -- 壇払等の早期利用費消費税        
                    ,h.etc_zei_sagaku_prc       -- 壇払等の掛金消費税差額
                    ,h.etc_zei_cd   -- 壇払等の消費税コード
                    ,h.seko_prc_kakute_kbn   -- 施行金額確定区分
                    ,h.delete_flg   -- 削除フラグ
                    ,h.zaimu_rendo_kbn
                    ,tt.tanto_nm
                    ,tt.tanto_knm
                    ,b.bumon_lnm
                    ,b.bumon_snm
                    ,b.bumon_lknm
                    ,b.bumon_sknm
                    ,b.bumon_kbn
                    ,COALESCE(TO_CHAR(h.keijo_ymd,'YYYY/MM/DD')
                        ,CASE WHEN h.data_kbn = 4 THEN TO_CHAR(m.juchu_ymd,'YYYY/MM/DD')
                            ELSE TO_CHAR(h.juchu_ymd,'YYYY/MM/DD') END) AS juchu_ymd2 		
                    ,COALESCE(TO_CHAR(h.keijo_ymd,'YYYYMM')
                        ,CASE WHEN h.data_kbn = 4 THEN TO_CHAR(m.juchu_ymd,'YYYYMM')
                            ELSE TO_CHAR(h.juchu_ymd,'YYYYMM') END) AS juchu_ym2 		
                    ,COALESCE(TO_CHAR(h.keijo_ymd,'YYYY/MM')
                        ,CASE WHEN h.data_kbn = 4 THEN TO_CHAR(m.juchu_ymd,'YYYY/MM')
                            ELSE TO_CHAR(h.juchu_ymd,'YYYY/MM') END) AS juchu_ym_x 		
                    FROM uriage_denpyo h
                    LEFT JOIN 
                        (SELECT m.uri_den_no, min(m.juchu_ymd) as juchu_ymd 
                            FROM uriage_denpyo_msi m 
                            WHERE delete_flg = 0 
                            GROUP BY m.uri_den_no) m
                        ON h.uri_den_no	= m.uri_den_no
                    LEFT JOIN tanto_mst tt
                        ON tt.delete_flg = 0
                        AND h.tanto_cd = tt.tanto_cd   
                        $dateWhere2
                    LEFT JOIN bumon_mst b
                        ON b.delete_flg = 0
                        AND h.bumon_cd = b.bumon_cd   
                        $dateWhere3
                    WHERE h.delete_flg = 0   
                        $dateWhere1
                ) T
                WHERE 
                    $whereStr
                $orderBy
                $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 売上伝票(ヘッダーのみ) 取得3
     *
     * <AUTHOR> Kayo
     * @since      2014/07/27
     * @version    2014/09/06 売上計上日を追加 Kayo
     * @version 2014/09/13 売上計上日の処理を追加 Kayo
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findDenpyo3($db, $keyHash = array(), $isDateEffective = false) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.denpyo_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; 
            $dateWhere2 = ''; 
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT *
            FROM (
                SELECT	
                    h.uri_den_no AS seikyu_no   -- 売上伝票№
                    ,h.uri_den_no AS uri_den_no	-- 売上伝票№
                    ,h.denpyo_no    -- 受注伝票№
                    ,TO_CHAR(h.juchu_ymd,'YYYY/MM/DD') AS juchu_ymd -- 売上日
                    ,TO_CHAR(h.juchu_ymd,'YYYYMM') AS juchu_ym      -- 売上年月
                    ,TO_CHAR(h.keijo_ymd,'YYYY/MM/DD') AS keijo_ymd -- 売上計上日		
                    ,TO_CHAR(h.keijo_ymd,'YYYY/MM') AS keijo_ym     -- 売上計上年月		
                    ,h.data_kbn     -- データ区分
                    ,h.seko_no      -- 施行番号
                    ,h.seko_no_sub  -- 施行番号（枝番）
                    ,h.bumon_cd     -- 売上部門コード
                    ,b.bumon_lnm    -- 売上部門名
                    ,b.bumon_snm    -- 売上部門名（簡略）
                    ,h.tanto_cd     -- 担当者コード
                    ,tt.tanto_nm    -- 担当者名		
                    ,h.gojokai_cose_cd  -- 互助会コースコード
                    ,h.seko_plan_cd -- 施行プランコード
                    ,TO_CHAR(h.seikyu_ymd,'YYYY/MM/DD') AS seikyu_ymd   -- 請求日
                    ,TO_CHAR(h.seikyu_ymd,'YYYYMM') AS seikyu_ym        -- 請求年月
                    ,TO_CHAR(h.zen_seikyu_ymd,'YYYY/MM/DD') AS zen_seikyu_ymd   -- 前回請求日
                    ,h.pay_method_cd    -- 支払方法コード
                    ,h.juchusaki_kbn    -- 受注先区分		
                    ,h.sekkyu_kaisu     -- 請求回数
                    ,h.kaishu_kbn       -- 回収区分
                    ,TO_CHAR(h.kaishu_ymd,'YYYY/MM/DD') AS kaishu_ymd   -- 回収予定日
                    ,h.nyukin_prc   -- 入金金額
                    ,h.uchikin_prc   -- 内金金額
                    ,h.cupon_prc   -- クーポン金額
                    ,h.seikyu_zan   -- 請求残高
                    ,h.sekyu_cd     -- 請求先コード
                    ,h.sekyu_nm     -- 請求先名
                    ,h.sekyu_knm    -- 請求先名カナ
                    ,h.sekyu_soufu_nm   -- 請求書送付先名
                    ,h.sekyu_yubin_no   -- 請求先郵便番号
                    ,h.sekyu_addr1  -- 請求先住所1
                    ,h.sekyu_addr2  -- 請求先住所2
                    ,COALESCE(h.sekyu_addr1,'') || COALESCE(h.sekyu_addr2,'') AS seikyu_addr    -- 請求先住所
                    ,h.sekyu_tel    -- 請求先電話番号
                    ,h.sekyu_fax    -- 請求先FAX
                    ,h.nonyu_cd     -- 納入先コード
                    ,h.nonyu_nm     -- 納入先名
                    ,h.nonyu_knm    -- 納入先名カナ
                    ,h.nonyu_yubin_no   -- 納入先郵便番号
                    ,h.nonyu_addr1  -- 納入先住所1
                    ,h.nonyu_addr2  -- 納入先住所2
                    ,h.nonyu_tel    -- 納入先電話番号
                    ,h.nonyu_fax    -- 納入先FAX
                    ,h.nonyu_dt     -- 納入予定日
                    ,h.uri_prc_sum  -- 売上金額合計
                    ,h.genka_prc_sum    -- 原価金額合計
                    ,h.uri_hepn_sum -- 売上返品合計
                    ,h.uri_nebk_sum -- 売上値引合計
                    ,h.hoshi_prc_sum    -- 奉仕料合計
                    ,h.uri_prc_sum
			+ h.uri_nebk_sum
			+ h.uri_hepn_sum
			+ h.hoshi_prc_sum AS seikyu_prc -- 請求金額		
                    ,h.szei_katax_taisho_prc    -- 外税課税対象額
                    ,h.uzei_katax_taisho_prc    -- 内税課税対象額
                    ,h.hitax_katax_taisho_prc   -- 非税課税対象額
                    ,h.tax_code_kbn -- 税区分コード区分
                    ,h.tax_cd   -- 税区分コード
                    ,h.tax_kbn  -- 税区分
                    ,h.zei_cd   -- 消費税コード
                    ,h.out_zei_prc  -- 外税消費税額
                    ,h.in_zei_prc   -- 内税消費税額
                    ,h.out_zei_prc 
                        + h.in_zei_prc AS zei_prc   -- 消費税額 	
                    ,h.uri_prc_sum
                    + h.uri_nebk_sum	  
                    + h.uri_hepn_sum
                    + h.hoshi_prc_sum
                    + h.out_zei_prc 
                    + h.sougi_keiyaku_prc + h.sougi_harai_prc
                    + h.sougi_keiyaku_zei
                    + h.sougi_wari_prc
                    + h.sougi_premium_service_prc
                    + h.sougi_meigi_chg_cost + h.sougi_meigi_chg_cost_zei
                    + h.sougi_early_use_cost + h.sougi_early_use_cost_zei
                    + h.etc_harai_prc
                    AS seikyu_zei_prc    -- 請求金額（消費税込み） 	
                    ,h.arari_prc    -- 粗利益額
                    ,h.denpyo_biko1 -- 伝票備考１
                    ,h.denpyo_biko2 -- 伝票備考２
                    ,h.inkan_img1   -- 承認１印鑑イメージ
                    ,h.inkan_img2   -- 承認２印鑑イメージ
                    ,h.shonin_dt1   -- 承認１日時
                    ,h.shonin_dt2   -- 承認２日時
                    ,h.status_kbn   -- ステータス
                    ,h.sougi_keiyaku_prc    -- 葬儀契約金額
                    ,h.sougi_keiyaku_zei    -- 葬儀契約消費税	
                    ,h.sougi_harai_prc  -- 葬儀払込金額
                    ,h.sougi_wari_prc   -- 葬儀前納割引額
                    ,h.sougi_premium_service_prc   -- 葬儀サービス割増金額
                    ,h.sougi_cose_chg_prc   -- 葬儀コース変更差額金
                    ,h.sougi_meigi_chg_cost -- 名義変更手数料
                    ,h.sougi_early_use_cost -- 葬儀早期利用費
                    ,h.sougi_early_use_cost_zei -- 葬儀早期利用費消費税        
                    ,h.sougi_zei_sagaku_prc     -- 葬儀掛金消費税差額
                    ,h.sougi_zei_cd     -- 葬儀消費税コード
                    ,h.etc_keiyaku_prc  -- 壇払等の契約金額
                    ,h.etc_keiyaku_zei	-- 壇払等の契約消費税	
                    ,h.etc_harai_prc    -- 壇払等の払込金額
                    ,h.etc_wari_prc     -- 壇払等の前納割引額
                    ,h.etc_cose_chg_prc -- 壇払等のコース変更差額金
                    ,h.etc_early_use_cost       -- 壇払等の早期利用費
                    ,h.etc_early_use_cost_zei   -- 壇払等の早期利用費消費税        
                    ,h.etc_zei_sagaku_prc       -- 壇払等の掛金消費税差額
                    ,h.etc_zei_cd   -- 壇払等の消費税コード
                    ,h.seko_prc_kakute_kbn   -- 施行金額確定区分
                    ,h.delete_flg   -- 削除フラグ
                    ,tt.tanto_nm
                    ,tt.tanto_knm
                    ,b.bumon_lnm
                    ,b.bumon_snm
                    ,b.bumon_lknm
                    ,b.bumon_sknm
                    ,b.bumon_kbn
                    ,h.n_free9
                    ,h.n_free10
                    ,h.n_free5
                    FROM uriage_denpyo h
                    LEFT JOIN tanto_mst tt
                        ON tt.delete_flg = 0
                        AND h.tanto_cd = tt.tanto_cd   
                        $dateWhere2
                    LEFT JOIN bumon_mst b
                        ON b.delete_flg = 0
                        AND h.bumon_cd = b.bumon_cd   
                        $dateWhere3
                    WHERE h.delete_flg = 0   
                        $dateWhere1
                ) T
                WHERE 
                    $whereStr
                $orderBy
                $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 売上伝票 取得(集計用)
     *
     * <AUTHOR> Kayo
     * @since      2014/08/04
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findSum($db, $keyHash = array(), $isDateEffective = false) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_no,T.disp_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; 
            $dateWhere2 = ''; 
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT *
            FROM (
                SELECT 
                    u.uri_den_no AS seikyu_no   -- 売上伝票№
                    ,u.denpyo_no    -- 受注伝票№
                    ,um.disp_no     -- 表示順		
                    ,TO_CHAR(COALESCE(u.juchu_ymd,um.juchu_ymd),'YYYY/MM/DD') AS juchu_ymd  -- 売上日
                    ,TO_CHAR(COALESCE(u.juchu_ymd,,um.juchu_ymd),'YYYYMM') AS juchu_ym      -- 売上年月
                    ,u.data_kbn     -- データ区分
                    ,u.seko_no      -- 施行番号
                    ,u.seko_no_sub  -- 施行番号（枝番）
                    ,u.bumon_cd     -- 売上部門コード
                    ,u.tanto_cd     -- 担当者コード
                    ,TO_CHAR(u.seikyu_ymd,'YYYY/MM/DD') AS seikyu_ymd   -- 請求日
                    ,TO_CHAR(u.seikyu_ymd,'YYYYMM') AS seikyu_ym        -- 請求年月
                    ,TO_CHAR(u.zen_seikyu_ymd,'YYYY/MM/DD') AS zen_seikyu_ymd   -- 前回請求日
                    ,u.nyukin_prc       -- 入金金額
                    ,u.seikyu_zan       -- 請求残高
                    ,u.uri_prc_sum      -- 売上金額合計
                    ,u.genka_prc_sum    -- 原価金額合計
                    ,u.uri_hepn_sum     -- 売上返品合計
                    ,u.uri_nebk_sum     -- 売上値引合計
                    ,u.hoshi_prc_sum    -- 奉仕料合計
                    ,u.uri_prc_sum 
                        + u.uri_nebk_sum 
                        + u.uri_hepn_sum 
                        + u.hoshi_prc_sum AS seikyu_prc -- 請求金額		
                    ,u.szei_katax_taisho_prc    -- 外税課税対象額
                    ,u.uzei_katax_taisho_prc    -- 内税課税対象額
                    ,u.hitax_katax_taisho_prc   -- 非税課税対象額
                    ,u.out_zei_prc  -- 外税消費税額
                    ,u.in_zei_prc   -- 内税消費税額
                    ,u.out_zei_prc 
                        + u.in_zei_prc AS zei_prc   -- 消費税額 	
                    ,u.uri_prc_sum
                    + u.uri_nebk_sum	  
                    + u.uri_hepn_sum
                    + u.hoshi_prc_sum
                    + u.out_zei_prc 
                    + u.sougi_keiyaku_prc + u.sougi_harai_prc
                    + u.sougi_keiyaku_zei
                    + u.sougi_wari_prc
                    + u.sougi_premium_service_prc
                    + u.sougi_meigi_chg_cost + u.sougi_meigi_chg_cost_zei
                    + u.sougi_early_use_cost + u.sougi_early_use_cost_zei
                    + u.etc_harai_prc
                    AS seikyu_zei_prc    -- 請求金額（消費税込み） 		
                    ,u.arari_prc    -- 粗利益額
                    ,u.sougi_keiyaku_prc    -- 葬儀契約金額
                    ,u.sougi_keiyaku_zei    -- 葬儀契約消費税	
                    ,u.sougi_harai_prc      -- 葬儀払込金額
                    ,u.sougi_wari_prc       -- 葬儀前納割引額
                    ,u.sougi_premium_service_prc       -- 葬儀サービス割増金額
                    ,u.sougi_cose_chg_prc   -- 葬儀コース変更差額金
                    ,u.sougi_meigi_chg_cost -- 名義変更手数料
                    ,u.sougi_early_use_cost -- 葬儀早期利用費
                    ,u.sougi_zei_sagaku_prc -- 葬儀掛金消費税差額
                    ,u.sougi_zei_cd         -- 葬儀消費税コード
                    ,u.etc_keiyaku_prc      -- 壇払等の契約金額
                    ,u.etc_keiyaku_zei      -- 壇払等の契約消費税	
                    ,u.etc_harai_prc        -- 壇払等の払込金額
                    ,u.etc_wari_prc         -- 壇払等の前納割引額
                    ,u.etc_cose_chg_prc     -- 壇払等のコース変更差額金
                    ,u.etc_early_use_cost   -- 壇払等の早期利用費
                    ,u.etc_zei_sagaku_prc   -- 壇払等の掛金消費税差額
                    ,u.etc_zei_cd   -- 壇払等の消費税コード
                    ,u.delete_flg   -- 削除フラグ
                    ,uri_prc        -- 受注金額
                    ,nebiki_prc     -- 値引額
                    ,gen_tnk        -- 原価単価
                    ,gen_gaku       -- 原価金額
                    ,arari_gaku     -- 粗利益額
                    ,out_zei_prc    -- 行外税消費税額
                    ,in_zei_prc     -- 行内税消費税額
                    ,hoshi_prc      -- 奉仕料金額
                FROM uriage_denpyo u
                LEFT JOIN uriage_denpyo_msi um
                    ON u.uri_den_no = um.uri_den_no
                    AND	0 = um.delete_flg				
                LEFT JOIN tanto_mst tt
                    ON tt.delete_flg = 0
                    AND u.tanto_cd = tt.tanto_cd   
                    $dateWhere2
                LEFT JOIN bumon_mst b
                    ON b.delete_flg = 0
                    AND u.bumon_cd = b.bumon_cd   
                    $dateWhere3
                LEFT JOIN code_nm_mst c
                    ON 0 = c.delete_flg
                    AND	'0920' = c.code_kbn
                    AND	u.data_kbn = c.kbn_value_cd_num			
                WHERE u.delete_flg = 0   
                    $dateWhere1
            ) T
            WHERE $whereStr
            $orderBy
            $tailClause
END_OF_SQL
                , $param);
        // 名札を編集
        $nafuda = '';  // 名札
        $init_flg = 0;  // 初回フラグ
        $new_key = '';  // NEWキー
        $old_key = '';  // OLDキー
        $count = 0;  // カウンター
        $retArray = array();
        $i = 0;
        foreach ($select as $uri_rec) {
            $new_key = $uri_rec['seikyu_no'];
            if ($init_flg == 0) {
                $old_key = $new_key;
                $init_flg = 1;
            }
            //請求番号がブレークしたとき
            if ($new_key !== $old_key) {
                $retArray[$i]['nafuda_nm'] = $nafuda; // 名札を再設定
                $old_key = $new_key;
                $nafuda = '';  // 名札
                $count = 0;  // カウンター
                ++$i;
            }
            ++$count;
            if ($count == 1) {
                $retArray[$i] = $uri_rec;
            }
            // 名札を編集
            if (strlen($uri_rec['nafuda_nm']) > 0) {
                if ($nafuda == '') {
                    $nafuda = $uri_rec['nafuda_nm'];
                } else {
                    $nafuda = $nafuda . '、' . $uri_rec['nafuda_nm'];
                }
            }
        }
        if ($count > 0) {
            $retArray[$i]['nafuda_nm'] = $nafuda; // 名札を再設定
        }
        return $retArray;
    }

    /**
     * コンビニ収納データアップロード用
     *
     * <AUTHOR> Matsuyama
     * @since      2017/02/15
     * @param      $db
     * @param      string $uri_den_no	売上伝票№
     * @return     array $select	売上伝票 
     */
    public static function getUriageDenpyoForConveni($db, $uri_den_no) {
        $select = $db->easySelOne(<<< END_OF_SQL
            SELECT                
                u.seko_no       -- 施行番号
                ,u.uri_den_no   -- 売上伝票№
                ,u.denpyo_no	-- 受注伝票№
                ,u.data_kbn AS data_kbn -- データ区分名							
                ,CASE u.data_kbn 
                    WHEN 1 THEN '1:葬儀'
                    WHEN 2 THEN '2：法事'
                    WHEN 3 THEN '3：単品'
                    WHEN 4 THEN '4：別注品'
                    WHEN 5 THEN '5：生前依頼'
                    WHEN 6 THEN '6：その他施行'
                    WHEN 8 THEN '8：新盆'
                ELSE '?' END AS data_kbn_nm -- データ区分名
                ,COALESCE(u.sekyu_nm,'') AS sekyu_nm -- 請求先名
                ,k.souke_nm || ' 家' AS souke_nm -- 葬家
                ,TO_CHAR(u.keijo_ymd,'YYYY/MM/DD') AS keijo_ymd -- 売上計上日    
                ,u.bumon_cd -- 売上部門コード
                ,bm.bumon_lnm AS bumon_nm   -- 売上部門名
                ,u.tanto_cd AS tanto_cd     -- 売上担当者コード
                ,tm.tanto_nm AS tanto_nm    -- 担当者名			
            FROM uriage_denpyo u
            LEFT JOIN seko_kihon_info k
                ON  k.delete_flg = 0
                AND k.seko_no = u.seko_no
            LEFT JOIN bumon_mst bm
                ON  u.bumon_cd = bm.bumon_cd
                AND 0 = bm.delete_flg			
            LEFT JOIN tanto_mst	tm
                ON u.tanto_cd = tm.tanto_cd
                AND 0 = tm.delete_flg			
            WHERE u.uri_den_no = :uri_den_no
                AND u.delete_flg = 0      
END_OF_SQL
                , array('uri_den_no' => $uri_den_no));
        return $select;
    }

}
