<?php
/**
 * Logic_FileTrans_Sample01
 *
 * ファイル転送生成ファイル 登録サンプル
 *
 * @category   App
 * @package    models\Logic\FocApi
 * <AUTHOR> Mihara
 * @since      2025/04/07
 * @filesource 
 */

/**
 * ファイル転送生成ファイル 登録サンプル
 * 
 * @category   App
 * @package    models\Logic\FocApi
 * <AUTHOR> Mihara
 * @since      2025/04/07
 */
class Logic_FileTrans_Sample01 extends Logic_FileTrans_FileTransGenLogic
{
    /**
     * ファイル作成 本処理
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @param      Logic_FileTrans_FileTransGenPushFiles $pushObj
     * @param      Msi_Sys_Db $db
     * @return     void
     */
    protected function _myLogic($pushObj, $db)
    {
        $buf = <<< END_OF_TXT
CSV-multi,ファイル1,
あああ,いいい,ううう,えええ,おおお
かかか,ききき,くくく,けけけ,こここ

END_OF_TXT;

        $file_id = $pushObj->genFileWithBuf( $file_type='CSVテスト-1.csv', $buf, $isHeaderLine=true, $isConvSjis=true );

        $buf2 = <<< END_OF_TXT
CSV-multi,ファイル2,
aaa,bbb,ccc,ddd,eee
さ,し,す,せ,そ
サ,シ,ス,セ,ソ

END_OF_TXT;

        $file_id2 = $pushObj->genFileWithBuf( $file_type='CSVテスト-2.csv', $buf2, $isHeaderLine=true, $isConvSjis=true );
    }

}
