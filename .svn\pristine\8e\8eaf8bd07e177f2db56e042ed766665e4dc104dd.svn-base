<?php
/**
 * Logic_FileTrans_FileTransGenErr
 *
 * ファイル転送生成ファイル(file_trans_gen_file) エラー登録
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/03/xx
 * @filesource 
 */

/**
 * ファイル転送生成ファイル(file_trans_gen_file) エラー登録
 * 
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/03/xx
 */
class Logic_FileTrans_FileTransGenErr
{
    /**
     * ファイル転送生成ファイル エラー登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      string $errcode    エラーコード
     * @param      string $err_detail エラー詳細
     * @param      string $ft_type    転送種別
     * @param      string $exe_mode   実行モード
     * @param      string $file_type  ファイルタイプ
     * @param      array  $param      パラメタ
     * @return     array  ファイル転送生成ファイル(file_trans_gen_file)レコード
     */
    public static function regErr( $db, $errcode, $err_detail, $ft_type, $exe_mode, $file_type='dummy', $param=array() )
    {
        // ログ出力
        $msg = sprintf("ファイル転送(%s): %s %s", $ft_type, $errcode, $err_detail);
        Msi_Sys_Utils::err( $msg );

        // 一旦、明示的にロールバック
        $db->rollback();
        $db->myPrepForUpdate();

        $myObj = new static( $db );
        $rtn = $myObj->_regErr( $errcode, $err_detail, $ft_type, $exe_mode, $file_type, $param );

        $db->commit();
        return $rtn;
    }

    /**
     * コンストラクタ
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db db
     */
    protected function __construct( $db )
    {
        $this->_db  = $db;
        list($this->_cur_ts, $this->_cur_user) = Logic_FileTrans_Utils::getTsAndUser($db);
    }

    /**
     * ファイル転送生成ファイル エラー登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      string $errcode    エラーコード
     * @param      string $err_detail エラー詳細
     * @param      string $ft_type    転送種別
     * @param      string $exe_mode   実行モード
     * @param      string $file_type  ファイルタイプ
     * @param      array  $param      パラメタ
     * @return     array  ファイル転送生成ファイル(file_trans_gen_file)レコード
     */
    protected function _regErr( $errcode, $err_detail, $ft_type, $exe_mode, $file_type, $param )
    {
        $db = $this->_db;
        $this->_ft_type = $ft_type;
        $this->_exe_mode = $exe_mode;
        $this->_file_type = $file_type;
        $this->_param = $param;

        $file_id   = $db->getSequenceNextVal( 'file_trans_gen_file_file_id_seq' );

        $dbFileTransGenFile = array( 'file_id'     => $file_id,
                                     'ft_type'     => $ft_type,
                                     'file_type'   => $file_type,
                                     'exe_mode'    => $exe_mode,
                                     'result'      => 'err',
                                     'errcode'     => $errcode,
                                     'err_detail'  => $err_detail,
                                     'gen_ts'      => $this->_cur_ts,
                                     'gen_user'    => $this->_cur_user,
                                     'rireki_no'   => null );
        $dbFileTransGenFile['ext_cond'] = Logic_FileTrans_Utils::getExtCondJsonStr($param);

        $isNew = DataMapper_Utils::upsertEasy( $db, 'file_trans_gen_file', $dbFileTransGenFile );

        return $dbFileTransGenFile;
    }
}
