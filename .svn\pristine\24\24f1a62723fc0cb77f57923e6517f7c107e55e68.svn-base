<?php
/**
 * App_Utils2
 *
 * App 用ユーティリティ関数
 * 会社毎にロジックが異なる場合、関数を作成する
 *
 * @category   library
 * @package    library\App
 * <AUTHOR> Sai
 * @since      2015/05/07
 * @version    2025/02/xx Tosaka bellmony版よりコピー
 * @filesource 
 */

/**
 * App 用ユーティリティ関数
 * 
 * @category   library
 * @package    library\App
 * <AUTHOR> Sai
 * @since      2015/05/07
 */
final class App_Utils2 extends App_Utils2Abstract {

    /**
     * 消費税等情報を反映した見積・請求金額を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/05/16
     * @param   number $prc_sum 
     * @param   array $shohizei 
     * @return  number 金額
     */
    public static function getJuchuSum($prc_sum, $shohizei, $sekoNo, $kbn, $history_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        // 請求金額
        $prc = $prc_sum + $shohizei[App_MitsuLib::ID_ZEIPRC];
        // データ区分
        $data_kbn = 1; // 葬儀
        $sekoKihon = DataMapper_SekoKihon::findOne($db, array('seko_no' => $sekoNo));
        if(Msi_Sys_Utils::myCount($sekoKihon) > 0){
            if ($sekoKihon['moushi_kbn'] == '2') {
                $data_kbn = 2; // 法事
            }
        }
        if($kbn == '1'){
            $denpyo = DataMapper_JuchuDenpyo::findOne($db, array('seko_no' => $sekoNo, 'data_kbn' => $data_kbn));
        }else{
            $denpyo = DataMapper_UriageDenpyo::findOne($db, array('seko_no' => $sekoNo, 'data_kbn' => $data_kbn));
        }
        if(Msi_Sys_Utils::myCount($denpyo) > 0){
            $prc += $denpyo['sougi_keiyaku_prc'] + $denpyo['sougi_harai_prc'] + $denpyo['sougi_wari_prc'];  // 残金額（コース施行）
            $prc += $denpyo['sougi_keiyaku_zei'] + $denpyo['sougi_wari_zei'];   // 会費消費税 + 割引金額消費税
            $prc += $denpyo['sougi_early_use_cost'];                            // 早期利用費
            $prc += $denpyo['sougi_early_use_cost_zei'];                        // 早期利用費消費税
            $prc += $denpyo['sougi_premium_service_prc'];                       // 割増金額
            $prc += $denpyo['sougi_tokuten_prc'] + $denpyo['n_free9'] + $denpyo['n_free10'];    // 完納充当
            $prc += $denpyo['n_free5'];                                         // 残金額（完納充当）
        }
        return $prc;
    }

    /**
     * 互助会値引マスタによる値引額調整(3)
     * 見積の割引を一行にまとめる対応
     *
     * <AUTHOR> sai
     * @since 2015/11/26
     * @param  string $seko_no
     * @param  array  $data
     * @return void
     */
    public static function adjGojokaiNebiki3($seko_no, &$data) {
//        App_DevCoverage_Manager::easyStart();
        $db = Msi_Sys_DbManager::getMyDb();
        $add_kbn_reikyu = '6';   // 追加区分：霊柩車
        $kihonData = DataMapper_SekoKihon::find($db, array('seko_no' => $seko_no));
        $kaiin_g = '100';   // 互助会
        $kaiin_k = '700';   // 企業
        $kaiin_kbn = null;
        // 移行データの場合は処理しない
        if (count($kihonData) > 0 && $kihonData[0]['convert_kbn'] <> 0) {
            return;
        }
        if (count($kihonData) > 0 && strlen($kihonData[0]['sougi_ymd_ex']) > 0) {
            $kijun = $kihonData[0]['sougi_ymd_ex'];
        } else {
            $kijun = Msi_Sys_Utils::getDate();
        }
        if ($kihonData[0]['moushi_kbn'] == '2') {
            $data_kbn = '2';
        } else {
            $data_kbn = '1';
        }
        // 用途によって分岐
        $gojoMember = DataMapper_SekoGojokaiMember::find($db, array('seko_no' => $seko_no));
        // 優先順位はコース施行→使用しない
        $yoto_kbn = null;
        $yoto_kbn_sub = null;
        foreach ($gojoMember as $val) {
            if (isset($yoto_kbn) && $yoto_kbn == '1') {
                continue;
            }
            if ($val['yoto_kbn'] == '1') {
                $yoto_kbn = $val['yoto_kbn'];
            } else {
                $yoto_kbn_sub = $val['yoto_kbn'];
            }
        }
        if (!isset($yoto_kbn) && isset($yoto_kbn_sub)) {
            $yoto_kbn = $yoto_kbn_sub;
        }
        $seko_plan_cd = $kihonData[0]['seko_plan_cd'];
        if (isset($seko_plan_cd)) {
            $planData = DataMapper_SekoPlan::findOne($db, array('seko_plan_cd' => $seko_plan_cd));
            $kaiin_kbn = $planData['gojokai_kbn'];
        }
        $shohinKbnAry = array();
        foreach ($data as &$rec) {
            $rec['juto_orderby'] = 1;
            // アップグレード商品は値引処理を行わない
            if (isset($rec['upgrade_kbn']) && $rec['upgrade_kbn'] == '2') {
                $rec['gojokai_nebiki_prc'] = 0;
                $rec['n_free2'] = null;
                continue;
            }
            $shohin_cd = null;
            $juchu_tnk = 0;
            $dai_bunrui = null;
            $chu_bunrui = null;
            $shohin_kbn = null;
            if (isset($rec['item']) && strlen($rec['item']) > 0) {
                $shohin_cd = $rec['item'];
            } else if (isset($rec['shohin_cd']) && strlen($rec['shohin_cd']) > 0) {
                $shohin_cd = $rec['shohin_cd'];
            }
            if (isset($rec['hanbai_tnk']) && strlen($rec['hanbai_tnk']) > 0) {
                $juchu_tnk = $rec['hanbai_tnk'];
            } else if (isset($rec['price']) && strlen($rec['price']) > 0) {
                $juchu_tnk = $rec['price'];
            } else if (isset($rec['juchu_tnk']) && strlen($rec['juchu_tnk']) > 0) {
                $juchu_tnk = $rec['juchu_tnk'];
            }
            if (isset($rec['juchu_suryo']) && strlen($rec['juchu_suryo']) > 0) {
                $juchu_suryo = $rec['juchu_suryo'];
            } else if (isset($rec['quantity']) && strlen($rec['quantity']) > 0) {
                $juchu_suryo = $rec['quantity'];
            }
            if (isset($rec['daibunruicd']) && strlen($rec['daibunruicd']) > 0) {
                $dai_bunrui = $rec['daibunruicd'];
            } else if (isset($rec['dai_bunrui_cd']) && strlen($rec['dai_bunrui_cd']) > 0) {
                $dai_bunrui = $rec['dai_bunrui_cd'];
            }
            if (isset($rec['chubunruicd']) && strlen($rec['chubunruicd']) > 0) {
                $chu_bunrui = $rec['chubunruicd'];
            } else if (isset($rec['chu_bunrui_cd']) && strlen($rec['chu_bunrui_cd']) > 0) {
                $chu_bunrui = $rec['chu_bunrui_cd'];
            }
            if (isset($rec['shohinkbn']) && strlen($rec['shohinkbn']) > 0) {
                $shohin_kbn = $rec['shohinkbn'];
            } else if (isset($rec['shohin_kbn']) && strlen($rec['shohin_kbn']) > 0) {
                $shohin_kbn = $rec['shohin_kbn'];
            }
            $shohin_bumon_cd = $rec['shohin_bumon_cd'];
            // 追加区分：6(霊柩車)の場合は商品単価マスタの単価を$juchu_tnkに設定する
            if (isset($rec['add_kbn']) && $rec['add_kbn'] == $add_kbn_reikyu) {
                $shohinTnk = DataMapper_ShohinTankaMst::find($db
                                , array('shohin_cd' => $shohin_cd, 'bumon_cd' => $shohin_bumon_cd, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                , false);
                if (count($shohinTnk) > 0) {
                    $juchu_tnk = $shohinTnk[0]['hanbai_tnk'];
                }
            }
            $gojokai_nebiki_prc = 0;
            $nebiki_ritsu = null;
            // 施行プランマスタを取得
            if (isset($seko_plan_cd)) {
                $planData = DataMapper_SekoPlan::findOne($db, array('seko_plan_cd' => $seko_plan_cd));
                // 契約先割引率マスタを参照する
                $recKeiyakuNebiki = DataMapper_KeiyakuSakiRateGroup::find($db
                                , array('partner_grp_cd' => $planData['wari_ptn_cd'], 'seko_kbn' => $data_kbn, 'dai_bunrui_cd' => $dai_bunrui, 'chu_bunrui_cd' => $chu_bunrui, 'shohin_kbn' => $shohin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                , false);
                if (count($recKeiyakuNebiki) > 0) {
                    // 割引金額が設定されれば割引金額、率であれば単価×数量×率で設定する
                    if (isset($recKeiyakuNebiki[0]['waribiki_prc']) && $recKeiyakuNebiki[0]['waribiki_prc'] != '0') {
                        // 合算充当商品区分が設定されている場合は分配方式
                        if (isset($recKeiyakuNebiki[0]['jyuto_shohin_kbn'])) {
                            $rec['juto_orderby'] = 0;
                            $key = $dai_bunrui . '-' . $chu_bunrui . '-' . $shohin_kbn;
                            if (!array_key_exists($key, $shohinKbnAry)) {
                                $shohinKbnAry[$key] = array('moto_dai_bunrui' => $dai_bunrui
                                    , 'moto_chu_bunrui' => $chu_bunrui
                                    , 'moto_shohin_kbn' => $shohin_kbn
                                    , 'saki_shohin_kbn' => $recKeiyakuNebiki[0]['jyuto_shohin_kbn']
                                    , 'juto_prc' => $recKeiyakuNebiki[0]['waribiki_prc']
                                );
                            }
                        }
                        $gojokai_nebiki_prc = $recKeiyakuNebiki[0]['waribiki_prc'];
                    } else {
                        $quantity = $juchu_suryo;
                        // 単価×数量×率
                        $gojokai_nebiki_prc = floor(($juchu_tnk * $quantity) * $recKeiyakuNebiki[0]['waribiki_ritsu']); // 小数点切り捨て
                        $nebiki_ritsu = $recKeiyakuNebiki[0]['waribiki_ritsu'] * 100;
                    }
                } else {
                    $recGojoNebiki = DataMapper_GojokaiNebiki::find($db
                                    , array('bumon_cd' => $shohin_bumon_cd, 'shohin_cd' => $shohin_cd, 'gojokai_kbn' => $kaiin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                    , false);
                    if (count($recGojoNebiki) > 0) {
                        if ($recGojoNebiki[0]['nebiki_kbn'] == 1) { // 金額値引
                            $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'];
                        } else if ($recGojoNebiki[0]['nebiki_kbn'] == 0) { // 率による値引
                            $gojokai_nebiki_prc = floor($juchu_tnk * $recGojoNebiki[0]['nebiki_ritu']); // 小数点切り捨て
                        } else if ($recGojoNebiki[0]['nebiki_kbn'] == 3) { // 単価×数量での値引き
                            $quantity = 0;
                            if ($recGojoNebiki[0]['nebiki_suryo'] <= $juchu_suryo) {
                                $quantity = $recGojoNebiki[0]['nebiki_suryo'];
                            } else {
                                $quantity = $juchu_suryo;
                            }
                            $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'] * $quantity;
                        }
                    } else {
                        // 互助会値引マスタに該当しなければコース・会員割引率マスタを参照する(会員区分を条件)
                        $recKaiinWari = DataMapper_CourseKaiinWari::find($db
                                        , array('gojokai_kbn' => $kaiin_kbn, 'seko_kbn' => $data_kbn, 'dai_bunrui_cd' => $dai_bunrui, 'chu_bunrui_cd' => $chu_bunrui, 'shohin_kbn' => $shohin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                        , false);
                        if (count($recKaiinWari) > 0) {
                            $gojokai_nebiki_prc = floor($juchu_tnk * $recKaiinWari[0]['waribiki_ritsu']); // 小数点切り捨て
                        }
                    }
                }
            } else {
                $recGojoNebiki = DataMapper_GojokaiNebiki::find($db
                                , array('bumon_cd' => $shohin_bumon_cd, 'shohin_cd' => $shohin_cd, 'gojokai_kbn' => $kaiin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                , false);
                if (count($recGojoNebiki) > 0) {
                    if ($recGojoNebiki[0]['nebiki_kbn'] == 1) { // 金額値引
                        $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'];
                    } else if ($recGojoNebiki[0]['nebiki_kbn'] == 0) { // 率による値引
                        $gojokai_nebiki_prc = floor($juchu_tnk * $recGojoNebiki[0]['nebiki_ritu']); // 小数点切り捨て
                    } else if ($recGojoNebiki[0]['nebiki_kbn'] == 3) { // 単価×数量での値引き
                        $quantity = 0;
                        if ($recGojoNebiki[0]['nebiki_suryo'] <= $juchu_suryo) {
                            $quantity = $recGojoNebiki[0]['nebiki_suryo'];
                        } else {
                            $quantity = $juchu_suryo;
                        }
                        $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'] * $quantity;
                    }
                } else {
                    // 互助会値引マスタに該当しなければコース・会員割引率マスタを参照する(会員区分を条件)
                    $recKaiinWari = DataMapper_CourseKaiinWari::find($db
                                    , array('gojokai_kbn' => $kaiin_kbn, 'seko_kbn' => $data_kbn, 'dai_bunrui_cd' => $dai_bunrui, 'chu_bunrui_cd' => $chu_bunrui, 'shohin_kbn' => $shohin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                    , false);
                    if (count($recKaiinWari) > 0) {
                        $gojokai_nebiki_prc = floor($juchu_tnk * $recKaiinWari[0]['waribiki_ritsu']); // 小数点切り捨て
                    }
                }
            }
            if ($gojokai_nebiki_prc > 0) {
                $gojokai_nebiki_prc = -1 * $gojokai_nebiki_prc;
            }
            // 単価が0円以下なら値引設定はしない
            if ($juchu_tnk <= 0) {
                $rec['gojokai_nebiki_prc'] = 0;
                continue;
            }
            $rec['gojokai_nebiki_prc'] = $gojokai_nebiki_prc;
            $rec['n_free2'] = $nebiki_ritsu;
        }
        App_Utils2::adjJutoNebiki($data, $shohinKbnAry);
    }

    /**
     *
     * 値引額調整
     * 見積の割引を一行にまとめる対応
     *
     * <AUTHOR> tosaka
     * @since 2023/xx/xx
     * @param  array  $data
     * @param  array  $jutoData
     * @return void
     */
    public static function adjJutoNebiki(&$data, $jutoData) {
        // 値引き額は分配方式で行う
        if (count($jutoData) == 0) {
            return;
        }
        array_multisort(array_column($data, 'juto_orderby'), SORT_ASC, $data);
        foreach ($jutoData as $val) {
            $moto_dai_bunrui = $val['moto_dai_bunrui'];
            $moto_chu_bunrui = $val['moto_chu_bunrui'];
            $moto_shohin_kbn = $val['moto_shohin_kbn'];
            $saki_shohin_kbn = $val['saki_shohin_kbn'];
            $nebiki_prc = $val['juto_prc'] * -1;
            $taisho_nebiki_prc = $val['juto_prc'] * -1;
            $taisho_prc = 0;
            foreach ($data as $rec) {
                $dai_bunrui = null;
                $chu_bunrui = null;
                $shohin_kbn = null;
                $juchu_tnk = 0;
                $juchu_suryo = 0;
                $gojokai_nebiki_prc = 0;
                if (isset($rec['hanbai_tnk']) && strlen($rec['hanbai_tnk']) > 0) {
                    $juchu_tnk = $rec['hanbai_tnk'];
                } else if (isset($rec['price']) && strlen($rec['price']) > 0) {
                    $juchu_tnk = $rec['price'];
                } else if (isset($rec['juchu_tnk']) && strlen($rec['juchu_tnk']) > 0) {
                    $juchu_tnk = $rec['juchu_tnk'];
                }
                if (isset($rec['juchu_suryo']) && strlen($rec['juchu_suryo']) > 0) {
                    $juchu_suryo = $rec['juchu_suryo'];
                } else if (isset($rec['quantity']) && strlen($rec['quantity']) > 0) {
                    $juchu_suryo = $rec['quantity'];
                }
                if (isset($rec['daibunruicd']) && strlen($rec['daibunruicd']) > 0) {
                    $dai_bunrui = $rec['daibunruicd'];
                } else if (isset($rec['dai_bunrui_cd']) && strlen($rec['dai_bunrui_cd']) > 0) {
                    $dai_bunrui = $rec['dai_bunrui_cd'];
                }
                if (isset($rec['chubunruicd']) && strlen($rec['chubunruicd']) > 0) {
                    $chu_bunrui = $rec['chubunruicd'];
                } else if (isset($rec['chu_bunrui_cd']) && strlen($rec['chu_bunrui_cd']) > 0) {
                    $chu_bunrui = $rec['chu_bunrui_cd'];
                }
                if (isset($rec['shohinkbn']) && strlen($rec['shohinkbn']) > 0) {
                    $shohin_kbn = $rec['shohinkbn'];
                } else if (isset($rec['shohin_kbn']) && strlen($rec['shohin_kbn']) > 0) {
                    $shohin_kbn = $rec['shohin_kbn'];
                }
                if ($dai_bunrui == $moto_dai_bunrui && $chu_bunrui == $moto_chu_bunrui && $shohin_kbn == $moto_shohin_kbn) {
                    $taisho_prc += $juchu_tnk * $juchu_suryo;
                } else if ($shohin_kbn == $saki_shohin_kbn) {
                    $taisho_prc += $juchu_tnk * $juchu_suryo;
                }
            }
            foreach ($data as &$rec) {
                $dai_bunrui = null;
                $chu_bunrui = null;
                $shohin_kbn = null;
                $juchu_tnk = 0;
                $juchu_suryo = 0;
                $gojokai_nebiki_prc = 0;
                if (isset($rec['hanbai_tnk']) && strlen($rec['hanbai_tnk']) > 0) {
                    $juchu_tnk = $rec['hanbai_tnk'];
                } else if (isset($rec['price']) && strlen($rec['price']) > 0) {
                    $juchu_tnk = $rec['price'];
                } else if (isset($rec['juchu_tnk']) && strlen($rec['juchu_tnk']) > 0) {
                    $juchu_tnk = $rec['juchu_tnk'];
                }
                if (isset($rec['juchu_suryo']) && strlen($rec['juchu_suryo']) > 0) {
                    $juchu_suryo = $rec['juchu_suryo'];
                } else if (isset($rec['quantity']) && strlen($rec['quantity']) > 0) {
                    $juchu_suryo = $rec['quantity'];
                }
                if (isset($rec['daibunruicd']) && strlen($rec['daibunruicd']) > 0) {
                    $dai_bunrui = $rec['daibunruicd'];
                } else if (isset($rec['dai_bunrui_cd']) && strlen($rec['dai_bunrui_cd']) > 0) {
                    $dai_bunrui = $rec['dai_bunrui_cd'];
                }
                if (isset($rec['chubunruicd']) && strlen($rec['chubunruicd']) > 0) {
                    $chu_bunrui = $rec['chubunruicd'];
                } else if (isset($rec['chu_bunrui_cd']) && strlen($rec['chu_bunrui_cd']) > 0) {
                    $chu_bunrui = $rec['chu_bunrui_cd'];
                }
                if (isset($rec['shohinkbn']) && strlen($rec['shohinkbn']) > 0) {
                    $shohin_kbn = $rec['shohinkbn'];
                } else if (isset($rec['shohin_kbn']) && strlen($rec['shohin_kbn']) > 0) {
                    $shohin_kbn = $rec['shohin_kbn'];
                }
                $prc = $juchu_tnk * $juchu_suryo;
                if ($dai_bunrui == $moto_dai_bunrui && $chu_bunrui == $moto_chu_bunrui && $shohin_kbn == $moto_shohin_kbn) {
                    if ($taisho_prc > 0 && $juchu_suryo > 0) {
                        if ($nebiki_prc >= $prc) {
                            $gojokai_nebiki_prc = $prc;
                            $nebiki_prc -= $prc;
                        } else if ($nebiki_prc < $prc) {
                            $gojokai_nebiki_prc = $nebiki_prc;
                            $nebiki_prc = 0;
                        }
                    }
                } else if ($shohin_kbn == $saki_shohin_kbn) {
                    if ($taisho_prc > 0 && $juchu_suryo > 0) {
                        if ($nebiki_prc >= $prc) {
                            $gojokai_nebiki_prc = $prc;
                            $nebiki_prc -= $prc;
                        } else if ($nebiki_prc < $prc) {
                            $gojokai_nebiki_prc = $nebiki_prc;
                            $nebiki_prc = 0;
                        }
                    }
                } else {
                    $gojokai_nebiki_prc = $rec['gojokai_nebiki_prc'];
                }
                if ($gojokai_nebiki_prc > 0) {
                    $gojokai_nebiki_prc = -1 * $gojokai_nebiki_prc;
                }
                $rec['gojokai_nebiki_prc'] = $gojokai_nebiki_prc;
            }
        }
    }

    /**
     *
     * 基本プランのパターンを求める。
     *
     * <AUTHOR> Sai
     * @since 2014/06/19
     * @version 2015/07/17 Juchu_JuchuAbstractより引越し
     * @param string $sogi  葬儀区分 1:個人 2:社葬 3:直葬 4:寺院葬
     * @param string $keishiki 形式 1:個人葬 2:団体葬（合同葬）3:家族葬 4:火葬式 5:お別れ会
     * @param string $anchi 安置先 00:自宅 01:通夜会場 99:その他
     * @param string $sikijyo 葬儀場所 00 自宅 01 寺院 02 ホール 03 他会館 04 公民館 05 通夜会場 06 他寺院 99 その他
     * @return array[main_pt_cd]      => 基本パターン区分コード
     *          array[main_pt_kbn]     => 基本パターン区分
     */
    public static function calcKihonPtn($sogi, $keishiki, $anchi, $sikijyo) {
        $main_pt_cd = null;
        $main_pt_kbn = null;
        //  3:直葬は個人置き換える
        if ($sogi === '3') {
            $sogi = '1';
        }
        if (($sogi === '1' || $sogi === '2') && $keishiki === '1' && !empty($anchi) && $anchi !== '01' && !empty($sikijyo) && $sikijyo !== '02') {
            $main_pt_cd = 'A';
            $main_pt_kbn = '1';
        } else if (($sogi === '1' || $sogi === '2') && $keishiki === '1' && !empty($anchi) && $anchi !== '01' && $sikijyo === '02') {
            $main_pt_cd = 'B';
            $main_pt_kbn = '1';
        } else if (($sogi === '1' || $sogi === '2') && $keishiki === '1' && $anchi === '01' && $sikijyo === '02') {
            $main_pt_cd = 'BT';
//            $main_pt_cd = 'B'; // BTはBを使用する
            $main_pt_kbn = '1';
        } else if (($sogi === '1' || $sogi === '2') && $keishiki === '1' && $anchi === '01' && $sikijyo === '05') {
            $main_pt_cd = 'BT';
//            $main_pt_cd = 'B'; // BTはBを使用する
            $main_pt_kbn = '1';
        } else if (($sogi === '1' || $sogi === '2') && $keishiki === '1' && $anchi === '01' && !empty($sikijyo) && $sikijyo !== '00') {
            $main_pt_cd = 'A';
            $main_pt_kbn = '1';
        } else if ($sogi === '1' && $keishiki === '3' && !empty($anchi) && $anchi !== '01' && !empty($sikijyo) && $sikijyo !== '02') {
            $main_pt_cd = 'D1';
            $main_pt_kbn = '3';
        } else if ($sogi === '1' && $keishiki === '3' && !empty($anchi) && $anchi !== '01' && $sikijyo === '02') {
            $main_pt_cd = 'D2';
            $main_pt_kbn = '3';
        } else if ($sogi === '1' && $keishiki === '3' && $anchi === '01' && $sikijyo === '02') {
            $main_pt_cd = 'D3';
            $main_pt_kbn = '3';
        } else if (($sogi === '1' || $sogi === '2') && $keishiki === '4' && !empty($anchi) && $anchi !== '01') {
            $main_pt_cd = 'K1';
            $main_pt_kbn = '4';
        } else if (($sogi === '1' || $sogi === '2') && $keishiki === '4' && $anchi === '01') {
            $main_pt_cd = 'K2';
            $main_pt_kbn = '4';
        } else if (($sogi === '1' || $sogi === '2') && $keishiki === '5' && $sikijyo === '02') {
            $main_pt_cd = 'C';
            $main_pt_kbn = '5';
        }
        return array($main_pt_cd, $main_pt_kbn);
    }

    /**
     * デフォルトは互助会の掛金残に対する税金を設定している
     * 佐野の場合は契約金に対する税金に置き換える
     *
     * <AUTHOR> Sai
     * @since 2015/10/14
     * @param  array  $zeiInfo 
     * @return void
     */
    public static function setZeiPrc(&$zeiInfo) {
        if (isset($zeiInfo['keiyaku_gaku_zei2'])) {
            $zeiInfo['zan_gaku_zei'] = $zeiInfo['keiyaku_gaku_zei2'];
        }
    }

    /**
     * 付帯値引き設定処理
     *
     * <AUTHOR> Sai
     * @since 2015/11/25
     * @param  array  $msi 
     * @param  string  $seko_no 
     * @param  array  $keyMapper
     * @return void
     */
    public static function setGojokaiNebiki(&$msi, $seko_no, $keyMapper = null) {
        $db = Msi_Sys_DbManager::getMyDb();
        $add_kbn_reikyu = '6';   // 追加区分：霊柩車
        $kihonData = DataMapper_SekoKihon::find($db, array('seko_no' => $seko_no));
        $kaiin_g = '100';   // 互助会
        $kaiin_k = '700';   // 企業
        $kaiin_kbn = null;
        // 移行データの場合は処理しない
        if (count($kihonData) > 0 && $kihonData[0]['convert_kbn'] <> 0) {
            return;
        }
        if (count($kihonData) > 0 && strlen($kihonData[0]['sougi_ymd']) > 0) {
            $kijun = $kihonData[0]['sougi_ymd'];
        } else {
            $kijun = Msi_Sys_Utils::getDate();
        }
        if ($kihonData[0]['moushi_kbn'] == '2') {
            $data_kbn = '2';
        } else {
            $data_kbn = '1';
        }
        // 用途によって分岐
        $gojoMember = DataMapper_SekoGojokaiMember::find($db, array('seko_no' => $seko_no));
        // 優先順位はコース施行→使用しない
        $yoto_kbn = null;
        $yoto_kbn_sub = null;
        foreach ($gojoMember as $val) {
            if (isset($yoto_kbn) && $yoto_kbn == '1') {
                continue;
            }
            if ($val['yoto_kbn'] == '1') {
                $yoto_kbn = $val['yoto_kbn'];
            } else {
                $yoto_kbn_sub = $val['yoto_kbn'];
            }
        }
        if (!isset($yoto_kbn) && isset($yoto_kbn_sub)) {
            $yoto_kbn = $yoto_kbn_sub;
        }
        $seko_plan_cd = $kihonData[0]['seko_plan_cd'];
        if (isset($seko_plan_cd)) {
            $planData = DataMapper_SekoPlan::findOne($db, array('seko_plan_cd' => $seko_plan_cd));
            $kaiin_kbn = $planData['gojokai_kbn'];
        }
        $shohinKbnAry = array();
        foreach ($msi as &$rec) {
            $rec['juto_orderby'] = 1;
            // アップグレード商品は値引処理を行わない
            if (isset($rec['upgrade_kbn']) && $rec['upgrade_kbn'] == '2') {
                $rec['gojokai_nebiki_prc'] = 0;
                $rec['n_free2'] = null;
                continue;
            }
            $shohin_cd = null;
            $juchu_tnk = 0;
            $dai_bunrui = null;
            $chu_bunrui = null;
            $shohin_kbn = null;
            if (isset($rec['item']) && strlen($rec['item']) > 0) {
                $shohin_cd = $rec['item'];
            } else if (isset($rec['shohin_cd']) && strlen($rec['shohin_cd']) > 0) {
                $shohin_cd = $rec['shohin_cd'];
            }
            if (isset($rec['hanbai_tnk']) && strlen($rec['hanbai_tnk']) > 0) {
                $juchu_tnk = $rec['hanbai_tnk'];
            } else if (isset($rec['price']) && strlen($rec['price']) > 0) {
                $juchu_tnk = $rec['price'];
            } else if (isset($rec['juchu_tnk']) && strlen($rec['juchu_tnk']) > 0) {
                $juchu_tnk = $rec['juchu_tnk'];
            }
            if (isset($rec['juchu_suryo']) && strlen($rec['juchu_suryo']) > 0) {
                $juchu_suryo = $rec['juchu_suryo'];
            } else if (isset($rec['quantity']) && strlen($rec['quantity']) > 0) {
                $juchu_suryo = $rec['quantity'];
            }
            if (isset($rec['daibunruicd']) && strlen($rec['daibunruicd']) > 0) {
                $dai_bunrui = $rec['daibunruicd'];
            } else if (isset($rec['dai_bunrui_cd']) && strlen($rec['dai_bunrui_cd']) > 0) {
                $dai_bunrui = $rec['dai_bunrui_cd'];
            }
            if (isset($rec['chubunruicd']) && strlen($rec['chubunruicd']) > 0) {
                $chu_bunrui = $rec['chubunruicd'];
            } else if (isset($rec['chu_bunrui_cd']) && strlen($rec['chu_bunrui_cd']) > 0) {
                $chu_bunrui = $rec['chu_bunrui_cd'];
            }
            if (isset($rec['shohinkbn']) && strlen($rec['shohinkbn']) > 0) {
                $shohin_kbn = $rec['shohinkbn'];
            } else if (isset($rec['shohin_kbn']) && strlen($rec['shohin_kbn']) > 0) {
                $shohin_kbn = $rec['shohin_kbn'];
            }
            $shohin_bumon_cd = $rec['shohin_bumon_cd'];
            // 追加区分：6(霊柩車)の場合は商品単価マスタの単価を$juchu_tnkに設定する
            if (isset($rec['add_kbn']) && $rec['add_kbn'] == $add_kbn_reikyu) {
                $shohinTnk = DataMapper_ShohinTankaMst::find($db
                                , array('shohin_cd' => $shohin_cd, 'bumon_cd' => $shohin_bumon_cd, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                , false);
                if (count($shohinTnk) > 0) {
                    $juchu_tnk = $shohinTnk[0]['hanbai_tnk'];
                }
            }
            $gojokai_nebiki_prc = 0;
            $nebiki_ritsu = null;
            // 施行プランマスタを取得
            if (isset($seko_plan_cd)) {
                $planData = DataMapper_SekoPlan::findOne($db, array('seko_plan_cd' => $seko_plan_cd));
                // 契約先割引率マスタを参照する
                $recKeiyakuNebiki = DataMapper_KeiyakuSakiRateGroup::find($db
                                , array('partner_grp_cd' => $planData['wari_ptn_cd'], 'seko_kbn' => $data_kbn, 'dai_bunrui_cd' => $dai_bunrui, 'chu_bunrui_cd' => $chu_bunrui, 'shohin_kbn' => $shohin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                , false);
                if (count($recKeiyakuNebiki) > 0) {
                    // 割引金額が設定されれば割引金額、率であれば単価×数量×率で設定する
                    if (isset($recKeiyakuNebiki[0]['waribiki_prc']) && $recKeiyakuNebiki[0]['waribiki_prc'] != '0') {
                        // 合算充当商品区分が設定されている場合は分配方式
                        if (isset($recKeiyakuNebiki[0]['jyuto_shohin_kbn'])) {
                            $rec['juto_orderby'] = 0;
                            $key = $dai_bunrui . '-' . $chu_bunrui . '-' . $shohin_kbn;
                            if (!array_key_exists($key, $shohinKbnAry)) {
                                $shohinKbnAry[$key] = array('moto_dai_bunrui' => $dai_bunrui
                                    , 'moto_chu_bunrui' => $chu_bunrui
                                    , 'moto_shohin_kbn' => $shohin_kbn
                                    , 'saki_shohin_kbn' => $recKeiyakuNebiki[0]['jyuto_shohin_kbn']
                                    , 'juto_prc' => $recKeiyakuNebiki[0]['waribiki_prc']
                                );
                            }
                        }
                        $gojokai_nebiki_prc = $recKeiyakuNebiki[0]['waribiki_prc'];
                    } else {
                        $quantity = $juchu_suryo;
                        // 単価×数量×率
                        $gojokai_nebiki_prc = floor(($juchu_tnk * $quantity) * $recKeiyakuNebiki[0]['waribiki_ritsu']); // 小数点切り捨て
                        $nebiki_ritsu = $recKeiyakuNebiki[0]['waribiki_ritsu'] * 100;
                    }
                } else {
                    $recGojoNebiki = DataMapper_GojokaiNebiki::find($db
                                    , array('bumon_cd' => $shohin_bumon_cd, 'shohin_cd' => $shohin_cd, 'gojokai_kbn' => $kaiin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                    , false);
                    if (count($recGojoNebiki) > 0) {
                        if ($recGojoNebiki[0]['nebiki_kbn'] == 1) { // 金額値引
                            $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'];
                        } else if ($recGojoNebiki[0]['nebiki_kbn'] == 0) { // 率による値引
                            $gojokai_nebiki_prc = floor($juchu_tnk * $recGojoNebiki[0]['nebiki_ritu']); // 小数点切り捨て
                        } else if ($recGojoNebiki[0]['nebiki_kbn'] == 3) { // 単価×数量での値引き
                            $quantity = 0;
                            if ($recGojoNebiki[0]['nebiki_suryo'] <= $juchu_suryo) {
                                $quantity = $recGojoNebiki[0]['nebiki_suryo'];
                            } else {
                                $quantity = $juchu_suryo;
                            }
                            $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'] * $quantity;
                        }
                    } else {
                        // 互助会値引マスタに該当しなければコース・会員割引率マスタを参照する(会員区分を条件)
                        $recKaiinWari = DataMapper_CourseKaiinWari::find($db
                                        , array('gojokai_kbn' => $kaiin_kbn, 'seko_kbn' => $data_kbn, 'dai_bunrui_cd' => $dai_bunrui, 'chu_bunrui_cd' => $chu_bunrui, 'shohin_kbn' => $shohin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                        , false);
                        if (count($recKaiinWari) > 0) {
                            $gojokai_nebiki_prc = floor($juchu_tnk * $recKaiinWari[0]['waribiki_ritsu']); // 小数点切り捨て
                        }
                    }
                }
            } else {
                $recGojoNebiki = DataMapper_GojokaiNebiki::find($db
                                , array('bumon_cd' => $shohin_bumon_cd, 'shohin_cd' => $shohin_cd, 'gojokai_kbn' => $kaiin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                , false);
                if (count($recGojoNebiki) > 0) {
                    if ($recGojoNebiki[0]['nebiki_kbn'] == 1) { // 金額値引
                        $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'];
                    } else if ($recGojoNebiki[0]['nebiki_kbn'] == 0) { // 率による値引
                        $gojokai_nebiki_prc = floor($juchu_tnk * $recGojoNebiki[0]['nebiki_ritu']); // 小数点切り捨て
                    } else if ($recGojoNebiki[0]['nebiki_kbn'] == 3) { // 単価×数量での値引き
                        $quantity = 0;
                        if ($recGojoNebiki[0]['nebiki_suryo'] <= $juchu_suryo) {
                            $quantity = $recGojoNebiki[0]['nebiki_suryo'];
                        } else {
                            $quantity = $juchu_suryo;
                        }
                        $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'] * $quantity;
                    }
                } else {
                    // 互助会値引マスタに該当しなければコース・会員割引率マスタを参照する(会員区分を条件)
                    $recKaiinWari = DataMapper_CourseKaiinWari::find($db
                                    , array('gojokai_kbn' => $kaiin_kbn, 'seko_kbn' => $data_kbn, 'dai_bunrui_cd' => $dai_bunrui, 'chu_bunrui_cd' => $chu_bunrui, 'shohin_kbn' => $shohin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                    , false);
                    if (count($recKaiinWari) > 0) {
                        $gojokai_nebiki_prc = floor($juchu_tnk * $recKaiinWari[0]['waribiki_ritsu']); // 小数点切り捨て
                    }
                }
            }
            if ($gojokai_nebiki_prc > 0) {
                $gojokai_nebiki_prc = -1 * $gojokai_nebiki_prc;
            }
            // 返品時付帯特典をプラン金額に
            if (isset($rec['juchu_suryo']) && $rec['juchu_suryo'] < 0 && $gojokai_nebiki_prc < 0) {
                $gojokai_nebiki_prc = -1 * $gojokai_nebiki_prc;
            }
            // 単価が0円以下なら値引設定はしない
            if ($juchu_tnk <= 0) {
                $rec['gojokai_nebiki_prc'] = 0;
                continue;
            }
            $rec['gojokai_nebiki_prc'] = $gojokai_nebiki_prc;
            $rec['n_free2'] = $nebiki_ritsu;
        }
        App_Utils2::adjJutoNebiki($msi, $shohinKbnAry);
    }

    /**
     * 付帯値引き設定処理(一明細用)
     *
     * <AUTHOR> Sai
     * @since 2015/11/25
     * @param  array  $msi 
     * @param  string  $seko_no 
     * @param  array  $keyMapper
     * @return void
     */
    public static function setGojokaiNebikiOne(&$rec, $seko_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $add_kbn_reikyu = '6';   // 追加区分：霊柩車
        $kihonData = DataMapper_SekoKihon::find($db, array('seko_no' => $seko_no));
        $kaiin_g = '100';   // 互助会
        $kaiin_k = '700';   // 企業
        $kaiin_kbn = null;
        // 移行データの場合は処理しない
        if (count($kihonData) > 0 && $kihonData[0]['convert_kbn'] <> 0) {
            return;
        }
        if (count($kihonData) > 0 && strlen($kihonData[0]['sougi_ymd_ex']) > 0) {
            $kijun = $kihonData[0]['sougi_ymd_ex'];
        } else {
            $kijun = Msi_Sys_Utils::getDate();
        }
        if ($kihonData[0]['moushi_kbn'] == '2') {
            $data_kbn = '2';
        } else {
            $data_kbn = '1';
        }
        // 用途によって分岐
        $gojoMember = DataMapper_SekoGojokaiMember::find($db, array('seko_no' => $seko_no));
        // 優先順位はコース施行→使用しない
        $yoto_kbn = null;
        $yoto_kbn_sub = null;
        foreach ($gojoMember as $val) {
            if (isset($yoto_kbn) && $yoto_kbn == '1') {
                continue;
            }
            if ($val['yoto_kbn'] == '1') {
                $yoto_kbn = $val['yoto_kbn'];
            } else {
                $yoto_kbn_sub = $val['yoto_kbn'];
            }
        }
        if (!isset($yoto_kbn) && isset($yoto_kbn_sub)) {
            $yoto_kbn = $yoto_kbn_sub;
        }
        $seko_plan_cd = $kihonData[0]['seko_plan_cd'];
        if (isset($seko_plan_cd)) {
            $planData = DataMapper_SekoPlan::findOne($db, array('seko_plan_cd' => $seko_plan_cd));
            $kaiin_kbn = $planData['gojokai_kbn'];
        }
        $shohinKbnAry = array();
        // アップグレード商品は値引処理を行わない
        if (isset($rec['upgrade_kbn']) && $rec['upgrade_kbn'] == '2') {
            $rec['gojokai_nebiki_prc'] = 0;
            $rec['n_free2'] = null;
            return;
        }
        $shohin_cd = null;
        $juchu_tnk = 0;
        $juchu_suryo = 0;
        $dai_bunrui = null;
        $chu_bunrui = null;
        $shohin_kbn = null;
        if (isset($rec['item']) && strlen($rec['item']) > 0) {
            $shohin_cd = $rec['item'];
        } else if (isset($rec['shohin_cd']) && strlen($rec['shohin_cd']) > 0) {
            $shohin_cd = $rec['shohin_cd'];
        }
        if (isset($rec['hanbai_tnk']) && strlen($rec['hanbai_tnk']) > 0) {
            $juchu_tnk = $rec['hanbai_tnk'];
        } else if (isset($rec['price']) && strlen($rec['price']) > 0) {
            $juchu_tnk = $rec['price'];
        } else if (isset($rec['juchu_tnk']) && strlen($rec['juchu_tnk']) > 0) {
            $juchu_tnk = $rec['juchu_tnk'];
        }
        if (isset($rec['juchu_suryo']) && strlen($rec['juchu_suryo']) > 0) {
            $juchu_suryo = $rec['juchu_suryo'];
        } else if (isset($rec['quantity']) && strlen($rec['quantity']) > 0) {
            $juchu_suryo = $rec['quantity'];
        }
        if (isset($rec['daibunruicd']) && strlen($rec['daibunruicd']) > 0) {
            $dai_bunrui = $rec['daibunruicd'];
        } else if (isset($rec['dai_bunrui_cd']) && strlen($rec['dai_bunrui_cd']) > 0) {
            $dai_bunrui = $rec['dai_bunrui_cd'];
        }
        if (isset($rec['chubunruicd']) && strlen($rec['chubunruicd']) > 0) {
            $chu_bunrui = $rec['chubunruicd'];
        } else if (isset($rec['chu_bunrui_cd']) && strlen($rec['chu_bunrui_cd']) > 0) {
            $chu_bunrui = $rec['chu_bunrui_cd'];
        }
        if (isset($rec['shohinkbn']) && strlen($rec['shohinkbn']) > 0) {
            $shohin_kbn = $rec['shohinkbn'];
        } else if (isset($rec['shohin_kbn']) && strlen($rec['shohin_kbn']) > 0) {
            $shohin_kbn = $rec['shohin_kbn'];
        }
        $shohin_bumon_cd = $rec['shohin_bumon_cd'];
        // 追加区分：6(霊柩車)の場合は商品単価マスタの単価を$juchu_tnkに設定する
        if (isset($rec['add_kbn']) && $rec['add_kbn'] == $add_kbn_reikyu) {
            $shohinTnk = DataMapper_ShohinTankaMst::find($db
                            , array('shohin_cd' => $shohin_cd, 'bumon_cd' => $shohin_bumon_cd, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                            , false);
            if (count($shohinTnk) > 0) {
                $juchu_tnk = $shohinTnk[0]['hanbai_tnk'];
            }
        }
        $gojokai_nebiki_prc = 0;
        $nebiki_ritsu = null;
        // 施行プランマスタを取得
        if (isset($seko_plan_cd)) {
            $planData = DataMapper_SekoPlan::findOne($db, array('seko_plan_cd' => $seko_plan_cd));
            // 契約先割引率マスタを参照する
            $recKeiyakuNebiki = DataMapper_KeiyakuSakiRateGroup::find($db
                            , array('partner_grp_cd' => $planData['wari_ptn_cd'], 'seko_kbn' => $data_kbn, 'dai_bunrui_cd' => $dai_bunrui, 'chu_bunrui_cd' => $chu_bunrui, 'shohin_kbn' => $shohin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                            , false);
            if (count($recKeiyakuNebiki) > 0) {
                // 割引金額が設定されれば割引金額、率であれば単価×数量×率で設定する
                if (isset($recKeiyakuNebiki[0]['waribiki_prc']) && $recKeiyakuNebiki[0]['waribiki_prc'] != '0') {
                    // 合算充当商品区分が設定されている場合は分配方式
                    if (isset($recKeiyakuNebiki[0]['jyuto_shohin_kbn'])) {
                        $key = $dai_bunrui . '-' . $chu_bunrui . '-' . $shohin_kbn;
                        if (!array_key_exists($key, $shohinKbnAry)) {
                            $shohinKbnAry[$key] = array('moto_dai_bunrui' => $dai_bunrui
                                , 'moto_chu_bunrui' => $chu_bunrui
                                , 'moto_shohin_kbn' => $shohin_kbn
                                , 'saki_shohin_kbn' => $recKeiyakuNebiki[0]['jyuto_shohin_kbn']
                                , 'juto_prc' => $recKeiyakuNebiki[0]['waribiki_prc']
                            );
                        }
                    }
                    $gojokai_nebiki_prc = $recKeiyakuNebiki[0]['waribiki_prc'];
                } else {
                    $quantity = $juchu_suryo;
                    // 単価×数量×率
                    $gojokai_nebiki_prc = floor(($juchu_tnk * $quantity) * $recKeiyakuNebiki[0]['waribiki_ritsu']); // 小数点切り捨て
                    $nebiki_ritsu = $recKeiyakuNebiki[0]['waribiki_ritsu'] * 100;
                }
            } else {
                $recGojoNebiki = DataMapper_GojokaiNebiki::find($db
                                , array('bumon_cd' => $shohin_bumon_cd, 'shohin_cd' => $shohin_cd, 'gojokai_kbn' => $kaiin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                , false);
                if (count($recGojoNebiki) > 0) {
                    if ($recGojoNebiki[0]['nebiki_kbn'] == 1) { // 金額値引
                        $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'];
                    } else if ($recGojoNebiki[0]['nebiki_kbn'] == 0) { // 率による値引
                        $gojokai_nebiki_prc = floor($juchu_tnk * $recGojoNebiki[0]['nebiki_ritu']); // 小数点切り捨て
                    } else if ($recGojoNebiki[0]['nebiki_kbn'] == 3) { // 単価×数量での値引き
                        $quantity = 0;
                        if ($recGojoNebiki[0]['nebiki_suryo'] <= $juchu_suryo) {
                            $quantity = $recGojoNebiki[0]['nebiki_suryo'];
                        } else {
                            $quantity = $juchu_suryo;
                        }
                        $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'] * $quantity;
                    }
                } else {
                    // 互助会値引マスタに該当しなければコース・会員割引率マスタを参照する(会員区分を条件)
                    $recKaiinWari = DataMapper_CourseKaiinWari::find($db
                                    , array('gojokai_kbn' => $kaiin_kbn, 'seko_kbn' => $data_kbn, 'dai_bunrui_cd' => $dai_bunrui, 'chu_bunrui_cd' => $chu_bunrui, 'shohin_kbn' => $shohin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                    , false);
                    if (count($recKaiinWari) > 0) {
                        $gojokai_nebiki_prc = floor($juchu_tnk * $recKaiinWari[0]['waribiki_ritsu']); // 小数点切り捨て
                    }
                }
            }
        } else {
            $recGojoNebiki = DataMapper_GojokaiNebiki::find($db
                            , array('bumon_cd' => $shohin_bumon_cd, 'shohin_cd' => $shohin_cd, 'gojokai_kbn' => $kaiin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                            , false);
            if (count($recGojoNebiki) > 0) {
                if ($recGojoNebiki[0]['nebiki_kbn'] == 1) { // 金額値引
                    $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'];
                } else if ($recGojoNebiki[0]['nebiki_kbn'] == 0) { // 率による値引
                    $gojokai_nebiki_prc = floor($juchu_tnk * $recGojoNebiki[0]['nebiki_ritu']); // 小数点切り捨て
                } else if ($recGojoNebiki[0]['nebiki_kbn'] == 3) { // 単価×数量での値引き
                    $quantity = 0;
                    if ($recGojoNebiki[0]['nebiki_suryo'] <= $juchu_suryo) {
                        $quantity = $recGojoNebiki[0]['nebiki_suryo'];
                    } else {
                        $quantity = $juchu_suryo;
                    }
                    $gojokai_nebiki_prc = $recGojoNebiki[0]['nebiki_prc'] * $quantity;
                }
            } else {
                // 互助会値引マスタに該当しなければコース・会員割引率マスタを参照する(会員区分を条件)
                $recKaiinWari = DataMapper_CourseKaiinWari::find($db
                                , array('gojokai_kbn' => $kaiin_kbn, 'seko_kbn' => $data_kbn, 'dai_bunrui_cd' => $dai_bunrui, 'chu_bunrui_cd' => $chu_bunrui, 'shohin_kbn' => $shohin_kbn, '__raw1' => "T.tekiyo_st_date <= '" . $kijun . "' AND T.tekiyo_ed_date >= '" . $kijun . "'")
                                , false);
                if (count($recKaiinWari) > 0) {
                    $gojokai_nebiki_prc = floor($juchu_tnk * $recKaiinWari[0]['waribiki_ritsu']); // 小数点切り捨て
                }
            }
        }
        if ($gojokai_nebiki_prc > 0) {
            $gojokai_nebiki_prc = -1 * $gojokai_nebiki_prc;
        }
        // 単価が0円以下なら値引設定はしない
        if ($juchu_tnk <= 0) {
            $rec['gojokai_nebiki_prc'] = 0;
            return;
        }
        $rec['gojokai_nebiki_prc'] = $gojokai_nebiki_prc;
        $rec['n_free2'] = $nebiki_ritsu;
        $data = array();
        $data[] = $rec;
        App_Utils2::adjJutoNebiki($data, $shohinKbnAry);
    }

    /**
     * 特別値引き処理（）
     * 　生花90・白木80以上を選択した場合、棺の割引金額の設定を行う
     * <AUTHOR> Sai
     * @since 2015/12/22
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @param  string  $juchuKakuteiYMD 受注確定日
     * @param  string  $gojokaiKbn 互助会区分
     * @param  string  $sekoPlanCd 施行プランコード
     * @param  array  $dataCol 明細データ
     * @param array &$dataApp 画面Appデータ
     * @param array $dataTrnDelCol グリッドトラン削除データ
     * @param array $delete_cds 削除データ（商品選択画面）
     * @return void
     */
    public static function adjJuchu($db, $sekoNo, $juchuDenpyoNo, $juchuKakuteiYMD, $gojokaiKbn, $sekoPlanCd, &$dataCol, $dataApp, $dataTrnDelCol = array(), $delete_cds = array()) {
        
    }

    /**
     * 互助会コースにかどうかのチェック処理
     * <AUTHOR> Sai
     * @since 2015/12/22
     * @param Msi_Sys_Db $db db
     * @param string $gojokaiKbn 互助会区分
     * @return boolean
     */
    private static function checkGojokai($db, $gojokaiKbn) {
        $flg = false;

        $sql = "
        SELECT
            gojokai_kbn
        FROM
            gojokai_price_mst
        WHERE
            gojokai_kbn = :gojokai_kbn
        AND delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('gojokai_kbn' => $gojokaiKbn));
        if (count($select) > 0) {
            $flg = true;
        }
        return $flg;
    }

    /**
     * 互助会の用途が葬送儀礼と祭壇値引きのカウントを取得する
     * <AUTHOR> Sai
     * @since 2015/12/22
     * @param Msi_Sys_Db $db db
     * @param string $sekoNo 施行番号
     * @return $cnt 
     */
    private static function getUseCount($db, $sekoNo) {
        // MYTODO 互助会利用換算口数プラン値引きは？
        $cnt = 0;
        $sql = "
        SELECT
            SUM(gcm.join_convert_cnt) AS cnt
        FROM
            seko_gojokai_member sgm
            INNER JOIN 
                gojokai_couse_mst gcm 
                ON (sgm.course_snm_cd = gcm.gojokai_cose_iw)
        WHERE
            sgm.seko_no = :seko_no
        AND sgm.yoto_kbn IN (1, 10) -- 葬送儀礼と祭壇値引き
        AND sgm.delete_flg = 0 
        AND gcm.delete_flg = 0 
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $sekoNo));
        if (count($select) > 0) {
            $cnt = $select['cnt'];
        }
        return $cnt;
    }

    /**
     * 法事用のサイドメニュー取得処理
     *
     * <AUTHOR> Kayo
     * @since 2016/07/28
     * @param  string $catKbn  カテゴリ区分
     * @param  string $sekoNo  施行番号
     * @return array サイドメニュー
     */
    public static function getSideMenuDatahoj($catKbn, $sekoNo) {
        switch ($catKbn) {
            case 101:       // 事後          
                $sideMenuData = Juchu_Utils::getSideMenuData('jigo', $sekoNo, null, '2');
                break;
            case 104:       // 位牌          
                $sideMenuData = Juchu_Utils::getSideMenuData('ihai', $sekoNo, null, '2');
                break;
            case 108:    // 仏壇          
                $sideMenuData = Juchu_Utils::getSideMenuData('butudan', $sekoNo, null, '2');
                break;
            case 112:      // 墓所・手元供養
                $sideMenuData = Juchu_Utils::getSideMenuData('bosho', $sekoNo, null, '2');
                break;
            case 115:      // 案内状        
                $sideMenuData = Juchu_Utils::getSideMenuData('annai', $sekoNo, null, '2');
                break;
            case 117:     // 祭壇・式場    
                $sideMenuData = Juchu_Utils::getSideMenuData('saidan', $sekoNo, null, '2');
                break;
            case 121:       // 法要          
                $sideMenuData = Juchu_Utils::getSideMenuData('hoyo', $sekoNo, null, '2');
                break;
            case 125:      // 墓参          
                $sideMenuData = Juchu_Utils::getSideMenuData('bosan', $sekoNo, null, '2');
                break;
            case 129:       // 返礼品        
                $sideMenuData = Juchu_Utils::getSideMenuData('gift', $sekoNo, null, '2');
                break;
            case 135:      // 法事・催事    
                $sideMenuData = Juchu_Utils::getSideMenuData('houji', $sekoNo, null, '2');
                break;
            case 137:       // 会食          
                $sideMenuData = Juchu_Utils::getSideMenuData('food', $sekoNo, null, '2');
                break;
            case 142:    // 非課税        
                $sideMenuData = Juchu_Utils::getSideMenuData('hikazei', $sekoNo, null, '2');
                break;
            default:
                throw new Exception("Juchu_JuchuItemhj00::save(): getSideMenuData( $catKbn ?");
        }
        return $sideMenuData;
    }

    /**
     * 法事用のサイドメニュー取得処理
     *
     * <AUTHOR> Kayo
     * @since 2016/07/28
     * @param  string $catKbn  カテゴリ区分
     * @return string 項目番号
     */
    public static function getItemnohoj($catKbn) {
        switch ($catKbn) {
            case 101:       // 事後          
                $itemno = 3;
                break;
            case 104:       // 位牌          
                $itemno = 4;
                break;
            case 108:    // 仏壇          
                $itemno = 5;
                break;
            case 112:      // 墓所・手元供養
                $itemno = 6;
                break;
            case 115:      // 案内状        
                $itemno = 7;
                break;
            case 117:     // 祭壇・式場    
                $itemno = 8;
                break;
            case 121:       // 法要          
                $itemno = 9;
                break;
            case 125:      // 墓参          
                $itemno = 10;
                break;
            case 129:       // 返礼品        
                $itemno = 11;
                break;
            case 135:      // 法事・催事    
                $itemno = 12;
                break;
            case 137:       // 会食          
                $itemno = 13;
                break;
            case 142:    // 非課税        
                $itemno = 14;
                break;
            default:
                $itemno = 3;
        }
        return $itemno;
    }

    /**
     * 領収書発行権限があれば真を返す
     * (管理者ロールか事務担当ロール)
     *
     * <AUTHOR> Mihara
     * @since      2015/06/xx
     * @return     boolean
     */
    public static function hasRyoshuHakkoRole() {
        $isAllowed = false;
        foreach (Msi_Sys_Utils::strArrayify_qw('sysman jimu ipan tanto tantoh msi') as $role) {
            if (Msi_Sys_Utils::hasRole($role)) {
                $isAllowed = true;
                break;
            }
        }
        return $isAllowed;
    }

    /**
     * 領収書発行権限があれば真を返す
     * (管理者ロールか事務担当ロール)
     *
     * <AUTHOR> Mihara
     * @since      2015/06/xx
     * @return     boolean
     */
    public static function hasSeikyuHakkoRole() {
        $isAllowed = false;
        foreach (Msi_Sys_Utils::strArrayify_qw('sysman manager tantoh jimu') as $role) {
            if (Msi_Sys_Utils::hasRole($role)) {
                $isAllowed = true;
                break;
            }
        }
        return $isAllowed;
    }

    /**
     * ホール選択 Dropdown を有効にするかどうかを返す
     *  
     *   juchu/mitsu, juchu/denpyo?
     *
     * <AUTHOR> Mihara
     * @since      2014/03/20
     * @version    2017/05/08  mihara  カスタマイズのため App_Utils から移動
     * @param      string   $moduleName
     * @param      string   $controllerName
     * @param      string   $actionName
     * @return     boolean
     */
    public static function isEnableHallDd($moduleName = null, $controllerName = null, $actionName = null) {
        /* 2014/07/24 del mihara
          if (static::isMitsuInJuchu($controllerName, $actionName)) {
          return true;
          }
         */
        $request = Msi_Sys_Utils::getRequestObject();
        if ($moduleName === null) {
            $moduleName = $request->getModuleName();
        }
        if ($controllerName === null) {
            $controllerName = $request->getControllerName();
        }
        if ($actionName === null) {
            $actionName = $request->getActionName();
        }

//ごんきや 当社葬家受注（別注品）入力画面は部門変更可能にする
//        if ($moduleName === 'juchu' && $controllerName === 'bechudenpyo') {
//            return true;
//        }

        if ($moduleName === 'juchu' && $controllerName === 'denpyo') {
            return true;
        }
        if ($moduleName === 'default' && $controllerName === 'index' && $actionName === 'index') {
            return true;
        }
        if (($moduleName === 'saimu' && $controllerName === 'hachudenpyo') || ($moduleName === 'saimu' && $controllerName === 'siiredenpyo')) { // 2016/11/18 ADD Kayo
            return true;
        }
        if ($moduleName === 'saiken' && $controllerName === 'nyukindenpyo') { // 2016/11/18 ADD Kayo
            return true;
        }
        if ($moduleName === 'juchu' && $controllerName === 'bechulist') {       // 2017/03/07 ADD Kayo
            return true;
        }
        if ($moduleName === 'juchu' && $controllerName === 'customerinfo') { // 受注業務の新規施行受付でホール選択できるように対応
            if ($actionName === 'input') { // 2014/07/24 add mihara
                if (preg_match('#/sn/\\d+\\b#', Msi_Sys_Utils::getUri())) { // 2017/04/12 mihara
                    $sekoNo = App_Utils::getSekoNoEasy();
                    if ($sekoNo && App_Utils::isMitsuKakutei($sekoNo))
                        return false;
                }
                return true;
            }
        }
        if ($moduleName === 'juchu' && $controllerName === 'mitsu' && $actionName === 'input') { // 2014/07/24 add mihara
            if (preg_match('#/sn/\\d+\\b#', Msi_Sys_Utils::getUri())) { // 2017/04/12 mihara
                $sekoNo = App_Utils::getSekoNoEasy('mitsu', 'mitsu');
                if ($sekoNo && App_Utils::isMitsuKakutei($sekoNo))
                    return false;
            }
            return true;
        }
        if ($moduleName === 'juchu' && $controllerName === 'houji' && $actionName === 'input') { // 2014/07/24 add mihara
            if (preg_match('#/sn/\\d+\\b#', Msi_Sys_Utils::getUri())) { // 2017/04/12 mihara
                $sekoNo = App_Utils::getSekoNoEasy();
                if ($sekoNo && App_Utils::isMitsuKakutei($sekoNo))
                    return false;
            }
            return true;
        }
        return false;
    }

    /** 施行受付と事前相談を分離させる場合オーバーライドして true に
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/06
     * @return boolean
     */
    public static function isSeparatePreconsult() {
        return true;
    }

    /** 施行受付とオーダーメイドを分離させる場合オーバーライドして true に
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/06
     * @return boolean
     */
    public static function isSeparateOrdermade() {
        return true;
    }

    /** 共通部門コード取得処理
     *
     * <AUTHOR> Tosaka
     * @since 2020/xx/xx
     * @return string
     */
    public static function getCommonBumonCd($bumon_cd) {
        $commonBumonCd = null;
        $db = Msi_Sys_DbManager::getMyDb();
        // 部門データを取得
        $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $bumon_cd));
        // 部門区分が共通部門のデータを取得
        if (count($bumonData) > 0) {
            $comBumonData = DataMapper_BumonEx::find($db, array('oya_bumon_cd' => $bumonData[0]['oya_bumon_cd'], 'bumon_kbn' => '3'));
            if ($bumonData[0]['bumon_kbn'] == '3') {
                $commonBumonCd = $bumon_cd;
            } else {
                foreach ($comBumonData as $oneBumon) {
                    // 会計部門コードが同一のものを施行式場に設定する
                    if ($oneBumon['kaikei_bumon_cd'] == $bumonData[0]['kaikei_bumon_cd']) {
                        $commonBumonCd = $oneBumon['bumon_cd'];
                    }
                }
            }
        }
        return $commonBumonCd;
    }

    /** $moushi_ctxt, $moushi_kbn_ctxtを取得する
     *
     * <AUTHOR> Tosaka
     * @since 2020/xx/xx
     * @param type $referer
     * @return type
     */
    public static function getMoushiCtxtInJuchu($referer = null) {
        $moushi_ctxt = '0990'; // (i090?) 'mitsu';  1：葬儀 5：生前依頼 6:その他施行
        $moushi_kbn_ctxt = '1,6,7,8,9,10';
        return array($moushi_ctxt, $moushi_kbn_ctxt);
    }

    /**
     * 印紙税額を取得する
     *
     * <AUTHOR> Tosaka
     * @since  2018/05/09
     * @params string   $prc　印紙税対象額
     * @params string   $inshi_zei_kbn　印紙税区分
     * @return string    印紙税額
     */
    public static function getStampPrc($prc, $inshi_zei_kbn) {
        $inshi_prc = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $kaisya_cd = App_Utils::getCtxtKaisyaEasy();
        if (isset($prc) && strlen($prc) > 0) {
            $sql = "
                SELECT
                    izm.inshi_zei_kingaku   -- 収入印紙税額
                FROM inshi_zei_mst izm
                WHERE izm.kaisya_cd   = :kaisya_cd
                AND izm.inshi_zei_kbn = :inshi_zei_kbn
                AND $prc >= kingaku_from
                AND $prc <= kingaku_to
                AND izm.delete_flg = 0
            ";
            $select = $db->easySelOne($sql, array('inshi_zei_kbn' => $inshi_zei_kbn, 'kaisya_cd' => $kaisya_cd));
            $inshi_prc = $select['inshi_zei_kingaku'];
        }
        return $inshi_prc;
    }

    /**
     * 商品摘要の展開
     *   例:マスタ「脱臭剤・脱臭麺その他（@n）日分」=>「脱臭剤・脱臭麺その他（１）日分」
     *
     * <AUTHOR> Mihara
     * @since 2015/06/xx
     * @param  string $shohin_tkiyo_cur    現在の商品摘要
     * @param  array  $rec                 $rec['quantity']など展開する要素が格納されているデータ
     * @param  array  $keyMapperP
     * @return string                      展開した商品摘要
     */
    public static function expandShohinTkiyoEasy($shohin_tkiyo_cur, $rec, $keyMapperP = null) {
        if (strlen($shohin_tkiyo_cur) <= 0) {
            return $shohin_tkiyo_cur;
        }

        $keyMapper = array('shohin_cd' => 'shohin_cd',
            'dai_bunrui_cd' => 'dai_bunrui_cd',
            'chu_bunrui_cd' => 'chu_bunrui_cd',
            'shohin_kbn' => 'shohin_kbn',
            'shohin_bumon_cd' => 'shohin_bumon_cd');

        if (is_array($keyMapperP)) {
            $keyMapper = array_merge($keyMapper, $keyMapperP);
        }

        // Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($rec) );

        $db = Msi_Sys_DbManager::getMyDb();

        $shohin_cd = $rec[$keyMapper['shohin_cd']];
        $dai_bunrui_cd = $rec[$keyMapper['dai_bunrui_cd']];
        $chu_bunrui_cd = $rec[$keyMapper['chu_bunrui_cd']];
        $shohin_kbn = $rec[$keyMapper['shohin_kbn']];
        $bumon_cd = $rec[$keyMapper['shohin_bumon_cd']];

        $cond = array('shohin_cd' => $shohin_cd,
            'dai_bunrui_cd' => $dai_bunrui_cd,
            'chu_bunrui_cd' => $chu_bunrui_cd,
            'shohin_kbn' => $shohin_kbn,
            'bumon_cd' => $bumon_cd);

        $shohinRec = DataMapper_Shohin0::findOne($db, $cond);

        if (!$shohinRec) {
            Msi_Sys_Utils::warn("expandShohinTkiyoEasy NO shohinRec: " . Msi_Sys_Utils::dump($cond));
            return $shohin_tkiyo_cur;
        }

        $_tkiyo = static::expandShohinTkiyo($shohin_tkiyo_cur, $shohinRec['shohin_tkiyo_nm'], $rec, $keyMapperP);

        $_tkiyo = preg_replace('/\\\\\\(/', '(', $_tkiyo);
        $_tkiyo = preg_replace('/\\\\\\)/', ')', $_tkiyo);

        return $_tkiyo;
    }

    /**
     * 担当者の権限名を取得する
     * <AUTHOR> Tosaka
     * @since 2018/10/22
     * @param $tanto_cd 担当コード
     */
    public function getRoleNm($tanto_cd) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = '
                SELECT roles
                FROM login_mst
                WHERE tanto_cd = :tanto_cd
                AND delete_flg=0';
        $selOne = $db->easySelOne($sql, array('tanto_cd' => $tanto_cd));
        return $selOne['roles'];
    }

    /**
     *
     * 出庫済み商品が存在するか
     *
     * <AUTHOR> Tosaka
     * @since 2018/5/31
     * @return boolean true|false
     */
    public static function hasSyukkoShohin($sekoNo, $data_kbn) {

        $flg = false;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
                SELECT
                    udm.v_free1
                FROM uriage_denpyo ud
                INNER JOIN uriage_denpyo_msi udm 
                ON udm.uri_den_no = ud.uri_den_no
                AND udm.v_free1 IS NOT NULL
                AND udm.delete_flg = 0
                WHERE ud.delete_flg = 0
                AND ud.seko_no = :seko_no
                AND ud.data_kbn = :data_kbn
                ";
        $select = $db->easySelect($sql, array('seko_no' => $sekoNo, 'data_kbn' => $data_kbn));
        // 出庫済商品が存在してたらtrue
        if (count($select) > 0) {
            $flg = true;
        }
        return $flg;
    }

    /**
     * 奉仕対象金額を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2021/12/28
     * @param   $juchuDenpyoMsi 受注伝票明細データ
     * @return  $juchuDenpyoMsi['juchu_prc'] 奉仕対象金額
     */
    public static function getHoshiTaisho($juchuDenpyoMsi) {
        return $juchuDenpyoMsi['juchu_prc'] + $juchuDenpyoMsi['nebiki_prc'] + $juchuDenpyoMsi['gojokai_nebiki_prc'];
    }

    /**
     * 
     * 領収証が存在するかチェック
     * 
     * <AUTHOR> Sugiyama
     * @since 2022/08/xx
     * @param type $seikyu_den_no
     * @return boolean
     */
    public static function hasRyosyu($seikyu_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT 
                 uri_den_no
                ,hako_count
            FROM ryosyusho_history
            WHERE uri_den_no = :seikyu_den_no
            AND hako_kbn IN (0, 1)
            ORDER BY uri_den_no,hako_count
        ";
        $select = $db->easySelect($sql, array('seikyu_den_no' => $seikyu_den_no));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            return true;
        }
        return false;
    }

    /**
     * 
     * 領収証(内金)が存在するかチェック
     * 
     * <AUTHOR> Sugiyama
     * @since 2022/08/xx
     * @param type $uri_den_no
     * @return boolean
     */
    public static function hasRyosyuUc($uri_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT 
                 uri_den_no
                ,hako_count
            FROM ryosyusho_history_uc
            WHERE uri_den_no = :uri_den_no
            AND hako_kbn IN (0, 1)
            ORDER BY uri_den_no,hako_count
        ";
        $select = $db->easySelect($sql, array('uri_den_no' => $uri_den_no));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            return true;
        }
        return false;
    }

    /**
     * 
     * 入金伝票が存在するかチェック
     * 
     * <AUTHOR> Sugiyama
     * @since 2022/09/xx
     * @param type $seikyu_den_no
     * @return boolean
     */
    public static function hasNyukin($seikyu_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT 
                seikyu_no
            FROM nyukin_denpyo
            WHERE seikyu_no = :seikyu_den_no
            AND delete_flg  = 0
            ORDER BY seikyu_no
        ";
        $select = $db->easySelect($sql, array('seikyu_den_no' => $seikyu_den_no));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            return true;
        }
        return false;
    }

    /**
     * 計上部門ドロップリスト用データを返す
     *
     * <AUTHOR> mihara
     * @since  2023/07/24
     * @return  array
     */
    public static function getUriageKeijyoBumonOpts($db = null) {
        if ($db === null) {
            $db = Msi_Sys_DbManager::getMyDb();
        }

        $dataKaisya = $db->easySelect(<<< END_OF_SQL
SELECT bumon_cd  AS id
      ,bumon_lnm AS text
  FROM bumon_mst
 WHERE bumon_kbn = 1
 ORDER BY bumon_cd desc
END_OF_SQL
        );

        $kaisya_ary = array();
        foreach ($dataKaisya as $kaisya) {
            $kaisya_ary[$kaisya['id']] = $kaisya['text'];
        }

        return $kaisya_ary;
    }

    /**
     *
     * 売上伝票履歴の財務連動区分が連携済が存在するか
     *
     * <AUTHOR> Tosaka
     * @since 2018/5/31
     * @return boolean true|false
     */
    public static function hasZaimuRendo($uri_den_no) {

        $flg = false;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
                SELECT uri_den_no
                FROM uriage_denpyo_history
                WHERE zaimu_rendo_kbn = 1
                AND uri_den_no = :uri_den_no
                ";
        $select = $db->easySelect($sql, array('uri_den_no' => $uri_den_no));
        if (count($select) > 0) {
            $flg = true;
        }
        return $flg;
    }
    /**
     * 部門参照を取得
     * 
     * <AUTHOR> Sugiyama
     * @since 2025/02/xx
     * @param int    $mode 1:担当コードから参照可能な部門を取得  2:部門から参照可能な部門を取得  3:担当コードから参照可能な親部門を取得  4:部門から参照可能な親部門を取得
     * @param string $bumon_cd 部門コード
     * @param db     $db データベース
     * @return array 対象部門
     *          ■対象部門が有る場合
     *              (
                        [0] => 36303
                        [1] => 36304
                        [2] => 36305
                            :
                            :
                        [20] => 35224
                        [21] => 36202
                        [22] => 35214
                    )
     *          ■対象部門が無い場合　空配列を返却
     *              (
                    )
     *          ■部門参照設定が無能の場合 null を返却
     *              
     */
    public static function getBumonRef($mode = 1, $bumon_cd = null, $db = null){
        if($mode == 1){
            // 担当コードから取得
            return self::getBumonRefFromTantoCd($db);
        }else if($mode == 2){
            // 計上部門コードから取得
            return self::getBumonRefFromKeijoBumonCd($bumon_cd, $db);
        }else if($mode == 3){
            // 担当コードから取得　親部門
            return self::getOyaBumonRefFromTantoCd($db);
        }else if($mode == 4){
            // 計上部門コードから取得　親部門
            return self::getOyaBumonRefFromKeijoBumonCd($bumon_cd, $db);
        }
        return null;
    }
    /**
     * ログイン情報から部門参照権限を取得
     * 
     * <AUTHOR> Sugiyama
     * @since 2025/02/xx
     * @param string $tanto_cd 担当コード ログイン情報以外の場合に設定
     * @param db $db データベース
     * @return array 対象部門
     *          ■対象部門が有る場合
     *              (
                        [0] => 36303
                        [1] => 36304
                        [2] => 36305
                            :
                            :
                        [20] => 35224
                        [21] => 36202
                        [22] => 35214
                    )
     *          ■対象部門が無い場合　空配列を返却
     *              (
                    )
     *          ■部門参照設定が無能の場合 null を返却
     *              
     */
    public static function getBumonRefFromTantoCd($db = null){
        $bumon_cds = array();
        if(!isset($db)){
            $db = Msi_Sys_DbManager::getMyDb();
        }
        // ログインユーザの担当コードを取得
        $tanto_cd = App_Utils::getTantoCd();
        
        // 部門参照権限の有無を確認
        if(!self::isBumonRefOptionCheck($db)){
            return null;
        }
        
        // ログイン担当者マスタから部門参照権限を取得
        $login_mst = DataMapper_LoginMst::findOne($db, array('tanto_cd' => $tanto_cd));
        if(Msi_Sys_Utils::myCount($login_mst) > 0){
            // コード名称マスタから部門参照権限を取得
            $code_mst = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '9735','kbn_value_cd_num' => $login_mst['bumon_ref_role']));
            if(Msi_Sys_Utils::myCount($code_mst) > 0){
                if(isset($code_mst['biko']) && strlen($code_mst['biko']) > 0){
                    // 備考から部門参照権限を取得
                    $bumon_ref_role_ary = explode(',', $code_mst['biko']);
                    $cond = array();
                    if(Msi_Sys_Utils::myCount($bumon_ref_role_ary) > 0){
                        $cond['__x1'] = DataMapper_Utils::condOneOf( 'bumon_ref_role', implode(',', $bumon_ref_role_ary) );
                    }
                    // 部門参照マスタから対象部門を取得
                    $bumon_ref_mst = DataMapper_Mstr_BumonRefMst::find($db, $cond);
                    foreach ($bumon_ref_mst as $value) {
                        $bumon_cds[] = $value['bumon_cd'];
                    }
                }else{
                    // 備考が未設定の場合
                    // ログイン担当者マスタの部門参照権限で部門参照マスタを取得
                    $bumon_ref_mst = DataMapper_Mstr_BumonRefMst::find($db, array('bumon_ref_role' => $login_mst['bumon_ref_role']));
                    foreach ($bumon_ref_mst as $value) {
                        $bumon_cds[] = $value['bumon_cd'];
                    }
                }
            }
        }
        return $bumon_cds;
    }
    /**
     * ログイン情報から部門参照権限の親部門を取得
     * 
     * <AUTHOR> Sugiyama
     * @since 2025/02/xx
     * @param string $tanto_cd 担当コード ログイン情報以外の場合に設定
     * @param db $db データベース
     * @return array 対象部門
     *          ■対象部門が有る場合
     *              (
                        [0] => 36303
                        [1] => 36304
                        [2] => 36305
                            :
                            :
                        [20] => 35224
                        [21] => 36202
                        [22] => 35214
                    )
     *          ■対象部門が無い場合　空配列を返却
     *              (
                    )
     *          ■部門参照設定が無能の場合 null を返却
     *              
     */
    public static function getOyaBumonRefFromTantoCd($db = null){
        $bumon_cds = array();
        if(!isset($db)){
            $db = Msi_Sys_DbManager::getMyDb();
        }
        // ログインユーザの担当コードを取得
        $tanto_cd = App_Utils::getTantoCd();
        
        // 部門参照権限の有無を確認
        if(!self::isBumonRefOptionCheck($db)){
            return null;
        }
        
        // ログイン担当者マスタから部門参照権限を取得
        $login_mst = DataMapper_LoginMst::findOne($db, array('tanto_cd' => $tanto_cd));
        if(Msi_Sys_Utils::myCount($login_mst) > 0){
            // コード名称マスタから部門参照権限を取得
            $code_mst = DataMapper_CodeNmMst::findOne($db, array('code_kbn' => '9735','kbn_value_cd_num' => $login_mst['bumon_ref_role']));
            if(Msi_Sys_Utils::myCount($code_mst) > 0){
                if(isset($code_mst['biko']) && strlen($code_mst['biko']) > 0){
                    // 備考から部門参照権限を取得
                    $bumon_ref_role_ary = explode(',', $code_mst['biko']);
                    $cond = array();
                    if(Msi_Sys_Utils::myCount($bumon_ref_role_ary) > 0){
                        $cond['__x1'] = DataMapper_Utils::condOneOf( 'bumon_ref_role', implode(',', $bumon_ref_role_ary) );
                    }
                    // 部門参照マスタから対象部門を取得
                    $bumon_ref_mst = DataMapper_Mstr_BumonRefMst::findOyaBumon($db, $cond);
                    foreach ($bumon_ref_mst as $value) {
                        $bumon_cds[] = $value['bumon_cd'];
                    }
                }else{
                    // 備考が未設定の場合
                    // ログイン担当者マスタの部門参照権限で部門参照マスタを取得
                    $bumon_ref_mst = DataMapper_Mstr_BumonRefMst::findOyaBumon($db, array('bumon_ref_role' => $login_mst['bumon_ref_role']));
                    foreach ($bumon_ref_mst as $value) {
                        $bumon_cds[] = $value['bumon_cd'];
                    }
                }
            }
        }
        return $bumon_cds;
    }
    /**
     * 売上計上部門から部門参照権限を取得
     * 
     * <AUTHOR> Sugiyama
     * @since 2025/02/xx
     * @param string $bumon_cd 部門コード 売上計上部門以外の場合に設定
     * @param db $db データベース
     * @return array 対象部門
     *          ■対象部門が有る場合
     *              (
                        [0] => 36303
                        [1] => 36304
                        [2] => 36305
                            :
                            :
                        [20] => 35224
                        [21] => 36202
                        [22] => 35214
                    )
     *          ■対象部門が無い場合　空配列を返却
     *              (
                    )
     *          ■部門参照設定が無能の場合 null を返却
     *              
     */
    public static function getBumonRefFromKeijoBumonCd($bumon_cd, $db = null){
        $bumon_cds = array();
        if(!isset($db)){
            $db = Msi_Sys_DbManager::getMyDb();
        }
        
        // 部門参照権限の有無を確認
        if(!self::isBumonRefOptionCheck($db)){
            return null;
        }
        
        // 部門参照マスタを取得
        $bumon_ref_mst = DataMapper_Mstr_BumonRefMst::findOne($db, array('bumon_cd' => $bumon_cd));
        if(Msi_Sys_Utils::myCount($bumon_ref_mst) > 0){
            // 部門参照権限で部門参照マスタを再取得
            $bumon_ref_mst2 = DataMapper_Mstr_BumonRefMst::find($db, array('bumon_ref_role' => $bumon_ref_mst['bumon_ref_role']));
            foreach ($bumon_ref_mst2 as $value) {
                $bumon_cds[] = $value['bumon_cd'];
            }
        }
        return $bumon_cds;
    }
    /**
     * 売上計上部門から部門参照権限の親部門を取得
     * 
     * <AUTHOR> Sugiyama
     * @since 2025/02/xx
     * @param string $bumon_cd 部門コード 売上計上部門以外の場合に設定
     * @param db $db データベース
     * @return array 対象部門
     *          ■対象部門が有る場合
     *              (
                        [0] => 36303
                        [1] => 36304
                        [2] => 36305
                            :
                            :
                        [20] => 35224
                        [21] => 36202
                        [22] => 35214
                    )
     *          ■対象部門が無い場合　空配列を返却
     *              (
                    )
     *          ■部門参照設定が無能の場合 null を返却
     *              
     */    
    public static function getOyaBumonRefFromKeijoBumonCd($bumon_cd, $db = null){
        $bumon_cds = array();
        if(!isset($db)){
            $db = Msi_Sys_DbManager::getMyDb();
        }
        
        // 部門参照権限の有無を確認
        if(!self::isBumonRefOptionCheck($db)){
            return null;
        }
        
        // 部門参照マスタを取得　親部門
        $bumon_ref_mst = DataMapper_Mstr_BumonRefMst::findOyaBumon($db, array('bumon_cd' => $bumon_cd));
        foreach ($bumon_ref_mst as $value) {
            $bumon_cds[] = $value['bumon_cd'];
        }
        return $bumon_cds;
    }
    
    /**
     * 部門参照権限の可否を判定
     * 
     * <AUTHOR> Sugiyama
     * @since 2025/02/xx
     * @param type $db
     * @return bool 部門参照権限の有無 有:true 無:false
     */
    public static function isBumonRefOptionCheck($db){
        // 部門参照権限のオプション制御の取得
        $code_nm_mst = DataMapper_CodeNmMst::findOne($db,array('code_kbn' => '9750', 'kbn_value_cd' => '10007'));
        if(Msi_Sys_Utils::myCount($code_nm_mst) > 0){
            if($code_nm_mst['kbn_value_cd_num'] == 0){
                // 0が設定されている場合
                return false;
            }
        }else{
            // オプション制御が設定されていない場合
            return false;
        }
        return true;
    }
    /**
     * 表示権限チェック処理
     * 　引数の部門コードがログインユーザーの参照可能部門に含まれているか判定
     * 
     * @param string $bumon_cd 部門コード
     * @param db $db データベース
     * @return bool 部門参照権限の有無 有:true 無:false
     */
    public static function isBumonRef1($bumon_cd, $db = null) {
        $bumon_ref_cds = self::getBumonRefFromTantoCd($db);
        if(is_null($bumon_ref_cds)) {
            // 部門参照権限が無効設定の場合
            return true;
        }
        foreach ($bumon_ref_cds as $bumon_ref_cd) {
            if($bumon_ref_cd == $bumon_cd){
                // 対象の部門コードが含まれている場合
                return true;
            }
        }
        return false;
    }
    /**
     * 表示権限チェック処理
     * 　引数の部門コードがログインユーザーの参照可能部門に含まれているか判定
     * 
     * @param string $bumon_cd 部門コード
     * @param db $db データベース
     * @throws Exception 
     */
    public static function isBumonRef2($bumon_cd, $db = null) {
        // 表示権限チェック処理を呼び出す
        if(!self::isBumonRef1($bumon_cd, $db)){
            throw new Exception("部門参照権限が有りません。");
        }
    }
}
