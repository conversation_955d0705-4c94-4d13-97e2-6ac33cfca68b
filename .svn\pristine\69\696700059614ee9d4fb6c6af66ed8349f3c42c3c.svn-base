<?php

/**
 *  Saiken_UploadfurikomiController
 *
 * 振込入金消込 コントローラクラス
 *
 * @category   Saiken
 * @package    controller
 * <AUTHOR> Kino
 * @since      2021/01/XX
 * @filesource 
 */
class Saiken_UploadfurikomiController extends Zend_Controller_Action {

    /** 処理方法：銀行 */
    const BANK_JUDG = 1010;
    /** 処理方法：コンビニ */
    const CONVENI_JUDG = 120;
    /** 処理タイプ：新規連結 */
    const PROC_TYPE_NEW = 1;
    /** 処理タイプ：履歴表示 */
    const PROC_TYPE_HIST = 2;
    /** ファイルの最大値（単位：M） */
    const FILE_MAX_SIZE = 4;
    /** ファイル種別 **/
    const FILE_TYPE_BANK = 1; // 1：振込
    const FILE_TYPE_CONV = 2; // 2：コンビニ
    /** 1レコードのバイト数 */
    const ONE_RECORD_BYTE = 200;
    /** 1レコードのバイト数(みずほ) */
    const ONE_RECORD_BYTE_M = 120;
    /** コード区分：9759 */
    const CODE_KBN_9759 = '9759';

    /*
     * メイン処理
     */
    public function nyukinAction() {
        // 利用 JavaScript 設定
        App_Smarty::pushJsFile([
            'app/lib.fileupload.js',
            'app/saiken.uploadfurikomi.js'
        ]);

        // 利用 CSS 設定
        App_Smarty::pushCssFile([
            'lib_jq.css',
            'main_pub.css',
            'app/gridtablestyle.css',
            'app/mstr.css',
            'app/lib.fileupload.css',
            'app/sais.css',
            'app/saiken.uploadfurikomi.css',
        ]);

        $data = $this->_getInitData();
        $json = Msi_Sys_Utils::json_encode($data);
        //tplへ        
        $this->view->data_json = $json;
    }

    /**
     * 画面初期表示用データ取得
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     */
    private function _getInitData() {
        $msg = null;
        $db = Msi_Sys_DbManager::getMyDb();
        $uploaddata = array(array(), array(), array(), array());
        $download_data = $this->_pickDataCol($uploaddata);
        $torikomi_ymd = Msi_Sys_Utils::getDate();

        $data = array(
            'status' => 'OK',
            'msg' => $msg,
            'torikomi_ymd' => $torikomi_ymd,
            'dataUpload' => $download_data,
        );

        return $data;
    }

    /**
     * 項目を抜き出して返す
     *
     * <AUTHOR> Kino
     * @since  2021/01/XX
     * @param  array  $data
     * @return array
     */
    protected function _pickDataCol($data) {
        $dataCol = array();
        foreach ($data as $rec) {
            $dataCol[] = $rec;
        }

        return $dataCol;
    }
    
    /**
     * ファイル読込処理 
     *
     * <AUTHOR> Kino
     * @since 2021/01/XX
     */
    public function readfileAction() {
        App_DevCoverage_Manager::easyStart();
        $req = $this->getRequest();
        $file_name = null;
        $file_tmpname = null;
        if ($req->isPost() && isset($_FILES['file'])) {
            $fileMaxSize = static::FILE_MAX_SIZE;
            $file_size = $_FILES["file"]["size"];
            $file_name = $_FILES["file"]["name"];
            $file_tmpname = $_FILES["file"]["tmp_name"];
            $file_error = $_FILES["file"]["error"];
            
            if ($file_size > $fileMaxSize * 1024 * 1024 || $file_error == UPLOAD_ERR_INI_SIZE) {
                Msi_Sys_Utils::outJson(array('status' => 'NG', 'msg' => ('ファイルサイズが大きすぎます')));
                return;
            }
            try {
                // 入金データ読み込み処理
                $db = Msi_Sys_DbManager::getMyDb();
                $hData = array(); // ヘッダー・レコード
                $allData = array(); // ヘッダー, データ・レコード
                // ファイルの存在＆読み込みチェック
                if (!is_readable($file_tmpname)) {
                    $message = sprintf("入力ファイル(%s)を読み込めません", $file_tmpname);
                    throw new Msi_Logic_ExceptionInput($message);
                }
                if (is_string($file_tmpname)) {
                    $fh = fopen($file_tmpname, 'r');
                    if ($fh === false) {
                        throw new Msi_Sys_Exception_RuntimeException("{$file_name} ファイルオープンに失敗しました");
                    }
                    $fhOrg = $fh;
                } else if (($rtype = get_resource_type($file_tmpname)) == 'file' || $rtype == 'stream' ) {
                    $fhOrg = $file_tmpname;
                } else {
                    throw new Msi_Sys_Exception_RuntimeException("ファイルが不正です");
                }

                // ファイルから文字列を取り出す
                $_str = '';
                while (($buf = fgets($fhOrg)) !== false) {
                    $_str .= $buf;
                }
                // UTF-8に変換
                $encoding = mb_detect_encoding($_str, "ASCII,JIS,UTF-8,EUC-JP,SJIS-win");
                if ($encoding != "UTF-8") {
                    $_str = mb_convert_encoding($_str, "UTF-8", $encoding);
                }
                // 正規表現
                $pattern = "#\n|\r\n|\r#";
                // 改行コードを空文字に置換する
                $str = preg_replace($pattern, '', $_str);   
                // 改行コードがある場合とない場合で取り出しパターンを変更する
//                if (preg_match($pattern, $_str)) {
//                    $size = mb_strlen($str);
//                    $row_cnt = $size / static::ONE_RECORD_BYTE_M; 
//                    $meisai_arr = array();
//                    for ($i = 0; $i < $row_cnt; $i++) {
//                        // 指定したバイトずつ取り出す
//                        $record = mb_substr($str, static::ONE_RECORD_BYTE_M * $i, static::ONE_RECORD_BYTE_M);
//                        $meisai_arr[] = $record;
//                    }
//                } else {
                    $size = mb_strlen($str);
                    $row_cnt = $size / static::ONE_RECORD_BYTE; 
                    $meisai_arr = array();
                    for ($i = 0; $i < $row_cnt; $i++) {
                        // 指定したバイトずつ取り出す
                        $record = mb_substr($str, static::ONE_RECORD_BYTE * $i, static::ONE_RECORD_BYTE);
                        $meisai_arr[] = $record;
                    }
//                }
                
                // データを成形し格納
                if (count($meisai_arr) > 0 ) {
                    $judg = null;
                    $cont = 0;
                    foreach ($meisai_arr as $value) {
                        $record_kbn = mb_substr($value, 0, 1);
                        //  ヘッダー・レコード (ヘッダーは最後のみ有効とするため、常に下記を通らせる)
                        if($record_kbn == '1'){
                            
                            // 銀行振込
                            $bank_judg = mb_substr($value, 0, 4, "utf-8");
                            if($bank_judg == static::BANK_JUDG){
                                $judg = static::BANK_JUDG;
                                $hData['data_kbn'] = mb_substr($value, 0, 1, "utf-8");        // データ区分
                                $hData['sbt_cd'] = mb_substr($value, 1, 2, "utf-8");          // 種別コード
                                $hData['code_kbn'] = mb_substr($value, 3, 1, "utf-8");        // コード区分
                                $hData['file_create_date'] = mb_substr($value, 4, 6, "utf-8"); // 作成日
                                $hData['bank_cd'] = mb_substr($value, 22, 4, "utf-8");        // 銀行コード
                                $hData['shiten_cd'] = mb_substr($value, 41, 3, "utf-8");      // 支店コード
                                $hData['yokin_sbt'] = mb_substr($value, 59, 1, "utf-8");      // 預金種目
                                $hData['kouza_no'] = mb_substr($value, 60, 7, "utf-8");       // 口座番号
                                
                                // 親部門情報取得
                                $bumon_info = static::getOyaFromKoza($db, $hData);
                                if (empty($bumon_info)) {
                                    throw new Exception('部門マスタに口座番号：'.$hData['kouza_no'].'で使用した親部門が登録されていません');
                                }
                                $hData['kaisya_cd'] = $bumon_info[0]['bumon_cd'];  // 会社コード
                                $hData['kaisya_nm'] = $bumon_info[0]['bumon_lnm']; // 会社名

                                // 振込種別
                                $hData['furikomi_sbt_nm'] = '銀行';                // 振込種別名

                                // 前回取込日取得
                                $hData['before_date'] = static::getUploadDate($db, $hData['kaisya_cd'], 1);

                                // ファイル情報
                                $hData['file_nm'] = $file_name;                    // ファイル名
                                $hData['file_type'] = static::FILE_TYPE_BANK;        // ファイル種別
                            }

                            // コンビニ
                            $conveni_judg = mb_substr($value, 0, 3, "utf-8");
                            if($conveni_judg == static::CONVENI_JUDG){
                                $judg = static::CONVENI_JUDG;
                                $hData['record_kbn'] = mb_substr($value, 0, 1, "utf-8");       // レコード区分
                                $hData['file_create_date'] = mb_substr($value, 1, 8, "utf-8"); // データ作成年月日
                                $hData['itaku_no'] = mb_substr($value, 14, 5, "utf-8");        // 委託者番号

                                // 親部門情報取得
                                $bumon_info = static::getKaisyaFromCode($db, $hData['itaku_no']);
                                if (empty($bumon_info)) {
                                    throw new Exception('コード名称マスタに'.$hData['itaku_no'].'で使用した親部門が登録されていません');
                                }
                                $hData['kaisya_cd'] = $bumon_info['bumon_cd'];  // 会社コード
                                $hData['kaisya_nm'] = $bumon_info['bumon_lnm']; // 会社名

                                // 振込種別
                                $hData['furikomi_sbt_nm'] = 'コンビニ';            // 振込種別名

                                // 前回取込日取得
                                $hData['before_date'] = static::getUploadDate($db, $hData['kaisya_cd'], 2);

                                // ファイル情報
                                $hData['file_nm'] = $file_name;                    // ファイル名
                                $hData['file_type'] = static::FILE_TYPE_CONV;      // ファイル種別
                            }
                            $cont++;
                        }
                        //  データ・レコード
                        else if($record_kbn == '2'){
                            $info = array();
                            // 銀行振込
                            if($judg ==  static::BANK_JUDG){
                                $info['shokai_no'] = mb_substr($value, 1, 6, "utf-8");            // 照会番号
                                $info['kanjyo_date'] = mb_substr($value, 7, 6, "utf-8");          // 勘定日
                                $info['azukeire_date'] = mb_substr($value, 13, 6, "utf-8");          // 起算日
                                $info['data_kbn'] = mb_substr($value, 0, 1, "utf-8");             // データ区分
                                $info['tori_kingaku'] = mb_substr($value, 19, 10, "utf-8");       // 取引金額
                                $info['taten_kenkingaku'] = mb_substr($value, 29, 10, "utf-8");   // うち他店券金額
                                $info['furikomi_rq_code'] = mb_substr($value, 39, 10, "utf-8");   // 振込依頼人コード
                                $info['furikomi_rq_nm'] = mb_substr($value, 49, 48, "utf-8");     // 振込依頼人名または契約者番号
                                $info['shimuke_bk_nm'] = mb_substr($value, 97, 15, "utf-8");     // 仕向銀行名
                                $info['shimuke_ten_nm'] = mb_substr($value, 112, 15, "utf-8");    // 仕向店名
                                $info['torikeshi_kbn'] = mb_substr($value, 127, 1, "utf-8");          // 取消区分
                                $info['edi_info'] = mb_substr($value, 128, 20, "utf-8");          // EDI情報
                            }
                            // コンビニ
                            else if($judg ==  static::CONVENI_JUDG){
                                $info['record_kbn'] = mb_substr($value, 0, 1, "utf-8");           // レコード区分
                                $info['type'] = mb_substr($value, 1, 2, "utf-8");                 // 種別
                                $info['shop_syuno_date'] = mb_substr($value, 3, 8, "utf-8");      // 店舗収納日
                                $info['shop_syuno_time'] = mb_substr($value, 11, 4, "utf-8");     // 店舗収納時間
                                $info['barcode_type'] = mb_substr($value, 15, 1, "utf-8");        // バーコード種別
                                $info['barcode_info'] = mb_substr($value, 16, 44, "utf-8");       // バーコード情報
                                $info['syuno_shop_code'] = mb_substr($value, 63, 7, "utf-8");     // 収納店舗コード
                                $info['kyakuso_code'] = mb_substr($value, 70, 2, "utf-8");        // 客層コード
                                $info['data_get_date'] = mb_substr($value, 72, 8, "utf-8");       // データ取得年月日
                                $info['furikomi_yotei'] = mb_substr($value, 80, 8, "utf-8");      // 振込予定日
                                $info['keiri_syori_date'] = mb_substr($value, 88, 8, "utf-8");    // 経理処理年月日
                                $info['cvs_code'] = mb_substr($value, 96, 6, "utf-8");            // CVSコード
                                $info['shiharai_prc'] = mb_substr($info['barcode_info'], 37, 6);  // 支払金額
                            }
                            $allData[] = $info;
                        }
                        
                    }
                } else {
                    throw new Exception('データがありません。');
                }
                
                $data = array(
                    'headData' => $hData, // ヘッダー・レコード
                    'allData' => $allData,  // データ・レコード
                    'status' => 'OK',
                    'msg' => ('更新しました'),
                );

                Msi_Sys_Utils::outJson($data);
            } catch (Exception $ex) {
                $data['status'] = 'NG';
                $data['msg'] = $ex->getMessage();
                Msi_Sys_Utils::outJson($data);
                return;
            }
        }
    }

    /**
     * save アクション　保存処理
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     * 
     */
    public function saveAction() {
        $req = $this->getRequest();
        try {
            if ($req->isPost()) {
                //データ取得
                $DataCol = Msi_Sys_Utils::json_decode($req->getPost('DataColJson'));      //画面入力データ(明細データ)
                $TorikomiYmd = Msi_Sys_Utils::json_decode($req->getPost('TorikomiYmdJson')); //画面入力データ(取込日)
                $db = Msi_Sys_DbManager::getMyDb();
                
                //新規登録処理            
                $rtn_data_arr = Logic_UploadFurikomiMake::Main($db, $DataCol, $TorikomiYmd);
                
                // 表示データ作成
                $dataResult = static::_getDispData($DataCol, 2);
                
                $data = array(
                    'dataResult' => $dataResult,
                    'rtn_data_arr' => $rtn_data_arr,
                    'torikomi_ymd' => $TorikomiYmd,
                    'status' => 'OK',
                    'msg' => ('保存しました. PDFが出力されるまでしばらくお待ちください.'),
                );
                Msi_Sys_Utils::outJson($data);
            }
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            Msi_Sys_Utils::outJson($errData);
        }
    }
    
    /**
     * save アクション　保存処理
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     * 
     */
    public function resultAction() {
        $req = $this->getRequest();
        try {
            if ($req->isPost()) {
                $DataCol = Msi_Sys_Utils::json_decode($req->getPost('DataColJson'));      //画面入力データ(明細データ)
                if (count($DataCol) <= 0) {
                    throw new Exception('処理対象のデータがありません。');
                }
                // 表示データ作成
                $dataResult = static::_getDispData($DataCol, 1);
                $data = array(
                    'dataResult' => $dataResult,
                    'status' => 'OK',
                );
                Msi_Sys_Utils::outJson($data);
            }
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $errData = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
            Msi_Sys_Utils::outJson($errData);
        }
    }
    
    /**
     * 画面表示用データ取得(処理結果)
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     * @param      array  $DataCol
     * @param      array  $mode     1:開始、2:開始＆終了
     * @return	    date   $dataResult
     */
    private function _getDispData($DataCol, $mode) {
        
        $dataResult = array();
        $dataArr = array();
        
        foreach($DataCol as $cRec){
            $kaisya_nm = $cRec['kaisya_nm'];
            $furikomi_sbt_nm = $cRec['furikomi_sbt_nm'];
            $dataArr[$kaisya_nm][$furikomi_sbt_nm] = null;
        }
        
        foreach($dataArr as $dKey => $dRec){
            foreach($dRec as $key => $rec){
                if($key == '銀行'){
                    $txt = '振込データ消込';
                }else{
                    $txt = '入金データ消込';
                }
                $dataResult[] = array('re_kaisya_nm' => $dKey
                                     ,'re_furikomi_sbt' => $key . $txt
                                     ,'re_result_info' => '開始');
                if($mode == 2){
                    $dataResult[] = array('re_kaisya_nm' => $dKey
                                     ,'re_furikomi_sbt' => $key . $txt
                                     ,'re_result_info' => '終了');
                }
            }
        }

        return $dataResult;
    }

    /**
     * 前回取込を取得
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     * @param      Msi_Sys_Db $db
     * @param      string     $bumon_cd
     * @param      string     $file_type 1：振込 2：コンビニ
     * @return	    date       upload_date	
     */
    private static function getUploadDate($db, $bumon_cd, $file_type) {
        
        // 部門コードを取得
        $sql = <<< END_OF_SQL
        SELECT kaisya_cd
             , TO_CHAR(max(upload_date), 'YYYY/MM/DD') AS upload_date
          FROM upload_furikomi_nyukin_history
         WHERE kaisya_cd = :bumon_cd
           AND file_type = :file_type
           AND delete_flg = 0
         GROUP BY kaisya_cd
END_OF_SQL;
        $select = $db->easySelOne($sql, array('bumon_cd'   => $bumon_cd
                                            , 'file_type'  => $file_type
                                            ));
        
        return $select['upload_date'];
    }
    
    /**
     * 口座番号から親会社情報を取得
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     * @param      Msi_Sys_Db $db
     * @param      array      $data
     * @return	    array      $bumon_info	
     */
    private function getOyaFromKoza($db, $data) {
        
        // 部門コードを取得
        $sql = <<< END_OF_SQL
        SELECT kbn_value_cd AS bumon_cd
          FROM code_nm_mst
         WHERE code_kbn = :code_kbn
           AND kbn_value_cd_num = :kbn_value_cd_num
           AND kbn_value_lnm = :kbn_value_lnm
           AND kbn_value_snm = :kbn_value_snm
           AND delete_flg = 0
        ORDER BY disp_nox
END_OF_SQL;
        $select = $db->easySelOne($sql, array('code_kbn'  => static::CODE_KBN_9759
                ,'kbn_value_cd_num' => (int)$data['sbt_cd']
                ,'kbn_value_lnm' => $data['kouza_no']
                ,'kbn_value_snm' => $data['bank_cd'].','.$data['shiten_cd']
                ) // 口座番号
                                             );
        if(empty($select)){
            return null;
        }
        
        // 部門コードから親部門名(会社名)を取得
        $bumon_info = static::getKaisyaArr($db, $select['bumon_cd']);
        
        // 子部門コードから親部門名(会社名)を取得
        if(Msi_Sys_Utils::myCount($bumon_info) == 0){
            $childBumon = DataMapper_BumonMst::findOyaBumonCd($db, $select['bumon_cd']);
            $bumon_info = static::getKaisyaArr($db, $childBumon);
        }
        
        return $bumon_info;
    }
    
    /**
     * 親会社情報の取得処理
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     * @param      Msi_Sys_Db $db
     * @param      string     $bumon_cd	
     * @return	    array      $bumon_info
     */
    private function getKaisyaArr($db, $bumon_cd) {
        
        $sql = <<< END_OF_SQL
        SELECT 
            bumon_cd
           ,bumon_lnm
        FROM bumon_mst
        WHERE bumon_kbn=0 -- 部門区分：親会社
            AND bumon_cd <> '00001' -- 全社は表示しない
            AND bumon_cd = :bumon_cd
            AND delete_flg = 0
        ORDER BY bumon_cd
END_OF_SQL;
        $select = $db->easySelect($sql, array('bumon_cd' => $bumon_cd) );
        return $select;
    }
    
    /**
     * コード名称から親会社情報の取得
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     * @param      Msi_Sys_Db $db
     * @param      string     $itaku_no	
     * @return	    array      $bumon_info
     */
    private function getKaisyaFromCode($db, $itaku_no) {
        
        $sql = <<< END_OF_SQL
        SELECT 
            kbn_value_cd as bumon_cd
           ,kbn_value_lnm as bumon_lnm
        FROM code_nm_mst
        WHERE code_kbn = '8554' -- 委託者番号区分
          AND kbn_value_cd_num = :itaku_no -- 委託者番号
          AND delete_flg = 0
        ORDER BY bumon_cd -- 複数あった場合はkbn_value_cd_numが若いものを取得
END_OF_SQL;
        $select = $db->easySelOne($sql, array('itaku_no' => $itaku_no) );
        return $select;
    }
}
