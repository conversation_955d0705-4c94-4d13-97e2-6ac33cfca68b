<?php
  /**
   * Logic_SekoOutCarn
   *
   * カーニバル連携用 処理
   *
   * @category   App
   * @package    models\Logic
   * <AUTHOR> Mihara
   * @since      2015/xx/xx
   * @version    2016/05/26 mihara 0090(自動車関係)の場合、siire_cd も考慮する  _linkShohinSp0090()
   * @version    2016/05/30 mihara SOGI_KEITAI_CD2=2 に固定。TUYA_BASYO_NM,SOGI_BASYO_NM の編集。
   * @version    2016/05/31 mihara 枕花を連携対象商品から除外。判定としては、商品分類マスタのsyoukei_group_cd=99を除外する
   * @version    2016/06/06 mihara 0090(自動車関係)の場合、_linkShohinSp0090() を SEKO_HACHU_INFO バージョンに戻す
   * @version    2016/07/20 mihara 0260(会葬礼状関係)の場合、0090(自動車関係)と同様、仕入先を考慮する
   * @version    2016/07/29 mihara 0260(会葬礼状関係)の場合、LINK_SHOHIN_MST.cnv_tanka を考慮して対応. _linkShohinSp0260 を作成
   * @version    2016/07/29 mihara 0001390(寝台料金),0001400(寝台料金２),0001410(寝台料金３)の特別処理
   * @version    2016/08/17 mihara _linkShohinSp0260() uri_tnk がある場合(確定後)は juchu_tnk でなく uri_tnk をみる
   * @version    2016/10/20 mihara _linkShohinSpShindai() 商品コード追加
   *                               １．移送者１で判定（0001390）への追加: 0013150、0013160、0013210、0013220
   *                               ２．移送者２で判定（0001400）への追加: 0013170、0013180、0013190、0013200
   *                               ３．自社霊柩車が固定（0001410）への追加: 0013230
   * @version    2019/06/xx sai 軽減税率対応
   * @version    2024/12/xx mihara SekoOutCarn.sano_k.php をコピーして作成. #11675 事前相談を出力対象. 項目追加.
   * @version    2025/04/xx mihara SekooutcarnController.sano_k.php をコピーして作成. #11675
   * @filesource 
   */

  /**
   * カーニバル連携用 処理
   * 
   * @category   App
   * @package    models\Logic
   * <AUTHOR> Mihara
   * @since      2015/xx/xx
   */
class Logic_SekoOutCarn
{
    /**
     * @ignore
     */
    protected $_sekoNos = null;

    /**
     * @ignore
     */
    protected $_cur_sekoNo = null;

    /**
     * @ignore
     */
    protected $_cur_expJutyuNo = null;

    /**
     * @ignore
     */
    protected $_sekoNo_jutyuNo_map = null; // 出力した 施行No x exp受注番号の対応表

    /**
     * @ignore
     */
    protected $_sekoNo_jutyuNo_map_new = null; // 新規追加する 施行No x exp受注番号の対応表

    /**
     * コンストラクタ
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      array  $seko_nos
     * @return void
     */
    protected function __construct($seko_nos)
    {
        $this->_sekoNos = Msi_Sys_Utils::arrayify($seko_nos);

        $this->_sekoNo_jutyuNo_map = array();
    }

    /**
     * 施行データ等出力
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      array  $seko_nos
     * @return     array        array( 'type1'=>array(array(),array(),...), 'type2'=>array(array(),array(),...), ...
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function doSekoExport($seko_nos)
    {
        set_time_limit( 300 );

        $db = Msi_Sys_DbManager::getMyDb();

        if ( ! $db->getColumnInfo('seko_kihon_info', 'link_exp_no') ) {
            throw new Exception( "seko_kihon_info へ link_exp_no 等を追加して下さい" );
        }

        $myObj = new static($seko_nos);

        $seko_nos = Msi_Sys_Utils::arrayify($seko_nos);

        // Msi_Sys_Utils::debug( '** doSekoExport() IN=>' . Msi_Sys_Utils::dump($seko_nos) );

        $jutyu_t_arr   = array(); // 受注T
        $jutyum_t_arr  = array(); // 受注明細T
        $kokyaku_t_arr = array(); // 葬儀顧客情報T

        $arrErr = array();
        $inCnt  = 0;
        $errCnt = 0;
        $errSekos = array();

        $okSekonos = array();
        $errMaxCnt = 10;
        foreach ( $seko_nos as $sekoNo ) {
            try {
                $inCnt++;
                list($jutyu_t, $jutyum_t, $kokyaku_t) = $myObj->sekoExportOne($sekoNo);
            }
            catch ( Exception $e ) {
                $err = $e->getMessage();
                Msi_Sys_Utils::warn( $err );
                if ( is_a($e, 'Msi_Sys_Exception_LogicException') ) { // Msi_Sys_Exception_LogicException は続行しない
                    throw $e;
                }
                $errCnt++;
                $arrErr[] = $err;
                $errSekos[] = $sekoNo;
                if ( $errCnt >= $errMaxCnt ) {
                    $errMsg = sprintf( "エラー件数が %d 件に達しました. エラー終了します.\nエラーの施行No:%s\n",
                                       $errMaxCnt, implode(', ', $errSekos) );
                    $errMsg .= implode("\n", array_slice($arrErr, 0, 4)) . "...\n";
                    throw new Exception( $errMsg );
                }
                continue;
            }

            $jutyu_t_arr = array_merge( $jutyu_t_arr, $jutyu_t );
            $jutyum_t_arr = array_merge( $jutyum_t_arr, $jutyum_t );
            $kokyaku_t_arr = array_merge( $kokyaku_t_arr, $kokyaku_t );

            $okSekonos[] = $sekoNo;
        }

        $updCnt = $myObj->updSekoExpInfo($okSekonos);

        $db->commit();

        $rtnData = array(
                         'jutyu_t' => $jutyu_t_arr,
                         'jutyum_t' => $jutyum_t_arr,
                         'kokyaku_t' => $kokyaku_t_arr,
                         '_metaInfo' => array(
                                              'inCnt'    => $inCnt,
                                              'updCnt'   => $updCnt,
                                              'errCnt'   => $errCnt,
                                              'errInfo'  => $arrErr,
                                              'errSekos' => $errSekos,
                                              ),
                         );

        return $rtnData;
    }

    /**
     * @ignore
     */
    protected $_sekoKihon = null;

    /**
     * 施行基本情報 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    protected function _setSekoKihon()
    {
        $sekoNo = $this->_cur_sekoNo;
        $db = Msi_Sys_DbManager::getMyDb();

        $sekoKihon = DataMapper_SekoKihonInfo::findOne( $db, array('seko_no'=>$sekoNo) );
        if ( ! $sekoKihon ) {
            throw new Exception( sprintf("施行No(%s)のデータが存在しません", $sekoNo) );
        }

        $sekoKihon2 = DataMapper_SekoKihon::findOne( $db, array('seko_no'=>$sekoNo) );
        if ( ! $sekoKihon2 ) {
            throw new Exception( sprintf("施行No(%s)のデータが存在しません(2)", $sekoNo) );
        }

        // Msi_Sys_Utils::debug( "** sekoExportOne::sekoKihon($sekoNo)=>" . Msi_Sys_Utils::dump($sekoKihon) );
        // Msi_Sys_Utils::debug( "** sekoExportOne::sekoKihon2($sekoNo)=>" . Msi_Sys_Utils::dump($sekoKihon2) );

        $sekoKihon = array_merge( $sekoKihon2, $sekoKihon );

        if ( isset($sekoKihon2['mg_kbn']) && $sekoKihon2['mg_kbn'] ) { // 故人に同じ
            $sekoKihon['mg_yubin_no'] = $sekoKihon2['kg_yubin_no'];
            $sekoKihon['mg_addr1'] = $sekoKihon2['kg_addr1'];
            $sekoKihon['mg_addr2'] = $sekoKihon2['kg_addr2'];
            $sekoKihon['mg_tel'] = $sekoKihon2['kg_tel'];
        } else {
            $sekoKihon['mg_yubin_no'] = $sekoKihon2['mg_yubin_no'];
            $sekoKihon['mg_addr1'] = $sekoKihon2['mg_addr1'];
            $sekoKihon['mg_addr2'] = $sekoKihon2['mg_addr2'];
            $sekoKihon['mg_tel'] = $sekoKihon2['mg_tel'];
        }

        $this->_sekoKihon = $sekoKihon;

        return $this->_sekoKihon;
    }

    /* /\** */
    /*  * @ignore */
    /*  *\/ */
    /* protected $_juchuDenpyo = null; */

    /* /\** */
    /*  * 受注伝票 取得 */
    /*  * */
    /*  * <AUTHOR> Mihara */
    /*  * @since      2015/xx/xx */
    /*  * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException */
    /*  *\/ */
    /* protected function _setJuchuDenpyo() */
    /* { */
    /*     $sekoNo = $this->_cur_sekoNo; */
    /*     $db = Msi_Sys_DbManager::getMyDb(); */

    /*     $juchuDenpyo = DataMapper_JuchuDenpyo::findOne( $db, array('seko_no'=>$sekoNo, '__raw_1'=>'data_kbn IN (1,2,5)') ); */

    /*     $this->_juchuDenpyo = $juchuDenpyo; */

    /*     return $this->_juchuDenpyo; */
    /* } */

    /**
     * 施行データ等出力（施行データ１件）
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      array  $sekoNos
     * @return     array        array( 'type1'=>array(array(),array(),...), 'type2'=>array(array(),array(),...), ...
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public function sekoExportOne($sekoNo)
    {
        $this->_cur_sekoNo = $sekoNo;

        $db = Msi_Sys_DbManager::getMyDb();

        $jutyu_t_arr   = array(); // １件だけ格納される(別注品は対象外)
        $jutyum_t_arr  = array();
        $kokyaku_t_arr = array();

        $this->_setSekoKihon();

        $sekoKihon = $this->_sekoKihon;
        // Msi_Sys_Utils::debug( "** sekoExportOne::sekoKihon($sekoNo)=>" . Msi_Sys_Utils::dump($sekoKihon) );

        $sekoNiteiTuya  = DataMapper_SekoNiteiExCarn::findOne( $db, array('seko_no'=>$sekoNo, 'nitei_kbn'=>4) );
        $sekoNiteiSogi  = DataMapper_SekoNiteiExCarn::findOne( $db, array('seko_no'=>$sekoNo, 'nitei_kbn'=>7) );
        $sekoNiteiKasou = DataMapper_SekoNiteiExCarn::findOne( $db, array('seko_no'=>$sekoNo, 'nitei_kbn'=>6) );

        // Msi_Sys_Utils::debug( "** sekoExportOne::sekoNiteiTuya($sekoNo)=>" . Msi_Sys_Utils::dump($sekoNiteiTuya) );
        // Msi_Sys_Utils::debug( "** sekoExportOne::sekoNiteiSogi($sekoNo)=>" . Msi_Sys_Utils::dump($sekoNiteiSogi) );
        // Msi_Sys_Utils::debug( "** sekoExportOne::sekoNiteiKasou($sekoNo)=>" . Msi_Sys_Utils::dump($sekoNiteiKasou) );

        $my_bumon_cd = $sekoKihon['bumon_cd'];
        list( $kaisya_cd, $siten_cd, $exp_bumon_cd) = $this->getExp3BumonCd( $my_bumon_cd );

        if ( strlen($kaisya_cd) <= 0 || strlen($siten_cd) <= 0 || strlen($exp_bumon_cd) <= 0 ) {
            $msg = sprintf( "施行No(%s)の(会社CD,支店CD,部門CD=%s,%s,%s)が設定できません. (売上部門CD:%s)",
                            $sekoNo, $kaisya_cd, $siten_cd, $exp_bumon_cd, $my_bumon_cd );
            throw new Exception( $msg );
        }

        $jutyu_no = $this->getExpJutyuNo();
        $this->_cur_expJutyuNo = $jutyu_no;
        $this->_sekoNo_jutyuNo_map[ $sekoNo ] = $jutyu_no;

        $jutyu_tan_cd = $this->getExpJutyuTanCd();

        $jutyu_ymd = $this->getExpJutyuYmd();
        $seko_kbn  = $this->getExpSekoKbn();

        @ $seko_nm   = $this->_f_souke($sekoKihon['souke_nm']);

        list($tuya_ymd, $tuya_str_time, $tuya_end_time, $tuya_basyo_nm) = $this->_f_nitei_data($sekoNiteiTuya);
        list($sogi_ymd, $sogi_str_time, $sogi_end_time, $sogi_basyo_nm) = $this->_f_nitei_data($sekoNiteiSogi);
        list($kasou_ymd, $kasou_str_time, $kasou_end_time, $kasou_basyo_nm) = $this->_f_nitei_data($sekoNiteiKasou);

        $syukyo_nm = mb_substr($sekoKihon['syushi_nm'], 0, 30);
        $syuha_nm  = mb_substr($sekoKihon['syuha_nm'], 0, 30);
        $jiin_nm   = mb_substr($sekoKihon['jiin_nm'], 0, 30);

        list($sogi_keitai_cd1, $sogi_keitai_cd2) = $this->getExpSogiKeitaiCds();

        $jutyuData = array(
                         'kaisya_cd' => $kaisya_cd,
                         'siten_cd'  => $siten_cd,
                         'bumon_cd'  => $exp_bumon_cd,
                         'jutyu_no'  => $jutyu_no,
                         'jutyu_tan_cd' => $jutyu_tan_cd,
                         'jutyu_ymd'    => $jutyu_ymd,
                         'seko_kbn'     => $seko_kbn,
                         'seko_nm'      => $seko_nm,
                         'tuya_ymd'      => $tuya_ymd,
                         'tuya_str_time' => $tuya_str_time,
                         'tuya_end_time' => $tuya_end_time,
                         'tuya_basyo_nm' => $tuya_basyo_nm, // 2025/01 #11675 $this->_adjBasyo( $tuya_basyo_nm ), // 2016/05/30
                         'sogi_ymd'      => $sogi_ymd,
                         'sogi_str_time' => $sogi_str_time,
                         'sogi_end_time' => $sogi_end_time,
                         'sogi_basyo_nm' => $sogi_basyo_nm, // 2025/01 #11675 $this->_adjBasyo( $sogi_basyo_nm ), // 2016/05/30
                         'kasou_ymd'      => $kasou_ymd,
                         'kasou_str_time' => $kasou_str_time,
                         'kasou_end_time' => $kasou_end_time,
                         'kasou_basyo_nm' => $kasou_basyo_nm, // $this->_adjBasyo?
                         'syukyo_nm'      => $syukyo_nm,
                         'syuha_nm'       => $syuha_nm,
                         'jiin_nm'        => $jiin_nm,
                         'sogi_keitai_cd1' => $sogi_keitai_cd1,
                         'sogi_keitai_cd2' => 2, // 固定値とする(2016/05/30)   $sogi_keitai_cd2,
                         'seko_kaihi_nyukin'       => 0,
                         'seko_kaihi_waribiki'     => 0,
                         'seko_kaihi_zan_nyukin'   => 0,
                         'seko_kaihi_zan_waribiki' => 0,
                         'seko_kaihi_zei'          => 0,
                         'seko_soki_kin'           => 0,
                         'seko_soki_zei'           => 0,
                         'seko_no'                 => $sekoNo,   // 2025/03/04 #11675
                         );

        $valItems11675 = $this->_getItems11675(); // 2024/12 #11675
        $jutyuData = array_merge( $jutyuData, $valItems11675 ); // 追加項目設定

        $jutyu_t_arr[] = $jutyuData;

        // Msi_Sys_Utils::debug( "** sekoExportOne::jutyu_t_arr($sekoNo)=>" . Msi_Sys_Utils::dump($jutyu_t_arr) );

        $isKakutei = !!$sekoKihon['jichu_kakute_ymd']; // 受注確定

        if ( $isKakutei ) {
            $msi000 = $this->getUriageMsi();
        } else {
            $msi000 = $this->getJuchuMsi();
        }

        // 受注明細T
        $jutyum_seq = 0;
        foreach ( $msi000 as $msi ) {
            if ( $this->_isJuchuMsiSkip($msi) ) { // 出力対象外  2016/05/31
                $den_no = '';
                if (isset($msi['denpyo_no'])) {
                    $den_no = $msi['denpyo_no'];
                } else if (isset($msi['uri_den_no'])) {
                    $den_no = $msi['uri_den_no'];
                }
                // Msi_Sys_Utils::info( '受注明細を出力しません:' . Msi_Sys_Utils::dump($msi) );
                Msi_Sys_Utils::info( sprintf('受注明細を出力しません:(seko_no=%s, denpyo_no=%s, msi_no=%s) shohin_cd=%s, shohin_nm=%s',
                                             $msi['seko_no'], $den_no, $msi['msi_no'], $msi['shohin_cd'], $msi['shohin_nm']) );
                continue;
            }
            ++ $jutyum_seq;
            $tab_no = 10; // 使わないのでこれでOK
            // Msi_Sys_Utils::debug( "** sekoExportOne::juchuMsi($sekoNo, $jutyum_seq)=>" . Msi_Sys_Utils::dump($msi) );
            $syoData = $this->getExpSyo($msi);
            $syo_cd      = $syoData['syo_cd'];
            $syo_nm      = $syoData['syo_nm'];
            $zei_rnd_kbn = $syoData['zei_rnd_kbn'];
            $zei_kbn     = $syoData['zen_kbn'];
            $zei_rit     = $syoData['zei_rit'];
            $service_flg = $syoData['service_flg'];
            $svc_rnd_kbn = $syoData['svc_rnd_kbn'];
            $svc_rit     = $syoData['svc_rit'];

            if ( $isKakutei ) { // 受注確定   金額は uriage_denpyo_msi から取得
                $tnk        = $msi['uri_tnk'];
                $syohin_num = $msi['juchu_suryo'];
                $goukei_kin = $msi['uri_prc'];
                $kojo_kin1  = $msi['gojokai_nebiki_prc'] * -1;
                $kojo_kin2  = $msi['nebiki_prc'] * -1;
                $uriage_kin = $goukei_kin - $kojo_kin1 - $kojo_kin2;
                $seko_kin   = $uriage_kin;
            }
            else { // 受注未確定   金額は juchu_denpyo_msi から取得
                $tnk        = $msi['juchu_tnk'];
                $syohin_num = $msi['juchu_suryo'];
                $goukei_kin = $msi['juchu_prc'];
                $kojo_kin1  = $msi['gojokai_nebiki_prc'] * -1;
                $kojo_kin2  = $msi['nebiki_prc'] * -1;
                $uriage_kin = $goukei_kin - $kojo_kin1 - $kojo_kin2;
                $seko_kin   = $uriage_kin;
            }

            $course_syohin_flg = 0;
            $plan_syohin_flg = 0;
            $biko = mb_substr($msi['shohin_tkiyo_nm'], 0, 30);

            $msiData = array(
                             'kaisya_cd'   => $kaisya_cd,
                             'siten_cd'    => $siten_cd,
                             'bumon_cd'    => $exp_bumon_cd,
                             'jutyu_no'    => $jutyu_no,
                             'seko_kbn'    => $seko_kbn,
                             'tab_no'      => $tab_no,
                             'jutyum_seq'  => $jutyum_seq,
                             'syo_cd'      => $syo_cd,
                             'syo_nm'      => $syo_nm,
                             'tnk'         => $tnk,
                             'syohin_num'  => $syohin_num,
                             'goukei_kin'  => $goukei_kin,
                             'uriage_kin'  => $uriage_kin,
                             'kojo_kin1'   => $kojo_kin1,
                             'kojo_kin2'   => $kojo_kin2,
                             'seko_kin'    => $seko_kin,
                             'zei_rnd_kbn' => $zei_rnd_kbn,
                             'zei_kbn'     => $zei_kbn,
                             'zei_rit'     => $zei_rit,
                             'service_flg'      => $service_flg,
                             'svc_rnd_kbn'      => $svc_rnd_kbn,
                             'svc_rit'          => $svc_rit,
                             'course_syohin_flg' => 0,
                             'plan_syohin_flg'   => 0,
                             'biko'        => $biko,
                             'reduced_tax_rate'  => $msi['reduced_tax_rate'],
                             );

            $jutyum_t_arr[] = $msiData;
        }

        //
        // 故人データ
        //
        $fnl_kokyaku_no = $this->getExpKokyakuNo();
        $sogi_jutyu_no = $jutyu_no;
        @ $simei_nm   = $sekoKihon['k_nm'];
        @ $simei_kana = $this->_f_kana_zen2han($sekoKihon['k_knm']);
        @ list($tel_no1, $tel_no2, $tel_no3) = $this->_f_tel_spliter($sekoKihon['kg_tel']);
        @ list($zip1, $zip2) = $this->_f_zip_spliter($sekoKihon['kg_yubin_no']);
        @ $adr1 = mb_substr($sekoKihon['kg_addr1'], 0, 75);
        @ $adr2 = mb_substr($sekoKihon['kg_addr2'], 0, 75);
        @ $birth_ymd = $this->_f_birth_u($sekoKihon['k_gengo'], $sekoKihon['k_seinengappi_ymd']);
        @ $sibo_ymd  = $this->getExpSiboYmd();
        @ $sibo_time = $this->getExpSiboTime(); // 2024/12 #11675
        @ list($mob_no1, $mob_no2, $mob_no3) = array(null, null, null); // 2024/12 #11675
        @ $sex         = $sekoKihon['k_sex_nm']; // 2024/12 #11675
        @ $zokugara_cd = $sekoKihon['m_zoku_kbn2']; // 2024/12 #11675    
        @ $zokugara_nm = $this->_getZokuNm($sekoKihon['m_zoku_kbn2']); // 2024/12 #11675   喪主様からみた故人様の続柄
        $kojinData = array(
                           'kaisya_cd'       => $kaisya_cd,
                           'siten_cd'        => $siten_cd,
                           'bumon_cd'        => $exp_bumon_cd,
                           'seko_kbn'        => $seko_kbn,
                           'fnl_kokyaku_no'  => $fnl_kokyaku_no,
                           'fnl_kokyaku_kbn' => '1', // 1:故人
                           'jutyu_no'        => $jutyu_no,
                           'sogi_jutyu_no'   => $sogi_jutyu_no,
                           'simei_nm'        => $simei_nm,
                           'simei_kana'      => $simei_kana,
                           'tel_no1'         => $tel_no1,
                           'tel_no2'         => $tel_no2,
                           'tel_no3'         => $tel_no3,
                           'zip1'            => $zip1,
                           'zip2'            => $zip2,
                           'adr1'            => $adr1,
                           'adr2'            => $adr2,
                           'birth_ymd'       => $birth_ymd,
                           'sibo_ymd'        => $sibo_ymd,
                           'sibo_time'       => $sibo_time,   // 2024/12 #11675
                           'mob_no1'         => $mob_no1,     // 2024/12 #11675
                           'mob_no2'         => $mob_no2,     // 2024/12 #11675
                           'mob_no3'         => $mob_no3,     // 2024/12 #11675
                           'sex'             => $sex,         // 2024/12 #11675
                           'zokugara_cd'     => $zokugara_cd, // 2024/12 #11675
                           'zokugara_nm'     => $zokugara_nm, // 2024/12 #11675
                           );
        $kokyaku_t_arr[] = $kojinData;

        //
        // 喪主データ
        //
        $fnl_kokyaku_no = $this->getExpKokyakuNo();
        $sogi_jutyu_no = $jutyu_no;
        @ $simei_nm   = $sekoKihon['m_nm'];
        if ( strlen($simei_nm) > 0 ) {
            @ $simei_kana = $this->_f_kana_zen2han($sekoKihon['m_knm']);
            @ list($tel_no1, $tel_no2, $tel_no3) = $this->_f_tel_spliter($sekoKihon['mg_tel']);
            @ list($zip1, $zip2) = $this->_f_zip_spliter($sekoKihon['mg_yubin_no']);
            @ $adr1 = mb_substr($sekoKihon['mg_addr1'], 0, 75);
            @ $adr2 = mb_substr($sekoKihon['mg_addr2'], 0, 75);
            @ $birth_ymd = $this->_f_birth_u($sekoKihon['m_gengo'], $sekoKihon['m_seinengappi_ymd']);
            $sibo_ymd  = null;
            $sibo_time = null; // 2024/12 #11675
            @ list($mob_no1, $mob_no2, $mob_no3) = $this->_f_tel_spliter($sekoKihon['mg_m_tel']); // 2024/12 #11675
            @ $sex       = null; // 2024/12 #11675
            @ $zokugara_cd = $sekoKihon['m_zoku_kbn']; // 2024/12 #11675
            @ $zokugara_nm = $this->_getZokuNm($sekoKihon['m_zoku_kbn']); // 2024/12 #11675  続柄
            $mosyuData = array(
                               'kaisya_cd'       => $kaisya_cd,
                               'siten_cd'        => $siten_cd,
                               'bumon_cd'        => $exp_bumon_cd,
                               'seko_kbn'        => $seko_kbn,
                               'fnl_kokyaku_no'  => $fnl_kokyaku_no,
                               'fnl_kokyaku_kbn' => '2', // 2:喪主
                               'jutyu_no'        => $jutyu_no,
                               'sogi_jutyu_no'   => $sogi_jutyu_no,
                               'simei_nm'        => $simei_nm,
                               'simei_kana'      => $simei_kana,
                               'tel_no1'         => $tel_no1,
                               'tel_no2'         => $tel_no2,
                               'tel_no3'         => $tel_no3,
                               'zip1'            => $zip1,
                               'zip2'            => $zip2,
                               'adr1'            => $adr1,
                               'adr2'            => $adr2,
                               'birth_ymd'       => $birth_ymd,
                               'sibo_ymd'        => $sibo_ymd,
                               'sibo_time'       => $sibo_time,   // 2024/12 #11675
                               'mob_no1'         => $mob_no1,     // 2024/12 #11675
                               'mob_no2'         => $mob_no2,     // 2024/12 #11675
                               'mob_no3'         => $mob_no3,     // 2024/12 #11675
                               'sex'             => $sex,         // 2024/12 #11675
                               'zokugara_cd'     => $zokugara_cd, // 2024/12 #11675
                               'zokugara_nm'     => $zokugara_nm, // 2024/12 #11675
                               );
            $kokyaku_t_arr[] = $mosyuData;
        }

        //
        // 施主データ
        //
        if ( $sekoKihon['sekyu_kbn'] == 1 ) { // 1：喪主に同じ
            $fnl_kokyaku_no = $this->getExpKokyakuNo();
            $sesyuData = $mosyuData;
            $sesyuData['fnl_kokyaku_no'] = $fnl_kokyaku_no;
            $sesyuData['fnl_kokyaku_kbn'] = 3;

            $kokyaku_t_arr[] = $sesyuData;
        }
        else { // sekyu_saki_info から取得
            $sekyuData000 = $this->sekyuSakiInfo();
            foreach ( $sekyuData000 as $sekyu ) {
                @ $simei_nm   = $sekyu['sekyu_nm'];
                if ( strlen($sekyu['sekyu_nm']) <= 0 ) {
                    continue;
                }
                $fnl_kokyaku_no = $this->getExpKokyakuNo();
                $sogi_jutyu_no = $jutyu_no;
                @ $simei_kana = $this->_f_kana_zen2han($sekyu['sekyu_knm']);
                @ list($tel_no1, $tel_no2, $tel_no3) = $this->_f_tel_spliter($sekyu['tel']);
                @ list($zip1, $zip2) = $this->_f_zip_spliter($sekyu['yubin_no']);
                @ $adr1 = mb_substr($sekyu['addr1'], 0, 75);
                @ $adr2 = mb_substr($sekyu['addr2'], 0, 75);
                $birth_ymd = null;
                $sibo_ymd  = null;
                $sibo_time = null; // 2024/12 #11675
                @ list($mob_no1, $mob_no2, $mob_no3) = $this->_f_tel_spliter($sekyu['mobile_tel']); // 2024/12 #11675
                @ $sex       = null; // 2024/12 #11675
                @ $zokugara_cd = $sekyu['kojin_kankei_kbn']; // 2024/12 #11675
                @ $zokugara_nm = $this->_getZokuNm($sekyu['kojin_kankei_kbn']); // 2024/12 #11675  故人様からみた続柄名
                $sesyuData = array(
                                   'kaisya_cd'       => $kaisya_cd,
                                   'siten_cd'        => $siten_cd,
                                   'bumon_cd'        => $exp_bumon_cd,
                                   'seko_kbn'        => $seko_kbn,
                                   'fnl_kokyaku_no'  => $fnl_kokyaku_no,
                                   'fnl_kokyaku_kbn' => '3', // 3:施主
                                   'jutyu_no'        => $jutyu_no,
                                   'sogi_jutyu_no'   => $sogi_jutyu_no,
                                   'simei_nm'        => $simei_nm,
                                   'simei_kana'      => $simei_kana,
                                   'tel_no1'         => $tel_no1,
                                   'tel_no2'         => $tel_no2,
                                   'tel_no3'         => $tel_no3,
                                   'zip1'            => $zip1,
                                   'zip2'            => $zip2,
                                   'adr1'            => $adr1,
                                   'adr2'            => $adr2,
                                   'birth_ymd'       => $birth_ymd,
                                   'sibo_ymd'        => $sibo_ymd,
                                   'sibo_time'       => $sibo_time, // 2024/12 #11675
                                   'sibo_time'       => $sibo_time,   // 2024/12 #11675
                                   'mob_no1'         => $mob_no1,     // 2024/12 #11675
                                   'mob_no2'         => $mob_no2,     // 2024/12 #11675
                                   'mob_no3'         => $mob_no3,     // 2024/12 #11675
                                   'sex'             => $sex,         // 2024/12 #11675
                                   'zokugara_cd'     => $zokugara_cd, // 2024/12 #11675
                                   'zokugara_nm'     => $zokugara_nm, // 2024/12 #11675
                                   );
                $kokyaku_t_arr[] = $sesyuData;
            }
        }

        $jutyum_t_arr = $this->_packJutyum($jutyum_t_arr); // 受注明細は商品CDで合算

        return array( $jutyu_t_arr, $jutyum_t_arr, $kokyaku_t_arr );
    }

    /**
     * 受注明細の調整。商品CDで合算する
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      array  $jutyum_t_arr   受注明細データ
     * @return     array                  新受注明細データ
     */
    protected function _packJutyum($jutyum_t_arr)
    {
        $keys_in = array(); // 合算するキーの出現順
        $new_jutyum_keyed = array(); // 合算するキー(syo_cd+syo_nm+tnk+zei_rnd_kbn+zei_kbn+zei_rit)でアクセスする受注明細データ

        foreach ( $jutyum_t_arr as $rec ) {
            // Msi_Sys_Utils::debug( "** _packJutyum==>" . Msi_Sys_Utils::dump($jutyum_t_arr) );
            $key = sprintf( "%s:%s:%s:%s:%s:%s", $rec['syo_cd'], $rec['syo_nm'], $rec['tnk'],
                            $rec['zei_rnd_kbn'], $rec['zei_kbn'], $rec['zei_rit'] );
            // Msi_Sys_Utils::debug( "** _packJutyum key==>" . $key );
            if ( array_key_exists($key, $new_jutyum_keyed) ) { // 同じキーのデータあり
                $rec00 = $new_jutyum_keyed[ $key ];
                foreach ( Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
syohin_num  goukei_kin  kojo_kin1  kojo_kin2  uriage_kin  seko_kin
END_OF_TXT
                                                         ) as $k ) {
                    $rec00[$k] = $rec00[$k] + $rec[$k];
                }
                $new_jutyum_keyed[ $key ] = $rec00;
            }
            else { // 同じキーのデータなし
                $new_jutyum_keyed[ $key ] = $rec;
                $keys_in[] = $key;
            }
        }

        $new_jutyum_t_arr = array(); // 新受注明細データ
        $new_jutyum_seq = 0; // 新しい jutyum_seq(行番号)
        foreach ( $keys_in as $key ) {
            $recNew = $new_jutyum_keyed[ $key ];
            $recNew['jutyum_seq'] = ++ $new_jutyum_seq;
            // if ( ($recNew['tnk'] * $recNew['syohin_num']) != $recNew['goukei_kin'] ) { // 単価の調整
            //    $recNew['tnk'] = round( $recNew['goukei_kin'] / $recNew['syohin_num'] );
            // }
            $new_jutyum_t_arr[] = $recNew;
        }

        // Msi_Sys_Utils::debug( "** _packJutyum(OLD)==>" . Msi_Sys_Utils::dump($jutyum_t_arr) );
        // Msi_Sys_Utils::debug( "** _packJutyum(NEW)==>" . Msi_Sys_Utils::dump($new_jutyum_t_arr) );

        return $new_jutyum_t_arr;
    }

    /**
     * 施行データ出力済み設定
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      array  $okSekonos   実際に正しく処理された seko_no
     * @return     void
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public function updSekoExpInfo($okSekonos)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $req_id = $db->getOneVal( "SELECT CURRVAL('x_req_id_seq')" );
        $dt = Msi_Sys_Utils::getDatetimeStd();

        $sql = <<< END_OF_SQL
UPDATE seko_kihon_info
   SET link_exp_no=:link_exp_no, link_exp_info=:link_exp_info
 WHERE seko_no=:seko_no
   AND delete_flg=0
END_OF_SQL;
        $stmt = $db->execPrepared( $sql );

        $info = array( 'user'   => Msi_Sys_Utils::getUserCd(),
                       'ts'     => $dt,
                       'req_id' => $req_id );
        $json = Msi_Sys_Utils::json_encode($info);

        $cnt = 0;
        foreach ( Msi_Sys_Utils::arrayify($this->_sekoNo_jutyuNo_map) as $sekoNo => $expJutyuNo ) {
            if ( !in_array($sekoNo, $okSekonos) ) continue; // OK でないものは読み飛ばす
            $dbparam = array( 'link_exp_no'   => $expJutyuNo,
                              'link_exp_info' => $json,
                              'seko_no'       => $sekoNo );
            $cnt += $db->stmtExecute($stmt, $dbparam);
        }

        return $cnt;
    }

    /**
     * 日程データフィルタ
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      string $nitei
     * @return     array( <yyyymmdd>, <hhmm> )
     */
    protected function _f_nitei_data($nitei)
    {
        $ymd   = null;
        $hh    = null;
        $hh_ed = null;
        $basyo = null;

        if ( $nitei && isset($nitei['nitei_ymd']) ) {
            $ts = $nitei['nitei_ymd'];
            $ymd = Msi_Sys_Utils::normYYYYMMDD($ts);
            if ( preg_match( '/^(\d{4,4})(\D)(\d{1,2})(\D)(\d{1,2}+)(\s+)(\d{1,2})(\D)(\d{1,2})/', $ts, $match ) ) {
                $hh = sprintf( "%02d%02d", $match[7], $match[9] );
            }
        }
        if ( $nitei && isset($nitei['nitei_ed_ymd']) ) {
            $ts_ed = $nitei['nitei_ed_ymd'];
            if ( preg_match( '/^(\d{4,4})(\D)(\d{1,2})(\D)(\d{1,2}+)(\s+)(\d{1,2})(\D)(\d{1,2})/', $ts_ed, $match ) ) {
                $hh_ed = sprintf( "%02d%02d", $match[7], $match[9] );
            }
        }

        if ( $nitei && isset($nitei['basho_nm']) ) {
            $_basho_kbn = $nitei['basho_kbn'];
            $_basho_nm = $nitei['basho_nm'];
            if ( $_basho_kbn == 7 && isset($nitei['sikijo_nm']) ) { // 斎場（火葬場）7
                $_basho_nm .= $nitei['sikijo_nm']; // #11675 場所区分が斎場の場合は場所名＋式場名にする
            }
            $basyo = mb_substr($_basho_nm, 0, 60); // now:  basho_nm          | character varying(40)
        } else if ($nitei && isset($nitei['basho_kbn']) && $nitei['basho_kbn'] == '0') {
            if (isset($nitei['nitei_kbn']) && ($nitei['nitei_kbn'] == '4' || $nitei['nitei_kbn'] == '7'
                                               || $nitei['nitei_kbn'] == '3')) { // 通夜と葬儀   3(納棺) 2024/12 #11675
                $basyo = '自宅';
            }
        }

        return array($ymd, $hh, $hh_ed, $basyo);
    }

    /**
     * 電話番号分割
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      string $tel
     * @return     array( tel_no1, tel_no2, tel_no3 )
     */
    protected function _f_tel_spliter($tel)
    {
        if ( !is_string($tel) ) {
            return array( null, null, null );
        }
        $tel = Msi_Sys_Utils::trim($tel);
        @ list($tel_no1, $tel_no2, $tel_no3) = preg_split('|[-]|', $tel,  3, PREG_SPLIT_NO_EMPTY );
        return array($tel_no1, $tel_no2, $tel_no3);
    }

    /**
     * 郵便番号分割
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      string $zip
     * @return     array( zip_no1, zip_no2)
     */
    protected function _f_zip_spliter($zip)
    {
        if ( !is_string($zip) ) {
            return array( null, null );
        }
        $zip = Msi_Sys_Utils::trim($zip);
        @ list($zip_no1, $zip_no2) = preg_split('|[-]|', $zip,  2, PREG_SPLIT_NO_EMPTY );
        return array($zip_no1, $zip_no2);
    }

    /**
     * 生年月日ユーティリティ関数
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      string $gen   元号 M：明治　T：大正 S：昭和 H：平成
     * @param      string $wareki_ymd
     * @return     string 
     */
    protected function _f_birth_u($gen, $wareki_ymd)
    {
        $y = $m = $d = null;
        if ( preg_match('/^(\d{1,4})\D(\d{1,2})\D(\d{1,2}+)/', $wareki_ymd, $match) ) {
            list( $y, $m, $d ) = array_slice($match, 1, 3);
        }
        if ( $y === null ) {
            return null; // $gen . $wareki_ymd;
        }

        $ymd = Msi_Sys_Utils::warekiToSeireki($gen, $y, $m, $d);
        if ( $ymd === null ) {
            return null; // $gen . $wareki_ymd;
        }

        return Msi_Sys_Utils::normYYYYMMDD($ymd);
    }

    /**
     * 全角カナを半角カナに変換して返す
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      string $zenkaku_kana
     * @return     string  半角カナ
     */
    protected function _f_kana_zen2han($zenkaku_kana)
    {
        if ( !is_string($zenkaku_kana) ) {
            return $zenkaku_kana;
        }
        $han = mb_convert_kana($zenkaku_kana, 'k', 'UTF-8'); // 半角カナ
        $han = preg_replace( '/(　)+/us', ' ', $han ); // zenkaku space
        return $han;
    }

    /**
     * 葬家フィルタ
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      string $txt
     * @return     string
     */
    protected function _f_souke($txt)
    {
        if ( strlen($txt) < 1 ) {
            return $txt;
        }
        return $txt . '家';
    }

    /**
     * カーニバル連携出力用 会社CD,支店CD,部門CD 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      string $bumon_cd
     * @return     array( kaisya_cd, siten_cd, bumon_cd )
     */
    public function getExp3BumonCd($bumon_cd)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $bumon = DataMapper_BumonEx::findOne($db, array('bumon_cd'=>$bumon_cd));

        if ( $bumon === null ) {
            $sekoNo = $this->_cur_sekoNo;
            throw new Exception( sprintf("施行No(%s)の部門CD(%s)で部門マスタが検索できません", $sekoNo, $bumon_cd) );
        }

        // 会社CD:エリアCDの１文字目、支店CD:エリアCDの２文字目、部門CD:kaikei_bumon_cd
        $area_cd = $bumon['area_cd'];
        $kaikei_bumon_cd = $bumon['kaikei_bumon_cd'];
        $kaisya_cd = substr($area_cd, 0, 1);
        $siten_cd  = substr($area_cd, 1, 1);

        return array($kaisya_cd, $siten_cd, $kaikei_bumon_cd);
    }

    /**
     * カーニバル連携出力用 受注No(8byte) 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     string
     */
    public function getExpJutyuNo()
    {
        $sekoNo = $this->_cur_sekoNo;
        $db = Msi_Sys_DbManager::getMyDb();

        $expJutyuNoRec = $db->easySelOne( <<< END_OF_SQL
SELECT link_exp_no, link_exp_info
  FROM seko_kihon_info
 WHERE seko_no=:seko_no
   AND delete_flg=0
END_OF_SQL
                                       , array('seko_no'=>$sekoNo) );
        if ( ! $expJutyuNoRec ) {
            $msg = sprintf("施行No(%s)の seko_kihon_info.link_exp_info が取得できません", $sekoNo);
            throw new Exception($msg);
        }

        $linkExpNo = $expJutyuNoRec['link_exp_no'];
        if ( strlen($linkExpNo) > 0 ) {
            return $linkExpNo;
        }

        $saiban = $this->_saibanNew( 'canival_jutyu_no' );
        $linkExpNoNew = sprintf( "%02s%06d", '12', $saiban );

        $this->_sekoNo_jutyuNo_map_new[ $sekoNo ] = $linkExpNoNew;

        return $linkExpNoNew;
    }

    /**
     * カーニバル連携出力用 受注No(8byte) 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     string
     */
    public function getExpKokyakuNo()
    {
        $saiban = $this->_saibanNew( 'canival_kokyaku_no' );
        $noStr = sprintf( "%02s%06d", '10', $saiban );
        return $noStr;
    }

    /**
     * 採番テーブルで番号を発行する
     *  cf. App_ClsGetCodeNo::GetCodeNo
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      string   $key
     * @return     string
     */
    protected function _saibanNew( $key )
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $knriNo = $db->easySelOne( <<< END_OF_SQL
SELECT knri_no
  FROM knri_no_mst
 WHERE tbl_id='carnival_link'
   AND komoku_id=:komoku_id
   AND yymm='0000'
   AND delete_flg=0
END_OF_SQL
                                   , array('komoku_id'=>$key) );

        if ( !$knriNo ) {
            $cnt = $db->easyExecute(<<< END_OF_SQL
INSERT INTO knri_no_mst( tbl_id, komoku_id, yymm, knri_no )
               VALUES  ( 'carnival_link', :komoku_id, '0000', 0 );
END_OF_SQL
                                    , array('komoku_id'=>$key) );
            if ( $cnt !== 1 ) {
                throw new Exception( "採番テーブル登録に失敗しました" );
            }
        }

        $knriNo = $db->easySelOne( <<< END_OF_SQL
SELECT knri_no
  FROM knri_no_mst
 WHERE tbl_id='carnival_link'
   AND komoku_id=:komoku_id
   AND yymm='0000'
   AND delete_flg=0
   FOR UPDATE
END_OF_SQL
                                   , array('komoku_id'=>$key) );

        if ( !$knriNo ) {
            throw new Exception( "採番テーブル取得に失敗しました" );
        }

        $newKnriNo = (int)$knriNo['knri_no'] + 1;

        $cnt = $db->easyExecute(<<< END_OF_SQL
UPDATE knri_no_mst
   SET knri_no=:knri_no
 WHERE tbl_id='carnival_link'
   AND komoku_id=:komoku_id
   AND yymm='0000'
   AND delete_flg=0
END_OF_SQL
                                , array('komoku_id'=>$key, 'knri_no'=>$newKnriNo) );
        if ( $cnt !== 1 ) {
            throw new Exception( "採番テーブル更新に失敗しました" );
        }

        return $newKnriNo;
    }

    /**
     * カーニバル連携出力用 受注担当者CD 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     string
     */
    public function getExpJutyuTanCd()
    {
        $sekoKihon = $this->_sekoKihon;

        $db = Msi_Sys_DbManager::getMyDb();
        $tanto_cd = $sekoKihon['uketuke_tanto_cd'];
        $loginMst = DataMapper_LoginMst::findOne($db, array('tanto_cd'=>$tanto_cd), false);
        if ( ! $loginMst ) {
            throw new Exception( sprintf("担当者CD(%s)の login_mst が存在しません", $tanto_cd) );
        }
        $jutyu_tan_cd = substr($loginMst['login_cd'], 0, 6);
        return $jutyu_tan_cd;
    }

    /**
     * カーニバル連携出力用 受注日時 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     string
     */
    public function getExpJutyuYmd()
    {
        $sekoNo = $this->_cur_sekoNo;

        $db = Msi_Sys_DbManager::getMyDb();
        $sekoKihonAllFree = $db->easySelOne( <<< END_OF_SQL
SELECT ts_free1
  FROM seko_kihon_all_free
 WHERE seko_no=:seko_no AND seq_no=:seq_no
   AND delete_flg=0
END_OF_SQL
                                             , array('seko_no'=>$sekoNo, 'seq_no'=>0) );

        if ( ! $sekoKihonAllFree ) {
            $msg = sprintf("施行No(%s)の seko_kihon_all_free が存在しません", $sekoNo);
            // Msi_Sys_Utils::warn( $msg );
            // throw new Exception($msg); // XXX
            $jutyu_ymd = null; // XXX XXX XXX  for nowl data test
        } else {
            $ts = $sekoKihonAllFree['ts_free1'];
            if ( preg_match( '/^(\d{4,4})(?:\D)(\d{1,2})(?:\D)(\d{1,2}+)(?:\s+)(\d{1,2})(?:\D)(\d{1,2})/', $ts, $m ) ) {
                $jutyu_ymd = sprintf( "%04d%02d%02d%02d%02d", $m[1], $m[2], $m[3], $m[4], $m[5] );
            } else {
                $msg = sprintf("施行No(%s)の seko_kihon_all_free.ts_free1 (%s)が不正です", $ts);
                throw new Exception($msg);
            }
        }

        return $jutyu_ymd;
    }

    /**
     * カーニバル連携出力用 施行区分 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @version    2024/12/xx mihara #11675 葬儀は１事前相談は9
     * @return     integer
     */
    public function getExpSekoKbn()
    {
        // 2024/12 org   return 1; // 1:葬儀

        $sekoNo = $this->_cur_sekoNo;

        $db = Msi_Sys_DbManager::getMyDb();
        $sekoKihon = $db->easySelOne( <<< END_OF_SQL
SELECT moushi_kbn
  FROM seko_kihon_info
 WHERE seko_no=:seko_no
   AND delete_flg=0
END_OF_SQL
                                      , array('seko_no' => $sekoNo) );
        if ( !$sekoKihon ) {
            $msg = sprintf("施行No(%s)の seko_kihon_info ?", $sekoNo);
            throw new Exception($msg);
        }

        $moushi_kbn = $sekoKihon['moushi_kbn'];
        if ( $moushi_kbn == 5 ) { // 5(事前相談) 0010(申込区分)
            $seko_kbn = 9; // 葬儀は１事前相談は9
        } else {
            $seko_kbn = 1; // 葬儀は１事前相談は9
        }

        return $seko_kbn;
    }

    /**
     * カーニバル連携出力用 葬儀形態CD1,CD2 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     array(sogi_keitai_cd1, sogi_keitai_cd2)
     */
    public function getExpSogiKeitaiCds()
    {
        $sekoNo = $this->_cur_sekoNo;

        $db = Msi_Sys_DbManager::getMyDb();
        $sekoKihon = $db->easySelOne( <<< END_OF_SQL
SELECT sougi_kbn, keishiki_kbn
  FROM seko_kihon_info
 WHERE seko_no=:seko_no
   AND delete_flg=0
END_OF_SQL
                                      , array('seko_no' => $sekoNo) );
        if ( !$sekoKihon ) {
            $msg = sprintf("施行No(%s)の seko_kihon_info ?", $sekoNo);
            throw new Exception($msg);
        }

        $sogi_keitai_cd1 = $sekoKihon['sougi_kbn'];
        $sogi_keitai_cd2 = $sekoKihon['keishiki_kbn'];
        return array( $sogi_keitai_cd1, $sogi_keitai_cd2 );
    }

    /**
     * 受注明細の出力可否
     * これが真を返せば出力を抑制する
     *
     * <AUTHOR> Mihara
     * @since      2016/05/31
     * @param      array   $juchuMsi
     * @return     boolean  true(出力しない)|false(出力する=通常)
     */
    protected function _isJuchuMsiSkip($juchuMsi)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $shohin_cd     = $juchuMsi['shohin_cd'];
        $dai_bunrui_cd = $juchuMsi['dai_bunrui_cd'];
        $chu_bunrui_cd = $juchuMsi['chu_bunrui_cd'];
        $shohin_kbn    = $juchuMsi['shohin_kbn'];

        $cond = array( 'shohin_cd'     => $shohin_cd,
                       'dai_bunrui_cd' => $dai_bunrui_cd,
                       'chu_bunrui_cd' => $chu_bunrui_cd,
                       'shohin_kbn'    => $shohin_kbn );

        $rec = DataMapper_ShohinBunrui::findOne($db, $cond, true); // st_date,ed_date を抽出条件にいれる
        if ( $rec === null ) {
            $rec = DataMapper_ShohinBunrui::findOne($db, $cond, false); // st_date,ed_date を抽出条件にいれない
        }

        if ( $rec === null ) {
            Msi_Sys_Utils::warn( '商品分類マスタがありません:' . Msi_Sys_Utils::dump($cond) );
            return false;
        }

        if ( (int)$rec['syoukei_group_cd'] === 99 ) {
            return true;
        }

        return false;
    }

    /**
     * カーニバル連携出力用 商品情報 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @param      array   $juchuMsi
     * @return     array('syo_cd'=>v1, 'syo_nm'=>v2, ...)
     */
    public function getExpSyo($juchuMsi)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        $zei_rit = 0;
        $svc_rit = 0;
        $zei_1 = DataMapper_ZeiMst::findOne( $db, array('zei_cd'=>$juchuMsi['zei_cd']), false );
        if ( $zei_1 ) {
            $zei_rit = $zei_1['zei_rtu'] / 100.0;
        }
        if ( $juchuMsi['hoshi_umu_kbn'] == 1 ) {
            $zei_2 = DataMapper_HoshirituMst::findOne( $db, array('hoshi_ritu_cd'=>$juchuMsi['hoshi_ritu_cd']), false );
            if ( $zei_2 ) {
                $svc_rit = $zei_2['zei_rtu'] / 100.0;
            }
        }

        $shohin_cd     = $juchuMsi['shohin_cd'];
        $shohin_nm     = $juchuMsi['shohin_nm'];
        $dai_bunrui_cd = $juchuMsi['dai_bunrui_cd'];
        $chu_bunrui_cd = $juchuMsi['chu_bunrui_cd'];
        $shohin_kbn    = $juchuMsi['shohin_kbn'];

        $cond = array( 'app_cd' => 2, // 2: カーニバル, code_kbn:1840
                       'shohin_cd'     => $shohin_cd,
                       'dai_bunrui_cd' => $dai_bunrui_cd,
                       'chu_bunrui_cd' => $chu_bunrui_cd,
                       'shohin_kbn'    => $shohin_kbn );

        // 2016/05/26 mihara 0090(自動車関係)の場合、siire_cd も考慮する
        // 2016/07/20 mihara 0260(会葬礼状関係)の場合、siire_cd も考慮する
        // 2016/07/29 mihara 寝台料金
        // 2016/10/20 mihara 0001390(寝台料金),0001400(寝台料金２),0001410(寝台料金３)へ追加
        // 2018/2/2 sai 0001411(寝台料金4),0001412(寝台料金5)へ追加
        if ( preg_match('/^(0001390|0001400|0001410|0001411|0001412|0013150|0013160|0013210|0013220|0013170|0013180|0013190|0013200|0013230)$/', $shohin_cd) ) { // 
            $_linkShohinMsts = $this->_linkShohinSpShindai( $db, $cond, $juchuMsi );
        } else if ( $chu_bunrui_cd == '0090' ) {
            $_linkShohinMsts = $this->_linkShohinSp0090( $db, $cond, $juchuMsi );
        } else if ( $chu_bunrui_cd == '0260' ) {
            $_linkShohinMsts = $this->_linkShohinSp0260( $db, $cond, $juchuMsi );
        } else {
            $_linkShohinMsts = DataMapper_LinkShohin::find( $db, $cond );
        }

        $linkShohinMst = null;
        foreach ( $_linkShohinMsts as $lsm00 ) { // 会員の場合はそのデータを選ぶ
            if ( $linkShohinMst === null ) {
                $linkShohinMst = $lsm00;
                continue;
            }
            // すでに設定されている場合は会員区分を考慮
            $mbr_kbn = $lsm00['mbr_kbn'];
            $kaiin_kbn = $this->_sekoKihon['kaiin_kbn']; // 1:互助会, 2:一般, 9:その他
            // Msi_Sys_Utils::debug( '** mbr_kbn=>' . $mbr_kbn . '  kaiin_kbn=>'  . $kaiin_kbn );
            if ( ($this->_sekoKihon['kaiin_kbn'] == 1 && $mbr_kbn == 1) ||
                 ($this->_sekoKihon['kaiin_kbn'] != 1 && $mbr_kbn == 0) ) {
                $linkShohinMst = $lsm00;
            }
        }

        if ( $linkShohinMst ) {
            $syo_cd = $linkShohinMst['cnv_sho_cd'];
            $syo_nm = $linkShohinMst['cnv_sho_nm'];
            $zei_kbn = $linkShohinMst['cnv_zei_kbn'];
//            $service_flg = $linkShohinMst['cnv_service_obj_flg'];
             $service_flg = $juchuMsi['hoshi_umu_kbn'];
        } else { // XXX patch 
            $msg = sprintf( "商品(%s,%s,%s,%s,%s)のリンク先商品マスタが存在しません",
                            $shohin_nm ,$shohin_cd, $dai_bunrui_cd, $chu_bunrui_cd, $shohin_kbn );
            throw new Exception( $msg );

            /*
            // XXX
            $syo_cd = sprintf("%08s", $juchuMsi['shohin_cd']);
            $syo_nm = mb_substr('XXX-' . $juchuMsi['shohin_nm'], 0, 25);
            $zei_kbn = 1;
            $service_flg = 0;
            */
        }

        $rtnData = array(
                         'syo_cd'      => $syo_cd,
                         'syo_nm'      => $syo_nm,
                         'zei_rnd_kbn' => 2,
                         'zen_kbn'     => $zei_kbn,
                         'zei_rit'     => $zei_rit,
                         'service_flg' => $service_flg,
                         'svc_rnd_kbn' => 2,
                         'svc_rit'     => $svc_rit,
                         );
        return $rtnData;
    }

    /**
     * 売上明細 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     array()
     */
    public function getUriageMsi()
    {
        $sekoNo = $this->_cur_sekoNo;
        $db = Msi_Sys_DbManager::getMyDb();
        // $oraDen = DataMapper_UriageDenpyo::find( $db, array('seko_no'=>$sekoNo) );
        $oraDen = $db->easySelect( <<< END_OF_SQL
SELECT *
  FROM uriage_denpyo ud
 WHERE ud.seko_no=:seko_no
   AND ud.delete_flg=0
   AND ud.data_kbn=1
END_OF_SQL
                                   , array('seko_no'=>$sekoNo) );

        $oraDenNos = array();
        foreach ( $oraDen as $den ) {
            $oraDenNos[] = "'" . $den['uri_den_no'] . "'";
            // $oraDenNos[] = "'" . $den['seikyu_no'] . "'"; // DataMapper_UriageDenpyo 内で uri_den_no => seikyu_no
        }

        if ( count($oraDenNos) <= 0 ) {
            return array();
        }

        $uri_den_no_where = sprintf(" uri_den_no IN (%s) ", implode(",", $oraDenNos));

        $msi = $db->easySelect( <<< END_OF_SQL
SELECT * 
  FROM uriage_denpyo_msi msi
 WHERE $uri_den_no_where
   AND msi.delete_flg=0
 ORDER BY uri_den_no ASC, disp_no ASC
END_OF_SQL
                                );

        return $msi;
    }

    /**
     * 受注明細 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     array()
     */
    public function getJuchuMsi()
    {
        $sekoNo = $this->_cur_sekoNo;
        $db = Msi_Sys_DbManager::getMyDb();
        $oraDen = DataMapper_JuchuDenpyo::find( $db, array('seko_no'=>$sekoNo,'data_kbn' => 1) );

        $oraDenNos = array();
        foreach ( $oraDen as $den ) {
            $oraDenNos[] = "'" . $den['denpyo_no'] . "'";
        }

        if ( count($oraDenNos) <= 0 ) {
            return array();
        }

        $denpyo_no_where = sprintf(" denpyo_no IN (%s) ", implode(",", $oraDenNos));

        $msi = $db->easySelect( <<< END_OF_SQL
SELECT * 
  FROM juchu_denpyo_msi msi
 WHERE $denpyo_no_where
   AND msi.delete_flg=0
 ORDER BY denpyo_no ASC, disp_no ASC
END_OF_SQL
                                );

        return $msi;
    }

    /**
     * カーニバル連携出力用 死亡日時 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     string
     */
    public function getExpSiboYmd()
    {
        $sekoNo = $this->_cur_sekoNo;

        $sibo_ymd = null;

        $db = Msi_Sys_DbManager::getMyDb();
        $sekoKihonAllFree = $db->easySelOne( <<< END_OF_SQL
SELECT ts_free5
  FROM seko_kihon_all_free
 WHERE seko_no=:seko_no AND seq_no=:seq_no
   AND delete_flg=0
END_OF_SQL
                                             , array('seko_no'=>$sekoNo, 'seq_no'=>0) );

        if ( $sekoKihonAllFree ) {
            $ts = $sekoKihonAllFree['ts_free5'];
            if ( preg_match( '/^(\d{4,4})(?:\D)(\d{1,2})(?:\D)(\d{1,2}+)(?:\s+)(\d{1,2})(?:\D)(\d{1,2})/', $ts, $m ) ) {
                $sibo_ymd = sprintf( "%04d%02d%02d", $m[1], $m[2], $m[3] );
                // $sibo_ymd = sprintf( "%04d%02d%02d%02d%02d", $m[1], $m[2], $m[3], $m[4], $m[5] );
            }
        }

        return $sibo_ymd;
    }

    /**
     * カーニバル連携出力用 死亡時刻 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     string
     */
    public function getExpSiboTime()
    {
        $sekoNo = $this->_cur_sekoNo;

        $sibo_time = null;

        $db = Msi_Sys_DbManager::getMyDb();
        $sekoKihonAllFree = $db->easySelOne( <<< END_OF_SQL
SELECT ts_free5
      ,free_kbn4
  FROM seko_kihon_all_free
 WHERE seko_no=:seko_no AND seq_no=:seq_no
   AND delete_flg=0
END_OF_SQL
                                             , array('seko_no'=>$sekoNo, 'seq_no'=>0) );

        if ( $sekoKihonAllFree ) {
            if ( $sekoKihonAllFree['free_kbn4'] != 1 ) { // 時刻入力がない場合は 1
                $ts = $sekoKihonAllFree['ts_free5'];
                if ( preg_match( '/^(\d{4,4})(?:\D)(\d{1,2})(?:\D)(\d{1,2}+)(?:\s+)(\d{1,2})(?:\D)(\d{1,2})/', $ts, $m ) ) {
                    $sibo_time = sprintf( "%02d%02d", $m[4], $m[5] );
                }
            }
        }

        return $sibo_time;
    }

    /**
     * カーニバル連携出力用 続柄名 取得
     *
     * <AUTHOR> Mihara
     * @since      2024/12/xx
     * @param      integer $soku_kbn  続柄コード
     * @return     string
     */
    protected function _getZokuNm($zoku_kbn)
    {
        if ( !$this->_hZokuNmCache ) {
            $db = Msi_Sys_DbManager::getMyDb();
            $sel = $db->easySelect( <<< END_OF_SQL
SELECT kbn_value_cd_num as k, kbn_value_lnm as v
  FROM code_nm_mst 
 WHERE code_kbn='0190'
 ORDER BY kbn_value_cd_num
END_OF_SQL
            );
            $hCache = array_reduce($sel, function ($acc, $rec) { $acc[ $rec['k'] ] = $rec['v']; return $acc; }, []);
            $this->_hZokuNmCache = $hCache;
        }

        if ( !array_key_exists($zoku_kbn, $this->_hZokuNmCache) ) {
            return null;
        }

        $val = $this->_hZokuNmCache[ $zoku_kbn ];
        return $val;
    }

    /**
     * 請求先情報 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/xx/xx
     * @return     array()
     */
    public function sekyuSakiInfo()
    {
        $sekoNo = $this->_cur_sekoNo;
        $db = Msi_Sys_DbManager::getMyDb();

        $data = $db->easySelect( <<< END_OF_SQL
SELECT * 
  FROM sekyu_saki_info ssi
 WHERE ssi.seko_no=:seko_no
   AND ssi.delete_flg=0
 ORDER BY sekyu_cd ASC
END_OF_SQL
                                 , array('seko_no'=>$sekoNo) );

        return $data;
    }

    /**
     * 0090(自動車関係)リンク商品取得
     *
     * 商品中分類コードが、0090：自動車関係の商品の場合のみ、
     * 
     * SEKO_HACHU_INFOにリンクする、siire_cdを抽出して、
     * LINK_SHOHIN_MSTより、siire_cdの抽出条件を追加して、カーニバルの商品コードを取得する。
     * また、取得できない場合は、siire_cd＝9999999として、再度取得する。
     * 尚、9999999でも取得できない場合、対象データ無しのエラーとして良い。
     *
     * <AUTHOR> Mihara
     * @since      2016/05/26
     * @param      Msi_Sys_Db $db
     * @param      array      $cond  条件の一部
     * @param      array      juchuMsi/uriageMsi
     * @return     array      該当データがない場合はarray()を返す
     */
    protected function _NOT_USED_linkShohinSp0090($db, $cond, $juchuMsi)
    {
        if ( strlen($juchuMsi['siire_cd']) > 0 ) {
            // siire_cd あり
            $siire_cd = $juchuMsi['siire_cd'];

            // siire_cd をつけて検索
            $cond['siire_cd'] = $siire_cd;
            $_linkShohinMsts = DataMapper_LinkShohin::find( $db, $cond );

            if ( count($_linkShohinMsts) > 0 ) {
                return $_linkShohinMsts;
            }
        }

        // siire_cd を '9999999' として再度検索
        $cond['siire_cd'] = '9999999';
        $_linkShohinMsts = DataMapper_LinkShohin::find( $db, $cond );

        return $_linkShohinMsts;
    }

    /**
     * 0090(自動車関係)リンク商品取得
     *
     * 商品中分類コードが、0090：自動車関係の商品の場合のみ、
     * SEKO_HACHU_INFOにリンクする、siire_cdを抽出して、
     * LINK_SHOHIN_MSTより、siire_cdの抽出条件を追加して、カーニバルの商品コードを取得する。
     * また、取得できない場合は、siire_cd＝9999999として、再度取得する。
     * 尚、9999999でも取得できない場合、対象データ無しのエラーとして良い。
     *
     * <AUTHOR> Mihara
     * @since      2016/05/26
     * @param      Msi_Sys_Db $db
     * @param      array      $cond  条件の一部
     * @param      array      juchuMsi/uriageMsi
     * @return     array      該当データがない場合はarray()を返す
     */
    protected function _linkShohinSp0090($db, $cond, $juchuMsi)
    {
        $sekoNo = $this->_cur_sekoNo;
        if ( !$sekoNo ) {
            throw new Exception( '施行Noが取得できません at _linkShohinSp0090' );
        }

        $cond2 = $cond;
        unset( $cond2['app_cd'] );
        $cond2['seko_no'] = $sekoNo;

        $shiRec = DataMapper_SekoHachuInfoEx::findOne($db, $cond2);

        if ( $shiRec !== null && strlen($shiRec['siire_cd']) > 0 ) {
            // siire_cd を取得できた
            $siire_cd = $shiRec['siire_cd'];

            // siire_cd をつけて検索
            $cond['siire_cd'] = $siire_cd;
            $_linkShohinMsts = DataMapper_LinkShohin::find( $db, $cond );

            if ( count($_linkShohinMsts) > 0 ) {
                return $_linkShohinMsts;
            }
        }

        // siire_cd を '9999999' として再度検索
        $cond['siire_cd'] = '9999999';
        $_linkShohinMsts = DataMapper_LinkShohin::find( $db, $cond );

        return $_linkShohinMsts;
    }

    /**
     * 0001390(寝台料金),0001400(寝台料金２),0001410(寝台料金３)特別処理
     * chu_bunrui_cd が 0090 なので注意
     *
     * (2016/07/29)
     * １．0001390：寝台料金　は、SEKO_KIHON_INFO　のiso_accept_kbn_1　を参照して、
     *   　①　1　→　自社寝台で、仕入先コードを’9999999’として、LINK_SHOHIN_MST　を検索する。
     *   　②　2　→　他社寝台で、仕入先コードを　iso_siire_cd1として、LINK_SHOHIN_MST　を検索する。
     *   　③　上記以外は、リンク商品無しのエラーとする。
     * ２．0001400：寝台料金２　は、SEKO_KIHON_INFO　のiso_accept_kbn_2　を参照して、
     *   　①　1　→　自社寝台で、仕入先コードを’9999999’として、LINK_SHOHIN_MST　を検索する。
     *   　②　2　→　他社寝台で、仕入先コードを　iso_siire_cd2として、LINK_SHOHIN_MST　を検索する。
     *   　③　上記以外は、リンク商品無しのエラーとする。
     * ３．0001410：寝台料金３　は、自社寝台が固定で、
     *   　　仕入先コードを’9999999’として、LINK_SHOHIN_MST　を検索する。
     *
     * <AUTHOR> Mihara
     * @since      2016/07/29 mihara
     * @param      Msi_Sys_Db $db
     * @param      array      $cond  条件の一部
     * @param      array      juchuMsi/uriageMsi
     * @return     array      該当データがない場合はarray()を返す
     */
    protected function _linkShohinSpShindai($db, $cond, $juchuMsi)
    {
        $sekoNo = $this->_cur_sekoNo;
        if ( !$sekoNo ) {
            throw new Exception( '施行Noが取得できません at _linkShohinSpShindai' );
        }

        $sekoKihonEx = $db->easySelOne( <<<END_OF_SQL
SELECT iso_accept_kbn_1, iso_siire_cd1, iso_accept_kbn_2, iso_siire_cd2, iso_accept_kbn_3, iso_siire_cd3
    ,iso_accept_kbn_4, iso_siire_cd4, iso_accept_kbn_5, iso_siire_cd5  
  FROM seko_kihon_info
 WHERE seko_no=:seko_no
   AND delete_flg=0
END_OF_SQL
                                      , array('seko_no'=>$sekoNo) );
        if ( $sekoKihonEx === null ) {
            throw new Exception( '施行情報(iso_...)が取得できません at _linkShohinSpShindai' );
        }

        $cond2 = $cond;
        unset( $cond2['app_cd'] );
        $cond2['seko_no'] = $sekoNo;

        $siire_cd = null;

        $shohin_cd = (string)$cond['shohin_cd'];
        if ( $shohin_cd === '0001390' || // 0001390(寝台料金)
             $shohin_cd === '0013150' || $shohin_cd === '0013160' || $shohin_cd === '0013210' || $shohin_cd === '0013220' ) {
            $accept_kbn = $sekoKihonEx['iso_accept_kbn_1'];
            if      ( $accept_kbn == 1 ) { $siire_cd = '9999999'; }
            else if ( $accept_kbn == 2 ) { $siire_cd = $sekoKihonEx['iso_siire_cd1']; }
            else                         { return array(); }
        } else if ( $shohin_cd === '0001400' || // 0001400(寝台料金２)
                    $shohin_cd === '0013170' || $shohin_cd === '0013180' || $shohin_cd === '0013190' || $shohin_cd === '0013200' ) {
            $accept_kbn = $sekoKihonEx['iso_accept_kbn_2'];
            if      ( $accept_kbn == 1 ) { $siire_cd = '9999999'; }
            else if ( $accept_kbn == 2 ) { $siire_cd = $sekoKihonEx['iso_siire_cd2']; }
            else                         { return array(); }
        } else if ( $shohin_cd === '0001410' || // 0001410(寝台料金３)
                    $shohin_cd === '0013230' ) { 
            $accept_kbn = $sekoKihonEx['iso_accept_kbn_3'];
            if      ( $accept_kbn == 1 ) { $siire_cd = '9999999'; }
            else if ( $accept_kbn == 2 ) { $siire_cd = $sekoKihonEx['iso_siire_cd3']; }
            else                         { return array(); }
        } else if ( $shohin_cd === '0001411') { // 0001410(寝台料金４)
            $accept_kbn = $sekoKihonEx['iso_accept_kbn_4'];
            if      ( $accept_kbn == 1 ) { $siire_cd = '9999999'; }
            else if ( $accept_kbn == 2 ) { $siire_cd = $sekoKihonEx['iso_siire_cd4']; }
            else                         { return array(); }
        } else if ( $shohin_cd === '0001412') { // 0001410(寝台料金５)
            $accept_kbn = $sekoKihonEx['iso_accept_kbn_5'];
            if      ( $accept_kbn == 1 ) { $siire_cd = '9999999'; }
            else if ( $accept_kbn == 2 ) { $siire_cd = $sekoKihonEx['iso_siire_cd5']; }
            else                         { return array(); }
            // 搬送者3の処理
        } else {
            throw new Exception( 'shohin_cd が不正です(_linkShohinSpShindai)' );
        }

        // siire_cd をつけて検索
        $cond['siire_cd'] = $siire_cd;
        $_linkShohinMsts = DataMapper_LinkShohin::find( $db, $cond );

        return $_linkShohinMsts;
    }

    /**
     * 0260(会葬礼状関係)リンク商品取得
     *
     * <AUTHOR> Mihara
     * @since      2016/07/29
     * @param      Msi_Sys_Db $db
     * @param      array      $cond  条件の一部
     * @param      array      juchuMsi/uriageMsi
     * @return     array      該当データがない場合はarray()を返す
     */
    protected function _linkShohinSp0260($db, $cond, $juchuMsi)
    {
        $sekoNo = $this->_cur_sekoNo;
        if ( !$sekoNo ) {
            throw new Exception( '施行Noが取得できません at _linkShohinSp0260' );
        }

        $cond2 = $cond;
        unset( $cond2['app_cd'] );
        $cond2['seko_no'] = $sekoNo;

        @ $tnk = $juchuMsi['juchu_tnk'];
        if ( isset($juchuMsi['uri_tnk']) ) { // 2016/08/17 mihara
            $tnk = $juchuMsi['uri_tnk'];
        }

        $shiRec = DataMapper_SekoHachuInfoEx::findOne($db, $cond2);

        if ( $shiRec !== null && strlen($shiRec['siire_cd']) > 0 ) {
            // siire_cd を取得できた
            $siire_cd = $shiRec['siire_cd'];

            // siire_cd をつけて検索
            $cond['siire_cd'] = $siire_cd;

            // 販売単価をつけて検索
            $cond['cnv_tanka'] = $tnk;

            $_linkShohinMsts = DataMapper_LinkShohin::find( $db, $cond );

            if ( count($_linkShohinMsts) > 0 ) { // データあり
                return $_linkShohinMsts;
            }
        }

        // siire_cd を '9999999' として再度検索
        $cond['siire_cd'] = '9999999';
        $cond['cnv_tanka'] = $tnk;
        $_linkShohinMsts = DataMapper_LinkShohin::find( $db, $cond );

        if (count($_linkShohinMsts) == 0) { // データなし
            unset($cond['siire_cd']);
            unset($cond['cnv_tanka']);
            $_linkShohinMsts = DataMapper_LinkShohin::find($db, $cond);
        }
        return $_linkShohinMsts;
    }

    // /**
    //  * 場所の編集
    //  * ※（　の前までの名称を設定
    //  * 　　ex.平安会館たま（２F）→平安会館たま
    //  *
    //  * <AUTHOR> Mihara
    //  * @since      2016/05/30
    //  * @param      string  $basyo_nm
    //  * @return     string  編集後場所
    //  */
    // protected function _adjBasyo($basyo_nm)
    // {
    //     $newBasyoNm = preg_replace( '/（.*$/u', '', $basyo_nm );
    //     return $newBasyoNm;
    // }

    /**
     * #11675 の追加項目取得
     *
     * <AUTHOR> Mihara
     * @since      2024/12/xx
     * @return     string
     */
    protected function _getItems11675()
    {
        $sekoNo = $this->_cur_sekoNo;
        $db = Msi_Sys_DbManager::getMyDb();

        $hanso_basho1 = null; // 搬送場所１
        $hanso_basho2 = null; // 搬送場所２
        $member_id_1  = null; // 加入番号1 (加入者番号)
        $apply_no_1   = null; // 契約番号1 (枝番)
        $member_id_2  = null; // 加入番号2 (加入者番号)
        $apply_no_2   = null; // 契約番号2 (枝番)
        $member_id_3  = null; // 加入番号3 (加入者番号)
        $apply_no_3   = null; // 契約番号3 (枝番)
        $member_id_4  = null; // 加入番号4 (加入者番号)
        $apply_no_4   = null; // 契約番号4 (枝番)
        $member_id_5  = null; // 加入番号5 (加入者番号)
        $apply_no_5   = null; // 契約番号5 (枝番)
        $noukan_ts    = null; // 納棺日時
        $noukan_basyo = null; // 納棺場所
        $tel_tanto    = null; // 電話受付担当者
        $iso_kbn      = null; // 移送区分
        $iso_tanto    = null; // 移送担当者
        $iso_tanto2   = null; // 移送担当者2
        $sibo_pl_cd   = null; // 亡くなられた場所区分  2025/04
        $sibo_pl_nm   = null; // 亡くなられた場所  2025/04
        $keisatsu_nm  = null; // 警察署名  2025/04
        $pickup_ymd   = null; // お迎え先予定日  2025/04
        $ofuse_prc    = null; // お布施  2025/04

        $recSeko = $db->easySelOne( <<< END_OF_SQL
SELECT ski.seko_no
      ,ski.hs_kbn
      ,ski.hs_gyomu_kbn
      ,ski.hs_spot_cd
      ,ski.hs_spot_kbn
      ,ski.hs_spot_nm
      ,ski.hs_gyomu_kbn_2
      ,ski.hs_spot_cd_2
      ,ski.hs_spot_kbn_2
      ,ski.hs_spot_nm_2
      ,ski.hs_gyomu_code_kbn2  -- (安置先) 搬送業務コード 区分2 0600(安置先)  2025/04
      ,ski.hs_gyomu_cd2        -- (安置先) 搬送業務コード2  2025/04
      ,ski.hs_gyomu_kbn2       -- (安置先) 搬送業務区分2  2025/04
      ,ski.hs_anchi_kbn  -- 安置先区分  2025/04
      ,ski.hs_anchi_cd   -- 安置先コード  2025/04
      ,ski.hs_anchi_nm   -- 安置先  2025/04
      ,ski.iso_accept_kbn_1  AS iso_kbn
      ,ski.iso_tanto_cd1     AS iso_tanto_cd
      ,tanto1.tanto_nm       AS iso_tanto_nm
      ,ski.iso_siire_cd1     AS iso_siire_cd
      ,siire1.siire_lnm      AS iso_siire_nm
      ,tanto1.tanto_nm  AS iso_tanto_nm1
      ,siire1.siire_lnm AS iso_siire_nm1
      ,sf.tanto_cd2     AS iso_tanto_cd12
      ,tanto2.tanto_nm  AS iso_tanto_nm12
      ,sf.tanto_cd1     AS tel_tanto_cd
      ,sf.tanto_nm1     AS tel_tanto_nm
      ,sf.free1_kbn        AS sibo_basho_kbn        -- 亡くなられた場所 区分  2025/04 搬送業務区分(0220)
                                                    --    0:自宅,5:病院,7:施設,6:警察,9:その他,8:その他場所,10:他社葬儀社移送
      ,cm0220.kbn_value_lnm AS sibo_basho_kbn_nm    -- 亡くなられた場所 区分名
      ,sf.free1_place_kbn  AS sibo_basho_place_kbn  -- 亡くなられた場所 場所区分  2025/04
      ,sf.free1_place_nm   AS sibo_basho_place_nm   -- 亡くなられた場所 場所名  2025/04
      ,sf.v_free23         AS keisatsu_cd           -- 警察署コード  2025/04
      ,sf.v_free24         AS keisatsu_nm           -- 警察署名  2025/04
      ,TO_CHAR(sf.ts_free2, 'YYYYMMDD') AS pickup_date_ymd   -- お迎え先予定日
      ,ski.n_free10        AS ofuse_prc             -- お布施  2025/04
  FROM seko_kihon_info  ski
  LEFT JOIN seko_kihon_all_free  sf
    ON sf.seko_no = ski.seko_no
   AND sf.seq_no = (SELECT MAX(sf00.seq_no) FROM seko_kihon_all_free sf00 WHERE sf00.seko_no=ski.seko_no AND sf00.delete_flg=0)
   AND sf.delete_flg = 0
  LEFT JOIN tanto_mst tanto1
    ON tanto1.tanto_cd = ski.iso_tanto_cd1
  LEFT JOIN tanto_mst tanto2
    ON tanto2.tanto_cd = sf.tanto_cd2
  LEFT JOIN siire_mst siire1
    ON siire1.kaisya_cd = '00000000'
   AND siire1.siire_cd = ski.iso_siire_cd1
  LEFT JOIN code_nm_mst cm0220
    ON cm0220.kbn_value_cd_num = sf.free1_kbn
   AND cm0220.code_kbn='0220'
 WHERE ski.seko_no=:seko_no
   AND ski.delete_flg = 0
END_OF_SQL
                                       , array('seko_no'=>$sekoNo) );
        if ( $recSeko ) {
            // 2025/04 NEW #11675
            $hs_gyomu_kbn2 = $recSeko['hs_gyomu_kbn2']; // (安置先) 搬送業務区分2
            if ( strlen($hs_gyomu_kbn2) > 0 && $hs_gyomu_kbn2 == 0 ) {
                $hanso_basho1 = '自宅';
            } else {
                $hanso_basho1 = Msi_Sys_Utils::emptyToNull( $recSeko['hs_anchi_nm'] );
            }

            // 2025/04 NEW #11675
            $hs_gyomu_kbn = $recSeko['hs_gyomu_kbn']; // 0(自宅). 0600(安置先)  お連れする場所１
            if ( strlen($hs_gyomu_kbn) > 0 && $hs_gyomu_kbn == 0 ) {
                $hanso_basho2 = '自宅';
            } else {
                $hanso_basho2 = Msi_Sys_Utils::emptyToNull( $recSeko['hs_spot_nm'] );
            }

            $_tel_tanto_cd = Msi_Sys_Utils::emptyToNull( $recSeko['tel_tanto_cd'] );
            $tel_tanto = $this->_getLoginCdFromTantoCd($db, $_tel_tanto_cd);

            $iso_kbn   = Msi_Sys_Utils::emptyToNull( $recSeko['iso_kbn'] );
            if ( $iso_kbn == 1 ) { // 1(自社 1930)
                $_iso_tanto_cd = Msi_Sys_Utils::emptyToNull( $recSeko['iso_tanto_cd'] );
                $iso_tanto = $this->_getLoginCdFromTantoCd($db, $_iso_tanto_cd);
                $_iso_tanto_cd12 = Msi_Sys_Utils::emptyToNull( $recSeko['iso_tanto_cd12'] );
                $iso_tanto2 = $this->_getLoginCdFromTantoCd($db, $_iso_tanto_cd12);
            } else if ( $iso_kbn == 2 ) { // 2(搬送業者依頼)
                $iso_tanto = Msi_Sys_Utils::emptyToNull( $recSeko['iso_siire_nm'] );
                $iso_tanto2 = null;
            } else {
                $iso_tanto = null;
                $iso_tanto2 = null;
            }

            // 2025/04 NEW #11675  亡くなられた場所区分、 亡くなられた場所
            $sibo_pl_cd = Msi_Sys_Utils::emptyToNull( $recSeko['sibo_basho_kbn'] );
            $sibo_pl_nm = Msi_Sys_Utils::emptyToNull( $recSeko['sibo_basho_place_nm'] );
            // if ( strlen($sibo_pl_nm) <= 0 ) {
            //     $sibo_pl_nm = Msi_Sys_Utils::emptyToNull( $recSeko['sibo_basho_kbn_nm'] );
            // }

            // 2025/04 NEW #11675  警察署
            $keisatsu_nm = Msi_Sys_Utils::emptyToNull( $recSeko['keisatsu_nm'] );

            // 2025/04 NEW #11675  お迎え先予定日
            $pickup_ymd = Msi_Sys_Utils::emptyToNull( $recSeko['pickup_date_ymd'] );

            // 2025/04 NEW #11675  お布施
            $ofuse_prc = Msi_Sys_Utils::emptyToNull( $recSeko['ofuse_prc'] );
        }

        $gojoMembers = $db->easySelect( <<< END_OF_SQL
SELECT sgm.*
  FROM seko_gojokai_member sgm
 WHERE sgm.seko_no = :seko_no
   AND sgm.yoto_kbn = 1 -- 2025/04 #11675 1:葬送儀礼のみ  0450 用途（葬儀） 1:葬送儀礼,4:使用しない,10:祭壇値引き,11:プラン値引き,12:解約充当
   AND sgm.delete_flg = 0
 ORDER BY kain_no, apply_no
END_OF_SQL
                                       , array('seko_no' => $sekoNo) );

        foreach ( range(1, 5) as $cnt ) {
            $rec = array_shift($gojoMembers);
            if ( !$rec ) {
                break;
            }
            $vMem = 'member_id_' . $cnt;
            $vApp = 'apply_no_' . $cnt;
            $$vMem = rtrim($rec['kain_no']);
            $$vApp = $rec['apply_no'];
        }

        $sekoNiteiNoukan = DataMapper_SekoNiteiExCarn::findOne( $db, array('seko_no'=>$sekoNo, 'nitei_kbn'=>3) ); // 3(納棺)
        if ( $sekoNiteiNoukan ) {
            list($_noukan_ymd, $_noukan_str_time, $_noukan_end_time, $_noukan_basyo_nm) = $this->_f_nitei_data($sekoNiteiNoukan);
            if ( $_noukan_ymd && $_noukan_str_time ) {
                $noukan_ts = sprintf("%s%s", $_noukan_ymd, $_noukan_str_time);
            }
            if ( strlen($_noukan_basyo_nm) > 0 ) {
                $noukan_basyo = $_noukan_basyo_nm;
            }
        }

        $rtnVals = compact( Msi_Sys_Utils::strArrayify_qw( <<< END_OF_TXT
hanso_basho1  hanso_basho2
member_id_1   apply_no_1     member_id_2   apply_no_2    member_id_3   apply_no_3
member_id_4   apply_no_4     member_id_5   apply_no_5
noukan_ts     noukan_basyo
tel_tanto
iso_kbn       iso_tanto      iso_tanto2
sibo_pl_cd    sibo_pl_nm     keisatsu_nm   pickup_ymd    ofuse_prc
END_OF_TXT
        ) );

        return $rtnVals;
    }

    /**
     * 担当者CD からログインCDを取得する
     *
     * <AUTHOR> Mihara
     * @since      2025/01/xx
     * @param      Msi_Sys_Db $db
     * @param      string     $tanto_cd
     * @return     string
     */
    protected function _getLoginCdFromTantoCd($db, $tanto_cd)
    {
        if ( strlen($tanto_cd) <= 0 ) {
            return null;
        }
        $loginMst = DataMapper_LoginMst::findOne($db, array('tanto_cd'=>$tanto_cd), false);
        if ( ! $loginMst ) {
            return null;
        }
        $login_cd = substr($loginMst['login_cd'], 0, 6); // 最長6
        return $login_cd;
    }

}
