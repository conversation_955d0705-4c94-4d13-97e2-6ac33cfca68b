var appcst = appcst || {};
var appgjk = appgjk || {}; // 互助会ライブラリ
var appsk = appsk || {};// 請求先ライブラリ
$(function () {
    "use strict";
    /** 申込区分: 1=>葬儀 */
    var MOUSHI_KBN_SOUGI = '1';
    /** 申込区分: 5=>生前依頼 */
    var MOUSHI_KBN_SEIZEN = '5';
    /** 申込区分: 19=>オーダーメイド */
    var MOUSHI_KBN_ORDERMADE = '19';
    /** 申込区分: 99=>サンプル */
    var MOUSHI_KBN_SAMPLE = '99';

    // 画面クラスとモデルのプロパティのオブジェクト
    appcst.pro = {
        // 受付①
        ts_free1_date: '#uketuke_date',
        ts_free1_time: '#uketuke_time',
        tanto_nm1: '#uketuke_danto',
        v_free1: '#uketuke_kojin_name',
        v_free2: '#uketuke_kojin_kana',
        zip_no1: '#u_zip_1',
        addr1_1: '#u_address_1_1',
        tel_no1: '#u_tel_1',
        ts_free5_date: '#sibo_date',
        ts_free5_time: '#sibo_time',
        free1_code_cd: '.cls_sibo_basho .select-container',
        free1_place_nm: '#sibo_basho_name',
        v_free3: '#byouto',
        v_free6: '#byouto_gai',
        ts_free2: '#pickup_date, #pickup_time',
        ts_free2_date: '#pickup_date',
        ts_free2_time: '#pickup_time',
        hs_gyomu_cd2: '.cls_dropoff_type .select-container',
        ts_free3: '#pickup_date2, #pickup_time2',
        ts_free3_date: '#pickup_date2',
        ts_free3_time: '#pickup_time2',
        ts_free4: '#pickup_date3, #pickup_time3',
        ts_free4_date: '#pickup_date3',
        ts_free4_time: '#pickup_time3',
        v_free4: '#renraku_name',
        free2_code_cd: '#renraku_zoku',
        v_free5: '#renraku_tel',
        iso_accept_cd_1: '.cls_iso_knn1 .select-container, #staff_3',
        iso_tanto_cd1: '#staff_3',
        iso_siire_cd1: '#staff_3',
        // 基本タブ
        moushi_cd: '#apply_type',
        sougi_cd: '#funeral_type',
        kaiin_cd: '#member',
        daicho_no_eria: '#code_1',
        daicho_no_mm: '#code_2',
        daicho_no_seq: '#code_3',
        kaiin_sonota: '#member_detail',
        uketuke_tanto_cd: '#staff_1',
        uketuke_tanto_nm: '#staff_1',
        seko_tanto_nm: '#staff_2',
        k_nm: '#input-tab #name',
        k_haigu_cd: '#input-tab #spouse',
        k_knm: '#input-tab #kana',
        k_gengo: '#input-tab #birthday_era',
        k_seinengappi_ymd: '#input-tab #birthday_date',
        k_nenrei_man: '#input-tab #age',
        k_nenrei_kyounen: '#input-tab #age_at_death',
        kg_yubin_no: '#input-tab #zip_1',
        kg_addr1: '#input-tab #address_1_1',
        kg_tel: '#input-tab #tel_1',
        kg_addr2: '#input-tab #address_1_2',
        kg_setai_cd: '#input-tab #head_1',
        kj_yubin_no: '#input-tab #zip_2',
        kj_addr1: '#input-tab #address_2_1',
        kj_tel: '#input-tab #tel_2',
        kj_addr2: '#input-tab #address_2_2',
        kj_setai_cd: '#input-tab #head_2',
        kh_yubin_no: '#input-tab #zip_3',
        kh_addr1: '#input-tab #address_3_1',
        kh_hito_cd: '#input-tab #head_3',
        kh_addr2: '#input-tab #address_3_2',
        kk_kinmusaki_kbn: '#input-tab #employee',
        kk_kinmusaki_nm: '#input-tab #company',
        kk_tel: '#input-tab #company_tel',
        kk_yakusyoku_nm: '#input-tab #position',
        kk_fax: '#input-tab #company_fax',
        souke_nm: '#input-tab #family_name',
        souke_knm: '#input-tab #family_name_kana',
        souke_tel: '#input-tab #family_tel',
        keishiki_cd: '#input-tab #funeral_style',
        syushi_kbn: '#input-tab #syushi_kbn',
        syuha_cd: '#input-tab #syuha_cd',
        jyusho_cd: '#input-tab #temple_cd',
        jyusho_nm: '#input-tab #temple',
        biko1: '#input-tab #memo',
        n_free4: '#input-tab #family_count',
        iso_ymd: '#iso_ts, #iso_time',
        iso_date: '#iso_ts',
        ts_free6: '#iso_ts, #iso_time',
        iso_time: '#iso_time',
        n_free10: '#ofuse_prc',
        n_free5: '#kaiso_count',
        // 日程タブ
        nitei_ymd: '.nitei_date, .nitei_time',
        nitei_date: '.nitei_date',
        nitei_time: '.nitei_time',
        nitei_ed_ymd: '.nitei_date, .nitei_ed_time',
        nitei_ed_time: '.nitei_ed_time',
        basho_nm: '.basho_nm',
        shikijo_shiyou_prc: '#add_charge #total_1',
        tuya_paku_su: '#add_charge #nights',
        tuya_paku_su2: '#add_charge #nights2',
        tuya_shikijo_tanka: '#add_charge #price_2',
        tuya_shikijo_tanka2: '#add_charge #price_3',
        tuya_shikijo_prc1: '#add_charge #total_2',
        tuya_shikijo_prc2: '#add_charge #total_3',
        n_free1: '#add_charge #add_unei',
        // 喪主タブ
        m_nm: '#infochief-tab #chief #name',
        m_knm: '#infochief-tab #chief #kana',
        m_gengo: '#infochief-tab #chief #birthday_era',
        m_seinengappi_ymd: '#infochief-tab #chief #birthday_date',
        mg_yubin_no: '#infochief-tab #chief #zip_1',
        mg_addr1: '#infochief-tab #chief #address_1_1',
        mg_tel: '#infochief-tab #chief #tel_1',
        mg_m_tel: '#infochief-tab #chief #mobile_tel_1',
        mg_addr2: '#infochief-tab #chief #address_1_2',
        mj_yubin_no: '#infochief-tab #chief #zip_2',
        mj_addr1: '#infochief-tab #chief #address_2_1',
        mj_tel: '#infochief-tab #chief #tel_2',
        mj_addr2: '#infochief-tab #chief #address_2_2',
        mh_yubin_no: '#infochief-tab #chief #zip_3',
        mh_addr1: '#infochief-tab #chief #address_3_1',
        mh_addr2: '#infochief-tab #chief #address_3_2',
        mk_kinmusaki_kbn: '#infochief-tab #chief #employee',
        mk_kinmusaki_nm: '#infochief-tab #chief #company',
        mk_tel: '#infochief-tab #chief #company_tel',
        mk_yakusyoku_nm: '#infochief-tab #chief #position',
        mk_fax: '#infochief-tab #chief #company_fax',
        // 喪主タブ 請求先情報
        sekyu_nm: '#infochief-tab #bill #name',
        sekyu_knm: '#infochief-tab #bill #kana',
        sekyu_moshu_kankei: '#bill_relationship_name',
        sekyu_yubin_no: '#zip_4',
        sekyu_addr1: '#address_4_1',
        sekyu_tel: '#tel_4',
        mobile_tel: '#mobile_tel_2',
        sekyu_addr2: '#address_4_2',
        sekyu_biko1: '#memo',
        // 互助会確認タブ
        kain_no: '.i_member_id',
        apply_no: '.i_apply_no',
        course_snm_cd: '.i_cose .select-container',
        kanyu_nm: '.i_member_name',
        yoto_kbn: '.i_usage .select-container',
        keiyaku_gaku: '.i_deposit',
        plan_convert_gaku: '.i_plan_convert',
        harai_gaku: '.i_pay',
        harai_no: '.i_times',
        zankin: '.zankin',
        wari_gaku: '.i_discount',
        cose_chg_gaku: '.i_balance',
        early_use_cost: '.i_early',
        kanyu_dt_gen: '.i_entry_era .select-container',
        kanyu_dt: '.i_entry',
        zei_kijyn_ymd: '.i_tax',
        kanyu_dantai_ext: '#other_entry_name',
        riyu_memo: '#change_reason',
        plan_use_prc: '#plan_use_prc',
        plan_change_prc: '#plan_change_prc',
        // その他タブ
        sd_yotei_ymd: '#date,#time',
        sd_yotei_date: '#date',
        tk_cyonaikai_nm: '#association',
        tk_kumicyo_nm: '#leader',
        hs_anchi_nm: '#dropoff_name',
        kasoba_nm: '#crematorium',
        az_gojokai_nm: '#membership',
        mt_area: '#catalog_area',
        mt_item: '#catalog_item',
        mt_hannyu_start: '#catalog_date,#catalog_time_from,#catalog_time_to',
        mt_hannyu_end: '#catalog_date,#catalog_time_from,#catalog_time_to',
        mt_hannyu_nm: '#catalog_place',
        tk_house_cnt: '#doors',
        tk_person_cnt: '#persons',
        sodan_kbn: '.cls_jizen_soudan .select-container',
        // 貸出備品タブ
        'kaisyu_yotei_ymd': '.return'
    };

    // 勤務先select2内容
    var _employee = [{id: '1', text: '元'}, {id: '2', text: '現'}];
    // 司式者人数select2内容
    var _shishikisha_ninzu = [
        {id: '0', text: '無'},
        {id: '1', text: '1名'},
        {id: '2', text: '2名'},
        {id: '3', text: '3名'},
        {id: '4', text: '4名'},
        {id: '5', text: '5名'},
        {id: '6', text: '6名'},
        {id: '7', text: '7名'},
        {id: '8', text: '8名'},
        {id: '9', text: '9名'},
        {id: '10', text: '10名'}
    ];
    // 診断書手続きselect2内容
    var _steps = [
        {id: '1', text: '葬家'},
        {id: '2', text: '当社'}
    ];
    // 死亡診断書select2内容
    var _certificate = [
        {id: '1', text: '1部'},
        {id: '2', text: '2部'},
        {id: '3', text: '3部'},
        {id: '4', text: '4部'},
        {id: '5', text: '5部'}
    ];
    // 隣組隣組長様select2内容
    var _doors = [
        {id: '1', text: '1軒'},
        {id: '2', text: '2軒'},
        {id: '3', text: '3軒'},
        {id: '4', text: '4軒'},
        {id: '5', text: '5軒'}
    ];
    var _persons = [
        {id: '1', text: '1名'},
        {id: '2', text: '2名'},
        {id: '3', text: '3名'},
        {id: '4', text: '4名'},
        {id: '5', text: '5名'}
    ];
    // 御写真select2内容
    var _portrait = [
        {id: '1', text: '済'},
        {id: '0', text: '未'}
    ];
    // 診断書コピーselect2内容
    var _copys = _portrait;
    /**
     * validation valid時処理
     * @param {View} view
     * @param {string} attr
     */
    var _valid = function (view, attr) {
        var $el = view.$(appcst.pro[attr]);
        if ($el.length) {
            $el.removeClass('error1');
            $el.attr('title', '');
        }
    };

    /**
     * validation invalid時処理
     * @param {View} view
     * @param {string} attr
     * @param {string} error
     */
    var _invalid = function (view, attr, error) {
        var $el = view.$(appcst.pro[attr]);
        if ($el.length) {
            $el.addClass('error1');
            $el.attr('title', error);
        }
    };

    /**
     * 日付と時刻を結合してタイムスタンプに設定する処理
     * @param {string} niteiDate 日付
     * @param {string} niteiTime 時刻
     * @param {string} pro モデルの属性
     * @param {model} model モデル
     */
    var _setNiteiymd = function (niteiDate, niteiTime, pro, model) {
        var niteiymd = null;
        if (!$.msiJqlib.isNullEx2(niteiDate)) {
            niteiymd = niteiDate;
        }
        if (!$.msiJqlib.isNullEx2(niteiTime)) {
            if (!$.msiJqlib.isNullEx2(niteiymd)) {
                niteiymd += " ";
            }
            niteiymd += niteiTime;
        }
        model.set(pro, niteiymd);
    };
    /**
     * 日付チェック処理
     * @param {string} value 日付
     */
    var _chkYmd = function (value) {
        if (!$.msiJqlib.isNullEx2(value) && !$.msiJqlib.chkDate(value)) {
            return Backbone.Validation.messages.ymd;
        }
    };

    // select2のvalを設定する
    var _setSelect2Val = function ($el, val) {
        $el.select2("val", val);
    };
    // select2のdataを取得する
    var _getSelect2Data = function ($el) {
        return $el.select2("data");
    };

    // 区分値コード数値設定処理
    var _setKbnCdVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_cd_num);
        }
    };
    // 和暦の生年月日チェック処理
    var _validateSeinengappi = function (gengo, value) {
        if ($.msiJqlib.isNullEx2(value) || $.msiJqlib.isNullEx2(gengo)) {
            return '生年月日は必須項目です';
        }
        var seinengappi = $.msiJqlib.warekiToseireki(gengo, value);
        if (!seinengappi) {
            return '生年月日の形式エラーです';
        }
    };
    // 赤字クラスの追加削除処理
    appcst.toggleAkajiClass = function (that, targets) {
        _.each(targets, function (val) {
            var target = that.model.get(val);
            if (!$.msiJqlib.isNullEx2(target) && target < 0) {
                that.$(appcst.pro[val]).addClass('com-akaji');
            } else {
                that.$(appcst.pro[val]).removeClass('com-akaji');
            }
        });
    };
    // 場所区分によるpickup入力フィールドの活性・非活性
    var _setSpotStatus = function (val, $elPickNm, $elPickDlg, disable) {
        $elPickNm.attr("readonly", "readonly");
        // 未設定または自宅
        if ($.msiJqlib.isNullEx2(val) || val === "0") { // 0:自宅
            $elPickNm.attr("disabled", "disabled");
            $elPickDlg.addClass("disabled");
        } else if (val === "9" || disable) {
            $elPickNm.removeAttr("readonly");
            $elPickNm.removeAttr("disabled");
            $elPickDlg.addClass("disabled");
        } else {
            $elPickNm.removeAttr("disabled");
            $elPickDlg.removeClass("disabled");
        }
    };
    // 場所区分によるpickup入力フィールドの活性・非活性
    var _setIsoStatus = function (val, $elPickNm, $elPickDlg) {
        $elPickNm.attr("readonly", "readonly");
        // 未設定または他社、無
        if ($.msiJqlib.isNullEx2(val) || val === "3" || val === "9") {
            $elPickNm.attr("disabled", "disabled");
            $elPickDlg.addClass("disabled");
        } else {
            $elPickNm.removeAttr("disabled");
            $elPickDlg.removeClass("disabled");
        }
    };
    // 場所区分によるpickup入力フィールドの活性・非活性
    var _setIsoStatus2 = function (val, $elPickNm, $elPickDlg) {
        $elPickNm.attr("readonly", "readonly");
        // 未設定または他社、無
        if ($.msiJqlib.isNullEx2(val) || val === "3" || val === "9" || val === "2") {
            $elPickNm.attr("disabled", "disabled");
            $elPickDlg.addClass("disabled");
        } else {
            $elPickNm.removeAttr("disabled");
            $elPickDlg.removeClass("disabled");
        }
    };
    /**
     * 日付と時刻を結合してタイムスタンプに設定する処理（移送時間用）
     * @param {string} niteiDate 日付
     * @param {string} niteiTime 時刻
     * @param {string} pro モデルの属性
     * @param {model} model モデル
     */
    var _setNiteiEnd = function (niteiDate, niteiTime, pro, model) {
        var niteiymd = null;
        if (!$.msiJqlib.isNullEx2(niteiDate)) {
            niteiymd = niteiDate;
        }
        if (!$.msiJqlib.isNullEx2(niteiTime)) {
            if (!$.msiJqlib.isNullEx2(niteiymd)) {
                niteiymd += " ";
            }
            niteiymd += niteiTime;
        } else {
            niteiymd = null;
        }
        model.set(pro, niteiymd);
    };
    appcst.setSpotStatus = _setSpotStatus;
    appcst.setYmd = _setNiteiymd;
    /**
     * obejct型→配列に変換する関数
     * keyはソート順
     * @param {object} dataObj 区分データ
     */
    var _objToArray = function (dataObj) {
        var dataArr = [];
        var keys = _.keys(dataObj);
        _.each(keys, function (item) {
            var objData = {};
            var items = dataObj[item];
            var i_keys = _.keys(items);
            if (i_keys.length > 1) {
                objData.id = dataObj[item][i_keys[0]];
                objData.text = dataObj[item][i_keys[1]];
            }
            for (var i = 2; i < i_keys.length; i++) {
                objData[i_keys[i]] = dataObj[item][i_keys[i]];
            }
            dataArr.push(objData);
        });
        return dataArr;
    };
    // ラジオボックスバインディング共通処理
    var _getRadioBinding = function (attr) {
        var binding = {
            observe: attr,
            update: function ($el, val) {
                if (!$.msiJqlib.isNullEx2(val)) {
                    _setAttrChecked($el.eq(val));
                }
            }
        };
        return binding;
    };
    // プロパティ(ラジオボックス)を設定する
    var _setAttrChecked = function ($el) {
        $el.attr("checked", "checked").button("refresh");
        $el.addClass("onCheck");
    };
    /**
     * @description 画面全体処理
     */
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                // 基本タブ
                bumon_cd: null, // 売上部門コード
                seko_no: null, // 施行番号
                moushi_cd: "1", // 申込コード
                moushi_kbn: "1", // 申込区分
                sougi_cd: "1", // 葬儀コード
                sougi_kbn: "1", // 葬儀区分
                daicho_no_eria: null, // 台帳番号1
                daicho_no_mm: null, // 台帳番号2
                daicho_no_seq: null, // 台帳番号3
                p_info_cd: null, // 個人情報保護コード
                p_info_kbn: null, // 個人情報保護区分
                kaiin_cd: "2", // 会員コード
                kaiin_kbn: "2", // 会員区分
                free2_cd: null, // カーニバル番号
                kaiin_sonota: null, // 会員区分その他
                uketuke_tanto_cd: null, // 受付担当者コード
                uketuke_tanto_nm: null, // 受付担当者名
                seko_tanto_cd: null, // 施行担当者コード
                seko_tanto_nm: null, // 施行担当者名
                k_nm: null, // 故人名
                k_file_nm: null, // 添付ファイル名OID
                k_file: null, // 添付ファイルOID一時
                k_sex_cd: "1", // 性別コード
                k_sex_kbn: "1", // 性別区分
                k_haigu_cd: null, // 配偶者コード
                k_haigu_kbn: null, // 配偶者区分
                k_knm: null, // 故人カナ名
                k_gengo: "S", // 生年月日元号
                k_seinengappi_ymd: null, // 生年月日
                k_nenrei_man: null, // 故人年齢
                k_nenrei_kyounen: null, // 享年
                kg_yubin_no: null, // 現住所郵便番号
                kg_addr1: null, // 現住所1
                kg_tel: null, // 現住所TEL
                kg_addr2: null, // 現住所2
                kg_setai_cd: "1", // 世帯主コード
                kg_setai_kbn: "1", // 世帯主区分
                kj_kbn: "0", // 住民登録住所の現住所に同じチェックボックス
                kj_yubin_no: null, // 住民登録住所郵便番号
                kj_addr1: null, // 住民登録住所1
                kj_tel: null, // 住民登録住所TEL
                kj_addr2: null, // 住民登録住所2
                kj_setai_cd: "1", // 住民登録住所世帯主コード
                kj_setai_kbn: "1", // 住民登録住所世帯主区分
                kh_kbn: "0", // 本籍の現住所に同じチェックボックス
                kh_yubin_no: null, // 本籍郵便番号
                kh_addr1: null, // 本籍住所1
                kh_hito_cd: "1", // 筆頭者コード
                kh_hito_kbn: "1", // 筆頭者区分
                kh_addr2: null, // 本籍住所2
                kk_kinmusaki_kbn: null, // 勤務先
                kk_kinmusaki_nm: null, // 勤務先名
                kk_tel: null, // 勤務先TEL
                kk_yakusyoku_nm: null, // 役職／職種
                kk_fax: null, // 勤務先FAX
                souke_nm: null, // 葬家
                souke_knm: null, // 葬家カナ
                souke_addr_cd: null, // 葬家住所コード
                souke_addr_kbn: null, // 葬家住所区分
                souke_tel: null, // 葬家TEL
                keishiki_cd: "1", // 葬儀形式コード
                keishiki_kbn: "1", // 葬儀形式区分
                syushi_cd: null, // 宗旨コード
                syushi_kbn: null, // 宗旨区分
                syuha_cd: null, // 宗派コード
                syuha_kbn: null, // 宗派区分
                syuha_nm: null, // 宗派名
                syuha_knm: null, // 宗派名カナ
                jyusho_cd: null, // 寺院コード
                jyusho_nm: null, // 寺院名
                jyusho_knm: null, // 寺院カナ名
                free1_code_cd: null, // 寺紹介者コード
                free1_kbn: null, // 寺紹介者区分
                biko1: null, // メモ（出棺経路・納骨・壇払など）
                n_free4: null, // 親族人数
                iso_ymd: null, // 移送日時
                iso_date: null, // 移送日時(日付のみ)
                ts_free6: null, // 移送日時（時間のみ用）
                iso_time: null, // 移送日時(時間のみ)
                v_free12: '御布施', // 御布施
                n_free10: null, // 御布施金額
                n_free5: null, // 会葬者人数
                // 喪主タブ
                m_nm: null, // 喪主名
                m_knm: null, // 喪主名カナ
                m_file_nm: null, // 喪主添付ファイル
                m_file: null, // 喪主添付ファイルOID一時
                m_zoku_cd: null, // 喪主続柄コード
                m_zoku_kbn: null, // 喪主続柄区分
                m_zoku_cd2: null, // 喪主続柄コード
                m_zoku_kbn2: null, // 喪主続柄区分
                m_gengo: "S", // 喪主生年月日元号
                m_seinengappi_ymd: null, // 喪主生年月日
                m_nenrei_man: null, // 喪主年齢
                mg_kbn: "0", // 故人に同じチェックボックス
                mg_yubin_no: null, // 喪主現住所郵便番号
                mg_addr1: null, // 喪主現住所1
                mg_tel: null, // 喪主現住所TEL
                mg_m_tel: null, // 喪主携帯
                mg_addr2: null, // 喪主現住所2
                mj_kbn: "0", // 住民登録住所の故人に同じチェックボックス
                mj_yubin_no: null, // 喪主住民登録住所郵便番号
                mj_addr1: null, // 喪主住民登録住所1
                mj_tel: null, // 喪主住民登録住所TEL
                mj_addr2: null, // 喪主住民登録住所2
                mh_kbn: "0", // 本籍の故人に同じチェックボックス
                mh_yubin_no: null, // 喪主本籍住所郵便番号
                mh_addr1: null, // 喪主本籍住所1
                mh_addr2: null, // 喪主本籍住所2
                mk_kinmusaki_kbn: null, // 喪主勤務先
                mk_kinmusaki_nm: null, // 喪主勤務先名
                mk_tel: null, // 喪主勤務先TEL
                mk_yakusyoku_nm: null, // 喪主役職／職種
                mk_fax: null, // 喪主勤務先FAX
                sekyu_kbn: "0", // 請求先の喪主に同じチェックボックス
                // その他タブ
                sd_hakko_kbn: "0", // 診断書発行区分
                sd_step_kbn: null, // 診断書手続き区分
                sd_yotei_ymd: null, // 診断書発行予定時刻
                sd_yotei_date: null, // 診断書発行予定時刻(日付のみ)
                sd_yotei_time: null, // 診断書発行予定時刻(時刻のみ)
                sd_copy_cnt: null, // 診断書コピー枚数
                tk_cyonaikai_nm: null, // 町内会名
                tk_kumicyo_nm: null, // 隣組長
                chiku_cd: null, // 地区
                kumi_cd: null, // 組
                tk_house_cnt: null, // 軒数
                tk_person_cnt: null, // 人数
                hs_kbn: "0", // 搬送
                iso_accept_cd_1: null, // 移送者区分コード1
                iso_accept_kbn_1: null, // 移送者区分1
                iso_tanto_cd1: null, // 移送者コード1
                iso_tanto_nm1: null, // 移送者名1
                iso_tanto_cd1_2: null, // 移送者コード3
                iso_tanto_nm1_2: null, // 移送者名3
                iso_siire_cd1: null, // 移送仕入先コード1
                iso_accept_cd_2: null, // 移送者区分コード2
                iso_accept_kbn_2: null, // 移送者区分2
                iso_tanto_cd2: null, // 移送者コード2
                iso_tanto_nm2: null, // 移送者名2
                iso_tanto_cd2_2: null, // 移送者コード4
                iso_tanto_nm2_2: null, // 移送者名4
                iso_siire_cd2: null, // 移送仕入先コード2
                iso_accept_cd_3: null, // 移送者区分コード3
                iso_accept_kbn_3: null, // 移送者区分3
                iso_tanto_cd3: null, // 移送者コード5
                iso_tanto_nm3: null, // 移送者名5
                iso_tanto_cd3_2: null, // 移送者コード6
                iso_tanto_nm3_2: null, // 移送者名6
                iso_siire_cd3: null, // 移送仕入先コード3
                iso_accept_cd_4: null, // 移送者区分コード4
                iso_accept_kbn_4: null, // 移送者区分4
                iso_tanto_cd4: null, // 移送者コード7
                iso_tanto_nm4: null, // 移送者名7
                iso_tanto_cd4_2: null, // 移送者コード8
                iso_tanto_nm4_2: null, // 移送者名8
                iso_siire_cd4: null, // 移送仕入先コード4
                iso_accept_cd_5: null, // 移送者区分コード5
                iso_accept_kbn_5: null, // 移送者区分5
                iso_tanto_cd5: null, // 移送者コード9
                iso_tanto_nm5: null, // 移送者名9
                iso_tanto_cd5_2: null, // 移送者コード10
                iso_tanto_nm5_2: null, // 移送者名10
                iso_siire_cd5: null, // 移送仕入先コード5
                hs_gyomu_cd: null, // 搬送業務コード(お伺い先1のselect2)
                hs_gyomu_kbn: null, // 搬送業務区分(お伺い先1のselect2)
                hs_spot_cd: null, // お伺い先コード
                hs_spot_kbn: null, // お伺い先区分
                hs_spot_nm: null, // お伺い先
                hs_gyomu_cd_2: null, // 搬送業務コード(お伺い先2のselect2)
                hs_gyomu_kbn_2: null, // 搬送業務区分(お伺い先2のselect2)
                hs_spot_cd_2: null, // お伺い先2コード
                hs_spot_kbn_2: null, // お伺い先2区分
                hs_spot_nm_2: null, // お伺い先2
                hs_gyomu_cd2: null, // 搬送業務コード2(安置先のselect2)
                hs_gyomu_kbn2: null, // 搬送業務区分2(安置先のselect2)
                hs_anchi_cd: null, // 安置先コード
                hs_anchi_kbn: null, // 安置先コード
                hs_anchi_nm: null, // 安置先
                kasoba_cd: null, // 火葬場コード
                kasoba_nm: null, // 火葬場名
                az_death_cnt: null, // 死亡診断書枚数
                az_inkan_kbn: "0", // 印鑑
                az_photo_cnt: null, // 御写真枚数
                az_gojokai_nm: null, // 互助会証書名称など
                mt_irai_kbn: "0", // 搬送
                mt_area: null, // 対象エリア
                mt_hannyu_start: null, // 搬入開始日時
                mt_hannyu_end: null, // 搬入終了日時
                mt_hannyu_ymd: null, // 搬入日
                mt_hannyu_start_time: null, // 搬入開始時刻
                mt_hannyu_end_time: null, // 搬入終了時刻
                mt_hannyu_cd: null, // 搬入場所コード
                hs_delivery_cd: null, // 納品場所コード(搬入場所のselect2)
                hs_delivery_kbn: null, // 納品場所区分(搬入場所のselect2)
                mt_hannyu_nm: null, // 搬入場所名
                mt_memo_cd: null, // 目録手配備考コード
                mt_memo_kbn: null, // 目録手配備考区分
                mt_memo_detail: null, // 目録手配備考
                sd_shohin_cd: null, // 寝台車商品コード
                shindaisya_prc: null, // 寝台車金額
                sk_shohin_cd: null, // 式場商品コード
                sk_kaijyo_cd: null, // 式場場所コード
                sk_kaijyo_nm: null, // 式場場所名称
                shikijo_shiyou_prc: null, // 式場使用料
                ty_shohin_cd: null, // 通夜会場商品コード
                ty_kaijyo_cd: null, // 通夜会場場所コード
                ty_kaijyo_nm: null, // 通夜会場場所名称
                tuya_paku_su: null, // 通夜会場使用泊数
                tuya_shikijo_tanka: null, // 通夜会場使用単価
                tuya_paku_su2: null, // 通夜会場使用泊数2
                tuya_shikijo_tanka2: null, // 通夜会場使用単価2
                tuya_shikijo_prc1: null, // 通夜会場使用料金額1
                tuya_shikijo_prc2: null, // 通夜会場使用料金額2
                tuya_shikijo_prc: null, // 通夜会場使用料金額
                n_free1: null, // 施行運営費
                free3_code_cd: '1', // 会場利用コード
                free3_kbn: '1', // 会場利用区分
                free5_code_cd: null, // 各方面よりお問合せのあった場合、お答えして良い項目コード
                free5_kbn: null, // 各方面よりお問合せのあった場合、お答えして良い項目区分
                free6_code_cd: null, // 各方面よりお供物(生花等)のご注文があった場合、承りはコード
                free6_kbn: null, // 各方面よりお供物(生花等)のご注文があった場合、承りは区分
                v_free5: null, // 条件付で可の条件
                v_free6: null, // 特記事項
                free4_kbn: null // ペースメーカー有無
            };
        },
        validation: {
            iso_accept_cd_1: {
                required: false
            },
            iso_tanto_cd1: {
                required: function () {
                    return false;
//                    if (this.get("iso_accept_kbn_1") === "1" || this.get("iso_accept_kbn_1") === "2") {
//                        return true;
//                    } else {
//                        return false;
//                    }
                }
            },
            moushi_cd: {
                required: true
            },
            sougi_cd: {
                required: function () {
                    return true;
                }
            },
            daicho_no_eria: {
                required: false,
                pattern: 'digits',
                //range: [0, 99]
                length: 2
            },
            daicho_no_mm: {
                required: false,
                pattern: 'digits',
                //range: [0, 99]
                length: 2
            },
            daicho_no_seq: {
                required: false,
                pattern: 'digits',
                maxLength: 10
            },
            kaiin_cd: {
                required: true
            },
            k_nm: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            uketuke_tanto_cd: {
                required: true
            },
//            uketuke_tanto_nm: {
//                required: true,
//                msg: '受付担当者は必須入力です'
//            },
            kaiin_sonota: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 20
            },
            k_knm: {
                required: false,
                //pattern: 'kana',
                maxLength: 30
            },
            k_gengo: {
                required: true
            },
            k_seinengappi_ymd: "validateSeinengappi",
            k_nenrei_kyounen: {
                required: false,
                pattern: 'digits'
            },
            kg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            kj_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            kh_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            kg_tel: {
                required: false,
                pattern: 'tel'
            },
            kj_tel: {
                required: false,
                pattern: 'tel'
            },
            kk_tel: {
                required: false,
                pattern: 'tel'
            },
            kg_addr1: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kg_addr2: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kj_addr1: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kj_addr2: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kh_addr1: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kh_addr2: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kk_kinmusaki_nm: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kk_yakusyoku_nm: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kk_fax: {
                required: false,
                pattern: 'tel'
            },
            souke_nm: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 10
            },
            souke_knm: {
                required: false,
                maxLength: 10
            },
            souke_tel: {
                required: false,
                pattern: 'tel'
            },
            syuha_nm: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            jyusho_nm: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            biko1: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 256
            },
            n_free4: {
                required: false,
                pattern: 'digits'
            },
//            iso_ymd: {
//                required: false,
//                pattern: 'timestamp',
//                msg: '日付と時刻の整合性がありません'
//            },
            iso_date: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
            iso_time: {
                required: false,
                pattern: 'time'
            },
            m_nm: {
                required: false,
                maxLength: 30
            },
            m_knm: {
                required: false,
                maxLength: 30
            },
            m_gengo: {
                required: true
            },
            m_seinengappi_ymd: "validateSeinengappiM",
            mg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            mg_tel: {
                required: false,
                pattern: 'tel'
            },
            mg_m_tel: {
                required: false,
                pattern: 'tel'
            },
            mg_addr1: {
                required: false,
                maxLength: 30
            },
            mg_addr2: {
                required: false,
                maxLength: 30
            },
            mj_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            mj_tel: {
                required: false,
                pattern: 'tel'
            },
            mj_addr1: {
                required: false,
                maxLength: 30
            },
            mj_addr2: {
                required: false,
                maxLength: 30
            },
            mh_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            mh_addr1: {
                required: false,
                maxLength: 30
            },
            mh_addr2: {
                required: false,
                maxLength: 30
            },
            mk_kinmusaki_nm: {
                required: false,
                maxLength: 30
            },
            mk_tel: {
                required: false,
                pattern: 'tel'
            },
            mk_yakusyoku_nm: {
                required: false,
                maxLength: 30
            },
            mk_fax: {
                required: false,
                pattern: 'tel'
            },
            sd_yotei_ymd: {
                required: false,
                pattern: 'timestamp',
                msg: '日付と時刻の整合性がありません'
            },
            sd_yotei_date: function (value) {
                return _chkYmd(value);
            },
            sd_yotei_time: {
                required: false,
                pattern: 'time'
            },
            tk_cyonaikai_nm: {
                required: false,
                maxLength: 30
            },
            tk_kumicyo_nm: {
                required: false,
                maxLength: 30
            },
            hs_gyomu_cd2: {
                required: false
            },
            hs_anchi_nm: {
//                required: function () {
//                    return this.get("hs_gyomu_cd2") !== '00';
//                },
                required: false,
                maxLength: 30
            },
            hs_spot_nm: {
                required: false,
                maxLength: 30
            },
            hs_spot_nm2: {
                required: false,
                maxLength: 30
            },
            kasoba_nm: {
                required: false,
                maxLength: 30
            },
            az_gojokai_nm: {
                required: false,
                maxLength: 30
            },
            mt_area: {
                required: false
            },
            tk_house_cnt: {
                required: false,
                maxLength: 2,
                pattern: 'digits'
            },
            tk_person_cnt: {
                required: false,
                maxLength: 2,
                pattern: 'digits'
            },
            mt_hannyu_start: {
                required: false,
                pattern: 'timestamp',
                msg: '日付と時刻の整合性がありません'
            },
            mt_hannyu_end: {
                required: false,
                pattern: 'timestamp',
                greaterThanOrNull: 'mt_hannyu_start',
                msg: '日付と時刻の整合性がありません'
            },
            mt_hannyu_nm: {
                required: false,
                maxLength: 30
            },
            shikijo_shiyou_prc: {
                required: false,
                pattern: 'number'
            },
            tuya_paku_su: {
                required: false,
                pattern: 'number'
            },
            tuya_shikijo_tanka: {
                required: false,
                pattern: 'number'
            },
            tuya_paku_su2: {
                required: false,
                pattern: 'number'
            },
            tuya_shikijo_tanka2: {
                required: false,
                pattern: 'number'
            },
            n_free1: {
                required: false,
                pattern: 'number'
            },
            n_free10: {
                required: false,
                pattern: 'digits'
            },
            ts_free6: function (val, attr, computed) {
                if ($.msiJqlib.isNullEx2(computed.iso_date) && !$.msiJqlib.isNullEx2(computed.iso_time)) {
                    return '日付と時刻の整合性がありません';
                }
            },
            n_free5: {
                required: false,
                pattern: 'digits'
            }
        },
        labels: {
            iso_accept_cd_1: '搬送者1区分',
            iso_tanto_cd1: '搬送者1',
            iso_siire_cd1: '搬送者1',
            hs_gyomu_cd2: '安置先区分',
            hs_anchi_nm: '安置先名',
            moushi_cd: '申込区分',
            kaiin_cd: '会員区分',
            sougi_cd: '葬儀区分',
            daicho_no_eria: '台帳番号（エリア）',
            daicho_no_mm: '台帳番号（月）',
            daicho_no_seq: '台帳番号（連番）',
            kaiin_sonota: '会員区分（その他）',
            uketuke_tanto_cd: '受付担当者',
            k_nm: '故人名',
            k_knm: '故人カナ名',
            k_gengo: '生年月日元号',
            k_nenrei_kyounen: '享年',
            kg_addr1: '現住所1',
            kg_addr2: '現住所2',
            kg_tel: '現住所TEL',
            kj_addr1: '住民登録住所1',
            kj_addr2: '住民登録住所2',
            kh_addr1: '本籍住所1',
            kh_addr2: '本籍住所2',
            kk_kinmusaki_nm: '勤務先名',
            kk_yakusyoku_nm: '役職／職種',
            souke_nm: '葬家',
            souke_knm: '葬家カナ',
            syuha_nm: '宗旨・宗派',
            jyusho_nm: '寺院名',
            biko1: 'メモ',
            n_free4: '親族人数',
            m_nm: '喪主名',
            m_knm: '喪主名カナ',
            m_gengo: '喪主生年月日元号',
            mg_yubin_no: '喪主現住所郵便番号',
            mg_addr1: '喪主現住所1',
            mg_addr2: '喪主現住所2',
            mg_tel: '喪主現住所TEL',
            mg_m_tel: '喪主携帯番号',
            mj_yubin_no: '喪主住民登録住所郵便番号',
            mj_addr1: '喪主住民登録住所1',
            mj_addr2: '喪主住民登録住所2',
            mj_tel: '喪主住民登録住所TEL',
            mh_yubin_no: '喪主本籍住所郵便番号',
            mh_addr1: '喪主本籍住所1',
            mh_addr2: '喪主本籍住所2',
            mk_kinmusaki_nm: '喪主勤務先名',
            mk_tel: '喪主勤務先TEL',
            mk_yakusyoku_nm: '喪主役職／職種',
            mk_fax: '喪主勤務先FAX',
            tk_cyonaikai_nm: '町内会名',
            tk_kumicyo_nm: '隣組長',
            tk_house_cnt: '隣組',
            tk_person_cnt: '軒数',
            hs_spot_nm: 'お伺い先1',
            hs_spot_nm_2: 'お伺い先2',
            kasoba_nm: '火葬場',
            az_gojokai_nm: '互助会証明書',
            mt_area: '対象エリア',
            mt_hannyu_start: '搬入日時',
            mt_hannyu_end: '搬入日時',
            mt_hannyu_nm: '搬入場所',
            shikijo_shiyou_prc: '式場使用料',
            tuya_paku_su: '通夜会場使用泊数',
            tuya_shikijo_tanka: '通夜会場使用単価',
            tuya_paku_su2: '通夜会場使用泊数2',
            tuya_shikijo_tanka2: '通夜会場使用単価2',
            n_free1: '施行運営費',
            n_free10: '御布施',
            n_free5: '会葬者'
        },
        validateSeinengappi: function (value, attr, computedState) {
            var gengo = computedState.k_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return _validateSeinengappi(gengo, value);
            }
        },
        validateSeinengappiM: function (value, attr, computedState) {
            var gengo = computedState.m_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return _validateSeinengappi(gengo, value);
            }
        }
    }); // AppModel

    var AppViewDef = {
        el: $("#wrapper"),
        events: {
            "click .tab li a": "changeTab",
            "click #staff_1, .label.dlg_staff1": "uketukeHelper",
            "click #staff_2, .label.dlg_staff2": "sekoHelper",
            "click #staff_3, .label.dlg_staff3": "isoHelper",
            "click #staff_4, .label.dlg_staff4": "isoHelper2",
            "click #staff_3_2, .label.dlg_staff3_2": "isoHelper3",
            "click #staff_4_2, .label.dlg_staff4_2": "isoHelper4",
            "click #staff_5, .label.dlg_staff5": "isoHelper5",
            "click #staff_5_2, .label.dlg_staff5_2": "isoHelper6",
            "click #staff_6, .label.dlg_staff6": "isoHelper7",
            "click #staff_6_2, .label.dlg_staff6_2": "isoHelper8",
            "click #staff_7, .label.dlg_staff7": "isoHelper9",
            "click #staff_7_2, .label.dlg_staff7_2": "isoHelper10",
            "click #btn_save": "doSave",
            "click #btn_print": "doPrint",
            "click #btn_print_seko": "doSekoInfoPrint",
            "click #btn_delete": "doDelete",
            "click #btn_cancel": "doCancel",
            "click #btn_seko_copy": "doSekoCopy",
            "click #btn_seko_copy2": "doSeko2SampleCopy",
            "click #btn_seko_copy3": "doSeko3SeizenCopy",
            "click #btn_seko_copy4": "doSekoCopy4",
            "click #inforental-tab #item_add": "itemHelper",
            "click #detail .label.dlg_zip": "zipHelper",
            "click #detail #temple, .label.dlg_temple": "nmjyushoHelper",
            "click #detail #crematorium, .label.dlg_crematorium": "nmjyushoHelper",
//            "click #detail #catalog_place, #detail .dlg_catalog_place": "hannyuHelper",
            "click #infodate-tab #add_place_1, #infodate-tab .label.add_place_1": "kaijyosikiHelper",
            "click #infodate-tab #add_place_2, #infodate-tab .label.add_place_2": "kaijyotuyaHelper",
            "change #detail #pickup_type": function () {
                this.model.set({'hs_spot_kbn': null, 'hs_spot_cd': null, 'hs_spot_nm': null});
            },
            "change #detail #pickup_type2": function () {
                this.model.set({'hs_spot_kbn_2': null, 'hs_spot_cd_2': null, 'hs_spot_nm_2': null});
            },
            "change #detail #iso_kbn1": function () {
                this.model.set({'iso_tanto_cd1': null, 'iso_tanto_nm1': null});
                this.model.set({'iso_tanto_cd1_2': null, 'iso_tanto_nm1_2': null});
            },
            "change #detail #iso_kbn2": function () {
                this.model.set({'iso_tanto_cd2': null, 'iso_tanto_nm2': null});
                this.model.set({'iso_tanto_cd2_2': null, 'iso_tanto_nm2_2': null});
            },
            "change #detail #iso_kbn3": function () {
                this.model.set({'iso_tanto_cd3': null, 'iso_tanto_nm3': null});
                this.model.set({'iso_tanto_cd3_2': null, 'iso_tanto_nm3_2': null});
            },
            "change #detail #iso_kbn4": function () {
                this.model.set({'iso_tanto_cd4': null, 'iso_tanto_nm4': null});
                this.model.set({'iso_tanto_cd4_2': null, 'iso_tanto_nm4_2': null});
            },
            "change #detail #iso_kbn5": function () {
                this.model.set({'iso_tanto_cd5': null, 'iso_tanto_nm5': null});
                this.model.set({'iso_tanto_cd5_2': null, 'iso_tanto_nm5_2': null});
            },
            "click #detail #pickup_name, .label.dlg_pickup_name": "hsSpotHelper",
            "click #detail #pickup_name2, .label.dlg_pickup_name2": "hsSpotHelper2",
            "change #detail #dropoff_type": function () {
                this.model.set({'hs_anchi_kbn': null, 'hs_anchi_cd': null, 'hs_anchi_nm': null});
            },
            "click #detail #dropoff_name, .label.dlg_dropoff_name": "anchiHelper",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
            "select2-opening #input-tab #syuha_cd": function () {
                // 宗派コードを開いたときに宗旨の区分で絞り込んで表示する
                var syushiCd = this.model.get("syushi_cd");
                var orgKbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
                var fileredKbns = [];
                _.each(orgKbns, function (item) {
                    if (item.kbn_value_cd_num === syushiCd) {
                        fileredKbns.push(item);
                    }
                });
                appcst.syuha_kbns = fileredKbns;
            },
            "select2-selecting #input-tab #syushi_cd": "clearSyuha",
            "select2-clearing #input-tab #syushi_cd": "clearSyuha",
            "select2-opening #infomisc-tab #group": function () {
                // 地区を開いたときに地区の区分で絞り込んで表示する
                var chikuCd = this.model.get("chiku_cd");
                var fileredKbns = [];
                _.each(appcst.kumi_kbns_org, function (item) {
                    if (item.chiku_cd === chikuCd) {
                        fileredKbns.push(item);
                    }
                });
                appcst.kumi_kbns = fileredKbns;
            },
            "change #detail #delivery": function () {
                this.model.set({'mt_hannyu_nm': null});
            },
            "select2-selecting #infomisc-tab #area": "clearKumi",
            "select2-clearing #infomisc-tab #area": "clearKumi",
//            "click #detail #pickup_name": "hansoHelper",
            "click #male": function () {
                // 基本タブ 性別（男）設定
                this.model.set("k_sex_cd", "1");
                this.model.set("k_sex_kbn", "1");
            },
            "click #female": function () {
                // 基本タブ 性別（女）設定
                this.model.set("k_sex_cd", "2");
                this.model.set("k_sex_kbn", "2");
            },
            "change #input-tab #as_address_2": function (e) {
                // 基本タブ 住民登録住所の現住所に同じチェックボックス値を設定
                this.setCheckBox(e, "kj_kbn", '#input-tab #as_address_2');
                if (this.model.get('kj_kbn') === "1") {
                    this.model.set('kj_yubin_no', null);
                    this.model.set('kj_addr1', null);
                    this.model.set('kj_addr2', null);
                    this.model.set('kj_tel', null);
                    this.$("#zip_2").attr("disabled", "disabled");
                    this.$("#address_2_1").attr("disabled", "disabled");
                    this.$("#address_2_2").attr("disabled", "disabled");
                    this.$("#tel_2").attr("disabled", "disabled");
//                    this.$("#zip_2").next(".dlg_zip").addClass("disabled");
                } else {
                    this.$("#zip_2").removeAttr("disabled");
                    this.$("#address_2_1").removeAttr("disabled");
                    this.$("#address_2_2").removeAttr("disabled");
                    this.$("#tel_2").removeAttr("disabled");
//                    this.$("#zip_2").next(".dlg_zip").removeClass("disabled");
                }
            },
            "change #input-tab #as_address_3": function (e) {
                // 基本タブ 本籍の現住所に同じチェックボックス値を設定
                this.setCheckBox(e, "kh_kbn", '#input-tab #as_address_3');
                if (this.model.get('kh_kbn') === "1") {
                    this.model.set('kh_yubin_no', null);
                    this.model.set('kh_addr1', null);
                    this.model.set('kh_addr2', null);
                    this.$("#input-tab #zip_3").attr("disabled", "disabled");
                    this.$("#input-tab #address_3_1").attr("disabled", "disabled");
                    this.$("#input-tab #address_3_2").attr("disabled", "disabled");
//                    this.$("#input-tab #zip_3").next(".dlg_zip").addClass("disabled");
                } else {
                    this.$("#input-tab #zip_3").removeAttr("disabled");
                    this.$("#input-tab #address_3_1").removeAttr("disabled");
                    this.$("#input-tab #address_3_2").removeAttr("disabled");
//                    this.$("#input-tab #zip_3").next(".dlg_zip").removeClass("disabled");
                }
            },
            "click #unfinished": function () {
                // 診断書発行区分 0：未発行 設定
                this.model.set("sd_hakko_kbn", "0");
            },
            "click #finished": function () {
                // 診断書発行区分 1:発行済み設定
                this.model.set("sd_hakko_kbn", "1");
            },
            "click #transport_no": function () {
                // 搬送区分 0：無
                this.model.set("hs_kbn", "0");
            },
            "click #transport_yes": function () {
                // 搬送区分 1:有
                this.model.set("hs_kbn", "1");
            },
            "change #stamp": function (e) {
                // "印鑑チェックボックス値を設定
                this.setCheckBox(e, "az_inkan_kbn", '#stamp');
            },
            "click #catalog_no": function () {
                // 依頼区分 0：無
                this.model.set("mt_irai_kbn", "0");
            },
            "click #catalog_yes": function () {
                // 依頼区分 1:有
                this.model.set("mt_irai_kbn", "1");
            },
            "change #infochief-tab #as_address_4": function (e) {
                // 喪主タブ 故人に同じチェックボックス値を設定
                this.setCheckBox(e, "mg_kbn", '#infochief-tab #as_address_4');
                if (this.model.get('mg_kbn') === "1") {
                    this.model.set('mg_yubin_no', null);
                    this.model.set('mg_addr1', null);
                    this.model.set('mg_addr2', null);
                    this.model.set('mg_tel', null);
                    this.$("#infochief-tab #zip_1").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_1_1").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_1_2").attr("disabled", "disabled");
                    this.$("#infochief-tab #tel_1").attr("disabled", "disabled");
//                    this.$("#infochief-tab #zip_1").next(".dlg_zip").addClass("disabled");
                    // 葬儀情報(ヘッダー)の喪主バインディング処理 故人の現住所を設定
                    this.setHeadeMaddr(this.model.get('kg_addr1'), this.model.get('kg_addr2'));
                } else {
                    this.$("#infochief-tab #zip_1").removeAttr("disabled");
                    this.$("#infochief-tab #address_1_1").removeAttr("disabled");
                    this.$("#infochief-tab #address_1_2").removeAttr("disabled");
                    this.$("#infochief-tab #tel_1").removeAttr("disabled");
//                    this.$("#infochief-tab #zip_1").next(".dlg_zip").removeClass("disabled");
                    // // 葬儀情報(ヘッダー)の喪主バインディング処理 喪主の現住所を設定
                    this.setHeadeMaddr(this.model.get('mg_addr1'), this.model.get('mg_addr2'));
                }
            },
            "change #infochief-tab #as_address_5, #infochief-tab #as_address_5_2": function (e) {
                this.model.set('mj_kbn', "0");
                var $target = $(e.currentTarget);
                if ($target.is("#as_address_5")) {
                    var val = $('#infochief-tab #as_address_5:checked').val();
                    if (val === "1") {
                        $('#infochief-tab #as_address_5_2').attr("checked", false).button("refresh");
                        this.model.set('mj_kbn', "1");
                    }
                } else if ($target.is("#as_address_5_2")) {
                    var val = $('#infochief-tab #as_address_5_2:checked').val();
                    if (val === "1") {
                        $('#infochief-tab #as_address_5').attr("checked", false).button("refresh");
                        this.model.set('mj_kbn', "2");
                    }
                } else {
                    return;
                }
                var mj_kbn = this.model.get('mj_kbn');
                if (mj_kbn === "1" || mj_kbn === "2") {
                    this.model.set('mj_yubin_no', null);
                    this.model.set('mj_addr1', null);
                    this.model.set('mj_addr2', null);
                    this.model.set('mj_tel', null);
                    this.$("#infochief-tab #zip_2").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_2_1").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_2_2").attr("disabled", "disabled");
                    this.$("#infochief-tab #tel_2").attr("disabled", "disabled");
//                    this.$("#infochief-tab #zip_2").next(".dlg_zip").addClass("disabled");
                } else {
                    this.$("#infochief-tab #zip_2").removeAttr("disabled");
                    this.$("#infochief-tab #address_2_1").removeAttr("disabled");
                    this.$("#infochief-tab #address_2_2").removeAttr("disabled");
                    this.$("#infochief-tab #tel_2").removeAttr("disabled");
//                    this.$("#infochief-tab #zip_2").next(".dlg_zip").removeClass("disabled");
                }
            },
            "change #infochief-tab #as_address_6": function (e) {
                // 喪主タブ 本籍に同じチェックボックス値を設定
                this.setCheckBox(e, "mh_kbn", '#infochief-tab #as_address_6');
                if (this.model.get('mh_kbn') === "1") {
                    this.model.set('mh_yubin_no', null);
                    this.model.set('mh_addr1', null);
                    this.model.set('mh_addr2', null);
                    this.$("#infochief-tab #zip_3").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_3_1").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_3_2").attr("disabled", "disabled");
//                    this.$("#infochief-tab #zip_3").next(".dlg_zip").addClass("disabled");
                } else {
                    this.$("#infochief-tab #zip_3").removeAttr("disabled");
                    this.$("#infochief-tab #address_3_1").removeAttr("disabled");
                    this.$("#infochief-tab #address_3_2").removeAttr("disabled");
//                    this.$("#infochief-tab #zip_3").next(".dlg_zip").removeClass("disabled");
                }
            },
            "change #infochief-tab #as_chief": function (e) {
                // 喪主タブ 請求先 喪主に同じチェックボックス値を設定
                this.setCheckBox(e, "sekyu_kbn", '#infochief-tab #as_chief');
                // 喪主タブ 請求先 喪主に同じチェックボックス値変更時処理
                appsk.setSeikyu(this);
            },
            "change #input-tab #birthday_date": "calcNereiK",
            "change #input-tab #birthday_era": "calcNereiK",
            "change #infochief-tab #chief #birthday_date": "calcNereiM",
            "change #infochief-tab #chief #birthday_era": "calcNereiM",
            "change #infodate-tab #kaikan_use": "calcKaijoRyo",
            "change #input-tab #syushi_cd": "setShuhaOther",
            "click input[name='pacemaker']": "doRdoClick",
        },
        bindings: {
            '#hall_cd': {
                observe: 'bumon_cd',
                updateView: false
            },
            '#apply_type': {
                observe: 'moushi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'moushi_kbn');
                    return $el.val();
                }
            },
            '#funeral_type': {
                observe: 'sougi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'sougi_kbn');
                    return $el.val();
                }
            },
            '#personal_info': {
                observe: 'p_info_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'p_info_kbn');
                    return $el.val();
                }
            },
            '#member': {
                observe: 'kaiin_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                // 葬儀情報(ヘッダー)の会員バインディング処理
                getVal: function ($el, event, options) {
                    var item = $el.select2("data");
                    this.$("#hd_kaiin").text(item.text);
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'kaiin_kbn');
                    return $el.val();
                }
            },
            '#code_1': {
                observe: 'daicho_no_eria',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo($el.val(), this.model.get("daicho_no_mm"), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_2': {
                observe: 'daicho_no_mm',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), $el.val(), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_3': {
                observe: 'daicho_no_seq',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), this.model.get("daicho_no_mm"), $el.val());
                    return $el.val();
                }
            },
            '#carn_no': 'free2_cd',
            '#member_detail': {
                observe: 'kaiin_sonota',
                // 葬儀情報(ヘッダー)の会員その他バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_seko_kaiin_sonota").text($el.val());
                    return $el.val();
                }
            },
            '#staff_1': {
                observe: 'uketuke_tanto_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('uketuke_tanto_cd', null);
                    }
                    return val;
                }
            },
            '#staff_2': {
                observe: 'seko_tanto_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('seko_tanto_cd', null);
                    }
                    return val;
                },
                // select2摘要時に修正が必要 葬儀情報(ヘッダー)の施行担当者バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_seko_tanto").text($el.val());
                    return $el.val();
                }
            },
            '#staff_3': {
                observe: 'iso_tanto_nm1',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd1', null);
                    }
                    return val;
                }
            },
            '#staff_3_2': {
                observe: 'iso_tanto_nm1_2',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd1_2', null);
                    }
                    return val;
                }
            },
            '#staff_4': {
                observe: 'iso_tanto_nm2',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd2', null);
                    }
                    return val;
                }
            },
            '#staff_4_2': {
                observe: 'iso_tanto_nm2_2',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd2_2', null);
                    }
                    return val;
                }
            },
            '#staff_5': {
                observe: 'iso_tanto_nm3',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd3', null);
                    }
                    return val;
                }
            },
            '#staff_5_2': {
                observe: 'iso_tanto_nm3_2',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd3_2', null);
                    }
                    return val;
                }
            },
            '#staff_6': {
                observe: 'iso_tanto_nm4',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd4', null);
                    }
                    return val;
                }
            },
            '#staff_6_2': {
                observe: 'iso_tanto_nm4_2',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd4_2', null);
                    }
                    return val;
                }
            },
            '#staff_7': {
                observe: 'iso_tanto_nm5',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd5', null);
                    }
                    return val;
                }
            },
            '#staff_7_2': {
                observe: 'iso_tanto_nm5_2',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('iso_tanto_cd5_2', null);
                    }
                    return val;
                }
            },
            '#input-tab #name': {
                observe: 'k_nm',
                // 葬儀情報(ヘッダー)の故人名バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_k_nm").text($el.val());
                    return $el.val();
                }
            },
            '#input-tab #spouse': {
                observe: 'k_haigu_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'k_haigu_kbn');
                    return $el.val();
                }
            },
            '#input-tab #kana': 'k_knm',
            '#input-tab #birthday_era': {
                observe: 'k_gengo',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #birthday_date': {
                observe: 'k_seinengappi_ymd'
            },
            '#input-tab #age': {
                observe: 'k_nenrei_man'
//                onGet: function (val, options) {
//                    if (!$.msiJqlib.isNullEx2(val)) {
//                        return '満 ' + val + ' 歳';
//                    }
//                }
            },
            '#input-tab #age_at_death': {
                observe: 'k_nenrei_kyounen'
            },
            '#input-tab #zip_1': 'kg_yubin_no',
            '#input-tab #address_1_1': 'kg_addr1',
            '#input-tab #tel_1': 'kg_tel',
            '#input-tab #address_1_2': 'kg_addr2',
            '#input-tab #head_1': {
                observe: 'kg_setai_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'kg_setai_kbn');
                    return $el.val();
                }
            },
//            'as_address_2':'',
            '#input-tab #zip_2': 'kj_yubin_no',
            '#input-tab #address_2_1': 'kj_addr1',
            '#input-tab #tel_2': 'kj_tel',
            '#input-tab #address_2_2': 'kj_addr2',
            '#input-tab #head_2': {
                observe: 'kj_setai_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'kj_setai_kbn');
                    return $el.val();
                }
            },
            '#input-tab #zip_3': 'kh_yubin_no',
            '#input-tab #address_3_1': 'kh_addr1',
            '#input-tab #head_3': {
                observe: 'kh_hito_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'kh_hito_kbn');
                    return $el.val();
                }
            },
            '#input-tab #address_3_2': 'kh_addr2',
            '#input-tab #employee': {
                observe: 'kk_kinmusaki_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #company_tel': 'kk_tel',
            '#input-tab #position': 'kk_yakusyoku_nm',
            '#input-tab #company_fax': 'kk_fax',
            '#input-tab #family_name': 'souke_nm',
            '#input-tab #family_name_kana': 'souke_knm',
            '#input-tab #family_tel': 'souke_tel',
            '#input-tab #funeral_style': {
                observe: 'keishiki_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'keishiki_kbn');
                    return $el.val();
                }
            },
            '#input-tab #company': 'kk_kinmusaki_nm',
            '#input-tab #keishiki_cd': {
                observe: 'souke_addr_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'souke_addr_kbn');
                    return $el.val();
                }
            },
            '#input-tab #syushi_cd': {
                observe: 'syushi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syushi_kbn');
                    return $el.val();
                }
            },
            '#input-tab #syuha_cd': {
                observe: 'syuha_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syuha_kbn');
                    // 宗派名設定処理
                    var item = $el.select2("data");
                    if ($.msiJqlib.isNullEx2(item)) {
                        this.model.set("syuha_nm", null);
                        this.model.set("syuha_knm", null);
                    } else {
                        this.model.set("syuha_nm", item.text);
                        this.model.set("syuha_knm", item.kbn_value_snm);
                    }
                    return $el.val();
                }
            },
            '#input-tab #syuha_nm_other': 'syuha_nm',
            '#input-tab #syuha_knm': 'syuha_knm',
            '#input-tab #syuha_knm2': 'syuha_knm',
            '#input-tab #ofuse': 'v_free12',
            '#input-tab #ofuse_prc': {
                observe: 'n_free10',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#input-tab #tera_shokai': {
                observe: 'free1_code_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'free1_kbn');
                    return $el.val();
                }
            },
            '#input-tab #temple_cd': 'jyusho_cd',
            '#input-tab #temple': {
                observe: 'jyusho_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("jyusho_cd", null);
                        this.model.set("jyusho_knm", null);
                    }
                    return val;
                }
            },
            '#input-tab #temple_knm': 'jyusho_knm',
            '#input-tab #memo': 'biko1',
            '#input-tab #family_count': 'n_free4',
            '#iso_ts': {
                observe: 'iso_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), null, 'iso_ymd', this.model);
                    return $el.val();
                }
            },
            '#iso_time': {
                observe: 'iso_time',
                getVal: function ($el, event, options) {
                    _setNiteiEnd(this.model.get('iso_date'), $el.val(), 'ts_free6', this.model);
                    return $el.val();
                }
            },
            '#infochief-tab #chief #name': {
                observe: 'm_nm',
                // 葬儀情報(ヘッダー)の喪主名バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_m_nm").text($el.val());
                    return $el.val();
                }
            },
            '#infochief-tab #chief #s_chief_relationship': {
                observe: 'm_zoku_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'm_zoku_kbn');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #s_chief_relationship2': {
                observe: 'm_zoku_cd2',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'm_zoku_kbn2');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #kana': 'm_knm',
            '#infochief-tab #chief #birthday_era': {
                observe: 'm_gengo',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #birthday_date': {
                observe: 'm_seinengappi_ymd'
            },
            '#infochief-tab #chief #age': {
                observe: 'm_nenrei_man',
                onGet: function (val, options) {
                    if (!$.msiJqlib.isNullEx2(val)) {
                        return '満' + val + '歳';
                    }
                }
            },
            '#infochief-tab #chief #zip_1': 'mg_yubin_no',
            '#infochief-tab #chief #address_1_1': {
                observe: 'mg_addr1',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                    return $el.val();
                },
                afterUpdate: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                }
            },
            '#infochief-tab #chief #tel_1': 'mg_tel',
            '#infochief-tab #chief #mobile_tel_1': 'mg_m_tel',
            '#infochief-tab #chief #address_1_2': {
                observe: 'mg_addr2',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr1');
                    this.setHeadeMaddr(address, $el.val());
                    return $el.val();
                }
            },
            '#infochief-tab #chief #zip_2': 'mj_yubin_no',
            '#infochief-tab #chief #address_2_1': 'mj_addr1',
            '#infochief-tab #chief #tel_2': 'mj_tel',
            '#infochief-tab #chief #address_2_2': 'mj_addr2',
            '#infochief-tab #chief #zip_3': 'mh_yubin_no',
            '#infochief-tab #chief #address_3_1': 'mh_addr1',
            '#infochief-tab #chief #address_3_2': 'mh_addr2',
            '#infochief-tab #chief #employee': {
                observe: 'mk_kinmusaki_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #company': 'mk_kinmusaki_nm',
            '#infochief-tab #chief #company_tel': 'mk_tel',
            '#infochief-tab #chief #position': 'mk_yakusyoku_nm',
            '#infochief-tab #chief #company_fax': 'mk_fax',
            '#infomisc-tab #steps': {
                observe: 'sd_step_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #date': {
                observe: 'sd_yotei_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('sd_yotei_time'), 'sd_yotei_ymd', this.model);
                    return $el.val();
                }

            },
            '#infomisc-tab #time': {
                observe: 'sd_yotei_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('sd_yotei_date'), $el.val(), 'sd_yotei_ymd', this.model);
                    return $el.val();
                }
            },
            '#infomisc-tab #copys': {
                observe: 'sd_copy_cnt',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #association': 'tk_cyonaikai_nm',
            '#infomisc-tab #leader': 'tk_kumicyo_nm',
            '#infomisc-tab #area': {
                observe: 'chiku_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #group': {
                observe: 'kumi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #doors': {
                observe: 'tk_house_cnt'
//                afterUpdate: function($el, event, options) {
//                    _setSelect2Val($el, $el.val());
//                }
            },
            '#infomisc-tab #persons': {
                observe: 'tk_person_cnt'
//                afterUpdate: function($el, event, options) {
//                    _setSelect2Val($el, $el.val());
//                }
            },
            '#iso_kbn1': {
                observe: 'iso_accept_cd_1',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'iso_accept_kbn_1');
                    return $el.val();
                }
            },
            '#iso_kbn2': {
                observe: 'iso_accept_cd_2',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'iso_accept_kbn_2');
                    return $el.val();
                }
            },
            '#iso_kbn3': {
                observe: 'iso_accept_cd_3',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'iso_accept_kbn_3');
                    return $el.val();
                }
            },
            '#iso_kbn4': {
                observe: 'iso_accept_cd_4',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'iso_accept_kbn_4');
                    return $el.val();
                }
            },
            '#iso_kbn5': {
                observe: 'iso_accept_cd_5',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'iso_accept_kbn_5');
                    return $el.val();
                }
            },
            '#pickup_type': {
                observe: 'hs_gyomu_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'hs_gyomu_kbn');
                    return $el.val();
                }
            },
            '#pickup_type2': {
                observe: 'hs_gyomu_cd_2',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'hs_gyomu_kbn_2');
                    return $el.val();
                }
            },
            '#pickup_name': {
                observe: 'hs_spot_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("hs_spot_kbn", null);
                        this.model.set("hs_spot_cd", null);
                    }
                    return val;
                }
            },
            '#pickup_name2': {
                observe: 'hs_spot_nm_2',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("hs_spot_kbn_2", null);
                        this.model.set("hs_spot_cd_2", null);
                    }
                    return val;
                }
            },
            '#dropoff_type': {
                observe: 'hs_gyomu_cd2',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'hs_gyomu_kbn2');
                    return $el.val();
                }
            },
            '#dropoff_name': {
                observe: 'hs_anchi_nm'
            },
            '#infomisc-tab #crematorium': 'kasoba_nm',
            '#certificate': {
                observe: 'az_death_cnt',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #portrait': {
                observe: 'az_photo_cnt',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infomisc-tab #membership': 'az_gojokai_nm',
            '#infomisc-tab #catalog_area': {
                observe: 'mt_area',
                afterUpdate: function ($el, event, options) {
                    var vals = $el.val().split(',');
                    _setSelect2Val($el, vals);
                },
                getVal: function ($el, event, options) {
                    return $el.val();
                }
            },
            '#infomisc-tab #catalog_item': {
                observe: 'mt_item',
                afterUpdate: function ($el, event, options) {
                    var vals = $el.val().split(',');
                    _setSelect2Val($el, vals);
                },
                getVal: function ($el, event, options) {
                    return $el.val();
                }
            },
            '#infomisc-tab #delivery': {
                observe: 'hs_delivery_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'hs_delivery_kbn');
                    return $el.val();
                }
            },
            '#infomisc-tab #delivery_memo_cd': {
                observe: 'mt_memo_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'mt_memo_kbn');
                    return $el.val();
                }
            },
            '#infomisc-tab #delivery_memo': {
                observe: 'mt_memo_detail'
            },
            '#infomisc-tab #catalog_date': {
                observe: 'mt_hannyu_ymd',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('mt_hannyu_start_time'), 'mt_hannyu_start', this.model);
                    _setNiteiymd($el.val(), this.model.get('mt_hannyu_end_time'), 'mt_hannyu_end', this.model);
                    return $el.val();
                }
            },
            '#infomisc-tab #catalog_time_from': {
                observe: 'mt_hannyu_start_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('mt_hannyu_ymd'), $el.val(), 'mt_hannyu_start', this.model);
                    return $el.val();
                }
            },
            '#infomisc-tab #catalog_time_to': {
                observe: 'mt_hannyu_end_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('mt_hannyu_ymd'), $el.val(), 'mt_hannyu_end', this.model);
                    return $el.val();
                }
            },
            '#infomisc-tab #catalog_place': 'mt_hannyu_nm',
            '#infodate-tab #add_place_1': {
                observe: 'sk_kaijyo_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('sk_kaijyo_cd', null);
                    }
                    return val;
                }
            },
            '#infodate-tab #total_1': {
                observe: 'shikijo_shiyou_prc',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#infodate-tab #add_place_2': {
                observe: 'ty_kaijyo_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('ty_kaijyo_cd', null);
                    }
                    return val;
                }
            },
            '#infodate-tab #nights': 'tuya_paku_su',
            '#infodate-tab #price_2': {
                observe: 'tuya_shikijo_tanka',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#infodate-tab #nights2': 'tuya_paku_su2',
            '#infodate-tab #price_3': {
                observe: 'tuya_shikijo_tanka2',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#infodate-tab #total_2': {
                observe: 'tuya_shikijo_prc1',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#infodate-tab #total_3': {
                observe: 'tuya_shikijo_prc2',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#infodate-tab #add_unei': {
                observe: 'n_free1',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#infodate-tab #kaikan_use': {
                observe: 'free3_code_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'free3_kbn');
                    return $el.val();
                }
            },
            '#infomisc-tab #irai_kakunin_1': {
                observe: 'free5_code_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'free5_kbn');
                    return $el.val();
                }
            },
            '#infomisc-tab #irai_kakunin_2': {
                observe: 'free6_code_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'free6_kbn');
                    return $el.val();
                }
            },
            '#infomisc-tab #jyokenka': 'v_free5',
            '#infomisc-tab #t_tokijiko': 'v_free6',
            "#input-tab-info input[name='pacemaker']": _getRadioBinding('free4_kbn'),
            '#input-tab #kaiso_count': 'n_free5'
        },
        // ラジオボタンクリック処理
        doRdoClick: function (e) {
            var $target = $(e.currentTarget);
            var attr = $target.parent("span").data("ref_attr");
            if (!$.msiJqlib.isNullEx2(attr)) {
                if ($target.hasClass("onCheck")) {
                    $target.attr("checked", false);
                    $target.button("refresh");
                    $target.parent().find("input").removeClass("onCheck");
                    this.model.set(attr, null);
                } else {
                    $target.parent().find("input").removeClass("onCheck");
                    $target.toggleClass("onCheck");
                }
            }
        },
        // 故人年齢計算処理
        calcNereiK: function () {
            var gengo = this.model.get("k_gengo"); // 故人生年月日元号
            var ymd = this.model.get("k_seinengappi_ymd"); // 故人生年月日和暦
            var seinengappi = $.msiJqlib.warekiToseireki(gengo, ymd); // 日付変換
            var moushi_cd = appcst.appModel.get("moushi_cd");
            if (seinengappi && moushi_cd !== MOUSHI_KBN_SEIZEN && moushi_cd !== MOUSHI_KBN_ORDERMADE) { // 事前相談時は年齢を計算しない
                var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
                var nakunariymd = null;
                if (nitei.length === 1) {
                    nakunariymd = nitei[0].get("nitei_date");
                    if (!$.msiJqlib.chkDate(nakunariymd)) { // 日付チェック
                        nakunariymd = null;
                    }
                }
                var pram = {
                    nakunariymd: nakunariymd,
                    seinengappi: seinengappi,
                    setData: function (data) { // コールバック
                        this.model.set("k_nenrei_man", data.man);
                        this.model.set("k_nenrei_kyounen", data.kyonen);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("k_nenrei_man", null);
                this.model.set("k_nenrei_kyounen", null);
            }
        },
        // 喪主年齢計算処理
        calcNereiM: function () {
            var gengo = this.model.get("m_gengo"); // 喪主生年月日元号
            var ymd = this.model.get("m_seinengappi_ymd"); // 喪主生年月日和暦
            var seinengappi = $.msiJqlib.warekiToseireki(gengo, ymd); // 日付変換
            if (seinengappi) {
                var pram = {
                    nakunariymd: null,
                    seinengappi: seinengappi,
                    setData: function (data) {
                        this.model.set("m_nenrei_man", data.man);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("m_nenrei_man", null);
            }
        },
        // 年齢計算処理
        calcNerei: function (pram) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/calcnenrei',
                data: {
                    nakunariymd: pram.nakunariymd,
                    seinengappi: pram.seinengappi
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        if (_.has(pram, "setData")) {
                            pram.setData.apply(that, [mydata]);
                        }
                    }
                }
            });
        },
        // 式場使用料再計算
        calcKaijoRyo: function () {
            var m = this.model;
            // 会場コード
            var sk_kaijyo_cd = m.get("sk_kaijyo_cd");
            if ($.msiJqlib.isNullEx2(sk_kaijyo_cd)) {
                return;
            }
            // 会館利用区分
            var kaikan_use = m.get("free3_kbn");
            // 葬送儀礼またはプラン値引きの利用ありか
            var dataGojokai = appcst.gojokaiMemberCol.filter(function (item) {
                return item.get("yoto_kbn") === '1' || item.get("yoto_kbn") === '11';
            });
            var gojokai_use = dataGojokai.length;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/calckaijoryo',
                data: {
                    sk_kaijyo_cd: sk_kaijyo_cd,
                    kaikan_use: kaikan_use,
                    gojokai_use: gojokai_use
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        m.set('shikijo_shiyou_prc', mydata.charge);
                    }
                }
            });

        },
        setShuhaOther: function () {
            if (this.model.get("syushi_cd") === '9') { // その他
                this.model.set("syuha_cd", '00099');
                this.$("#syuha_nm_other").show();
                this.$("#syuha_knm2").show();
                this.$("#syuha_knm").hide();
//                // 宗派名設定処理
//                var item = this.$("#syuha_cd").select2("data");
//                if (!$.msiJqlib.isNullEx2(item)) {
//                    this.model.set('syuha_nm', item.text);
//                    this.model.set('syuha_knm', item.kbn_value_snm);
//                }
            } else {
                this.$("#syuha_nm_other").hide();
                this.$("#syuha_knm2").hide();
                this.$("#syuha_knm").show();
            }
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        // マイナスのときに赤字クラスを追加する処理
        toggleClass: function (e) {
            appcst.toggleAkajiClass(this, ['shikijo_shiyou_prc', 'tuya_paku_su', 'tuya_paku_su2', 'tuya_shikijo_tanka', 'tuya_shikijo_tanka2', 'tuya_shikijo_prc1', 'tuya_shikijo_prc2', 'n_free1']);
        },
        reCalcShikijoPrc: function () {
            var m = this.model;
            var tnk = m.get("tuya_shikijo_tanka");
            var suryo = m.get("tuya_paku_su");
            if (!$.msiJqlib.isNullEx2(tnk) && !$.msiJqlib.isNullEx2(suryo) && !isNaN(tnk) && !isNaN(suryo)) {
                m.set('tuya_shikijo_prc1', tnk * suryo);
            } else {
                m.set('tuya_shikijo_prc1', null);
            }
        },
        reCalcShikijoPrc2: function () {
            var m = this.model;
            var tnk2 = m.get("tuya_shikijo_tanka2");
            var suryo2 = m.get("tuya_paku_su2");
            if (!$.msiJqlib.isNullEx2(tnk2) && !$.msiJqlib.isNullEx2(suryo2) && !isNaN(tnk2) && !isNaN(suryo2)) {
                m.set('tuya_shikijo_prc2', tnk2 * suryo2);
            } else {
                m.set('tuya_shikijo_prc2', null);
            }
        },
        // 宗派情報をクリアする
        clearSyuha: function (e) {
            if (e.val !== this.model.get("syushi_cd")) {
                this.model.set({'syuha_cd': null, 'syuha_kbn': null, 'syuha_nm': null, 'syuha_knm': null});
            }
        },
        // 組情報をクリアする
        clearKumi: function (e) {
            if (e.val !== this.model.get("chiku_cd")) {
                this.model.set({'kumi_cd': null});
            }
        },
        // 葬儀情報(ヘッダー)の台帳番号バインディング処理
        setHeaderDaichoNo: function (d1, d2, d3) {
            this.$("#hd_daicho_no").text(d1 + '-' + d2 + '-' + d3);
        },
        // 葬儀情報(ヘッダー)の喪主バインディング処理
        setHeadeMaddr: function (addr1, addr2) {
            if ($.msiJqlib.isNullEx2(addr1)) {
                addr1 = '';
            }
            if ($.msiJqlib.isNullEx2(addr2)) {
                addr2 = '';
            }
            this.$("#hd_mg_addr").text(addr1 + ' ' + addr2);
        },
        setIsoTime: function (el, val) {
            var iso_time = this.model.get("iso_time");
            var space = " ";
            var ymd = null;
            if (!$.msiJqlib.isNullEx2(iso_time)) {
                ymd = val + space + iso_time;
                this.model.set('ts_free6', ymd);
            } else {
                this.model.set('ts_free6', ymd);
            }
        },
        initialize: function () {
            this.listenTo(appcst.niteiCol, 'reset', this.addAllNiteiCol);
            this.listenTo(appcst.kashidasiCol, 'reset', this.addAllKashidasiCol);
            this.listenTo(appcst.kashidasiCol, 'add', this.addKashidasiOne);
            this.listenTo(appcst.appModel, 'change:kaiin_cd', this.setKaiinStatus);
            this.listenTo(appcst.appModel, 'change:hs_gyomu_cd', this.setHsSpotStatus);
            this.listenTo(appcst.appModel, 'change:hs_gyomu_cd_2', this.setHsSpotStatus2);
            this.listenTo(appcst.appModel, 'change:iso_accept_cd_1', this.setIsoStatus);
            this.listenTo(appcst.appModel, 'change:iso_accept_cd_2', this.setIsoStatus2);
            this.listenTo(appcst.appModel, 'change:iso_accept_cd_3', this.setIsoStatus3);
            this.listenTo(appcst.appModel, 'change:iso_accept_cd_4', this.setIsoStatus4);
            this.listenTo(appcst.appModel, 'change:iso_accept_cd_5', this.setIsoStatus5);
            this.listenTo(appcst.appModel, 'change:hs_gyomu_cd2', this.setAnchiStatus);
            this.listenTo(appcst.appModel, 'change:hs_delivery_cd', this.setDeliveryStatus);
            this.listenTo(appcst.appModel, 'change:moushi_cd', this.setLabels);
            this.listenTo(appcst.appModel, 'change:tuya_paku_su change:tuya_shikijo_tanka', this.reCalcShikijoPrc);
            this.listenTo(appcst.appModel, 'change:tuya_paku_su2 change:tuya_shikijo_tanka2', this.reCalcShikijoPrc2);
            this.listenTo(appcst.appModel, 'change:shikijo_shiyou_prc change:tuya_paku_su change:tuya_paku_su2 change:tuya_shikijo_tanka change:tuya_shikijo_tanka2 change:n_free1', this.toggleClass);
            this.listenTo(appcst.appModel, 'change:sk_kaijyo_cd', this.setKaijyosikiStatus);
            this.listenTo(appcst.appModel, 'change:ty_kaijyo_cd', this.setKaijyotuyaStatus);
            this.listenTo(appcst.appModel, 'change:k_nenrei_kyounen', this.setKyounenLabel);
            this.listenTo(appcst.appModel, 'change:mt_memo_cd', this.setMtMemo);
            this.listenTo(appcst.appModel, 'change:free6_kbn', this.setJyokenMemo);
            this.listenTo(appcst.appModel, 'change:sk_kaijyo_cd change:ty_kaijyo_cd change:shikijo_shiyou_prc change:tuya_shikijo_prc1 change:tuya_shikijo_prc2', function () {
                appcst.bettoCreFlg = true;
            });
            this.listenTo(appcst.appModel, 'change:n_free1', function () {
                appcst.uneiCreFlg = true;
            });
            this.listenTo(appcst.appModel, 'change:iso_date', this.setIsoTime);
            // 会員切り替え処理
            this.setKaiinStatus();
            // 伺い先切り替え処理
            this.setHsSpotStatus();
            this.setHsSpotStatus2();
            // 移送先区分切替処理
            this.setIsoStatus();
            this.setIsoStatus2();
            this.setIsoStatus3();
            this.setIsoStatus4();
            this.setIsoStatus5();
            // 安置先切り替え処理
            this.setAnchiStatus();
            // 搬入場所切り替え処理
            this.setDeliveryStatus();
            // 申込区分切り替え処理
            this.setLabels();
            // 目録手配備考切り替え処理
            this.setMtMemo();
            //  条件付で可の条件切り替え処理
            this.setJyokenMemo();

            // 赤字クラス切り替え処理
            this.toggleClass();
            // その他 診断書 発行予定日時
            this.$("#date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));

            // その他 目録日時
//            this.$("#catalog_date").datetimepicker($.extend({}, $.msiJqlib.datePickerOnlyDefault()));
//            this.$("#catalog_time_from").datetimepicker($.extend({}, $.msiJqlib.timePickerOnlyDefault()));
//            this.$("#catalog_time_to").datetimepicker($.extend({}, $.msiJqlib.timePickerOnlyDefault()));
            this.$("#catalog_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#catalog_time_from").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.$("#catalog_time_to").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            Backbone.Validation.bind(this);
            this.render();
        },
        render: function () {
            var m = this.model;
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.stickit();
            this.setSelect2();
            return this;
        },
        // select2設定処理
        setSelect2: function () {
            /** 基本タブ*/
            // 申込区分
            $.msiJqlib.setSelect2Com1(this.$("#apply_type"), {data: $.msiJqlib.objToArray3(data.dataKbns.moushi_kbn)});
            // 葬儀区分
            $.msiJqlib.setSelect2Com1(this.$("#funeral_type"), {data: $.msiJqlib.objToArray3(data.dataKbns.sougi_kbn)});
            // 個人情報保護
            $.msiJqlib.setSelect2Com1(this.$("#personal_info"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.p_info)}, $.msiJqlib.setSelect2Default1)));
            // 会員区分
            $.msiJqlib.setSelect2Com1(this.$("#member"), {data: $.msiJqlib.objToArray3(data.dataKbns.kaiin_kbn)});
            // 配偶者
            $.msiJqlib.setSelect2Com1(this.$("#spouse"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.haigu_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #birthday_era"), {data: $.msiJqlib.objToArray3(data.dataKbns.gengo)});
            // 現住所 世帯主
            $.msiJqlib.setSelect2Com1(this.$("#head_1, #u_head_1"), {data: $.msiJqlib.objToArray3(data.dataKbns.setai_kbn)});
            // 住民登録住所 世帯主
            $.msiJqlib.setSelect2Com1(this.$("#head_2"), {data: $.msiJqlib.objToArray3(data.dataKbns.setai_kbn)});
            // 筆頭者
            $.msiJqlib.setSelect2Com1(this.$("#head_3"), {data: $.msiJqlib.objToArray3(data.dataKbns.hito_kbn)});
            // 勤務先
//            $.msiJqlib.setSelect2Com1(this.$("#input-tab #employee"), {data: _employee});
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #employee"), ($.extend({data: _employee}, $.msiJqlib.setSelect2Default1)));
            // 葬儀形式
            $.msiJqlib.setSelect2Com1(this.$("#funeral_style"), {data: $.msiJqlib.objToArray3(data.dataKbns.keishiki_kbn)});
            // 葬家住所
            $.msiJqlib.setSelect2Com1(this.$("#keishiki_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.sk_addr)}, $.msiJqlib.setSelect2Default1)));
            // 宗旨区分
            $.msiJqlib.setSelect2Com1(this.$("#syushi_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.syushi_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 宗派区分
            appcst.syuha_kbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
            $.msiJqlib.setSelect2Com1(this.$("#syuha_cd"), ($.extend({data: function () {
                    return {results: appcst.syuha_kbns};
                }}, $.msiJqlib.setSelect2Default2)));
            // 寺紹介者
            $.msiJqlib.setSelect2Com1(this.$("#tera_shokai"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.tera_shokai)}, $.msiJqlib.setSelect2Default1)));
            // 位牌区分1
            $.msiJqlib.setSelect2Com1(this.$("#ihai_kbn1"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.ihai_kbn1)}, $.msiJqlib.setSelect2Default1)));
            // 位牌区分2
            $.msiJqlib.setSelect2Com1(this.$("#ihai_kbn2"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.ihai_kbn2)}, $.msiJqlib.setSelect2Default1)));

            /** 喪主タブ*/
            // 続柄
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #s_chief_relationship"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 喪主様からみた続柄
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #s_chief_relationship2"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #birthday_era"), {data: $.msiJqlib.objToArray3(data.dataKbns.gengo)});
            // 勤務先
//            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #employee"), {data: _employee});
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #employee"), ($.extend({data: _employee}, $.msiJqlib.setSelect2Default1)));

            /** その他タブ*/
            // 診断書手続き
            $.msiJqlib.setSelect2Com1(this.$("#steps"), ($.extend({data: _steps}, $.msiJqlib.setSelect2Default1)));
            // 診断書コピー
            $.msiJqlib.setSelect2Com1(this.$("#copys"), ($.extend({data: _copys}, $.msiJqlib.setSelect2Default1)));
            // 隣組隣組長様
//            $.msiJqlib.setSelect2Com1(this.$("#doors"), ($.extend({data: _doors}, $.msiJqlib.setSelect2Default1)));
//            $.msiJqlib.setSelect2Com1(this.$("#persons"), ($.extend({data: _persons}, $.msiJqlib.setSelect2Default1)));
            // 移送先            
            $.msiJqlib.setSelect2Com1(this.$("#iso_kbn1, #iso_kbn2, #iso_kbn3, #iso_kbn4, #iso_kbn5"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.iso_kbn)}, $.msiJqlib.setSelect2Default1)));
            // お連れする場所
            $.msiJqlib.setSelect2Com1(this.$("#pickup_type"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.hs_anchi_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#pickup_type2"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.hs_anchi_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 安置先
            $.msiJqlib.setSelect2Com1(this.$("#dropoff_type"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.hs_anchi_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 死亡診断書
            $.msiJqlib.setSelect2Com1(this.$("#certificate"), ($.extend({data: _certificate}, $.msiJqlib.setSelect2Default1)));
            // 御写真
            $.msiJqlib.setSelect2Com1(this.$("#portrait"), ($.extend({data: _portrait}, $.msiJqlib.setSelect2Default1)));
            // 対象エリア
            $.msiJqlib.setSelect2Com1(this.$("#catalog_area"),
                    ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.mt_area)}, $.msiJqlib.setSelect2Default2, {multiple: true, placeholder: '(複数可)', maximumSelectionSize: 5})));
            $.msiJqlib.setSelect2Com1(this.$("#catalog_item"),
                    ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.mt_item)}, $.msiJqlib.setSelect2Default2, {multiple: true, placeholder: '(複数可)', maximumSelectionSize: 5})));
//            this.$("#catalog_area").parent().find(".select2-input.select2-default").css("width", "90px"); // placeholderが一部隠れるため
            // 地区
            $.msiJqlib.setSelect2Com1(this.$("#area"), ($.extend({data: $.msiJqlib.objToArray2(data.dataKbns.chiku)}, $.msiJqlib.setSelect2Default2)));
            //$.msiJqlib.setSelect2Com1(this.$("#group"), ($.extend({data: $.msiJqlib.objToArray2(data.dataKbns.kumi)}, $.msiJqlib.setSelect2Default2)));
            appcst.kumi_kbns = _objToArray(data.dataKbns.kumi);
            appcst.kumi_kbns_org = appcst.kumi_kbns;
            $.msiJqlib.setSelect2Com1(this.$("#group"), ($.extend({data: function () {
                    return {results: appcst.kumi_kbns};
                }}, $.msiJqlib.setSelect2Default2)));
            // 搬入場所区分
            $.msiJqlib.setSelect2Com1(this.$("#delivery"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.delivery_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 目録備考区分
            $.msiJqlib.setSelect2Com1(this.$("#delivery_memo_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.mt_biko)}, $.msiJqlib.setSelect2Default1)));
            // 日程タブ
            // 会場利用
            $.msiJqlib.setSelect2Com1(this.$("#kaikan_use"), {data: $.msiJqlib.objToArray3(data.dataKbns.kaijo_use)});
            // 依頼確認
            $.msiJqlib.setSelect2Com1(this.$("#irai_kakunin_1"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.irai_1)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#irai_kakunin_2"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.irai_2)}, $.msiJqlib.setSelect2Default1)));
        },
        addNiteiOne: function (nitei) {
            var v = new NiteiView({model: nitei});
            this.$("#infodate-tab #infodate").append(v.render().el);
        },
        addAllNiteiCol: function (collection) {
            var $infodate = this.$("#infodate-tab #infodate");
            $infodate.find('fieldset').remove();
            collection.each(this.addNiteiOne, this);
        },
        addKashidasiOne: function (kashidasi) {
            var v = new KashidasiView({model: kashidasi});
            this.$("#kashidasi-bihin").append(v.render().el);
        },
        addAllKashidasiCol: function (collection) {
            var $kashidasi = this.$("#kashidasi-bihin");
            $kashidasi.find('li').remove();
            collection.each(this.addKashidasiOne, this);
        },
        // 会員切り替え処理
        setKaiinStatus: function () {
            var val = this.model.get('kaiin_cd');
            // その他
            if (val === "9") {
                this.$("#member_detail").removeAttr("disabled");
            } else {
                // 加入団体が安心倶楽部になる場合その他も安心倶楽部を自動クリアをやめる
//                this.model.set('kaiin_sonota', null);
//                // 葬儀情報(ヘッダー)の会員その他クリア処理
//                this.$("#hd_seko_kaiin_sonota").text("");
                this.$("#member_detail").attr("disabled", "disabled");
            }
        },
        // 伺い先select2切り替え処理
        setHsSpotStatus: function () {
            var val = this.model.get('hs_gyomu_kbn');
            _setSpotStatus(val, this.$("#pickup_name"), this.$(".dlg_pickup_name"));
        },
        setHsSpotStatus2: function () {
            var val = this.model.get('hs_gyomu_kbn_2');
            _setSpotStatus(val, this.$("#pickup_name2"), this.$(".dlg_pickup_name2"));
        },
        setIsoStatus: function () {
            var val = this.model.get('iso_accept_cd_1');
            _setIsoStatus(val, this.$("#staff_3"), this.$(".dlg_staff3"));
            _setIsoStatus2(val, this.$("#staff_3_2"), this.$(".dlg_staff3_2"));
        },
        setIsoStatus2: function () {
            var val = this.model.get('iso_accept_cd_2');
            _setIsoStatus(val, this.$("#staff_4"), this.$(".dlg_staff4"));
            _setIsoStatus2(val, this.$("#staff_4_2"), this.$(".dlg_staff4_2"));
        },
        setIsoStatus3: function () {
            var val = this.model.get('iso_accept_cd_3');
            _setIsoStatus(val, this.$("#staff_5"), this.$(".dlg_staff5"));
            _setIsoStatus2(val, this.$("#staff_5_2"), this.$(".dlg_staff5_2"));
        },
        setIsoStatus4: function () {
            var val = this.model.get('iso_accept_cd_4');
            _setIsoStatus(val, this.$("#staff_6"), this.$(".dlg_staff6"));
            _setIsoStatus2(val, this.$("#staff_6_2"), this.$(".dlg_staff6_2"));
        },
        setIsoStatus5: function () {
            var val = this.model.get('iso_accept_cd_5');
            _setIsoStatus(val, this.$("#staff_7"), this.$(".dlg_staff7"));
            _setIsoStatus2(val, this.$("#staff_7_2"), this.$(".dlg_staff7_2"));
        },
        // 安置先区分select2切り替え処理
        setAnchiStatus: function () {
            var val = this.model.get('hs_gyomu_kbn2');
            _setSpotStatus(val, this.$("#dropoff_name"), this.$(".dlg_dropoff_name"));
        },
        // 搬入場所select2切り替え処理
        setDeliveryStatus: function () {
            var val = this.model.get('hs_delivery_cd');
            if (val === "9") { // 9:その他
                this.$("#catalog_place").removeAttr("disabled");
            } else {
                this.$("#catalog_place").attr("disabled", "disabled");
            }
        },
        // 申込区分変更によるラベル切り替え処理
        setLabels: function () {
            if (this.model.get('moushi_cd') === MOUSHI_KBN_SEIZEN || this.model.get('moushi_cd') === MOUSHI_KBN_ORDERMADE) { // 生前依頼 OR オーダーメイド
                this.replaceLabel(this.$(".lbl_replace_kojin"), "故人", "対象者");
                this.replaceLabel(this.$(".lbl_replace_moshu").add(this.$(".tab").find("a,span")), "喪主", "相談者");
                // 現住所 世帯主
                $.msiJqlib.setSelect2Com1(this.$("#head_1, #u_head_1"), {data: $.msiJqlib.objToArray3(data.dataKbns.setai_kbn2)});
                // 住民登録住所 世帯主
                $.msiJqlib.setSelect2Com1(this.$("#head_2"), {data: $.msiJqlib.objToArray3(data.dataKbns.setai_kbn2)});
                // 筆頭者
                $.msiJqlib.setSelect2Com1(this.$("#head_3"), {data: $.msiJqlib.objToArray3(data.dataKbns.hito_kbn2)});
                // 死亡日
                $("#input-tab-info .sibo_date_field").hide();
                // 亡くなられた場所
                this.replaceLabel(this.$(".lbl_sibo_basho"), "亡くなられた場所", "入院先等");
                // 喪主様からみた故人様の続柄
                this.replaceLabel(this.$(".lbl_relationship"), "喪主様からみた故人様の続柄", "相談者からみた対象者の続柄");


            } else {
                this.replaceLabel(this.$(".lbl_replace_kojin"), "対象者", "故人");
                this.replaceLabel(this.$(".lbl_replace_moshu").add(this.$(".tab").find("a,span")), "相談者", "喪主");
                // 現住所 世帯主
                $.msiJqlib.setSelect2Com1(this.$("#head_1, #u_head_1"), {data: $.msiJqlib.objToArray3(data.dataKbns.setai_kbn)});
                // 住民登録住所 世帯主
                $.msiJqlib.setSelect2Com1(this.$("#head_2"), {data: $.msiJqlib.objToArray3(data.dataKbns.setai_kbn)});
                // 筆頭者
                $.msiJqlib.setSelect2Com1(this.$("#head_3"), {data: $.msiJqlib.objToArray3(data.dataKbns.hito_kbn)});
                // 死亡日
                $("#input-tab-info .sibo_date_field").show();
                // 亡くなられた場所
                this.replaceLabel(this.$(".lbl_sibo_basho"), "入院先等", "亡くなられた場所");
                // 喪主様からみた故人様の続柄
                this.replaceLabel(this.$(".lbl_relationship"), "相談者からみた対象者の続柄", "喪主様からみた故人様の続柄");
            }
        },
        setMtMemo: function () {
            var val = this.model.get('mt_memo_cd');
            if ($.msiJqlib.isNullEx2(val)) {
                this.$("#delivery_memo").removeAttr("disabled");
            } else {
                this.$("#delivery_memo").attr("disabled", "disabled");
                this.model.set('mt_memo_detail', null);
            }
        },
        setJyokenMemo: function () {
            var val = this.model.get('free6_kbn');
            if (val === '2') {
                this.$("#jyokenka").removeAttr("disabled");
            } else {
                this.$("#jyokenka").attr("disabled", "disabled");
                this.model.set('v_free5', null);
            }
        },
        // 享年のラベルを設定する
        setKyounenLabel: function (m, val) {
            if (!$.msiJqlib.isNullEx2(val) && !isNaN(val)) {
                this.$("#age_at_death_pref").text("享年");
                this.$("#age_at_death_suf").text("歳");
            } else {
                this.$("#age_at_death_pref").text("");
                this.$("#age_at_death_suf").text("");
            }
        },
        replaceLabel: function ($tagets, substr, newsubstr) {
            $tagets.each(function () {
                var $taget = $(this);
                var lable = $taget.text().replace(substr, newsubstr);
                $taget.text(lable);
            });
        },
        // チェックボックス切り替え処理
        setCheckBox: function (e, pro, target) {
            var val = $(target + ':checked').val();
            this.model.set(pro, val === "1" ? "1" : "0");
        },
        // タブ切り替え処理
        changeTab: function (e) {
            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.tab li'), $(e.currentTarget));
            var $tabId = $.msiJqlib.getTabId();
            if ($tabId.is('#tab-report-info')) {
                // 報告書タブ設定処理
                appcst.resetReport();
            }
            // 互助会確認の場合、加入状況をクリックする
            if ($tabId.is('#tab-kaiin-info')) {
                this.$("#member_group_set #member_group_1").click();
            }
        },
        isInputOk: function () {

            var aMsg = [];
            // 施行基本フリーモデルチェック
            var result = appcst.kfModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    if ($.inArray(v, aMsg) < 0) {
                        aMsg.push(v);
                    }
                });
            }
            // 施行基本モデルチェック
            var result = appcst.appModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 施行日程コレクションチェック
            appcst.niteiCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                    });
                }
            });

            // 請求先情報モデルチェック
            var result = appcst.sekyuModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }

            // 施行互助会会員コレクションチェック
            appcst.gojokaiMemberCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                        ;
                    });
                }
            });

            // 施行互助会情報モデルチェック
            var result = appcst.gojokaiInfoModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }

            // 施行互貸出備品コレクションチェック
            appcst.kashidasiCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                        ;
                    });
                }
            });
            // 施行報告書モデルチェック
            var result = appcst.houkokuModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 引継ぎモデルチェック
            var result = appcst.hikitsugiModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            var result = appcst.SekoHikitsugiModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    if ($.inArray(v, aMsg) < 0) {
                        aMsg.push(v);
                    }
                });
            }

            // NG
            if (aMsg.length > 0) {
                var errClsNm = ".error1";
                var $li = this.$('.tab');
                if (this.$("#input-tab-info").find(errClsNm).length) {
                    $li.find("#tab-input-info1 a").click();
                } else if (this.$("#input-tab").find(errClsNm).length) {
                    $li.find("#tab-input-info2 a").click();
                } else if (this.$("#infodate-tab").find(errClsNm).length) {
                    $li.find("#tab-seko-info a").click();
                } else if (this.$("#infochief-tab").find(errClsNm).length) {
                    $li.find("#tab-seikyu-info a").click();
                } else if (this.$("#infomember-tab").find(errClsNm).length) {
                    $li.find("#tab-kaiin-info a").click();
                } else if (this.$("#infomisc-tab").find(errClsNm).length) {
                    $li.find("#tab-other-info a").click();
                } else if (this.$("#inforental-tab").find(errClsNm).length) {
                    $li.find("#tab-kashidasi-info a").click();
                } else if (this.$("#report-tab").find(errClsNm).length) {
                    $li.find("#tab-report-info a").click();
                }
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }

            // OK
            $.msiJqlib.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },
        doSekoCopy: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('施行情報をコピーして新規作成します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/sekocopy',
                data: {
                    seko_no: sekoNo,
                    controllerName: appcst.controllerName,
                    copy_kbn: 1 // サンプルTO葬儀
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
//                        var m = location.href.match(/\/(\D+)\/(\D+)\/(\D+)\/(\D+)/);
                        var herf;
//                        if (m) {
//                            herf =  m[0] + mydata.seko_no;
//                        } else {
                        if (appcst.controllerName === "juchuhenko") {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/customerinfo/sn/' + mydata.seko_no;
                        } else {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/input/sn/' + mydata.seko_no;
                        }
//                        }
                        location.href = herf;
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });

        },
        doSeko2SampleCopy: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('施行情報をコピーしてサンプルを作成します。よろしいですか？')) {
                return;
            }
            var moushi_cd = appcst.appModel.get("moushi_cd");
            var copyKbn = '';
            if (moushi_cd === MOUSHI_KBN_SOUGI) {
                copyKbn = 2; // 葬儀TOサンプル
            } else if (moushi_cd === MOUSHI_KBN_SEIZEN) {
                copyKbn = 3;// 事前TOサンプル
            } else if (moushi_cd === MOUSHI_KBN_ORDERMADE) {
                copyKbn = 4;// オーダーメイドTOサンプル
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/sekocopy',
                data: {
                    seko_no: sekoNo,
                    controllerName: appcst.controllerName,
                    copy_kbn: copyKbn // 
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
                        var herf;
                        if (appcst.controllerName === "juchuhenko") {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/customerinfo/sn/' + mydata.seko_no;
                        } else {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/input/sn/' + mydata.seko_no;
                        }
                        location.href = herf;
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });

        },
        doSeko3SeizenCopy: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('事前相談をコピーして事前相談を作成します。よろしいですか？')) {
                return;
            }
            var moushi_cd = appcst.appModel.get("moushi_cd");
            var copyKbn = '';
            if (moushi_cd === MOUSHI_KBN_SEIZEN) {
                copyKbn = 5;// 事前TOサンプル
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/sekocopy',
                data: {
                    seko_no: sekoNo,
                    controllerName: appcst.controllerName,
                    copy_kbn: copyKbn // 事前TOサンプル
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
                        var herf;
                        if (appcst.controllerName === "juchuhenko") {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/customerinfo/sn/' + mydata.seko_no;
                        } else {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/input/sn/' + mydata.seko_no;
                        }
                        location.href = herf;
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });

        },
        doSekoCopy4: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('事前相談情報をコピーして新規施行を作成します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/sekocopy',
                data: {
                    seko_no: sekoNo,
                    controllerName: appcst.controllerName,
                    copy_kbn: 6 // 事前相談TO葬儀
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        $.msiJqlib.showInfo(mydata.msg);
                        var herf;
                        if (appcst.controllerName === "juchuhenko") {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/customerinfo/sn/' + mydata.seko_no;
                        } else {
                            herf = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/input/sn/' + mydata.seko_no;
                        }
                        location.href = herf;
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });

        },
        doSave: function () {
            if (!this.isInputOk()) {
                return;
            }
            if (this.model.get("moushi_cd") === MOUSHI_KBN_SAMPLE && orgDataApp.moushi_cd !== this.model.get("moushi_cd")) {
                if (!confirm('サンプルのデータを作成します。一度作成すると申込区分の変更はできません。よろしいですか')) {
                    return;
                }

            }
            var bumonCd = $('#hall_cd').val();
            var bumonChanged = bumonCd !== appcst.appModel.get("bumon_cd");
            // 見積確定
            if (!$.msiJqlib.isNullEx2(appcst.appModel.get("jichu_kakute_ymd")) && bumonChanged) {
                this.exeCheck();
            } else {
                this.exeSave();
            }
        },
        exeCheck: function () {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/CheckGetujiFixInput',
                data: {dataAppJson: JSON.stringify(appcst.appModel.toJSON())},
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        that.exeSave();
                    } else if (mydata.status === 'INFO') {
                        $.msiJqlib.setProgressing(false);
                        if (!confirm(mydata.msg)) {
                            return;
                        }
                        $.msiJqlib.setProgressing(true);
                        that.exeSave();
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        exeSave: function () {
//            // 会員コード設定処理
//            this.setKaiinCd();
            // 施行基本イコール
            var sekoKihonEq = $.msiJqlib.isEqual(appcst.appModel.toJSON(), orgDataApp);
            // 施行基本汎用フリー情報イコール
            var sekoKihonFreeEq = $.msiJqlib.isEqual(appcst.kfModel.toJSON(), orgDataKihonFree);
            // 施行日程イコール
            var sekoNiteiEq = $.msiJqlib.isEqual(appcst.niteiCol.toJSON(), orgDataNiteiCol);
            // 請求先情報イコール
            var sekyuInfoEq = $.msiJqlib.isEqual(appcst.sekyuModel.toJSON(), orgDataSekyuInfo);
            // 施行互助会情報イコール
            var sekoGojokaiInfoEq = $.msiJqlib.isEqual(appcst.gojokaiInfoModel.toJSON(), orgDataGojokaiInfo);
            // 施行互助会加入者イコール
            var sekoGojokaiMemberEq = $.msiJqlib.isEqual(appcst.gojokaiMemberCol.toJSON(), orgDataGojokaiMemberCol);
            // 貸出備品イコール
            var kashidasiEq = $.msiJqlib.isEqual(appcst.kashidasiCol.toJSON(), orgDataKashidasiCol);
            //  施行報告書イコール
            var houkokuEq = $.msiJqlib.isEqual(appcst.houkokuModel.toJSON(), appcst.orgHoukokuModelJson);
            //  引継ぎイコール
            var hikitsugiEq = $.msiJqlib.isEqual(appcst.hikitsugiModel.toJSON(), appcst.orgHikitsugiModelJson);
            //  施行引継ぎイコール
            var sekoHikitsugiEq = $.msiJqlib.isEqual(appcst.SekoHikitsugiModel.toJSON(), appcst.orgSekoHikitsugiModelJson);
            //  アフターフォローイコール
            var afterFollowEq = $.msiJqlib.isEqual(appcst.afterFollowModel.toJSON(), appcst.orgAfterFollowModelJson);
            var afterFollowEq2 = $.msiJqlib.isEqual(appcst.naiyoKakuninCol1.toJSON(), appcst.orgNaiyoKakuninCol1);
            afterFollowEq2 = afterFollowEq2 && $.msiJqlib.isEqual(appcst.naiyoKakuninCol2.toJSON(), appcst.orgNaiyoKakuninCol2);

            if (sekoKihonEq && sekoNiteiEq && sekyuInfoEq && sekoGojokaiInfoEq && sekoGojokaiMemberEq && kashidasiEq && houkokuEq && hikitsugiEq && afterFollowEq && afterFollowEq2 && sekoKihonFreeEq && sekoHikitsugiEq) {
                $.msiJqlib.showInfo('データの変更がありません');
                return;
            }
            // チェンジフラグ設定
            var changeFlg = JSON.stringify({
                kihonChangeFlg: !sekoKihonEq,
                kihonFreeChangeFlg: !sekoKihonFreeEq,
                niteiChangeFlg: !sekoNiteiEq,
                sekyuInfoChangeFlg: !sekyuInfoEq,
                gojokaiInfoChangeFlg: !sekoGojokaiInfoEq,
                gojokaiMemberChangeFlg: !sekoGojokaiMemberEq,
                kashidasiChangeFlg: !kashidasiEq,
                houkokuChangeFlg: !houkokuEq,
                hikitsugiChangeFlg: !hikitsugiEq,
                sekoHikitsugiChangeFlg: !sekoHikitsugiEq,
                afterFollowEqChangeFlg: !afterFollowEq,
                afterFollowEqChangeFlg2: !afterFollowEq2,
                bettoCreFlg: appcst.bettoCreFlg,
                uneiCreFlg: appcst.uneiCreFlg
            });
            var bumonCd = $('#hall_cd').val();
            appcst.appModel.set('bumon_cd', bumonCd);
            // 施行基本情報
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            // 施行基本汎用フリー情報
            var dataSekoKihonFreeJson = JSON.stringify(appcst.kfModel.toJSON());
            // 施行日程情報
            var dataNiteiColJson = JSON.stringify(appcst.niteiCol.toJSON());
            // 請求先情報
            var dataSekyuInfoJson = JSON.stringify(appcst.sekyuModel.toJSON());
            // 施行互助会情報
            var dataGojokaiInfoJson = JSON.stringify(appcst.gojokaiInfoModel.toJSON());
            // 施行互助会加入者情報
            // 会員番号があるデータに絞込み
            var dataGojokaiMemberCol = this.gojokaiMemberfilter();
            var dataGojokaiMemberColJson = JSON.stringify(dataGojokaiMemberCol);

            // 貸出備品情報変更があるデータに絞込み
            var dataKashidasiCol = appcst.kashidasiCol.filter(function (item) {
                return item.get("up_flg") === 1;
            });
            var dataKashidasiColJson = JSON.stringify(dataKashidasiCol);
            // 貸出備品削除データ
            var dataKashidasiDelColJson = JSON.stringify(kashidasiDelCol.toJSON());
            // 報告書データ
            var dataHoukokuJson = JSON.stringify(appcst.houkokuModel.toJSON());
            // 引継ぎデータ
            var dataHikitsugiJson = JSON.stringify(appcst.hikitsugiModel.toJSON());
            var dataSekoHikitsugiJson = JSON.stringify(appcst.SekoHikitsugiModel.toJSON());
            // アフターフォローデータ
            var dataCstSekoHstJson = JSON.stringify(appcst.afterFollowModel.toJSON());
            var dataCstAfterMsiJson1 = JSON.stringify(appcst.naiyoKakuninCol1.toJSON());
            var dataCstAfterMsiJson2 = JSON.stringify(appcst.naiyoKakuninCol2.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/customerinfosave',
                data: {
                    controllerName: appcst.controllerName,
                    dataSekoKihonJson: dataSekoKihonJson,
                    dataSekoKihonFreeJson: dataSekoKihonFreeJson,
                    dataNiteiColJson: dataNiteiColJson,
                    dataSekyuInfoJson: dataSekyuInfoJson,
                    dataGojokaiInfoJson: dataGojokaiInfoJson,
                    dataGojokaiMemberColJson: dataGojokaiMemberColJson,
                    dataKashidasiColJson: dataKashidasiColJson,
                    dataKashidasiDelColJson: dataKashidasiDelColJson,
                    dataHoukokuJson: dataHoukokuJson,
                    dataHikitsugiJson: dataHikitsugiJson,
                    dataSekoHikitsugiJson: dataSekoHikitsugiJson,
                    dataCstSekoHstJson: dataCstSekoHstJson,
                    dataCstAfterMsiJson1: dataCstAfterMsiJson1,
                    dataCstAfterMsiJson2: dataCstAfterMsiJson2,
                    changeFlg: changeFlg
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        _resetData(mydata.dataSekoKihon,
                                mydata.dataNiteiCol,
                                mydata.dataSekyuInfo,
                                mydata.dataGojokaiInfo,
                                mydata.dataGojokaiMemberCol,
                                mydata.dataKashidasiCol,
                                mydata.dataSekoKihonFree);
                        $("#header .id_number span.number").text(mydata.dataSekoKihon.seko_no);  // 施行番号設定
                        appcst.backupReportData(); // 報告書データ退避
                        appcst.setImgLink(); // 報告書画像リンク設定
                        $.msiJqlib.showInfo(mydata.msg);

                        var matcheds = location.href.match(/(\/sn\/\d+)/);
                        if (!matcheds) {
                            var m = location.href.match(/\/(\D+)\/(\D+)\/(\D+)\/(\D+)/);
                            var herf;
                            if (m) {
                                herf = $.msiJqlib.baseUrl() + "/" + m[2] + "/" + m[3] + "/" + m[4].replace("/", '') + "/sn/" + mydata.dataSekoKihon.seko_no;
                            } else {
                                herf = location.href + "/sn/" + mydata.dataSekoKihon.seko_no;
                            }
//                            history.pushState("", "", herf);
                            location.href = herf;
                        }
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                    //_setDisable('');
                    //console.log('ajax res msg==>' + mydata.msg);
                }
            });
        },
        doPrint: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            var pdfUrl = 'pdf0101';
            var $tabId = $.msiJqlib.getTabId();
            // その他タブは目録手配を印刷する
//            if (idx === 4) {
//                pdfUrl = 'pdf0105';
//                msiLib2.openWinPv(
//                        $.msiJqlib.baseUrl() + '/juchu/' + pdfUrl + '?preview=on&seko_no=' + sekoNo,
//                        {
//                            //p1: 'v1', // ...
//                        }
//                );
//                return;
//            } else 
            var mode = '';
            if ($tabId.is('#tab-report-info')) {
                var subIdx = appcst.reportViewIdx;
                if (subIdx === 0) {
                    pdfUrl = 'pdf0116';
                } else if (subIdx === 1) {
                    pdfUrl = 'pdf0117';
                } else if (subIdx === 3) {
                    pdfUrl = 'pdf0121';
                } else if (subIdx === 4) {
                    pdfUrl = 'pdf0122';
                } else if (subIdx === 5) {
                    pdfUrl = 'pdf0123';
                } else if (subIdx === 6) {
                    pdfUrl = 'pdf0124';
                } else if (subIdx === 7) {
                    pdfUrl = 'pdf0127';
                } else if (subIdx === 8) {
                    pdfUrl = 'pdf0134';
                    mode = 'seka';
                } else if (subIdx === 9) {
                    pdfUrl = 'pdf0135';
                } else if (subIdx === 10) {
                    pdfUrl = 'pdf0134';
                    mode = 'kago';
                } else if (subIdx === 11) {
                    pdfUrl = 'pdf0134';
                    mode = 'hanawa';
                } else {
                    return;
                }
            }
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/' + pdfUrl,
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo,
                    mode: mode
                }
            });
            return;
            /* 現在版
             var $form = this.$("#customerinfo-form-id");
             $.msiJqlib.setHiddenInput('preview', 'off', $form);
             $.msiJqlib.setHiddenInput('send', 'off', $form);
             $.msiJqlib.setHiddenInput('seko_no', sekoNo, $form);
             $form.attr('action', $.msiJqlib.baseUrl() + '/juchu/pdf0101');
             $form.attr('method', 'POST');
             $form.submit();
             */
        },
        doSekoInfoPrint: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0120',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });

        },
        doDelete: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('お客様情報を削除します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/customerinfodelete',
                data: {
                    seko_no: sekoNo,
                    controllerName: appcst.controllerName
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        var matcheds = location.href.match(/(.+)(\/sn\/\d+)/);
                        if (matcheds) {
                            location.href = matcheds[1];
                        } else {
                            window.location.reload();
                        }
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        doCancel: function () {
            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }
            window.location.reload();
        },
//        // 会員区分が一般の状態で、互助会加入情報を登録する場合、一般から互助会に変更する
//        setKaiinCd: function() {
//            if (this.model.get("kaiin_cd") !== '1') { // 1:互助会以外
//                // 会員番号があるデータに絞込み
//                var dataGojokaiMemberCol = this.gojokaiMemberfilter();
//                if (dataGojokaiMemberCol.length > 0) {
//                    this.model.set({kaiin_kbn: "1", kaiin_cd: "1"});
//                }
//            }
//        }, 
        // コースがあるデータに絞込み
        gojokaiMemberfilter: function () {
            var dataGojokaiMemberCol = appcst.gojokaiMemberCol.filter(function (item) {
                return !$.msiJqlib.isNullEx2(item.get("course_snm_cd"));
            });
            return dataGojokaiMemberCol;
        },
        // 郵便番号ヘルパー処理
        zipHelper: function (e) {
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            var val = $target.data("zip");
            var zip, addr1;
            var m = this.model;
            if (val === 'k1') { // 故人現住所
                zip = 'kg_yubin_no';
                addr1 = 'kg_addr1';
            } else if (val === 'k2') {// 故人住民登録住所
                zip = 'kj_yubin_no';
                addr1 = 'kj_addr1';
            } else if (val === 'k3') {// 故人本籍
                zip = 'kh_yubin_no';
                addr1 = 'kh_addr1';
            } else if (val === 'm1') {// 喪主現住所
                zip = 'mg_yubin_no';
                addr1 = 'mg_addr1';
            } else if (val === 'm2') {// 喪主住民登録住所
                zip = 'mj_yubin_no';
                addr1 = 'mj_addr1';
            } else if (val === 'm3') {// 喪主本籍
                zip = 'mh_yubin_no';
                addr1 = 'mh_addr1';
            } else if (val === 's1') {// 請求先現住所
                zip = 'sekyu_yubin_no';
                addr1 = 'sekyu_addr1';
                m = appcst.sekyuModel;
            } else if (val === 'uk1') {// 受付情報①現住所
                zip = 'zip_no1';
                addr1 = 'addr1_1';
                m = appcst.kfModel;
            }
            this.$el.msiPickHelper({
                action: 'zipno',
                onSelect: function (data) {
                    m.set(zip, data.code);
                    m.set(addr1, data.name);
                },
                onClear: function () {
                    m.set(zip, null);
                    m.set(addr1, null);
                }
            });
        },
        // 受付担当者ヘルパー処理 
        uketukeHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'uketuke_tanto_cd': data.code, 'uketuke_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'uketuke_tanto_cd': null, 'uketuke_tanto_nm': null});
                }
            });
        },
        // 施行担当者ヘルパー処理
        sekoHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'seko_tanto_cd': data.code, 'seko_tanto_nm': data.name});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text(data.name);
                },
                onClear: function () {
                    m.set({'seko_tanto_cd': null, 'seko_tanto_nm': null});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text('');
                }
            });
        },
        // その他移送者1ヘルパー処理 
        isoHelper: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_1");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else if (isoKbn === "2") {
                act = "siire";
                transfer_kbn = "1";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd1': data.code, 'iso_tanto_nm1': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd1': null, 'iso_tanto_nm1': null});
                }
            });
        },
        // その他移送者2ヘルパー処理 
        isoHelper2: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_2");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else if (isoKbn === "2") {
                act = "siire";
                transfer_kbn = "1";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd2': data.code, 'iso_tanto_nm2': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd2': null, 'iso_tanto_nm2': null});
                }
            });
        },
        // その他移送者3ヘルパー処理 
        isoHelper3: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_1");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd1_2': data.code, 'iso_tanto_nm1_2': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd1_2': null, 'iso_tanto_nm1_2': null});
                }
            });
        },
        // その他移送者4ヘルパー処理 
        isoHelper4: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_2");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd2_2': data.code, 'iso_tanto_nm2_2': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd2_2': null, 'iso_tanto_nm2_2': null});
                }
            });
        },
        // その他移送者5ヘルパー処理 
        isoHelper5: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_3");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else if (isoKbn === "2") {
                act = "siire";
                transfer_kbn = "1";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd3': data.code, 'iso_tanto_nm3': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd3': null, 'iso_tanto_nm3': null});
                }
            });
        },
        // その他移送者6ヘルパー処理 
        isoHelper6: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_3");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd3_2': data.code, 'iso_tanto_nm3_2': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd3_2': null, 'iso_tanto_nm3_2': null});
                }
            });
        },
        // その他移送者7ヘルパー処理 
        isoHelper7: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_4");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else if (isoKbn === "2") {
                act = "siire";
                transfer_kbn = "1";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd4': data.code, 'iso_tanto_nm4': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd4': null, 'iso_tanto_nm4': null});
                }
            });
        },
        // その他移送者8ヘルパー処理 
        isoHelper8: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_4");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd4_2': data.code, 'iso_tanto_nm4_2': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd4_2': null, 'iso_tanto_nm4_2': null});
                }
            });
        },
        // その他移送者9ヘルパー処理 
        isoHelper9: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_5");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else if (isoKbn === "2") {
                act = "siire";
                transfer_kbn = "1";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd5': data.code, 'iso_tanto_nm5': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd5': null, 'iso_tanto_nm5': null});
                }
            });
        },
        // その他移送者10ヘルパー処理 
        isoHelper10: function () {
            var m = this.model;
            var isoKbn = m.get("iso_accept_kbn_5");
            var act = "";
            var transfer_kbn = "";
            if (isoKbn === "1") {
                act = "tanto";
            } else {
                return;
            }
            this.$el.msiPickHelper({
                action: act,
                mydata: {s_transfer_kbn: transfer_kbn, init_search: 1},
                onSelect: function (data) {
                    m.set({'iso_tanto_cd5_2': data.code, 'iso_tanto_nm5_2': data.name});
                },
                onClear: function () {
                    m.set({'iso_tanto_cd5_2': null, 'iso_tanto_nm5_2': null});
                }
            });
        },
        // 名所住所ヘルパー処理
        nmjyushoHelper: function (e) {
            var t = this;
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var kind = $target.data("kind2");
            var code = $target.data("code");
            var name = $target.data("name");
            var kname = $target.data("kname");
            var m = this.model;
            var syuha_cd = null;
            if (kind === 1) {
                syuha_cd = this.model.get("syuha_cd");
            }
            this.$el.msiPickHelper({
                action: 'nmjyusho',
                kind2: kind,
                mydata: {s_syuha_cd: syuha_cd},
                onSelect: function (data) {
                    m.set(code, data.code);
                    m.set(name, data.name);
                    if (!$.msiJqlib.isNullEx2(kname)) {
                        m.set(kname, data.jyusho_lknm);
                        if (kind === 1) {
                            if (m.get("syuha_cd") !== data.syuha_cd) {
                                m.set('syushi_cd', data.syushi_cd);
                                m.set('syuha_cd', data.syuha_cd);
                                m.set('syuha_kbn', data.syushi_cd);
                                m.set('syuha_nm', data.syuha_nm);
                                m.set('syuha_knm', data.syuha_kana);
                                t.setShuhaOther();
                            }
                        }
                    }
                },
                onClear: function () {
                    m.set(code, null);
                    m.set(name, null);
                    if (!$.msiJqlib.isNullEx2(kname)) {
                        m.set(kname, null);
                    }
                }
            });
        },
        // 搬入場所ヘルパー処理
//        hannyuHelper: function() {
//            var m = this.model;
//            this.$el.msiPickHelper({
//                action: 'nonyu',
//                data: {
//                },
//                onSelect: function(data) {
//                    m.set({
//                        'mt_hannyu_cd': data.code,
//                        'mt_hannyu_nm': data.addr1 + data.addr2
//                    });
//                },
//                onClear: function() {
//                    m.set({
//                        'mt_hannyu_cd': null,
//                        'mt_hannyu_nm': null
//                    });
//                }
//            });
//        },
        // お伺い先ヘルパー処理
        hsSpotHelper: function () {
            var m = this.model;
            var kind = m.get("hs_gyomu_kbn");

            var select = function (data) {
                m.set('hs_spot_cd', data.code);
                m.set('hs_spot_nm', data.name);
                m.set('hs_spot_kbn', data.jyusho_kbn);
            };
            var clear = function (data) {
                m.set('hs_spot_cd', null);
                m.set('hs_spot_nm', null);
                m.set('hs_spot_kbn', null);
            };
            // 3:自社会館
            if (kind === '3') {
                this.$el.msiPickHelper({
                    action: 'kaijyo',
                    mydata: {s_kaijyo_kbn: '5'},
                    onSelect: select,
                    onClear: clear
                });
            }
            if (kind === '2') {
                this.$el.msiPickHelper({
                    action: 'nmjyusho',
                    kind2: '1',
                    onSelect: select,
                    onClear: clear
                });
            }
            if (kind === '7') { // 火葬場
                this.$el.msiPickHelper({
                    action: 'nmjyusho',
                    kind2: '3',
                    onSelect: select,
                    onClear: clear
                });
            }
        },
        // お伺い先ヘルパー処理
        hsSpotHelper2: function () {
            var m = this.model;
            var kind = m.get("hs_gyomu_kbn_2");

            var select = function (data) {
                m.set('hs_spot_cd_2', data.code);
                m.set('hs_spot_nm_2', data.name);
                m.set('hs_spot_kbn_2', data.jyusho_kbn);
            };
            var clear = function (data) {
                m.set('hs_spot_cd_2', null);
                m.set('hs_spot_nm_2', null);
                m.set('hs_spot_kbn_2', null);
            };
            // 3:自社会館
            if (kind === '3') {
                this.$el.msiPickHelper({
                    action: 'kaijyo',
                    mydata: {s_kaijyo_kbn: '5'},
                    onSelect: select,
                    onClear: clear
                });
            }
            if (kind === '2') {
                this.$el.msiPickHelper({
                    action: 'nmjyusho',
                    kind2: '1',
                    onSelect: select,
                    onClear: clear
                });
            }
            if (kind === '7') { // 火葬場
                this.$el.msiPickHelper({
                    action: 'nmjyusho',
                    kind2: '3',
                    onSelect: select,
                    onClear: clear
                });
            }
        },
        // 安置先ヘルパー処理
        anchiHelper: function () {
            var m = this.model;
            var kind = m.get("hs_gyomu_kbn2");
            var select = function (data) {
                m.set('hs_anchi_cd', data.code);
                m.set('hs_anchi_nm', data.name);
                m.set('hs_anchi_kbn', kind);
            };
            var clear = function (data) {
                m.set('hs_anchi_cd', null);
                m.set('hs_anchi_nm', null);
                m.set('hs_anchi_kbn', null);
            };
            // 3:自社会館
            if (kind === '3') {
                this.$el.msiPickHelper({
                    action: 'kaijyo',
                    mydata: {s_kaijyo_kbn: '5'},
                    onSelect: select,
                    onClear: clear
                });
            }
            if (kind === '2') {
                this.$el.msiPickHelper({
                    action: 'nmjyusho',
                    kind2: '1',
                    onSelect: select,
                    onClear: clear
                });
            }
            if (kind === '7') { // 火葬場
                this.$el.msiPickHelper({
                    action: 'nmjyusho',
                    kind2: '3',
                    onSelect: select,
                    onClear: clear
                });
            }
        },
        // 式場ヘルパー処理
        kaijyosikiHelper: function () {
            var actNm = 'kaijyosiki';
            var nitei = appcst.niteiCol.where({nitei_kbn: 7}); // 日程の葬儀                
            if (nitei.length === 1) {
                var bashoKbn = nitei[0].get("basho_kbn");
                if (bashoKbn === "5") { // 通夜会場の場合
                    actNm = 'kaijyotuya';
                }
            }
            var m = this.model;
            this.$el.msiPickHelper({
                action: actNm,
                onSelect: function (data) {
                    m.set('sk_kaijyo_cd', data.code);
                    m.set('sk_kaijyo_nm', data.name);
                    m.set('sk_shohin_cd', data.sk_shohin_cd);

                    // 会館利用区分
                    var kaikan_use = m.get("free3_kbn");
                    // 葬送儀礼またはプラン値引きの利用ありか
                    var dataGojokai = appcst.gojokaiMemberCol.filter(function (item) {
                        return item.get("yoto_kbn") === '1' || item.get("yoto_kbn") === '11';
                    });
                    // 互助会会員
                    if (dataGojokai.length > 0) {
                        if (kaikan_use === '1') { // 1会館利用
                            m.set('shikijo_shiyou_prc', data.sk_charge_mb);
                        } else if (kaikan_use === '2') { // 2会館利用
                            m.set('shikijo_shiyou_prc', data.sk_charge_all_mb);
                        }
                        // 一般
                    } else {
                        if (kaikan_use === '1') { // 1会館利用
                            m.set('shikijo_shiyou_prc', data.sk_charge);
                        } else if (kaikan_use === '2') { // 2会館利用
                            m.set('shikijo_shiyou_prc', data.sk_charge_all);
                        }
                    }
                },
                onClear: function () {
                    m.set('sk_kaijyo_cd', null);
                    m.set('sk_kaijyo_nm', null);
                    m.set('shikijo_shiyou_prc', null);
                    m.set('sk_shohin_cd', null);
                }
            });
        },
        setKaijyosikiStatus: function () {
            var code = this.model.get('sk_kaijyo_cd');
            if ($.msiJqlib.isNullEx2(code)) {
                this.$("#add_charge #total_1").attr("readonly", "readonly");
            } else {
                this.$("#add_charge #total_1").removeAttr("readonly");
            }
        },
        // 通夜会場ヘルパー処理
        kaijyotuyaHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'kaijyotuya',
                onSelect: function (data) {
                    m.set('ty_shohin_cd', data.ty_shohin_cd);
                    m.set('tuya_shikijo_tanka', data.ty_charge_1st);
                    m.set('tuya_shikijo_tanka2', data.ty_charge_2nd);
                    m.set('ty_kaijyo_cd', data.code);
                    m.set('ty_kaijyo_nm', data.name);
                },
                onClear: function () {
                    m.set('ty_kaijyo_nm', null);
                    m.set('tuya_paku_su', null);
                    m.set('tuya_shikijo_tanka', null);
                    m.set('tuya_paku_su2', null);
                    m.set('tuya_shikijo_tanka2', null);
                    m.set('ty_kaijyo_cd', null);
                    m.set('ty_shohin_cd', null);
                }
            });
        },
        setKaijyotuyaStatus: function () {
            var tnk = this.model.get('tuya_shikijo_tanka');
            var tnk2 = this.model.get('tuya_shikijo_tanka2');
            if ($.msiJqlib.isNullEx2(tnk) || tnk === '0') {
                this.$("#add_charge #nights, #add_charge #price_2").attr("readonly", "readonly");
            } else {
                this.$("#add_charge #nights, #add_charge #price_2").removeAttr("readonly");
            }
            if ($.msiJqlib.isNullEx2(tnk2) || tnk2 === '0') {
                this.$("#add_charge #nights2, #add_charge #price_3").attr("readonly", "readonly");
            } else {
                this.model.set('tuya_paku_su', 1);
                this.$("#add_charge #nights").attr("readonly", "readonly");
                this.$("#add_charge #nights2, #add_charge #price_3").removeAttr("readonly");
            }
//            if ($.msiJqlib.isNullEx2(tnk) && $.msiJqlib.isNullEx2(tnk2)) {
//                this.$("#add_charge #nights, #add_charge #price_2").removeAttr("readonly");
//            }
        },
        // 貸出備品ヘルパー処理
        itemHelper: function () {
            this.$el.msiPickHelper({
                action: 'bihin',
                mydata: {init_search: 1, no_cond: 1},
                onSelect: function (data) {
                    var kashidasi = new KashidasiModel();
                    kashidasi.set({'shohin_cd': data.code, 'shohin_nm': data.name, 'suryo': 1, 'nm_input_kbn': data.nm_input_kbn});
                    var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
                    if (nitei.length === 1) {
                        var nakunaribi = nitei[0].get("nitei_date");
                        if ($.msiJqlib.chkDate(nakunaribi)) { // 日付チェック
                            kashidasi.set("kaisyu_yotei_ymd", $.msiJqlib.addDays(nakunaribi, appcst.data.kaisya_info.kashidashi_kaisyu_day));
                        }
                    }
                    appcst.kashidasiCol.add(kashidasi);
                },
                onClear: function () {
                }
            });
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        }
    };
//    var AppView = $.extend(true, AppViewDef, $.customerFileup);
    // 全体ビュー
    var AppView = Backbone.View.extend($.extend(true, AppViewDef, $.customerFileup)); // AppView

    /**
     * @description 日程タブ処理
     */
    // 日程タブ明細モデル
    var NiteiModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                nitei_kbn: null, // 日程区分
                ts_based_nm: null, // 日程基本名
                ts_based_nm2: null, // 日程基本名2 
                nitei_ymd: null, // 日程タイムスタンプ
                nitei_date: null, // 日程日付のみ
                nitei_time: null, // 日程時刻のみ
                nitei_ed_ymd: null, // 日程終了
                nitei_ed_time: null, // 日程終了時刻
                spot_cd: null, // 場所区分コード
                basho_kbn: null, // 場所区分
                basho_cd: null, // 場所コード
                basho_nm: null, // 場所名
//                nyukan_kyo: '0', // 入棺経 0:なし １：前　２：後
                free1_kbn: null, // 火葬場時の式場区分
                v_free4: null, // 火葬場時の式場名(手入力)
                free2_kbn: null, // 初七日 1 戻り七日 2 付七日
//                syukan_kyo: '0', // 出棺経 0:なし　１：あり
                kaso_kyo: '0', // 火葬経 0:なし　１：あり
                shishikisha_ninzu: '1' // 司式者人数
            };
        },
        validation: {
            nitei_ymd: function (val, attr, computed) {
                if (computed.nitei_kbn === 7 && !$.msiJqlib.isNullEx2(appcst.appModel.get("jichu_kakute_ymd")) && $.msiJqlib.isNullEx2(computed.nitei_date)) {
                    return '見積確定後は施行日は必須です';
                }
                if ($.msiJqlib.isNullEx2(computed.nitei_date) && !$.msiJqlib.isNullEx2(computed.nitei_time)) {
                    return '日付と時刻の整合性がありません';
                }
//                if (!$.msiJqlib.isNullEx2(computed.nitei_date) && $.msiJqlib.isNullEx2(computed.nitei_time) && !(computed.nitei_kbn === 1)) {
//                    return '日付と時刻の整合性がありません';
//                }
                if (computed.nitei_kbn === 7 && !$.msiJqlib.isNullEx2(computed.nitei_date)
                        && !$.msiJqlib.isNullEx2(appcst.kfModel.get("ts_free1_date"))
                        && computed.nitei_date < appcst.kfModel.get("ts_free1_date")) {
                    return '告別式の日付は受注日以降になる必要があります';
                }
            },
            nitei_date: function (value) {
                return _chkYmd(value);
            },
            nitei_time: {
                required: false,
                pattern: 'time'
            },
            nitei_ed_ymd: function (val, attr, computed) {
                if ($.msiJqlib.isNullEx2(computed.nitei_date) && !$.msiJqlib.isNullEx2(computed.nitei_ed_time)) {
                    return '日付と時刻の整合性がありません';
                }
            },
            nitei_ed_time: {
                required: false,
                pattern: 'time'
            },
//            nitei_ed_time: {
//                required: false,
//                customFun: function (value) {
//                    if ($.msiJqlib.isNullEx2(this.get('nitei_date')) && !$.msiJqlib.isNullEx2(this.get('nitei_ed_time'))) {
//                        return '日付と時刻の整合性がありません';
//                    }
//                },
//                pattern: 'time'
//            },
            basho_nm: {
                required: function () {
                    var require = false;
                    if (this.get('nitei_kbn') === 7) {
                        if (this.get('basho_kbn') === '2') {
                            require = true;
                        }
                    }
                    return require;
                },
                //pattern: 'zenkaku',
                maxLength: 40
            }
        },
        labels: {
            basho_nm: '場所名'
        }
    }); // NiteiModel

    // 日程コレクション
    var NiteiCollection = Backbone.Collection.extend({
        model: NiteiModel
//        comparator: 'nitei_kbn'
    });

    // 日程ビュー
    var NiteiView = Backbone.View.extend({
        tagName: 'fieldset',
        //1:亡日 2:湯灌 3:入棺 4:通夜 5:出棺 6:火葬 7:葬儀 8:壇払
        tmpl1: _.template($('#tmpl-nitei-1').html()),
        tmpl2: _.template($('#tmpl-nitei-2').html()),
        tmpl3: _.template($('#tmpl-nitei-3').html()),
        tmpl4: _.template($('#tmpl-nitei-4').html()),
        tmpl5: _.template($('#tmpl-nitei-5').html()),
        tmpl6: _.template($('#tmpl-nitei-6').html()),
        tmpl7: _.template($('#tmpl-nitei-7').html()),
        tmpl8: _.template($('#tmpl-nitei-8').html()),
        events: {
            "click .basho_nm, .label.dlg_place": "nmjyushoHelper",
            "change .nitei_spot_cd": function () {
                this.model.set({'basho_cd': null, 'basho_nm': null});
                this.model.set({'free1_kbn': null, 'v_free4': null});
            },
            "select2-opening .cls_sikijo_cd": function () {
                // 式場を開いたときに火葬場場所の区分で絞り込んで表示する
                var fileredKbns = [];
                var niteiKbn = this.model.get("nitei_kbn");
                var bashoKbn = this.model.get("basho_kbn");
                var bashoCd = this.model.get("basho_cd");
                if ((niteiKbn === 4 || niteiKbn === 7) && bashoKbn === "7" && !$.msiJqlib.isNullEx2(bashoCd)) {
                    var orgKbns = $.msiJqlib.objToArray3(data.dataKbns.sikijo_kbn);
                    _.each(orgKbns, function (item) {
                        if (Number(item.kbn_value_cd_num) === Number(bashoCd)) {
                            fileredKbns.push(item);
                        }
                    });
                }
                appcst.sikijo_kbns = fileredKbns;
            }
        },
        bindings: {
            '.nitei_kbn_nm': 'nitei_kbn_nm',
            '.nitei_date': {
                observe: 'nitei_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('nitei_time'), 'nitei_ymd', this.model);
                    _setNiteiymd($el.val(), this.model.get('nitei_ed_time'), 'nitei_ed_ymd', this.model);
                    // 死亡日を設定
                    if (this.model.get('nitei_kbn') === 1) {
                        appcst.afterFollowModel.set('die_date', $el.val());
                    }
                    // 施行基本の葬儀日を設定
                    if (this.model.get('nitei_kbn') === 7) {
                        appcst.appModel.set('sougi_ymd', $el.val());
                        appcst.afterFollowModel.set('funeral_date', $el.val());
                        // 葬儀情報(ヘッダー)の葬儀日バインディング処理
                        $("#hd_mg_sogibi").text($el.val());
                    }
                    return $el.val();
                }

            },
            '.nitei_time': {
                observe: 'nitei_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('nitei_date'), $el.val(), 'nitei_ymd', this.model);
                    return $el.val();
                }
            },
            '.nitei_ed_time': {
                observe: 'nitei_ed_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('nitei_date'), $el.val(), 'nitei_ed_ymd', this.model);
                    return $el.val();
                }
            },
            '.nitei_spot_cd': {
                observe: 'spot_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'basho_kbn');
                    return $el.val();
                }
            },
            '.basho_nm': {
                observe: 'basho_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("basho_cd", null);
//                        this.model.set("basho_kbn", null);
                    }
                    return val;
                }
            },
            '.kyo_kbn_nm': 'kyo_kbn_nm',
//            '.nyukan_kyo': {
//                observe: 'nyukan_kyo',
//                afterUpdate: function($el, event, options) {
//                    _setSelect2Val($el, $el.val());
//                }
//            },
//            '.kigae': {
//                observe: 'free1_kbn',
//                afterUpdate: function ($el, event, options) {
//                    _setSelect2Val($el, $el.val());
//                }
//            },
//            '.nanoka': {
//                observe: 'free2_kbn',
//                afterUpdate: function ($el, event, options) {
//                    _setSelect2Val($el, $el.val());
//                }
//            },
            '.nanoka': {
                observe: 'v_free3',
                afterUpdate: function ($el, event, options) {
                    var vals = $el.val().split(',');
                    _setSelect2Val($el, vals);
                },
                getVal: function ($el, event, options) {
                    return $el.val();
                }
            },
//            '.syukan_kyo': {
//                observe: 'syukan_kyo',
//                afterUpdate: function($el, event, options) {
//                    _setSelect2Val($el, $el.val());
//                }
//            },
            '.kaso_kyo': {
                observe: 'kaso_kyo',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '.shishikisha_ninzu': {
                observe: 'shishikisha_ninzu',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '.kaso_sikijo': {
                observe: 'free1_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    var d = $.msiJqlib.getSelect2Data($el);
                    if ($.msiJqlib.isNullEx2(d)) {
                        this.model.set("v_free4", null);
                    } else {
                        this.model.set("v_free4", d.text);
                    }
                    return $el.val();
                }
            },
            '.sikijo_nm_other': 'v_free4'
        },
        initialize: function () {
            this.listenTo(this.model, 'change:spot_cd', this.setPickupNmStatus);
            this.listenTo(this.model, 'change:basho_nm change:spot_cd', this.setBashoNm);
            this.listenTo(this.model, 'change:basho_nm', this.setSikijoBeforeClear);
            this.listenTo(this.model, 'change:nitei_date', function () {
                if (this.model.get('nitei_kbn') === 1) { // 1:亡日
                    var nakunaribi = this.model.get("nitei_date");
                    if ($.msiJqlib.chkDate(nakunaribi)) { // 日付チェック
                        // 故人年齢計算
                        appcst.appView.calcNereiK();
                        _.each(appcst.kashidasiCol.models, function (m) {
                            m.set("kaisyu_yotei_ymd", $.msiJqlib.addDays(nakunaribi, appcst.data.kaisya_info.kashidashi_kaisyu_day));
                        });
                    } else {
                        // 享年をクリア
                        appcst.appModel.set("k_nenrei_kyounen", null);
                    }
                }
            });
            this.listenTo(this.model, 'change:nitei_date', this.setNiteiDate);
            this.listenTo(this.model, 'change:nitei_ed_time', this.setNiteiTime);
            Backbone.Validation.bind(this);
        },
        render: function () {
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            // 場所区分データ
            var niteiSpotKbns = {};
            // 日程区分
            var niteiKbn = this.model.get("nitei_kbn");
            if (niteiKbn === 1) {// 亡日
                this.$el.html(this.tmpl1(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.hs_gyomu_kbn;
            } else if (niteiKbn === 2) { // 湯灌
                this.$el.html(this.tmpl2(this.model.toJSON()));
//                $.msiJqlib.setSelect2Com1(this.$(".timing"));
            } else if (niteiKbn === 3) {// 入棺
                this.$el.html(this.tmpl3(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 4) { // 通夜
                this.$el.html(this.tmpl4(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 5) {// 出棺
                this.$el.html(this.tmpl5(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 6) {// 火葬
                this.$el.html(this.tmpl6(this.model.toJSON()));
            } else if (niteiKbn === 7) {// 葬儀
                this.$el.html(this.tmpl7(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 8) {// 壇払
                this.$el.html(this.tmpl8(this.model.toJSON()));
//                niteiSpotKbns = data.dataKbns.dan_kaijo_kbn;
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            }
            this.$(".nitei_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            if (niteiKbn === 1 || niteiKbn === 5 || niteiKbn === 3) {
                this.$(".nitei_time, .nitei_ed_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            } else if (niteiKbn === 4 || niteiKbn === 7) {
                this.$(".nitei_time, .nitei_ed_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault2, {minuteGrid: 5, stepMinute: 5}));
            } else {
                this.$(".nitei_time, .nitei_ed_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault2));
            }

            this.stickit();

            // 式場区分
            appcst.sikijo_kbns = $.msiJqlib.objToArray3(data.dataKbns.sikijo_kbn);
            $.msiJqlib.setSelect2Com1(this.$(".kaso_sikijo"), ($.extend({data: function () {
                    return {results: appcst.sikijo_kbns};
                }}, $.msiJqlib.setSelect2Default2)));
            // 入棺経
//            $.msiJqlib.setSelect2Com1(this.$(".nyukan_kyo"), {data: $.msiJqlib.objToArray3(data.dataKbns.nyukan_kyo)});
            // 納棺着替
            $.msiJqlib.setSelect2Com1(this.$(".kigae"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kigae)}, $.msiJqlib.setSelect2Default1)));
            // 出棺経
//            $.msiJqlib.setSelect2Com1(this.$(".syukan_kyo"), {data: $.msiJqlib.objToArray3(data.dataKbns.syukan_kyo)});
            // 火葬経
            $.msiJqlib.setSelect2Com1(this.$(".kaso_kyo"), {data: $.msiJqlib.objToArray3(data.dataKbns.kaso_kyo)});
            // 初七日
            $.msiJqlib.setSelect2Com1(this.$(".nanoka"),
                    ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.nanoka)}, $.msiJqlib.setSelect2Default2, {multiple: true, placeholder: '(複数可)', maximumSelectionSize: 5})));
            // 司会者
            $.msiJqlib.setSelect2Com1(this.$(".shishikisha_ninzu"), {data: _shishikisha_ninzu});
            // 場所区分
            $.msiJqlib.setSelect2Com1(this.$(".nitei_spot_cd"), ($.extend({data: $.msiJqlib.objToArray3(niteiSpotKbns)}, $.msiJqlib.setSelect2Default1)));
            this.setPickupNmStatus();
            this.setSikijo();
            return this;
        },
        // 名所住所ヘルパー処理
        nmjyushoHelper: function (e) {
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var kind = $target.data("kind2");
            if ($.msiJqlib.isNullEx2(kind)) {
                return;
            }
            var actionNm = 'nmjyusho';
            var action = $target.data("action");
            if (!$.msiJqlib.isNullEx2(action)) {
                actionNm = action;
            }
            var hanso_kbn = null;
            var s_hanso_kbn = $target.data("s_hanso_kbn");
            if (!$.msiJqlib.isNullEx2(s_hanso_kbn)) {
                hanso_kbn = s_hanso_kbn;
            }
            var m = this.model;
            this.$el.msiPickHelper({
                action: actionNm,
                kind2: kind,
                mydata: {s_hanso_kbn: hanso_kbn},
                onSelect: function (data) {
                    m.set("basho_cd", data.code);
                    m.set("basho_nm", data.name);
                },
                onClear: function () {
                    m.set("basho_cd", null);
                    m.set("basho_nm", null);
                }
            });
        },
        // 場所区分切り替え処理
        setPickupNmStatus: function () {
            // 日程区分 1:亡日 2:湯灌 3:入棺 4:通夜 5:出棺 6:火葬 7:葬儀 8:壇払
            var niteiKbn = this.model.get("nitei_kbn");
            if ($.inArray(niteiKbn, [1, 3, 4, 5, 7, 8]) >= 0) {
                // 場所区分
                var bashoKbn = this.model.get('basho_kbn');

                if (((niteiKbn === 5 || niteiKbn === 7 || niteiKbn === 4) && bashoKbn === "2")) {// 7:葬儀・8:壇払のホールは、会場マスタを設定する
                    this.$(".place").data("action", 'kaijyosiki');
                } else if ((niteiKbn === 3 || niteiKbn === 8) && bashoKbn === "2") {// 納棺の自社会館
                    this.$(".place").data("action", 'kaijyo');
                } else if (((niteiKbn === 3 || niteiKbn === 5 || niteiKbn === 7 || niteiKbn === 4 || niteiKbn === 8) && bashoKbn === "5")) { // 3:入棺・5:出棺・7:葬儀かつ5:通夜は、通夜会場マスタを設定する
                    this.$(".place").data("action", 'kaijyotuya');
                } else if ((niteiKbn === 8 && bashoKbn === "4")) { // 8:初七日、4:法事会館
                    this.$(".place").data("action", 'kaijyohouji');
                } else {
                    this.$(".place").data("action", null);
                }
                // pickname入力可能区分
                var disable = false;
                if ((niteiKbn === 7 || niteiKbn === 4) && (bashoKbn === "3" || bashoKbn === "4" || bashoKbn === "6")) { // 7:葬儀の場所区分 3:他会館 4:公民館 6:他寺院
                    disable = true;
                }
//                if (niteiKbn === 8 && (bashoKbn === "2" || bashoKbn === "3")) { // 8:壇払の場所区分 2:他会館 3:公民館
//                    disable = true;
//                }
                _setSpotStatus(bashoKbn, this.$(".place"), this.$(".dlg_place"), disable);
                if (bashoKbn === '0' || bashoKbn === '9' || disable) { // 0:自宅と9:その他はpickup無し
                    bashoKbn = null;
                }
                this.$(".place").data("s_hanso_kbn", "");
                if (niteiKbn !== 1 && bashoKbn === "7") { // 7:火葬場（斎場）
                    this.$(".place").data("kind2", "3");
                    this.$(".place").data("s_hanso_kbn", bashoKbn);
                } else if (bashoKbn === "8") {
                    this.$(".place").data("kind2", "12");
                } else if (bashoKbn === "10") {
                    this.$(".place").data("kind2", "13");
                } else {
                    this.$(".place").data("kind2", bashoKbn);
                }
                if ((niteiKbn === 4 || niteiKbn === 7) && bashoKbn === "7") { // 7:火葬場（斎場）
                    this.$(".cls_sikijo_cd, .sikijo_nm_other").removeAttr("disabled");
                } else {
                    this.$(".cls_sikijo_cd, .sikijo_nm_other").attr("disabled", "disabled");
                }
            }
        },
        setSikijoBeforeClear: function () {
            this.model.set("v_free4", null);
            this.model.set("free1_kbn", null);
            this.setSikijo();
        },
        setSikijo: function () {
            var niteiKbn = this.model.get("nitei_kbn");
            var bashoKbn = this.model.get("basho_kbn");
            var bashoCd = this.model.get("basho_cd");
            if ((niteiKbn === 4 || niteiKbn === 7) && bashoKbn === "7") {
                if ((bashoCd === '00001' || bashoCd === '00002') || $.msiJqlib.isNullEx2(bashoCd)) {
                    this.$(".cls_sikijo_cd").show();
                    this.$(".sikijo_nm_other").hide();
//                    this.model.set("v_free4",null);

                } else {
                    this.$(".cls_sikijo_cd").hide();
                    this.$(".sikijo_nm_other").show();
//                    this.model.set("free1_kbn",null);
                }
            }

        },
        setBashoNm: function () {
            var t = this;
            var cm = this.model;
            var cur_nitei_kbn = cm.get("nitei_kbn");
            if ($.inArray(cur_nitei_kbn, [4, 5, 7]) >= 0) { // // 4:通夜 5:出棺 7:葬儀 
                _.each(appcst.niteiCol.models, function (m) {
                    var niteiKbn = m.get("nitei_kbn");
                    if ($.inArray(niteiKbn, [4, 5, 7]) >= 0 && cur_nitei_kbn !== niteiKbn) { // 4:通夜 5:出棺 7:葬儀 
                        var orgNitei = t.getOrgNitei(niteiKbn);
                        if ((!$.msiJqlib.isNullEx2(orgNitei) && $.msiJqlib.isNullEx2(orgNitei.basho_kbn))) {
                            var opt = {};
                            m.set("basho_kbn", cm.get("basho_kbn"), opt);
                            m.set("spot_cd", cm.get("spot_cd"), opt);
                            m.set("basho_cd", cm.get("basho_cd"), opt);
                            m.set("basho_nm", cm.get("basho_nm"), opt);
                        }
                    }
                });
            }
        },
        getOrgNitei: function (kbn) {
            var ret = null;
            _.each(orgDataNiteiCol, function (o) {
                if (o.nitei_kbn === kbn) {
                    ret = o;
                }
            });
            return ret;
        },
        setNiteiDate: function () {
            var t = this;
            var cm = this.model;
            var cur_nitei_kbn = cm.get("nitei_kbn");
            if ($.inArray(cur_nitei_kbn, [5, 6, 7, 8]) >= 0) { // 5:出棺 6:火葬 7:葬儀 8:初七日法要 
                _.each(appcst.niteiCol.models, function (m) {
                    var niteiKbn = m.get("nitei_kbn");
                    if ($.inArray(niteiKbn, [5, 6, 7, 8]) >= 0 && cur_nitei_kbn !== niteiKbn) { // 5:出棺 6:火葬 7:葬儀 8:初七日法要 
                        var orgNitei = t.getOrgNitei(niteiKbn);
                        if ((!$.msiJqlib.isNullEx2(orgNitei) && $.msiJqlib.isNullEx2(orgNitei.nitei_date))) {
                            var opt = {};
                            m.set("nitei_date", cm.get("nitei_date"), opt);
                            if (cm.get("nitei_date") === "") {
                                m.set("nitei_ymd", null, opt);
                            } else {
                                m.set("nitei_ymd", cm.get("nitei_date"), opt);
                            }
                        }
                    }
                });
            }
            if (cur_nitei_kbn === 7) {
                appcst.appModel.set('sougi_ymd', cm.get("nitei_date"));
            }
        },
        setNiteiTime: function () {
            var t = this;
            var cm = this.model;
            var cur_nitei_kbn = cm.get("nitei_kbn");
            if ($.inArray(cur_nitei_kbn, [5, 7]) >= 0) { // 5:出棺 7:葬儀
                _.each(appcst.niteiCol.models, function (m) {
                    var niteiKbn = m.get("nitei_kbn");
                    if ($.inArray(niteiKbn, [5, 7]) >= 0 && cur_nitei_kbn !== niteiKbn) { // 5:出棺 7:葬儀
                        var orgNitei = t.getOrgNitei(niteiKbn);
                        if ((!$.msiJqlib.isNullEx2(orgNitei) && $.msiJqlib.isNullEx2(orgNitei.nitei_time))) {
                            var opt = {};
                            var ymd = m.get("nitei_date") + " " + cm.get("nitei_ed_time");
                            m.set("nitei_time", cm.get("nitei_ed_time"), opt);
                            m.set("nitei_ymd", ymd, opt);
                        }
                    }
                });
            }
        }
    }); // NiteiView

    /**
     * @description 貸出備品タブ処理
     */
    // 貸出備品モデル
    var KashidasiModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                disp_no: null, // 表示順序
                shohin_cd: null, // 商品コード
                shohin_nm: null, // 商品名
                kashidashi_kbn: 2, // 貸出区分
                suryo: null, // 貸出数量
                nm_input_kbn: null, // 名称入力区分
                kaisyu_yotei_ymd: null, // 回収予定日
                up_flg: 1, // 更新フラグ　0:更新対象外 1:更新対象
                data_status: 1 // データ状態 0:マスタデータ 1:画面新規入力データ 2:トランデータ
            };
        },
        validation: {
            kaisyu_yotei_ymd: function (value) {
                return _chkYmd(value);
            }
        },
        labels: {
            kaisyu_yotei_ymd: '回収予定日'
        }
    }); // KashidasiModel

    // 貸出備品コレクション
    var KashidasiCollection = Backbone.Collection.extend({
        model: KashidasiModel,
        comparator: 'disp_no'
    });

    // 貸出備品ビュー
    var KashidasiView = Backbone.View.extend({
        tagName: 'li',
        className: 'item',
        tmpl: _.template($('#tmpl-kashidasi-bihin').html()),
        events: {
            "click .return": function (e) {
                $(e.currentTarget).datepicker("show");
            },
            "click .plus": "doPlus",
            "click .minus": "doMinus",
            "click .delete": "doDelete"
        },
        bindings: {
            '.name': 'shohin_nm',
            '.num': 'suryo',
            '.return': 'kaisyu_yotei_ymd'
        },
        initialize: function () {
            this.listenTo(this.model, 'destroy', this.remove);
            this.listenTo(this.model, 'change', function () {
                this.model.set('up_flg', 1);
            });
            Backbone.Validation.bind(this);
            this.render();
        },
        render: function () {
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.$el.html(this.tmpl(this.model.toJSON()));
            this.$(".return").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.stickit();
            return this;
        },
        doPlus: function () {
            var $num = this.$('.num');
            var n = parseInt($num.text());
            n++;
            $num.text(n);
            this.model.set('suryo', n);
        },
        doMinus: function () {
            var $num = this.$('.num');
            var n = parseInt($num.text());
            if (n > 0) {
                n--;
                $num.text(n);
            }
            this.model.set('suryo', n);
        },
        doDelete: function () {
            // 備品データの場合は削除コレクションに追加する
            kashidasiDelCol.add(this.model.clone());
            this.model.destroy();
        }

    }); // KashidasiView
    // 施行基本フリーモデル
    var KihonFreeModel = Backbone.Model.extend({
        defaults: function () {
            return {
                // 施行受付メモ
                ts_free1: null, // 受付日
                ts_free1_date: null, // 受付日(日付のみ)
                ts_free1_time: null, // 受付日(時間のみ)
                tanto_cd1: null, // 受付者
                tanto_nm1: null, // 受付者お名前
                v_free1: null, // 故人様のお名前
                v_free2: null, // 故人様のお名前カナ
                zip_no1: null, // 現住所郵便番号
                addr1_1: null, // 現住所1
                addr1_2: null, // 現住所2
                tel_no1: null, // 現住所電話番号
                free8_code_cd: "1", // 現住所世帯主コード
                free8_kbn: "1", // 現住所世帯主区分
                ts_free5: null, // 死亡日
                ts_free5_date: null, // 死亡日(日付のみ)
                ts_free5_time: null, // 死亡日(時間のみ)
                free_kbn4: null, // 死亡日が日付のみは1
                v_free3: null, // 病棟
                v_free6: null, // 階
                ts_free2: null, // お迎え予定時間
                ts_free2_date: null, // お迎え予定時間(日付のみ)
                ts_free2_time: null, // お迎え予定時間(時間のみ)
                ts_free3: null, // お迎え予定時間2
                ts_free3_date: null, // お迎え予定時間2(日付のみ)
                ts_free3_time: null, // お迎え予定時間2(時間のみ)
                ts_free4: null, // お迎え予定時間3
                ts_free4_date: null, // お迎え予定時間3(日付のみ)
                ts_free4_time: null, // お迎え予定時間3(時間のみ)
                free1_code_cd: null, // 亡くなられた場所コード
                free1_kbn: null, // 亡くなられた場所区分
                free1_place_kbn: null, // 亡くなられた場所場所区分
                free1_place_nm: null, // 亡くなられた場所場所
                v_free4: null, // 連絡者お名前
                free2_code_cd: null, // 連絡者続柄
                free3_code_cd: null, // はせがわ
                free4_code_cd: null, // 阿部石材店
                free5_code_cd: null, // 手続
                free6_code_cd: null, // 川崎清風霊園
                free7_code_cd: null, // ゆいまーる
                free9_code_cd: null, // 石蔵
                v_free5: null, // 連絡者携帯番号
                v_free19: null, // 駐車人員依頼書書の発注先コード
                free_kbn11: null, // 位牌区分1
                free_kbn12: null, // 位牌区分2
                v_free21: null, // お勤め
                v_free22: null, // 法名・戒名
                v_free23: null, // 警察署コード
                v_free24: null, // 警察署名
                v_free25: null, // 警察署担当者名
                biko2: null, // 連絡事項
                tanto_cd5: null, // 搬送者4の二番目担当コード
                tanto_cd6: null, // 搬送者5の二番目担当コード
                free_kbn8: null, // 施行依頼区分
                v_free26: null, // 施行依頼詳細
                free_kbn9: null, // 紹介区分
                v_free27: null, // 紹介区分詳細
                tanto_cd7: null, // 通夜チーフ
                tanto_nm7: null, // 通夜チーフお名前
                tanto_cd8: null, // 通夜サブ
                tanto_nm8: null, // 通夜サブお名前
                free11_code_cd: null, // 通夜他人数
                tanto_cd9: null, // 葬儀チーフ
                tanto_nm9: null, // 葬儀チーフお名前
                tanto_cd10: null, // 葬儀サブ
                tanto_nm10: null, // 葬儀サブお名前
                free12_code_cd: null, // 葬儀他人数
            };
        },
        validation: {
            ts_free1_date: {
                required: false,
                customFun: function (value) {
                    return _chkYmd(value);
                }
            },
            ts_free1_time: {
                required: false,
                pattern: 'time'
            },
            tanto_nm1: {
                required: false
            },
            v_free1: {
                required: false
            },
            zip_no1: {
                required: false,
                pattern: 'zip'
            },
            addr1_1: {
                required: false
            },
            tel_no1: {
                required: false,
                pattern: 'tel'
            },
            ts_free5_date: {
                required: function () {
                    return false;
//                    var moushi_cd = appcst.appModel.get("moushi_cd");
//                    if (moushi_cd === MOUSHI_KBN_SEIZEN || moushi_cd === MOUSHI_KBN_SAMPLE || moushi_cd === MOUSHI_KBN_ORDERMADE) {
//                        return false;
//                    } else {
//                        return true;
//                    }
//                    return this.get("free1_code_cd") !== '02';
                },
                customFun: function (value) {
                    return _chkYmd(value);
                }
            },
            ts_free5_time: {
                required: false,
                pattern: 'time'
            },
            free1_code_cd: {
                required: false
            },
            free1_place_nm: {
//                required: function () {
//                    return this.get("free1_code_cd") !== '02';
//                }
                required: false
            },
            ts_free2: {
                required: false,
                pattern: 'timestamp2',
                msg: '日付と時刻の整合性がありません'
            },
            ts_free2_date: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
            ts_free2_time: {
                required: false,
                pattern: 'time'
            },
            ts_free3: {
                required: false,
                pattern: 'timestamp2',
                msg: '日付と時刻の整合性がありません'
            },
            ts_free3_date: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
            ts_free3_time: {
                required: false,
                pattern: 'time'
            },
            ts_free4: {
                required: false,
                pattern: 'timestamp2',
                msg: '日付と時刻の整合性がありません'
            },
            ts_free4_date: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
            ts_free4_time: {
                required: false,
                pattern: 'time'
            }
        },
        labels: {
            ts_free1: '受付日',
            ts_free1_date: '受付日',
            ts_free1_time: '受付時間',
            tanto_nm1: '受付者',
            v_free1: '故人様のお名前',
            zip_no1: '現住所郵便番号',
            addr1_1: '現住所',
            tel_no1: '現住所電話番号',
            ts_free5_date: '死亡日',
            ts_free5_time: '死亡時間',
            free1_code_cd: '亡くなられた場所区分',
            free1_place_nm: '亡くなられた場所',
            ts_free2: 'お迎え予定日',
            ts_free2_date: 'お迎え予定日',
            ts_free2_time: 'お迎え予定時間',
            ts_free3: 'お迎え予定日',
            ts_free3_date: 'お迎え予定日',
            ts_free3_time: 'お迎え予定時間',
            ts_free4: 'お迎え予定日',
            ts_free4_date: 'お迎え予定日',
            ts_free4_time: 'お迎え予定時間',
            v_free4: '連絡者お名前',
            v_free5: '連絡者携帯番号',
            free_kbn8: '施行依頼',
            v_free26: '施行依頼',
            free_kbn9: '紹介区分',
            v_free27: '紹介区分',
        },
        validateSeinengappiT: function (value, attr, computedState) {
            var gengo = computedState.free5_code_cd;
            if (!$.msiJqlib.isNullEx2(value)) {
                return appcst.validateSeinengappi(gengo, value);
            }
        }
    });

    // 施行基本フリービュー
    var KihonFreeView = Backbone.View.extend({
        el: $("#customer-tab"),
        events: {
            "click  .label.dlg_uketuke_danto": "uketukeHelper",
            "click  .label.dlg_tanto_cd7": "tsuyachiefHelper",
            "click  .label.dlg_tanto_cd8": "tsuyasubHelper",
            "click  .label.dlg_tanto_cd9": "sougichiefHelper",
            "click  .label.dlg_tanto_cd10": "sougisubHelper",
            "click #sibo_basho_name, .label.dlg_sibo_basho": "siboBashoHelper",
            "click .label.dlg_keisatsu_basho": "keisatsuHelper",
            "click input.annai_check_set": "doRdoClick",
            "change #sibo_basho": function () {
                this.model.set({'free1_place_kbn': null, 'free1_place_nm': null});
            }
        },
        bindings: {
            '#uketuke_date': {
                observe: 'ts_free1_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('ts_free1_time'), 'ts_free1', this.model);
                    return $el.val();
                }
            },
            '#uketuke_time': {
                observe: 'ts_free1_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('ts_free1_date'), $el.val(), 'ts_free1', this.model);
                    return $el.val();
                }
            },
            '#uketuke_danto': 'tanto_nm1',
            '#uketuke_kojin_name': 'v_free1',
            '#uketuke_kojin_kana': 'v_free2',
            '#u_zip_1': 'zip_no1',
            '#u_address_1_1': 'addr1_1',
            '#u_address_1_2': 'addr1_2',
            '#u_tel_1': 'tel_no1',
            '#u_head_1': {
                observe: 'free8_code_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'free8_kbn');
                    return $el.val();
                }
            },
            '#sibo_date': {
                observe: 'ts_free5_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('ts_free5_time'), 'ts_free5', this.model);
                    return $el.val();
                }
            },
            '#sibo_time': {
                observe: 'ts_free5_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('ts_free5_date'), $el.val(), 'ts_free5', this.model);
                    return $el.val();
                }
            },
//            '#kojin_nenrei': 'i_free1',
            '#byouto': 'v_free3',
            '#byouto_gai': 'v_free6',
            '#pickup_date': {
                observe: 'ts_free2_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('ts_free2_time'), 'ts_free2', this.model);
                    return $el.val();
                }
            },
            '#pickup_time': {
                observe: 'ts_free2_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('ts_free2_date'), $el.val(), 'ts_free2', this.model);
                    return $el.val();
                }
            },
            '#pickup_date2': {
                observe: 'ts_free3_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('ts_free3_time'), 'ts_free3', this.model);
                    return $el.val();
                }
            },
            '#pickup_time2': {
                observe: 'ts_free3_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('ts_free3_date'), $el.val(), 'ts_free3', this.model);
                    return $el.val();
                }
            },
            '#pickup_date3': {
                observe: 'ts_free4_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('ts_free4_time'), 'ts_free4', this.model);
                    return $el.val();
                }
            },
            '#pickup_time3': {
                observe: 'ts_free4_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('ts_free4_date'), $el.val(), 'ts_free4', this.model);
                    return $el.val();
                }
            },
            '#sibo_basho': {
                observe: 'free1_code_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'free1_kbn');
                    return $el.val();
                }
            },
            '#sibo_basho_name': {
                observe: 'free1_place_nm'
            },
            '#renraku_name': 'v_free4',
            '#renraku_zoku': {
                observe: 'free2_code_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'free2_kbn');
                    return $el.val();
                }
            },
            '#renraku_tel': 'v_free5',
            '#infomisc-tab #chusya_hachu': {
                observe: 'v_free19',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    return $el.val();
                }
            },
            '#ihai_kbn1': {
                observe: 'free_kbn11',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#ihai_kbn2': {
                observe: 'free_kbn12',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#otsutome': 'v_free21',
            '#homyo_kaimyo': 'v_free22',
            '#keisatsu': 'v_free24',
            '#keisatsu_tantosya': 'v_free25',
            '#renraku_jiko': 'biko2',
            "input[name='annai_1_kbn']": _getRadioBinding('free3_code_cd'),
            "input[name='annai_2_kbn']": _getRadioBinding('free4_code_cd'),
            "input[name='annai_3_kbn']": _getRadioBinding('free5_code_cd'),
            "input[name='annai_4_kbn']": _getRadioBinding('free6_code_cd'),
            "input[name='annai_5_kbn']": _getRadioBinding('free7_code_cd'),
            "input[name='annai_6_kbn']": _getRadioBinding('free9_code_cd'),
            '#seko_irai': {
                observe: 'free_kbn8',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#shokai_kbn': {
                observe: 'free_kbn9',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#irai_detail': 'v_free26',
            '#shokai_kbn_detail': 'v_free27',
            '#shokai_dantai_mei': 'v_free28',
            '#shokai_kojin_mei': 'v_free29',
            '#tanto_cd7': 'tanto_nm7',
            '#tanto_cd8': 'tanto_nm8',
            '#tanto_cd9': 'tanto_nm9',
            '#tanto_cd10': 'tanto_nm10',
            '#free11_code_cd': {
                observe: 'free11_code_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#free12_code_cd': {
                observe: 'free12_code_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
        },
        // ラジオボタンクリック処理
        doRdoClick: function (e) {
            var $target = $(e.currentTarget);
            var attr = $target.parent("span").data("ref_attr");
            if (!$.msiJqlib.isNullEx2(attr)) {
                if ($target.hasClass("onCheck")) {
                    $target.attr("checked", false);
                    $target.button("refresh");
                    $target.parent().find("input").removeClass("onCheck");
                    this.model.set(attr, null);
                } else {
                    $target.parent().find("input").removeClass("onCheck");
                    $target.toggleClass("onCheck");
                }

            }
        },
        initialize: function () {
            this.listenTo(appcst.kfModel, 'change:free1_code_cd', this.setSiboBashoStatus);
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.listenTo(appcst.kfModel, 'change:v_free1', function (a, v) {
                appcst.appModel.set("k_nm", v);
            });
            this.listenTo(appcst.kfModel, 'change:v_free2', function (a, v) {
                appcst.appModel.set("k_knm", v);
            });
            this.listenTo(appcst.kfModel, 'change:zip_no1', function (a, v) {
                appcst.appModel.set("kg_yubin_no", v);
            });
            this.listenTo(appcst.kfModel, 'change:addr1_1', function (a, v) {
                appcst.appModel.set("kg_addr1", v);
            });
            this.listenTo(appcst.kfModel, 'change:addr1_2', function (a, v) {
                appcst.appModel.set("kg_addr2", v);
            });
            this.listenTo(appcst.kfModel, 'change:tel_no1', function (a, v) {
                appcst.appModel.set("kg_tel", v);
            });
            this.listenTo(appcst.kfModel, 'change:free8_code_cd', function (a, v) {
                appcst.appModel.set("kg_setai_cd", v);
                appcst.appModel.set("kg_setai_kbn", v);
            });
            this.listenTo(appcst.kfModel, 'change:free1_place_nm', function (a, v) {
                var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
                if (nitei.length === 1) {
                    nitei[0].set("basho_nm", v);
                }
            });
            this.listenTo(appcst.kfModel, 'change:ts_free5', function (a, v) {
                var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
                if (nitei.length === 1) {
                    nitei[0].set("nitei_ymd", v);
                }
            });
            this.listenTo(appcst.kfModel, 'change:ts_free5_date', function (a, v) {
                var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
                if (nitei.length === 1) {
                    nitei[0].set("nitei_date", v);
                }
            });
            this.listenTo(appcst.kfModel, 'change:ts_free5_time', function (a, v) {
                var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
                if (nitei.length === 1) {
                    nitei[0].set("nitei_time", v);
                }
            });
            this.listenTo(appcst.kfModel, 'change:free1_code_cd', function (a, v) {
                var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
                if (nitei.length === 1) {
                    nitei[0].set("spot_cd", v);
                    nitei[0].set("basho_cd", null);
                    nitei[0].set("basho_nm", null);
                }
            });
            this.listenTo(appcst.kfModel, 'change:free1_kbn', function (a, v) {
                var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
                if (nitei.length === 1) {
                    nitei[0].set("basho_kbn", v);
                }
            });
            this.listenTo(this.model, 'change:free_kbn8', this.setIraiKbn);
            this.listenTo(this.model, 'change:free_kbn9', this.setShoukaiKbn);
            this.render();

        },
        render: function () {
            this.stickit();
            this.$("#uketuke_date, #sibo_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#uketuke_time, #sibo_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.$("#pickup_date, #pickup_date2, #pickup_date3, #iso_ts").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#pickup_time, #pickup_time2, #pickup_time3, #iso_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            /** 受付1*/
            // 亡くなられた場所
            $.msiJqlib.setSelect2Com1(this.$("#sibo_basho"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.hs_gyomu_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 連絡者続柄
            $.msiJqlib.setSelect2Com1(this.$("#renraku_zoku"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));

            // 駐車人員依頼書書の発注先
            $.msiJqlib.setSelect2Com1(this.$("#chusya_hachu"), ($.extend({data: data.dataKbns.chusya_hachu}, $.msiJqlib.setSelect2Default1)));
            // 施行依頼
            $.msiJqlib.setSelect2Com1(this.$("#seko_irai"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.seko_irai_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 紹介区分
            $.msiJqlib.setSelect2Com1(this.$("#shokai_kbn"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.shoukai_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 通夜他
            $.msiJqlib.setSelect2Com1(this.$("#free11_code_cd"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.tsu_sou_ninzu)}, $.msiJqlib.setSelect2Default1)));
            // 葬儀他
            $.msiJqlib.setSelect2Com1(this.$("#free12_code_cd"), ($.extend({data: $.msiJqlib.objToArray3(appcst.data.dataKbns.tsu_sou_ninzu)}, $.msiJqlib.setSelect2Default1)));

            this.setSiboBashoStatus();
            return this;
        },
        setIraiKbn: function () {
            if (this.model.get('free_kbn8') === '99') {
                this.$("#irai_detail").removeAttr("disabled");
            } else {
                this.$("#irai_detail").attr("disabled", "disabled");
                this.model.set('v_free26', null);
            }
        },
        setShoukaiKbn: function () {
            if (this.model.get('free_kbn9') === '99') {
                this.$("#shokai_kbn_detail").removeAttr("disabled");
            } else {
                this.$("#shokai_kbn_detail").attr("disabled", "disabled");
                this.model.set('v_free27', null);
            }
        },
        // 受付担当者ヘルパー処理 
        uketukeHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'tanto_cd1': data.code, 'tanto_nm1': data.name});
                },
                onClear: function () {
                    m.set({'tanto_cd1': null, 'tanto_nm1': null});
                }
            });
        },
        // 通夜チーフヘルパー処理 
        tsuyachiefHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'tanto_cd7': data.code, 'tanto_nm7': data.name});
                },
                onClear: function () {
                    m.set({'tanto_cd7': null, 'tanto_nm7': null});
                }
            });
        },
        // 通夜サブヘルパー処理 
        tsuyasubHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'tanto_cd8': data.code, 'tanto_nm8': data.name});
                },
                onClear: function () {
                    m.set({'tanto_cd8': null, 'tanto_nm8': null});
                }
            });
        },
        // 葬儀チーフヘルパー処理 
        sougichiefHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'tanto_cd9': data.code, 'tanto_nm9': data.name});
                },
                onClear: function () {
                    m.set({'tanto_cd9': null, 'tanto_nm9': null});
                }
            });
        },
        // 葬儀サブヘルパー処理 
        sougisubHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'tanto_cd10': data.code, 'tanto_nm10': data.name});
                },
                onClear: function () {
                    m.set({'tanto_cd10': null, 'tanto_nm10': null});
                }
            });
        },
        // 亡くなられた場所ヘルパー処理
        siboBashoHelper: function () {
            var m = this.model;
            var kind = m.get("free1_kbn");
            var nitei = appcst.niteiCol.where({nitei_kbn: 1}); // 日程の亡日
            var select = function (data) {
                m.set('free1_place_kbn', data.code);
                m.set('free1_place_nm', data.name);
                if (nitei.length === 1) {
                    nitei[0].set("basho_cd", data.code);
                    nitei[0].set("basho_nm", data.name);
                }
            };
            var clear = function (data) {
                m.set('free1_place_kbn', null);
                m.set('free1_place_nm', null);
                if (nitei.length === 1) {
                    nitei[0].set("basho_cd", null);
                    nitei[0].set("basho_nm", null);
                }
            };
            // 5:病院 6:警察 7:施設
            if (kind === '5' || kind === '6' || kind === '7') {
                this.$el.msiPickHelper({
                    action: 'nmjyusho',
                    kind2: kind,
                    onSelect: select,
                    onClear: clear
                });
            }
        },
        // 警察ヘルパー処理
        keisatsuHelper: function (e) {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'nmjyusho',
                kind2: '6',
                onSelect: function (data) {
                    m.set("v_free23", data.code);
                    m.set("v_free24", data.name);
                },
                onClear: function () {
                    m.set("v_free23", null);
                    m.set("v_free24", null);
                }
            });
        },
        // 亡くなられた場所select2切り替え処理
        setSiboBashoStatus: function () {
            var val = this.model.get('free1_kbn');
            _setSpotStatus(val, this.$("#sibo_basho_name"), this.$(".dlg_sibo_basho"));
            if (val === '5') {
                this.$("#byouto, #byouto_gai").removeAttr("disabled");
            } else {
                this.$("#byouto, #byouto_gai").attr("disabled", "disabled");
                appcst.kfModel.set('v_free3', null);
                appcst.kfModel.set('v_free6', null);
            }
        }
    });

    msiGlobalObj.markObj.mark('start');
    // データ取得
    try {
        // DBから取得したJsonデータ
        var data = JSON.parse(_.unescape($('#data-customerinfo').text()));
        //console.log(JSON.stringify(mydata.dataSekoDtlCol))
    } catch (e) {
        console.log('JSON error. ' + e);
        $.msiJqlib.showErr('JSON error. ' + e);
    }
    appcst.data = data;
    // コントローラー名
    appcst.controllerName = data.controllerName;
    // 日程タブ初期化
    appcst.niteiCol = new NiteiCollection();
    // 互助会タブ初期化
    appcst.gojokaiInfoModel = new appgjk.GojokaiInfoModel();
    appcst.gojokaiMemberCol = new appgjk.GojokaiMemberCollection();
    appcst.gojokaiInfoView = new appgjk.GojokaiInfoView({model: appcst.gojokaiInfoModel});
    // 貸出備品タブ初期化
    appcst.kashidasiCol = new KashidasiCollection();
    // 喪主タブ請求先初期化処理
    appcst.sekyuModel = new appsk.SekyuModel();
    appcst.sekyuView = new appsk.SekyuView({model: appcst.sekyuModel});
    // APP初期化処理
    appcst.kfModel = new KihonFreeModel();
    appcst.kfView = new KihonFreeView({model: appcst.kfModel});
    appcst.appModel = new AppModel();
    appcst.appView = new AppView({model: appcst.appModel});
    //
    var orgDataApp, orgDataNiteiCol, orgDataSekyuInfo, orgDataGojokaiInfo, orgDataGojokaiMemberCol, orgDataKashidasiCol, kashidasiDelCol, orgDataKihonFree;
    var _resetData = function (dataSekoKihon, dataNiteiCol, dataSekyuInfo, dataGojokaiInfo, dataGojokaiMemberCol, dataKashidasiCol, dataKihonFree) {
        // 貸出備品データを画面より削除した場合、保持するコレクション
        kashidasiDelCol = new KashidasiCollection();
        // モデルのデータを設定
        appcst.kfView.model.set(dataKihonFree);
        appcst.appView.model.set(dataSekoKihon);
        appcst.sekyuView.model.set(dataSekyuInfo);
        appcst.gojokaiInfoView.model.set(dataGojokaiInfo);
        // resetによりAppViewのresetイベントが発火
        appcst.niteiCol.reset(dataNiteiCol);
        appcst.gojokaiMemberCol.reset(dataGojokaiMemberCol);
        appcst.kashidasiCol.reset(dataKashidasiCol);

        // 基本・喪主・その他タブ 
        if (dataSekoKihon) {
            if (!$.msiJqlib.isNullEx2(dataSekoKihon.jichu_kakute_ymd)) {
                $("#hall_cd").attr("disabled", "disabled");
            }
            // 受注変更の場合、部門コード変更可にする
            if (appcst.controllerName === "juchuhenko") {
                $("#hall_cd").removeAttr("disabled");
            }
            // 基本タブ 性別を設定
            if (dataSekoKihon.k_sex_kbn === "2") {
                $('#female').click();
            } else {
                $('#male').click();
            }
            // 基本タブ 住民登録住所の現住所に同じを設定
            if (dataSekoKihon.kj_kbn === "1" && $('#as_address_2:checked').val() !== "1") {
                $('#as_address_2').click();
            }
            // 基本タブ 本籍の現住所に同じを設定
            if (dataSekoKihon.kh_kbn === "1" && $('#as_address_3:checked').val() !== "1") {
                $('#as_address_3').click();
            }

            // 喪主タブ 故人に同じを設定
            if (dataSekoKihon.mg_kbn === "1" && $('#as_address_4:checked').val() !== "1") {
                $('#as_address_4').click();
            }
            // 喪主タブ 住民登録住所の故人に同じを設定
            if (dataSekoKihon.mj_kbn === "1" && $('#as_address_5:checked').val() !== "1") {
                $('#as_address_5').click();
            }
            // 喪主タブ 住民登録住所の現住所に同じを設定
            if (dataSekoKihon.mj_kbn === "2" && $('#as_address_5_2:checked').val() !== "1") {
                $('#as_address_5_2').click();
            }
            // 喪主タブ 本籍の故人に同じを設定
            if (dataSekoKihon.mh_kbn === "1" && $('#as_address_6:checked').val() !== "1") {
                $('#as_address_6').click();
            }
            // 喪主タブ 請求先の喪主に同じを設定
            if (dataSekoKihon.sekyu_kbn === "1" && $('#as_chief:checked').val() !== "1") {
                $('#as_chief').click();
            }

            // 診断書発行を設定
            if (dataSekoKihon.sd_hakko_kbn === "1") {
                $('#finished').click();
            } else {
                $('#unfinished').click();
            }
            // 搬送区分を設定
            if (dataSekoKihon.hs_kbn === "1") {
                $('#transport_yes').click();
            } else {
                $('#transport_no').click();
            }
            // 印鑑を設定
            if (dataSekoKihon.az_inkan_kbn === "1" && $('#stamp:checked').val() !== "1") {
                $('#stamp').click();
            }
            // 依頼区分を設定
            if (dataSekoKihon.mt_irai_kbn === "1") {
                $('#catalog_yes').click();
            } else {
                $('#catalog_no').click();
            }
            // 宗派その他の場合の処理
            appcst.appView.setShuhaOther();
            // 故人名画像ファイルリンク設定
            appcst.appView.setImgLink("#input-tab", dataSekoKihon.k_file_nm, null);
            // 喪主名画像ファイルリンク設定
            appcst.appView.setImgLink("#infochief-tab #chief", dataSekoKihon.m_file_nm, null);
            // その他の御写真
            appcst.filePhoto = fileUpLib.upload({m: appcst.appModel, attr_oid: 'img_free1', attr_fnm: 'v_free1', el: '#file_clip_portrait', imgprv: false});
            $('#btn_seko_copy, #btn_seko_copy2, #btn_seko_copy3').hide();
            if (dataSekoKihon.moushi_cd === MOUSHI_KBN_SAMPLE) {
                $('#apply_type').attr("disabled", "disabled");
                $('#btn_seko_copy').show();
            }
            if ((dataSekoKihon.moushi_cd === MOUSHI_KBN_SOUGI || dataSekoKihon.moushi_cd === MOUSHI_KBN_SEIZEN) && !$.msiJqlib.isNullEx2(appcst.appModel.get("seko_no"))) {
                $('#btn_seko_copy2').show();
            }
            if (dataSekoKihon.moushi_cd === MOUSHI_KBN_SEIZEN) {
                $('#btn_seko_copy3').show();
            }
            if (dataSekoKihon.moushi_cd === MOUSHI_KBN_SEIZEN) {
                $('#btn_seko_copy4').show();
            }
        }
        if (dataSekyuInfo) {
            // 請求先画像ファイルリンク設定
            appcst.appView.setImgLink("#infochief-tab #bill", dataSekyuInfo.sekyu_file_nm, null);
        }
        // 互助会タブ
        if (dataGojokaiInfo) {
            // 施行履歴を設定
            if (dataGojokaiInfo.rireki_kbn === "1") {
                $('#experienced').click();
            } else {
                $('#inexperienced').click();
            }
            // 事前相談有無を設定
//            if (dataGojokaiInfo.sodan_kbn === "1") {
//                $('#adviced').click();
//            } else {
//                $('#unadviced').click();
//            }
            // 客様加入確認有無を設定
            if (dataGojokaiInfo.kanyu_kakunin_kbn === "1") {
                $('#registered').click();
            } else {
                $('#unregistered').click();
            }
            // 加入団体利用確認有無を設定
            if (dataGojokaiInfo.join_use_kbn === "1") {
                $('#join_used').click();
            } else {
                $('#join_unused').click();
            }
            if (dataGojokaiInfo.plan_use_kbn === '1') {
                $('#plan_agree').click();
            } else {
                $('#plan_disagree').click();
            }
        }
        appcst.setPlanUsePrc();
//        var str = jQuery.format.date( '2014/08/19 08:00:00', 'M月d日　a hh:mm' );
//        var str = jQuery.format.date( new Date(2014, 1,2,12,5), 'M月d日　a hh:mm' );
//        console.log(str);
        // 別途費用の式場商品・通夜会場商品受注伝票作成フラグ
        appcst.bettoCreFlg = false;
        // 施行運営費作成フラグ
        appcst.uneiCreFlg = false;
        // データを退避する
        orgDataApp = appcst.appModel.toJSON();
        orgDataKihonFree = appcst.kfModel.toJSON();
        orgDataNiteiCol = appcst.niteiCol.toJSON();
        orgDataSekyuInfo = appcst.sekyuModel.toJSON();
        orgDataGojokaiInfo = appcst.gojokaiInfoModel.toJSON();
        orgDataGojokaiMemberCol = appcst.gojokaiMemberCol.toJSON();
        orgDataKashidasiCol = appcst.kashidasiCol.toJSON();
        // 上記プラン選択を非活性
        $("#plan_change_set input[name='plan_kbn']").button({disabled: true});

    };
    msiGlobalObj.markObj.mark('reset');
    _resetData(data.dataSekoKihon, data.dataNiteiCol, data.dataSekyuInfo, data.dataGojokaiInfo, data.dataGojokaiMemberCol, data.dataKashidasiCol, data.dataSekoKihonFree);
    msiGlobalObj.markObj.markOutput();
    // 報告書タブ設定処理
    appcst.resetReport(appcst.data);
    // かな自動入力ペア key:名称要素 value[カナ要素, カナモデルプロパティ, モデル]
    var _name_kana_pair = {
        '#input-tab #name': ['#input-tab #kana', 'k_knm', appcst.appModel]
        , '#input-tab #family_name': ['#input-tab #family_name_kana', 'souke_knm', appcst.appModel]
        , '#infochief-tab #chief #name': ['#infochief-tab #chief #kana', 'm_knm', appcst.appModel]
        , '#infochief-tab #bill #name': ['#infochief-tab #bill #kana', 'sekyu_knm', appcst.sekyuModel]
        , '#uketuke_kojin_name': ['#uketuke_kojin_kana', 'v_free2', appcst.kfModel]
        , '#input-tab #syuha_nm_other': ['#input-tab #syuha_knm2', 'syuha_knm', appcst.appModel]
    };
    // カナ自動入力設定
    $.msiJqlib.setAutoKanaModel(_name_kana_pair);

    // タブの横幅設定
    var $li = $("#detail .tab li");
    $li.width(100 / $li.length + "%");

    $("#customer-div-wrapper").show();

    // ヘッダー共通処理
    // 新規作成ボタン押下
    $("#header #btn_new").click(function () {
        // mihara 20150514   if (!confirm('新規作成します。よろしいですか？')) {
        //     return;
        // }
        location.href = $.msiJqlib.baseUrl() + '/juchu/' + appcst.controllerName + '/new';
    });

});
