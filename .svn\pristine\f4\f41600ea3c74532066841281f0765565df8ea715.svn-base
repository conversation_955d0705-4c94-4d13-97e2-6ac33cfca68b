<?php
/**
 * PDF 請求書
 *
 * @category   App
 * @package    controller
 * <AUTHOR> <PERSON>
 * @since      2014/02/24
 * @version    2017/04/17 Mat<PERSON>yama 法事請求書の税抜・税込小計の印字を修正
 * @filesource
 */

/**
 * PDF 請求書
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Sato
 * @since      2014/02/24
 */
class Saiken_Pdf1101Controller extends Zend_Controller_Action {

    // ファイル出力形式
    const OUTTYPE_MULTIFILE = 0; // 異なる請求書を別ファイルとする
    const OUTTYPE_SINGLEFILE = 1; // 全ての請求書を１ファイルとする

    private static $title = '請求書';
    private static $sourceFileName = array('', 'pdf_tmpl/1101_01.pdf', 'pdf_tmpl/1101_01_other.pdf', 'pdf_tmpl/1101_03.pdf', 'pdf_tmpl/1101_03.pdf', '', 'pdf_tmpl/1101_01.pdf');
    private static $sourceFileNameDetail = array('', 'pdf_tmpl/1101_01D.pdf', 'pdf_tmpl/1101_01D_houji.pdf', 'pdf_tmpl/1101_03D.pdf', 'pdf_tmpl/1101_03D.pdf', '', 'pdf_tmpl/1101_01D.pdf');
    private static $sourceFileNameNote = array('', 'pdf_tmpl/1101_01_note.pdf', 'pdf_tmpl/1101_01_other_note.pdf', 'pdf_tmpl/1101_03_note.pdf', 'pdf_tmpl/1101_03_note.pdf', '', 'pdf_tmpl/1101_01.pdf'); // 請求書控え 2017/09/07 ADD Otake
    private static $sourceFileNameDetailNote = array('', 'pdf_tmpl/1101_01D_note.pdf', 'pdf_tmpl/1101_01D_note_houji.pdf', 'pdf_tmpl/1101_03D_note.pdf', 'pdf_tmpl/1101_03D_note.pdf', '', 'pdf_tmpl/1101_01D.pdf'); // 請求書明細控え 2017/09/07 ADD Otake
    private static $sourceFileHyosi = 'pdf_tmpl/1101_H.pdf';
    private static $sourceFileGuide = 'pdf_tmpl/1101_G.pdf';
    private static $sourceFileSaiji = 'pdf_tmpl/1101_01_other2.pdf';
    private static $color = array(84, 130, 53);

    /** 中分類コード：0001=>プラン　plazaでは中分類コードが0001になっている */
    const CHU_BUNRUI_CD_PLN = '0001';

    /** 中分類コード：0240=>互助会・コース差額分 (会員値引) */
    const CHU_BUNRUI_CD_KINNEBIKI = '0240';

    /** 商品区分：1410=>互助会・コース差額分 (会員値引) */
    const SHOHIN_KBN_KINNEBIKI = '1410';
    const CHU_BUNRUI_CD_A = '0020';
    const CHU_BUNRUI_CD_B = '0030';
    const CHU_BUNRUI_CD_PLAN = '0040';
    const CHU_BUNRUI_CD_OKUNI = '0050';
    const CHU_BUNRUI_CD_C = '0060';
    const CHU_BUNRUI_CD_D = '0070';
    const CHU_BUNRUI_CD_E = '0080';
    const CHU_BUNRUI_CD_F = '0100';
    const CHU_BUNRUI_CD_G = '0090';
    const CHU_BUNRUI_CD_H = '0120';
    const ADD_KBN_GOJYOKAITOKUTEN = 8;
    const SERVICE_KBN_GOJYOKAI = 1;
    const SERVICE_KBN_GOJYOKAI_NEBIKI = 2;

    /** 大分類コード：0005=>プラン　中分類コード0050プランを　大分類0005とみなす（SQLで処理済） */
    const DAI_BUNRUI_P = "0005";
    /** 大分類コード：0010=>葬送儀礼 */
    const DAI_BUNRUI_1 = "0010";
    /** 大分類コード：0020=>会葬返礼 */
    const DAI_BUNRUI_2 = "0020";
    /** 大分類コード：0030=>料理 */
    const DAI_BUNRUI_3 = "0030";
    /** 大分類コード：0050=>別途費用 */
    const DAI_BUNRUI_5 = "0050";
    /** 大分類コード：0060=>立替費用 */
    const DAI_BUNRUI_6 = "0060";
    /** 大分類コード：0070=>値引 */
    const DAI_BUNRUI_7 = "0070";

    /** 大分類コード：0050=>別途費用 商品区分：2000=>雑収入 */
    const DAI_BUNRUI_5_2000 = "0050_2000";

    /** 商品区分コード：2000 => 雑収入 */
    const SHOHIN_KBN_2000 = "2000";
    const SUMID_KEIYAKUGAKU = 'keiyaku_gaku';
    const SUMID_A_PLAN = 'a_plan';
    const SUMID_A_GOJOKAI = 'a_gojokai';
    const SUMID_A_GOJOKAINEBIKI = 'a_gojokai_nebiki';
    const SUMID_A_NEBIKI_PRC = 'a_nebiki';
    const SUMID_A_GOJOKAIGAI = 'a_gojokai_gai';
    const SUMID_AZUKARI_DAN = 'azukari_dan';
    const SUMID_AZUKARI_HEN = 'azukari_hen';
    const SUMID_MEIGICGCOST = 'mg_chg_cost';
    const SUMID_EARLYUSE = 'early_use';
    const SUMID_EARLYUSEZEI = 'early_use_zei';
    const SUMID_COSECHGGAKU = 'cose_chg_gaku';
    const SUMID_GOJOHARAI = 'gojo_harai';
    const SUMID_GOJOHARAI_HOUJI = 'gojo_harai';
    const SUMID_ZENNOWARI = 'zen_wari';
    const SUMID_KAKEZEISAGAKU = 'kake_zei_sagaku';
    const SUMID_KAKEZAN = 'zan_gaku';
    const SUMID_KAKEZANZEI = 'zan_gaku_zei';
    const SUMID_A_SETNEBIKI = 'a_set_nebiki'; // セット値引
    const SUMID_A_KAIINNEBIKI = 'a_kaiin_nebiki'; // 会員値引き
    const SUMID_A_UNPLAN = 'a_unplanned'; // プラン外選択商品
    const SUMID_A_UNPLANSAGAKU = 'a_unplanned_sagaku'; // プラン外選択商品差額
    const PAY_METHOD_TOJITSU = '20';

    private static $sum_name = array(
        self::SUMID_A_PLAN => '葬送儀礼',
        self::SUMID_A_GOJOKAI => '互助会品目合計',
        self::SUMID_A_GOJOKAINEBIKI => '使用コース値引', //'会員値引き',
        self::SUMID_A_NEBIKI_PRC => '割引金額',
        self::SUMID_A_GOJOKAIGAI => '互助会外プラン品目合計',
        self::SUMID_AZUKARI_DAN => '預り金（壇払い）',
        self::SUMID_AZUKARI_HEN => '契約役務外利用', // 2017/09/01 MOD Otake'預り金（返礼品）',
        self::SUMID_MEIGICGCOST => '名義変更手数料',
        self::SUMID_EARLYUSE => '早期利用費',
        self::SUMID_EARLYUSEZEI => '早期利用費消費税',
        self::SUMID_KAKEZAN => '会費残額',
        self::SUMID_KAKEZANZEI => '会費消費税',
        self::SUMID_COSECHGGAKU => 'コース変更差額',
        self::SUMID_GOJOHARAI => '互助会払込金額',
        self::SUMID_GOJOHARAI_HOUJI => '契約役務外利用',
        self::SUMID_ZENNOWARI => '前納割引',
        self::SUMID_KAKEZEISAGAKU => '掛金消費税差額',
        self::SUMID_A_SETNEBIKI => 'セット値引',
        self::SUMID_A_KAIINNEBIKI => '互助会・コース差額分', //'会員値引',
        self::SUMID_A_UNPLAN => 'プラン外選択商品',
        self::SUMID_A_UNPLANSAGAKU => 'プラン外選択商品差額',
    );
    private static $msi_name_gojokai = 'A-1（互助会）';
    private static $msi_name_gojokaitokuten = '互助会会員特典';
    private static $msi_name_gojokaigai = 'A-2（プラン選択商品）';

    /** 申込区分 1：葬儀 2：法事 5：生前依頼 6：その他依頼 */
    private $_moushiKbn = null;

    /** 中分類コード */
    const CHU_BUNRUI_CD_JIGO = '1000'; // 事後
    const CHU_BUNRUI_CD_IHAI = '1010'; // 位牌
    const CHU_BUNRUI_CD_BUTSUDAN = '1020'; // 仏壇
    const CHU_BUNRUI_CD_BOSHO = '1030'; // 墓所・手元供養
    const CHU_BUNRUI_CD_ANNAI = '1040'; // 案内状
    const CHU_BUNRUI_CD_SAIDAN = '1050'; // 祭壇・式場
    const CHU_BUNRUI_CD_05BOSHO = '1060'; // 05墓所・手元供養
    const CHU_BUNRUI_CD_HOUYOU = '1070'; // 法要
    const CHU_BUNRUI_CD_HAKAMAIRI = '1080'; // 墓参り
    const CHU_BUNRUI_CD_HENREI = '1090'; // 返礼品
    const CHU_BUNRUI_CD_HOUJI = '1110'; // 法事・催事
    const CHU_BUNRUI_CD_KAISHOKU = '1120'; // 会食
    const CHU_BUNRUI_CD_HIKAZEI = '1130'; // 非課税
    const CHU_BUNRUI_CD_BETTOHIYOU = '1140'; // 別途費用
    const CHU_BUNRUI_CD_NEBIKI = '1150'; // 値引き

    private static $houyou_hakamairi = [
          self::CHU_BUNRUI_CD_JIGO
        , self::CHU_BUNRUI_CD_IHAI
        , self::CHU_BUNRUI_CD_BUTSUDAN
        , self::CHU_BUNRUI_CD_BOSHO
        , self::CHU_BUNRUI_CD_ANNAI
        , self::CHU_BUNRUI_CD_SAIDAN
        , self::CHU_BUNRUI_CD_05BOSHO
        , self::CHU_BUNRUI_CD_HOUYOU
        , self::CHU_BUNRUI_CD_HAKAMAIRI
        , self::CHU_BUNRUI_CD_HOUJI
    ];
    private static $omotenasi = [
          self::CHU_BUNRUI_CD_HENREI
        , self::CHU_BUNRUI_CD_KAISHOKU
    ];
    private static $sonota = [
          self::CHU_BUNRUI_CD_BETTOHIYOU
        , self::CHU_BUNRUI_CD_NEBIKI
    ];

    /**
     * アクション
     *
     * <AUTHOR> Sato
     * @since      2014/02/24
     */
    public function indexAction() {
        $params = Msi_Sys_Utils::webInputs();
        $out_type = self::OUTTYPE_SINGLEFILE;
        /*        if (isset($params['preview'])) {
          $preview = htmlspecialchars($params['preview']) == 'on';
          } else {
          $preview = false;
          } */
        if (isset($params['out_type'])) {
            $out_type = $params['out_type'];
        }

        $db = Msi_Sys_DbManager::getMyDb();

        // 売上伝票No.指定
        if (isset($params['uri_den_no'])) {
            if (is_string($params['uri_den_no'])) {
                $uri_den_no_ary[] = htmlspecialchars($params['uri_den_no']);
            } else if (is_array($params['uri_den_no'])) {
                foreach ($params['uri_den_no'] as $value) {
                    $uri_den_no_ary[] = htmlspecialchars($value);
                }
            }
            if (!isset($params['output_kbn'])) {    // 2015/03/24 ADD Kayo
                foreach ($uri_den_no_ary as $uri_den_no) {
                    $param_ary[] = DataMapper_Pdf1101::changeUriDenNoToParam($db, $uri_den_no);
                }
            } else {
                // 別注品一覧から印刷された場合
                foreach ($uri_den_no_ary as $uri_den_no) {
                    $param_ary[] = DataMapper_Pdf1101::changeUriDenNoToParam2($db, $uri_den_no);
                }
            }
        } else {
            $seko_no = htmlspecialchars($params['seko_no']);          // 施行番号
            $this->_moushiKbn = DataMapper_PdfCommon::getSekoMoushiKbn($seko_no);        // 申込区分　1：葬儀 2：法事 5：生前依頼 6：その他依頼
            if (array_key_exists('seko_no_sub', $params)) {
                $seko_no_sub = htmlspecialchars($params['seko_no_sub']);
            } // 施行番号（枝番）
            else {
                $seko_no_sub = '00';
            }
            $data_kbn = htmlspecialchars($params['data_kbn']);        // データ区分　1：葬儀 2：法事 3：単品 4：別注品
            $param_ary = DataMapper_Pdf1101::changeSekoNoToParam($db, $data_kbn, $seko_no, $seko_no_sub);
        }

        if (isset($params['printKbn'])) { // 並び替え区分 0:明細複合 1:互助会別
            $print_kbn = $params['printKbn'];
        } else {
            $print_kbn = 0;
        }
        if (isset($params['kakuin'])) {
            $isNeedKakuin = ($params['kakuin'] === 'on');
        } else {
            $isNeedKakuin = false;
        }

        $issue_date = date('Y/m/d');

        if ($out_type == self::OUTTYPE_MULTIFILE) {
            foreach ($param_ary as $param) {
                $pdfObj = new App_Pdf($this->getFileName($db, $param['seko_no'], self::$title));
                $this->outData($pdfObj, $db, $issue_date, $param['uri_den_no'], $param['data_kbn'], $param['seko_no'], $param['seko_no_sub'], $print_kbn, $isNeedKakuin);
                $pdfObj->downloadAjaxPush();
            }
            $pdfObj->downloadAjaxFlush();
        } else {
            if (Msi_Sys_Utils::myCount($param_ary) === 1) {
                $pdfObj = new App_Pdf($this->getFileName($db, $param_ary[0]['seko_no'], self::$title));
            } else {
                $pdfObj = new App_Pdf(self::$title . '(複数)');
            }
            foreach ($param_ary as $param) {
                $this->outData($pdfObj, $db, $issue_date, $param['uri_den_no'], $param['data_kbn'], $param['seko_no'], $param['seko_no_sub'], $print_kbn, $isNeedKakuin);
            }
            $pdfObj->download();
        }
        // 別注品の場合は、発行済みに更新する。
        if (isset($params['output_kbn'])) {    // 2015/03/24 ADD Kayo
            $uri_ary = array();
            foreach ($param_ary as $uri_den_no) {
                $uri_ary[] = $uri_den_no['uri_den_no'];
            }
            Logic_SeikyuEtc::seikyuhakkoMulti($uri_ary);
        }
    }

    private function outData($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn, $isNeedkakuin = false) {
        // 軽減税率対応 sugiyama keigen   基準日の設定
        $this->_prepKeigen($seko_no, $seko_no_sub, $data_kbn);

        switch ($data_kbn) {
            // 葬儀
            case '1':
                $this->outData01($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn, true, $isNeedkakuin);
                break;
            // 法事
            case '2':
                $this->outData02($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn, true, $isNeedkakuin);
                break;
            // 単品
            case '3':
                $oldNumPages = $this->outData03($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn);
                // 会社控え
                $this->outData03($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, true, $oldNumPages);
                break;
            // 別注品
            case '4':
                $oldNumPages = $this->outData03($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn);
                // 会社控え
                $this->outData03($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, true, $oldNumPages);
                break;
            // その他施工
            case '6':
                $this->outData01($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn);
                break;
        }
    }

    /**
     * 請求書（葬儀）
     * @version 2017/09/06 DEL 未精算供物:$rec1101['n_free5']
     * @version 2017/09/06 DEL 御入金金額:$rec1101['nyukin_prc']
     * @version 2017/09/06 DEL 葬儀施行金額:$rec1101['seikyu_prc'] + $rec1101['zei_prc']
     * @version 2017/09/06 DEL 備考:$rec1101['v_free9']
     * @param type $pdfObj
     * @param type $db
     * @param date $issue_date
     * @param type $uri_den_no
     * @param type $data_kbn
     * @param type $seko_no
     * @param type $seko_no_sub
     * @param type $print_kbn
     * @param bool $isNeedNote 請求書控えが必要かそうか
     * @return type
     */
    private function outData01($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn, $isNeedNote = false, $isNeedKakuin = false) {
        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');

        // 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);

        /* ******************************* *
         * PDF出力に利用するデータを取得     *
         * ******************************* */
        // pdf出力用データ(共通)
        $recCmn = DataMapper_PdfCommon::find($db, array("seko_no" => $seko_no));
        if (Msi_Sys_Utils::myCount($recCmn) == 0) {
            return array();
        }
        $recCommon = $recCmn[0];
        // pdf出力用データ(1101用)
        $kh1101 = array("uri_den_no" => $uri_den_no, "seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub);
        $recWk = DataMapper_Pdf1101::find($db, $kh1101);
        if (Msi_Sys_Utils::myCount($recWk) == 0) {
            return;
        }
        $rec1101 = $recWk[0];
        // 請求先情報
        $recSekyu = App_Utils::getSekoSekyuInfo($seko_no);
        // 請求書関連の各種金額
        $kingaku = App_SeikyuLib::getKingaku($db, $seko_no, $seko_no_sub, $data_kbn);
        // 売上伝票より請求金額
        $sum = App_Utils::getSeikyuKingaku($seko_no, $seko_no_sub, $data_kbn);
        // 会員名
        $kain_nm = App_Utils::getKainNm();
        // 表紙データ
        $hyoshiData = $this->createHyoshiData($db, $seko_no);

        /* ************** *
         * データの整理    *
         * ************** */
        // 会社ロゴ
        $logo = null;
        $kaisyalogo = array(
            'logo' => $logo,
            'nm' => '',
            'bumon_nm' => '',
            'bumon_tel' => ''
        );

        // 明細行配列取得 と 鏡の金額の配列
        list($detailRowArray, $sumArr) = $this->createDetailData($db, $uri_den_no, $seko_no, $data_kbn, $seko_no_sub, $rec1101['kain_no'], $print_kbn);

        // 互助会の文字列を会員名で置き換える
        $this->replaceGojokaiByName($kain_nm);

        // 鏡の金額
        $sumArr['tax'] = array('name' => '消費税', 'sum' => $kingaku[App_SeikyuLib::ID_ZEIPRC]);
        $sumArr[self::SUMID_KEIYAKUGAKU] = array('sum' => $kingaku[App_SeikyuLib::ID_KEIYAKUGAKU]);
        $sumArr[self::SUMID_AZUKARI_DAN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_DAN], 'sum' => $kingaku[App_SeikyuLib::ID_AZUKARI_DAN]);
        $sumArr[self::SUMID_AZUKARI_HEN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_HEN], 'sum' => $kingaku[App_SeikyuLib::ID_AZUKARI_HEN]);
        $sumArr[self::SUMID_MEIGICGCOST] = array('name' => self::$sum_name[self::SUMID_MEIGICGCOST], 'sum' => $kingaku[App_MitsuLib::ID_MEIGICGCOST]);
        $sumArr[self::SUMID_EARLYUSE] = array('name' => self::$sum_name[self::SUMID_EARLYUSE], 'sum' => $kingaku[App_SeikyuLib::ID_EARLYUSE]);
        $sumArr[self::SUMID_EARLYUSEZEI] = array('name' => self::$sum_name[self::SUMID_EARLYUSEZEI], 'sum' => $kingaku[App_SeikyuLib::ID_EARLYUSEZEI]);
        $sumArr[self::SUMID_KAKEZAN] = array('name' => self::$sum_name[self::SUMID_KAKEZAN], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZAN]);
        $sumArr[self::SUMID_KAKEZANZEI] = array('name' => self::$sum_name[self::SUMID_KAKEZANZEI], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZANZEI]);
        $sumArr[self::SUMID_COSECHGGAKU] = array('name' => self::$sum_name[self::SUMID_COSECHGGAKU], 'sum' => $kingaku[App_SeikyuLib::ID_COSECHGGAKU]);
        $sumArr[self::SUMID_GOJOHARAI] = array('name' => self::$sum_name[self::SUMID_GOJOHARAI], 'sum' => $kingaku[App_SeikyuLib::ID_GOJOHARAI]);
        $sumArr[self::SUMID_ZENNOWARI] = array('name' => self::$sum_name[self::SUMID_ZENNOWARI], 'sum' => $kingaku[App_SeikyuLib::ID_ZENNOWARI]);
        $sumArr[self::SUMID_KAKEZEISAGAKU] = array('name' => self::$sum_name[self::SUMID_KAKEZEISAGAKU], 'sum' => $kingaku[App_SeikyuLib::ID_KAKEZEISAGAKU]);

        //必要なら請求金額に追加する 未精算供物:$rec1101['n_free5']
        $sekyu_sum = $sum;
        // 軽減税率対応のためデータ設定  2019/05/xx sugiyama keigen
        $this->_p_tgt_type = 'normal';
        $this->_p_seko_no = $seko_no;
        $this->_p_seko_no_sub = $seko_no_sub;
        $this->_p_data_kbn = $data_kbn;
        $this->_p_history_no = null;
        $sumArr['total'] = array('name' => '御請求金額', 'sum' => $sekyu_sum);

        // 振込案内用データ作成
        $furikomiGuideData = array(
            'sekyu_no' => $rec1101['uri_den_no'],
            'souke_nm' => $rec1101['souke_nm'],
            'tanto_nm' => $rec1101['tanto_nm'],
            'bumon_cd' => $recCommon['bumon_cd'],
            'sekyu_nm' => $recSekyu['sekyu_nm'],
            'sekyu_yubin_no' => $recSekyu['yubin_no'],
            'sekyu_addr1' => $recSekyu['addr1'],
            'sekyu_addr2' => $recSekyu['addr2'],
            'sekyu_gaku' => $sekyu_sum,
            'issue_date' => $issue_date,
            'data_kbn' => $data_kbn,
        );


        /* ************ *
         * 帳票出力      *
         * ************ */
        // 表紙を追加・出力
//		$this->addHyoshi($pdfObj, $hyoshiData);
        // 請求書控えが必要なら２回出力
        $maxLoop = $isNeedNote ? 2 : 1;
        $numPages = 0;
        for ($i = 1; $i <= $maxLoop; $i++) {
            $isFirstTime = ($i === 1);

            // 葬儀請求書を追加・出力(２週目以降は控えを出力)
            $this->addSogiSekyusho($pdfObj, $db, $recSekyu, $rec1101, $seko_no, $data_kbn, $issue_date, !$isFirstTime, $rec1101['uri_den_no'], $recCommon, !$isFirstTime, $isNeedKakuin);

            // 金額を出力
            $strSeikyuSum = Msi_Sys_Utils::filterComma($sekyu_sum);
            $pdfObj->write_string(array('x' => 47, 'y' => 160, 'width' => 167, 'height' => 15, 'font_size' => 18, 'align' => 'R'), "￥" . $strSeikyuSum . '＊');
            $this->outDataFaceSum01($db, $pdfObj, $sumArr, $rec1101['gojokai_cose_nm'], $recWk, $seko_no);
            $this->outKaiinInfo($db, $pdfObj, $seko_no);
            // 鏡出力終了 / 鏡のページを取得
            $kagamiPage = $pdfObj->getNumPages();
            $meisaiAry = array();
            foreach ($detailRowArray as $row) {
                $meisaiAry[] = $row;
            }
            if (Msi_Sys_Utils::myCount($meisaiAry) > 0) {
                $this->addSogiSekyushoDetail($pdfObj, $meisaiAry, $data_kbn, !$isFirstTime);
            }
            // 請求書明細出力 二週目以降は控えを出力
            // $this->addSogiSekyushoDetail($pdfObj, $detailRowArray, $data_kbn, !$isFirstTime);
            // ページ等を出力
            $this->outMeisaiInfo($pdfObj, $kagamiPage, $recSekyu['sekyu_nm'], $kaisyalogo, $issue_date, $seko_no, $uri_den_no, $numPages);
        }
        // 振込案内出力
        $this->addFurikomiGuide($db, $pdfObj, $furikomiGuideData);
    }

    /**
     * 葬儀請求書ページを追加する
     */
    private function addSogiSekyusho($pdfObj, $db, $recSekyu, $rec1101, $seko_no, $data_kbn, $issue_date, $isFirstTime, $uri_den_no, $recCommon, $shouldOutNote = false, $isNeedKakuin = false) {
        // 控えを出力するべきかどうか
        $sourceFile = !$shouldOutNote ? self::$sourceFileName[$this->getSourceFileIndex($data_kbn)] : self::$sourceFileNameNote[$this->getSourceFileIndex($data_kbn)];
        $pdfObj->addSourcePage(__DIR__ . '/' . $sourceFile);

        // 社判
        //$pdfObj->syaban_out($db, 3, 440, 80);
        App_Utils2::syabanImgOut($db, $pdfObj, $recCommon, 380, 81, 2, null, 83.5);

        // 事業所番号
        if ($this->_isKeigenAppliedCtxt()) {
            $toroku_bango = App_KeigenUtils::getJigyoshaTorokuBango();
            if ($toroku_bango) {
                $bango_str = sprintf("事業者番号 %s", $toroku_bango);
                $pdfObj->write_string(array('x' => 437, 'y' => 25, 'width' => 150, 'height' => 15, 'font_size' => 9), $bango_str);
            }
        }
        // 発行日
        $pdfObj->write_date(array('type' => 'ymd', 'x' => 437, 'y' => 41, 'width' => 150, 'height' => 15, 'font_size' => 9), $issue_date, '発　行　日　　Y年m月d日');

        if (isset($recSekyu)) {
            $pdfObj->write_string(array('x' => 75, 'y' => 25, 'width' => 200, 'height' => 45, 'font_size' => 9), $recSekyu['yubin_no']);
            $pdfObj->write_string(array('x' => 75, 'y' => 36, 'width' => 200, 'height' => 45, 'font_size' => 9), $recSekyu['addr1']);
            $pdfObj->write_string(array('x' => 75, 'y' => 47, 'width' => 200, 'height' => 45, 'font_size' => 9), $recSekyu['addr2']);
            $pdfObj->write_string(array('x' => 75, 'y' => 80, 'width' => 200, 'height' => 20, 'font_size' => 14, 'align' => 'L'), $recSekyu['sekyu_nm'] . '　様');
        }
        $pdfObj->write_string(array('x' => 45, 'y' => 183, 'width' => 400, 'height' => 10, 'font_size' => 10), '故　' . $rec1101['k_nm'] . ' 様');

        // 印鑑
        if (isset($rec1101['inkan_img'])) {
            $img = $db->readBlobCont($rec1101['inkan_img']);
            $pdfObj->seal_out(532, 180, $img);
        }
        if (isset($rec1101['inkan_img1'])) {
            $img = $db->readBlobCont($rec1101['inkan_img1']);
            $pdfObj->seal_out(492, 180, $img);
        }
        if (isset($rec1101['inkan_img2'])) {
            $img = $db->readBlobCont($rec1101['inkan_img2']);
            $pdfObj->seal_out(452, 180, $img);
        }

        // 受付部門
        $pdfObj->write_multi(
                array(
                    array('type' => 'string', 'x' => 480, 'y' => 59, 'width' => 115, 'height' => 15, 'font_size' => 9, 'value' => $seko_no),
                    array('type' => 'string', 'x' => 480, 'y' => 69, 'width' => 115, 'height' => 15, 'font_size' => 9, 'value' => $rec1101['uri_den_no'])
                )
        );
        // 消費税金額の内訳出力     2019/04/30 sugiyama keigen
        if ($this->_isKeigenAppliedCtxt()) {
            $this->outZeiUchiwake($pdfObj, null, $uri_den_no);
        }
    }

    /**
     * 請求書（法事）
     * @param type $pdfObj
     * @param type $db
     * @param type $issue_date
     * @param type $uri_den_no
     * @param type $data_kbn
     * @param type $seko_no
     * @param type $seko_no_sub
     * @param type $print_kbn
     * @return type
     */
    private function outData02($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn, $isNeedNote = false, $isNeedKakuin = false) {
        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');

        // 確認用のグリッドを出力
//          $pdfObj->test_line_out(600, 1000);

        /* ******************************* *
         * PDF出力に利用するデータを取得     *
         * ******************************* */
        // pdf出力用データ(共通)
        $recCmn = DataMapper_PdfCommon::find($db, array("seko_no" => $seko_no));
        if (Msi_Sys_Utils::myCount($recCmn) == 0) {
            return array();
        }
        $recCommon = $recCmn[0];
        // pdf出力用データ(1101用)
        $kh1101 = array("uri_den_no" => $uri_den_no, "seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub);
        $recWk = DataMapper_Pdf1101::find($db, $kh1101);
        if (Msi_Sys_Utils::myCount($recWk) == 0) {
            return;
        }
        $rec1101 = $recWk[0];
        // 請求先情報
        $recSekyu = App_Utils::getSekoSekyuInfo($seko_no);
        // 請求書関連の各種金額
        $kingaku = App_SeikyuLib::getKingaku($db, $seko_no, $seko_no_sub, $data_kbn);
        // 売上伝票より請求金額
        $sum = App_Utils::getSeikyuKingaku($seko_no, $seko_no_sub, $data_kbn);
        // 会員名
        $kain_nm = App_Utils::getKainNm();
        /* ************** *
         * データの整理    *
         * ************** */
        // 会社ロゴ
        $logo = null;
        $kaisyalogo = array(
            'logo' => $logo,
            'nm' => '',
            'bumon_nm' => '',
            'bumon_tel' => ''
        );

        // 表紙用金額・明細書用データ
        list($detailRowArray, $sumArr) = $this->createDetailDataHouji($db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn);

        // 互助会の文字列を会員名で置き換える
        $this->replaceGojokaiByName($kain_nm);

        // 鏡の金額
        $sumArr['tax'] = array('name' => '消費税', 'sum' => $kingaku[App_SeikyuLib::ID_ZEIPRC]);
        $sumArr[self::SUMID_KEIYAKUGAKU] = array('sum' => $kingaku[App_SeikyuLib::ID_KEIYAKUGAKU]);
        $sumArr[self::SUMID_AZUKARI_DAN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_DAN], 'sum' => $kingaku[App_SeikyuLib::ID_AZUKARI_DAN]);
        $sumArr[self::SUMID_AZUKARI_HEN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_HEN], 'sum' => $kingaku[App_SeikyuLib::ID_AZUKARI_HEN]);
        $sumArr[self::SUMID_MEIGICGCOST] = array('name' => self::$sum_name[self::SUMID_MEIGICGCOST], 'sum' => $kingaku[App_MitsuLib::ID_MEIGICGCOST]);
        $sumArr[self::SUMID_EARLYUSE] = array('name' => self::$sum_name[self::SUMID_EARLYUSE], 'sum' => $kingaku[App_SeikyuLib::ID_EARLYUSE]);
        $sumArr[self::SUMID_EARLYUSEZEI] = array('name' => self::$sum_name[self::SUMID_EARLYUSEZEI], 'sum' => $kingaku[App_SeikyuLib::ID_EARLYUSEZEI]);
        $sumArr[self::SUMID_KAKEZAN] = array('name' => self::$sum_name[self::SUMID_KAKEZAN], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZAN]);
        $sumArr[self::SUMID_KAKEZANZEI] = array('name' => self::$sum_name[self::SUMID_KAKEZANZEI], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZANZEI]);
        $sumArr[self::SUMID_COSECHGGAKU] = array('name' => self::$sum_name[self::SUMID_COSECHGGAKU], 'sum' => $kingaku[App_SeikyuLib::ID_COSECHGGAKU]);
        $sumArr[self::SUMID_GOJOHARAI] = array('name' => self::$sum_name[self::SUMID_GOJOHARAI], 'sum' => $kingaku[App_SeikyuLib::ID_GOJOHARAI]);
        $sumArr[self::SUMID_ZENNOWARI] = array('name' => self::$sum_name[self::SUMID_ZENNOWARI], 'sum' => $kingaku[App_SeikyuLib::ID_ZENNOWARI]);
        $sumArr[self::SUMID_KAKEZEISAGAKU] = array('name' => self::$sum_name[self::SUMID_KAKEZEISAGAKU], 'sum' => $kingaku[App_SeikyuLib::ID_KAKEZEISAGAKU]);
        $sumArr[self::SUMID_GOJOHARAI_HOUJI] = array('name' => self::$sum_name[self::SUMID_GOJOHARAI_HOUJI], 'sum' => $kingaku[App_SeikyuLib::ID_GOJOHARAI]);
        //必要なら請求金額に追加する 未精算供物:$rec1101['n_free5']
        $sekyu_sum = $sum;

        // 軽減税率対応のためデータ設定  2019/05/xx sugiyama keigen
        $this->_p_tgt_type = 'normal';
        $this->_p_seko_no = $seko_no;
        $this->_p_seko_no_sub = $seko_no_sub;
        $this->_p_data_kbn = $data_kbn;
        $this->_p_history_no = null;
        $sumArr['total'] = array('name' => '御請求金額', 'sum' => $sekyu_sum);

        /* ************ *
         * 帳票出力      *
         * ************ */
        // 請求書控えが必要なら２回出力
        $maxLoop = $isNeedNote ? 2 : 1;
        $numPages = 0;
        for ($i = 1; $i <= $maxLoop; $i++) {
            $isFirstTime = ($i === 1);

            // 葬儀請求書を追加・出力(２週目以降は控えを出力)
            if ($i === 1) {
                $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$this->getSourceFileIndex($data_kbn)]);
            } else {
                $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileNameNote[$this->getSourceFileIndex($data_kbn)]);
            }
            // 社判
            App_Utils2::syabanImgOutHouji($db, $pdfObj, $recCmn[0], 385, 83, 2, null, 88);
            // 事業所番号
            if ($this->_isKeigenAppliedCtxt()) {
                $toroku_bango = App_KeigenUtils::getJigyoshaTorokuBango();
                if ($toroku_bango) {
                    $bango_str = sprintf("事業者番号 %s", $toroku_bango);
                    $pdfObj->write_string(array('x' => 437, 'y' => 25, 'width' => 150, 'height' => 15, 'font_size' => 9), $bango_str);
                }
            }
            // 発行日
            $pdfObj->write_date(array('type' => 'ymd', 'x' => 437, 'y' => 41, 'width' => 150, 'height' => 15, 'font_size' => 9), $issue_date, '発　行　日　　Y年m月d日');

            if (isset($recSekyu)) {
                $pdfObj->write_string(array('x' => 75, 'y' => 25, 'width' => 200, 'height' => 45, 'font_size' => 9), $recSekyu['yubin_no']);
                $pdfObj->write_string(array('x' => 75, 'y' => 36, 'width' => 200, 'height' => 45, 'font_size' => 9), $recSekyu['addr1']);
                $pdfObj->write_string(array('x' => 75, 'y' => 47, 'width' => 200, 'height' => 45, 'font_size' => 9), $recSekyu['addr2']);
                $pdfObj->write_string(array('x' => 75, 'y' => 80, 'width' => 200, 'height' => 20, 'font_size' => 14, 'align' => 'L'), $recSekyu['sekyu_nm'] . '　様');
            }
            // 受付部門
            $pdfObj->write_multi(
                    array(
                        array('type' => 'string', 'x' => 480, 'y' => 59, 'width' => 115, 'height' => 15, 'font_size' => 9, 'value' => $seko_no),
                        array('type' => 'string', 'x' => 480, 'y' => 69, 'width' => 115, 'height' => 15, 'font_size' => 9, 'value' => $rec1101['uri_den_no'])
                    )
            );
            // 表紙の金額を出力
            $this->outDataFaceSum_houji($db, $pdfObj, $sumArr, $rec1101['gojokai_cose_nm'], $recWk, $seko_no);
            $this->outKaiinInfo($db, $pdfObj, $seko_no);

            // 振込情報
            $bank_info = App_Utils2::getBankInfo($db, $recCommon['bumon_cd']);
            $yokin_nm = $this->getCodeName($db, '1980', $bank_info['yokin_sbt']);
            $bank_bun1 = 'お振込の場合は下記の銀行をご利用ください。';
            $bank_bun2 = 'お振込の際はお名前の前に請求書右上の請求番号の下５桁を';
            $bank_bun7 = 'ご記入（ご入力）ください。';
            $bank_bun3 = '例）54321 ナリタタロウ';
            $bank_bun4 = '恐れ入りますが、振込手数料はお客様の負担でお願い致します。';
            $bank_bun5 = $bank_info['bank_nm'] . '　　' . $bank_info['shiten_nm'] . '　　口座番号（' . $yokin_nm . '）' . $bank_info['kouza_no'];
            $bank_bun6 = '口座名義人　' . $bank_info['koza_meigi_nm'];
            $pdfObj->write_string(array('x' => 50, 'y' => 680, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun1);
            $pdfObj->write_string(array('x' => 50, 'y' => 691, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun2);
            $pdfObj->write_string(array('x' => 50, 'y' => 702, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun7);
            $pdfObj->write_string(array('x' => 50, 'y' => 713, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun3);
            $pdfObj->write_string(array('x' => 50, 'y' => 724, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun4);
            $pdfObj->write_string(array('x' => 50, 'y' => 741, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun5);
            $pdfObj->write_string(array('x' => 50, 'y' => 752, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun6); 
            
            // 印鑑（ログイン担当者）
            $inkan_img = App_Utils::getTantoInkanOid();
            if(isset($inkan_img)){
                $img = $db->readBlobCont($inkan_img);
                $pdfObj->seal_out(536, 609, $img);
            }

            $pdfObj->write_string(array('x' => 390, 'y' => 760, 'width' => 390, 'height' => 45, 'font_size' => 10), '受付部門');              // 受付部門
            $pdfObj->write_string(array('x' => 440, 'y' => 760, 'width' => 145, 'height' => 45, 'font_size' => 10), $recCommon['bumon_lnm']); // 受付部門
            //$pdfObj->write_string(array('x' => 390, 'y' => 771, 'width' => 400, 'height' => 45, 'font_size' => 10), '担 当 者');              // 担当者
            //$pdfObj->write_string(array('x' => 440, 'y' => 771, 'width' => 145, 'height' => 45, 'font_size' => 10), $rec1101['tanto_nm']);    // 担当者
            // 鏡出力終了 / 鏡のページを取得
            $kagamiPage = $pdfObj->getNumPages();
            $meisaiAry = array();
            foreach ($detailRowArray as $row) {
                $meisaiAry[] = $row;
            }
            if (Msi_Sys_Utils::myCount($meisaiAry) > 0) {
                $this->addSogiSekyushoDetail($pdfObj, $meisaiAry, $data_kbn, !$isFirstTime);
            }
            // ページ等を出力
            $this->outMeisaiInfo($pdfObj, $kagamiPage, $recSekyu['sekyu_nm'], $kaisyalogo, null, $seko_no, $uri_den_no, $numPages);
        }
    }

    /**
     * 表紙の金額を出力（法事）
     *
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    private function outDataFaceSum_houji($db, $pdfObj, $sumArr, $coseNm, $recWk, $seko_no, $isMituKakutei = false) {
        $row_height = 23.05;
        static $item_indent = '　　';

        $set_arr[] = array('x' => 50, 'width' => 280, 'height' => 15, 'font_size' => 12);                     // 項目
        $set_arr[] = array('x' => 342, 'width' => 80, 'height' => 15, 'font_size' => 12, 'type' => 'num');    // 金額
        $set_arr[] = array('x' => 430, 'width' => 135, 'height' => 15, 'font_size' => 12);                     // 備考
        $set_arr[] = array('x' => 250, 'width' => 85, 'height' => 15, 'font_size' => 12);                     // 項目（２列目）
        $set_arr[] = array('x' => 342, 'width' => 80, 'height' => 20, 'font_size' => 12, 'align' => 'R');     // 合計金額
        $set_arr[] = array('x1' => 45, 'y1' => -2, 'x2' => 577, 'y2' => -2, 'width' => 1, 'color' => self::$color, 'type' => 'line'); // 上線
        $set_arr[] = array('x' => 357, 'width' => 65, 'height' => 15, 'font_size' => 12, 'type' => 'num');    // 金額
        $set_arr[] = array('x' => 339, 'width' => 135, 'height' => 15, 'font_size' => 12);                     // 備考
        // 各項目開始位置
        $y_sosogirei = 210;
        $y_total_sum = 735;

        //
        // 葬送儀礼費用
        //
        for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
            $set_arr[$index]['y'] = $y_sosogirei;
        }

        $total1 = 0;
        $row_arr = array();
        $tatekae_sum = 0;
        /*
         *  1. 御請求金額
         */
        // 1. 御請求金額から省く項目
        $exist_arr = array('total', 'tax', 'azukari_hen', 'mg_chg_cost', 'early_use', 'early_use_zei', 'kake_zei_sagaku', 'keiyaku_gaku', 'zan_gaku_zei', 'gojo_harai');
        foreach ($sumArr as $key => $value) {
            // 該当する項目を判定
            if (in_array($key, $exist_arr, true)) {
                continue;
            }
            if ($key == '0050_2000') {
                $value['name'] = "その他";
            }
            if ($key == '1130') {
                $tatekae_sum += $value['sum'];
                continue;
            }
            // 中分類ごとの小計を配列に格納
            if ($value['sum'] != 0) {
                $row_arr[] = array($value['name'], $value['sum'], null, null, null, null, null, null);
            }
            $total1 += $value['sum'];
        }
        // 合計項目を表示する行までから配列を入れる
        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 10; $index++) {
            $row_arr[] = null;
        }
        $total5 = 0;
        $total6 = 0;
        $total7 = 0;
        $total8 = 0;
        // 合計
        $total4 = $total1 + $tatekae_sum;
        if ($tatekae_sum > 0) {
            $row_arr[] = array($item_indent . "合計　　　　　　　①", $total1);
            // 立替金合計
            $row_arr[] = array($item_indent . '立替費用　　　　　②', $tatekae_sum);
            // 合計の消費税
            if (isset($sumArr['tax'])) {
                $value = $sumArr['tax'];
                $row_arr[] = array($item_indent . '①＋②の消費税　　③', $value['sum']);
                $total5 = $value['sum'];
            }
        } else {
            $row_arr[] = array($item_indent . "合計　　　　　①", $total1);
            // 合計の消費税
            if (isset($sumArr['tax'])) {
                $value = $sumArr['tax'];
                $row_arr[] = array($item_indent . '①の消費税　　②', $value['sum']);
                $total5 = $value['sum'];
            }
        }
        // 【F】立替金
        if (isset($sumArr[self::DAI_BUNRUI_6])) {
            $value = $sumArr[self::DAI_BUNRUI_6];
            $in_zei_disp = "";
            if ($sumArr[self::DAI_BUNRUI_6]['sum_in_zei'] > 0) {
                $in_zei_disp = "(内)";
            }
            $row_arr[] = array($item_indent . $value['name'] . '　', null, null, null, null, null, $value['sum'], $in_zei_disp);
            $total6 = $value['sum'];
        }

        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            $row_arr[] = array($item_indent . $value['name'] . '　', $value['sum']);
            $total7 = $value['sum'];
        }

        // 契約役務外利用を除いた合計金額
        $total_sum = $total4 + $total5 + $total6 + $total7;
        $num_title = "③";
        if ($tatekae_sum > 0) {
            $row_arr[] = array($item_indent . "合計金額＝①＋②＋③", $total_sum);
            $num_title = "④";
        } else {
            $row_arr[] = array($item_indent . "合計金額＝①＋②", $total_sum);
        }
        // 契約役務外利用表示
        //self::setSekoKaiyakuPrc($db, $seko_no, $pdfObj);
        $exist_arr = array('gojo_harai');
        $total9 = 0;
        foreach ($sumArr as $key => $value) {
            // 該当する項目を判定
            if (in_array($key, $exist_arr, true)) {
                // 中分類ごとの小計を配列に格納
                if ($value['sum'] != 0) {
                    $row_arr[] = array("　　" . $value['name'] . $num_title, $value['sum'], null, null, null, null, null, null);
                }
                $total9 += $value['sum'];
            }
        }
        $total_sum += $total9;
        // 預り金（返礼品）
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            $row_arr[] = array($item_indent . $value['name'] . '　', null, null, null, null, null, $value['sum'], "(内)");
            $total8 = $value['sum'];
        }
        // 合計項目表示行は５行
        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 5; $index++) {
            $row_arr[] = null;
        }
        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        // 1. 御見積金額合計
        $row_arr = null;
        $total_sum = $total_sum + $total8;
        // 円マークを付ける
        if (isset($total_sum) && strlen($total_sum) > 0) {
            $sum = '￥' . number_format($total_sum);
        }
        for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
            $set_arr[$index]['y'] = 553;
        }
        $row_arr[] = array('', null, null, null, $sum);
        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
        $row_arr = null;
        // 御見積総合計
        $total_sum_num = $total4 + $total5 + $total6 + $total7 + $total8 + $total9;
        // 円マークを付ける
        if (!empty($total_sum_num)) {
            $total_sum_num = '￥' . number_format($total_sum_num) . '＊';
        }
        $pdfObj->write_string(array('x' =>50, 'y' => 153, 'width' => 158, 'height' => 15, 'font_size' => 15, 'align' => 'R'), $total_sum_num);
        // 消費税金額の内訳出力     2019/04/30 sugiyama keigen
        if ($this->_isKeigenAppliedCtxt()) {
            $this->outZeiUchiwake($pdfObj, 2, $recWk[0]['uri_den_no']); // 2:法事
        }
        // 備考
        $pdfObj->write_strings2(array('x' => 50, 'y' => 602, 'width' => 315, 'height' => 72, 'font_size' => 12), $recWk[0]['biko1']);
    }

    /**
     * 表紙用のデータ＆明細用のデータを作成する（法事）
     *
     * <AUTHOR> Sugiyama
     * @since  2019/12/03
     * @param Msi_Sys_Db $db データベース
     * @param string $seko_no 施行番号
     * @param string $data_kbn データ区分
     * @param string $seko_no_sub 施行番号枝番
     * @param string $print_kbn 印刷区分
     * @return type
     */
    private function createDetailDataHouji($db, $seko_no, $data_kbn, $seko_no_sub, $print_kbn) {
        // 出力する明細行
        $row_arr = array();
        // 明細金額を集計した配列
        $sumArr = array();
        $sumArr[self::SUMID_A_PLAN] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_PLAN]);
        $sumArr[self::SUMID_A_GOJOKAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAI]);
        $sumArr[self::SUMID_A_GOJOKAINEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        $sumArr[self::SUMID_A_NEBIKI_PRC] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_NEBIKI_PRC]);
        $sumArr[self::SUMID_A_GOJOKAIGAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAIGAI]);
        $sumArr[self::SUMID_A_SETNEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_SETNEBIKI]);
        $sumArr[self::SUMID_A_KAIINNEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_KAIINNEBIKI]);
        $sumArr[self::SUMID_A_UNPLAN] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_UNPLAN]);
        $sumArr[self::SUMID_A_UNPLANSAGAKU] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_UNPLANSAGAKU]);
        // ブレーク処理
        $break_key_dai_bunrui_cd = '';
        $break_key_chu_bunrui_cd = '';
        $break_key_food_and_drink = '';
        $broken_dai_bunrui_nm = '';
        $broken_chu_bunrui_nm = '';
        // 集計用
        $total = 0;
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $shokei_ippan = 0; // 項目ごとの金額小計
        $shokei_nebiki1 = 0; // 項目ごとの互助会値引き小計
        $shokei_nebiki2 = 0; // 項目ごとの割引小計

        $order_by = array('dai_bunrui_cd ASC'
            , 'chu_bunrui_sum_kbn ASC'
            , 'chu_bunrui_cd ASC'
            , 'mitumori_print_seq ASC'
            , 'disp_no ASC'
        );
        $shohin_kbn_cond = array('x', "T2.shohin_kbn <> :x1_1", array(':x1_1' => self::SHOHIN_KBN_2000));
        $recMsi = DataMapper_Pdf1101::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__x1" => $shohin_kbn_cond, "__etc_orderby" => $order_by), $print_kbn);
        $shohin_kbn_cond = array('x', "T2.shohin_kbn = :x1_1", array(':x1_1' => self::SHOHIN_KBN_2000));
        $recMsi2 = DataMapper_Pdf1101::findMsi($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__x1" => $shohin_kbn_cond, "__etc_orderby" => $order_by), $print_kbn);
        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);

        $recMsi_plan = array();
        $recMsi_notplan = array();
        foreach ($recMsi as $item) { //プラン商品とプラン外商品に振り分ける
            // プラン商品:1 プラン外商品:0 奉仕料:NULL
            $item['in_seko_plan'] === 1 ? $recMsi_plan[] = $item : $recMsi_notplan[] = $item;
        }
        if (Msi_Sys_Utils::myCount($recMsi_plan) > 0) {    // プラン商品存在判定
            if (Msi_Sys_Utils::myCount($recMsi_notplan) > 0) {   // プラン外商品存在判定
                if ($recMsi_notplan[0]['dai_bunrui_cd'] === '0010') {    // プラン外商品の先頭が葬送儀礼の場合
                    //プラン商品とプラン外商品の間に空白行を挿入する
                    $dummy = array_map(function() {
                        return null;
                    }, $recMsi[0]);
                    $dummy['dai_bunrui_cd'] = '0010';
                    $recMsi = array_merge($recMsi_plan, array($dummy), $recMsi_notplan);
                }
            }
        }
        $zatsu_ary = array();    // 雑収入用配列
        $in_zei_prc = 0;
        // 集計処理
        foreach ($recMsi as $value) {
            $dai_bunrui_cd = $value['dai_bunrui_cd'];           // 大分類コード
            $dai_bunrui_nm = $value['dai_bunrui_nm'];           // 大分類名
            $chu_bunrui_cd = $value['chu_bunrui_cd'];           // 中分類コード
            $chu_bunrui_nm = $value['chu_bunrui_nm'];           // 中分類名
            $shohin_kbn = $value['shohin_kbn'];                 // 商品区分
            $shohin_nm = $value['shohin_nm'];                   // 商品名
            $shohin_tkiyo_nm = $value['shohin_tkiyo_nm'];       // 商品摘要名
            $juchu_suryo = $value['juchu_suryo'];               // 受注数量
            $juchu_tnk = $value['uri_tnk'];                   // 受注単価
            $juchu_prc = $value['uri_prc'];                   // 受注金額
            $gojokai_nebiki_prc = $value['gojokai_nebiki_prc']; // 付帯割引
            $nebiki_prc = $value['nebiki_prc'];                 // 値引金額
            $sum_flg = isset($juchu_prc) || isset($gojokai_nebiki_prc) || isset($nebiki_prc);
            $seikyu_prc = $sum_flg ? $juchu_prc + $gojokai_nebiki_prc + $nebiki_prc : null; // 請求金額
            $zei_disp = ($juchu_tnk || $juchu_prc || $nebiki_prc || $gojokai_nebiki_prc) ? $value['zei_disp'] : ''; // 単価・値引がすべて0なら税区分表示しない
            $hoshi_disp = $value['hoshi_disp'];                 // サービス
            // ブレーク処理
            if ($break_key_chu_bunrui_cd != $chu_bunrui_cd) {
                if (Msi_Sys_Utils::myCount($row_arr) > 1) {
                    // 各大分類の最後に行う処理
                    // return直前にもこれと同じ処理を行う
                    // 合計行挿入
                    $row_arr[] = $this->makeTotalRow($broken_chu_bunrui_nm, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, $break_key_chu_bunrui_cd);
                    $row_arr[] = array();
                    $sumArr[$break_key_chu_bunrui_cd] = $this->makeDaibunruiSummary($broken_chu_bunrui_nm, $total, $total_ippan, $total_tokuten, $total_kyoka, $in_zei_prc);
                    // 別途費用の分類が変更された時に雑収入があれば表示する
                    //if(Msi_Sys_Utils::myCount($zatsu_ary) > 0){
                    //    $this->makeZatsuRow($zatsu_ary, $sumArr, $row_arr, $sum_flg, $break_key_chu_bunrui_cd);
                    //}
                }
                // 集計値リセット
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $shokei_ippan = 0; // 項目ごとの金額小計
                $shokei_nebiki1 = 0; // 項目ごとの互助会値引き小計
                $shokei_nebiki2 = 0; // 項目ごとの割引小計
                $in_zei_prc = 0;
                // ブレークキーの更新
                $break_key_chu_bunrui_cd = $chu_bunrui_cd;
                $broken_chu_bunrui_nm = $chu_bunrui_nm;
                // 次の大分類名行を挿入
                $row_arr[] = $this->makeRow(['shohin_nm' => '◆' . $chu_bunrui_nm . '◆']);
            } // end ブレーク処理
            // 軽減税率対応  2019/04/30 sugiyama keigen 
            if ($this->_isKeigenAppliedCtxt()) {
                if (isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2) { // 軽減税率適用
                    $this->_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $hoshi_disp = $hoshi_disp . '＊';
                }
            }
            //
            // 雑収入の判定 商品区分：2000
//                if($shohin_kbn === self::SHOHIN_KBN_2000){
//                    // 雑収入を配列に格納
//                    $zatsu_ary[] = $value;
//                    continue;
//                }else{
//                    // 明細行挿入
//                    $row_arr[] = $this->makeDetailRow($shohin_nm, $shohin_tkiyo_nm, $juchu_suryo, $juchu_tnk, $juchu_prc, $gojokai_nebiki_prc, $nebiki_prc, $seikyu_prc, $zei_disp, $hoshi_disp);
//                }
            // 明細行挿入
            $row_arr[] = $this->makeDetailRow($shohin_nm, $shohin_tkiyo_nm, $juchu_suryo, $juchu_tnk, $juchu_prc, $gojokai_nebiki_prc, $nebiki_prc, $seikyu_prc, $zei_disp, $hoshi_disp);
            $total_ippan += $juchu_prc;
            $total_tokuten += $gojokai_nebiki_prc + $nebiki_prc;
            $total += $seikyu_prc;
            $shokei_ippan += $juchu_prc; // 項目ごとの金額小計
            $shokei_nebiki1 += $gojokai_nebiki_prc; // 項目ごとの互助会値引き小計
            $shokei_nebiki2 += $nebiki_prc; // 項目ごとの割引小計
            $in_zei_prc += $value['in_zei_prc'];
        } // end 集計処理
        // 明細確認
        if (Msi_Sys_Utils::myCount($recMsi) > 0) {
            // 合計行挿入
            $row_arr[] = $this->makeTotalRow($broken_chu_bunrui_nm, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, $break_key_chu_bunrui_cd);
            $row_arr[] = array();
            $sumArr[$break_key_chu_bunrui_cd] = $this->makeDaibunruiSummary($broken_chu_bunrui_nm, $total, $total_ippan, $total_tokuten, $total_kyoka, $in_zei_prc);
        }
        // 別途費用の分類が変更された時に雑収入があれば表示する
        if (Msi_Sys_Utils::myCount($recMsi2) > 0) {
            $this->makeZatsuRow($recMsi2, $sumArr, $row_arr, $sum_flg, $break_key_chu_bunrui_cd);
        }
        return array($row_arr, $sumArr);
    }

    /**
     * 請求書（単品・別注品）
     * @version 2014/11/06 伝票備考２は入金備考に変更したので、印刷しないように修正 2014/11/06 Kayo
     * @version 2014/12/19 請求先名がない場合は、「様」を印刷しないように修正。
     * @version 2017/09/11 備考削除 $rec['denpyo_biko1'].PHP_EOL.$rec['denpyo_biko2'] Otake
     * @version 2017/09/11 御請求金額削除 $rec['seikyu_prc'] + $rec['zei_prc'] Otake
     * @version 2017/09/11 入金金額・入金情報削除 $rec['nyukin_prc'], $this->outNyukin() Otake
     * @version 2017/09/11 受付部門削除 $bumon['bumon_nm'], $rec['tanto_nm'], $issue_date, $rec['uri_den_no']
     * @param type $pdfObj
     * @param type $db
     * @param type $issue_date
     * @param type $uri_den_no
     * @param type $data_kbn
     * @return type
     */
    private function outData03($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $isNeedNote = false, $oldNumPages = 0) {
        $numPagesCur = $pdfObj->getNumPages(); // 現在の総ページ数

        if ($isNeedNote) {
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileNameNote[$this->getSourceFileIndex($data_kbn)]);
        } else {
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$this->getSourceFileIndex($data_kbn)]);
        }
        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');

        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        /* ****************************** *
         * 帳票出力に利用するデータ取得     *
         * ****************************** */
        $bumon = DataMapper_Pdf1101::getUriBumon($db, $uri_den_no);

        // 単品
        if ($data_kbn == 3) {
            $recWk = DataMapper_Pdf1101::findTanpin($db, array("uri_den_no" => $uri_den_no));
        } else {
            $recWk = DataMapper_Pdf1101::findBechu($db, array("uri_den_no" => $uri_den_no));
        }
        if (Msi_Sys_Utils::myCount($recWk) == 0) {
            return;
        }
        $rec = $recWk[0];
        $rec['seikyu_print_tanto_cd'] = App_Utils::getTantoCd(); // 請求書発行者

        /* ************** *
         * データの整理    *
         * ************** */
        // 別注品
        if ($data_kbn == 4) {
            // 別注品ではお届け先に喪主を出力
            $rec['nonyu_nm'] = $rec['m_nm'];
        }

        // 請求先
        $sekyu['sekyu_nm'] = $rec['sekyu_nm'];
        $sekyu['soufu_nm'] = $rec['sekyu_soufu_nm'];
        $sekyu['yubin_no'] = $rec['sekyu_yubin_no'];
        $sekyu['addr1'] = $rec['sekyu_addr1'];
        $sekyu['addr2'] = $rec['sekyu_addr2'];
        $sekyu['tel'] = $rec['sekyu_tel'];
        $sekyu['data_kbn'] = $rec['data_kbn'];
        $pay_method_cd = $rec['pay_method_cd'];

//        if ($data_kbn == 4 && ( $pay_method_cd == 10 || $pay_method_cd == 99 )) {
//            if (empty($sekyu['soufu_nm'])) {
//                $sekyu['soufu_nm'] = !$rec['sekyu_kbn'] ? $rec['seko_sekyu_nm'] : $rec['m_nm'];
//            }
//            if (empty($sekyu['yubin_no'])) {
//                if (!$rec['sekyu_kbn']) {
//                    $sekyu['yubin_no'] = $rec['seko_sekyu_yubin_no'];
//                    $sekyu['addr1'] = $rec['seko_sekyu_addr1'];
//                    $sekyu['addr2'] = $rec['seko_sekyu_addr2'];
//                } elseif ($rec['sekyu_kbn'] == 1 && $rec['mg_kbn'] == 0) {
//                    $sekyu['yubin_no'] = $rec['moshu_yubin_no'];
//                    $sekyu['addr1'] = $rec['moshu_addr1'];
//                    $sekyu['addr2'] = $rec['moshu_addr2'];
//                } else {
//                    $sekyu['yubin_no'] = $rec['kojin_yubin_no'];
//                    $sekyu['addr1'] = $rec['kojin_addr1'];
//                    $sekyu['addr2'] = $rec['kojin_addr2'];
//                }
//            }
//        }

        /***********
         * 帳票出力 *
         ***********/

        // 事業所番号
        if ($this->_isKeigenAppliedCtxt()) {
            $toroku_bango = App_KeigenUtils::getJigyoshaTorokuBango();
            if ($toroku_bango) {
                $bango_str = sprintf("事業者番号　%s", $toroku_bango);
                $pdfObj->write_string(array('x' => 429, 'y' => 25, 'width' => 150, 'height' => 15, 'font_size' => 9), $bango_str);
            }
        }
        // 日付
        if (isset($issue_date)) { // 発行日
            $pdfObj->write_date(array('type' => 'ymd', 'x' => 429, 'y' => 41, 'width' => 150, 'height' => 15, 'font_size' => 9), $issue_date, '発　行　日　Y年m月d日');
        }
        // 請求番号
        $pdfObj->write_string(array('x' => 470, 'y' => 75.5, 'width' => 75, 'height' => 10, 'font_size' => 9), $rec['seko_no']);
        $pdfObj->write_string(array('x' => 470, 'y' => 87, 'width' => 75, 'height' => 10, 'font_size' => 9), $rec['uri_den_no']);
        // 社判
        App_Utils2::syabanImgOut($db, $pdfObj, $rec, 377, 98, 2, null, 89);
        
        // 請求先情報出力
        $pdfObj->write_string(array('x' => 75, 'y' => 25, 'width' => 200, 'height' => 45, 'font_size' => 9), $sekyu['yubin_no']);
        $pdfObj->write_string(array('x' => 75, 'y' => 36, 'width' => 200, 'height' => 45, 'font_size' => 9), $sekyu['addr1']);
        $pdfObj->write_string(array('x' => 75, 'y' => 47, 'width' => 200, 'height' => 45, 'font_size' => 9), $sekyu['addr2']);

        $sekyu_nm = !empty($sekyu['sekyu_nm']) ? $sekyu['sekyu_nm'] : '';
        $sekyu_nm = $sekyu_nm."　様";
        //全角スラッシュを分割
        if (strpos($sekyu_nm, '／')) {
            $sekyu_nm = explode('／', $sekyu_nm);
        }
        // 請求先名
        if(is_array($sekyu_nm)){
            // スラッシュがある場合
            $x_seikyu = 0;
            foreach ($sekyu_nm as $sekyu_nm_val) {
                $pdfObj->write_string(array('x' => 75, 'y' => 95 + $x_seikyu, 'width' => 200, 'height' => 20, 'font_size' => 14, 'align' => 'L'), $sekyu_nm_val);
                $x_seikyu += 15;
            }
        }else{
            // スラッシュがない場合
            $pdfObj->write_string(array('x' => 75, 'y' => 95, 'width' => 200, 'height' => 20, 'font_size' => 14, 'align' => 'L'), $sekyu_nm);
        }
        // 故人名
        $kojin_nm = !empty($rec['k_nm']) ? '故　' . $rec['k_nm'] . ' 様' : '';
        if ($kojin_nm) {
            $pdfObj->write_string(array('x' => 55, 'y' => 196, 'width' => 170, 'height' => 15, 'font_size' => 12, 'align' => 'L'), $kojin_nm);
        }

        // 備考(お客様用)
        $pdfObj->write_strings(array('x' => 32, 'y' => 637, 'width' => 300, 'height' => 60), $rec['v_free7']);

        // 振込情報
        $bank_info = App_Utils2::getBankInfo($db, $bumon['bumon_cd']);
        $yokin_nm = $this->getCodeName($db, '1980', $bank_info['yokin_sbt']);
        $bank_bun1 = 'お振込の場合は下記の銀行をご利用ください。';
        $bank_bun2 = 'お振込の際はお名前の前に請求書右上の請求番号の下５桁を';
        $bank_bun7 = 'ご記入（ご入力）ください。';
        $bank_bun3 = '例）54321 ナリタタロウ';
        $bank_bun4 = '恐れ入りますが、振込手数料はお客様の負担でお願い致します。';
        $bank_bun5 = $bank_info['bank_nm'] . '　　' . $bank_info['shiten_nm'] . '　　口座番号（' . $yokin_nm . '）' . $bank_info['kouza_no'];
        $bank_bun6 = '口座名義人　' . $bank_info['koza_meigi_nm'];
        $pdfObj->write_string(array('x' => 30, 'y' => 680, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun1);
        $pdfObj->write_string(array('x' => 30, 'y' => 691, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun2);
        $pdfObj->write_string(array('x' => 30, 'y' => 702, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun7);
        $pdfObj->write_string(array('x' => 30, 'y' => 713, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun3);
        $pdfObj->write_string(array('x' => 30, 'y' => 724, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun4);
        $pdfObj->write_string(array('x' => 30, 'y' => 741, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun5);
        $pdfObj->write_string(array('x' => 30, 'y' => 752, 'width' => 400, 'height' => 45, 'font_size' => 10), $bank_bun6);

        // 印鑑(発行者)
        $inkan_img = $this->getTantoInkan($db, $rec['seikyu_print_tanto_cd']);
        if (isset($inkan_img)) {
            $img = $db->readBlobCont($inkan_img);
            $pdfObj->seal_out(520, 725, $img);
        }

        $pdfObj->write_string(array('x' => 360, 'y' => 760, 'width' => 405, 'height' => 45, 'font_size' => 10), '受付部門');         // 受付部門
        $pdfObj->write_string(array('x' => 410, 'y' => 760, 'width' => 155, 'height' => 45, 'font_size' => 10), $bumon['bumon_nm']); // 受付部門
        // 明細書
        $zei_prc = DataMapper_SeikyuCalc::getZeiPrc($db, array('uri_den_no' => $uri_den_no));

        $prcArr = array('uri_prc_sum' => $rec['uri_prc_sum'] + $rec['uri_hepn_sum'] + $rec['uri_nebk_sum'], 'zei_prc' => $zei_prc['out_zei_prc']);
        $seikyu_zan = $prcArr['uri_prc_sum'] + $prcArr['zei_prc'];

        $str_seikyu_zan = '￥' . Msi_Sys_Utils::filterComma($seikyu_zan) . '*';
        $pdfObj->write_string(array('x' => 40, 'y' => 172, 'width' => 165, 'height' => 15, 'font_size' => 16, 'align' => 'R'), $str_seikyu_zan); // 今回御請求金額
        $this->outDataDetail03($pdfObj, $db, $uri_den_no, $data_kbn, $rec['tax_kbn'], $prcArr, $isNeedNote);


        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);
            $pdfObj->write_string(array('x' => 45, 'y' => 815, 'width' => 530, 'height' => 15, 'align' => 'C'), ($page - $oldNumPages) . '/' . ($numPages - $oldNumPages));

            // 明細書
            if ($page > $numPagesCur + 1) {
                // 請求先名
                //$pdfObj->write_string(array('x' => 45, 'y' => 59, 'width' => 180, 'height' => 15, 'font_size' => 15, 'align' => 'C'), $sekyu['sekyu_nm']);
                // 発行日
                $pdfObj->write_date(array('type' => 'ymd', 'x' => 440, 'y' => 773, 'width' => 115, 'height' => 9, 'font_size' => 9), $issue_date, 'Y年m月d日');
                // 請求No.
                $pdfObj->write_string(array('x' => 440, 'y' => 784, 'width' => 115, 'height' => 9, 'font_size' => 9), $rec['uri_den_no']);
            }

            // 軽減税率対応 凡例   sugiyama keigen
            if ($this->_isKeigenAppliedCtxt()) { // &&
                // isset($this->_is_reduced_tax_rate_row_exists) && $this->_is_reduced_tax_rate_row_exists ) {
                $pdfObj->write_string(array('x' => 28,
                    'y' => ($page == ($numPagesCur + 1) ? 599 : 774),
                    'width' => 180, 'height' => 9, 'font_size' => 9, 'align' => 'L'), '＊は軽減税率対象');
            }
        }
        if ($oldNumPages == 0) { // 控え用ページ調整
            $oldNumPages = $numPages;
            return $oldNumPages;
        }
    }

    /**
     * 会員番号を取得
     * @param type $db
     * @param type $seko_no
     * @param type $yoto_kbn
     * @return null
     */
    private function getKainNo($db, $seko_no, $yoto_kbn) {
        return DataMapper_SekoGojokaiMember::find($db, array("seko_no" => $seko_no, "yoto_kbn" => $yoto_kbn));
    }

    /**
     * 明細を出力
     *
     * @param type $pdfObj
     * @param array $detailRowArray
     * @param type $data_kbn
     * @param type $isNeedNote
     * @return array    明細金額を集計した配列
     */
    private function addSogiSekyushoDetail($pdfObj, $detailRowArray, $data_kbn, $isNeedNote = false) {
        static $meisai_top = 114;
        static $meisai_row_height = 26.57;
        static $meisai_row_count = 26;

        $set_arr = array();
        $set_arr[] = array('x' => 45, 'y' => $meisai_top, 'width' => 160, 'height' => 15); // 項目1 摘要がない場合
        $set_arr[] = array('x' => 45, 'y' => $meisai_top - 7, 'width' => 160, 'height' => 15); // 項目2 摘要がある場合
        $set_arr[] = array('x' => 45, 'y' => $meisai_top + 4, 'width' => 160, 'height' => 15); // 摘要
        $set_arr[] = array('x' => 210, 'y' => $meisai_top, 'width' => 36, 'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 245, 'y' => $meisai_top, 'width' => 53, 'height' => 15, 'type' => 'num'); // 単価
        $set_arr[] = array('x' => 298, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'type' => 'num'); // 金額
        $set_arr[] = array('x' => 355, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'type' => 'num'); // 付帯・割引
        $set_arr[] = array('x' => 415, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'type' => 'num'); // 割引金額
        $set_arr[] = array('x' => 475, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'type' => 'num'); // 請求金額
        $set_arr[] = array('x' => 533, 'y' => $meisai_top, 'width' => 25, 'height' => 15, 'align' => 'C'); // 税
        $set_arr[] = array('x' => 555, 'y' => $meisai_top, 'width' => 25, 'height' => 15, 'align' => 'C'); // サ
        if (!$isNeedNote) {
            $pdfObj->write_table($set_arr, $meisai_row_height, $detailRowArray, $meisai_row_count, __DIR__ . '/' . self::$sourceFileNameDetail[$this->getSourceFileIndex($data_kbn)]);
        } else {
            $pdfObj->write_table($set_arr, $meisai_row_height, $detailRowArray, $meisai_row_count, __DIR__ . '/' . self::$sourceFileNameDetailNote[$this->getSourceFileIndex($data_kbn)]);
        }
    }

    /**
     * 明細行用の配列 と 各種合計金額の配列 を作成する
     *
     * <AUTHOR> Otake
     * @since 2017/09/07
     * @param type $db
     * @param type $uri_den_no
     * @param type $seko_no
     * @param type $data_kbn
     * @param type $seko_no_sub
     * @param type $kain_no
     * @param type $print_kbn
     * @return array($detailRowArray, $sumArr)
     */
    private function createDetailData($db, $uri_den_no, $seko_no, $data_kbn, $seko_no_sub, $kain_no, $print_kbn) {
        // 出力する明細行
        $row_arr = array();
        // 明細金額を集計した配列
        $sumArr = array();
        $sumArr[self::SUMID_A_PLAN] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_PLAN]);
        $sumArr[self::SUMID_A_GOJOKAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAI]);
        $sumArr[self::SUMID_A_GOJOKAINEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        $sumArr[self::SUMID_A_NEBIKI_PRC] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_NEBIKI_PRC]);
        $sumArr[self::SUMID_A_GOJOKAIGAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAIGAI]);
        $sumArr[self::SUMID_A_SETNEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_SETNEBIKI]); // プラン値引
        $sumArr[self::SUMID_A_KAIINNEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_KAIINNEBIKI]); // 会員値引
        $sumArr[self::SUMID_A_UNPLAN] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_UNPLAN]); // プラン外商品計
        $sumArr[self::SUMID_A_UNPLANSAGAKU] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_UNPLANSAGAKU]); // プラン外商品差額
        // ブレーク処理
        $break_key_dai_bunrui_cd = '';
        $break_key_food_and_drink = '';
        $broken_dai_bunrui_nm = '';
        // 施行時解約処理用
        $sekoKaiyakuData = DataMapper_Pdf1101::getSekoKaiyakuData($seko_no);
        $seko_kaiyaku_prc_sum = 0;
        // 集計用
        $total = 0;
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $shokei_ippan = 0; // 項目ごとの金額小計
        $shokei_nebiki1 = 0; // 項目ごとの互助会値引き小計
        $shokei_nebiki2 = 0; // 項目ごとの割引小計

        $shohin_kbn_cond = array('x', "T2.shohin_kbn <> :x1_1", array(':x1_1' => self::SHOHIN_KBN_2000));
        $recMsi = DataMapper_Pdf1101::findMsi($db, array("uri_den_no" => $uri_den_no, "seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__x1" => $shohin_kbn_cond), $print_kbn);
        $shohin_kbn_cond = array('x', "T2.shohin_kbn = :x1_1", array(':x1_1' => self::SHOHIN_KBN_2000));
        $recMsi2 = DataMapper_Pdf1101::findMsi($db, array("uri_den_no" => $uri_den_no, "seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, "__x1" => $shohin_kbn_cond), $print_kbn);

        $recMsi_plan = array();
        $recMsi_notplan = array();
        foreach ($recMsi as $item) { //プラン商品とプラン外商品に振り分ける
            // プラン商品:1 プラン外商品:0 奉仕料:NULL
            $item['in_seko_plan'] === 1 ? $recMsi_plan[] = $item : $recMsi_notplan[] = $item;
        }
        if (Msi_Sys_Utils::myCount($recMsi_plan) > 0) {    // プラン商品存在判定
            if (Msi_Sys_Utils::myCount($recMsi_notplan) > 0) {   // プラン外商品存在判定
                if ($recMsi_notplan[0]['dai_bunrui_cd'] === '0010') {    // プラン外商品の先頭が葬送儀礼の場合
                    //プラン商品とプラン外商品の間に空白行を挿入する
                    //$dummy = array_map(function() {
                    //    return null;
                    //}, $recMsi[0]);
                    //$dummy['dai_bunrui_cd'] = '0010';
                    //$recMsi = array_merge($recMsi_plan, array($dummy), $recMsi_notplan);
                    $recMsi = array_merge($recMsi_plan, $recMsi_notplan);
                }
            }
        }

        $zatsu_ary = array();    // 雑収入用配列
        $in_zei_prc = 0;
        // 集計処理
        foreach ($recMsi as $value) {
            $dai_bunrui_cd = $value['dai_bunrui_cd'];           // 大分類コード
            $dai_bunrui_nm = $value['dai_bunrui_nm'];           // 大分類名
            $chu_bunrui_cd = $value['chu_bunrui_cd'];           // 中分類コード
            $shohin_kbn = $value['shohin_kbn'];                 // 商品区分
            $shohin_nm = $value['shohin_nm'];                   // 商品名
            $shohin_tkiyo_nm = $value['shohin_tkiyo_nm'];       // 商品摘要名
            $juchu_suryo = $value['juchu_suryo'];               // 受注数量
            $uri_tnk = $value['uri_tnk'];                       // 受注単価
            $uri_prc = $value['uri_prc'];                       // 受注金額
            $gojokai_nebiki_prc = $value['gojokai_nebiki_prc']; // 付帯割引
            $nebiki_prc = $value['nebiki_prc'];                 // 値引金額
            $sum_flg = isset($uri_prc) || isset($gojokai_nebiki_prc) || isset($nebiki_prc);
            $seikyu_prc = $sum_flg ? $uri_prc + $gojokai_nebiki_prc + $nebiki_prc : null; // 請求金額
            $zei_disp = ($uri_tnk || $uri_prc || $nebiki_prc || $gojokai_nebiki_prc) ? $value['zei_disp'] : ''; // 単価・値引がすべて0なら税区分表示しない
            //$hoshi_disp = $value['hoshi_disp'];                 // サービス
            $hoshi_disp = "";
            $seko_kaiyaku_prc = $value['seko_kaiyaku_prc']; // 契約役務外利用]
            // 軽減税率対応  2019/05/xx sugiyama keigen 
            if ($this->_isKeigenAppliedCtxt()) {
                if (isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2) { // 軽減税率適用
                    $this->_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $hoshi_disp = '＊';
                }
            }
            // ブレーク処理
            if ($break_key_dai_bunrui_cd != $dai_bunrui_cd) {
                if (Msi_Sys_Utils::myCount($row_arr) > 1) {
                    // 各大分類の最後に行う処理
                    // return直前にもこれと同じ処理を行う
                    /// 印刷グループ(1,2,3,4)の施行時解約があればここで出力
                    // 別途費用・立替費用は各明細の直後の行に出力
//                    $seko_kaiyaku_row = $this->makeSekoKaiyakuSummaryRow($sekoKaiyakuData, $break_key_dai_bunrui_cd);
//                    if ($seko_kaiyaku_row) {
//                        $row_arr[] = $seko_kaiyaku_row;
//                        $shokei_ippan += $seko_kaiyaku_row[5];
//                        $total += $seko_kaiyaku_row[8];
//                    }
                    // 合計行挿入
                    $row_arr[] = $this->makeTotalRow($broken_dai_bunrui_nm, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, $break_key_dai_bunrui_cd);
                    $row_arr[] = array();
                    $sumArr[$break_key_dai_bunrui_cd] = $this->makeDaibunruiSummary($broken_dai_bunrui_nm, $total, $total_ippan, $total_tokuten, $total_kyoka, $in_zei_prc);
                    // 別途費用の分類が変更された時に雑収入があれば表示する
                    //if(Msi_Sys_Utils::myCount($zatsu_ary) > 0){
                    //    $this->makeZatsuRow($zatsu_ary, $sumArr, $row_arr, $sum_flg, $break_key_dai_bunrui_cd);
                    //}
                }
                // 集計値リセット
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $shokei_ippan = 0;   // 項目ごとの金額小計
                $shokei_nebiki1 = 0; // 項目ごとの互助会値引き小計
                $shokei_nebiki2 = 0; // 項目ごとの割引小計
                $in_zei_prc = 0;
                // ブレークキーの更新
                $break_key_dai_bunrui_cd = $dai_bunrui_cd;
                $broken_dai_bunrui_nm = $dai_bunrui_nm;
                // 次の大分類名行を挿入
                $row_arr[] = $this->makeRow(['shohin_nm' => '◆' . $dai_bunrui_nm . '◆']);
            } // end ブレーク処理

            /* #871対応 商品が以下の分類の時 商品区分名を表示する
             * 大分類0030 料理・飲物、中分類0210 料理関係、商品区分0610 通夜料理
             * 大分類0030 料理・飲物、中分類0210 料理関係、商品区分0620 告別料理
             * 大分類0030 料理・飲物、中分類0210 料理関係、商品区分0640 通夜飲物
             * 大分類0030 料理・飲物、中分類0210 料理関係、商品区分0645 告別式飲物
             */
            if ($dai_bunrui_cd === '0030' && $chu_bunrui_cd === '0210') {
                $check_key_food_and_drink = $dai_bunrui_cd . $chu_bunrui_cd . $value['shohin_kbn'];
                if ($break_key_food_and_drink !== $check_key_food_and_drink) {
                    $row_arr[] = $this->makeRow(['shohin_nm' => '【' . $value['koumoku'] . '】']);
                    $break_key_food_and_drink = $check_key_food_and_drink;
                }
            } // end #871
            // 雑収入の判定 商品区分：2000
//            if($shohin_kbn === self::SHOHIN_KBN_2000){
//                // 雑収入を配列に格納
//                $zatsu_ary[] = $value;
//                continue;
//            }else{
//                // 明細行挿入
//                $row_arr[] = $this->makeDetailRow($shohin_nm, $shohin_tkiyo_nm, $juchu_suryo, $uri_tnk, $uri_prc, $gojokai_nebiki_prc, $nebiki_prc, $seikyu_prc, $zei_disp, $hoshi_disp);
//            }            
//            if ($seko_kaiyaku_prc) {
//                $row_arr[] = $this->makeDetailRow('契約役務外利用', null, 1, $seko_kaiyaku_prc, $seko_kaiyaku_prc, null, null, $seko_kaiyaku_prc, '内', null);
//                $shokei_ippan += $seko_kaiyaku_prc;
//                $total_ippan += $seko_kaiyaku_prc;
//                $total += $seko_kaiyaku_prc;
//                $seko_kaiyaku_prc_sum += $seko_kaiyaku_prc;
//            }
            // 明細行挿入
            $row_arr[] = $this->makeDetailRow($shohin_nm, $shohin_tkiyo_nm, $juchu_suryo, $uri_tnk, $uri_prc, $gojokai_nebiki_prc, $nebiki_prc, $seikyu_prc, $zei_disp, $hoshi_disp);
            // 葬送儀礼
            if ($dai_bunrui_cd == self::DAI_BUNRUI_1) {
                $sumArr[self::SUMID_A_PLAN]['sum'] += $uri_prc; // 基本費用
                // 値引項目細分化 2017/08/10 MOD Otake $sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] += $nebiki_prc; // 付帯特典
                if ($value['chu_bunrui_cd'] == self::CHU_BUNRUI_CD_PLN) {
                    $sumArr[self::SUMID_A_PLAN]['sum'] += $gojokai_nebiki_prc; // プラン値引きは基本費用に合算する
                } else {
                    $sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] += $gojokai_nebiki_prc; // 使用コース割引(付帯特典)
                }
                $sumArr[self::SUMID_A_NEBIKI_PRC]['sum'] += $nebiki_prc; // 割引金額
            }
            $total_ippan += $uri_prc;
            $total_tokuten += $gojokai_nebiki_prc + $nebiki_prc;
            $total += $seikyu_prc;
            $shokei_ippan += $uri_prc; // 項目ごとの金額小計
            $shokei_nebiki1 += $gojokai_nebiki_prc; // 項目ごとの互助会値引き小計
            $shokei_nebiki2 += $nebiki_prc; // 項目ごとの割引小計
            $in_zei_prc += $value['in_zei_prc'];
        } // end 集計処理
        if (Msi_Sys_Utils::myCount($recMsi) > 0) {
            // 施行時解約があればここで出力
//            $seko_kaiyaku_row = $this->makeSekoKaiyakuSummaryRow($sekoKaiyakuData, $break_key_dai_bunrui_cd);
//            if ($seko_kaiyaku_row) {
//                $row_arr[] = $seko_kaiyaku_row;
//                $shokei_ippan += $seko_kaiyaku_row[5];
//                $total += $seko_kaiyaku_row[8];
//            }
            // 合計行挿入
            $row_arr[] = $this->makeTotalRow($broken_dai_bunrui_nm, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, $break_key_dai_bunrui_cd);
            $row_arr[] = array();
            $sumArr[$break_key_dai_bunrui_cd] = $this->makeDaibunruiSummary($broken_dai_bunrui_nm, $total - $seko_kaiyaku_prc_sum, $total_ippan, $total_tokuten, $total_kyoka, $in_zei_prc);
        }
        // 別途費用の分類が変更された時に雑収入があれば表示する
        if (Msi_Sys_Utils::myCount($recMsi2) > 0) {
            $this->makeZatsuRow($recMsi2, $sumArr, $row_arr, $sum_flg, $break_key_dai_bunrui_cd);
        }
        return array($row_arr, $sumArr);
    }

    private function makeZatsuRow(&$zatsu_ary, &$sumArr, &$row_arr, $sum_flg, $dai_bunrui_cd) {
        $total_ippan = 0;
        $total_tokuten = 0;
        $total = 0;
        $total_kyoka = 0;
        $shokei_ippan = 0; // 項目ごとの金額小計
        $shokei_nebiki1 = 0; // 項目ごとの互助会値引き小計
        $shokei_nebiki2 = 0; // 項目ごとの割引小計
        $kamoku = "";
        $in_zei_prc = 0;
        foreach ($zatsu_ary as $index => $value) {
            if ($index === 0) {
                $row_arr[] = $this->makeRow(['shohin_nm' => '◆' . $value['koumoku'] . '◆']);
                $kamoku = $value['koumoku'];
            }
            $shohin_nm = $value['shohin_nm'];                   // 商品名
            $shohin_tkiyo_nm = $value['shohin_tkiyo_nm'];       // 商品摘要名
            $juchu_suryo = $value['juchu_suryo'];               // 受注数量
            $juchu_tnk = $value['uri_tnk'];                   // 受注単価
            $juchu_prc = $value['uri_prc'];                   // 受注金額
            $gojokai_nebiki_prc = $value['gojokai_nebiki_prc']; // 付帯割引
            $nebiki_prc = $value['nebiki_prc'];                 // 値引金額
            $seikyu_prc = $sum_flg ? $juchu_prc + $gojokai_nebiki_prc + $nebiki_prc : null; // 請求金額
            $zei_disp = ($juchu_tnk || $juchu_prc || $nebiki_prc || $gojokai_nebiki_prc) ? $value['zei_disp'] : ''; // 単価・値引がすべて0なら税区分表示しない
            $hoshi_disp = $value['hoshi_disp'];                 // サービス
            $row_arr[] = $this->makeDetailRow($shohin_nm, $shohin_tkiyo_nm, $juchu_suryo, $juchu_tnk, $juchu_prc, $gojokai_nebiki_prc, $nebiki_prc, $seikyu_prc, $zei_disp, $hoshi_disp);
            $total_ippan += $juchu_prc;
            $total_tokuten += $gojokai_nebiki_prc + $nebiki_prc;
            $total += $seikyu_prc;
            $shokei_ippan += $juchu_prc;           // 項目ごとの金額小計
            $shokei_nebiki1 += $gojokai_nebiki_prc;  // 項目ごとの互助会値引き小計
            $shokei_nebiki2 += $nebiki_prc;          // 項目ごとの割引小計
            $in_zei_prc += $value['in_zei_prc'];
        }
        // 合計金額集計
        if (Msi_Sys_Utils::myCount($zatsu_ary) > 0) {
            $row_arr[] = $this->makeTotalRow($kamoku, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, $dai_bunrui_cd);
            $sumArr[self::DAI_BUNRUI_5_2000] = $this->makeDaibunruiSummary($kamoku, $total, $total_ippan, $total_tokuten, $total_kyoka, $in_zei_prc);
        }
        // 配列の初期化
        $zatsu_ary = array();
    }

    /**
     * $sumArr用のデータ作成関数
     *
     * @param type $broken_dai_bunrui_nm
     * @param type $total
     * @param type $total_ippan
     * @param type $total_tokuten
     * @param type $total_kyoka
     * @return type
     */
    private function makeDaibunruiSummary($broken_dai_bunrui_nm, $total, $total_ippan, $total_tokuten, $total_kyoka, $in_zei_prc = 0) {
        $dai_bunrui_summary = [
            'name' => $broken_dai_bunrui_nm,
            'sum' => $total,
            'sum_i' => $total_ippan,
            'sum_t' => $total_tokuten,
            'sum_k' => $total_kyoka,
            'sum_in_zei' => $in_zei_prc,
        ];
        return $dai_bunrui_summary;
    }

    /**
     * $row_arr用のデータ作成関数
     * - 合計行の作成
     *
     * @param type $dai_bunrui_nm
     * @param type $shokei_ippan
     * @param type $shokei_nebiki1
     * @param type $shokei_nebiki2
     * @param type $total
     * @return type
     */
    private function makeTotalRow($dai_bunrui_nm, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, $dai_bunrui_cd = null) {
        $argument = [
            'shohin_nm' => '【' . $dai_bunrui_nm . '合計】',
            'uri_prc' => $shokei_ippan,
            'gojokai_nebiki_prc' => $shokei_nebiki1,
            'nebiki_prc' => $shokei_nebiki2,
            'seikyu_prc' => $total,
        ];
        return $this->makeRow($argument, $dai_bunrui_cd);
    }

    private function makeSekoKaiyakuSummaryRow($sekoKaiyakuData, $dai_bunrui_cd) {
        $seko_kaiyaku_prc = 0;
        switch (true) {
            case $dai_bunrui_cd === '0010':
                $seko_kaiyaku_prc += empty($sekoKaiyakuData['sk_type_1']) ? 0 : $sekoKaiyakuData['sk_type_1'];
                break;
            case $dai_bunrui_cd === '0020':
                $seko_kaiyaku_prc += empty($sekoKaiyakuData['sk_type_2']) ? 0 : $sekoKaiyakuData['sk_type_2'];
                break;
            case $dai_bunrui_cd === '0030':
                $seko_kaiyaku_prc += empty($sekoKaiyakuData['sk_type_3']) ? 0 : $sekoKaiyakuData['sk_type_3'];
                $seko_kaiyaku_prc += empty($sekoKaiyakuData['sk_type_4']) ? 0 : $sekoKaiyakuData['sk_type_4'];
                break;
            default:
                break;
        }
        if ($seko_kaiyaku_prc) {
            return $this->makeDetailRow('契約役務外利用', null, 1, $seko_kaiyaku_prc, $seko_kaiyaku_prc, null, null, $seko_kaiyaku_prc, '内', null);
        } else {
            return [];
        }
    }

    /**
     * $row_arr用のデータ作成関数
     * - 商品行の作成
     *
     * @param type $shohin_nm
     * @param type $shohin_tkiyo_nm
     * @param type $juchu_suryo
     * @param type $uri_tnk
     * @param type $uri_prc
     * @param type $gojokai_nebiki_prc
     * @param type $nebiki_prc
     * @param type $seikyu_prc
     * @param type $zei_disp
     * @param type $hoshi_disp
     * @return type
     */
    private function makeDetailRow($shohin_nm, $shohin_tkiyo_nm, $juchu_suryo, $uri_tnk, $uri_prc, $gojokai_nebiki_prc, $nebiki_prc, $seikyu_prc, $zei_disp, $hoshi_disp) {
        $argument = [
            'shohin_nm' => strlen($shohin_nm) ? '　　' . $shohin_nm : null,
            'shohin_tkiyo_nm' => strlen($shohin_tkiyo_nm) ? '　　' . $shohin_tkiyo_nm : null,
            'juchu_suryo' => $juchu_suryo,
            'uri_tnk' => $uri_tnk,
            'uri_prc' => $uri_prc,
            'gojokai_nebiki_prc' => $gojokai_nebiki_prc,
            'nebiki_prc' => $nebiki_prc,
            'seikyu_prc' => $seikyu_prc,
            'zei_disp' => $zei_disp,
            'hoshi_disp' => $hoshi_disp,
        ];
        return $this->makeRow($argument);
    }

    /**
     * $row_arr用のデータ作成関数
     * - テーブル出力用のデータフォーマット
     *
     * @param type $argument
     * @return type
     */
    private function makeRow($argument, $dai_bunrui_cd = null) {
        $shohin_nm = isset($argument['shohin_nm']) ? $argument['shohin_nm'] : null; // 商品名 -
        $shohin_tkiyo_nm = isset($argument['shohin_tkiyo_nm']) ? $argument['shohin_tkiyo_nm'] : null; // 摘要 -
        $juchu_suryo = isset($argument['juchu_suryo']) ? $argument['juchu_suryo'] : null; // 数量 -
        $uri_tnk = isset($argument['uri_tnk']) ? $argument['uri_tnk'] : null; // 単価 juchu_tnk
        $uri_prc = isset($argument['uri_prc']) ? $argument['uri_prc'] : null; // 金額(数量x単価) juchu_prc
        $gojokai_nebiki_prc = !empty($argument['gojokai_nebiki_prc']) ? $argument['gojokai_nebiki_prc'] : null; // 互助会値引き -
        $nebiki_prc = !empty($argument['nebiki_prc']) ? $argument['nebiki_prc'] : null; // 値引額 -
        $seikyu_prc = isset($argument['seikyu_prc']) ? $argument['seikyu_prc'] : null; // 請求金額(金額 + 各種値引) $disp_seikyu_prc
        $zei_disp = isset($argument['zei_disp']) ? $argument['zei_disp'] : null; // 税 -
        $hoshi_disp = isset($argument['hoshi_disp']) ? $argument['hoshi_disp'] : null; // サービス -

        $row = [
            strlen($shohin_tkiyo_nm) ? null : $shohin_nm, // 摘要なし
            strlen($shohin_tkiyo_nm) ? $shohin_nm : null, // 摘要あり
            $shohin_tkiyo_nm, // 摘要
            $juchu_suryo, // 数量
            $uri_tnk, // 単価
            $uri_prc, // 金額(数量x単価)
            $gojokai_nebiki_prc, // 互助会値引き
            $nebiki_prc, // 値引額
            $seikyu_prc, // 請求金額(金額 + 各種値引)
            $zei_disp, // 税
            $hoshi_disp, // サービス
            $dai_bunrui_cd,
        ];
        return $row;
    }

    /**
     * 明細出力(請求書)
     *
     * @param type $pdfObj
     * @param type $db
     * @param type $uri_den_no
     * @param type $seko_no
     * @param type $data_kbn
     * @param type $seko_no_sub
     * @param type $print_kbn
     * @return array 明細金額を集計した配列
     */
    private function outDataDetail02($pdfObj, $db, $uri_den_no, $seko_no, $data_kbn, $seko_no_sub, $print_kbn) {
        static $meisai_top = 95;
        static $meisai_row_height = 20.3;
        static $meisai_row_count = 34;

        // 明細金額を集計した配列
        $sumArr = array();
        $sumArr[self::SUMID_A_PLAN] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_PLAN]);
        $sumArr[self::SUMID_A_GOJOKAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAI]);
        $sumArr[self::SUMID_A_GOJOKAINEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        $sumArr[self::SUMID_A_NEBIKI_PRC] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_NEBIKI_PRC]);
        $sumArr[self::SUMID_A_GOJOKAIGAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAIGAI]);

        $set_arr[] = array('x' => 35, 'y' => $meisai_top, 'width' => 190, 'height' => 15); // 品名
        $set_arr[] = array('x' => 225, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 275, 'y' => $meisai_top, 'width' => 65, 'height' => 15, 'type' => 'num'); // 単価
        $set_arr[] = array('x' => 345, 'y' => $meisai_top, 'width' => 80, 'height' => 15, 'type' => 'num'); // 金額
        $set_arr[] = array('x' => 425, 'y' => $meisai_top, 'width' => 65, 'height' => 15, 'type' => 'num'); // 割引金額
        $set_arr[] = array('x' => 490, 'y' => $meisai_top, 'width' => 85, 'height' => 15, 'type' => 'num'); // 請求金額

        $breakKey = '';
        $chu_bunrui_nm = '';
        $row_arr = array();
        $total = 0;
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total_tax = 0;
        $shokei_ippan = 0; // 項目ごとの金額小計
        $shokei_nebiki = 0; // 項目ごとの値引き小計

        $recMsi = DataMapper_Pdf1101::findMsi($db
                        , array("uri_den_no" => $uri_den_no
                    , "seko_no" => $seko_no
                    , "data_kbn" => $data_kbn
                    , "seko_no_sub" => $seko_no_sub
                    , '__etc_orderby' => array('dai_bunrui_cd ASC'
                        , 'chu_bunrui_cd ASC'
                        , 'mitumori_print_seq ASC'
                        , 'disp_no ASC'
                    )
                        )
                        , $print_kbn
        );
        foreach ($recMsi as $value) {
            // 中分類が切替る場合は必要に応じて見出しと合計の追加を行う
            if ($breakKey != $value['chu_bunrui_cd']) {
                if (Msi_Sys_Utils::myCount($row_arr) > 1) {
                    if ($shokei_nebiki == 0) {
                        $shokei_nebiki = null;
                    }
                    $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, $shokei_ippan, $shokei_nebiki, $total);
                    $sumArr[$breakKey]['name'] = $chu_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                    $sumArr[$breakKey]['tax'] = $total_tax;
                }
                $row_arr[] = array('◆' . $value['chu_bunrui_nm'] . '◆', null, null, null, null, null);

                $breakKey = $value['chu_bunrui_cd'];
                $chu_bunrui_nm = $value['chu_bunrui_nm'];
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $total_tax = 0;

                $shokei_ippan = 0; // 項目ごとの金額小計
                $shokei_nebiki = 0; // 項目ごとの値引き小計
            }

            // 付帯割引
            $gojokai_nebiki_prc = $value['gojokai_nebiki_prc'];
            // 割引金額
            $nebiki_prc = $value['nebiki_prc'];
            // 請求金額
            $seikyu_prc = $value['uri_prc'] + $gojokai_nebiki_prc + $nebiki_prc;
            // 消費税
            $total_tax += $value['out_zei_prc'];

            if ($gojokai_nebiki_prc == 0) {
                $gojokai_nebiki_prc = null;
            }
            if ($nebiki_prc == 0) {
                $nebiki_prc = null;
            }
            $nebiki_sum = $gojokai_nebiki_prc + $nebiki_prc;
            if ($nebiki_sum == 0) {
                $nebiki_sum = null;
            }
            $shohin_nm = '　' . $value['shohin_nm'];
            $row_arr[] = array($shohin_nm
                , $value['juchu_suryo']
                , $value['uri_tnk']
                , $value['uri_prc']
                , $nebiki_sum
                , $seikyu_prc);

            // 葬送儀礼
            if ($value['dai_bunrui_cd'] == self::DAI_BUNRUI_1) {
                // 基本費用
                $sumArr[self::SUMID_A_PLAN]['sum'] += $value['uri_prc'];
                // 付帯特典
                $sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] += $value['gojokai_nebiki_prc'];
                // 割引金額
                $sumArr[self::SUMID_A_NEBIKI_PRC]['sum'] += $value['nebiki_prc'];
            }

            // 枕花は供花・供物に加算する
//			if ($value['print_group_cd'] == '99') {
//				$total_kyoka += $value['uri_prc'] + $gojokai_nebiki_prc + $nebiki_prc;
//			} else {
            $total_ippan += $value['uri_prc'];
            $total_tokuten += $gojokai_nebiki_prc + $nebiki_prc;
            $total += $seikyu_prc;
//			}
            $shokei_ippan += $value['uri_prc']; // 項目ごとの金額小計
            $shokei_nebiki += $gojokai_nebiki_prc + $nebiki_prc; // 項目ごとの値引き小計
        }

        if (Msi_Sys_Utils::myCount($recMsi) > 0) {
            if ($shokei_nebiki == 0) {
                $shokei_nebiki = null;
            }
            // 最後の中分類の合計を追加
            $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, $shokei_ippan, $shokei_nebiki, $total);
            $rec = $recMsi[Msi_Sys_Utils::myCount($recMsi) - 1];
            $sumArr[$rec['chu_bunrui_cd']]['name'] = $rec['chu_bunrui_nm'];
            $sumArr[$rec['chu_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['chu_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['chu_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['chu_bunrui_cd']]['sum_k'] = $total_kyoka;
            $sumArr[$rec['chu_bunrui_cd']]['tax'] = $total_tax;
        }
        $pdfObj->write_table($set_arr, $meisai_row_height, $row_arr, $meisai_row_count, __DIR__ . '/' . self::$sourceFileNameDetail[$this->getSourceFileIndex($data_kbn)]);
        return $sumArr;
    }

    /**
     * 明細を出力（単品・別注品）
     *
     * @param type $pdfObj
     * @param type $db
     * @param type $uri_den_no
     * @param type $data_kbn
     * @param type $tax_kbn
     * @param type $sumAry
     * @return なし
     */
    private function outDataDetail03($pdfObj, $db, $uri_den_no, $data_kbn, $tax_kbn, $sumAry, $isNeedNote = false) {
        static $meisai_top1 = 251;
        static $meisai_top2 = 115;
        static $y1 = 10; // 1行
        static $y1_1 = 3; // 1行の1行目
        static $y1_2 = 17; // 1行の2行目
        static $y2_1 = -1; // 1行の1行目
        static $y2_2 = 11; // 1行の2行目
        static $y2_3 = 23; // 1行の3行目
        static $meisai_row_height = 38.8;
        static $meisai_row_count1 = 9;
        static $meisai_row_count2 = 17;

        $set_arr[] = array('x' => 28, 'y' => $meisai_top1 + $y1, 'width' => 20, 'height' => 15, 'type' => 'num'); // 0 No.
        $set_arr[] = array('x' => 58, 'y' => $meisai_top1 + $y1, 'width' => 250, 'height' => 15);   // 1 品名（摘要なし）
        $set_arr[] = array('x' => 58, 'y' => $meisai_top1 + $y1_1, 'width' => 250, 'height' => 15);   // 2 品名（摘要あり）
        $set_arr[] = array('x' => 58, 'y' => $meisai_top1 + $y1_2, 'width' => 250, 'height' => 15);   // 3 摘要
        $set_arr[] = array('x' => 58, 'y' => $meisai_top1 + $y2_1, 'width' => 250, 'height' => 15);   // 4 名札1
        $set_arr[] = array('x' => 58, 'y' => $meisai_top1 + $y2_2, 'width' => 250, 'height' => 15);   // 5 名札2
        $set_arr[] = array('x' => 58, 'y' => $meisai_top1 + $y2_3, 'width' => 250, 'height' => 15);   // 6 名札3
        $set_arr[] = array('x' => 352, 'y' => $meisai_top1 + $y1, 'width' => 37, 'height' => 15, 'type' => 'num');   // 7数量
        $set_arr[] = array('x' => 384, 'y' => $meisai_top1 + $y1, 'width' => 24, 'height' => 15, 'align' => 'C');    // 8単位
        $set_arr[] = array('x' => 413, 'y' => $meisai_top1 + $y1, 'width' => 60, 'height' => 15, 'type' => 'num');   // 9単価
        $set_arr[] = array('x' => 483, 'y' => $meisai_top1 + $y1, 'width' => 70, 'height' => 15, 'type' => 'num');   // 10金額（値引きなし）
        $set_arr[] = array('x' => 483, 'y' => $meisai_top1 + $y1_1, 'width' => 70, 'height' => 15, 'type' => 'num'); // 11金額
        $set_arr[] = array('x' => 475, 'y' => $meisai_top1 + $y1_2, 'width' => 27, 'height' => 15, 'font_size' => 7); // 12（値引）
        $set_arr[] = array('x' => 498, 'y' => $meisai_top1 + $y1_2, 'width' => 55, 'height' => 15, 'type' => 'num'); // 13 値引額
        $set_arr[] = array('x' => 328, 'y' => $meisai_top1 + $y1, 'width' => 25, 'height' => 15, 'align' => 'C'); // 14税区分
        $set_arr[] = array('x' => 563, 'y' => $meisai_top1 + $y1, 'width' => 20, 'height' => 15); // 15 軽減税率対象マーク sugiyama keigen

        $row_arr = array();
        $rec = DataMapper_Pdf1101::findMsi03($db, array("uri_den_no" => $uri_den_no));
        for ($index = 0; $index < Msi_Sys_Utils::myCount($rec); $index++) {
            $value = $rec[$index];
            // 明細を「/」で分割する
            $msibiko1_ary = explode('/', preg_replace('/／/', '/', $value['msi_biko1']));
            for ($i = 0; $i < 2; $i++) {
                if (isset($msibiko1_ary[$i]) && strlen($msibiko1_ary[$i]) > 0) {
                    $ary[] = $msibiko1_ary[$i];   // 名札
                } else {
                    $ary[] = NULL;   // 名札
                }
            }
            // 1行目出力
            $arr = array();
            $arr[0] = $index + 1;
            $edit_flg = 0;
            if (strlen($value['shohin_tkiyo_nm']) > 0) {
                // 商品摘要あり
                $arr[1] = null;
                $arr[2] = $value['shohin_nm'];
                $arr[3] = $value['shohin_tkiyo_nm'];
                $arr[4] = null;
                $arr[5] = null;
                $arr[6] = null;
                $edit_flg = 1;
            } else if (strlen($value['nafuda_nm']) > 0) {
                // 名札あり
                $arr[1] = null;
                $arr[2] = $value['shohin_nm'];
                $arr[3] = $value['nafuda_nm'];
                $arr[4] = null;
                $arr[5] = null;
                $arr[6] = null;
                $edit_flg = 2;
            } else if (strlen($value['msi_biko1']) > 0) {
                // 明細備考１あり
                if (Msi_Sys_Utils::myCount($msibiko1_ary) >= 2) {
                    $arr[1] = null;
                    $arr[2] = null;
                    $arr[3] = null;
                    $arr[4] = $value['shohin_nm'];
                    $arr[5] = $msibiko1_ary[0];
                    $arr[6] = $msibiko1_ary[1];
                } else {
                    $arr[1] = null;
                    $arr[2] = $value['shohin_nm'];
                    $arr[3] = $value['msi_biko1'];
                    $arr[4] = null;
                    $arr[5] = null;
                    $arr[6] = null;
                }
                $edit_flg = 3;
            } else if (strlen($value['msi_biko2']) > 0) {
                // 明細備考１あり
                $arr[1] = null;
                $arr[2] = $value['shohin_nm'];
                $arr[3] = $value['msi_biko2'];
                $arr[4] = null;
                $arr[5] = null;
                $arr[6] = null;
                $edit_flg = 4;
            } else {
                // なし
                $arr[1] = $value['shohin_nm'];
                $arr[2] = null;
                $arr[3] = null;
                $arr[4] = null;
                $arr[5] = null;
                $arr[6] = null;
                $edit_flg = 5;
            }
            $arr[7] = $value['juchu_suryo'];
            $arr[8] = $value['tani_nm'];
            $arr[9] = $value['uri_tnk'];
            if (isset($value['nebiki_prc']) && $value['nebiki_prc'] != 0) {
                $arr[10] = NULL;
                $arr[11] = $value['uri_prc'];
                $arr[12] = '（割引）';
                $arr[13] = $value['nebiki_prc'];
            } else {
                $arr[10] = $value['uri_prc'];
                $arr[11] = NULL;
                $arr[12] = NULL;
                $arr[13] = NULL;
            }
            if (isset($value['zei_disp'])) {
                $arr[14] = $value['zei_disp'];
            }
            // 軽減税率対応  sugiyama keigen
            $value['reduced_tax_rate_txt'] = '';
            if ($this->_isKeigenAppliedCtxt()) {
                if (isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2) { // 軽減税率適用
                    $this->_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['reduced_tax_rate_txt'] = '＊';
                }
            }
            $arr[15] = $value['reduced_tax_rate_txt'];
            $row_arr[] = $arr;
            $out_flg = 1;
            if ($edit_flg >= 1) {    // 商品摘要の編集まであり
                if (strlen($value['nafuda_nm']) > 0 || strlen($value['msi_biko1']) > 0 || strlen($value['msi_biko2']) > 0) {
                    // 2行目出力
                    $arr = array();
                    $arr[0] = null;
                    if ($edit_flg == 1) { // 商品摘要編集あり
                        if (strlen($value['nafuda_nm']) > 0) {
                            if (strlen($value['msi_biko1']) > 0) {
                                if (Msi_Sys_Utils::myCount($msibiko1_ary) >= 2) {
                                    $arr[1] = null;
                                    $arr[2] = null;
                                    $arr[3] = null;
                                    $arr[4] = $value['nafuda_nm'];
                                    $arr[5] = $msibiko1_ary[0];
                                    $arr[6] = $msibiko1_ary[1];
                                } else {
                                    $arr[1] = null;
                                    $arr[2] = $value['nafuda_nm'];
                                    $arr[3] = $value['msi_biko1'];
                                    $arr[4] = null;
                                    $arr[5] = null;
                                    $arr[6] = null;
                                }
                            } else if (strlen($value['msi_biko2']) > 0) {
                                $arr[1] = null;
                                $arr[2] = $value['nafuda_nm'];
                                $arr[3] = $value['msi_biko2'];
                                $arr[4] = null;
                                $arr[5] = null;
                                $arr[6] = null;
                            } else {
                                $arr[1] = $value['nafuda_nm'];
                                $arr[2] = null;
                                $arr[3] = null;
                                $arr[4] = null;
                                $arr[5] = null;
                                $arr[6] = null;
                            }
                        } else if (strlen($value['msi_biko1']) > 0) {
                            if (strlen($value['msi_biko2']) > 0) {
                                if (Msi_Sys_Utils::myCount($msibiko1_ary) >= 2) {
                                    $arr[1] = null;
                                    $arr[2] = null;
                                    $arr[3] = null;
                                    $arr[4] = $msibiko1_ary[0];
                                    $arr[5] = $msibiko1_ary[1];
                                    $arr[6] = $value['msi_biko2'];
                                } else {
                                    $arr[1] = null;
                                    $arr[2] = $value['msi_biko1'];
                                    $arr[3] = $value['msi_biko2'];
                                    $arr[4] = null;
                                    $arr[5] = null;
                                    $arr[6] = null;
                                }
                            } else {
                                if (Msi_Sys_Utils::myCount($msibiko1_ary) >= 2) {
                                    $arr[1] = null;
                                    $arr[2] = $msibiko1_ary[0];
                                    $arr[3] = $msibiko1_ary[1];
                                    $arr[4] = null;
                                    $arr[5] = null;
                                    $arr[6] = null;
                                } else {
                                    $arr[1] = $value['msi_biko1'];
                                    $arr[2] = null;
                                    $arr[3] = null;
                                    $arr[4] = null;
                                    $arr[5] = null;
                                    $arr[6] = null;
                                }
                            }
                        } else {
                            $arr[1] = $value['msi_biko2'];
                            $arr[2] = null;
                            $arr[3] = null;
                            $arr[4] = null;
                            $arr[5] = null;
                            $arr[6] = null;
                        }
                    }
                    if ($edit_flg == 2) { // 名札編集あり
                        if (strlen($value['msi_biko1']) > 0) {
                            if (strlen($value['msi_biko2']) > 0) {
                                if (Msi_Sys_Utils::myCount($msibiko1_ary) >= 2) {
                                    $arr[1] = null;
                                    $arr[2] = null;
                                    $arr[3] = null;
                                    $arr[4] = $msibiko1_ary[0];
                                    $arr[5] = $msibiko1_ary[1];
                                    $arr[6] = $value['msi_biko2'];
                                } else {
                                    $arr[1] = null;
                                    $arr[2] = $value['msi_biko1'];
                                    $arr[3] = $value['msi_biko2'];
                                    $arr[4] = null;
                                    $arr[5] = null;
                                    $arr[6] = null;
                                }
                            } else {
                                if (Msi_Sys_Utils::myCount($msibiko1_ary) >= 2) {
                                    $arr[1] = null;
                                    $arr[2] = $msibiko1_ary[0];
                                    $arr[3] = $msibiko1_ary[1];
                                    $arr[4] = null;
                                    $arr[5] = null;
                                    $arr[6] = null;
                                } else {
                                    $arr[1] = $value['msi_biko1'];
                                    $arr[2] = null;
                                    $arr[3] = null;
                                    $arr[4] = null;
                                    $arr[5] = null;
                                    $arr[6] = null;
                                }
                            }
                        } else if (strlen($value['msi_biko2']) > 0) {
                            $arr[1] = $value['msi_biko2'];
                            $arr[2] = null;
                            $arr[3] = null;
                        } else {
                            $out_flg = 0;
                            $arr[1] = null;
                            $arr[2] = null;
                            $arr[3] = null;
                        }
                    }
                    if ($edit_flg == 3) { // 明細備考１編集あり
                        if (strlen($value['msi_biko2']) > 0) {
                            $arr[1] = $value['msi_biko2'];
                            $arr[2] = null;
                            $arr[3] = null;
                        } else {
                            $out_flg = 0;
                            $arr[1] = null;
                            $arr[2] = null;
                            $arr[3] = null;
                        }
                    }
                    $arr[7] = null;
                    $arr[8] = null;
                    $arr[9] = null;
                    $arr[10] = NULL;
                    $arr[11] = null;
                    $arr[12] = null;
                    $arr[13] = null;
                    $arr[14] = null;
                    if ($out_flg == 1) {
                        $row_arr[] = $arr;
                    }
                }
            }
        }

        // 明細（表紙）
        $row_arrWk = array_slice($row_arr, 0, $meisai_row_count1);
        $pdfObj->write_table_simple($set_arr, $meisai_row_height, $row_arrWk);
        // 明細１枚の場合、明細計を出力
        // 常に１ページ目に合計を印刷する。2014/10/26 Kayo
        // 内税の場合、消費税額の見出しに（内）を出力
        // keigen OLD
        // if ($tax_kbn == 1) {
        //     $pdfObj->write_string(array('x' => 465, 'y' => 571, 'width' => 30, 'height' => 15), '（内）');
        // }
        // 消費税金額の内訳出力     sugiyama keigen
        if ($this->_isKeigenAppliedCtxt()) {
            $this->outZeiUchiwake03($pdfObj, $uri_den_no);
        }

        if (Msi_Sys_Utils::myCount($row_arr) <= $meisai_row_count1) {
            
        } else {
            // 明細（２ページ目～）
            for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
                $set_arr[$index]['y'] = $set_arr[$index]['y'] - $meisai_top1 + $meisai_top2;
            }

            $row_arrWk = array_slice($row_arr, $meisai_row_count1);
            if ($isNeedNote) {
                $pdfObj->write_table($set_arr, $meisai_row_height, $row_arrWk, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileNameDetailNote[$this->getSourceFileIndex($data_kbn)]);
            } else {
                $pdfObj->write_table($set_arr, $meisai_row_height, $row_arrWk, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileNameDetail[$this->getSourceFileIndex($data_kbn)]);
            }
            // 確認用のグリッドを出力
            //$pdfObj->test_line_out(600, 1000);
        }
    }

    /**
     * 表紙の金額を出力
     *
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    //public static function outDataFaceSum01($pdfObj, $sumArr, $coseNm)
    public function outDataFaceSum01($db, $pdfObj, $sumArr, $coseNm, $recWk, $seko_no) {
        static $row_height = 14;
        static $item_indent = '　　';

        //互助会複数対応
        if (Msi_Sys_Utils::myCount($recWk) > 1) {
            $coseNm = "";
            $coseNm_bk = "";
            $coseNm_dub = null;
            $cose_ary = array();
            $i = 0;
            while ($i < Msi_Sys_Utils::myCount($recWk)) {
                if (isset($recWk[$i]['gojokai_cose_nm'])) {
                    $pos = array_search($recWk[$i]['gojokai_cose_nm'], $cose_ary);

                    //同コースが2回目の場合はスキップ
                    if ($coseNm_bk == $recWk[$i]['gojokai_cose_nm']) {
                        $coseNm_dub = $recWk[$i]['gojokai_cose_nm'];
                    }
                    //前回とコース名称が違う場合はコース名に追記する
                    else if ($coseNm_bk != $recWk[$i]['gojokai_cose_nm']) {
                        if (strlen($coseNm) > 0) {
                            $coseNm = $coseNm . '＋';
                        }
                        $coseNm = $coseNm . $recWk[$i]['gojokai_cose_nm'];
                    }
                    //前回分のコース名称確保
                    $coseNm_bk = $recWk[$i]['gojokai_cose_nm'];
                    $cose_ary[] = $recWk[$i]['gojokai_cose_nm'];
                }
                $i++;
            }
        }

        $set_arr[] = array('x' => 50, 'width' => 290, 'height' => 15); // 項目
        $set_arr[] = array('x' => 350, 'width' => 80, 'height' => 15, 'type' => 'num');    // 金額
        $set_arr[] = array('x' => 440, 'width' => 135, 'height' => 15); // 備考
        $set_arr[] = array('x' => 250, 'width' => 90, 'height' => 15); // 項目（２列目）
        $set_arr[] = array('x1' => 45, 'y1' => -1, 'x2' => 575, 'y2' => -1, 'width' => 1, 'color' => self::$color, 'type' => 'line'); // 上線
        $set_arr[] = array('x' => 350, 'width' => 80, 'height' => 15, 'align' => 'R');    // 小計金額
        //
		// 葬送儀礼費用
        //
		for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
            $set_arr[$index]['y'] = 236;
        }

        $total1 = 0;
        $row_arr = array();

        // コース変更
        if ($sumArr[self::SUMID_COSECHGGAKU]['sum'] != 0) {
            $biko_cose_chg = floor($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] / 10000) . '万コース→' .
                    floor(($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] + $sumArr[self::SUMID_COSECHGGAKU]['sum']) / 10000) . '万コース';
        } else if (isset($coseNm_dub)) {
            $tmpArray = array_count_values($cose_ary);
            $cnt = $tmpArray[$coseNm_dub];
            if ($cnt > 1) {
                $biko_cose_chg = $coseNm_dub . "(" . $cnt . "口)";
            } else {
                $biko_cose_chg = null;
            }
        } else {
            $biko_cose_chg = null;
        }
        // 葬送儀礼費用基本プラン　小計　　　　　　　　　　　　　（ア）
        // 葬送儀礼費用基本プラン　葬送儀礼費用プラン外選択品目　（イ）
        // 葬送儀礼費用プラン外選択品目　　　　　　　　　　　　　（ウ）
        // 葬送儀礼費用　小計　　　　　　（ア）＋（イ）＋（ウ）
        // の（ア）・（イ）・（ウ）を出力する
        static $fld_no = array('（ア）', '（イ）', '（ウ）');
        $fld_no_index = 0;

        // パックプラン
        if (isset($sumArr[self::DAI_BUNRUI_P])) {
            if ($sumArr[self::DAI_BUNRUI_P]['sum'] != 0) {
                $value = $sumArr[self::DAI_BUNRUI_P];
                $p_sum = $value['sum'];
                $row_arr[] = array($value['name'], $p_sum);
                $total1 += $value['sum'];
            }
        }
        // 【A】葬送儀礼費用基本プラン
        if (isset($sumArr[self::DAI_BUNRUI_1])) { // プラン料金
            if ($sumArr[self::SUMID_A_PLAN]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_PLAN];
                $db = Msi_Sys_DbManager::getMyDb();
                if ($this->checkRiyoPlan()) {
                    $plan_price = DataMapper_Pdf1101::getPlanPrice2($db, $seko_no);
                } else {
                    $plan_price = DataMapper_Pdf0113::getPlanPrice($db, $seko_no);
                }
                $web_disp_nm = DataMapper_Pdf0113::getPlanWebDisp($db, $seko_no);
                if ($plan_price) {
                    // プランが存在していればその金額を利用
                    $row_arr[] = array($value['name'], $plan_price, $web_disp_nm);
                    $total1 += $plan_price;
                    // 差額をプラン外商品計とする
                    $unplaned_prc = $value['sum'] - $plan_price;
                    $sumArr[self::SUMID_A_UNPLAN]['sum'] = $unplaned_prc;
                } else {
                    $row_arr[] = array($value['name'], $value['sum']);
                    $total1 += $value['sum'];
                }
            }
            if ($sumArr[self::SUMID_A_SETNEBIKI]['sum'] != 0) { // セット値引
                $value = $sumArr[self::SUMID_A_SETNEBIKI];
                $row_arr[] = array($value['name'], $value['sum'], null, null);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] != 0) { // 互助会値引き
                $value = $sumArr[self::SUMID_A_GOJOKAINEBIKI];
                $row_arr[] = array($value['name'], $value['sum'], null, null);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_KAIINNEBIKI]['sum'] != 0) { // 会員値引き
                $value = $sumArr[self::SUMID_A_KAIINNEBIKI];
                $row_arr[] = array($value['name'], $value['sum'], null, null);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_UNPLAN]['sum'] != 0) { // プラン外合計
                $value = $sumArr[self::SUMID_A_UNPLAN];
                $row_arr[] = array($value['name'], $value['sum'], null, null);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_UNPLANSAGAKU]['sum'] != 0) { // プラン外差額
                $value = $sumArr[self::SUMID_A_UNPLANSAGAKU];
                $row_arr[] = array($value['name'], $value['sum'], null, null);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_NEBIKI_PRC]['sum'] != 0) { // 値引額
                $value = $sumArr[self::SUMID_A_NEBIKI_PRC];
                $row_arr[] = array($value['name'], $value['sum'], null, null);
                $total1 += $value['sum'];
            }
        }
        // 空行を設定
        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 5; $index++) {
            $row_arr[] = null;
        }

        // 葬送儀礼費用合計
        $fld_no_syokei = null;
        for ($index = 0; $index < $fld_no_index; $index++) {
            if (isset($fld_no_syokei)) {
                $fld_no_syokei .= '＋';
            }
            $fld_no_syokei .= $fld_no[$index];
        }
        $row_arr[] = array('', null, null, $fld_no_syokei, null, '￥' . number_format($total1));

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // 返礼品費用
        //
                        for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
            $set_arr[$index]['y'] = 354;
        }

        $total2 = 0;
        $row_arr = array();

        // 【C】返礼品
        if (isset($sumArr[self::DAI_BUNRUI_2])) {
            $value = $sumArr[self::DAI_BUNRUI_2];
            $row_arr[] = array($value['name'], $value['sum_i']);
            if ($value['sum_t'] != 0) {
                $row_arr[] = array($value['name'] . '値引', $value['sum_t']);
            }
            $total2 += $value['sum'];
        }
        // 空行を設定
        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 3; $index++) {
            $row_arr[] = null;
        }
        // 返礼品費用合計
        $row_arr[] = array('', null, null, null, null, '￥' . number_format($total2));

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // 料理費用
        //
                for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
            $set_arr[$index]['y'] = 443;
        }
        $total3 = 0;
        $row_arr = array();
        // 【D】飲食費
        if (isset($sumArr[self::DAI_BUNRUI_3])) {
            $value = $sumArr[self::DAI_BUNRUI_3];
            $row_arr[] = array($value['name'], $value['sum_i']);
            if ($value['sum_t'] != 0) {
                $row_arr[] = array($value['name'] . '値引', $value['sum_t']);
            }
            $total3 += $value['sum'];
        }
        // 空行を設定
        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 3; $index++) {
            $row_arr[] = null;
        }
        // 料理費用合計
        $row_arr[] = array('', null, null, null, null, '￥' . number_format($total3));
        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // その他費用
        //
		for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
            $set_arr[$index]['y'] = 532;
        }
        $total4 = 0;
        $row_arr = array();

        // 【G】別途費用
        if (isset($sumArr[self::DAI_BUNRUI_5])) {
            $value = $sumArr[self::DAI_BUNRUI_5];
            if ($value['sum'] != 0) {
                $row_arr[] = array($value['name'], $value['sum']);
                $total4 += $value['sum'];
            }
            if ($value['sum_k'] != 0) {
                $row_arr[] = array('供花・供物費用', $value['sum_k']);
                $total4 += $value['sum_k'];
            }
        }
        // 雑収入
        if (isset($sumArr[self::DAI_BUNRUI_5_2000])) {
            $value = $sumArr[self::DAI_BUNRUI_5_2000];
            if ($value['sum'] != 0) {
                $in_zei_disp = "";
                if ($sumArr[self::DAI_BUNRUI_5_2000]['sum_in_zei'] > 0) {
                    $in_zei_disp = "(内消費税)";
                }
                $row_arr[] = array($value['name'], $value['sum'], $in_zei_disp);
                $total4 += $value['sum'];
            }
        }
        // 【H】値引き
        if (isset($sumArr[self::DAI_BUNRUI_7])) {
            $value = $sumArr[self::DAI_BUNRUI_7];
            $row_arr[] = array($value['name'], $value['sum']);
            $total4 += $value['sum'];
        }
        // 空行を設定
        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 5; $index++) {
            $row_arr[] = null;
        }
        // その他費用合計
        $row_arr[] = array('', null, null, null, null, '￥' . number_format($total4));
        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // 御見積金額
        //
		for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
            $set_arr[$index]['y'] = 649;
        }

        $total6 = 0;
        $total7 = 0;
        $total8 = 0;
        $row_arr = array();

        // 合計⑤
        $row_arr[] = array($item_indent . '合計⑤＝小計①＋小計②＋小計③＋小計④', $total1 + $total2 + $total3 + $total4);
        $total5 = $total1 + $total2 + $total3 + $total4;

        $total_nm = '合計金額＝合計⑤';
        static $circle_num = array(5 => '⑥', 6 => '⑦', 7 => '⑧', 8 => '⑨');
        $num = 5;
        // 【F】立替金
        if (isset($sumArr[self::DAI_BUNRUI_6])) {
            $value = $sumArr[self::DAI_BUNRUI_6];
            $in_zei_disp = "";
            if ($sumArr[self::DAI_BUNRUI_6]['sum_in_zei'] > 0) {
                $in_zei_disp = "(内消費税)";
            }
            $row_arr[] = array($item_indent . $value['name'] . '　' . $circle_num[$num], $value['sum'], $in_zei_disp);
            $total7 = $value['sum'];

            $total_nm .= '＋' . $circle_num[$num];
            $num++;
        }
        // 合計⑤の消費税
        if (isset($sumArr['tax'])) {
            $value = $sumArr['tax'];
            $biko = "";
            if ($num > 5) {
                $row_arr[] = array($item_indent . '合計⑤＋' . $circle_num[$num - 1] . 'の消費税　' . $circle_num[$num], $value['sum'], $biko);
            } else {
                $row_arr[] = array($item_indent . '合計⑤の消費税　' . $circle_num[$num], $value['sum'], $biko);
            }

            $total6 = $value['sum'];

            $total_nm .= '＋' . $circle_num[$num];
            $num++;
        }
        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            $row_arr[] = array($item_indent . $value['name'] . '　' . $circle_num[$num], $value['sum']);
            $total7 = $value['sum'];

            $total_nm .= '＋' . $circle_num[$num];
            $num++;
        }

        // 契約役務外利用を除いた合計金額
        $row_arr[] = array($item_indent . $total_nm, $total5 + $total6 + $total7);
        // 合計金額
        $total_sum = $total5 + $total6 + $total7 + $total8;

        // 【西村】解約充当 -> 施行時解約として表示するのでコメントアウト
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            $row_arr[] = array($item_indent . $value['name'] . '　' . $circle_num[$num], $value['sum'], "(非課税)"); // 契約役務外利用は非課税固定
            $total8 = $value['sum'];
            $total_nm .= '＋' . $circle_num[$num];
            $num++;
            // 契約役務外利用表示
            self::setSekoKaiyakuPrc($db, $seko_no, $pdfObj);
        }

        $zei_ritu_disp = ''; // 税率文言
        $zei_ritu = ''; // 税率文言
        if (Msi_Sys_Utils::myCount($recWk) > 0) {
            $kijun = $recWk[0]['sougi_ymd'];
            if (empty($kijun)) {
                $kijun = date('Y/m/d');
            }
            $zeiTbl = App_ClsTaxLib::GetGojoTaxInfo(Msi_Sys_DbManager::getMyDb(), $kijun);
            if (Msi_Sys_Utils::myCount($zeiTbl) > 0) {
                $zei_ritu = $zeiTbl['zei_rtu'];
                $zei_ritu_disp = '(' . + $zei_ritu . '%)';
            }
        }

        $zan_disp = ''; // 残額文言
        /* 会費残額0でも表示させるため 2017/08/22 MOD Otake
          if ($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] != 0 && $sumArr[self::SUMID_KAKEZAN]['sum'] != 0) {
         *///		if ($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] != 0 || $sumArr[self::SUMID_KAKEZAN]['sum'] != 0) {
//            $keiyaku = $sumArr[self::SUMID_KEIYAKUGAKU]['sum'];
//            $zan = $sumArr[self::SUMID_KAKEZAN]['sum'];
//            $harai = $keiyaku - $zan;
//            $zan_disp = ' (' .  Msi_Sys_Utils::num_format($keiyaku) . ' - ' . Msi_Sys_Utils::num_format($harai) . ')';
        /* 会費残額0でも表示させるため 2017/08/22 MOD Otake
          }

          // 掛金残金
          if ($sumArr[self::SUMID_KAKEZAN]['sum'] != 0) {
         */
//            $value = $sumArr[self::SUMID_KAKEZAN];
//            $row_arr[] = array($item_indent . $value['name'] . $zan_disp, $value['sum']);
//        }
//
//        // 名義変更手数料
//        if ($sumArr[self::SUMID_MEIGICGCOST]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_MEIGICGCOST];
//            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
//        }
//
//        // 早期利用費
//        if ($sumArr[self::SUMID_EARLYUSE]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_EARLYUSE];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
//
//        // 早期利用費の消費税
//        if ($sumArr[self::SUMID_EARLYUSEZEI]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_EARLYUSEZEI];
//            $row_arr[] = array($item_indent.$value['name'] . $zei_ritu_disp, $value['sum']);
//        }
//
//        // 掛金残金の消費税
//        if ($sumArr[self::SUMID_KAKEZANZEI]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_KAKEZANZEI];
//            $gojokai_zei_ritu_disp = DataMapper_SekoGojokaiMember::getZeirtu(Msi_Sys_DbManager::getMyDb(), array('seko_no' => $seko_no));
//            $row_arr[] = array($item_indent . $value['name'] . $gojokai_zei_ritu_disp, $value['sum']);
//        }
//
//        // コース変更差額
//        if ($sumArr[self::SUMID_COSECHGGAKU]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_COSECHGGAKU];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
//        // 掛金残金
//        if ($sumArr[self::SUMID_KAKEZAN]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_KAKEZAN];
//            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
//        }
//
//        // 掛金残金の消費税
//        if ($sumArr[self::SUMID_KAKEZANZEI]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_KAKEZANZEI];
//            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
//        }
//        // 互助会払込金額
//        if ($sumArr[self::SUMID_GOJOHARAI]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_GOJOHARAI];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
//
//        // 前納割引
//        if ($sumArr[self::SUMID_ZENNOWARI]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_ZENNOWARI];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
//
//        // 掛金消費税差額
//        if ($sumArr[self::SUMID_KAKEZEISAGAKU]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_KAKEZEISAGAKU];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
        // 空行を設定
        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 10; $index++) {
            $row_arr[] = null;
        }

        // 御見積金額
        if (isset($sumArr['total'])) {
            $value = $sumArr['total'];
            $row_arr[] = array('', null, null, null, null, '￥' . number_format($value['sum']));
        }

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        // 消費税金額の内訳出力     2019/04/30 sugiyama keigen
//            if ( $this->_isKeigenAppliedCtxt() ) {
//                $this->outZeiUchiwake($pdfObj);
//            }
    }

    /**
     * 契約役務外利用の内訳を表示
     * 
     * @param type $db
     * @param type $seko_no
     * @param type $pdfObj
     * @return array
     */
    private function setSekoKaiyakuPrc($db, $seko_no, $pdfObj) {
        // 契約役務外利用取得
        $sekoKaiyakuData = DataMapper_Pdf1101::findSekoKaiyaku($db, $seko_no, $this->_p_data_kbn);
        $kingaku_ary = array();     // 各内訳格納用配列
        $kingaku_ary[5] = 0;  // その他費用初期値
        foreach ($sekoKaiyakuData as $Kaiyaku) {
            // type 集計用変数 1:葬送儀礼 2:返礼品 3:料理 4:飲物
            $type = $Kaiyaku['seko_kaiyaku_type'];
            if ($type == 4) {
                $type = 3;
            }
            if (isset($kingaku_ary[$type])) {
                if (in_array($type, array(1, 2, 3))) {
                    $kingaku_ary[$type] += $Kaiyaku['kaiyaku_prc'];
                } else if (in_array($type, array(13))) {
                    $kingaku_ary[1] += $Kaiyaku['kaiyaku_prc'];
                } else if (in_array($type, array(14))) {
                    $kingaku_ary[2] += $Kaiyaku['kaiyaku_prc'];
                } else {
                    // その他費用
                    $kingaku_ary[5] += $Kaiyaku['kaiyaku_prc'];
                }
            } else {
                if (in_array($type, array(1, 2, 3))) {
                    $kingaku_ary[$type] = $Kaiyaku['kaiyaku_prc'];
                } else if (in_array($type, array(13))) {
                    $kingaku_ary[1] = $Kaiyaku['kaiyaku_prc'];
                } else if (in_array($type, array(14))) {
                    $kingaku_ary[2] = $Kaiyaku['kaiyaku_prc'];
                } else {
                    $kingaku_ary[5] += $Kaiyaku['kaiyaku_prc'];
                }
            }
        }
        // キー(type)で昇順
        ksort($kingaku_ary);
        // 内訳表示
        $y = 0;
        if ($this->_p_data_kbn == 2) {
            // 法事用の処理を追加
            $y_st = 441.5;
            $x_st1 = 427;
            $x_st2 = 510;
            foreach ($kingaku_ary as $key => $value) {
                if (!empty($value)) {
                    switch ($key) {
                        case 1: // 葬送儀礼
                            $pdfObj->write_string(array('x' => $x_st1, 'y' => $y_st + $y, 'width' => 80, 'height' => 15), "法要・供花物代");
                            $pdfObj->write_num(array('x' => $x_st2, 'y' => $y_st + $y, 'width' => 65, 'height' => 15), $value);
                            break;
                        case 2: // 返礼品
                            $pdfObj->write_string(array('x' => $x_st1, 'y' => $y_st + $y, 'width' => 80, 'height' => 15), "法要・返礼品代");
                            $pdfObj->write_num(array('x' => $x_st2, 'y' => $y_st + $y, 'width' => 65, 'height' => 15), $value);
                            break;
                        case 3: // 料理・飲料
                            $pdfObj->write_string(array('x' => $x_st1, 'y' => $y_st + $y, 'width' => 80, 'height' => 15), "料理");
                            $pdfObj->write_num(array('x' => $x_st2, 'y' => $y_st + $y, 'width' => 65, 'height' => 15), $value);
                            break;
                        case 5: // その他
                            $pdfObj->write_string(array('x' => $x_st1, 'y' => $y_st + $y, 'width' => 80, 'height' => 15), "その他");
                            $pdfObj->write_num(array('x' => $x_st2, 'y' => $y_st + $y, 'width' => 65, 'height' => 15), $value);
                            break;
                        default:
                            break;
                    }
                    $y += 23;
                }
            }
        } else {
            foreach ($kingaku_ary as $key => $value) {
                if (!empty($value)) {
                    switch ($key) {
                        case 1: // 葬送儀礼
                            $pdfObj->write_string(array('x' => 437, 'y' => 733.5 + $y, 'width' => 60, 'height' => 15), "葬送儀礼");
                            $pdfObj->write_num(array('x' => 500, 'y' => 733.5 + $y, 'width' => 75, 'height' => 15), $value);
                            break;
                        case 2: // 返礼品
                            $pdfObj->write_string(array('x' => 437, 'y' => 733.5 + $y, 'width' => 60, 'height' => 15), "返礼品");
                            $pdfObj->write_num(array('x' => 500, 'y' => 733.5 + $y, 'width' => 75, 'height' => 15), $value);
                            break;
                        case 3: // 料理・飲料
                            $pdfObj->write_string(array('x' => 437, 'y' => 733.5 + $y, 'width' => 60, 'height' => 15), "料理");
                            $pdfObj->write_num(array('x' => 500, 'y' => 733.5 + $y, 'width' => 75, 'height' => 15), $value);
                            break;
                        case 5: // その他
                            $pdfObj->write_string(array('x' => 437, 'y' => 733.5 + $y, 'width' => 60, 'height' => 15), "その他");
                            $pdfObj->write_num(array('x' => 500, 'y' => 733.5 + $y, 'width' => 75, 'height' => 15), $value);
                            break;
                        default:
                            break;
                    }
                    $y += 14.2;
                }
            }
        }
    }

    /**
     * 表紙の金額を出力
     *
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    public static function outDataFaceSum01_other($pdfObj, $sumArr, $coseNm) {
        static $row_height = 14.2;
        static $item_indent = '　　';

        $set_arr[] = array('x' => 50, 'width' => 290, 'height' => 15); // 項目
        $set_arr[] = array('x' => 350, 'width' => 80, 'height' => 15, 'type' => 'num');    // 金額
        $set_arr[] = array('x' => 440, 'width' => 135, 'height' => 15); // 備考
        $set_arr[] = array('x' => 250, 'width' => 90, 'height' => 15); // 項目（２列目）
        $set_arr[] = array('x1' => 45, 'y1' => -1, 'x2' => 575, 'y2' => -1, 'width' => 1, 'color' => self::$color, 'type' => 'line'); // 上線
        //
		// 葬送儀礼費用
        //
		for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
            $set_arr[$index]['y'] = 214;
        }

        $total1 = 0;
        $row_arr = null;

        // コース変更
        if ($sumArr[self::SUMID_COSECHGGAKU]['sum'] != 0) {
            $biko_cose_chg = floor($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] / 10000) . '万コース→' .
                    floor(($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] + $sumArr[self::SUMID_COSECHGGAKU]['sum']) / 10000) . '万コース';
        } else {
            $biko_cose_chg = null;
        }

        // 葬送儀礼費用基本プラン　小計　　　　　　　　　　　　　（ア）
        // 葬送儀礼費用基本プラン　葬送儀礼費用プラン外選択品目　（イ）
        // 葬送儀礼費用プラン外選択品目　　　　　　　　　　　　　（ウ）
        // 葬送儀礼費用　小計　　　　　　（ア）＋（イ）＋（ウ）
        // の（ア）・（イ）・（ウ）を出力する
        static $fld_no = array('（ア）', '（イ）', '（ウ）');
        $fld_no_index = 0;

        // 【A】葬送儀礼費用基本プラン
        if (isset($sumArr[self::DAI_BUNRUI_1])) {
            $value = $sumArr[self::DAI_BUNRUI_1];
//            $row_arr[] = array($value['name'], '');
            if ($sumArr[self::SUMID_A_PLAN]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_PLAN];
//                $row_arr[] = array($item_indent.$value['name'], $value['sum']);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_GOJOKAI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAI];
//                $row_arr[] = array($item_indent.$value['name'], $value['sum'], $biko_cose_chg, $coseNm);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAINEBIKI];
//                $row_arr[] = array($item_indent.$value['name'], $value['sum']);
                $total1 += $value['sum'];
            }
//            $row_arr[] = array($item_indent.'小計', $total1, null, $fld_no[$fld_no_index++]);
            if ($sumArr[self::SUMID_A_GOJOKAIGAI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAIGAI];
//                $row_arr[] = array($item_indent.$value['name'], $value['sum'], null, $fld_no[$fld_no_index++], true);
                $total1 += $value['sum'];
            }
//            $row_arr[] = null;
        }

        // 【B】葬送儀礼費用プラン外選択品目
//        if (isset($sumArr[self::CHU_BUNRUI_CD_B])) {
//            $value = $sumArr[self::CHU_BUNRUI_CD_B];
// //           $row_arr[] = array($value['name'], $value['sum'], null, $fld_no[$fld_no_index++]);
//            $total1 += $value['sum'];
//        }
        // 空行を設定
        //       for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 7; $index++) {
        //           $row_arr[] = null;
        //       }
        // 葬送儀礼費用合計
        $fld_no_syokei = null;
        for ($index = 0; $index < $fld_no_index; $index++) {
            if (isset($fld_no_syokei)) {
                $fld_no_syokei .= '＋';
            }
            $fld_no_syokei .= $fld_no[$index];
        }
        $fld_no2 = array('計①', '計②', '計③');
        $fld_total = '';
        if ($total1 != 0) {
            $kei = array_shift($fld_no2);
            $fld_total .= $kei;
            $row_arr[] = array('葬送儀礼費用　 ' . $kei, $total1, null, $fld_no_syokei);
        }

//        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
        //
		// おもてなし費用
        //
//        for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
//            $set_arr[$index]['y'] = 228;
//        }

        $total2 = 0;
//        $row_arr = null;
        // 【C】返礼品
        if (isset($sumArr[self::DAI_BUNRUI_2])) {
            $value = $sumArr[self::DAI_BUNRUI_2];
//            $row_arr[] = array($value['name'], $value['sum']);
            $total2 += $value['sum'];
        }

        // 【D】飲食費
        if (isset($sumArr[self::DAI_BUNRUI_3])) {
            $value = $sumArr[self::DAI_BUNRUI_3];
//            $row_arr[] = array($value['name'], $value['sum']);
            $total2 += $value['sum'];
        }

        // 【E】飲食費（壇払い）
//        if (isset($sumArr[self::CHU_BUNRUI_CD_E])) {
//            $value = $sumArr[self::CHU_BUNRUI_CD_E];
////            $row_arr[] = array($value['name'], $value['sum']);
//            $total2 += $value['sum'];
//        }
        // 空行を設定
//        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 4; $index++) {
//            $row_arr[] = null;
//        }
        // おもてなし費用合計
        if ($total2 != 0) {
            $kei = array_shift($fld_no2);
            if (empty($fld_total)) {
                $fld_total = $kei;
            } else {
                $fld_total .= '＋' . $kei;
            }
            $row_arr[] = array('おもてなし費用 ' . $kei, $total2);
        }

//        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
        //
		// その他費用
        //
//        for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
//            $set_arr[$index]['y'] = 242;
//        }

        $total3 = 0;
//        $row_arr = null;
        // 【G】別途費用
        if (isset($sumArr[self::DAI_BUNRUI_5])) {
            $value = $sumArr[self::DAI_BUNRUI_5];
            //           $row_arr[] = array($value['name'], $value['sum']);
            $total3 += $value['sum'];
        }

        // 【H】値引き
        if (isset($sumArr[self::DAI_BUNRUI_7])) {
            $value = $sumArr[self::DAI_BUNRUI_7];
//            $row_arr[] = array($value['name'], $value['sum']);
            $total3 += $value['sum'];
        }

        // 空行を設定
        //       for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 3; $index++) {
        //           $row_arr[] = null;
        //       }
        // その他費用合計
        if ($total3 != 0) {
            $kei = array_shift($fld_no2);
            if (empty($fld_total)) {
                $fld_total = $kei;
            } else {
                $fld_total .= '＋' . $kei;
            }
            $row_arr[] = array('その他費用 　　' . $kei, $total3);
        }
//        $row_arr[] = array('', $total3);
//        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
        //
		// 御見積金額
        //
//        for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
//            $set_arr[$index]['y'] = 270;
//        }

        $total5 = 0;
        $total6 = 0;
        $total7 = 0;
        $total8 = 0;
//        $row_arr = null;
        // 空行を追加
        $row_arr[] = null;

        $total_kei = '合計';
        if (!empty($fld_total)) {
            $total_kei = $total_kei . '＝' . $fld_total;
        }
        // 合計④
//        $row_arr[] = array($item_indent.'合計＝計①＋計②＋計③　　　（ア）', $total1 + $total2 + $total3);
        $row_arr[] = array($item_indent . $total_kei, $total1 + $total2 + $total3, null, '（ア）');
        $total4 = $total1 + $total2 + $total3;

        //$total_nm = '合計金額＝合計④';
        $total_nm = '合計金額＝（ア）';
        static $circle_num = array(5 => '⑤', 6 => '⑥', 7 => '⑦', 8 => '⑧');
        $num = 5;

        // 合計④の消費税
        if (isset($sumArr['tax'])) {
            $value = $sumArr['tax'];
//            $row_arr[] = array($item_indent.'合計の消費税　　　　　　　　（イ）', $value['sum']);
            $row_arr[] = array($item_indent . '合計の消費税', $value['sum'], null, '（イ）');
            $total5 = $value['sum'];

            //$total_nm .= '＋'.$circle_num[$num];
            $total_nm .= '＋（イ）';
            $num++;
        }

        // 【F】立替金
        if (isset($sumArr[self::DAI_BUNRUI_6])) {
            $value = $sumArr[self::DAI_BUNRUI_6];
            //$row_arr[] = array($value['name'].'　'.$circle_num[$num], $value['sum']);
//            $row_arr[] = array($item_indent.'立替金　　　　　　　　　　　（ウ）', $value['sum']);
            $row_arr[] = array($item_indent . '立替金', $value['sum'], null, '（ウ）');
            $total6 = $value['sum'];

            //$total_nm .= '＋'.$circle_num[$num];
            $total_nm .= '＋（ウ）';
            $num++;
        }

        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            //$row_arr[] = array($item_indent.$value['name'].'　'.$circle_num[$num], $value['sum']);
//            $row_arr[] = array($item_indent.$value['name'].'　　　　　　（エ）', $value['sum']);
            $row_arr[] = array($item_indent . $value['name'], $value['sum'], null, '（エ）');
            $total7 = $value['sum'];

            //$total_nm .= '＋'.$circle_num[$num];
            $total_nm .= '＋（エ）';
            $num++;
        }

        // 預り金（返礼品）
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            //$row_arr[] = array($item_indent.$value['name'].'　'.$circle_num[$num], $value['sum']);
//            $row_arr[] = array($item_indent.$value['name'].'　　　　　　（オ）', $value['sum']);
            $row_arr[] = array($item_indent . $value['name'], $value['sum'], null, '（オ）');
            $total8 = $value['sum'];

            //$total_nm .= '＋'.$circle_num[$num];
            $total_nm .= '＋（オ）';
            $num++;
        }

        // 合計金額
        $row_arr[] = array($item_indent . $total_nm, $total4 + $total5 + $total6 + $total7 + $total8);
        $total_sum = $total4 + $total5 + $total6 + $total7 + $total8;

        // 早期利用費
        if ($sumArr[self::SUMID_EARLYUSE]['sum'] != 0) {
            $value = $sumArr[self::SUMID_EARLYUSE];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }

        // 早期利用費の消費税
        if ($sumArr[self::SUMID_EARLYUSEZEI]['sum'] != 0) {
            $value = $sumArr[self::SUMID_EARLYUSEZEI];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }

        // 掛金残金
        if ($sumArr[self::SUMID_KAKEZAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_KAKEZAN];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }

        // 掛金残金の消費税
        if ($sumArr[self::SUMID_KAKEZANZEI]['sum'] != 0) {
            $value = $sumArr[self::SUMID_KAKEZANZEI];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }
        // コース変更差額
//        if ($sumArr[self::SUMID_COSECHGGAKU]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_COSECHGGAKU];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
//        // 互助会払込金額
//        if ($sumArr[self::SUMID_GOJOHARAI]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_GOJOHARAI];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
//
//        // 前納割引
//        if ($sumArr[self::SUMID_ZENNOWARI]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_ZENNOWARI];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
//
//        // 掛金消費税差額
//        if ($sumArr[self::SUMID_KAKEZEISAGAKU]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_KAKEZEISAGAKU];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
        // 空行を設定
        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 15; $index++) {
            $row_arr[] = null;
        }

        // 御見積金額
        if (isset($sumArr['total'])) {
            $value = $sumArr['total'];
            $row_arr[] = array('', $value['sum']);
        }

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
    }

    /**
     * 表紙の金額を出力（法事)
     *
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    public static function outDataFaceSum01_houji($pdfObj, $sumArr, $coseNm) {
        static $row_height = 20;

        $set_arr[] = array('x' => 30, 'width' => 65, 'height' => 27, 'align' => 'C');   // 項目
        $set_arr[] = array('x' => 90, 'width' => 100, 'height' => 27, 'type' => 'num'); // 金額
        $set_arr[] = array('x' => 156, 'width' => 100, 'height' => 27, 'type' => 'num'); // 値引き(付帯特典・割引金額)
        $set_arr[] = array('x' => 256, 'width' => 100, 'height' => 27, 'type' => 'num'); // 御見積金額
        $set_arr[] = array('x' => 350, 'width' => 90, 'height' => 27, 'type' => 'num'); // 消費税
        $set_arr[] = array('x' => 450, 'width' => 135, 'height' => 27); // 備考
        for ($index = 0; $index < Msi_Sys_Utils::myCount($set_arr); $index++) {
            $set_arr[$index]['y'] = 360;
        }

        $hoyo_sum = 0;     // 法要・墓参り費用合計
        $zei_nuki_nebiki_sum = 0;  // 値引き合計(税抜項目)
        $zei_komi_nebiki_sum = 0;  // 値引き合計(税込項目)
        $omote_sum = 0;     // おもてなし費用合計
        $sonota_sum = null;     // その他費用合計
        $hikazei = 0;     // 非課税
        $azukari_dan = 0;     // 預金(壇払い)
        $azukari_hen = 0;     // 預金(返礼品)
        $row_arr = null;

        // 法要・墓参り費用内訳
        foreach (self::$houyou_hakamairi as $key) {
            if (isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0) {
                $value = $sumArr[$key];
                $hoyo_sum += $value['sum_i'];
                $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
                $zei_nuki_nebiki_sum += $nebiki;
                $row_arr[] = array($sumArr[$key]['name'] // 中分類
                    , $sumArr[$key]['sum_i'] // 金額
                    , $nebiki                // 値引き額
                    , $sumArr[$key]['sum']   // 見積金額
                    , null    // 消費税
                    , null                   // 備考
                );
            }
        }

        // おもてなし費用内訳
        foreach (self::$omotenasi as $key) {
            if (isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0) {
                $omote_sum += $sumArr[$key]['sum_i'];
                $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
                $zei_nuki_nebiki_sum += $nebiki;
                $row_arr[] = array($sumArr[$key]['name']  // 中分類
                    , $sumArr[$key]['sum_i'] // 金額
                    , $nebiki                // 値引き額
                    , $sumArr[$key]['sum']   // 見積金額
                    , null    // 消費税
                    , null                   // 備考
                );
            }
        }

        // その他費用内訳
        foreach (self::$sonota as $key) {
            if (isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0) {
                $sonota_sum += $sumArr[$key]['sum_i'];
                $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
                $zei_nuki_nebiki_sum += $nebiki;
                $row_arr[] = array($sumArr[$key]['name']  // 中分類
                    , $sumArr[$key]['sum_i'] // 金額
                    , $nebiki                // 値引き額
                    , $sumArr[$key]['sum']   // 見積金額
                    , null    // 消費税
                    , null                   // 備考
                );
            }
        }

        // 税込項目
        if (isset($sumArr[self::CHU_BUNRUI_CD_HIKAZEI])) {
            $value = $sumArr[self::CHU_BUNRUI_CD_HIKAZEI];
            $nebiki = $value['sum_t'] == 0 ? null : $value['sum_t'];
            $zei_komi_nebiki_sum += $nebiki;
            $row_arr[] = array($value['name']
                , $value['sum_i'] // 金額
                , $nebiki  // 値引き額
                , $value['sum'] // 見積金額
                , null  // 消費税
                , null  // 備考
            );
            $hikazei = $value['sum_i'];
        }

        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            $row_arr[] = array($value['name']
                , $value['sum'] // 金額
                , null          // 値引き額
                , $value['sum'] // 見積金額
                , null          // 消費税
                , null          // 備考
            );
            $azukari_dan = $value['sum'];
        }

        // 預り金（返礼品）
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            $row_arr[] = array($value['name']
                , $value['sum'] // 金額
                , null          // 値引き額
                , $value['sum'] // 見積金額
                , null          // 消費税
                , null          // 備考
            );
            $azukari_hen = $value['sum'];
        }

        // 空行を設定
        for ($index = Msi_Sys_Utils::myCount($row_arr); $index < 12; $index++) {
            $row_arr[] = null;
        }

        $zei_nuki_sum = $hoyo_sum + $omote_sum + $sonota_sum + $azukari_dan + $azukari_hen;
        $zei_komi_sum = $hikazei;
        $kingaku_sum = $zei_nuki_sum + $zei_komi_sum;
        $nebiki_sum = $zei_nuki_nebiki_sum + $zei_komi_nebiki_sum;
        $zei = null;
        if (isset($sumArr['tax'])) {
            $zei = $sumArr['tax']['sum'];
        }

        // 税抜項目小計
        $row_arr[] = array(null
            , $zei_nuki_sum != 0 ? $zei_nuki_sum : ''      // 金額
            , $zei_nuki_nebiki_sum < 0 ? $zei_nuki_nebiki_sum : ''     // 値引き額
            , $zei_nuki_sum + $zei_nuki_nebiki_sum != 0 ? $zei_nuki_sum + $zei_nuki_nebiki_sum : ''// 請求金額
            , $zei != 0 ? $zei : ''        // 消費税
            , null           // 備考
        );

        // 税込項目小計
        $row_arr[] = array(null
            , $zei_komi_sum != 0 ? $zei_komi_sum : ''        // 金額
            , $zei_komi_nebiki_sum < 0 ? $zei_komi_nebiki_sum : ''       // 値引き額
            , $zei_komi_sum + $zei_komi_nebiki_sum != 0 ? $zei_komi_sum + $zei_komi_nebiki_sum : ''  // 請求金額
            , null             // 消費税
            , null             // 備考
        );

        // 合計
        $row_arr[] = array(''
            , $kingaku_sum != 0 ? $kingaku_sum : ''        // 金額
            , $nebiki_sum < 0 ? $nebiki_sum : ''        // 値引き額
            , $kingaku_sum + $nebiki_sum != 0 ? $kingaku_sum + $nebiki_sum : '' // 見積金額
            , $zei != 0 ? $zei : ''          // 消費税
            , null            // 備考
        );

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
    }

    /**
     * 入金情報を出力
     *
     * @param type $pdfObj
     * @param type $db
     * @param type $kaisyalogo
     * @param type $seko_no
     * @param type $meisai_top
     * @param type $add_x
     * @param type $row_height
     * @return なし
     */
    private function outNyukin($pdfObj, $db, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $meisai_top, $add_x = 0, $row_height = 14.2) {
        static $meisai_row_count = 5;

        $set_arr[] = array('x' => 45 + $add_x, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'type' => 'ymd', 'format' => 'Y/m/d', 'align' => 'C'); // 入金日
        $set_arr[] = array('x' => 110 + $add_x, 'y' => $meisai_top, 'width' => 185, 'height' => 15); // 品目
        $set_arr[] = array('x' => 305 + $add_x, 'y' => $meisai_top, 'width' => 65, 'height' => 15, 'type' => 'num'); // 入金金額

        $row_arr = array();
        $total = 0;

        $rec = DataMapper_Pdf1101::findNyukin($db, array("data_kbn" => $data_kbn, "seko_no" => $seko_no, "seko_no_sub" => $seko_no_sub, "seikyu_no" => $uri_den_no));
        $count = Msi_Sys_Utils::myCount($rec);
        if ($count > $meisai_row_count) {
            $count = $meisai_row_count - 1;
        }
        for ($index = 0; $index < $count; $index++) {
            $row = $rec[$index];
            $row_arr[] = array($row['nyukin_ymd'], $row['kamoku_nm'], $row['nyukin_prc']);
            $total += $row['nyukin_prc'];
        }

        // その他
        if (Msi_Sys_Utils::myCount($rec) > $meisai_row_count) {
            $etc = 0;
            for ($index = $count; $index < Msi_Sys_Utils::myCount($rec); $index++) {
                $row = $rec[$index];
                $total += $row['nyukin_prc'];
                $etc += $row['nyukin_prc'];
            }
            $row_arr[] = array(null, 'その他', $etc);
        }

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
        $pdfObj->write_num(array('x' => 305 + $add_x, 'y' => $meisai_top + 70, 'width' => 65, 'height' => 15), $total);  // 入金合計
    }

    static private function getChuBunruiName($db, $chu_bunrui_cd) {
        $chu_bun_ary = DataMapper_MsterGetLib::GetChuBunruiMst($db);
        foreach ($chu_bun_ary as $row) {
            if ($row['chu_bunrui_cd'] == $chu_bunrui_cd) {
                return $row['chu_bunrui_nm'];
            }
        }
        return null;
    }

    /**
     *
     * ソースファイルのインデックスを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/8/19
     * @return int インデックス
     */
    private function getSourceFileIndex($data_kbn) {
//		if ($this->_moushiKbn == 6) {
//			return $this->_moushiKbn;
//		} else {
        return $data_kbn;
//		}
    }

    /**
     *
     * 表紙を追加する
     *
     * <AUTHOR> Otake
     * @since 2017/09/07
     * @return
     */
    private function createHyoshiData($db, $seko_no) {
        $kaisya = DataMapper_PdfCommon::getKaisya($db, array('kaisya_snm', 'syamei_logo_img '));
        $sekyusaki = App_Utils::getSekoSekyuInfo($seko_no);
        $sekokihon = DataMapper_SekoKihonInfo::findOne($db, array('seko_no' => $seko_no));

        $hyoshiData = array();
        if (isset($kaisya['syamei_logo_img'])) {
            $logo = $db->readBlobCont($kaisya['syamei_logo_img']);
            $hyoshiData['logo'] = $logo;
        }
        if (isset($sekyusaki['sekyu_nm'])) {
            $hyoshiData['sekyu_nm'] = $sekyusaki['sekyu_nm'];
        }
        if (Msi_Sys_Utils::myCount($sekokihon) > 0) {
            // 葬家名
            $hyoshiData['souke_nm'] = $sekokihon['souke_nm'];
        }
        return $hyoshiData;
        // 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);
    }

    /**
     * 表紙ページを追加する
     *
     * <AUTHOR> Otake
     * @since 2017/09/06
     * @param type $pdfObj
     * @param array $hyoshiData
     * @return type
     */
    private function addHyoshi($pdfObj, $hyoshiData) {
        if (!$hyoshiData) {
            return;
        }
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileHyosi);
        if (!empty($hyoshiData['logo'])) {
            $pdfObj->write_image(array('x' => 160, 'y' => 700, 'width' => 0, 'height' => 60), $hyoshiData['logo']);
        }
        if (!empty($hyoshiData['sekyu_nm'])) {
            $pdfObj->write_string(array('x' => 50, 'y' => 96, 'width' => 180, 'height' => 15, 'align' => 'C', 'font_size' => 15), $hyoshiData['sekyu_nm']);
        }
        if (!empty($hyoshiData['souke_nm'])) {
            // 葬家名
            $pdfObj->write_string(array('x' => 125, 'y' => 207, 'width' => 130, 'height' => 45, 'font_size' => 36, 'align' => 'C'), $hyoshiData['souke_nm']);
        }
    }

    /**
     * 振込み案内ページ（請求書送付案内ページ）を追加する
     *
     * <AUTHOR> Kobayashi
     * @since 2024/04/23
     * @param type $db
     * @param type $pdfObj
     * @param array $furikomiGuideData 振込案内データ
     */
    private function addFurikomiGuide($db, $pdfObj, $furikomiGuideData) {
        if (!$furikomiGuideData) {
            return;
        }
        $number = substr($furikomiGuideData['sekyu_no'], -5);
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileGuide);

        $pdfObj->write_string(array('x' => 60, 'y' => 60, 'width' => 90, 'height' => 45, 'font_size' => 14, 'align' => 'C'), $furikomiGuideData['souke_nm'] . '　家'); // 葬家名
        // 会社簡略名
        $kaisyaInfo = DataMapper_KaisyaInfo::findOne($db);
        $pdfObj->write_string(array('x' => 160, 'y' => 242, 'width' => 85, 'height' => 45, 'font_size' => 12, 'align' => 'C'), $kaisyaInfo['kaisya_snm']); // 会社簡略名
        // 振込情報
        $bank_info = App_Utils2::getBankInfo($db, $furikomiGuideData['bumon_cd']);
        $yokin_nm = $this->getCodeName($db, '1980', $bank_info['yokin_sbt']);
        $bank = $bank_info['bank_nm'] . '　' . $bank_info['shiten_nm'] . '　' . $yokin_nm . '　' . $bank_info['kouza_no'];
        $pdfObj->write_string(array('x' => 160, 'y' => 364, 'width' => 130, 'height' => 45, 'font_size' => 12), $bank_info['koza_meigi_nm']); // 口座名義
        $pdfObj->write_string(array('x' => 110, 'y' => 420, 'width' => 400, 'height' => 45, 'font_size' => 14, 'align' => 'C'), $bank); // 振込情報
        $strSeikyuSum = Msi_Sys_Utils::filterComma($furikomiGuideData['sekyu_gaku']);
        $pdfObj->write_string(array('x' => 240, 'y' => 513, 'width' => 130, 'height' => 45, 'font_size' => 16, 'align' => 'R'), "￥" . $strSeikyuSum . '＊'); // 御請求金額
        $pdfObj->write_string(array('x' => 240, 'y' => 568, 'width' => 130, 'height' => 45, 'font_size' => 16, 'align' => 'C'), $number); // 請求番号
    }

    /**
     *
     * 見積表紙に会員情報を出力する
     *
     * <AUTHOR> Sai
     * @since 2015/12/17
     * @version 2017/09/06 MSI Otake 解約充当追加
     * @version 2018/07/27 MSI Otake 出力位置変更
     * @param $db
     * @param $pdfObj
     * @param $seko_no
     * @return
     */
    private function outKaiinInfo($db, $pdfObj, $seko_no) {
        // 会員情報(用途区分:1)出力処理
        $kainNo = $this->getKainNo($db, $seko_no, 1);
        if (empty($kainNo)) {
            return;
        }
        $rec = $kainNo[0];
//	        $pdfObj->write_string(array('x' => 440, 'y' => 236, 'width' => 130, 'height' => 16,), $rec['kanyu_nm']. " 様");
//		$pdfObj->write_string(array('x' => 440, 'y' => 250, 'width' => 140, 'height' => 16,), trim($rec['kain_no']) . " " . $rec['apply_no'] . " " . $rec['course_snm_cd']);
//		$pdfObj->write_string(array('x' => 440, 'y' => 264, 'width' => 140, 'height' => 16,), $rec['web_disp_moji']);

        $pdfObj->write_string(array('x' => 440, 'y' => 250, 'width' => 140, 'height' => 16,), $rec['web_disp_moji']);
        $pdfObj->write_string(array('x' => 440, 'y' => 264, 'width' => 130, 'height' => 16,), trim($rec['kain_no']) . " " . $rec['apply_no'] . " " . $rec['course_snm_cd'] . ' ' . $rec['kanyu_nm'] . " 様");
    }

    private function outPlanInfo($db, $pdfObj, $seko_no) {
        $planName = DataMapper_Pdf0113::getSekoPlanName($db, $seko_no);
        if ($planName) {
            $rec = array_shift($planName);
            $pdfObj->write_string(array('x' => 260, 'y' => 236, 'width' => 85, 'height' => 15,), $rec['shohin_nm']);
        }
    }

    /**
     * PDFのファイル名を施行No + 葬家名 + タイトル 形式で取得する
     *
     * <AUTHOR> Matsuyama
     * @since 2016/09/06
     * @return string   PDFのファイル名
     */
    public function getFileName($db, $seko_no, $title) {
        $file_name = $title;
        $rec = DataMapper_SekoKihonInfo::findOne($db, array('seko_no' => $seko_no));
        if (Msi_Sys_Utils::myCount($rec) > 0) {
            $file_name = $seko_no . $rec['souke_nm'] . '家' . $title;
        }
        return $file_name;
    }

    /**
     *
     * 担当者印印字処理
     * 承認日が2017/04/25以前の場合は印鑑が黒くなるため、対応前のバージョンで出力を行う
     *
     * <AUTHOR> Sai
     * @since 2017/04/28
     * @param type $pdfObj
     * @param type $shonin_dt
     * @param type $x
     * @param type $y
     * @param type $img
     * @return
     */
    private function seal_out($pdfObj, $shonin_dt, $x, $y, $img) {
        if ($shonin_dt > '2017/04/25') {
            $pdfObj->seal_out($x, $y, $img);
        } else {
            $pdfObj->seal_out_old($x, $y, $img);
        }
    }

    /**
     * 鏡・明細にページ情報出力
     * 明細書にヘッダーとフッターを出力
     *
     * <AUTHOR> Otake
     * @since 2017/09/05
     * @param type $pdfObj
     * @param int $numPagesCur 鏡のページ位置
     * @param str $sekyu_nm 請求先名
     * @param array? $kaisyalogo
     * @param date $issue_date
     * @param str $seko_no
     * @param str $uri_den_no
     */
    private function outMeisaiInfo($pdfObj, $numPagesCur, $sekyu_nm, $kaisyalogo, $issue_date, $seko_no, $uri_den_no, &$oldNumPages) {
        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages + 1; $i++) {
            $page = $i;
            $pdfObj->setPage($page);
            // 確認用のグリッドを出力
//            $pdfObj->test_line_out(600, 1000);
            $pdfObj->write_string(array('x' => 45, 'y' => 814, 'width' => 530, 'height' => 15, 'align' => 'C', 'font_size' => 9.7), ($page - $oldNumPages) . '/' . ($numPages - $oldNumPages));
            // 明細書
            if ($page > $numPagesCur) {
                // 請求先名
                $pdfObj->write_string(array('x' => 45, 'y' => 59, 'width' => 180, 'height' => 15, 'font_size' => 15, 'align' => 'C'), $sekyu_nm);
                // 発行日
                if (isset($issue_date)) {
                    $pdfObj->write_date(array('type' => 'ymd', 'x' => 492, 'y' => 797.5, 'width' => 115, 'height' => 9, 'font_size' => 9), $issue_date, 'Y年m月d日');
                }
                // 施行No.
                $pdfObj->write_string(array('x' => 372, 'y' => 808, 'width' => 115, 'height' => 9, 'font_size' => 9), $seko_no);
                // 請求No.
                $pdfObj->write_string(array('x' => 492, 'y' => 806.5, 'width' => 115, 'height' => 9, 'font_size' => 9), $uri_den_no);
                // 軽減税率対応 凡例   2019/04/30 sugiyama keigen
                if ($this->_isKeigenAppliedCtxt()) { // &&
                    // isset($this->_is_reduced_tax_rate_row_exists) && $this->_is_reduced_tax_rate_row_exists ) {
                    $pdfObj->write_string(array('x' => 45, 'y' => 800, 'width' => 180, 'height' => 9, 'font_size' => 9, 'align' => 'L'), '＊は軽減税率対象');
                }
            }
        }
        $oldNumPages = $numPages;
    }

    /**
     * 互助会の文字列を置き換え
     *
     * @a互uthor MSI Otake
     * @since 2017/09/05
     * @param str $kain_nm 会員名
     */
    private function replaceGojokaiByName($kain_nm) {
        // 互助会の文字列を置き換え
        self::$sum_name[self::SUMID_A_GOJOKAI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAI]);
        self::$sum_name[self::SUMID_A_GOJOKAINEBIKI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        self::$sum_name[self::SUMID_A_GOJOKAIGAI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAIGAI]);
        self::$sum_name[self::SUMID_GOJOHARAI] = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_GOJOHARAI]);
        self::$msi_name_gojokai = str_replace('互助会', $kain_nm, self::$msi_name_gojokai);
        self::$msi_name_gojokaitokuten = str_replace('互助会', $kain_nm, self::$msi_name_gojokaitokuten);
    }

    /**
     * 消費税金額の内訳を出力する
     *
     * <AUTHOR> sugiyama
     * @since 2019/05/xx
     * @param App_Pdf  $pdfObj
     * @param integer  $type     1:葬儀, 2:法事
     * @return void
     */
    protected function outZeiUchiwake($pdfObj, $type = 1, $uri_den_no) {
        $tgt_type = $this->_p_tgt_type;
        $seko_no = $this->_p_seko_no;
        $seko_no_sub = $this->_p_seko_no_sub;
        $data_kbn = $this->_p_data_kbn;
        // $history_no  = $this->_p_history_no;

        $db = Msi_Sys_DbManager::getMyDb();

        //$shohizei = App_KeigenUtils::getSeikyuShohizeiEasy($db, $seko_no, $seko_no_sub, $data_kbn);
        if ($type == 2) {
            $shohizei = App_KeigenUtils::getSeikyuShohizeiEasy($db, $seko_no, $seko_no_sub, $data_kbn);
        } else {
            $shohizei = App_KeigenUtils::getSeikyuShohizeiEasy5($db, $uri_den_no);
        }
        /* if (is_null($history_no)) { */
        /*     $shohizei = App_KeigenUtils::getMitsuShohizeiEasy($db, $seko_no, $seko_no_sub, $data_kbn); */
        /* } else { */
        /*     $shohizei = App_KeigenUtils::getMitsuShohizeiFromHistoryEasy($db, $seko_no, $seko_no_sub, $data_kbn, $history_no); */
        /* } */

        $aUchiwake = $shohizei;

        if ($type == 2) { // 法事
//            $optAttr = array( 'left'  => 445, // 447.5,   // X
//                              'top'   => 363, // 310,     // Y
//                              'width' => 130,
//                              'height' => 20.2 );
            $left = 425.5;
            $top = 206;
            $width = 150;
            $optAttr = array('left' => $left, // X
                'top' => $top, // Y
                'width' => $width,
                'height' => 23.05,
                'fontSize' => 11,
                    //'stretch' => null 
            );
            App_KeigenPdfUtils::outZeiUchiwake01($pdfObj, '消費税の内訳', $aUchiwake, $optAttr);
        } else { // 葬儀
            $top = 734.5;
            $left = 70;
            $width = 250;
            $optAttr = array('left' => $left, // X
                'top' => $top, // Y
                'width' => $width,
                'height' => 14,
                'stretch' => null
            );
            App_KeigenPdfUtils::outZeiUchiwake04($pdfObj, $aUchiwake, $optAttr);
        }
        // $pdfObj->test_line_out(600, 1000);  // mesh for DEV
    }

    /**
     * 消費税金額の内訳を出力する(単品、別注品)
     *
     * <AUTHOR> sugiyama
     * @since 2019/05/xx
     * @param App_Pdf  $pdfObj
     * @param   string          $uri_den_no   売上伝票№
     * @return void
     */
    protected function outZeiUchiwake03($pdfObj, $uri_den_no) {
        $db = Msi_Sys_DbManager::getMyDb();

        // $shohizei = App_KeigenUtils::getSeikyuShohizeiUriEasy($db, $uri_den_no); // 税種別毎
        $shohizei = App_KeigenUtils::getSeikyuShohizeiEasyRitsuEkimu($db, $uri_den_no); // 税率毎

        $aUchiwake = $shohizei;

        // 御請求金額行を追加
        $sum = 0;
        foreach ($shohizei as $i => $line) {
            if (preg_match('/内消費税/u', $line[0])) {
                continue;
            }
            $sum += +$line[1];
        }
        $aUchiwake[] = array('御請求金額', $sum);

        $optAttr = array('left' => 363, // X
            'top' => 597.9, // Y
            'width' => 190,
            'height' => 13.1);

        App_KeigenPdfUtils::outZeiUchiwake03($pdfObj, '', $aUchiwake, $optAttr);

        // $pdfObj->test_line_out(600, 1000);  // mesh for DEV
    }

    protected $_sougi_ymd = null; // 消費税基準日
    protected $_is_keigen_ctxt = false; // 軽減税率対応形式で出力

    /**
     * 軽減税率対象の可否を設定する
     * 葬儀や法事は、消費税基準日(sougi_ymd)を設定する
     *   juchu_*_history からの出力時でも現在の sougi_ymd を使うので注意
     * 単品、別注品は常に可で設定する
     *
     * <AUTHOR> sugiyama
     * @since 2019/05/xx
     * @param $seko_no
     * @param $seko_no_sub
     * @param $data_kbn
     * @return void
     */

    protected function _prepKeigen($seko_no, $seko_no_sub, $data_kbn) {
        $db = Msi_Sys_DbManager::getMyDb();

        if ($data_kbn == 3 || $data_kbn == 4) { // // 単品(3),別注品(4)は sougi_ymd を気にしない
            $this->_forceKeigenAppliedCtxt();
            return;
        }

        $sougi_ymd = $db->getOneVal(<<< END_OF_SQL
SELECT TO_CHAR(sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd
  FROM seko_kihon_info
 WHERE seko_no=?
   AND delete_flg=0 
END_OF_SQL
                , array($seko_no));

        $this->_sougi_ymd = $sougi_ymd; // null もあり得る
    }

    /**
     * 強制的に軽減税率対象とする
     *
     * <AUTHOR> sugiyama
     * @since 2019/05/xx
     * @param $boolean
     * @return void
     */
    protected function _forceKeigenAppliedCtxt($boolean = true) {
        $this->_is_keigen_ctxt = $boolean;
    }

    /**
     * 消費税基準日(sougi_ymd)が軽減税率対象となる場合に真を返す
     *
     * <AUTHOR> sugiyama
     * @since 2019/05/xx
     * @return boolean
     */
    protected function _isKeigenAppliedCtxt() {
        if ($this->_is_keigen_ctxt) {
            return true;
        }

        $sougi_ymd = $this->_sougi_ymd;
        if ($sougi_ymd == null) {
            return false;
        }

        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen();
        if ($keigenBgnYmd <= $sougi_ymd) {
            return true;
        }
        return false;
    }

    /**
     * 
     * 送付先情報を施行基本情報から取得する
     * 
     * @param string $seko_no 施行番号
     * @return array
     */
    private function getSofuNm($seko_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT 
         COALESCE(ski.v_free10,'')   AS  zip_7
        ,COALESCE(ski.v_free11,'')   AS  address_7_1
        ,COALESCE(ski.v_free12,'')   AS  address_7_2
        ,COALESCE(ski.v_free13,'')   AS  soufu_saki_nm
    FROM 
        seko_kihon_info ski
    WHERE 
        ski.seko_no    = :seko_no
    AND ski.delete_flg = 0
;
END_OF_SQL
                , array("seko_no" => $seko_no));
        return $select;
    }

    /**
     * 
     * 一般の火葬式プランかチェック
     * 
     * @return boolean
     */
    private function checkRiyoPlan() {
        $db = Msi_Sys_DbManager::getMyDb();
        $select = $db->easySelOne("
        SELECT 
             main_pt_kbn
            ,gojokai_kbn
            ,seko_no
        FROM seko_kihon_info 
        WHERE seko_no = :seko_no
        AND delete_flg = 0
        ", array('seko_no' => $this->_p_seko_no));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            if ($select['main_pt_kbn'] == '4' && $select['gojokai_kbn'] == '0') {
                return true;
            }
        }
        return false;
    }

    /**
     * コード名称マスタからコード名を取得
     *
     * <AUTHOR> Tosaka
     * @since 2018/01/12
     * @param string $db $db
     * @param string $codeKbn コード区分
     * @param string $cd コード値
     * @return string コード名称
     */
    private function getCodeName($db, $codeKbn, $cd) {

        $sql = "
            SELECT
                kbn_value_lnm
                ,kbn_value_snm
            FROM
               code_nm_mst 
            WHERE
                delete_flg = 0
            AND code_kbn = :code_kbn
            AND kbn_value_cd = :kbn_value_cd
                ";
        $select = $db->easySelOne($sql, array('code_kbn' => $codeKbn, 'kbn_value_cd' => $cd));
        return $select['kbn_value_snm'];
    }

    /**
     * 担当者マスタから印鑑を取得
     *
     * <AUTHOR> Kobayashi
     * @since 2024/04/26
     * @param string $db $db
     * @param string $cd コード値
     */
    private function getTantoInkan($db, $cd) {

        $sql = "
            SELECT
                inkan_img
            FROM
               tanto_mst 
            WHERE
                delete_flg = 0
            AND tanto_cd = :tanto_cd
                ";
        $select = $db->easySelOne($sql, array('tanto_cd' => $cd));
        return $select['inkan_img'];
    }

}
