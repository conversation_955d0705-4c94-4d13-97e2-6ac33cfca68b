<?php

/**
 * Logic_UnmatchKeshikomiMake
 *
 * 入金マッチ処理
 *
 * @category	 App
 * @package	 models\Logic
 * <AUTHOR> Kino
 * @since 	 2020/02/XX
 * @filesource 
 */

/**
 * 入金マッチ処理
 * 
 * @category	 App
 * @package	 models\Logic
 * <AUTHOR> Kino
 * @since 	 2020/02/XX
 */
class Logic_UnmatchKeshikomiMake {

    static protected $_denpyo_kbn_fk = 2;      // 入金種別 2:振込　（コード名称：0640）
    static protected $_denpyo_kbn_ch = 7;      // 入金種別 7:手数料（コード名称：0640）
    static protected $_diff_code_kbn = 8552;   // コード区分 8552:振込入金差異
    static protected $_chg_code_kbn = 8557;    // コード区分 8557:銀行振込手数料科目
    static protected $_pl_code_kbn = 8565;    // コード区分 8565:消費税区分(課税仕入)
    static protected $_nyu_code_kbn = 9762;    // コード区分 9762:入金科目(預り金)
    static protected $_print_kbn_vr = '3';     // 請求書印刷区分：3=>仮想口座

    /**
     * 入金マッチ処理メイン
     *
     * <AUTHOR> Kino
     * @since	   2020/02/XX
     * @param	   Msi_Sys_Db $db	  データベース
     * @param	   array  $payment_data   振込入金消込情報(画面データ)
     * @param	   array  $seikyu_deta    請求伝票(画面データ)
     * @param	   string $keshikomi_kbn  消込区分 (0:未消込 ,1:消込済み ,9:削除)
     */
    public static function Main($db, $payment_data, $seikyu_deta, $keshikomi_kbn) {
        
        $cnt = 0;
        
        // 振込入金ファイル取込明細取得
        $param = array('history_no' => $payment_data['history_no']
                      ,'msi_no'     => $payment_data['msi_no']);
        $furikomi_msi = static::getUploadFurikomiMsi($db, $param);
        $payment_info = static::getPaymentInfo($db, $param);
        // 更新回数の差分が発生した場合はエラーを返す
        if ($payment_data['mod_cnt'] != $payment_info[0]['_mod_cnt']) {
            $message = sprintf("既に変更が行われています。画面を更新してください。");
            throw new Msi_Logic_ExceptionInput($message);
        }
        
        // 月次チェック
        $kanjyo = str_pad($furikomi_msi['kanjyo_date'], 6, '0', STR_PAD_LEFT);
        $nyukin_ymd = Logic_UploadFurikomiMake::warekiToSeirekiFromYYMMDD($kanjyo);
        $fixFlg = self::checkDataFix($db, $nyukin_ymd, 3); // 3:入金確定
        if($fixFlg){
            $message = sprintf("月次確定済みのため、変更ができません. (勘定日:".$nyukin_ymd.")");
            throw new Msi_Logic_ExceptionInput($message);
        }
        
        // 仮受金
        $kari_cost = $payment_data['kariuke_prc'];
        
        // 手数料
        $paramCn = array('code_kbn' => static::$_diff_code_kbn, 'kbn_value_cd' => trim($payment_data['kaisya_cd']));
        $selCn = DataMapper_CodeNmMst::findOne($db, $paramCn);
        $chg_cost = $payment_data['furikomi_chg_cost'];
        $furikomi_msi['diff_prc'] = $payment_data['diff_prc'];

        // マッチ
        if($keshikomi_kbn == 0){ // 0:未消込
            // マッチ削除の場合(仮受金になる)
            if(array_key_exists('delete_chk', $payment_data) && $payment_data['delete_chk'] == 1){
                // 振込入金消込情報を更新
                $kari_cost = $payment_data['nyukin_prc'];
                $cnt += static::FurikomiNyukinMake($db, $furikomi_msi, 0, $kari_cost, 1);
                $furikomi_msi['kariuke_prc'] = $kari_cost;
                $cnt += static::doMatch($db, $furikomi_msi);
            }
            // マッチ消込済の場合
            else{
                // 振込入金消込情報を更新
                $cnt += static::FurikomiNyukinMake($db, $furikomi_msi, $chg_cost, $kari_cost, 0);
                if(!empty($seikyu_deta)){
                    // 請求データを請求金額(小さい順)、請求伝票順で並べ替え
                    $seikyu_deta = static::getSortData($seikyu_deta, 'this_nyukin_prc', 'seikyu_den_no', $chg_cost);
                    foreach ($seikyu_deta as $seikyu_rec) {
                        $chg_cost = array_key_exists('chg_cost', $seikyu_rec) ? $seikyu_rec['chg_cost'] : 0;
                        $cnt += static::doMatch($db, $furikomi_msi, $seikyu_rec, $chg_cost);
                    }
                }
                // 仮受金
                if($kari_cost && $kari_cost !== 0){
                    $furikomi_msi['kariuke_prc'] = $kari_cost;
                    $cnt += static::doMatch($db, $furikomi_msi);
                }
            }
        }
        // アンマッチ(未消込解除)
        else if($keshikomi_kbn == 1){ // 1:消込済み
            $cnt += static::doUnMatch($db, $furikomi_msi, 1);
        }
        // アンマッチ(削除解除)
        else if($keshikomi_kbn == 9){ // 9:削除
            if(array_key_exists('delete_chk', $payment_data) && $payment_data['delete_chk'] == 0){
                $cnt += static::doUnMatch($db, $furikomi_msi, 9);
            }
        }
        
        return $cnt;
    }
    
    /**
     * マッチ処理
     *
     * <AUTHOR> Kino
     * @since	   2020/02/XX
     * @param     Msi_Sys_Db $db                  データベース
     * @param     array      $uploadFurikomiData  振込入金ファイル取込明細
     * @param     array      $seikyuDenpyoData    請求伝票
     * @param     int        $chg_cost            手数料
     * @return	   integer $cnt 更新件数
     */
    private static function doMatch($db, $uploadFurikomiData, $seikyuDenpyoData=array(), $chg_cost=0){
        
        $cnt = 0;
        
        // 入金伝票を作成
        $nyukin_data = static::NyukinDenpyoMake($db, $uploadFurikomiData, $seikyuDenpyoData, $chg_cost);
        // 入金伝票明細を作成
        $cnt += static::NyukinDenpyoMsiMake($db, $uploadFurikomiData, $seikyuDenpyoData, $nyukin_data, $chg_cost);
        // 振込入金消込情報明細を作成
        $furikomi_msi = static::FurikomiNyukinMsiMake($db, $uploadFurikomiData, $seikyuDenpyoData, $nyukin_data);
        
        // 仮受金の場合は請求伝票・売上伝票は更新しない (仮受金の場合、紐づく請求伝票がない)
        if(empty($seikyuDenpyoData)){
            return  1;
        }
        
        // 入金伝票から売上伝票の入金金額、請求残高を修正して更新する
        Logic_SyukeiTblUpdateSub::updNyukinPrcSeikyu2($db, $seikyuDenpyoData['seikyu_den_no']);
        
        return 1;
    }
    
    /**
     * アンマッチ処理
     *
     * <AUTHOR> Kino
     * @since	   2020/02/XX
     * @param     Msi_Sys_Db $db                  データベース
     * @param     array      $uploadFurikomiData  振込入金ファイル取込明細
     * @param     int        $kari_cost           仮受金
     * @param     int        $mode                モード 1:未消込解除、9:削除解除
     * @return	   integer $cnt 更新件数
     */
    private static function doUnMatch($db, $uploadFurikomiData, $mode=1){
        
        $cnt = 0;
        $message = null;
        
        // 振込入金消込情報明細を取得 (入金伝票No., 請求伝票No.取得のため)
        $selPayMsi = self::getPaymentMsi($db, $uploadFurikomiData);
        
        foreach($selPayMsi as $payRec){
            
            // 財務連動に出力済みだった場合はエラー
            $selNyukinMsi = $db->easySelect(<<< END_OF_SQL
		SELECT * FROM nyukin_denpyo_msi WHERE delete_flg  = 0 AND denpyo_no = :denpyo_no AND zaimu_rendo_kbn = 1
END_OF_SQL
                , array('denpyo_no' => $payRec['nyukin_den_no']));
            if ( Msi_Sys_Utils::myCount($selNyukinMsi) > 0 ) {
                $message = sprintf("財務連動済みのため、変更ができません.");
                break;
            }
            
            $ryosyuFlg = static::checkRyosyushoHistory($db, $payRec['nyukin_den_no']);
            if($ryosyuFlg){
                $message = sprintf("領収書発行済みのため、変更ができません.");
                break;
            }
            
            // 入金伝票、入金伝票明細を削除
            $cnt += static::NyukinDenpyoDelete($db, $payRec['nyukin_den_no']);
            
            // 9:削除解除以外の場合のみ更新
            if($mode !== 9 && isset($payRec['seikyu_no'])){
                // 入金伝票から売上伝票の入金金額、請求残高を修正して更新する
                Logic_SyukeiTblUpdateSub::updNyukinPrcSeikyu2($db, $payRec['seikyu_no']);
            }
        }
        
        if($message){
            throw new Msi_Logic_ExceptionInput($message);
        }
        
        // 振込入金消込情報を更新・振込入金消込情報明細を削除
        $cnt += static::FurikomiNyukinDelete($db, $uploadFurikomiData, $mode);
        
        return $cnt;
    }
    
    /**
     * 入金伝票を作成
     *
     * <AUTHOR> Kino
     * @since	   2020/02/XX
     * @param	   Msi_Sys_Db $db	   データベース
     * @param	   array  $upload_furikomi 振込入金ファイル取込明細情報
     * @param	   array  $upload_seikyu   請求伝票情報 (array()の場合はマッチ削除扱い)
     * @param     int    $chg_cost        手数料
     * @return	   array  $hdrData         入金伝票レコード
     */
    private static function NyukinDenpyoMake($db, $upload_furikomi, $upload_seikyu, $chg_cost=0) {

        $nyuDenpyo = array();
        // 存在しない入金伝票番号を取得
        $denpyo_no = App_ClsGetCodeNo::GetCodeNo($db, 'nyukin_denpyo', 'denpyo_no', null);
        $nyuDenpyo['denpyo_no'] = $denpyo_no;                                        // 伝票NO
        if($upload_seikyu){
            $nyuDenpyo['data_kbn'] = $upload_seikyu['data_kbn'];                     // データ区分
            $nyuDenpyo['seko_no'] = $upload_seikyu['seko_no'];                       // 施行番号
            $nyuDenpyo['seko_no_sub'] = $upload_seikyu['seko_no_sub'];               // 施行番号（枝番）
            $nyuDenpyo['seikyu_no'] = $upload_seikyu['seikyu_den_no'];               // 請求書№
            $nyuDenpyo['kaisya_cd'] = $upload_seikyu['kaisya_cd'];                   // 会社コード
            $nyuDenpyo['bumon_cd'] = $upload_seikyu['bumon_cd'];                     // 部門コード
            $nyuDenpyo['nyu_kbn'] = 1;                                               // 入金区分(1:FB入金)
            $nyukin_prc = $upload_seikyu['this_nyukin_prc'] + $chg_cost; 
        }else{
            $nyuDenpyo['seko_no'] = '0000000000';                                    // 施行番号
            $nyuDenpyo['seko_no_sub'] = '00';                                        // 施行番号（枝番）
            $nyuDenpyo['seikyu_no'] = '0000000000';                                  // 請求書№
            $nyuDenpyo['bumon_cd'] = trim($upload_furikomi['kaisya_cd']);            // 部門コード
            $nyuDenpyo['nyu_kbn'] = 1;                                               // 入金区分(1:FB入金)
            $nyukin_prc = $upload_furikomi['kariuke_prc']; 
        }
        $nyuDenpyo['tanto_cd'] = App_Utils::getTantoCd();                            // 担当者コード
        $nyuDenpyo['uri_history_no'] = 0;                                            // 売上履歴番号
        $kanjyo = str_pad($upload_furikomi['kanjyo_date'], 6, '0', STR_PAD_LEFT);
        $kanjyo_date = Logic_UploadFurikomiMake::warekiToSeirekiFromYYMMDD($kanjyo);
        $nyuDenpyo['nyukin_ymd'] = $kanjyo_date;                                     // 入金日
        $nyuDenpyo['nyukin_prc'] = $nyukin_prc;                                      // 入金金額合計 (今回入金金額 + 手数料 + 仮受金)
        if($nyuDenpyo['nyukin_ymd'] && $upload_seikyu){
            $ZeiTbl = App_ClsTaxLib::GetTaxInfo($db, $nyuDenpyo['nyukin_ymd']);
            $TaxPrc = App_ClsTaxLib::CalcTax($chg_cost, 1, $ZeiTbl['zei_rtu'], $ZeiTbl['zei_hasu_kbn']);
            $nyuDenpyo['in_zei_prc'] = $TaxPrc['ZeiPrc'];                            // 内税消費税額
        }
        
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('nyukin_denpyo', $nyuDenpyo);
        $cnt = $db->easyExecute($sql, $param);

        return $nyuDenpyo;
    }
    
    /**
     * 入金伝票明細を作成
     *
     * <AUTHOR> Kino
     * @since	   2020/02/XX
     * @param	   Msi_Sys_Db $db	   データベース
     * @param	   array  $upload_furikomi 振込入金ファイル取込明細情報
     * @param	   array  $upload_seikyu   請求伝票情報 (array()の場合はマッチ削除扱い)
     * @param	   array  $nyukin_data     入金伝票レコード
     * @param     int    $chg_cost        手数料
     * @return    int    $cnt             更新件数
     */
    private static function NyukinDenpyoMsiMake($db, $upload_furikomi, $upload_seikyu, $nyukin_data, $chg_cost=0) {
        
        $cnt = 0;
        $nyuDenpyoMsi = array();
        $count = 1;
        $nyuDenpyoMsi['denpyo_no'] = $nyukin_data['denpyo_no'];                     // 伝票NO
        $nyuDenpyoMsi['msi_no'] = $count;                                           // 明細№
        $nyuDenpyoMsi['disp_no'] = $count;                                          // 表示順
        $nyuDenpyoMsi['denpyo_kbn'] = static::$_denpyo_kbn_fk;                      // 伝票区分
        if($upload_seikyu){
            $nyuDenpyoMsi['nyukn_kbn'] = 0;                                         // 入金区分 0:入金
            $nyuDenpyoMsi['kaisya_cd'] = $upload_seikyu['kaisya_cd'];               // 会社コード
            $nyuDenpyoMsi['bumon_cd'] = $upload_seikyu['bumon_cd'];                 // 部門コード
            // 親部門を取得
            $oya_bumon_cd = DataMapper_BumonMst::findOyaBumonCd($db, $upload_seikyu['bumon_cd']);
            $bankBumonCd = $oya_bumon_cd;
            $nyuDenpyoMsi['nyukin_prc'] = $upload_seikyu['this_nyukin_prc'];        // 入金金額
        }else{
            $nyuDenpyoMsi['nyukn_kbn'] = 2;                                         // 入金区分 2:仮受金
            $nyuDenpyoMsi['bumon_cd'] = trim($upload_furikomi['kaisya_cd']);        // 部門コード
            $bankBumonCd = trim($upload_furikomi['kaisya_cd']);        // 部門コード
            $nyuDenpyoMsi['nyukin_prc'] = $upload_furikomi['kariuke_prc'];          // 入金金額
        }
        // 科目コード
        $kamokuArr = static::getKamoku($db, array('code_kbn' => static::$_nyu_code_kbn));
        if($kamokuArr){
            $nyuDenpyoMsi['kamoku_cd'] = $kamokuArr['kamoku_cd'];                     // 入金科目コード
            $nyuDenpyoMsi['hojo_cd'] = $kamokuArr['biko'];                         // 補助科目コード
        }
        $ZeiTbl = App_ClsTaxLib::GetTaxInfo($db, $nyukin_data['nyukin_ymd']);
        $nyuDenpyoMsi['shoihi_zei_cd'] = 0;                         // 消費税コード(入金は0設定)
        if(array_key_exists('kamoku_cd' ,$nyuDenpyoMsi)){
            $zei_cd = static::getZeiCd($db, $nyuDenpyoMsi['kamoku_cd'], 0);
            // 税入力区分が「あり」以外は税関連コードはNULLにする
            if (isset($zei_cd)) {
                $nyuDenpyoMsi['zei_cd'] = $zei_cd;
            } else {
                $nyuDenpyoMsi['shoihi_zei_cd'] = null;
            }
        }
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('nyukin_denpyo_msi', $nyuDenpyoMsi);
        $cnt += $db->easyExecute($sql, $param);
        
        // 手数料がある場合は明細を作成
        // (上記の明細と差分があるところのみ書き換え)
        if($chg_cost !== 0 && $chg_cost !== "0"){
            $count = 2;
            $nyuDenpyoMsi['msi_no'] = $count;                                  // 明細№
            $nyuDenpyoMsi['disp_no'] = $count;                                 // 表示順
            $nyuDenpyoMsi['denpyo_kbn'] = static::$_denpyo_kbn_ch;             // 伝票区分
            $nyuDenpyoMsi['nyukin_prc'] = $chg_cost;                           // 入金金額
            // 消費税コード
            $ZeiTbl = App_ClsTaxLib::GetTaxInfo($db, $nyukin_data['nyukin_ymd']);
            $nyuDenpyoMsi['shoihi_zei_cd'] = $ZeiTbl['zei_cd'];     
             // 内税消費税額
            $TaxPrc = App_ClsTaxLib::CalcTax($chg_cost, 1, $ZeiTbl['zei_rtu'], $ZeiTbl['zei_hasu_kbn']);
            $nyuDenpyoMsi['in_zei_prc'] = $TaxPrc['ZeiPrc'];
            // 科目コード
            $kamokuArr = static::getKamoku($db, array('code_kbn' => static::$_chg_code_kbn));
            if($kamokuArr){
                $nyuDenpyoMsi['kamoku_cd'] = $kamokuArr['kamoku_cd'];          // 入金科目コード
                $nyuDenpyoMsi['hojo_cd'] = $kamokuArr['hojo_cd'];              // 補助科目コード
                // 税コード
                $zei_cd = static::getZeiCd($db, $nyuDenpyoMsi['kamoku_cd'], $nyuDenpyoMsi['shoihi_zei_cd']);
                // 税入力区分が「あり」以外は税関連コードはNULLにする
                if (isset($zei_cd)) {
                    $nyuDenpyoMsi['zei_cd'] = $zei_cd;
                } else {
                    $nyuDenpyoMsi['shoihi_zei_cd'] = null;     
                }
            }
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('nyukin_denpyo_msi', $nyuDenpyoMsi);
            $cnt += $db->easyExecute($sql, $param);
        }
        
        return $cnt;
    }
    
     /**
     * 入金伝票・明細を削除
     *
     * <AUTHOR> Kino
     * @since	   2020/02/XX
     * @param	   Msi_Sys_Db $db	   データベース
     * @param	   array  $nyukin_denpyo_no 振込入金ファイル取込明細情報
     * @return	   bool   true：成功 false:該当なし
     */
    private static function NyukinDenpyoDelete($db, $nyukin_denpyo_no) {

        $cnt = 0;
        // 入金伝票を削除
        $cnt += $db->easyExecute(<<< END_OF_SQL
		UPDATE nyukin_denpyo
		   SET	 delete_flg = 1
		 WHERE	delete_flg  = 0
		 AND	denpyo_no   = :denpyo_no
END_OF_SQL
                , array('denpyo_no' => $nyukin_denpyo_no
        ));
        // 入金伝票明細を削除
        $cnt += $db->easyExecute(<<< END_OF_SQL
		UPDATE nyukin_denpyo_msi
		   SET	 delete_flg = 1
		 WHERE	delete_flg  = 0
		 AND	denpyo_no   = :denpyo_no
END_OF_SQL
                , array('denpyo_no' => $nyukin_denpyo_no
        ));
        return $cnt;
    }
    
    /**
     * 振込入金消込情報を作成
     *
     * <AUTHOR> Kino
     * @since	   2020/02/XX
     * @param	   Msi_Sys_Db $db	データベース
     * @param	   array $upload_rec    振込入金ファイル取込明細情報
     * @param     int   $chg_cost      手数料
     * @param     int   $kari_cost     仮受金
     * @param     int   $mode          モード 0:マッチ消込済(default)、1:マッチ削除
     * @return	   integer $cnt 更新件数
     */
    private static function FurikomiNyukinMake($db, $upload_rec, $chg_cost, $kari_cost, $mode=0) {

        $where['history_no'] = $upload_rec['history_no'];       // 取込履歴番号
        $where['msi_no'] = $upload_rec['msi_no'];               // 取込明細番号
        
        $furikomiNyu = array();
        if($mode == 0){ // マッチ消込済の場合
            $furikomiNyu['upload_date'] = Msi_Sys_Utils::getDate(); // 取込日
            if($upload_rec['diff_prc'] > 0){ // 差額がある場合
                $payment_kbn = 0; // 0：未消込
            }else{
                $payment_kbn = 1; // 1：消込済み
            }
        }else if($mode == 1){ // マッチ削除の場合
            $payment_kbn = 9; // 9：削除
            $furikomiNyu['upload_date'] = Msi_Sys_Utils::getDate(); // 取込日
        }
        $furikomiNyu['payment_kbn'] = $payment_kbn;            // 消込区分 (0：未消込 1：消込済み 9：削除)
        if($chg_cost !== 0 && $chg_cost !== "0"){   
            $furikomiNyu['furikomi_chg_cost'] = $chg_cost;     // 振込手数料
        }   
        if($kari_cost !== 0 && $kari_cost !== "0"){   
            $furikomiNyu['kariuke_prc'] = $kari_cost;          // 仮受金
        }
        
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL('furikomi_payment_info', $furikomiNyu, $where);
        $cnt = $db->easyExecute($sql, $param);

        return $cnt;
    }

    /**
     * 振込入金消込情報明細を作成
     *
     * <AUTHOR> Kino
     * @since	   2020/02/XX
     * @param	   Msi_Sys_Db $db	   データベース
     * @param	   array  $upload_furikomi 振込入金ファイル取込明細情報
     * @param	   array  $upload_seikyu   請求伝票情報 (array()の場合はマッチ削除扱い)
     * @param	   int    $nyu_denpyo      入金伝票No
     * @return	   array  $furikomiNyuMsi  振込入金消込情報明細レコード
     */
    private static function FurikomiNyukinMsiMake($db, $upload_furikomi, $upload_seikyu, $nyu_denpyo) {

        $furikomiNyuMsi = array();
        $furikomiNyuMsi['history_no'] = $upload_furikomi['history_no'];   // 取込履歴番号
        $furikomiNyuMsi['msi_no'] = $upload_furikomi['msi_no'];           // 取込明細番号
        $furikomiNyuMsi['nyukin_den_no'] = $nyu_denpyo['denpyo_no'];      // 入金伝票No
        if(!empty($upload_seikyu)){ // マッチ消込済の場合
            $seikyu_den_no = $upload_seikyu['seikyu_den_no'];
        }else{ // マッチ削除の場合
            $seikyu_den_no = null;
        }
        $furikomiNyuMsi['seikyu_no'] = $seikyu_den_no;   // 請求伝票No
            
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('furikomi_payment_info_msi', $furikomiNyuMsi);
        $cnt = $db->easyExecute($sql, $param);

        return $furikomiNyuMsi;
    }
    
    /**
     * 振込入金消込情報を更新・明細を削除
     *
     * <AUTHOR> Kino
     * @since	   2020/02/XX
     * @param	   Msi_Sys_Db $db	データベース
     * @param	   array $upload_rec    振込入金ファイル取込明細情報
     * @return	   array $furikomiNyu   振込入金消込レコード
     */
    private static function FurikomiNyukinDelete($db, $upload_rec, $mode) {
        $cnt = 0;
        
        $param = array('history_no' => $upload_rec['history_no']);
        $furikomi_data = static::getUploadFurikomi($db, $param);
        if ($mode == 1) {
            $date = Msi_Sys_Utils::getDate();
        } else {
            $date = $furikomi_data['upload_date'];
        }
        
        // 振込入金消込情報を更新
        $cnt = $db->easyExecute(<<< END_OF_SQL
          UPDATE furikomi_payment_info
             SET payment_kbn = 0
               , upload_date = :upload_date
               , furikomi_chg_cost = 0
               , kariuke_prc = 0
           WHERE delete_flg = 0
           AND   history_no  = :history_no
           AND   msi_no      = :msi_no
END_OF_SQL
                , array('upload_date' => $date,
                        'history_no'  => $upload_rec['history_no'],
                        'msi_no'      => $upload_rec['msi_no']
        ));
        // 振込入金消込情報明細を削除
        $cnt = $db->easyExecute(<<< END_OF_SQL
          UPDATE furikomi_payment_info_msi
             SET delete_flg = 1
           WHERE delete_flg = 0
           AND   history_no = :history_no
           AND   msi_no     = :msi_no
END_OF_SQL
                , array('history_no' => $upload_rec['history_no'],
                        'msi_no'     => $upload_rec['msi_no']
        ));

        return $cnt;
    }
    
    /**
     * 月次確定チェック処理
     *
     * <AUTHOR> Kino
     * @since  2021/02/XX
     * @param  Msi_Sys_Db $db      データベース
     * @param  string     $ymd     対象日 (計上日、入金日etc...)
     * @param  string     $fix_kbn 確定区分 1:売上, 2:仕入, 3:入金 
     * @return boolean    true:確定済, false:未確定
     */
    private static function checkDataFix($db, $ymd, $fix_kbn) {
        // 月次確定チェック
        $flg = false;
        $fixInfo = DataMapper_DataFixTable::findOne($db, array('fix_kbn' => $fix_kbn));
        if (!empty($fixInfo) && count($fixInfo) > 0) {
            if ($ymd <= $fixInfo['fix_date_ymd']) {
                $flg = true;
            }
        }
        return $flg;
    }
    
    /**
     * 領収書発行チェック処理
     *
     * <AUTHOR> Kino
     * @since  2021/02/XX
     * @param  Msi_Sys_Db $db            データベース
     * @param  string     $nyukin_den_no 入金伝票番号
     * @return boolean    true:発行済, false:未発行
     */
    private static function checkRyosyushoHistory($db, $nyukin_den_no) {
        
        $flg = false;
        $param = array('hako_kbn' => array('<>', '9') // 9:破棄
                     , 'nyukin_den_no' => $nyukin_den_no); 
        $selRyoshu = DataMapper_RyosyushoHistory::find($db, $param);
        if(Msi_Sys_Utils::myCount($selRyoshu) > 0){
            $flg = true;
        }
        
        return $flg;
    }
    
    /**
     * 振込入金ファイル取込履歴の取得処理
     * 
     * <AUTHOR> Kino
     * @since     2020/02/XX
     * @param	   Msi_Sys_Db $db         データベース
     * @param	   int        $param 
     * @return    boolean
     */
    private static function getUploadFurikomi($db, $param) {
        
        $sql = <<< END_OF_SQL
        SELECT *
          FROM upload_furikomi_nyukin_history
         WHERE history_no = :history_no
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        
        return $select;
    }
    
    /**
     * 振込入金ファイル取込明細の取得処理
     * 
     * <AUTHOR> Kino
     * @since     2020/02/XX
     * @param	   Msi_Sys_Db $db         データベース
     * @param	   int        $param 
     * @return    boolean
     */
    private static function getUploadFurikomiMsi($db, $param) {
        
        $sql = <<< END_OF_SQL
        SELECT *
          FROM upload_furikomi_nyukin_msi
         WHERE history_no = :history_no
           AND msi_no = :msi_no
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        
        return $select;
    }
    
    /**
     * 	振込入金消込情報明細を取得
     *
     * <AUTHOR> Mogi
     * @since	   2018/08/01
     * @param	   Msi_Sys_Db $db	          データベース
     * @param     array      $uploadFurikomiData  振込入金ファイル取込明細
     * @return	   bool   true：成功 false:該当なし
     */
    private static function getPaymentMsi($db, $uploadFurikomiData) {

        $denpyo_no = $db->easySelect(<<< END_OF_SQL
		SELECT *
                FROM furikomi_payment_info_msi
		 WHERE	delete_flg = 0
		 AND	history_no	= :history_no
                AND	msi_no	        = :msi_no
END_OF_SQL
                , array('history_no' => $uploadFurikomiData['history_no'],
                        'msi_no'     => $uploadFurikomiData['msi_no']
        ));

        return $denpyo_no;
    }
    
    /**
     * 	振込入金消込情報を取得
     *
     * <AUTHOR> Tosaka
     * @since	   2021/xx/xx
     * @param	   Msi_Sys_Db $db	          データベース
     * @param     array      $uploadFurikomiData  振込入金ファイル取込明細
     * @return	   bool   true：成功 false:該当なし
     */
    private static function getPaymentInfo($db, $uploadFurikomiData) {

        $select = $db->easySelect(<<< END_OF_SQL
		SELECT *
                FROM furikomi_payment_info
		 WHERE	delete_flg = 0
		 AND	history_no	= :history_no
                AND	msi_no	        = :msi_no
END_OF_SQL
                , array('history_no' => $uploadFurikomiData['history_no'],
                        'msi_no'     => $uploadFurikomiData['msi_no']
        ));

        return $select;
    }
    
    /**
     * 科目・補助科目コードの取得処理
     * 区分値正式名に科目、区分値コードに補助科目
     * 
     * <AUTHOR> Kino
     * @since     2021/02/XX
     * @param	   Msi_Sys_Db $db         データベース
     * @return    array      $select
     */
    private static function getKamoku($db, $param) {
        
        $sql = <<< END_OF_SQL
        SELECT kbn_value_lnm AS kamoku_cd -- 区分値正式名に科目
             , kbn_value_cd AS hojo_cd    -- 区分値コードに補助科目
             , biko -- 9762の場合は備考に補助科目
          FROM code_nm_mst
         WHERE code_kbn = :code_kbn
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        
        return $select;
    }
    
    /**
     * 税コード取得処理
     * (科目マスタの税コード入力区分が1の場合、税コードマスタを参照)
     * 
     * <AUTHOR> Kino
     * @since     2020/02/XX
     * @param	   Msi_Sys_Db $db             データベース
     * @param	   int        $kamoku_cd      科目コード
     * @param	   int        $shoihi_zei_cd  消費税コード
     * @return    int        $zei_cd         税コード
     */
    private static function getZeiCd($db, $kamoku_cd, $shoihi_zei_cd) {
        
        $zei_cd = null;
        // 税コード入力区分を確認
        $param = array('kamoku_cd'      => $kamoku_cd
                      ,'zei_cd_inp_kbn' => 1);
        $sql = <<< END_OF_SQL
        SELECT *
          FROM kamoku_mst
         WHERE kamoku_cd = :kamoku_cd
           AND zei_cd_inp_kbn = :zei_cd_inp_kbn
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        
        if(empty($select)){
            return $zei_cd;
        }
        
        // 性格区分によってコード区分を取得する
        if ($select['seikaku_kbn'] === '2') {
            $codeMst = DataMapper_CodeNmMst::find($db, array('code_kbn' => static::$_pl_code_kbn, 'kbn_value_cd' => $shoihi_zei_cd));
            if (count($codeMst) > 0) {
                $zei_cd = $codeMst[0]['kbn_value_cd_num'];
            }
        }
        return $zei_cd;
    }
    
    /**
     * 多次元の配列をソートし必要データを取得
     * (また、一番請求金額が大きい配列に手数料を格納する)=最後のデータ
     * 
     * <AUTHOR> Kino
     * @since     2020/02/XX
     * @param	   array      $data 
     * @param	   string     $key1
     * @param	   string     $key2
     * @return    array      $data
     */
    private static function getSortData($data, $key1, $key2, $chg_cost) {
    
        // 配列をソートする
        $key_cnt = 0;
        foreach ($data as $key => $row) {
            $keyArr1[$key] = $row[$key1];
            $keyArr2[$key] = $row[$key2];
            $key_cnt++;
        }
        array_multisort($keyArr1, SORT_ASC ,$keyArr2, SORT_ASC, $data);
        // 最終行に手数料をいれる
        $data[$key_cnt-1]['chg_cost'] = $chg_cost;

        return $data;
    }
}
