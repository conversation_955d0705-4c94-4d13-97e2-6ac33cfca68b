<?php
  /**
   * DataMapper_RyosyushoHistory
   *
   * 領収書発行履歴 データマッパークラス
   *
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2015/06/xx
   * @filesource 
   */

  /**
   * 領収書発行履歴 データマッパークラス
   * 
   * @category   App
   * @package    models\DataMapper
   * <AUTHOR> Mihara
   * @since      2015/06/xx
   */
class DataMapper_RyosyushoHistory extends DataMapper_Abstract
{
    /**
     * データ 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/06/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.uri_den_no, T.hako_count DESC ';
        }

        $isDateEffective = false;
        if ( $isDateEffective ) {
            // $dateWhere     = " AND m.tekiyo_st_date <= CURRENT_DATE AND m.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere     = '';
        } else {
            $dateWhere = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
        ,TO_CHAR(hako_date, 'YYYY/MM/DD hh24:mi')            AS hako_date_disp
        ,TO_CHAR(haki_date, 'YYYY/MM/DD hh24:mi')            AS haki_date_disp
        ,TO_CHAR(hako_date, 'YYYY/MM/DD hh24:mi:ss')         AS hako_date_disp2
        ,TO_CHAR(haki_date, 'YYYY/MM/DD hh24:mi:ss')         AS haki_date_disp2
        ,uri_den_no || '-' || lpad(hako_count::text, 2, '0') AS ryoshusho_no
        ,TO_CHAR(hako_date, 'YYYYMMDDHH24MISS')              AS hako_ymdhis
        ,lpad(hako_count::text, 2, '0')                      AS ryoshu_edaban
        ,ryosyusho_no || '-' || ryosyusho_eda_no             AS ryosyusho_no_ceremo -- 領収書番号   CEREMO
        ,ryosyusho_no || '-' || ryosyusho_eda_no             AS ryosyusho_no_sub    -- 領収書番号
        ,TO_CHAR(nyukin_ymd, 'YYYY/MM/DD')                   AS nyukin_date_disp
  FROM (
SELECT  m.*
       ,CASE m.hako_kbn WHEN 0 THEN '新規発行'
                        WHEN 1 THEN '再発行'
                        WHEN 9 THEN '破棄'
                        ELSE ''
        END                                                  AS hako_kbn_nm
       ,t1.tanto_nm AS syukin_tanto_nm
       ,t2.tanto_nm AS jimu_tanto_nm
       ,t3.tanto_nm AS haki_user
       ,rhf.v_free1 AS ryoshu_zei_prc
       ,rhf.d_free1 AS ryoshu_date
       ,rhf.k_free2 
       ,rhf.k_free2_cd
       ,rhf.v_free2 
       ,rhf.v_free4 
       ,rhf.v_free5
       ,rhf.n_free2 
       ,rhf.n_free3 
       ,rhf.n_free4 
       ,rhf.n_free5 
       ,rhf.n_free6 
       ,rhf.n_free7 
       ,nd.v_free1                                          AS card_den_no       -- カード決済伝票番号
       ,nd.v_free2                                          AS moushi_no         -- 申込番号
       ,TRIM(sd.pay_method_cd)                              AS pay_method_cd     -- 入金方法区分（支払方法区分）
       ,n_kbn.kbn_value_lnm                                 AS pay_method_nm     -- 入金方法
       ,COALESCE(hanbai.pay_kbn,syunou1.pay_kbn)            AS pay_kbn
       ,nd.nyukin_ymd
  FROM ryosyusho_history m
  LEFT JOIN tanto_mst t1
    ON m.syukin_tanto_cd=t1.tanto_cd
   AND t1.delete_flg=0
  LEFT JOIN tanto_mst t2
    ON m.jimu_tanto_cd=t2.tanto_cd
   AND t2.delete_flg=0
  LEFT JOIN tanto_mst t3
    ON (SELECT tanto_cd FROM login_mst lm WHERE lm.login_cd = substr(m._mod_user, 10) AND lm.delete_flg=0 LIMIT 1)=t3.tanto_cd
   AND t3.delete_flg=0
   AND m.haki_date IS NOT NULL
  LEFT JOIN ryosyusho_history_all_free rhf
    ON rhf.uri_den_no = m.uri_den_no
   AND rhf.hako_count = m.hako_count
  LEFT JOIN nyukin_denpyo nd
    ON nd.denpyo_no  = m.nyukin_den_no
   AND nd.delete_flg = 0
  LEFT JOIN front_denpyo hanbai
    ON hanbai.seikyu_den_no = nd.seikyu_no
   AND hanbai.delete_flg    = 0
 LEFT JOIN front_trn_msi m_syunou1
    ON m_syunou1.nyukin_den_no = m.nyukin_den_no
   AND m_syunou1.delete_flg    = 0
  LEFT JOIN front_denpyo syunou1
    ON syunou1.front_den_no = m_syunou1.front_den_no
   AND syunou1.delete_flg   = 0
  LEFT JOIN seikyu_denpyo sd
    ON sd.seikyu_den_no = nd.seikyu_no
   AND sd.delete_flg    = 0
  LEFT JOIN code_nm_mst n_kbn
    ON n_kbn.code_kbn = '8526'
   AND n_kbn.kbn_value_cd = TRIM(sd.pay_method_cd)
   AND n_kbn.delete_flg = 0
 WHERE m.delete_flg=0
       $dateWhere
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
	    
    /**
     * データ 取得
     *
     * <AUTHOR> Tosaka
     * @since      2017/7/19
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findUC($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.uri_den_no, T.hako_count DESC ';
        }

        $isDateEffective = false;
        if ( $isDateEffective ) {
            // $dateWhere     = " AND m.tekiyo_st_date <= CURRENT_DATE AND m.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere     = '';
        } else {
            $dateWhere = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
       ,TO_CHAR(hako_date, 'YYYY/MM/DD hh24:mi')            AS hako_date_disp
       ,TO_CHAR(haki_date, 'YYYY/MM/DD hh24:mi')            AS haki_date_disp
       ,TO_CHAR(hako_date, 'YYYY/MM/DD hh24:mi:ss')         AS hako_date_disp2
       ,TO_CHAR(haki_date, 'YYYY/MM/DD hh24:mi:ss')         AS haki_date_disp2
       ,uri_den_no || '-' || lpad(hako_count::text, 2, '0') AS ryoshusho_no
       ,ryosyusho_no || '-' || ryosyusho_eda_no	AS ryosyusho_no_ceremo	-- 領収書番号	CEREMO
       ,TO_CHAR(hako_date, 'YYYYMMDDHH24MISS')              AS hako_ymdhis
       ,lpad(hako_count::text, 2, '0')                      AS ryoshu_edaban
       ,ryosyusho_no || '-' || ryosyusho_eda_no             AS ryosyusho_no_sub    -- 領収書番号                
       ,TO_CHAR(nyukin_ymd, 'YYYY/MM/DD')                   AS nyukin_date_disp
  FROM (
SELECT  m.*
       ,CASE m.hako_kbn WHEN 0 THEN '新規発行'
                        WHEN 1 THEN '再発行'
                        WHEN 9 THEN '破棄'
                        ELSE ''
        END                                                  AS hako_kbn_nm
       ,t1.tanto_nm AS syukin_tanto_nm
       ,t2.tanto_nm AS jimu_tanto_nm
       ,t3.tanto_nm AS haki_user
       ,nd.v_free1                                          AS card_den_no       -- カード決済伝票番号
       ,nd.v_free2                                          AS moushi_no         -- 申込番号
       ,TRIM(sd.pay_method_cd)                              AS pay_method_cd     -- 入金方法区分（支払方法区分）
       ,n_kbn.kbn_value_lnm                                 AS pay_method_nm     -- 入金方法
       ,COALESCE(hanbai.pay_kbn,syunou1.pay_kbn)            AS pay_kbn
       ,nd.nyukin_ymd
  FROM ryosyusho_history_uc m
  LEFT JOIN tanto_mst t1
    ON m.syukin_tanto_cd=t1.tanto_cd
   AND t1.delete_flg=0
  LEFT JOIN tanto_mst t2
    ON m.jimu_tanto_cd=t2.tanto_cd
   AND t2.delete_flg=0
  LEFT JOIN tanto_mst t3
    ON (SELECT tanto_cd FROM login_mst lm WHERE lm.login_cd = substr(m._mod_user, 10) AND lm.delete_flg=0 LIMIT 1)=t3.tanto_cd
   AND t3.delete_flg=0
   AND m.haki_date IS NOT NULL
  LEFT JOIN nyukin_denpyo nd
    ON nd.denpyo_no  = m.nyukin_den_no
   AND nd.delete_flg = 0
  LEFT JOIN front_denpyo hanbai
    ON hanbai.seikyu_den_no = nd.seikyu_no
   AND hanbai.delete_flg    = 0
  LEFT JOIN front_trn_msi m_syunou1
    ON m_syunou1.nyukin_den_no = m.nyukin_den_no
   AND m_syunou1.delete_flg    = 0
  LEFT JOIN front_denpyo syunou1
    ON syunou1.front_den_no = m_syunou1.front_den_no
   AND syunou1.delete_flg   = 0
  LEFT JOIN seikyu_denpyo sd
    ON sd.seikyu_den_no = nd.seikyu_no
   AND sd.delete_flg    = 0
  LEFT JOIN code_nm_mst n_kbn
    ON n_kbn.code_kbn = '8526'
   AND n_kbn.kbn_value_cd = TRIM(sd.pay_method_cd)
   AND n_kbn.delete_flg = 0
 WHERE m.delete_flg=0
       $dateWhere
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
    
    /**
     * データ 取得(1レコード)
     *
     * <AUTHOR> Tosaka
     * @since      2017/7/19
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findoneUC($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.uri_den_no, T.hako_count ';
        }

        $isDateEffective = false;
        if ( $isDateEffective ) {
            // $dateWhere     = " AND m.tekiyo_st_date <= CURRENT_DATE AND m.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere     = '';
        } else {
            $dateWhere = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
       ,TO_CHAR(hako_date, 'YYYY/MM/DD hh24:mi') AS hako_date_disp
       ,TO_CHAR(haki_date, 'YYYY/MM/DD hh24:mi') AS haki_date_disp
       ,uri_den_no || '-' || lpad(hako_count::text, 2, '0') AS ryoshusho_no
  FROM (
SELECT  m.*
       ,t1.tanto_nm AS syukin_tanto_nm
       ,t2.tanto_nm AS jimu_tanto_nm
       ,t3.tanto_nm AS haki_user
  FROM ryosyusho_history_uc m
  LEFT JOIN tanto_mst t1
    ON m.syukin_tanto_cd=t1.tanto_cd
   AND t1.delete_flg=0
  LEFT JOIN tanto_mst t2
    ON m.jimu_tanto_cd=t2.tanto_cd
   AND t2.delete_flg=0
  LEFT JOIN tanto_mst t3
    ON (SELECT tanto_cd FROM login_mst lm WHERE lm.login_cd = substr(m._mod_user, 10) AND lm.delete_flg=0 LIMIT 1)=t3.tanto_cd
   AND t3.delete_flg=0
   AND m.haki_date IS NOT NULL
 WHERE m.delete_flg=0
       $dateWhere
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );
        if ( count($select) == 0 ) {
            return null;
        }
        return $select[0];
    }
        /**
     * データ 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/06/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findRyoshuNo($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.uri_den_no, T.hako_count ';
        }
        $dateWhere = ' WHERE seko_no = '."'".$keyHash['seko_no']."' AND uri_den_no = '".$keyHash['uri_den_no']."'";
     

        $select = $db->easySelect( <<< END_OF_SQL
  SELECT *
      FROM(
            SELECT *
            FROM   ryosyusho_history  
            WHERE  hako_count = (SELECT MAX(hako_count) FROM ryosyusho_history
            $dateWhere    
                )
            ) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
    /**
     * データ 取得(セレモ専用) 
     *
     * <AUTHOR> Oka
     * @since      2017/06/16
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find2($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.uri_den_no, T.hako_count ';
        }

        $isDateEffective = false;
        if ( $isDateEffective ) {
            // $dateWhere     = " AND m.tekiyo_st_date <= CURRENT_DATE AND m.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere     = '';
        } else {
            $dateWhere = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
       ,TO_CHAR(hako_date, 'YYYY/MM/DD hh24:mi') AS hako_date_disp
       ,TO_CHAR(haki_date, 'YYYY/MM/DD hh24:mi') AS haki_date_disp
       ,daicho_no_eria || '-' || lpad(hako_count::text, 4, '100') AS ryoshusho_no
  FROM (
SELECT  m.*
       ,t1.tanto_nm AS syukin_tanto_nm
       ,t2.tanto_nm AS jimu_tanto_nm
       ,t3.tanto_nm AS haki_user
       ,ski.daicho_no_eria 
  FROM ryosyusho_history m
  LEFT JOIN tanto_mst t1
    ON m.syukin_tanto_cd=t1.tanto_cd
   AND t1.delete_flg=0
  LEFT JOIN tanto_mst t2
    ON m.jimu_tanto_cd=t2.tanto_cd
   AND t2.delete_flg=0
  LEFT JOIN tanto_mst t3
    ON (SELECT tanto_cd FROM login_mst lm WHERE lm.login_cd = substr(m._mod_user, 10) AND lm.delete_flg=0 LIMIT 1)=t3.tanto_cd
   AND t3.delete_flg=0
   AND m.haki_date IS NOT NULL
  LEFT JOIN seko_kihon_info ski
    ON m.seko_no =ski.seko_no
    AND ski.delete_flg = 0                
 WHERE m.delete_flg=0
       $dateWhere
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }
        /**
     * データ 取得(セレモ専用)
     *
     * <AUTHOR> Oka
     * @since      2017/06/26
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findRyoshuNo2($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.uri_den_no, T.hako_count ';
        }
        $dateWhere = ' WHERE uri_den_no = '."'".$keyHash['uri_den_no']."'";
     

        $select = $db->easySelect( <<< END_OF_SQL
  SELECT *
      FROM(
            SELECT *
            FROM   ryosyusho_history  
            WHERE  hako_count = (SELECT MAX(hako_count) FROM ryosyusho_history
            $dateWhere    
                )
            ) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }    
    

    /**
     * データ 取得
     *
     * <AUTHOR> Mihara
     * @since      2015/06/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findCeremony($db, $keyHash=array())
    {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.uri_den_no, T.hako_count ';
        }

        $isDateEffective = false;
        if ( $isDateEffective ) {
            // $dateWhere     = " AND m.tekiyo_st_date <= CURRENT_DATE AND m.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere     = '';
        } else {
            $dateWhere = '';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT *
       ,TO_CHAR(hako_date, 'YYYY/MM/DD hh24:mi') AS hako_date_disp
       ,TO_CHAR(haki_date, 'YYYY/MM/DD hh24:mi') AS haki_date_disp
       ,TO_CHAR(d_free1, 'YYYY/MM/DD') AS d_free1
       ,uri_den_no || '-' || lpad(hako_count::text, 2, '0') AS ryoshusho_no
       ,lpad(hako_count::text, 2, '0') AS ryoshu_edaban     --2017/10/17 Add Mogi
       ,ryosyusho_no || '-' || ryosyusho_eda_no	AS ryosyusho_no_ceremo	-- 領収書番号	CEREMO
        FROM (
SELECT  m.*
       ,rhf.k_free1
       ,rhf.v_free1
       ,rhf.v_free2
       ,rhf.v_free3
       ,rhf.d_free1
       ,rhf.n_free1
       ,rhf.n_free2
       ,rhf.n_free3
       ,rhf.n_free4
       ,rhf.n_free5
       ,rhf.n_free6
       ,rhf.text_free1
       ,t1.tanto_nm AS syukin_tanto_nm
       ,t2.tanto_nm AS jimu_tanto_nm
       ,t3.tanto_nm AS haki_user
  FROM ryosyusho_history m
  LEFT JOIN tanto_mst t1
    ON m.syukin_tanto_cd=t1.tanto_cd
   AND t1.delete_flg=0
  LEFT JOIN tanto_mst t2
    ON m.jimu_tanto_cd=t2.tanto_cd
   AND t2.delete_flg=0
  LEFT JOIN tanto_mst t3
    ON (SELECT tanto_cd FROM login_mst lm WHERE lm.login_cd = substr(m._mod_user, 10) AND lm.delete_flg=0 LIMIT 1)=t3.tanto_cd
   AND t3.delete_flg=0
   AND m.haki_date IS NOT NULL
  LEFT JOIN ryosyusho_history_all_free rhf
    ON m.uri_den_no = rhf.uri_den_no
    AND m.hako_count = rhf.hako_count
    AND rhf.delete_flg = 0
 WHERE m.delete_flg=0
       $dateWhere
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                                   , $param );

        return $select;
    }    
    
	/**
     * 領収書発行時に使う領収書番号を取得する
     * <AUTHOR> Tosaka
     * @since 2017/10/04
     * @param Msi_Sys_Db $db
     * @param str $uri_den_no 売上伝票番号
     * @param int $hako_kbn 発行区分
	 * @param int $referenceMonth 年度始月
	 * @param int $referenceDay 年度始日
     * @return  array('ryosyu_no'=>$next_ryosyu_no,'ryosyu_no_sub'=>$next_ryoshu_no_sub)
     */
    public static function getNextRyosyuNos($db, $uri_den_no, $hako_kbn, $referenceMonth = 4, $referenceDay = 1) {
        switch (true) {
            case ($hako_kbn === 0): // 0:新規発行
                // 全領収書から最大の領収書番号を取得
                $max_ryoshu_no = static::getMaxRyosyuNo($db, null, $referenceMonth, $referenceDay);
                $i_ryosyu_no = $max_ryoshu_no ? (int) $max_ryoshu_no : 0;
                $next_ryosyu_no = sprintf('%06d', $i_ryosyu_no + 1);
                $next_ryoshu_no_sub = 0;
                break;
            case ($hako_kbn === 1): // 1:変更発行
                // 売上伝票から最大の領収書番号を取得
                $max_ryoshu_no = static::getMaxRyosyuNo($db, $uri_den_no, $referenceMonth, $referenceDay);
                // 売上伝票に紐付いている領収書がなければ全領収書から今年度最大値を取得
                $max_ryoshu_no2 = $max_ryoshu_no ? $max_ryoshu_no : static::getMaxRyosyuNo($db, null, $referenceMonth, $referenceDay);
                $i_ryosyu_no = $max_ryoshu_no2 ? (int) $max_ryoshu_no2 : 0;
                $next_ryosyu_no = sprintf('%06d', $i_ryosyu_no + 1);
                $next_ryoshu_no_sub = static::getNextRyosyuNoSub($db, $next_ryosyu_no, $referenceMonth, $referenceDay);
                break;
            case ($hako_kbn === 2): // 2:再発行（破棄をしないで新しく領収書番号を取得する場合）＊東上用
                // 全領収書から最大の領収書番号を取得
                $max_ryoshu_no = static::getMaxRyosyuNo($db, null, $referenceMonth, $referenceDay);
                $i_ryosyu_no = $max_ryoshu_no ? (int) $max_ryoshu_no : 0;
                $next_ryosyu_no = sprintf('%06d', $i_ryosyu_no + 1);
                $next_ryoshu_no_sub = 0;
                break;
            default:
                $next_ryosyu_no = null;
                $next_ryoshu_no_sub = null;
                break;
        }
        return array('ryosyu_no' => $next_ryosyu_no, 'ryosyu_no_sub' => $next_ryoshu_no_sub);
    }

    /**
     * 任意の売上伝票に紐付いている請求書番号の今年度最大値を取得
     * 売上伝票の指定がなければ全領収書から今年度最大値を取得
     * 今年度一度も発行されていなければ null を返す
     * <AUTHOR> Otake
     * @since 2017/10/04
     * @param type $db
     * @param type $uri_den_no
     * @param type $referenceMonth
     * @param type $referenceDay
     */
    public static function getMaxRyosyuNo($db, $uri_den_no, $referenceMonth = 4, $referenceDay = 1) {
        $keyHash = array();
        $uri_den_no && $keyHash['uri_den_no'] = $uri_den_no;
        list($whereStr, $param) = DataMapper_Utils::setWhere($keyHash, array(), 'ryosyusho_history');
        list($first_ts, $last_ts) = static::getTermThisFiscalYear($referenceMonth, $referenceDay);
        $sql = <<<END_OF_SQL
SELECT MAX(ryosyu_no) AS max_ryosyu_no
FROM ryosyusho_history
WHERE $whereStr
AND hako_date BETWEEN CAST('$first_ts' AS TIMESTAMP) AND CAST('$last_ts' AS TIMESTAMP)
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        return $select['max_ryosyu_no'];
    }

    /**
     * 今年度の年度始めと年度末を取得
     * <AUTHOR> Otake
     * @since 2017/09/26
     * @param type $referenceMonth
     * @param type $referenceDay
     * @return type
     */
    public static function getTermThisFiscalYear($referenceMonth = 4, $referenceDay = 1) {
        $zendDate = new Zend_Date();
        // 年度を取得するための計算
        $zendDate->sub($referenceMonth - 1, Zend_Date::MONTH)->sub($referenceDay - 1, Zend_Date::DAY);
        $fiscalYear = $zendDate->toString("yyyy");
        return static::getTerm($fiscalYear, $referenceMonth, $referenceDay);
    }

    /**
     * 年度始めと年度末の日付を取得する
     * <AUTHOR> Otake
     * @since 2017/09/26
     * @param int $fiscalYear 年度
     * @param int $referenceMonth 基準月
     * @param int $referenceDay 基準日
     * @return array
     */
    public static function getTerm($fiscalYear, $referenceMonth = 4, $referenceDay = 1) {
        $ts_format = "yyyy-MM-dd HH:mm:ss";
        $firstDate = new Zend_Date(array('year' => $fiscalYear, 'month' => $referenceMonth, 'day' => $referenceDay));
        $lastDate = clone $firstDate;
        $lastDate->add(1, Zend_Date::YEAR)->sub(1, Zend_Date::SECOND);
        $firstDay = $firstDate->toString($ts_format);
        $lastDay = $lastDate->toString($ts_format);
        return array($firstDay, $lastDay);
    }

    /**
     * 任意の売上伝票に紐付いている請求書番号の今年度最大値を取得
     * 今年度一度も発行されていなければ null
     * <AUTHOR> Otake
     * @since 2017/10/04
     * @param type $db
     * @param type $max_ryosyu_no
     * @param type $referenceMonth
     * @param type $referenceDay
     */
    public static function getNextRyosyuNoSub($db, $max_ryosyu_no, $referenceMonth = 4, $referenceDay = 1) {
        list($first_ts, $last_ts) = static::getTermThisFiscalYear($referenceMonth, $referenceDay);
        // レコードの数を次の枝番とすればよい
        $sql = <<<END_OF_SQL
SELECT COUNT(ryosyu_no) AS ryosyu_no_sub
FROM ryosyusho_history
WHERE hako_date BETWEEN CAST('$first_ts' AS TIMESTAMP) AND CAST('$last_ts' AS TIMESTAMP)
AND ryosyu_no = :max_ryosyu_no
END_OF_SQL;
        $select = $db->easySelOne($sql, array('max_ryosyu_no' => $max_ryosyu_no));
        return $select['ryosyu_no_sub'];
    }

        }
