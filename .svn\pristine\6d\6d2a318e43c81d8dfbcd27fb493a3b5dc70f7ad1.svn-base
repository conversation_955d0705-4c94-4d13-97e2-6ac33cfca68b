<?php

/**
 * DataMapper_FrontSyunou
 *
 * フロント収納 データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Sugiyama
 * @since      2020/03/23
 * @version    2025/04/xx Tosaka ベースよりコピー
 * @filesource 
 */

/**
 * フロント収納 データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Sugiyama
 * @since      2020/03/23
 */
class DataMapper_FrontSyunou extends DataMapper_Abstract {

    /**
     * 店舗データ 取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/03/03
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findTenpo($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.tenpo_cd ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
    SELECT 
         bm.bumon_cd    AS tenpo_cd
        ,bm.bumon_lnm   AS tenpo_nm
        ,bm.register_no AS regi_cd
        ,bm.char_free3  AS regi_nm
        ,bm.register_no AS kbn_value_cd_num
        ,bm.char_free3  AS kbn_value_lnm
    FROM bumon_mst bm
    WHERE bm.register_no IS NOT NULL
    AND bm.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 担当者データ 取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/03/03
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findTanto($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.bumon_cd, T.tanto_cd ';
        }
        $select = $db->easySelect(<<< END_OF_SQL
SELECT * FROM (
    SELECT 
        tm.tanto_cd,
        tm.tanto_nm AS kbn_value_lnm,
        tm.tanto_cd AS kbn_value_cd_num,
        szm.bumon_cd
    FROM 
        tanto_mst tm
    LEFT JOIN tanto_sozoku_mst szm
      ON szm.tanto_cd = tm.tanto_cd
     AND szm.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 商品区分データ 取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/03/03
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findShohinKbn($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.shohin_kbn ';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT * FROM (
        SELECT 
             sbm.dai_bunrui_cd
            ,skm.shohin_kbn_nm AS kbn_value_lnm
            ,skm.shohin_kbn    AS kbn_value_cd_num
            ,skm.shohin_kbn_nm
            ,skm.shohin_kbn
        FROM 
            shohin_kbn_mst skm
        LEFT JOIN shohin_bunrui_mst sbm
        ON sbm.shohin_kbn = skm.shohin_kbn
        AND sbm.delete_flg = 0
        GROUP BY 
             sbm.dai_bunrui_cd
            ,skm.shohin_kbn_nm
            ,skm.shohin_kbn
    ) T
     WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 商品データ 取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/03/03
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      string      $kijunYmd  基準日
     * @return     array      該当データがない場合はarray()を返す
     */
    public function findShohin($db, $keyHash = array(), $kijunYmd = null) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.bumon_cd, T.dai_bunrui_cd, T.chu_bunrui_cd, T.shohin_kbn, T.shohin_cd';
        }
        if (isset($kijunYmd)) {
            $tankaWhere = "'$kijunYmd' BETWEEN TO_CHAR(stm.tekiyo_st_date,'YYYY/MM/DD') AND TO_CHAR(stm.tekiyo_ed_date,'YYYY/MM/DD')";
            $zeiWhere = "'$kijunYmd' BETWEEN TO_CHAR(zm.tekiyo_st_date,'YYYY/MM/DD') AND TO_CHAR(zm.tekiyo_ed_date,'YYYY/MM/DD')";
            $shohinWhere = "AND '$kijunYmd' BETWEEN T.hanbai_st_ymd_ex AND T.hanbai_end_ymd_ex";
        } else {
            $tankaWhere = "CURRENT_DATE BETWEEN stm.tekiyo_st_date AND stm.tekiyo_ed_date";
            $zeiWhere = "CURRENT_TIMESTAMP BETWEEN zm.tekiyo_st_date AND zm.tekiyo_ed_date";
            $shohinWhere = "";
        }
        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
    SELECT 
         sbm.shohin_kbn
        ,skm.shohin_kbn_nm
        ,sbm.dai_bunrui_cd
        ,sbm.chu_bunrui_cd
        ,sbm.shohin_cd
        ,sm.shohin_nm
        ,sbm.bumon_cd
        ,COALESCE(stm.hanbai_tnk,0) AS uri_tnk
        ,COALESCE(stm.siire_tnk,0)  AS gen_tnk
        ,sm.shohin_cd               AS kbn_value_cd_num
        ,sm.shohin_nm || '(' || sm.shohin_cd || ')' AS kbn_value_lnm
        ,sm.tani_cd
        ,zm.zei_cd
        ,zm.zei_rtu
        ,zm.zei_hasu_kbn
        ,zm.reduced_tax_rate
        ,sm.uri_zei_kbn             AS zei_kbn
        ,sm.tnk_chg_kbn
        ,sm.nm_input_kbn
        ,sm.gentei_shohin_kbn
        ,sm.zaiko_knri_kbn
        ,TO_CHAR(sm.hanbai_st_ymd,'YYYY/MM/DD') AS hanbai_st_ymd_ex
        ,TO_CHAR(sm.hanbai_end_ymd,'YYYY/MM/DD') AS hanbai_end_ymd_ex
        ,siire.siire_cd
        ,siire.siire_lnm
    FROM 
        shohin_mst sm
    INNER JOIN shohin_bunrui_mst sbm
    ON sbm.shohin_cd   = sm.shohin_cd
    AND sbm.bumon_cd   = sm.bumon_cd
    AND sbm.delete_flg = 0
    LEFT JOIN shohin_tanka_mst stm
    ON stm.shohin_cd   = sm.shohin_cd
    AND stm.bumon_cd   = sm.bumon_cd
    AND stm.delete_flg = 0
    AND $tankaWhere
    LEFT JOIN shohin_kbn_mst skm
    ON skm.shohin_kbn  = sbm.shohin_kbn
    AND skm.delete_flg = 0
    LEFT JOIN zei_mst zm
    ON $zeiWhere
    AND zm.reduced_tax_rate = sm.uri_reduced_tax_rate
    AND zm.delete_flg  = 0
    LEFT JOIN siire_mst siire
    ON siire.siire_cd    = sm.siire_cd
    AND siire.delete_flg = 0
    ORDER BY sm.shohin_cd
) T
WHERE $whereStr
$shohinWhere
$orderBy
$tailClause
END_OF_SQL
                , $param
        );
        return $select;
    }

    /**
     * 式場データ取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/04/14
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public function findSikijou($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.bumon_cd';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT *
    FROM (
        SELECT 
            bm.bumon_cd   AS kbn_value_cd_num,
            bm.bumon_lnm  AS kbn_value_lnm,
            bm.* 
        FROM bumon_mst bm
    ) T
    WHERE $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 伝票データ取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/04/14
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public function findDenpyo($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.torihiki_ymd DESC, T.front_den_no DESC';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT *
    FROM (
        SELECT 
             TO_CHAR(fd.trn_date,'yyyy/mm/dd') AS torihiki_ymd
            ,fd.front_den_no
            ,ud.uri_den_no
            ,jd.denpyo_no
            ,fd.denpyo_kbn
            ,cnm_d.kbn_value_lnm AS denpyo_kbn_nm
            ,fd.bumon_cd
            ,bm.bumon_lnm
            ,fd.tanto_cd
            ,tm.tanto_nm
            ,fd.pay_kbn
            ,cnm_p.kbn_value_lnm AS pay_kbn_nm
            ,fd.register_no
            ,bm.char_free3  AS register_nm
            ,bm.char_free3
            ,TO_CHAR(fd.siharai_prc_sum, 'FM999,999,999') AS uri_prc_sum
            ,fd.siharai_prc_sum
            ,fd.azukari_prc
            ,fd.cupon_prc
            ,fd.nebiki_prc_sum AS nebiki_prc
            ,fd.cupon_kbn
            ,fd.change_prc
            ,TO_CHAR(COALESCE(fd.azukari_prc, 0) - COALESCE(fd.change_prc, 0) + COALESCE(fd.cupon_prc, 0) - COALESCE(fd.nebiki_prc_sum, 0), 'FM999,999,999') AS sum_prc
            ,COALESCE(fd.azukari_prc, 0) - COALESCE(fd.change_prc, 0) + COALESCE(fd.cupon_prc, 0) AS sum_prc2
            ,fd.aka_kuro_den_no
            ,fd.aka_kuro_kbn
        FROM 
            front_denpyo fd
        LEFT JOIN uriage_denpyo ud
          ON ud.front_den_no = fd.front_den_no
         AND ud.delete_flg   = 0
        LEFT JOIN juchu_denpyo jd
          ON jd.denpyo_no    = ud.denpyo_no
         AND jd.delete_flg   = 0
        LEFT JOIN tanto_mst tm
          ON tm.tanto_cd   = fd.tanto_cd
         AND tm.delete_flg = 0
        LEFT JOIN code_nm_mst cnm_p
          ON cnm_p.code_kbn         = '7874'
         AND cnm_p.kbn_value_cd_num = fd.pay_kbn
         AND cnm_p.delete_flg       = 0
        LEFT JOIN code_nm_mst cnm_d
          ON cnm_d.code_kbn         = '7868'
         AND cnm_d.kbn_value_cd_num = fd.denpyo_kbn
         AND cnm_d.delete_flg       = 0
        LEFT JOIN bumon_mst bm
          ON bm.bumon_cd   = fd.bumon_cd
         AND bm.delete_flg = 0
        WHERE fd.delete_flg = 0
          --AND fd.denpyo_kbn = 1
    ) T
    WHERE $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        foreach ($select as &$rec) {
            $dtl = static::findDenpyoMsi($db, $rec['front_den_no']);
            $rec['_dtl_'] = $dtl;
        }
        return $select;
    }

    /**
     * 
     * フロント販売明細
     * 
     * @param type $db
     * @param type $denpyo_no
     * @return type
     */
    public function findDenpyoMsi($db, $denpyo_no) {
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            fum.* 
           ,fum.uri_prc + fum.out_zei_prc AS uri_prc
           ,skm.shohin_kbn_nm
           --,sm.uri_zei_kbn          AS zei_kbn
           ,fum.zei_kbn
           ,fum.zei_cd
           ,zm.zei_rtu
           ,sm.uri_reduced_tax_rate AS reduced_tax_rate
           ,sm.*
           --,zm.*
           ,COALESCE(stm.siire_tnk,0)  AS gen_tnk
           ,siire.siire_cd
           ,siire.siire_lnm
        FROM front_uriage_msi fum
        INNER JOIN front_denpyo fd
          ON fd.front_den_no = fum.front_den_no
         AND fd.delete_flg   = 0
        LEFT JOIN shohin_mst sm
          ON sm.shohin_cd    = fum.shohin_cd
         AND sm.bumon_cd     = fum.shohin_bumon_cd
         AND sm.delete_flg   = 0
        LEFT JOIN shohin_tanka_mst stm
          ON stm.shohin_cd   = sm.shohin_cd
         AND stm.bumon_cd    = sm.bumon_cd
         AND stm.delete_flg  = 0
         AND CURRENT_DATE BETWEEN stm.tekiyo_st_date AND stm.tekiyo_ed_date
        LEFT JOIN shohin_kbn_mst skm
          ON skm.shohin_kbn  = fum.shohin_kbn
         AND skm.delete_flg  = 0
        LEFT JOIN zei_mst zm
          ON fd.trn_date BETWEEN zm.tekiyo_st_date AND zm.tekiyo_ed_date
         AND zm.reduced_tax_rate = sm.uri_reduced_tax_rate
         AND zm.delete_flg = 0
        LEFT JOIN siire_mst siire
          ON siire.siire_cd   = sm.siire_cd
         AND siire.delete_flg = 0
        WHERE fum.front_den_no = :front_den_no;
END_OF_SQL
                , array('front_den_no' => $denpyo_no));
        return $select;
    }

    /**
     * 伝票データ取得（フロント入出金伝票明細）
     *
     * <AUTHOR> Sugiyama
     * @since      2020/04/14
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public function findTrnDenpyo($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.torihiki_ymd DESC, T.front_den_no DESC';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT *
    FROM (
        SELECT 
             TO_CHAR(fd.trn_date,'yyyy/mm/dd') AS torihiki_ymd
            ,TO_CHAR(fd.trn_time,'HH24:MI')    AS torihiki_time
            ,ftm.torihiki_kbn_cd               AS torihiki_kbn
            ,CASE WHEN tkm.torihiki_kbn_nm IS NULL AND fd.denpyo_kbn = 1 THEN 'フロント売上'
                  ELSE tkm.torihiki_kbn_nm
             END                               AS torihiki_kbn_nm
            ,fd.nyukin_prc_sum                 AS nyukin_prc
            ,fd.syukkin_prc_sum                AS syukkin_prc
            ,ftm.msi_no
            ,tm.tanto_nm
            ,fd.*
        FROM 
            front_denpyo fd
        LEFT JOIN front_trn_msi ftm
          ON fd.front_den_no = ftm.front_den_no
         AND ftm.delete_flg = 0
        LEFT JOIN torihiki_kbn_mst tkm
          ON tkm.torihiki_kbn_cd = ftm.torihiki_kbn_cd
         AND tkm.delete_flg = 0
        LEFT JOIN tanto_mst tm
          ON tm.tanto_cd = fd.tanto_cd
         AND tm.delete_flg = 0
        WHERE fd.delete_flg  = 0
         --AND fd.denpyo_kbn = 6
    ) T
    WHERE $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        //foreach ($select as &$rec) {
        //    $dtl = static::findTrnDenpyoMsi($db, $rec['front_den_no']);
        //    $rec['_dtl_'] = $dtl;
        //}
        return $select;
    }

    /**
     * 
     * フロント入出金伝票明細
     * 
     * @param type $db
     * @param type $denpyo_no
     * @return type
     */
    public function findTrnDenpyoMsi($db, $denpyo_no) {
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            ftm.* 
        FROM front_trn_msi ftm
        INNER JOIN front_denpyo fd
          ON fd.front_den_no = ftm.front_den_no
         AND fd.delete_flg   = 0
        WHERE ftm.front_den_no = :front_den_no;
END_OF_SQL
                , array('front_den_no' => $denpyo_no));
        return $select;
    }

    /**
     * 仮払伝票取得
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findKaribaraiDenpyo($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.front_den_no DESC, T.msi_no';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT *
    FROM (
        SELECT 
             TO_CHAR(fd.trn_date,'yyyy/mm/dd') AS torihiki_ymd
            ,TO_CHAR(fd.trn_time,'HH24:MI')    AS torihiki_time
            ,ftm.torihiki_kbn_cd               AS torihiki_kbn
            ,tkm.torihiki_kbn_nm               AS torihiki_kbn_nm
            ,fd.nyukin_prc_sum                 AS nyukin_prc
            ,fd.syukkin_prc_sum                AS syukkin_prc
            ,ftm.msi_no
            ,tm.tanto_nm
            ,ftm.karibarai_tanto_cd
            ,tm2.tanto_nm                      AS s_tanto_nm
            ,shikijo.bumon_lnm                 AS shikijo
            ,shikijo.bumon_cd                  AS shikijo_cd
            ,bumon.bumon_lnm
            ,bumon.bumon_cd                    AS kari_bumon_cd
            ,cnm.kbn_value_lnm                 AS karibarai_kbn_nm
            ,ftm.karibarai_kbn
            ,ftm.karibarai_status
            ,fd.bumon_cd                       AS kaisya_cd
            ,fd.*
            ,TO_CHAR(fd.syukkin_prc_sum, 'FM999,999,999') AS syukkin_prc_disp
            ,TO_CHAR(ftm.seisan_yotei_date,'yyyy/mm/dd')  AS seisan_yotei_date
            ,fd.syukkin_prc_sum                AS kari_zan
            ,fd.seko_no
            ,(SELECT ftm2.shinsei_den_no FROM front_trn_msi ftm2 
              WHERE ftm2.shinsei_den_no = ftm.front_den_no AND ftm2.delete_flg = 0
              LIMIT 1)                         AS shinsei_den_no
        FROM 
            front_denpyo fd
        LEFT JOIN front_trn_msi ftm
          ON fd.front_den_no = ftm.front_den_no
         AND ftm.delete_flg = 0
        LEFT JOIN torihiki_kbn_mst tkm
          ON tkm.torihiki_kbn_cd = ftm.torihiki_kbn_cd
         AND tkm.delete_flg = 0
        LEFT JOIN tanto_mst tm
          ON tm.tanto_cd = ftm.karibarai_tanto_cd
         AND tm.delete_flg = 0
        LEFT JOIN tanto_mst tm2
          ON tm2.tanto_cd = fd.tanto_cd
         AND tm2.delete_flg = 0
        LEFT JOIN bumon_mst shikijo
          ON shikijo.bumon_cd   = fd.bumon_cd
         AND shikijo.delete_flg = 0
        LEFT JOIN bumon_mst bumon
          ON bumon.bumon_cd   = ftm.bumon_cd
         AND bumon.delete_flg = 0
        LEFT JOIN code_nm_mst cnm
          ON cnm.code_kbn         = '7877'
         AND cnm.kbn_value_cd_num = ftm.karibarai_status
         AND cnm.delete_flg       = 0
        WHERE fd.delete_flg  = 0
          AND ftm.karibarai_kbn = 1
    ) T
    WHERE $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        foreach ($select as &$rec) {
            $dtl = static::findKaribaraiDenpyoMsi($db, $rec['front_den_no']);
            $rec['_dtl_'] = $dtl;
        }
        return $select;
    }

    /**
     * 
     * 仮払伝票明細
     * 
     * @param type $db
     * @param type $denpyo_no
     * @return type
     */
    public function findKaribaraiDenpyoMsi($db, $denpyo_no) {
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
             ftm.front_den_no
            ,ftm.msi_no
            ,ftm.nyukin_den_no
            --,ftm.torihiki_kbn_cd
            ,rpad('2000002', 10, ' ')                       AS torihiki_kbn_cd -- 仮払精算
            ,2                                              AS karibarai_kbn
            ,ftm.karibarai_status
            ,ftm.bumon_cd
            ,ftm.maisu
            ,1                                              AS dc_kbn
            --,ftm.nyukin_prc
            ,ftm.syukkin_prc                                AS nyukin_prc
            ,NULL                                           AS syukkin_prc
            ,ftm.tekiyo
            ,ftm.karibarai_tanto_cd
            ,tm.tanto_nm                                    AS karibarai_tanto_nm
            ,TO_CHAR(ftm.seisan_yotei_date,'yyyy/mm/dd')    AS seisan_yotei_date
            ,km.zei_cd_inp_kbn
            ,ftm.zei_cd
            ,ftm.zei_kbn
        FROM front_trn_msi ftm
        INNER JOIN front_denpyo fd
          ON fd.front_den_no = ftm.front_den_no
         AND fd.delete_flg   = 0
        LEFT JOIN tanto_mst tm
          ON tm.tanto_cd   = ftm.karibarai_tanto_cd
         AND tm.delete_flg = 0
        LEFT JOIN torihiki_kbn_mst tkm
          ON tkm.torihiki_kbn_cd = '2000002'
	 AND tkm.delete_flg = 0
        LEFT JOIN kamoku_mst km
          ON km.kamoku_cd  = tkm.kamoku_cd
         AND km.delete_flg = 0
        WHERE ftm.front_den_no = :front_den_no;
END_OF_SQL
                , array('front_den_no' => $denpyo_no));
        return $select;
    }

    /**
     * 伝票データ取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/04/14
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public function findNyusyukinDenpyo($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.torihiki_ymd DESC, T.front_den_no DESC';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT *
    FROM (
        SELECT 
             TO_CHAR(fd.trn_date,'yyyy/mm/dd') AS torihiki_ymd
            ,fd.front_den_no
            ,fd.front_den_no AS denpyo_no
            ,ud.uri_den_no
            ,fd.denpyo_kbn
            ,cnm_d.kbn_value_lnm AS denpyo_kbn_nm
            ,fd.bumon_cd
            ,bm.bumon_lnm
            ,fd.tanto_cd
            ,tm.tanto_nm
            ,fd.pay_kbn
            ,cnm_p.kbn_value_lnm AS pay_kbn_nm
            ,fd.register_no
            ,bm.char_free3  AS register_nm
            ,bm.char_free3
            ,TO_CHAR(fd.siharai_prc_sum, 'FM999,999,999') AS uri_prc_sum
            ,fd.siharai_prc_sum
            ,fd.azukari_prc
            ,fd.cupon_prc
            ,fd.cupon_kbn
            ,fd.change_prc
            ,TO_CHAR(COALESCE(fd.azukari_prc, 0) - COALESCE(fd.change_prc, 0) + COALESCE(fd.cupon_prc, 0), 'FM999,999,999') AS sum_prc
            ,COALESCE(fd.azukari_prc, 0) - COALESCE(fd.change_prc, 0) + COALESCE(fd.cupon_prc, 0) AS sum_prc2
            ,fd.aka_kuro_den_no
            ,fd.aka_kuro_kbn
            ,TO_CHAR(fd.nyukin_prc_sum , 'FM999,999,999') AS nyukin_prc_sum
            ,TO_CHAR(fd.syukkin_prc_sum, 'FM999,999,999') AS syukkin_prc_sum
            ,array_to_string(ARRAY(SELECT tekiyo FROM front_trn_msi WHERE front_den_no = fd.front_den_no ORDER BY msi_no),',') AS tekiyo
        FROM 
            front_denpyo fd
        INNER JOIN front_trn_msi ftm
          ON ftm.front_den_no  = fd.front_den_no
         AND ftm.karibarai_kbn = 0
         AND ftm.delete_flg    = 0
        LEFT JOIN uriage_denpyo ud
          ON ud.front_den_no = fd.front_den_no
         AND ud.delete_flg   = 0
        LEFT JOIN juchu_denpyo jd
          ON jd.denpyo_no    = ud.denpyo_no
         AND jd.delete_flg   = 0
        LEFT JOIN tanto_mst tm
          ON tm.tanto_cd   = fd.tanto_cd
         AND tm.delete_flg = 0
        LEFT JOIN code_nm_mst cnm_p
          ON cnm_p.code_kbn         = '7874'
         AND cnm_p.kbn_value_cd_num = fd.pay_kbn
         AND cnm_p.delete_flg       = 0
        LEFT JOIN code_nm_mst cnm_d
          ON cnm_d.code_kbn         = '7868'
         AND cnm_d.kbn_value_cd_num = fd.denpyo_kbn
         AND cnm_d.delete_flg       = 0
        LEFT JOIN bumon_mst bm
          ON bm.bumon_cd   = fd.bumon_cd
         AND bm.delete_flg = 0
        WHERE fd.delete_flg = 0
    ) T
    WHERE $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        foreach ($select as &$rec) {
            $dtl = static::findNyusyukinDenpyoMsi($db, $rec['front_den_no']);
            $rec['_dtl_'] = $dtl;
        }
        return $select;
    }

    /**
     * 
     * 仮払伝票明細
     * 
     * @param type $db
     * @param type $denpyo_no
     * @return type
     */
    public function findNyusyukinDenpyoMsi($db, $denpyo_no) {
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
             ftm.front_den_no
            ,ftm.msi_no
            ,ftm.nyukin_den_no
            ,ftm.torihiki_kbn_cd
            --,rpad('2000002', 10, ' ')                       AS torihiki_kbn_cd -- 仮払精算
            --,2                                              AS karibarai_kbn
            ,ftm.karibarai_kbn
            ,ftm.karibarai_status
            ,ftm.bumon_cd
            ,ftm.maisu
            ,ftm.dc_kbn
            ,ftm.nyukin_prc
            ,ftm.syukkin_prc
            ,ftm.tekiyo
            ,ftm.karibarai_tanto_cd
            ,tm.tanto_nm                                    AS karibarai_tanto_nm
            ,TO_CHAR(ftm.seisan_yotei_date,'yyyy/mm/dd')    AS seisan_yotei_date
            ,ftm.zei_cd
            ,ftm.zei_kbn
            ,km.zei_cd_inp_kbn
            ,ftm.invoice_kbn
        FROM front_trn_msi ftm
        INNER JOIN front_denpyo fd
          ON fd.front_den_no = ftm.front_den_no
         AND fd.delete_flg   = 0
        LEFT JOIN tanto_mst tm
          ON tm.tanto_cd   = ftm.karibarai_tanto_cd
         AND tm.delete_flg = 0
        LEFT JOIN torihiki_kbn_mst tkm
          ON tkm.torihiki_kbn_cd = ftm.torihiki_kbn_cd
	 AND tkm.delete_flg = 0
        LEFT JOIN kamoku_mst km
          ON km.kamoku_cd  = tkm.kamoku_cd
         AND km.delete_flg = 0
        WHERE ftm.front_den_no = :front_den_no;
END_OF_SQL
                , array('front_den_no' => $denpyo_no));
        return $select;
    }

    /**
     * 
     * フロント入出金伝票明細
     * 
     * @param type $db
     * @param type $denpyo_no
     * @return type
     */
    public function findTrnDenpyoMsi2($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.front_den_no DESC, T.msi_no';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT *
    FROM (
        SELECT 
            ftm.* 
        FROM front_trn_msi ftm
        INNER JOIN front_denpyo fd
          ON fd.front_den_no = ftm.front_den_no
         AND fd.delete_flg   = 0
    ) T
    WHERE $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 請求先一覧データ取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/04/14
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public function findSeikyu($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_no, T.uri_den_no';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT 
         * 
        ,CASE WHEN T.record = 1 THEN 
            CASE T.sd_bun_gas_kbn_num 
                WHEN 2 THEN 
                    (SELECT ftm.front_den_no FROM front_trn_msi ftm 
                     WHERE ftm.seikyu_den_no = T.sd_seikyu_den_no AND ftm.uri_den_no = T.sd2_uri_den_no AND delete_flg = 0 
                     ORDER BY ftm.front_den_no DESC LIMIT 1)
                ELSE 
                    (SELECT ftm.front_den_no FROM front_trn_msi ftm 
                     WHERE ftm.seikyu_den_no = T.sd_seikyu_den_no AND ftm.uri_den_no = T.sd_uri_den_no AND delete_flg = 0 
                     ORDER BY ftm.front_den_no DESC LIMIT 1)
            END 
         ELSE T.front_den_no 
        END                     AS front_den_no
    FROM (
        SELECT 
             *
            ,CASE 
                WHEN zan_prc = seikyu_prc  THEN     --'未決済'
                        (SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn = '7941' AND kbn_value_cd = '1' LIMIT 1)
                WHEN zan_prc = 0           THEN     --'決済終了'
                        (SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn = '7941' AND kbn_value_cd = '4' LIMIT 1)
                WHEN zan_prc <> seikyu_prc THEN     --'一部決済'
                        (SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn = '7941' AND kbn_value_cd = '3' LIMIT 1)
                ELSE ''
             END                                    AS kesan_kbn
            ,CASE 
                WHEN zan_prc = seikyu_prc  THEN 1 --'未決済'
                WHEN zan_prc = 0           THEN 4 --'決済終了'
                WHEN zan_prc <> seikyu_prc THEN 3 --'一部決済'
                ELSE null
             END                                    AS kesan_kbn_cd
            ,CASE 
               WHEN LENGTH(nafuda_nm) = 0 THEN 1
               WHEN nafuda_nm IS NULL     THEN 1
               ELSE 0
             END                                    AS disp_order
            ,zan_prc AS org_zan_prc
        FROM (
            -- 請求伝票
            SELECT 
                 1                  AS record
                ,sd.seko_no
                ,sd.bun_gas_kbn_num AS sd_bun_gas_kbn_num
                ,sd.seikyu_den_no   AS sd_seikyu_den_no
                ,sd.uri_den_no      AS sd_uri_den_no
                ,sd2.uri_den_no     AS sd2_uri_den_no
                ,CASE sd.bun_gas_kbn_num 
                    WHEN 2 THEN sd2.uri_den_no
                    ELSE sd.uri_den_no
                 END                                    AS uri_den_no
                ,sd.seikyu_den_no   AS seikyu_no
                ,NULL AS front_den_no
                ,seko_kbn.kbn_value_lnm AS seko_kbn
                ,seko_kbn.kbn_value_cd  AS s_seko_kbn
                ,sd.data_kbn
                ,ski.souke_nm
                --,sd.sekyu_nm
                ,CONCAT(seikyu.sekyu_nm1,seikyu.sekyu_nm2) AS sekyu_nm
                ,(SELECT 
                    TRIM('/' FROM COALESCE(udm.nafuda_nm  || '/', '') ||
                                  COALESCE(udm.nafuda_nm2 || '/', '') || 
                                  COALESCE(udm.nafuda_nm3 || '/', '') || 
                                  COALESCE(udm.nafuda_nm4 || '/', '') ||
                                  COALESCE(udm.nafuda_nm5 || '/', '') || 
                                  COALESCE(udm.nafuda_nm6 || '/', '') || 
                                  COALESCE(udm.nafuda_nm7 || '/', '') || 
                                  COALESCE(udm.nafuda_nm8 || '/', ''))
                 FROM uriage_denpyo_msi udm 
                 WHERE udm.uri_den_no = sd.uri_den_no
                 LIMIT 1
                )                  AS nafuda_nm
                ,cnm.kbn_value_lnm AS status
                ,sd.status_kbn
                ,sd.uri_prc_sum
                + sd.uri_nebk_sum	  
                + sd.uri_hepn_sum
                + sd.hoshi_prc_sum
                + sd.out_zei_prc 
                - sd.sougi_keiyaku_prc
                + (sd.sougi_keiyaku_prc + sd.sougi_harai_prc + sd.sougi_wari_prc)
                - sd.sougi_zei_sagaku_prc
                + sd.sougi_meigi_chg_cost + sd.sougi_meigi_chg_cost_zei
                + sd.sougi_early_use_cost + sd.sougi_early_use_cost_zei
                + sd.etc_harai_prc AS seikyu_prc
                ,TO_CHAR(ski.sougi_ymd,'yyyy/mm/dd')     AS seko_ymd
                ,NULL                  AS shiharai_prc
                ,COALESCE(sd.seikyu_zan,0)   AS zan_prc
                ,NULL AS cupon_prc
                ,sd.juchusaki_kbn
                ,sd.pay_method_cd
                ,CASE WHEN sd.juchusaki_kbn = 2 AND sd.pay_method_cd = '70' THEN 1
                      ELSE 0
                 END juchusaki_kbn_tmp
                ,sd._mod_ts
                ,ski.k_nm
            FROM seikyu_denpyo sd
            LEFT JOIN seko_kihon_info ski
            ON ski.seko_no     = sd.seko_no
            AND ski.delete_flg = 0
            LEFT JOIN uriage_denpyo ud
            ON ud.uri_den_no  = sd.uri_den_no
            AND ud.delete_flg = 0
            LEFT JOIN code_nm_mst cnm
            ON  cnm.code_kbn = '0610'
            AND cnm.kbn_value_cd_num = sd.status_kbn
            AND cnm.delete_flg = 0
            LEFT JOIN code_nm_mst seko_kbn
            ON  seko_kbn.code_kbn = '7804'
            AND seko_kbn.kbn_value_cd_num = sd.data_kbn
            AND seko_kbn.delete_flg = 0
            LEFT JOIN seikyu_sekyu_saki_info seikyu
            ON  seikyu.seikyu_den_no = sd.seikyu_den_no
            AND seikyu.seko_no       = sd.seko_no
            AND seikyu.delete_flg    = 0
            LEFT JOIN seikyu_denpyo sd2
            ON  sd2.seikyu_den_no = sd.bun_gas_seikyu_den_no
            AND sd2.delete_flg    = 0
            WHERE sd.delete_flg = 0	
            -- データ区分
            AND CASE WHEN sd.data_kbn IN (1, 2) THEN  -- データ区分：葬儀、法事
                          (CASE WHEN ski.moushi_kbn  IN (1, 2, 7, 8, 9, 10) THEN -- 申込区分：葬儀、法事、貸式場、エンバー依頼、搬送依頼、その他施行
                                     (CASE WHEN sd.status_kbn IN (4,5) THEN -- ステータス 4:請求確定
                                                (CASE WHEN sd.seikyu_approval_status = 1 THEN -- 請求書承認状況 1:承認済
                                                           (CASE WHEN sd.bun_gas_kbn_num IN (0, 2, 20) THEN TRUE
                                                                 ELSE FALSE
                                                            END)
                                                      ELSE FALSE
                                                 END)
                                           ELSE FALSE
                                      END) 
                                ELSE FALSE
                           END)
                     WHEN sd.data_kbn IN (4) THEN  -- データ区分：別注
                          (CASE WHEN sd.juchusaki_kbn = 2 THEN -- 別注品は喪家外のみ
                                     (CASE WHEN sd.status_kbn IN (4) THEN -- ステータス 4:請求確定
                                                (CASE WHEN sd.seikyu_approval_status = 1 THEN -- 請求書承認状況 1:承認済
                                                           (CASE WHEN sd.bun_gas_kbn_num IN (0, 2, 20) THEN TRUE
                                                                 ELSE FALSE
                                                            END)
                                                      ELSE FALSE
                                                 END)
                                           WHEN sd.status_kbn IN(3) THEN -- ステータス 3:施行金額確定済
                                                (CASE WHEN sd.bun_gas_kbn_num IN (0) THEN TRUE
                                                      ELSE FALSE
                                                 END)
                                           WHEN sd.status_kbn IN(5) THEN -- ステータス 5
                                                (CASE WHEN sd.seikyu_approval_status = 1 THEN -- 請求書承認状況 1:承認済
                                                           (CASE WHEN sd.bun_gas_kbn_num IN (0, 2, 20) THEN TRUE
                                                                 ELSE FALSE
                                                            END)
                                                      WHEN sd.bun_gas_kbn_num IN (0) THEN TRUE
                                                      ELSE FALSE
                                                 END)
                                           ELSE FALSE
                                      END)
                                ELSE FALSE
                           END)
                     WHEN sd.data_kbn IN (3) THEN  -- データ区分：単品
                          (CASE WHEN sd.status_kbn IN (4) THEN -- ステータス 4:請求確定
                                     (CASE WHEN sd.seikyu_approval_status = 1 THEN -- 請求書承認状況 1:承認済
                                                (CASE WHEN sd.bun_gas_kbn_num IN (0, 2, 20) THEN TRUE
                                                      ELSE FALSE
                                                 END)
                                           ELSE FALSE
                                      END)
                                WHEN sd.status_kbn IN(3) THEN -- ステータス 3:施行金額確定済
                                     (CASE WHEN sd.seikyu_approval_status = 0 THEN -- 請求書承認状況 0:未承認
                                                (CASE WHEN sd.bun_gas_kbn_num IN (0) THEN TRUE
                                                      ELSE FALSE
                                                 END)
                                           ELSE FALSE
                                      END)
                                WHEN sd.status_kbn IN (5) THEN -- ステータス 5
                                     (CASE WHEN sd.seikyu_approval_status = 1 THEN -- 請求書承認状況 1:承認済
                                                (CASE WHEN sd.bun_gas_kbn_num IN (0, 2, 20) THEN TRUE
                                                      ELSE FALSE
                                                 END)
                                           WHEN sd.seikyu_approval_status = 0 THEN -- 請求書承認状況 0:未承認
                                                (CASE WHEN sd.bun_gas_kbn_num IN (0) THEN TRUE
                                                      ELSE FALSE
                                                 END)
                                           ELSE FALSE
                                      END)
                                ELSE FALSE
                           END)
                     ELSE FALSE
                END
            -- 入金状況 画面の条件で見るようにする
            -- AND COALESCE(sd.nyukin_status,0) = 0
            UNION 
            -- 売上伝票
            SELECT 
                 2      AS record
                ,ud.seko_no    
                ,NULL   AS sd_bun_gas_kbn_num
                ,NULL   AS sd_seikyu_den_no
                ,NULL   AS sd_uri_den_no
                ,NULL   AS sd2_uri_den_no 
                ,ud.uri_den_no
                ,NULL   AS seikyu_no
                ,ud.front_den_no
                ,seko_kbn.kbn_value_lnm AS seko_kbn
                ,seko_kbn.kbn_value_cd  AS s_seko_kbn
                ,ud.data_kbn
                ,ski.souke_nm
                ,ud.sekyu_nm
                ,(SELECT 
                    TRIM('/' FROM COALESCE(udm.nafuda_nm  || '/', '') ||
                    COALESCE(udm.nafuda_nm2 || '/', '') || 
                    COALESCE(udm.nafuda_nm3 || '/', '') || 
                    COALESCE(udm.nafuda_nm4 || '/', '') ||
                    COALESCE(udm.nafuda_nm5 || '/', '') || 
                    COALESCE(udm.nafuda_nm6 || '/', '') || 
                    COALESCE(udm.nafuda_nm7 || '/', '') || 
                    COALESCE(udm.nafuda_nm8 || '/', ''))
                 FROM uriage_denpyo_msi udm 
                 WHERE udm.uri_den_no = ud.uri_den_no
                 LIMIT 1
                )                  AS nafuda_nm
                ,cnm.kbn_value_lnm AS status
                ,ud.status_kbn
                ,ud.uri_prc_sum
                + ud.uri_nebk_sum	  
                + ud.uri_hepn_sum
                + ud.hoshi_prc_sum
                + ud.out_zei_prc 
                - ud.sougi_keiyaku_prc
                + (ud.sougi_keiyaku_prc + ud.sougi_harai_prc + ud.sougi_wari_prc)
                - ud.sougi_zei_sagaku_prc
                + ud.sougi_meigi_chg_cost + ud.sougi_meigi_chg_cost_zei
                + ud.sougi_early_use_cost + ud.sougi_early_use_cost_zei
                + ud.etc_harai_prc AS seikyu_prc
                ,TO_CHAR(ski.sougi_ymd,'yyyy/mm/dd')     AS seko_ymd
                ,NULL  AS shiharai_prc
                ,COALESCE(ud.seikyu_zan, 0)              AS zan_prc
                ,NULL                                    AS cupon_prc
                ,ud.juchusaki_kbn
                ,ud.pay_method_cd
                ,CASE WHEN ud.juchusaki_kbn = 2 AND ud.pay_method_cd = '70' THEN 1
                      ELSE 0
                 END juchusaki_kbn_tmp
                ,ud._mod_ts
                ,ski.k_nm
            FROM uriage_denpyo ud
            INNER JOIN seko_kihon_info ski
            ON ski.seko_no     = ud.seko_no
            AND ski.delete_flg = 0
            LEFT JOIN seikyu_denpyo sd
            ON sd.uri_den_no  = ud.uri_den_no
            AND sd.delete_flg = 0
            LEFT JOIN code_nm_mst cnm
            ON  cnm.code_kbn = '0610'
            AND cnm.kbn_value_cd_num = ud.status_kbn
            AND cnm.delete_flg = 0
            LEFT JOIN code_nm_mst seko_kbn
            ON  seko_kbn.code_kbn = '7804'
            AND seko_kbn.kbn_value_cd_num = ud.data_kbn
            AND seko_kbn.delete_flg = 0
            WHERE ud.delete_flg = 0
            AND sd.seikyu_den_no IS NULL
            AND ud.data_kbn     IN ('1','2')            -- データ区分      ：葬儀、法事
            AND ski.moushi_kbn  IN (1, 2, 7, 8, 9, 10)  -- 申込区分        ：葬儀、法事、貸式場、エンバー依頼、搬送依頼、その他施行
            AND ud.status_kbn   = 2                     -- ステータス      ：2:見積確定済
            AND ud.seko_prc_kakute_kbn = 0              -- 施行金額確定区分：0:未確定
        ) T2
    ) T
    WHERE $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * レジ番号データ取得
     *
     * <AUTHOR> Sugiyama
     * @since      2020/04/14
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public function findRegi($db, $keyHash = array()) {
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT 
         bm.register_no AS kbn_value_cd_num
        ,bm.char_free3  AS kbn_value_lnm
    FROM bumon_mst bm
    WHERE bm.register_no IS NOT NULL
    AND bm.delete_flg = 0
END_OF_SQL
        );
        return $select;
    }

    /**
     * 部門番号データ取得
     *
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public function findBumon($db, $keyHash = array()) {
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT 
        bm.bumon_cd  AS kbn_value_cd_num,
        bm.bumon_lnm AS kbn_value_lnm
    FROM bumon_mst bm
END_OF_SQL
        );
        return $select;
    }

    /**
     * 
     * 取引区分取得
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findTorihiki($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.torihiki_kbn_cd';
        }
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT * FROM (
            SELECT 
                 tkm.*
                ,tkm.torihiki_kbn_cd AS kbn_value_cd_num
                ,tkm.torihiki_kbn_nm AS kbn_value_lnm
                ,km.zei_cd_inp_kbn
            FROM torihiki_kbn_mst tkm
            LEFT JOIN kamoku_mst km
              ON km.kamoku_cd  = tkm.kamoku_cd
             AND km.delete_flg = 0
            WHERE tkm.delete_flg = 0
            ORDER BY tkm.torihiki_kbn_cd
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
        );
        return $select;
    }

    /**
     * 
     * 取引区分取得
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findTorihikiDelete($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.torihiki_kbn_cd';
        }
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT * FROM (
            SELECT 
                 tkm.*
                ,tkm.torihiki_kbn_cd AS kbn_value_cd_num
                ,tkm.torihiki_kbn_nm AS kbn_value_lnm
                ,km.zei_cd_inp_kbn
            FROM torihiki_kbn_mst tkm
            LEFT JOIN kamoku_mst km
              ON km.kamoku_cd  = tkm.kamoku_cd
             AND km.delete_flg = 0
            ORDER BY tkm.torihiki_kbn_cd
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
        );
        return $select;
    }

    /**
     * 
     * 施行情報取得
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findSekoDlg($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no';
        }
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            DISTINCT 
            *
        FROM (
            SELECT 
                ski.seko_no,                                        -- 施行番号
                ski.souke_nm,                                       -- 喪家名
                ski.k_nm,                                           -- 故人名
                ski.k_knm,                                          -- 故人名カナ
                ski.m_nm,                                           -- 喪主名
                ski.m_knm,                                          -- 喪主名カナ
                TO_CHAR(ski.sougi_ymd,'yyyy/mm/dd') AS sougi_ymd,   -- 葬儀日
                ski.moushi_kbn,                                     -- 申込区分
                cnm1.kbn_value_lnm  AS moushi_kbn_nm,               -- 申込区分名
                --'決済状況' AS kesan,                                -- 決済状況
                CASE WHEN sd.seikyu_den_no IS NULL THEN 
                    CASE WHEN ud.uri_den_no IS NULL THEN '未決済' ELSE
                        (CASE  
                            WHEN COALESCE(ud.seikyu_zan,0) = COALESCE(ud.uchikin_prc,0) + COALESCE(ud.cupon_prc,0)
                                THEN '決済終了'
                            WHEN COALESCE(ud.uchikin_prc,0) + COALESCE(ud.cupon_prc,0) = 0
                                THEN '未決済'
                            WHEN COALESCE(ud.seikyu_zan,0) <> COALESCE(ud.uchikin_prc,0) + COALESCE(ud.cupon_prc,0)
                                THEN '一部決済'
                            ELSE '未決済'
                        END)
                    END 
                     WHEN sd.data_kbn IN ('1', '2', '4') THEN 
                        (CASE  
                            WHEN COALESCE(ud.seikyu_zan,0) = (SELECT COALESCE(SUM(nd.nyukin_prc),0) FROM nyukin_denpyo nd WHERE nd.seikyu_no = sd.seikyu_den_no AND nd.delete_flg = 0)
                                THEN '決済終了'
                            WHEN 0 = (SELECT COALESCE(SUM(nd.nyukin_prc),0) FROM nyukin_denpyo nd WHERE nd.seikyu_no = sd.seikyu_den_no AND nd.delete_flg = 0)
                                THEN '未決済'
                            WHEN COALESCE(ud.seikyu_zan,0) <> (SELECT COALESCE(SUM(nd.nyukin_prc),0) FROM nyukin_denpyo nd WHERE nd.seikyu_no = sd.seikyu_den_no AND nd.delete_flg = 0)
                                THEN '一部決済'
                            ELSE '未決済'
                        END)
                     WHEN sd.data_kbn IN ('3') THEN -- 3は無いが念の為の条件
                        (CASE  
                            WHEN COALESCE(sd.seikyu_zan,0) = COALESCE(sd.uchikin_prc,0) + COALESCE(sd.cupon_prc,0)
                                THEN '決済終了'
                            WHEN COALESCE(sd.uchikin_prc,0) + COALESCE(sd.cupon_prc,0) = 0
                                THEN '未決済'
                            WHEN COALESCE(sd.seikyu_zan,0) <> COALESCE(sd.uchikin_prc,0) + COALESCE(sd.cupon_prc,0)
                                THEN '一部決済'
                            ELSE '未決済'
                        END)			
                     ELSE '未決済'
                END                 AS kesan,
                ski.uketuke_tanto_cd,                               -- 受付担当者コード
                tm1.tanto_nm        AS uketuke_tanto_nm,            -- 受付担当者名
                ski.seko_tanto_cd,                                  -- 施行担当者コード
                tm2.tanto_nm        AS seko_tanto_nm,               -- 施行担当者名
                ski.kaiin_kbn,                                      -- 会員区分
                cnm2.kbn_value_lnm  AS kaiin_kbn_nm,                -- 会員区分名
                cnm3.kbn_value_lnm  AS status_kbn,                  -- ステータス
                ssi.tel,                                            -- 請求先TEL
                ssi.sekyu_nm,                                       -- 請求先名
                ssi.addr1,                                          -- 請求先住所1
                ssi.addr2,                                          -- 請求先住所2
                ssi.addr1 || ssi.addr2 AS addr,                     -- 請求先住所
                ski.bumon_cd,                                       -- 部門コード
                sn1.basho_nm,                                       -- 式場名
                ski.est_shikijo_cd,                                 -- 式場コード
                TO_CHAR(sn2.nitei_ymd,'yyyy/mm/dd') AS shibou_ymd,  -- 死亡日
                --jd.juchu_prc_sum,                                   
                --ud.uri_prc_sum,
                jd.denpyo_no,
                ud.uri_den_no,
                TO_CHAR((
                        jd.juchu_prc_sum 
                        + jd.juchu_hepn_sum 
                        + jd.juchu_nebk_sum 
                        + jd.hoshi_prc_sum 
                        + jd.out_zei_prc
                        + (jd.sougi_keiyaku_prc + jd.sougi_harai_prc + jd.sougi_wari_prc)
                        + (jd.sougi_keiyaku_zei + jd.sougi_wari_zei)
                        + jd.sougi_early_use_cost
                        + jd.sougi_early_use_cost_zei
                        + jd.sougi_premium_service_prc
                        + jd.sougi_tokuten_prc + jd.n_free9 + jd.n_free10
                        + jd.n_free5 
                    )  , 'FM999,999,999') AS juchu_prc_sum,    -- 御見積金額
                TO_CHAR(ud.seikyu_zan
			+ ud.nyukin_prc
			+ ud.uchikin_prc
			+ ud.kouden_uchikin_prc
			+ ud.bad_debt_loss_prc
			+ COALESCE(ud.cupon_prc,0)
                        , 'FM999,999,999') AS uri_prc_sum       -- 御請求金額
            FROM seko_kihon_info ski            -- 施行基本
            LEFT JOIN seko_kihon_all_free skaf  -- 施行基本フリー
              ON skaf.seko_no    = ski.seko_no
             AND skaf.delete_flg = 0
            LEFT JOIN sekyu_saki_info ssi       -- 請求先情報
              ON ssi.sekyu_cd    = ski.sekyu_cd
             AND ssi.delete_flg  = 0
            LEFT JOIN seko_nitei sn1            -- 施行日程：葬儀
              ON sn1.seko_no = ski.seko_no
             AND sn1.nitei_kbn   = 11
             AND sn1.delete_flg  = 0
            LEFT JOIN seko_nitei sn2            -- 施行日程：死亡
              ON sn2.seko_no = ski.seko_no
             AND sn2.nitei_kbn   = 1
             AND sn2.delete_flg  = 0
            LEFT JOIN tanto_mst tm1             -- 担当者マスタ：受付担当者
              ON tm1.tanto_cd    = ski.uketuke_tanto_cd
             AND tm1.delete_flg  = 0
            LEFT JOIN tanto_mst tm2             -- 担当者マスタ：施行担当者
              ON tm2.tanto_cd = ski.seko_tanto_cd
             AND tm2.delete_flg  = 0
            LEFT JOIN code_nm_mst cnm1          -- コード名称マスタ：申込区分
              ON cnm1.code_kbn   = '0010'
             AND cnm1.kbn_value_cd_num = ski.moushi_kbn
             AND cnm1.delete_flg = 0
            LEFT JOIN code_nm_mst cnm2          -- コード名称マスタ：会員区分
              ON cnm2.code_kbn = '0030'
             AND cnm2.kbn_value_cd_num = ski.kaiin_kbn
             AND cnm2.delete_flg = 0
            LEFT JOIN juchu_denpyo jd
              ON jd.seko_no    = ski.seko_no
             AND jd.delete_flg = 0
            LEFT JOIN uriage_denpyo ud
              ON ud.denpyo_no  = jd.denpyo_no
             AND ud.delete_flg = 0
            LEFT JOIN seikyu_denpyo sd
              ON sd.uri_den_no = ud.uri_den_no
             AND sd.delete_flg = 0
            LEFT JOIN code_nm_mst cnm3
              ON cnm3.code_kbn = '0610'
             AND cnm3.kbn_value_cd_num = ski.status_kbn
             AND cnm3.delete_flg = 0
            WHERE (CASE WHEN COALESCE(sd.status_kbn, 0) > 3 THEN
                            (CASE WHEN sd.bun_gas_kbn_num IN (0, 2, 20) THEN TRUE
                                ELSE FALSE
                             END)
                        WHEN COALESCE(ud.status_kbn, 0) <= 2        AND -- ステータス:見積確定済以下
                             ski.moushi_kbn IN (1, 2, 7, 8, 9, 10) AND -- 申込区分  ：葬儀、法事、貸式場、エンバー依頼、搬送依頼、その他施行
                             COALESCE(ud.seko_prc_kakute_kbn,0) = 0                -- 施行金額確定区分：0:未確定
                            THEN TRUE
                        ELSE FALSE
                  END)
                AND jd.data_kbn IN (1,2)
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 
     * 請求伝票取得
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findSeikyuDenpyo($db, $keyHash = array()) {
        $param = array();
        $addsql = "";
        if (isset($keyHash['pay_kbn']) && strlen($keyHash['pay_kbn']) > 0) {
            $pay_kbn = $keyHash['pay_kbn'];
            $addsql = " (SELECT SUM(COALESCE(fd.siharai_prc_sum,0)) FROM front_denpyo fd WHERE fd.seikyu_den_no = sd.seikyu_den_no AND fd.delete_flg = 0 AND fd.pay_kbn = $pay_kbn) AS nyukin_prc_sum, ";
        }
        unset($keyHash['pay_kbn']);
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no';
        }
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            *
        FROM (
            SELECT 
                sd.* ,
                CASE sd.bun_gas_kbn_num WHEN 2 THEN bun_sd.uri_den_no
                                        ELSE sd.uri_den_no
                END                                AS uri_den_no,
                TO_CHAR(sd.keijo_ymd,'yyyy/mm/dd') AS seikyu_ymdx,
                sd.uri_prc_sum
                + sd.uri_nebk_sum	  
                + sd.uri_hepn_sum
                + sd.hoshi_prc_sum
                + sd.out_zei_prc 
                - sd.sougi_keiyaku_prc
                + (sd.sougi_keiyaku_prc + sd.sougi_harai_prc + sd.sougi_wari_prc)
                - sd.sougi_zei_sagaku_prc
                + sd.sougi_meigi_chg_cost + sd.sougi_meigi_chg_cost_zei
                + sd.sougi_early_use_cost + sd.sougi_early_use_cost_zei
                + sd.etc_harai_prc
                AS  seikyu_zei_prc,
                array_to_string(
                    ARRAY(SELECT nafuda_nm FROM uriage_denpyo_msi udm WHERE udm.uri_den_no = sd.uri_den_no ), '/'
                )
                AS nafuda_nm,
                ud.front_den_no,
                cnm.kbn_value_lnm AS seikyu_kbn_nm,
                $addsql
                CASE WHEN sd.juchusaki_kbn = 2 AND sd.pay_method_cd = '70' THEN 1
                      ELSE 0
                END juchusaki_kbn_tmp,
                sd.uchikin_prc,
                sd.cupon_prc ,  
                sd.nyukin_prc 
                + sd.uchikin_prc
                + sd.cupon_prc
                + sd.kouden_uchikin_prc
                AS  total_nyukin_prc,
                COALESCE(COALESCE(sssi.sekyu_nm1, '') || COALESCE(sssi.sekyu_nm2, ''), sd.sekyu_nm) AS sekyusaki_sekyu_nm,
                COALESCE(COALESCE(sssi.sekyu_knm1, '') || COALESCE(sssi.sekyu_knm2, ''), sd.sekyu_knm) AS sekyusaki_sekyu_knm,
                COALESCE(COALESCE(sssi.sekyu_addr1, '') || COALESCE(sssi.sekyu_addr2, ''), COALESCE(sd.sekyu_addr1, '') || COALESCE(sd.sekyu_addr2, '')) AS sekyusaki_sekyu_addr,
                COALESCE(sssi.sekyu_cd, sd.sekyu_cd) AS sekyusaki_sekyu_cd,
                CASE 
                    WHEN COALESCE(sd.seikyu_zan, 0) = sd.uri_prc_sum
                                                    + sd.uri_nebk_sum	  
                                                    + sd.uri_hepn_sum
                                                    + sd.hoshi_prc_sum
                                                    + sd.out_zei_prc 
                                                    - sd.sougi_keiyaku_prc
                                                    + (sd.sougi_keiyaku_prc + sd.sougi_harai_prc + sd.sougi_wari_prc)
                                                    - sd.sougi_zei_sagaku_prc
                                                    + sd.sougi_meigi_chg_cost + sd.sougi_meigi_chg_cost_zei
                                                    + sd.sougi_early_use_cost + sd.sougi_early_use_cost_zei
                                                    + sd.etc_harai_prc   THEN     --'未決済'
                            (SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn = '7941' AND kbn_value_cd = '1' LIMIT 1)
                    WHEN COALESCE(sd.seikyu_zan, 0) = 0           THEN     --'決済終了'
                            (SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn = '7941' AND kbn_value_cd = '4' LIMIT 1)
                    WHEN COALESCE(sd.seikyu_zan, 0) <> sd.uri_prc_sum
                                                    + sd.uri_nebk_sum	  
                                                    + sd.uri_hepn_sum
                                                    + sd.hoshi_prc_sum
                                                    + sd.out_zei_prc 
                                                    - sd.sougi_keiyaku_prc
                                                    + (sd.sougi_keiyaku_prc + sd.sougi_harai_prc + sd.sougi_wari_prc)
                                                    - sd.sougi_zei_sagaku_prc
                                                    + sd.sougi_meigi_chg_cost + sd.sougi_meigi_chg_cost_zei
                                                    + sd.sougi_early_use_cost + sd.sougi_early_use_cost_zei
                                                    + sd.etc_harai_prc  THEN     --'一部決済'
                                                        (SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn = '7941' AND kbn_value_cd = '3' LIMIT 1)
                    ELSE ''
                 END                                    AS kesan_kbn
            FROM 
                seikyu_denpyo sd
            LEFT JOIN uriage_denpyo ud
            ON ud.uri_den_no  = sd.uri_den_no
            AND ud.delete_flg = 0
            LEFT JOIN seikyu_denpyo bun_sd
            ON bun_sd.seikyu_den_no = sd.bun_gas_seikyu_den_no
            AND bun_sd.delete_flg   = 0
            LEFT JOIN seikyu_sekyu_saki_info sssi   -- ダイアログ用で分割元は表示されない想定
            ON sssi.seikyu_den_no = sd.seikyu_den_no
            AND sssi.delete_flg = 0
            LEFT JOIN code_nm_mst cnm
            ON cnm.code_kbn    = '0920'
            AND cnm.kbn_value_cd_num = sd.data_kbn
            AND cnm.delete_flg = 0
            WHERE sd.data_kbn IN ('1','2','3','4')
            AND sd.delete_flg = 0
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);
        foreach ($select as &$rec) {
            $dtl = static::findSeikyuMsi($db, $rec['seikyu_den_no']);
            $rec['_dtl_'] = $dtl;
        }
        return $select;
    }

    /**
     * 
     * 請求伝票明細情報取得
     * 
     * @param type $db
     * @param type $denpyo_no
     * @return type
     */
    public function findSeikyuMsi($db, $denpyo_no) {
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            sdm.* 
        FROM seikyu_denpyo_msi sdm
        WHERE sdm.seikyu_den_no = :seikyu_den_no;
END_OF_SQL
                , array('seikyu_den_no' => $denpyo_no));
        return $select;
    }

    /**
     * 
     * 売上伝票取得
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findUriageDenpyo($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no';
        }
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            *
        FROM (
            SELECT 
                * ,
                uri_prc_sum
                + uri_nebk_sum	  
                + uri_hepn_sum
                + hoshi_prc_sum
                + out_zei_prc 
                - sougi_keiyaku_prc
                + (sougi_keiyaku_prc + sougi_harai_prc + sougi_wari_prc)
                - sougi_zei_sagaku_prc
                + sougi_meigi_chg_cost + sougi_meigi_chg_cost_zei
                + sougi_early_use_cost + sougi_early_use_cost_zei
                + etc_harai_prc
                AS  seikyu_zei_prc,
                uri_prc_sum
                + uri_nebk_sum	  
                + uri_hepn_sum
                + hoshi_prc_sum
                + out_zei_prc 
                - sougi_keiyaku_prc
                + (sougi_keiyaku_prc + sougi_harai_prc + sougi_wari_prc)
                - sougi_zei_sagaku_prc
                + sougi_meigi_chg_cost + sougi_meigi_chg_cost_zei
                + sougi_early_use_cost + sougi_early_use_cost_zei
                + etc_harai_prc
                - uchikin_prc
                - cupon_prc
                AS  seikyu_zan_prc,
                CASE 
                    WHEN COALESCE(seikyu_zan, 0) = uri_prc_sum
                                                    + uri_nebk_sum	  
                                                    + uri_hepn_sum
                                                    + hoshi_prc_sum
                                                    + out_zei_prc 
                                                    - sougi_keiyaku_prc
                                                    + (sougi_keiyaku_prc + sougi_harai_prc + sougi_wari_prc)
                                                    - sougi_zei_sagaku_prc
                                                    + sougi_meigi_chg_cost + sougi_meigi_chg_cost_zei
                                                    + sougi_early_use_cost + sougi_early_use_cost_zei
                                                    + etc_harai_prc   THEN     --'未決済'
                            (SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn = '7941' AND kbn_value_cd = '1' LIMIT 1)
                    WHEN COALESCE(seikyu_zan, 0) = 0           THEN     --'決済終了'
                            (SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn = '7941' AND kbn_value_cd = '4' LIMIT 1)
                    WHEN COALESCE(seikyu_zan, 0) <> uri_prc_sum
                                                    + uri_nebk_sum	  
                                                    + uri_hepn_sum
                                                    + hoshi_prc_sum
                                                    + out_zei_prc 
                                                    - sougi_keiyaku_prc
                                                    + (sougi_keiyaku_prc + sougi_harai_prc + sougi_wari_prc)
                                                    - sougi_zei_sagaku_prc
                                                    + sougi_meigi_chg_cost + sougi_meigi_chg_cost_zei
                                                    + sougi_early_use_cost + sougi_early_use_cost_zei
                                                    + etc_harai_prc  THEN     --'一部決済'
                                                        (SELECT kbn_value_lnm FROM code_nm_mst WHERE code_kbn = '7941' AND kbn_value_cd = '3' LIMIT 1)
                    ELSE ''
                 END                                    AS kesan_kbn
            FROM 
                uriage_denpyo
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);
        foreach ($select as &$rec) {
            $dtl = static::findUriageMsi($db, $rec['uri_den_no']);
            $rec['_dtl_'] = $dtl;
        }
        return $select;
    }

    /**
     * 
     * 売上伝票明細情報取得
     * 
     * @param type $db
     * @param type $denpyo_no
     * @return type
     */
    public function findUriageMsi($db, $denpyo_no) {
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            udm.* 
        FROM uriage_denpyo_msi udm
        WHERE udm.uri_den_no = :uri_den_no;
END_OF_SQL
                , array('uri_den_no' => $denpyo_no));
        return $select;
    }

    /**
     * 
     * レジ残高情報取得
     * 
     * @param type $db
     * @param type $keyHash
     */
    public function findRegiZan($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.bumon_cd, T.register_no DESC, T.trn_date DESC, T.data_kbn DESC, T.history_no DESC';
        }
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
             * 
            --,T.curr_day_sys_other_zan AS prc_other_5
            ,COALESCE(
             (SELECT SUM(COALESCE(fd.nyukin_prc_sum,0) - COALESCE(fd.syukkin_prc_sum,0) - COALESCE(fd.cupon_prc,0)) 
              FROM front_denpyo fd 
              WHERE fd.delete_flg = 0 
              AND fd.trn_date = T.trn_date AND fd.trn_time >= T.trn_time AND fd.pay_kbn = 2
              AND fd.register_no = T.register_no AND fd.bumon_cd = T.bumon_cd)
             ,0) -
             COALESCE(
                (SELECT
                SUM(COALESCE(ftm.nyukin_prc,0)) - SUM(COALESCE(ftm.syukkin_prc,0)) - SUM(COALESCE(ftm.cupon_prc,0)) AS prc_sum
                FROM front_trn_msi ftm 
                LEFT JOIN front_denpyo fd
                ON  fd.front_den_no = ftm.front_den_no
                AND fd.delete_flg   = 0
                LEFT JOIN torihiki_kbn_mst tkm
                ON  tkm.torihiki_kbn_cd = ftm.torihiki_kbn_cd
                AND tkm.delete_flg = 0
                WHERE fd.bumon_cd  = T.bumon_cd
                AND fd.register_no = T.register_no
                AND fd.trn_date    = T.trn_date
                AND fd.trn_time >= T.trn_time
                AND fd.delete_flg  = 0
                AND COALESCE(tkm.swk_cre_kbn,0) = 1)
	     ,0) AS prc_coupon_3 -- 現金売上
            ,COALESCE(
             (SELECT SUM(COALESCE(fd.nyukin_prc_sum,0) - COALESCE(fd.syukkin_prc_sum,0)) 
              FROM front_denpyo fd 
              WHERE fd.delete_flg = 0
              AND fd.trn_date = T.trn_date AND fd.trn_time >= T.trn_time AND fd.pay_kbn <> 2
              AND fd.register_no = T.register_no AND fd.bumon_cd = T.bumon_cd)
            ,0) +
             COALESCE(
             (SELECT SUM(COALESCE(fd.cupon_prc,0)) 
              FROM front_denpyo fd 
              WHERE fd.delete_flg = 0 
              AND fd.trn_date = T.trn_date AND fd.trn_time >= T.trn_time AND fd.pay_kbn = 2
              AND fd.register_no = T.register_no AND fd.bumon_cd = T.bumon_cd)
             ,0) +
            COALESCE(
                (SELECT --SUM(COALESCE(fd.nyukin_prc_sum,0) - COALESCE(fd.syukkin_prc_sum,0) - COALESCE(fd.cupon_prc,0)) 
                        COALESCE(SUM(ftm.nyukin_prc),COALESCE(SUM(fd.nyukin_prc_sum),0)) - COALESCE(SUM(ftm.syukkin_prc),COALESCE(SUM(fd.syukkin_prc_sum),0)) - COALESCE(SUM(ftm.cupon_prc),COALESCE(SUM(fd.cupon_prc),0))
                FROM front_denpyo fd 
                LEFT JOIN front_trn_msi ftm
                ON  ftm.front_den_no = fd.front_den_no
                AND ftm.delete_flg   = 0
                LEFT JOIN torihiki_kbn_mst tkm
                ON  tkm.torihiki_kbn_cd = ftm.torihiki_kbn_cd
                AND tkm.delete_flg      = 0
                WHERE fd.delete_flg = 0 
                AND fd.trn_date = T.trn_date AND fd.trn_time >= T.trn_time AND fd.pay_kbn = 2
                AND fd.register_no = T.register_no AND fd.bumon_cd = T.bumon_cd
                AND COALESCE(tkm.swk_cre_kbn,0) = 1)
                ,0)                 AS curr_day_sys_other_zan -- 現金以外		
        FROM (
            SELECT 
                 rz.bumon_cd
                ,rz.register_no
                ,rz.trn_date
                ,rz.trn_time
                ,rz.data_kbn
                ,rz.history_no
                ,rz.prev_day_cash_zan
                ,rz.prev_day_sys_cash_zan
                ,rz.curr_day_maisu_1
                ,rz.curr_day_maisu_2
                ,rz.curr_day_maisu_3
                ,rz.curr_day_maisu_4
                ,rz.curr_day_maisu_5
                ,rz.curr_day_maisu_6
                ,rz.curr_day_maisu_7
                ,rz.curr_day_maisu_8
                ,rz.curr_day_maisu_9
                ,rz.curr_gift_token_maisu
                ,rz.curr_gift_token_prc
                ,rz.curr_cupon_maisu
                ,rz.curr_cupon_prc
                ,rz.curr_st_day_cash_zan
                ,rz.curr_st_day_other_zan
                ,rz.nyukin_chosei_prc
                ,rz.syukkin_chosei_prc
                --,rz.curr_day_sys_other_zan
                ,rz.curr_day_sys_cash_zan
                ,rz.closing_kbn
            FROM register_zan rz
            WHERE rz.delete_flg = 0
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 
     * 前日レジ残高情報取得
     * 
     * @param type $db
     * @param type $keyHash
     */
    public function findZenRegiZan($db, $keyHash = array(), $trn_date = null) {
        if (!isset($trn_date)) {
            $trn_date = date("Y/m/d");
        }
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.bumon_cd, T.register_no DESC, T.trn_date DESC, T.data_kbn DESC, T.history_no DESC';
        }
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            * ,
            T.prev_day_cash_zan AS prev_day_sys_cash_zan
        FROM (
            SELECT 
                bumon_cd,
                register_no,
                trn_date,
                data_kbn,
                history_no,
                curr_day_maisu_1 AS prc_10000_1,
                curr_day_maisu_2 AS prc_5000_1,
                curr_day_maisu_3 AS prc_1000_1,
                curr_day_maisu_4 AS prc_500_1,
                curr_day_maisu_5 AS prc_100_1,
                curr_day_maisu_6 AS prc_50_1,
                curr_day_maisu_7 AS prc_10_1,
                curr_day_maisu_8 AS prc_5_1,
                curr_day_maisu_9 AS prc_1_1,
                COALESCE(curr_day_maisu_1,0) * 10000
                + COALESCE(curr_day_maisu_2,0) * 5000
                + COALESCE(curr_day_maisu_3,0) * 1000
                + COALESCE(curr_day_maisu_4,0) * 500
                + COALESCE(curr_day_maisu_5,0) * 100
                + COALESCE(curr_day_maisu_6,0) * 50
                + COALESCE(curr_day_maisu_7,0) * 10
                + COALESCE(curr_day_maisu_8,0) * 5
                + COALESCE(curr_day_maisu_9,0) * 1 AS prev_day_cash_zan
            FROM register_zan 
            WHERE delete_flg = 0
            AND '$trn_date' > TO_CHAR(trn_date,'YYYY/mm/dd')
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 
     * 前日レジ残高をフロント伝票から取得
     * 
     * @param type $db
     * @param type $keyHash
     */
    public function findZenRegiZanFront($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            
        }
        // 現金入出金
        $prc1 = 0;
        $select1 = $db->easySelect(<<< END_OF_SQL
        SELECT 
            SUM(COALESCE(fd.nyukin_prc_sum,0)) - SUM(COALESCE(fd.syukkin_prc_sum,0)) - SUM(COALESCE(fd.cupon_prc,0)) AS prc_sum
        FROM front_denpyo fd
        WHERE fd.bumon_cd   = :bumon_cd
        AND fd.register_no  = :register_no
        AND fd.trn_date     = :trn_date
        AND fd.pay_kbn    = 2
        AND fd.delete_flg = 0
END_OF_SQL
                , $param);
        if (Msi_Sys_Utils::myCount($select1) > 0) {
            $prc1 = $select1[0]['prc_sum'];
        }
        // 前日レジチェック（開始）金額
        $prc2 = 0;
        $select2 = $db->easySelect(<<< END_OF_SQL
        SELECT 
            COALESCE(prev_day_cash_zan,0)
                AS prev_day_cash_zan
        FROM register_zan 
        WHERE bumon_cd  = :bumon_cd
        AND register_no = :register_no
        AND trn_date    = :trn_date
        AND data_kbn    = 0 -- 前処理（前日レジチェック（開始））
        AND delete_flg  = 0
END_OF_SQL
                , $param);
        if (Msi_Sys_Utils::myCount($select2) > 0) {
            $prc2 = $select2[0]['prev_day_cash_zan'];
        }
        // 本社への送金(クーポン券)分
        $prc3 = 0;
        $select3 = $db->easySelect(<<< END_OF_SQL
        SELECT 
            SUM(COALESCE(ftm.nyukin_prc,0)) - SUM(COALESCE(ftm.syukkin_prc,0)) - SUM(COALESCE(ftm.cupon_prc,0)) AS prc_sum
        FROM front_trn_msi ftm 
        LEFT JOIN front_denpyo fd
        ON  fd.front_den_no = ftm.front_den_no
        AND fd.delete_flg   = 0
        LEFT JOIN torihiki_kbn_mst tkm
        ON  tkm.torihiki_kbn_cd = ftm.torihiki_kbn_cd
        AND tkm.delete_flg = 0
        WHERE fd.bumon_cd  = :bumon_cd
        AND fd.register_no = :register_no
        AND fd.trn_date    = :trn_date
        AND fd.delete_flg  = 0
        AND COALESCE(tkm.swk_cre_kbn,0) = 1
END_OF_SQL
                , $param);
        if (Msi_Sys_Utils::myCount($select3) > 0) {
            $prc3 = $select3[0]['prc_sum'];
        }
        // 入出金調整金額
        $prc4 = 0;
        $select4 = $db->easySelect(<<< END_OF_SQL
        SELECT 
            SUM(COALESCE(fd.nyukin_prc_sum,0)) - SUM(COALESCE(fd.syukkin_prc_sum,0)) - SUM(COALESCE(fd.cupon_prc,0)) AS prc_sum
        FROM front_denpyo fd
        WHERE fd.denpyo_kbn = 6 
        AND fd.bumon_cd    = :bumon_cd
        AND fd.register_no = :register_no
        AND fd.trn_date    = :trn_date
        AND fd.delete_flg  = 0
END_OF_SQL
                , $param);
        if (Msi_Sys_Utils::myCount($select4) > 0) {
            $prc4 = $select4[0]['prc_sum'];
        }
        return $prc1 + $prc2 - $prc3 + $prc4;
    }

    /**
     * 
     * 前回レジ残高情報取得
     * 
     * @param type $db
     * @param type $keyHash
     */
    public function findZenkaiRegiZan($db, $keyHash = array(), $trn_date = null) {
        if (!isset($trn_date)) {
            $trn_date = date("Y/m/d");
        }
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.bumon_cd, T.register_no DESC, T.trn_date DESC, T.data_kbn DESC, T.history_no DESC';
        }
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT 
            * ,
            T.prev_day_cash_zan AS prev_day_sys_cash_zan
        FROM (
            SELECT 
                bumon_cd,
                register_no,
                trn_date,
                data_kbn,
                history_no,
                curr_day_maisu_1 AS prc_10000_1,
                curr_day_maisu_2 AS prc_5000_1,
                curr_day_maisu_3 AS prc_1000_1,
                curr_day_maisu_4 AS prc_500_1,
                curr_day_maisu_5 AS prc_100_1,
                curr_day_maisu_6 AS prc_50_1,
                curr_day_maisu_7 AS prc_10_1,
                curr_day_maisu_8 AS prc_5_1,
                curr_day_maisu_9 AS prc_1_1,
                COALESCE(curr_day_maisu_1,0) * 10000
                + COALESCE(curr_day_maisu_2,0) * 5000
                + COALESCE(curr_day_maisu_3,0) * 1000
                + COALESCE(curr_day_maisu_4,0) * 500
                + COALESCE(curr_day_maisu_5,0) * 100
                + COALESCE(curr_day_maisu_6,0) * 50
                + COALESCE(curr_day_maisu_7,0) * 10
                + COALESCE(curr_day_maisu_8,0) * 5
                + COALESCE(curr_day_maisu_9,0) * 1 AS prev_day_cash_zan
            FROM register_zan 
            WHERE delete_flg = 0
            AND '$trn_date' >= TO_CHAR(trn_date,'YYYY/mm/dd')
        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 
     * 入金、出金調整金を取得
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findPrcSum($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.trn_date DESC';
        }
        $select = $db->easySelect(<<< END_OF_SQL
        SELECT *
        FROM (
            SELECT 
                 SUM(fd.nyukin_prc_sum)     AS nyukin_chosei_prc
                ,SUM(fd.syukkin_prc_sum)    AS syukkin_chosei_prc
                ,fd.trn_date
                ,fd.register_no
                ,fd.bumon_cd
            FROM 
                front_denpyo fd
            INNER JOIN front_trn_msi ftm
               ON fd.front_den_no = ftm.front_den_no
              AND ftm.delete_flg  = 0
             LEFT JOIN torihiki_kbn_mst tkm
               ON tkm.torihiki_kbn_cd = ftm.torihiki_kbn_cd
              AND tkm.delete_flg      = 0
             LEFT JOIN tanto_mst tm
               ON tm.tanto_cd   = fd.tanto_cd
              AND tm.delete_flg = 0
            WHERE fd.delete_flg = 0
              AND fd.denpyo_kbn = 6 -- 5:調整処理
            GROUP BY 
                 fd.trn_date
                ,fd.register_no
                ,fd.bumon_cd

        ) T
        WHERE $whereStr
        $orderBy
        $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

    /**
     * 
     * 出納帳情報を取得
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     */
    public function findSuitou($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.torihiki_ymd DESC, T.front_den_no DESC';
        }
        $select = $db->easySelect(<<< END_OF_SQL
    SELECT 
         *
        ,CASE WHEN TRIM(T.torihiki_kbn) = '2000001' THEN CONCAT(T.trn_tekiyo,'／',T.kari_tanto_nm)
              WHEN TRIM(T.torihiki_kbn) = '1100001' AND T.trn_seikyu_den_no IS NULL THEN CONCAT('施行番号:',T.seko_no)
              WHEN TRIM(T.torihiki_kbn) = '1100001' THEN CONCAT('請求伝票:',T.trn_seikyu_den_no)
              WHEN TRIM(T.torihiki_kbn) = '1200001' THEN CONCAT('請求伝票:',T.trn_seikyu_den_no)
              WHEN TRIM(T.torihiki_kbn) = '1200002' THEN CONCAT('請求伝票:',T.trn_seikyu_den_no)
	      ELSE T.tekiyo END AS tekiyo
    FROM (
        SELECT 
             TO_CHAR(fd.trn_date,'yyyy/mm/dd') AS torihiki_ymd
            ,TO_CHAR(fd.trn_time,'HH24:MI')    AS torihiki_time
            ,ftm.torihiki_kbn_cd               AS torihiki_kbn
            ,CASE WHEN tkm.torihiki_kbn_nm IS NULL AND fd.denpyo_kbn = 1 THEN 'フロント売上'
                  ELSE tkm.torihiki_kbn_nm
             END                               AS torihiki_kbn_nm
            -- 現金入金
            ,CASE WHEN tkm.cupon_kbn = 1 THEN 0
                  WHEN fd.pay_kbn = 2 AND fd.aka_kuro_kbn = 1 THEN 
                    CASE WHEN tkm.torihiki_kbn_nm IS NULL AND fd.denpyo_kbn = 1 THEN fd.nyukin_prc_sum
                         ELSE ftm.nyukin_prc END
                  WHEN fd.pay_kbn = 2 THEN -- 2:現金
                    CASE WHEN tkm.torihiki_kbn_nm IS NULL AND fd.denpyo_kbn = 1 THEN fd.nyukin_prc_sum - fd.cupon_prc
                         ELSE ftm.nyukin_prc END
                  WHEN fd.pay_kbn IS NULL THEN fd.nyukin_prc_sum
                  ELSE 0
             END                               AS nyukin_prc
            -- 現金出金
            ,CASE WHEN tkm.cupon_kbn = 1 THEN 0
                  WHEN fd.pay_kbn = 2 AND fd.aka_kuro_kbn = 1 THEN 
                    CASE WHEN tkm.torihiki_kbn_nm IS NULL AND fd.denpyo_kbn = 1 THEN fd.syukkin_prc_sum + fd.cupon_prc
                         ELSE ftm.syukkin_prc END 
                  WHEN fd.pay_kbn = 2 THEN -- 2:現金
                    CASE WHEN tkm.torihiki_kbn_nm IS NULL AND fd.denpyo_kbn = 1 THEN fd.syukkin_prc_sum
                         ELSE ftm.syukkin_prc END 
                  WHEN fd.pay_kbn IS NULL THEN fd.syukkin_prc_sum
                  ELSE 0
             END                               AS syukkin_prc
            -- 現金以外入出金
            ,CASE WHEN tkm.cupon_kbn = 1 THEN ftm.nyukin_prc - ftm.syukkin_prc
                  WHEN fd.pay_kbn <> 2 THEN 
                    CASE WHEN fd.aka_kuro_kbn = 1 THEN 
                            CASE WHEN tkm.torihiki_kbn_nm IS NULL AND fd.denpyo_kbn = 1 THEN (fd.cupon_prc + fd.nyukin_prc_sum + fd.syukkin_prc_sum) * -1
                                 ELSE ftm.cupon_prc + ftm.nyukin_prc + ftm.syukkin_prc  * -1 END 
                         ELSE 
                            CASE WHEN tkm.torihiki_kbn_nm IS NULL AND fd.denpyo_kbn = 1 THEN fd.cupon_prc + fd.nyukin_prc_sum + fd.syukkin_prc_sum
                                 ELSE ftm.cupon_prc + ftm.nyukin_prc + ftm.syukkin_prc END 
                    END
                  WHEN fd.cupon_kbn = 1 AND fd.data_kbn = 99 THEN ftm.cupon_prc
                  WHEN fd.cupon_kbn = 1 THEN fd.cupon_prc
                  ELSE 0
             END                               AS other_prc
            ,ftm.msi_no
            ,tm.tanto_nm
            ,ftm.tekiyo                        AS trn_tekiyo
            ,ftm.karibarai_tanto_cd
            ,kari_tm.tanto_nm                  AS kari_tanto_nm
            ,ftm.seikyu_den_no                 AS trn_seikyu_den_no
            ,fd.*
            ,ftm.nyukin_den_no
        FROM 
            front_denpyo fd
        LEFT JOIN front_trn_msi ftm
          ON fd.front_den_no = ftm.front_den_no
         AND ftm.delete_flg = 0
        LEFT JOIN torihiki_kbn_mst tkm
          ON tkm.torihiki_kbn_cd = ftm.torihiki_kbn_cd
        LEFT JOIN tanto_mst tm
          ON tm.tanto_cd = fd.tanto_cd
         AND tm.delete_flg = 0
        LEFT JOIN tanto_mst kari_tm
          ON kari_tm.tanto_cd   = ftm.karibarai_tanto_cd
         AND kari_tm.delete_flg = 0
        WHERE fd.delete_flg  = 0
    ) T
    WHERE $whereStr
    $orderBy
    $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

}
