#!/bin/env php
<?php
/**
 * ライフランド ファイル連携 サンプル01
 * cf. extfiles_sample01.php
 *
 * usage: extfiles_sample01.php
 *    ex. extfiles_sample01.php -c 46460000
 *
 * @category   batch
 * @package    App
 * <AUTHOR> Mihara
 * @since      2025/04/xx
 * @filesource
 */

require_once realpath(dirname(__FILE__) . '/../../../library/Msi/batch_env2.php');

$_cmd_name = 'ライフランドファイル連携サンプル01';

$_path_psql = Msi_Sys_Utils::getConfigByKeyDefault( 'msi.path.command.psql', 'psql' );


/**
 * 画面にメッセージを出力する
 *
 * <AUTHOR> Mihara
 * @since      2025/04/xx
 * @param      string   $msg   メッセージ
 * @return     void
 */
function _console( $msg ) {
    $msg = rtrim($msg) . "\n";
    print $msg;
}

/**
 * メッセージ出力を標準化する
 *
 * <AUTHOR> Mihara
 * @since      2025/04/xx
 * @param      string   $msg   メッセージ
 * @return     string
 */
function _msg( $msg ) {
    global $_cmd_name;
    $_msg = $_cmd_name . ': ' . $msg;
    return $_msg;
}

/**
 * エラー出力を標準化し、Exception を送出する
 *
 * <AUTHOR> Mihara
 * @since      2025/04/xx
 * @param      string   $msg   メッセージ
 * @return     void
 * @throws     Exception
 */
function _err( $msg ) {
    throw new Exception( _msg($msg) );
}

/**
 * コマンド説明を表示し終了する
 *
 * <AUTHOR> Mihara
 * @since      2025/04/xx
 * @param      int      $exitval  終了値
 * @param      string   $errMsg   エラーメッセージ
 * @return     void
 */
function _usage_exit( $exitval=1, $errMsg='' ) {
    global $_cmd_name;
    _console( $errMsg );
    $mycmd = basename( __FILE__, '' );
    _console( <<< END_USAGE
$_cmd_name
usage: $mycmd -c <kaisya_cd>
   ex. $mycmd -c 46460000
   -c: 会社コード

END_USAGE
              );

    exit( $exitval );
}

$opts = getopt("c:h");
// var_dump($opts);

if ( isset($opts['h']) ) _usage_exit(0);

if ( ! isset($opts['c']) ) _usage_exit(1, '-c オプションは必須です');
$kaisya_cd = $opts['c'];

try {
    $sysdb = Msi_Sys_DbManager::getDb('sysdb', false);

    $kaisya = $sysdb->easySelOne( <<< END_OF_SQL
SELECT *
  FROM s_kaisya
 WHERE kaisya_cd=:kaisya_cd
END_OF_SQL
                            , array ( ':kaisya_cd' => $kaisya_cd ) );
    if ( $kaisya == null ) {
        _err( sprintf('会社情報(CD:%s)が登録されていません', $kaisya_cd) );
    }

    Msi_Sys_Utils::putenv(Msi_Sys_Constants::MSI_ENV_KAISYA_CD, $kaisya_cd);

    Msi_Sys_Utils::info( _msg(sprintf('%s(会社コード:%s)を開始します', $_cmd_name, $kaisya_cd)) );

    // Msi_Sys_Utils::debug( '* kaisya=>' . Msi_Sys_Utils::dump($kaisya) );

    $kaisya_name = $kaisya['kaisya_lnm'];

    $db_name = $kaisya['db_name'];

    Msi_Sys_Utils::setCstmKeyEx( $kaisya['cstm_key'] ); // カスタマイズキーを明示的に設定

    $db = Msi_Sys_DbManager::getMyDb( $db_name );

    $param = array( 'ft_type' => 'sample',
                    'exe_mode' => 'batch',
                    'ext_cond' => array('start_ymd' => '2025-01-01',
                                        'end_ymd'   => '2025-01-31') );
    $aFileIds = Logic_FileTrans_Sample01::doExec( $db, $param );

    $db->commit();

    Msi_Sys_Utils::info( _msg(sprintf('%s(会社コード:%s)を終了します', $_cmd_name, $kaisya_cd)) );
}
catch ( Exception $e ){
    $msg = $e->getMessage();
    _console( $msg );
    Msi_Sys_Utils::err( $msg );
    exit( 1 );
}

exit(0);
