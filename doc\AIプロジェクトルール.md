# AIプロジェクトルール

**【重要】このドキュメントは、AIが葬儀施行システム(FDN)開発で作業を行う際に必ず遵守すべきルールとガイドラインです。**

## 【必須】AIが守るべき基本ルール

### 🔴 絶対に守ること（必須ルール）

1. **日本語での回答**: すべての回答、コメント、説明は日本語で記述する
2. **機密情報の保護**: 実際の個人情報（故人名、遺族名、住所、電話番号等）は絶対に使用しない
3. **架空データの使用**: サンプルデータ作成時は必ず架空の情報を使用する
4. **コーディング規約の遵守**: 本ドキュメントで定義されたコーディング規約に従う

### 🟡 推奨事項

1. **詳細な説明の提供**: コードの動作や仕様について詳細に説明する
2. **エラーハンドリングの実装**: 適切な例外処理を含める
3. **コメントの充実**: 日本語でわかりやすいコメントを記述する
4. **専門用語の正確な使用**: 葬儀業界の専門用語を正しく理解し使用する

### ❌ 禁止事項

1. **実データの使用**: 本番環境のデータや実際の個人情報を使用しない
2. **セキュリティ情報の露出**: データベース接続情報やパスワードを含めない
3. **規約違反のコード**: 本ドキュメントのコーディング規約に反するコードを生成しない
4. **不適切な専門用語の使用**: 葬儀業界の専門用語を間違って使用しない

---

このガイドは、AIがプロジェクトの構造と仕組みを理解し、適切に作業を行うための情報を提供します。

## 【作業前確認】AIが作業開始前に必ず確認すること

### ✅ 作業前チェックリスト

作業を開始する前に、以下の項目を必ず確認してください：

1. **対象会社の確認**: どの会社（カスタマイズキー）向けの作業かを確認
2. **機能モジュールの確認**: 受注(juchu)、発注(hachu)、請求(seikyu)のどの機能か確認
3. **ファイル種別の確認**: PHP、JavaScript、Smarty、CSSのどのファイルか確認
4. **カスタマイズの有無**: 標準機能かカスタマイズ機能かを確認

### 📚 必須理解事項：専門用語辞書

#### 葬儀業界専門用語（必ず理解すること）

| 用語 | 読み | 意味 | 使用例 |
|------|------|------|--------|
| 施行 | せこう | 葬儀の実施・執行 | 「施行番号」「施行管理」 |
| 受注 | じゅちゅう | 葬儀の依頼を受けること | 「受注管理」「受注伝票」 |
| 発注 | はっちゅう | 必要な商品・サービスを注文すること | 「発注管理」「発注伝票」 |
| 喪家 | そうけ | 故人の家族・遺族 | 「喪家名」「喪家情報」 |
| 通夜 | つや | 故人を偲ぶ夜の儀式 | 「通夜会場」「通夜日程」 |
| 告別式 | こくべつしき | 故人との最後の別れの儀式 | 「告別式会場」「告別式日程」 |
| 法事・法要 | ほうじ・ほうよう | 故人の供養のための仏教儀式 | 「法事予約」「法要施行」 |
| 互助会 | ごじょかい | 葬儀費用の積立制度 | 「互助会会員」「互助会情報」 |
| 湯灌 | ゆかん | 故人の体を清める儀式 | 「湯灌施行」「湯灌料」 |
| 納棺 | のうかん | 故人を棺に納める儀式 | 「納棺施行」「納棺料」 |
| 出棺 | しゅっかん | 棺を葬儀場から運び出すこと | 「出棺時間」「出棺準備」 |
| 火葬 | かそう | 故人を火で荼毘に付すこと | 「火葬場」「火葬料」 |
| 供花 | きょうか | 故人に供える花 | 「供花注文」「供花料」 |
| 返礼品 | へんれいひん | 参列者への感謝の品 | 「返礼品選択」「返礼品発注」 |

#### システム内専門用語（必ず理解すること）

| 用語 | 意味 | 重要度 | 使用例 |
|------|------|--------|--------|
| seko_no | 施行番号（葬儀案件の一意識別子） | 🔴 最重要 | 「施行番号管理」「施行番号検索」 |
| kaisya_cd | 会社コード | 🔴 最重要 | 「会社コード管理」「会社コード検索」 |
| cstm_key | カスタマイズキー | 🔴 最重要 | 「カスタマイズ設定」「カスタマイズ管理」 |
| uri_den_no | 売上伝票番号 | 🟡 重要 | 「売上伝票管理」「売上伝票検索」 |
| denpyo_no | 伝票番号 | 🟡 重要 | 「伝票管理」「伝票検索」 |
| sekyu_nm | 請求先名 | 🟡 重要 | 「請求先管理」「請求先検索」 |
| souke_nm | 喪家名 | 🟡 重要 | 「喪家情報管理」「喪家情報検索」 |
| bumon_cd | 部門コード | 🟡 重要 | 「部門管理」「部門検索」 |
| shohin_cd | 商品コード | 🟡 重要 | 「商品管理」「商品検索」 |
| data_kbn | データ区分（1:葬儀, 2:法事, 3:単品, 4:別注品, 5:生前依頼, 6:その他施行, 8:新盆） | 🟡 重要 | 「データ区分管理」「データ区分検索」 |
| nitei_kbn | 日程区分 | 🟡 重要 | 「日程管理」「日程検索」 |
| delete_flg | 削除フラグ（0:有効, 1:削除） | 🟡 重要 | 「削除管理」「削除検索」 |


---

## 1. プロジェクト概要

Fdnは、葬儀社向けの施行管理システムです。お客様情報管理、受注管理、発注管理、請求管理など、葬儀業務に関わる様々な機能を提供しています。

### 1.1 主要機能

- お客様情報管理
- 受注管理
- 発注管理
- 請求管理
- 帳票出力
- マスタ管理

## 2. システムアーキテクチャ

### 2.1 技術スタック

- **フレームワーク**: Zend Framework 1.x
- **テンプレートエンジン**: Smarty
- **フロントエンド**: jQuery, JavaScript
- **データベース**: PostgreSQL
- **サーバー環境**: Apache, PHP

### 2.2 ディレクトリ構造

```
Fdn.keigen/
├── application/
│   ├── configs/         # 設定ファイル
│   ├── modules/         # モジュール（機能単位）
│   │   ├── hachu/       # 発注モジュール
│   │   ├── juchu/       # 受注モジュール
│   │   ├── seikyu/      # 請求モジュール
│   │   └── ...
│   └── views/
│       └── smarty/      # Smartyテンプレート
├── library/             # 共通ライブラリ
├── public_dev/          # 公開ファイル（開発用）
│   ├── css/             # CSSファイル
│   ├── js/              # JavaScriptファイル
│   └── images/          # 画像ファイル
└── doc/                 # ドキュメント
```

## 3. カスタマイズキーシステム

### 3.1 カスタマイズキーの定義

- カスタマイズキーは、会社ごとに設定される小文字のみからなる10文字程度の文字列です
- `fdn_sys` データベースの `s_kaisya.cstm_key` (会社管理表.カスタマイズキー) フィールドに設定されます
- カンマ区切りで複数設定可能で、前方が優先されます
- 優先順位の最後にカスタマイズキーが付いていないファイル（標準ファイル）が参照されます

### 3.2 会社ごとのカスタマイズキー一覧
カスタマイズキーは、各会社に対して設定されます。以下は一部の会社とそのカスタマイズキーの例です：

| 会社CD | 会社名 | カスタマイズキー |
|--------|--------|-----------------|
| 01001000 | 株式会社ナウエル | nowl |
| 02002020 | 株式会社セレモニア | sano_k,sano |
| 03003033 | 株式会社　彩苑 | saien2,saien,sano |
| 04044004 | 株式会社　御牧 | mimaki,sano |
| 05505500 | サンメンバーズ株式会社 | sanmen2,sanmen_k,sanmen,sano |
| 06644460 | 株式会社　セレモ | ceremo_k,day20,ceremo2,ceremo,hiragana,ver02,sanmen,sano |
| 07121270 | 株式会社　セレモニー | ceremony_k,ceremony,ver02,ceremo,sanmen |
| 08006620 | アイパル株式会社 | ipal_k,ipal,sanmen,sano |
| 09027110 | 株式会社東上セレモサービス | tojo,sanmen,sano |
| 10555550 | 株式会社ごんきや | gonkiya_k,gonkiya,sanmen,sano |
| 12000120 | 株式会社　西村企業 | plaza2_k,plaza2,plaza_k,plaza,sanmen,sano |
| 13333300 | 株式会社ライフシステム | lifesystem_k,lifesystem,sanmen,sano |
| 14022520 | 株式会社ジェイエイ仙南サービス | jasennan_k,jasennan,nise01_k,nise01,gonkiya,sanmen |
| 15015150 | (株)埼玉金周 | saikane_k,saikane,nise01_k,nise01,gonkiya,sanmen |
| 16016160 | (株)みつわ | mitsuwa_k,mitsuwa,nise01_k,nise01,gonkiya,sanmen |
| 17017170 | (株)成南協心社 | seinan,nise01,gonkiya,sanmen |
| 18032320 | 株式会社雅裳苑 | gashoen2,gashoen_k,gashoen,sano |
| 19019190 | (株)公益社 | gessin_k,gessin,nise01_k,nise01,gonkiya,sanmen |
| 20020200 | (株)想心季 | sousinki_k,sousinki,gessin_k,gessin,nise01_k,nise01,gonkiya,sanmen |
| 21021210 | 株式会社リンクモア | linkmore_k,linkmore,gessin_k,gessin,nise01_k,nise01,gonkiya,sanmen |
| 22022220 | 株式会社　幸成社 | kouseisya,nise01,gonkiya,sanmen |
| 23023230 | 株式会社　都島葬祭 | miyakojima,nise01,gonkiya,sanmen |
| 24834100 | ファミリード株式会社 | famld,nise02,nise01,gonkiya,sanmen |
| 25025250 | 株式会社　ふじや本店 | fujiya,nise01_k,nise01,gonkiya,sanmen |
| 26822115 | 株式会社　共栄会館 | haifu,kyouei,nise02,nise01,gonkiya,sanmen |
| 27027270 | 株式会社　公益社 | nkoueki,nise01_k,nise01,gonkiya,sanmen |
| 28344440 | 昭和興業株式会社 | syouwa,nise03 |
| 29002420 | 東條造花店 | tzouka,nise02,nise01,gonkiya,sanmen |
| 30030300 | 株式会社　博全社 | hakuzen,nise02,nise01,gonkiya,sanmen |
| 31001510 | 株式会社　サニーライフ | snlife,nise02,nise01,gonkiya,sanmen |
| 33033330 | ミツギ | mitsugi,saikane_k,saikane,nise01_k,nise01,gonkiya,sanmen |
| 34022227 | あいあーる | aiaru,nise02,nise01_k,nise01,gonkiya,sanmen_k,sanmen |
| 36360000 | セレモア | ceremore,hakuzen,nise02,nise01,gonkiya,sanmen |
| 37037370 | 有限会社　都典礼 | miyakotenrei,saikane_k,saikane,nise01_k,nise01,gonkiya,sanmen |
| 38038380 | (株)さがみくみあいサービス | sagamikumiai,nise04,nise01_k,nise01,gonkiya,sanmen |
| 39890048 | (株)白雲社 | hakuunsya,nise04,nise01_k,nise01,gonkiya,sanmen |
| 40040400 | (株)おおの式典 | oonosikiten,nise04,nise01_k,nise01,gonkiya,sanmen |
| 41013170 | 株式会社ベルモニー | bellmony,ceremore,hakuzen,nise02,nise01,gonkiya,sanmen |
| 42042420 | 株式会社ナリコー | narikoh,nise04,nise01_k,nise01,gonkiya,sanmen |
| 43043430 | 株式会社板橋 | itabashi,bellmony,ceremore,hakuzen,nise02,nise01,gonkiya,sanmen |
| 44009444 | 株式会社マリアージュインベルコ | bellco,bellmony,ceremore,hakuzen,nise02,nise01,gonkiya,sanmen |
| 45045450 | 株式会社サンレー | sunray,bellmony,ceremore,hakuzen,nise02,nise01,gonkiya,sanmen |
| 46460000 | 株式会社ライフランド | lifeland,bellmony,ceremore,hakuzen,nise02,nise01,gonkiya,sanmen |

### 3.3 カスタマイズ対象リソース

以下のリソースがカスタマイズキーによる置き換え対象となります：

1. **コントローラ**（コントローラクラス、アクションメソッド）
   - 例: `BechulistController.nowl.php`

2. **オートロード対象のクラス**（DataMapper_*、Logic_*、Juchu_* 等）
   - 例: `JuchuBechuDenpyo.nowl.php`

3. **JavaScript ファイル**
   - 例: `juchu.bechu.list.nowl.js`

4. **CSS ファイル**
   - 例: `juchu.bechu.list.nowl.css`

5. **Smarty テンプレート**（*.tpl）
   - 例: `main-nowl.tpl`

6. **帳票PDFテンプレート**（PDF下敷き）
   - 例: `0113-nowl.pdf`

7. **表示名置換**
   - 例: `nameMapTable.nowl.php`

8. **メニュー**
   - 例: `menuTable01.nowl.php`

9. **ロール**
   - 例: `appRoleTable.nowl.php`

### 3.4 ファイル命名規則

カスタマイズされたファイルは、元ファイルと同じフォルダに配置され、以下の命名規則に従います：

- **Smarty テンプレートと PDF テンプレート**: ハイフン区切りでカスタマイズキーを付与
  - 例: `main-nowl.tpl`, `0113-nowl.pdf`

- **その他のファイル**: ドット区切りでカスタマイズキーを付与
  - 例: `BechulistController.nowl.php`, `juchu.bechu.list.nowl.js`

### 3.5 リソース読み込みの優先順位

1. カスタマイズキーの配列を前から順に処理
2. 各カスタマイズキーに対応するファイルが存在するか確認
3. 最初に見つかったカスタマイズファイルを使用
4. カスタマイズファイルが見つからない場合は標準ファイルを使用

### 3.6 注意点
- カスタマイズはファイルレベルでの置き換えであり、ファイル内の項目や要素のマージではありません
- ファイルレベルでは差分として管理しますが、ファイル内では差分ではなく置き換えとして使用されます
- 2021年4月9日以降、カスタマイズキーの先頭に `c<会社CD>` が自動的に追加されるようになりました

## 4. ファイル構造と命名規則

### 4.1 モジュール構造

- **モジュール**: 機能単位でディレクトリが分かれています
  - 例: `juchu`（受注）, `hachu`（発注）, `seikyu`（請求）

### 4.2 ファイル命名規則

#### Smartyテンプレート
- 場所: `application\views\smarty\[モジュール]\[コントローラ]\[アクション].tpl`
- 命名規則: 小文字、ハイフン区切り
- 例: `application\views\smarty\hachu\hachushori\henrei-sano.tpl`

#### JavaScript
- 場所: `public_dev\js\app\[モジュール]\[機能].js`
- 命名規則: 小文字、ドット区切り
- 例: `public_dev\js\app\hachu\hachu.henrei.sano.js`

#### CSS
- 場所: `public_dev\css\app\[モジュール]\[機能].css`
- 命名規則: 小文字、ドット区切り
- 例: `public_dev\css\app\hachu\hachu.henrei.sano.css`

#### PHP（コントローラ）
- 場所: `application\modules\[モジュール]\controllers\[コントローラ]Controller.php`
- 命名規則: パスカルケース
- 例: `application\modules\hachu\controllers\HenreidataController.php`

## 5. メニューシステム

### 5.1 メニューファイルの場所と命名規則

- メインメニュー: `application/configs/menuTable01.{カスタマイズキー}.php`
  - 例: `application/configs/menuTable01.nowl.php`（ナウエル用）
  - 例: `application/configs/menuTable01.lifesystem.php`（ライフシステム用）

- サイドメニュー: `application/configs/sideMenu{タイプ}.{カスタマイズキー}.php`
  - 例: `application/configs/sideMenuMitsu.nowl.php`（ナウエル用見積画面）
  - 例: `application/configs/sideMenuHouji.sano.php`（セレモニア用法事画面）

### 5.2 メニューファイルの構造

メニューファイルは以下のような構造になっています：

```php
// メインメニューの例
01.受注業務                sales
    01.新規施行受付        juchu/customerinfo/input/new/1
    02.施行打合せ          juchu/mitsu/input/%m_sn%
    03.別注品一覧          juchu/bechulist/index/readonly/1
```

```php
// サイドメニューの例
お客様情報           customer    /juchu/mitsu/input            1         1
タイムスケジュール   schedule    /juchu/mitsu/timeschedule     2         2
```

### 5.3 URLパスからソースファイルの特定方法

URLパスの形式は通常 `module/controller/action` となっており、これに基づいてソースファイルを特定できます：

1. コントローラファイル: `application/modules/{module}/controllers/{Controller}Controller.php`
   - 例: `juchu/mitsu/input` → `application/modules/juchu/controllers/MitsuController.php`の`inputAction()`メソッド

2. ビューファイル: `application/views/smarty/{module}/{controller}/{action}.tpl`
   - 例: `juchu/mitsu/input` → `application/views/smarty/juchu/mitsu/input.tpl`

3. JavaScriptファイル: `public_dev/js/app/{module}/{module}.{controller}.js`
   - 例: `juchu/mitsu/input` → `public_dev/js/app/juchu/juchu.mitsu.js`

## 6. データベース構造

### 6.1 主要テーブル

#### 6.1.1 施行関連テーブル
- **seko_kihon_info**: 施行基本情報
- **seko_kihon_all_free**: 施行基本フリー情報
- **seko_nitei**: 施行日程情報
- **sekyu_saki_info**: 請求先情報
- **seko_gojokai_info**: 互助会情報
- **seko_gojokai_member**: 互助会会員情報

#### 6.1.2 売上・請求関連テーブル
- **uriage_denpyo**: 売上伝票
- **urikake_zan**: 売掛金残高
- **seikyu_zan**: 請求残高
- **nyukin_denpyo**: 入金伝票

#### 6.1.3 マスタテーブル
- **shohin_mst**: 商品マスタ
- **kaisya_mst**: 会社マスタ

#### 6.1.4 命名規則
- **テーブル名**: スネークケース（例: `seko_kihon_info`）
- **カラム名**: スネークケース（例: `kaisya_cd`, `seko_no`）
- **削除フラグ**: `delete_flg`（0:有効, 1:削除）
- **作成日時**: `_cre_ts`（作成タイムスタンプ）
- **更新日時**: `_upd_ts`（更新タイムスタンプ）

### 6.2 データベース接続

データベース接続は、Zend Frameworkの`Zend_Db`を使用しています。接続情報は`application/configs/application.ini`に定義されています。

## 7. AIツールのプロジェクトルールとコーディング規約

### 7.1 AIツールのプロジェクトルール

以下のルールを遵守してください：

#### 7.1.1 基本ルール
- **言語設定**: 「すべて日本語で回答してください」
- **詳細度**: 「詳細な説明を含めてください」
- **コメント**: 「コメントは日本語で記述してください」
- **出力形式**: コードブロックには適切な言語タグを付与
- **エラーハンドリング**: 「例外は適切に処理してください」
- 既存のデザインとコードスタイルを踏襲すること

#### 7.1.2 プロジェクト固有のコーディング規約
- **フレームワーク**: Zend Framework 1.x使用
- **テンプレートエンジン**: Smarty使用
- **データベース**: PostgreSQL使用
- **文字コード**: UTF-8
- **改行コード**: LF
- **インデント**: 4スペース

#### 7.1.3 機密情報の取り扱い制約
- 個人情報（故人名、遺族名、住所、電話番号等）は実際の値を使用しない
- サンプルデータ作成時は架空の情報を使用
- データベース接続情報やパスワードは含めない
- 会社固有の業務ロジックの詳細は外部に漏らさない

### 7.2 PHP コーディング規約

#### 7.2.1 命名規則
- **クラス名**: パスカルケース（例: `HenreidataController`）
- **メソッド名**: キャメルケース（例: `inputAction`）
- **変数名**: キャメルケース（例: `$userData`）
- **定数名**: アッパースネークケース（例: `MOUSHI_KBN_HOUJI`）
- **プライベートメソッド**: アンダースコア接頭辞（例: `_validateData`）

#### 7.2.2 コメント規約
- **クラスコメント**: PHPDocで記述、日本語で機能説明
- **メソッドコメント**: @param, @return, @throws を記述
- **行コメント**: 複雑なロジックには日本語で説明

#### 7.2.3 エラーハンドリング
- 例外は適切なMsi_Sys_Exception系クラスを使用
- データベースエラーは適切にキャッチして処理
- ユーザー向けエラーメッセージは日本語で記述

### 7.3 JavaScript コーディング規約

#### 7.3.1 命名規則
- **関数名**: キャメルケース（例: `saveData`）
- **変数名**: キャメルケース（例: `userInfo`）
- **定数**: アッパースネークケース（例: `MOUSHI_KBN_HOUJI`）
- **jQuery変数**: $接頭辞（例: `$element`）

#### 7.3.2 構造
- 即座実行関数パターンを使用: `$(function() { ... })`
- 名前空間: `appcst`、`appgjk`を使用
- コメント: 日本語で記述

### 7.4 Smarty テンプレート規約

#### 7.4.1 命名規則
- **ファイル名**: 小文字、ハイフン区切り
- **変数名**: スネークケース（例: `{$user_name}`）
- **テンプレート変数**: 日本語コメントで説明

#### 7.4.2 構造
- HTMLの構造は適切にインデント
- Smartyタグは適切に改行
- コメントは日本語で記述

### 7.5 CSS コーディング規約

- **クラス名**: ハイフン区切り（例: `input-area`）
- **ID名**: ハイフン区切り（例: `customer-info`）
- **コメント**: 日本語で記述


## 8. セキュリティとテスト

### 8.1 セキュリティ規約

#### 8.1.1 個人情報保護
- 故人情報、遺族情報は最高レベルの機密情報として扱う
- 開発・テスト時は必ず架空のデータを使用

#### 8.1.2 アクセス制御

システムでは、`application\configs\appRoleTable.php`でユーザーロールに基づく機能制限を実装しています。

**重要**: アクセス制御は各会社ごとのカスタマイズキー付きファイルで個別に設定されています。

##### カスタマイズキー対応のアクセス制御ファイル

アクセス制御設定は、カスタマイズキーシステムに対応しており、各会社の業務要件に応じて個別にロール権限を設定できます：

- **標準ファイル**: `application\configs\appRoleTable.php`
- **カスタマイズファイル**: `application\configs\appRoleTable.{カスタマイズキー}.php`

**例**:
- `appRoleTable.nise02.php` - 二世会（互助会版）用
- `appRoleTable.gonkiya.php` - ごんきや用
- `appRoleTable.saikane.php` - 埼玉金周用

##### ファイル読み込みの優先順位

システムは以下の順序でアクセス制御ファイルを読み込みます：

1. カスタマイズキーの配列を前から順に処理
2. 各カスタマイズキーに対応する`appRoleTable.{カスタマイズキー}.php`が存在するか確認
3. 最初に見つかったカスタマイズファイルを使用
4. カスタマイズファイルが見つからない場合は標準の`appRoleTable.php`を使用

この仕組みにより、各会社の業務フローや組織構造に合わせた柔軟なアクセス制御が実現されています。

##### ロール定義と権限

**1. system（システム管理者）**
- 権限: システム全体の管理機能
- アクセス可能機能:
  - `default.` - デフォルト機能
  - `system.` - システム管理機能
  - `dev.` - 開発機能
  - `mstr.` - マスタ管理機能
  - `saime.` - 債務管理機能
  - `sysmref.` - システム参照機能

**2. sysman（システムマネージャー）**
- 権限: 全機能アクセス（開発・サンプル機能除く）
- アクセス可能機能: `-ALL-`（全機能）
- 制限機能: `dev.`（開発機能）、`sample.`（サンプル機能）

**3. manager（マネージャー）**
- 権限: 管理業務全般
- アクセス可能機能:
  - `default.` - デフォルト機能
  - `mref.` - 参照機能
  - `juchu.customerinfo.` - 受注顧客情報
  - `juchu.mitsu.` - 受注見積
  - `juchu.houji.` - 受注法事
  - `hachu.` - 発注機能
  - `juchu.denpyo.` - 受注伝票
  - `juchu.etc` - 受注その他
  - `saiken.` - 債権管理
  - 各種帳票出力機能（売上、請求、入金等）
  - `gaiji.` - 外字機能
  - `sync.` - 同期機能
- 読み取り専用機能:
  - `juchu.bechulist.` - 別注品一覧
  - `juchu.bechudenpyo.` - 別注品伝票
  - 各種ダイアログ機能

**4. tanto（担当者）**
- 権限: 日常業務機能
- アクセス可能機能:
  - `default.` - デフォルト機能
  - `mref.` - 参照機能
  - `juchu.customerinfo.` - 受注顧客情報
  - `juchu.mitsu.` - 受注見積
  - `juchu.houji.` - 受注法事
  - `juchu.denpyo.` - 受注伝票
  - `hachu.` - 発注機能
  - `juchu.etc` - 受注その他
  - 一部帳票出力機能
  - `gaiji.` - 外字機能
  - `sync.` - 同期機能
- 読み取り専用機能:
  - 別注品関連機能
  - 債権請求関連機能

**5. jimu（事務）**
- 権限: 事務処理全般
- アクセス可能機能:
  - `default.` - デフォルト機能
  - `mref.` - 参照機能
  - `juchu.` - 受注機能全般
  - `hachu.` - 発注機能
  - `kanri.` - 管理機能
  - `saiken.` - 債権管理
  - `saimu.` - 債務管理
  - `zaiko.` - 在庫管理
  - `sukko.` - 出庫機能
  - `gaiji.` - 外字機能
  - `sync.` - 同期機能

**6. hachuinp（発注入力）**
- 権限: 発注業務特化
- アクセス可能機能:
  - `default.` - デフォルト機能
  - `mref.` - 参照機能
  - `hachu.` - 発注機能
  - `juchu.etc` - 受注その他
  - `saimu.` - 債務管理
  - `zaiko.` - 在庫管理
  - 各種帳票出力機能
  - `kanri.idodenpyo.` - 移動伝票管理
  - `gaiji.` - 外字機能
- 読み取り専用機能:
  - 受注関連機能（見積、別注品、法事、伝票等）

**7. ro（読み取り専用）**
- 権限: 参照のみ
- アクセス可能機能:
  - `default.` - デフォルト機能
  - `mref.` - 参照機能
- 読み取り専用機能:
  - 受注関連機能全般（見積、別注品、法事、伝票等）

**8. zaikoinp（在庫入力）**
- 権限: 在庫管理特化
- アクセス可能機能:
  - `default.` - デフォルト機能
  - `mref.` - 参照機能
  - `zaiko.` - 在庫管理
  - `kanri.idodenpyo.` - 移動伝票管理
- 読み取り専用機能:
  - 受注関連機能全般
  - 発注機能
  - 債務管理

**9. local（ローカル）**
- 権限: ローカル環境用
- アクセス可能機能:
  - `default.` - デフォルト機能
  - `mref.` - 参照機能
  - `juchu.` - 受注機能全般
  - `kanri.pdf` - PDF管理
  - `sync.` - 同期機能
  - `gaiji.` - 外字機能
- 制限機能: `dev.`（開発機能）、`sample.`（サンプル機能）

##### アクセス制御の仕組み

1. **機能ベースアクセス制御**: 各機能は「モジュール.コントローラ.」形式で定義
2. **階層的権限**: 上位ロールほど多くの機能にアクセス可能
3. **読み取り専用権限**: 一部機能は参照のみ許可
4. **制限機能**: 特定機能の明示的な禁止設定

#### 8.1.3 データ検証
- 入力データの適切なバリデーション
- SQLインジェクション対策
- XSS対策

### 8.2 テスト規約

#### 8.2.1 テストデータ
- 架空の故人名、遺族名を使用
- 実在しない住所、電話番号を使用

#### 8.2.2 テスト項目
- 機能テスト: 各業務フローの動作確認
- データ整合性テスト: データベースの整合性確認
- セキュリティテスト: アクセス制御の確認
- パフォーマンステスト: 大量データでの動作確認

## 9. 開発環境と運用環境

### 9.1 開発環境

- 開発環境では、`public_dev`ディレクトリ内のファイルを直接編集します
- CSSとJavaScriptは非圧縮版を使用します
- デバッグ情報の出力が有効

### 9.2 運用環境

- 運用環境では、`public`ディレクトリ内の圧縮・最適化されたファイルが使用されます
- ビルドプロセスにより、`public_dev`から`public`へファイルが生成されます
- エラーログの適切な管理
