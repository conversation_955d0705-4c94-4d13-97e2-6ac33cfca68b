<?php
/**
 * Logic_FileTrans_FileTransRirekiReg
 *
 * ファイル転送履歴(file_trans_rireki, file_trans_rireki_file) 登録
 * ファイル転送生成ファイル(file_trans_gen_file)で未転送ファイルをファイルタイプ（論理ファイル名）でまとめて返す
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/03/xx
 * @filesource 
 */

/**
 * ファイル転送履歴(file_trans_rireki, file_trans_rireki_file) 登録
 * 
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/03/xx
 */
class Logic_FileTrans_FileTransRirekiReg
{
    /**
     * ファイル転送履歴 登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      string $ft_type    転送種別
     * @param      array  $param      パラメタ    previousFile(未処理ファイル)
     * @return     array( 'result' => <ok|err|nop>, 'rireki_no' => integer|null, ['err_msg' => ...] ))       履歴番号
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function regFile( $db, $ft_type, $param )
    {
        $myObj = new static( $db );
        $rtn = $myObj->_regFile( $ft_type, $param );
        return $rtn;
    }

    /**
     * コンストラクタ
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db db
     */
    protected function __construct( $db )
    {
        $this->_db  = $db;
        list($this->_cur_ts, $this->_cur_user) = Logic_FileTrans_Utils::getTsAndUser($db);
    }

    /**
     * ファイル転送履歴 登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      string $ft_type    転送種別
     * @param      array  $param      パラメタ    previousFile(未処理ファイル)
     * @return     array( 'result' => <ok|err|nop>, 'rireki_no' => integer|null ))       履歴番号
     */
    protected function _regFile( $ft_type, $param )
    {
        $db = $this->_db;
        $this->_ft_type = $ft_type;
        $this->_param = $param;
        $exe_mode   = Msi_Sys_Utils::arrDefinedOrDefault($param, 'exe_mode', 'unknown');

        // 未処理ファイル
        $strPreviousFile = Msi_Sys_Utils::arrDefinedOrDefault($param, 'previousFile');
        $aPreviousFile = strlen($strPreviousFile) <= 0 ? [] : Msi_Sys_Utils::strArrayify_qw($strPreviousFile);
        $this->_aPreviousFile = $aPreviousFile;

        try {

            // ロック取得
            $bool = Logic_FileTrans_FileTransManUpd::lockTransFile($db, $ft_type);

            // エラーがあればエラーとして終了
            $condErr = array( 'ft_type'         => $ft_type,
                              '__raw_rireki_no' => 'rireki_no IS NULL', // ファイル転送されていない
                              'result'          => 'err',               // エラー
                              '__etc_orderby'   => array('file_id ASC') // 古い順
            );
            $errFiles = DataMapper_FileTrans_FileTransGenFile::find( $db, $condErr );
            if ( count($errFiles) > 0 ) { // エラーあり
                return $this->_regFileErrorReturn( $errFiles );
            }

            // ファイル転送されていないファイルを取得
            $cond = array( 'ft_type'         => $ft_type,
                           '__raw_rireki_no' => 'rireki_no IS NULL', // ファイル転送されていない
                           'result'          => 'ok',                  // 正常
                           '__raw_oid'       => 'oid IS NOT NULL',     // result=ok と同意だが
                           '__etc_orderby'   => array( 'file_id ASC' ) // 古い順
            );
            $genFiles = DataMapper_FileTrans_FileTransGenFile::find( $db, $cond );

            // 対象出力なし
            if ( count($genFiles) <= 0 ) {
                // 前回送信分で proc-done にできるものは設定
                $this->_setProcDoneInSendSt();

                return $this->_regFileNopReturn();
            }

            $exe_mode = $genFiles[0]['exe_mode'];

            $rireki_no = $db->getSequenceNextVal( 'file_trans_rireki_rireki_no_seq' );

            $dbFileTransRireki = array( 'rireki_no' => $rireki_no,
                                        'ft_type'   => $ft_type,
                                        'exe_mode'  => $exe_mode,
                                        'ext_cond'  => $genFiles[0]['ext_cond'],
                                        'result'    => $genFiles[0]['result'],
                                        'ft_ts'     => $this->_cur_ts,
                                        'ft_user'   => $this->_cur_user );

            $isNew1 = DataMapper_Utils::upsertEasy( $db, 'file_trans_rireki', $dbFileTransRireki );

            foreach ( $genFiles as $recFile ) {
                $this->_regFileOne( $recFile, $rireki_no );
            }

            // 前回送信分で proc-done にできるものは設定
            $this->_setProcDoneInSendSt();

            // ファイル転送管理(file_trans_man) を更新
            Logic_FileTrans_FileTransManUpd::updOk( $db, $ft_type, $dbFileTransRireki );

            $rtn = array( 'result' => 'ok', 'rireki_no' => $rireki_no );
            return $rtn;
        }
        catch ( Exception $e ) {
            $err = $e->getMessage();
            $errcode = 'err-file-rireki';
            $err_detail=$err;
            $rtn = Logic_FileTrans_FileTransRirekiErr::regErr($db, $errcode, $err_detail, $ft_type, $exe_mode);
            return $rtn;
        }
    }

    /**
     * ファイル転送履歴ファイル 登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      array   $recFileTransGenFile  (result:err の ファイル転送生成ファイル)
     * @param      integer $rireki_no    履歴番号
     * @return     void
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    protected function _regFileOne( $recFileTransGenFile, $rireki_no )
    {
        $db = $this->_db;
        $ft_type = $this->_ft_type;

        $file_type = $recFileTransGenFile['file_type'];
        $file_id   = $recFileTransGenFile['file_id'];
        $is_header_flg = !!$recFileTransGenFile['is_header_flg'];

        $dbFileTransRirekiFile = Msi_Sys_Utils::remapArrayFlat( $recFileTransGenFile, <<< END_OF_TXT
file_type   exe_mode   ext_cond
filename    size       mime        is_header_flg   result
output_cnt  errcode    err_detail  addinfo
END_OF_TXT
        );
        $dbFileTransRirekiFile['rireki_no'] = $rireki_no;

        $outBuf = $db->readBlobCont( $recFileTransGenFile['oid'] );

        // 同じ「転送種別+論理ファイル名」がすでにあれば連結して返す
        $condDup = array( 'rireki_no'      => $rireki_no,
                          'ft_type'        => $ft_type,
                          'file_type'      => $file_type,
                          'result'         => 'ok',
                          'trans_status'   => 'send',
                          '__etc_orderby'  => array( 'rireki_no DESC' ) );
        $recDup = DataMapper_FileTrans_FileTransRirekiFile::findOne( $db, $condDup );
        if ( $recDup ) { // 今回複数ファイルあり. 内容を連結して、oid などファイル属性を変更する
            $remainBuf = $db->readBlobCont( $recDup['oid'] ); // 未処理分のデータ
            $outBuf = Logic_FileTrans_Utils::concatBuf( $remainBuf, $outBuf, $is_header_flg ); // データを連結

            list($_size, $_mime, $_output_cnt) = Logic_FileTrans_Utils::getFileAttrWithBuf($outBuf);
            $recDup['size'] = $_size;
            $recDup['mime'] = $_mime;
            $recDup['output_cnt'] = $_output_cnt;

            $this->_chgFileTransRirekiFileOidEtc( $db, $outBuf, $recDup );
            $this->_updFileTransGenFile( $db, $file_id, $rireki_no );

            return;
        }

        // 前回送信分があれば取得
        $condPre = array( 'ft_type'        => $ft_type,
                          'file_type'      => $file_type,
                          'result'         => 'ok',
                          'trans_status'   => 'send',           // 連携ステータス:転送済み
                          '__raw_next_rireki_no' => 'next_rireki_no IS NULL',
                          '__etc_orderby'   => array( 'rireki_no DESC' ) );
        $recPre = DataMapper_FileTrans_FileTransRirekiFile::findOne( $db, $condPre );
        if ( $recPre ) { // 前回送信分あり
            $_file_type_pre = $recPre['file_type'];
            if ( in_array($_file_type_pre, $this->_aPreviousFile) ) { // 前回送信分が未処理
                $remainBuf = $db->readBlobCont( $recPre['oid'] ); // 未処理分のデータ
                $outBuf = Logic_FileTrans_Utils::concatBuf( $remainBuf, $outBuf, $is_header_flg ); // データを連結

                list($_size, $_mime, $_output_cnt) = Logic_FileTrans_Utils::getFileAttrWithBuf($outBuf);
                $dbFileTransRirekiFile['size'] = $_size;
                $dbFileTransRirekiFile['mime'] = $_mime;
                $dbFileTransRirekiFile['output_cnt'] = $_output_cnt;
                $dbFileTransRirekiFile['pre_rireki_no'] = $recPre['rireki_no'];
                
                $this->_updFileTransRirekiFileNextDone($db, $recPre, $rireki_no); // 未処理分のファイル転送履歴を更新
            } else { // 前回送信分は処理された
                $this->_updFileTransRirekiFileProcDone($db, $recPre); // 前回送信分は処理済
            }
        }

        $tmpFile = Logic_FileTrans_Utils::writeTmpFile($outBuf);
        $oid = $db->writeBlob($tmpFile);

        $dbFileTransRirekiFile['oid'] = $oid;

        $dbFileTransRirekiFile['trans_status'] = 'send';

        $isNew2 = DataMapper_Utils::upsertEasy( $db, 'file_trans_rireki_file', $dbFileTransRirekiFile );

        $this->_updFileTransGenFile( $db, $file_id, $rireki_no );

        return;
    }

    /**
     * ファイル転送履歴 エラー登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      array     (result:err の ファイル転送生成ファイル)のリスト
     * @return     integer   履歴番号
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    protected function _regFileErrorReturn( $errFiles )
    {
        $db = $this->_db;
        $rtn = Logic_FileTrans_FileTransRirekiErr2::regErrs( $db, $errFiles );
        return $rtn;
    }

    /**
     * ファイル転送履歴 データなし処理
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @return     null         履歴番号
     */
    protected function _regFileNopReturn()
    {
        $db = $this->_db;
        $ft_type = $this->_ft_type;

        // ファイル転送管理(file_trans_man) を更新
        Logic_FileTrans_FileTransManUpd::updOkNop( $db, $ft_type );

        $rtn = array( 'result' => 'nop', 'rireki_no' => null );
        return $rtn;
    }

    /**
     * ファイル転送生成ファイル.転送履歴番号(file_trans_gen_file.rireki_no)を更新する
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      integer    $file_id    生成ファイル番号
     * @param      integer    $rireki_no  転送履歴番号
     * @return     void
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    protected function _updFileTransGenFile( $db, $file_id, $rireki_no )
    {
        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE file_trans_gen_file
   SET rireki_no = :rireki_no
 WHERE file_id = :file_id
END_OF_SQL
                                 , array('file_id'   => $file_id,
                                         'rireki_no' => $rireki_no) );
        if ( $cnt !== 1 ) {
            throw new Msi_Sys_Exception_LogicException( sprintf("(e79f34b7)file_trans_gen_file(file_id:%d) update error", $file_id) );
        }
    }

    /**
     * ファイル転送履歴ファイル(file_trans_rireki_file)の添付ファイル属性を変更する
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $recPre            ファイル転送履歴ファイルデータ
     * @param      integer    $next_rireki_no    引き継いだ履歴番号
     * @return     void
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    protected function _chgFileTransRirekiFileOidEtc( $db, $outBuf, $recDup )
    {
        extract( $recDup );

        // 既存 oid 削除
        $oidCur = $recDup['oid'];
        DataMapper_Utils::lo_unlink_safe($db, $oidCur);

        // 新 oid 登録
        $tmpFile = Logic_FileTrans_Utils::writeTmpFile($outBuf);
        $oidNew = $db->writeBlob($tmpFile);

        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE file_trans_rireki_file
   SET oid = :oidNew
      ,size = :size
      ,mime = :mime
      ,output_cnt = :output_cnt
 WHERE rireki_no = :rireki_no
   AND file_type = :file_type
END_OF_SQL
                                 , array( 'oidNew'     => $oidNew,
                                          'size'       => $size,
                                          'mime'       => $mime,
                                          'output_cnt' => $output_cnt,
                                          'rireki_no'  => $rireki_no,
                                          'file_type'  => $file_type ) );
        if ( $cnt !== 1 ) {
            throw new Msi_Sys_Exception_LogicException( sprintf("(22a70f0e)file_trans_rireki_file(rireki_no:%d) update error", $rireki_no) );
        }
    }

    /**
     * ファイル転送履歴ファイル(file_trans_rireki_file)を trans_status = 'next-done' に更新する
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $recPre            ファイル転送履歴ファイルデータ
     * @param      integer    $next_rireki_no    引き継いだ履歴番号
     * @return     void
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    protected function _updFileTransRirekiFileNextDone( $db, $recPre, $next_rireki_no )
    {
        $rireki_no = $recPre['rireki_no'];
        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE file_trans_rireki_file
   SET trans_status = 'next-done'
      ,next_rireki_no = :next_rireki_no
 WHERE rireki_no = :rireki_no
   AND file_type = :file_type
END_OF_SQL
                                 , array('next_rireki_no' => $next_rireki_no,
                                         'rireki_no'      => $rireki_no,
                                         'file_type'      => $recPre['file_type']) );
        if ( $cnt !== 1 ) {
            throw new Msi_Sys_Exception_LogicException( sprintf("(142b8bcb)file_trans_rireki_file(rireki_no:%d) update error", $rireki_no) );
        }
    }

    /**
     * ファイル転送履歴ファイル(file_trans_rireki_file)を trans_status = 'proc-done' に更新する
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $recPre            ファイル転送履歴ファイルデータ
     * @return     void
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    protected function _updFileTransRirekiFileProcDone( $db, $recPre )
    {
        $rireki_no = $recPre['rireki_no'];
        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE file_trans_rireki_file
   SET trans_status = 'proc-done'
 WHERE rireki_no = :rireki_no
   AND file_type = :file_type
END_OF_SQL
                                 , array('rireki_no'      => $rireki_no,
                                         'file_type'      => $recPre['file_type']) );
        if ( $cnt !== 1 ) {
            throw new Msi_Sys_Exception_LogicException( sprintf("(f6522187)file_trans_rireki_file(rireki_no:%d) update error", $rireki_no) );
        }
    }

    /**
     * 前回送信分で proc-done にできるものは設定
     *
     * <AUTHOR> Mihara
     * @since      2025/06/03
     * @return     更新件数
     */
    protected function _setProcDoneInSendSt()
    {
        $db = $this->_db;
        $ft_type = $this->_ft_type;
        $aPreviousFile = $this->_aPreviousFile;
        $curr_req_id = $db->getOneVal( "SELECT CURRVAL('x_req_id_seq')" ); // この要求での更新データは除外する
        $cnt = 0;

        // 前回送信分を取得
        $condPre = array( 'ft_type'        => $ft_type,
                          // 'file_type'      => $file_type,
                          'result'         => 'ok',
                          'trans_status'   => 'send',           // 連携ステータス:転送済み
                          '_req_id'        => array('<>', $curr_req_id),   // 今回データは対象外
                          '__raw_next_rireki_no' => 'next_rireki_no IS NULL',
                          '__etc_orderby'   => array( 'rireki_no DESC' ) );
        $_aRecPre = DataMapper_FileTrans_FileTransRirekiFile::find( $db, $condPre );
        foreach ( $_aRecPre as $recPre ) {
            $_file_type_pre = $recPre['file_type'];
            if ( ! in_array($_file_type_pre, $this->_aPreviousFile) ) { // 前回送信分は処理済み
                $this->_updFileTransRirekiFileProcDone($db, $recPre); // 前回送信分を処理済に
                $cnt++;
            }
        }

        return $cnt;
    }

}
