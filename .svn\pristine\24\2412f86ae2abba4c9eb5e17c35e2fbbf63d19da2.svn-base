<?php
/**
 * Logic_FileTrans_Utils
 *
 * ファイル転送 ユーティリティ関数
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/03/xx
 * @filesource 
 */

/**
 * ファイル転送 ユーティリティ関数
 * 
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/03/xx
 */
class Logic_FileTrans_Utils
{
    /**
     * ファイル内容を SJIS-win に変換して一時ファイルに格納して返す
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      string   $buf
     * @param      string   $outEnc  指定時には文字コード変換も行う. ex. SJIS-win
     * @return     string   一時ファイル名
     * @throws     Msi_Sys_Exception_LogicException
     */
    public static function writeTmpFileSJis($buf)
    {
        $tmpFile = Logic_FileTrans_Utils::writeTmpFile($buf, 'SJIS-win');
        return $tmpFile;
    }

    /**
     * ファイル内容を渡して一時ファイルに格納して返す
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      string   $buf
     * @param      string   $outEnc  指定時には文字コード変換も行う. ex. SJIS-win
     * @return     string   一時ファイル名
     * @throws     Msi_Sys_Exception_LogicException
     */
    public static function writeTmpFile($buf, $outEnc=null)
    {
        if ( $outEnc ) {
            $buf = mb_convert_encoding( $buf, $outEnc );
        }

        $tmpFile = Msi_Sys_Utils::tempnam();
        $isOk = Msi_Sys_Utils::put_contents($tmpFile, $buf);
        if ( !$isOk ) {
            throw new Msi_Sys_Exception_LogicException("(598e1911)ファイル出力に失敗しました");
        }
        return $tmpFile;
    }

    /**
     * ファイルの filesize や mime など属性を返す
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      string   $fileName
     * @return     array( <file size>, <mime type>, <line_cnt> )
     */
    public static function getFileAttr($fileName)
    {
        $size = filesize($fileName);
        $mime = Msi_Sys_Utils::guessMimeType( $fileName );
        $lineCnt = Logic_FileTrans_Utils::getLineCntInFile($fileName);
        $rtn = array( $size, $mime, $lineCnt );
        return $rtn;
    }

    /**
     * ファイルの filesize や mime など属性を返す(buf 版)
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      string   $buf
     * @return     array( <file size>, <mime type>, <line_cnt> )
     */
    public static function getFileAttrWithBuf($buf)
    {
        $tmpFile = Logic_FileTrans_Utils::writeTmpFile($buf);
        return Logic_FileTrans_Utils::getFileAttr($tmpFile);
    }

    /**
     * 文字列の行数を返す
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      string   $buf
     * @return     integer|null
     */
    public static function getLineCnt($buf)
    {
        if ( $buf === null ) {
            return null;
        }
        $lines = explode("\n", trim($buf));
        $lineCount = count($lines);
        return $lineCount;
    }

    /**
     * ファイルの行数を返す
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      string   $fileName
     * @return     integer|null
     */
    public static function getLineCntInFile($fileName)
    {
        $cont = Msi_Sys_Utils::get_contents($fileName);
        if ( $cont === false ) {
            return null;
        }
        return Logic_FileTrans_Utils::getLineCnt($cont);
    }

    /**
     * 現タイムスタンプと現ログインユーザを返す
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @return     array(ts, user)
     */
    public static function getTsAndUser($db)
    {
        $ts   = $db->getOneVal( "select CURRENT_TIMESTAMP" );
        $user = Msi_Sys_Utils::getUserCd();
        $rtn = array( $ts, $user );
        return $rtn;
    }

    /**
     * 出力ファイル用のデータを連結して返す
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      string   $buf1  前に置かれる
     * @param      string   $buf2  後に置かれる
     * @param      boolean  $isHeaderLineRemoved  true なら $buf2 の１行目を削除して連結する
     * @return     string
     */
    public static function concatBuf($buf1, $buf2, $isHeaderLineRemoved=false)
    {
        if ( strlen($buf1) <= 0 ) {
            return $buf2;
        }

        if ( strpos($buf1, "\r\n") !== false ) {
            $nlChar = "\r\n";
        } else if ( strpos($buf1, "\r") !== false ) {
            $nlChar = "\r";
        } else if ( strpos($buf1, "\n") !== false ) {
            $nlChar = "\n";
        } else {
            $nlChar = "\r\n";
        }

        if ( $isHeaderLineRemoved ) {
            $lines = explode("\n", $buf2);
            array_shift($lines); // 先頭行を削除
            $buf2 = implode("\n", $lines);
        }

        $buf1 = rtrim($buf1, "\r\n");
        $newBuf = $buf1 . $nlChar . $buf2;
        return $newBuf;
    }

    /**
     * 転送履歴番号のファイルを zip の一時ファイルにまとめる
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      integer    $rireki_no
     * @return     string     一時ファイル名
     */
    public static function genZipFileWithRirekiNo($db, $rireki_no)
    {
        $zipFileName = Msi_Sys_Utils::tempnam();

        $cond = array( 'rireki_no' => $rireki_no,
                       'result'    => 'ok',
                       '__etc_orderby' => array('disp_no ASC', 'file_type ASC') );
        $sel = DataMapper_FileTrans_FileTransRirekiFile::find( $db, $cond );

        $pFiles = array();
        foreach ( $sel as $rec ) {
            $myFileName = $rec['file_type'];
            $myTempFile = $db->readBlobIntoTemp( $rec['oid'] );
            $pFiles[ $myTempFile ] = $myFileName;
        }

        $zipFilePath = App_FileTransUtils::createZipArchive($pFiles, $zipFileName);
        if ( $zipFilePath === false ) {
            throw new Exception("(d5c8b64b)createZipArchive error");
        }

        return $zipFilePath;
    }

    // /**
    //  * ファイル転送生成ファイル(file_trans_gen_file)の直前の送信レコードを返す
    //  *
    //  * <AUTHOR> Mihara
    //  * @since      2025/03/xx
    //  * @param      Msi_Sys_Db $db
    //  * @param      string     $ft_type    転送種別
    //  * @param      string     $file_type  ファイルタイプ（論理ファイル名）
    //  * @return     array
    //  */
    // public static function getRecLastFileTransGenFile($db, $ft_type, $file_type)
    // {
    //     // 前回送信分があれば取得
    //     $condPre = array( 'ft_type'        => $ft_type,
    //                       'file_type'      => $dbFileTransRirekiFile['file_type'],
    //                       'result'         => 'ok',
    //                       '__etc_orderby'  => array('file_id ASC') );
    //     // 'trans_status'   => 'send',           // 連携ステータス:転送済み
    //     // '__raw_next_rireki_no' => 'next_rireki_no IS NULL' );           // 結合先履歴番号
    //     $recPre = DataMapper_FileTrans_FileTransRirekiFile::findOne( $db, $condPre );

    //     return $recPre;
    // }


    // /**
    //  * ファイル転送履歴ファイル(file_trans_rireki_file)の trans_status=send のレコードを返す
    //  *
    //  * <AUTHOR> Mihara
    //  * @since      2025/03/xx
    //  * @param      Msi_Sys_Db $db
    //  * @param      string $ft_type    転送種別
    //  * @param      string $file_type  論理ファイル名
    //  * @return     mixed{array|null}
    //  */
    // public static function getFtrfRecRemain($db, $ft_type, $file_type)
    // {
    //     // 未処理分があれば取得
    //     $condRemains = array( 'ft_type'        => $ft_type,
    //                           'file_type'      => $file_type,
    //                           'result'         => 'ok',
    //                           'trans_status'   => 'send',           // 連携ステータス:転送済み
    //                           '__raw_next_rireki_no' => 'next_rireki_no IS NULL' );           // 結合先履歴番号
    //     $recRemains = DataMapper_FileTrans_FileTransRirekiFile::findOne( $db, $condRemains );
    //     return $recRemains;
    // }

//     /**
//      * _mod_user(_cre_user) 設定値から担当者名を返す
//      *
//      * <AUTHOR> Mihara
//      * @since      2025/03/xx
//      * @param      string   $_mod_user
//      * @param      Msi_Sys_Db $db
//      * @return     mixed(string|null)
//      */
//     public static function getTantoNmFromModUser($mod_user, $db=null)
//     {
//         if ( $db === null ) $db = Msi_Sys_DbManager::getMyDb();

//         $rec = $db->easySelOne( <<< END_OF_SQL
// SELECT tn.tanto_nm AS tanto_nm
//   FROM login_mst lm
//   LEFT JOIN tanto_mst tn
//     ON tn.tanto_cd = lm.tanto_cd
//  WHERE lm.login_cd = regexp_replace(:mod_user, '[^.]+\.', '')
// END_OF_SQL
//                                 , array('mod_user' => $mod_user) );

//         if ( $rec === null ) {
//             return null;
//         }

//         $rtnTxt = $rec['tanto_nm'];
//         return $rtnTxt;
//     }

//     /**
//      * _mod_user, _mod_ts 設定値から最終更新情報を返す
//      *
//      * <AUTHOR> Mihara
//      * @since      2025/03/xx
//      * @param      Msi_Sys_Db $db
//      * @param      string   $_mod_ts
//      * @param      string   $_mod_user
//      * @return     mixed(string|null)
//      */
//     public static function getLastUpdInfo($db, $mod_ts, $mod_user)
//     {
//         $tanto_nm = Logic_FileTrans_Utils::getTantoNmFromModUser($mod_user, $db);

//         if ( $tanto_nm === null ) {
//             $tanto_nm = $mod_user; // XXX
//         }

//         $rtnStr = sprintf('最終更新：%s %s', $mod_ts, $tanto_nm);

//         return $rtnStr;
//     }

    /**
     * ファイル転送履歴ファイル(file_trans_rireki_file)の trans_status を強制変更する
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      integer    $rireki_no  履歴番号
     * @param      string     $file_type  ファイルタイプ
     * @param      string     $trans_status  設定する連携ステータス   通常、proc-done, cancel だけ
     * @return     boolean    true:設定した false:設定できず
     */
    public static function updTransStInFileTransRirekiFile( $db, $rireki_no, $file_type, $trans_status )
    {
        $cnt = $db->easyExecute( <<< END_OF_SQL
UPDATE file_trans_rireki_file
   SET trans_status = :trans_status
 WHERE rireki_no = :rireki_no
   AND file_type = :file_type
END_OF_SQL
                                 , array('trans_status'   => $trans_status,
                                         'rireki_no'      => $rireki_no,
                                         'file_type'      => $file_type) );
        return $cnt === 1;
    }

    /**
     * 与えられた履歴番号をもつ、ファイル転送履歴ファイル(file_trans_rireki_file)のデータ件数を返す
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      integer    $rireki_no  履歴番号
     * @return     integer    データ件数
     */
    public static function countFileTransRirekiFileWithRirekiNo( $db, $rireki_no )
    {
        $cnt = $db->getOneVal( 'SELECT COUNT(*) FROM file_trans_rireki_file WHERE rireki_no=?', array($rireki_no) );
        return $cnt;
    }

    /**
     * ファイル転送履歴ファイル(file_trans_rireki_file)と関連データを物理削除(DELETE)する
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      integer    $rireki_no  履歴番号
     * @param      string     $file_type  ファイルタイプ
     * @return     void
     */
    public static function delFileTransRirekiFileEx( $db, $rireki_no, $file_type )
    {
        $cond = array( 'rireki_no'      => $rireki_no,
                       'file_type'      => $file_type );
        $recFtrf = DataMapper_FileTrans_FileTransRirekiFile::findOne( $db, $cond );
        if ( $recFtrf === null ) {
            return;
        }

        // ファイル転送履歴ファイル(file_trans_rireki_file)を削除
        static::delFileTransRirekiFile( $db, $rireki_no, $file_type, $recFtrf );

        // ファイル転送生成ファイル(file_trans_gen_file)を削除
        $condGen = array( 'rireki_no' => $rireki_no,
                          'ft_type'   => $recFtrf['ft_type'],
                          'file_type' => $file_type );
        $selGenFiles = DataMapper_FileTrans_FileTransGenFile::find( $db, $condGen );
        foreach ( $selGenFiles as $_recGen ) {
            static::delFileTransGenFile( $db, $_recGen['file_id'], $_recGen );
        }

        $cntFtrf = static::countFileTransRirekiFileWithRirekiNo( $db, $rireki_no );
        if ( $cntFtrf > 0 ) {
            return;
        }

        // ファイル転送履歴(file_trans_rireki)を削除
        static::delFileTransRireki( $db, $rireki_no );
    }

    /**
     * ファイル転送生成ファイル(file_trans_gen_file) 削除
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      integer        $file_id  生成ファイル番号
     * @param      array          $rec      file_trans_gen_file レコード
     * @return     void
     */
    public static function delFileTransGenFile( $db, $file_id, $rec=null )
    {
        if ( $rec === null ) {
            $rec = DataMapper_FileTrans_FileTransGenFile::findOne( $db, array('file_id' => $file_id) );
            if ( $rec === null ) {
                return;
            }
        }

        $file_id = $rec['file_id'];
        $oid = $rec['oid'];

        DataMapper_Utils::lo_unlink_safe($db, $oid);

        $cnt2 = $db->easyExecute( <<< END_OF_SQL
DELETE FROM file_trans_gen_file
 WHERE file_id = :file_id
END_OF_SQL
                                  , array('file_id' => $file_id) );
    }

    /**
     * ファイル転送履歴(file_trans_rireki) 削除
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      integer    $rireki_no  履歴番号
     * @return     void
     */
    public static function delFileTransRireki( $db, $rireki_no )
    {
        $cnt = $db->easyExecute( <<< END_OF_SQL
DELETE FROM file_trans_rireki
 WHERE rireki_no = :rireki_no
END_OF_SQL
                                  , array('rireki_no' => $rireki_no) );
    }

    /**
     * ファイル転送履歴ファイル(file_trans_rireki_file) 削除
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      integer    $rireki_no  履歴番号
     * @param      string     $file_type  ファイルタイプ
     * @param      array      $rec        file_trans_rireki_file レコード
     * @return     void
     */
    public static function delFileTransRirekiFile( $db, $rireki_no, $file_type, $rec=null )
    {
        if ( $rec === null ) {
            $cond = array( 'rireki_no'      => $rireki_no,
                           'file_type'      => $file_type );
            $rec = DataMapper_FileTrans_FileTransRirekiFile::findOne( $db, $cond );
            if ( $rec === null ) {
                return;
            }
        }

        $oid = $rec['oid'];
        DataMapper_Utils::lo_unlink_safe($db, $oid);

        $cnt2 = $db->easyExecute( <<< END_OF_SQL
DELETE FROM file_trans_rireki_file
 WHERE rireki_no = :rireki_no
   AND file_type = :file_type
END_OF_SQL
                                 , array('rireki_no'      => $rireki_no,
                                         'file_type'      => $file_type ) );
    }

    /**
     * 抽出条件(ext_cond)があれば、JSON にして返す
     *
     * <AUTHOR> Mihara
     * @since      2025/06/03
     * @param      array    $param
     * @return     mixed (string|null)
     */
    public static function getExtCondJsonStr($param)
    {
        $json = null;
        if ( is_array($param) && array_key_exists('ext_cond', $param) ) {
            $v = $param['ext_cond'];
            $json = Msi_Sys_Utils::json_encode($v);
            if ( $json === false ) {
                $json = null;
            }
        }
        return $json;
    }
    
}
