{* cf. zaiko/idodenpyo/order *}
{include file="fdn_head_std.tpl"}
{include file="fdn_header_0.tpl"}

<form id="my-form-id" method="post" class="{$ctxt_readonly}">
    <div id="main">
        <div id="order" style="display:none">
            <div class="page-title"><span>領収書発行</span></div>
            <div class="my-clearfix"></div>

            <div id="ryoshu-header" class="my-clearfix">
                <div id="seikyu-info">
                    <table id="seikyu-tbl">
                        <tr>
                            <th class="col1-width" >請求先名</th>
                            <td class="col2-width"><span id="seikyu_nm"></span><span> 様</span></td>
                            <th rowspan="2" class="col3-width">請求先住所</th>
                            <td class="col4-width" id="seikyu_addr1"></td>
                            <th class="col5-width">請求金額</th>
                            {*
                            <th class="col6-width">入金金額</th>
                            <th class="col7-width">請求残高</th>
                            *}
                            <th class="col8-width">領収書発行額</th>
                        </tr>
                        <tr>
                            <th class="col1-width">TEL</th>
                            <td class="col2-width" id="seikyu_tel" ></td>
                            <td class="col4-width"  id="seikyu_addr2"></td>
                            <td class="col5-width commafy" id="seikyu_gaku"></td>
                            {*
                            <td class="col6-width commafy" id="nyukin_gaku"></td>
                            <td class="col7-width commafy" id="seikyu_zan"></td>
                            *}
                            <td class="col8-width commafy" id="ryoshu_hako_gaku"></td>
                        </tr>
                    </table>
                    {*
                    <table id="nyukin-tbl">
                        <tr>
                            <th class="col6-width">入金伝票No</th>
                            <th class="col6-width">入金日</th>
                            <th class="col6-width">入金方法</th>
                            <th class="col6-width">入金金額</th>
                            <th class="col6-width">調整額</th>
                        </tr>
                        <tr>
                            <td class="col6-width" id="nyukin_den_no"></td>
                            <td class="col6-width" id="nyukin_ymd"></td>
                            <td class="col6-width" id="pay_method_nm"></td>
                            <td class="col6-width commafy" id="nyukin_prc"></td>
                            <td class="col6-width commafy" id="chosei_prc"></td>
                        </tr>
                    </table>
                    *}
                </div>
            </div><!-- /#ryoshu-header -->

            <!-- 明細行 (内訳データ) -->
            <div class="items" style="overflow:hidden">
                <div class="header" style="overflow-y:scroll;overflow-x:hidden;">
                    <table>
                        <tr>
                            <td class="row" rowspan="2">選択</td>
                            <td class="t-ryoshusho_no">No.</td>
                            <td class="t-atena" rowspan="2">宛名</td>
                            <td class="t-kingaku-all" colspan="2">金額
                                <a href="javascript:void(0)" id="kin-imp-btn" title="残額を現金項目に取り込みます"><span class="glyphicon glyphicon-download"></span></a>
                            </td>
                            <td class="t-gokei">合計</td>
                            <td class="t-inshi" rowspan="2">印紙税額</td>
                            <td class="t-hako_date">発行日時</td>
                            <td class="t-haki_date">破棄日時</td>
                        </tr>
                        <tr>
                            <td class="t-hako_kbn">発行区分／再発行文字印刷</td>
                            <td class="t-tadashikaki">但し書き</td>
                            <td class="t-syukin_tanto">集金担当者</td>
                            <td class="t-jimu_tanto">事務担当者</td>
                            <td class="t-biko" colspan="2">備考</td>
                        </tr>
                    </table>
                </div><!-- /.header -->
                <div class="list"  style="overflow-y:scroll;overflow-x:hidden;height:80%;padding-bottom:1px;">
                    <table id="dtl-table-id"></table>
                </div><!-- /.list -->
            </div><!-- /.items -->

            {* 軽減税率対応 keigen *}
            <div id="ryoshu-footer" class="my-clearfix">
                <div id="seikyu-info-2"></div>
            </div><!-- /#ryoshu-header -->

            <!-- 処理ボタン -->
            <div class="buttons">
                <input type="button" name="btn_save"    id="btn_save"   value="領収書発行"          style="width:180px"/>
                <input type="button" name="btn_reissue" id="btn_reissue" value="再発行としてコピー" style="width:180px"/>
                <input type="button" name="btn_cancel"  id="btn_cancel" value="回収(破棄)"          style="width:150px"/>
            </div><!-- /.buttons -->
        </div><!-- /.order -->
    </div><!-- /#main -->
</form><!-- /#my-form-id -->

<!-- 明細行 テンプレート -->
<script type="text/template" id="item-template">
<!-- tbody -->
<tr>
    <td class="row" rowspan="2">
        <span class="radio_set">
            <label for="ryoshu_check_<%=idx%>" class="lbl_ryoshu_check lbl_com_check lbl_select_kbn "></label>
            <input name="ryoshu_check_<%=idx%>" id="ryoshu_check_<%=idx%>" class="ryoshu_check cursor-pointer" type="checkbox" value="1" />
        </span>
    </td>
    <td class="t-ryoshusho_no"><input type="text" class="ryoshusho_no my-txt90 text-center" value="" readonly /></td>
    <td class="t-atena" rowspan="2"><input type="text" class="my-txt85 atena my-select-all" value="" /><span>様</span></td>
    <td class="t-kingaku-all" colspan="2">
        <div class="div-kin1">
            <input type="text" class="way_1 kin-way" value="" />
            <input type="text" class="zei_1 kin-zei" value="" />
            <input type="text" class="kin_1 kin-kin my-select-all my-bold commafy to_alpha_num" value="" />
        </div>
        <div class="div-kin2">
            <input type="text" class="way_2 kin-way" value="" />
            <input type="text" class="zei_2 kin-zei" value="" />
            <input type="text" class="kin_2 kin-kin my-select-all my-bold commafy to_alpha_num" value="" />
        </div>
        <div class="div-kin3">
            <input type="text" class="way_3 kin-way" value="" />
            <input type="text" class="zei_3 kin-zei" value="" />
            <input type="text" class="kin_3 kin-kin my-select-all my-bold commafy to_alpha_num" value="" />
        </div>
        <div class="div-kin4">
            <input type="text" class="way_4 kin-way" value="" />
            <input type="text" class="zei_4 kin-zei" value="" />
            <input type="text" class="kin_4 kin-kin my-select-all my-bold commafy to_alpha_num" value="" />
        </div>
    </td>
    <td class="t-gokei"><input type="text" class="my-txt90 gokei text-right my-select-all my-bold commafy" tabindex="-1" readonly /></td>
    <td class="t-inshi" rowspan="2"><input type="text" class="my-txt90 inshi_prc text-right my-select-all my-bold commafy" tabindex="-1" readonly /></td>
    <td class="t-hako_date"><input type="text" class="my-txt85 hako_date text-center" tabindex="-1" readonly /></td>
    <td class="t-haki_date"><input type="text" class="my-txt85 haki_date text-center" tabindex="-1" readonly />
        <input type="text" style="display:none" class="my-txt85 haki_user" value="" readonly />
    </td>
</tr>
<tr>
    <td class="t-hako_kbn">
        <label class="hako_kbn"></label>
        <input type="hidden" style="width:50%;float:right" class="msi-picker disp_kbn small" data-picker-kind="cdNm" data-picker-param="kind2:'xx40',placeholder:''" disabled="disabled"/>
    </td>
    <td class="t-tadashikaki">
        <input type="hidden" style="width:30%" class="tadashikaki_cd small"/>
        <input type="text" class="my-txt65 tadashikaki" value="" maxlength="40" style="float:right"/>               
    </td>    
    <td class="t-syukin_tanto syukin_tanto-ref" >
        <input type="text" class="syukin_tanto_nm my-txt75" value="" readonly />
        <input type="hidden" class="syukin_tanto_cd" value=""/>
        <div class="label dlg_tanto tanto-ref my-readonly-hidden cursor-pointer"></div>
    </td>
    <td class="t-jimu_tanto"><input type="text" class="my-txt90 jimu_tanto_nm" value="" readonly></td>
    <td class="t-biko" colspan="2"><input type="text" class="my-txt95 biko" value="" maxlength="40" /></td>
</tr>
<!-- /tbody -->
</script><!-- /#item-template -->

<script id="my-data-init-id" type="application/json">
{$mydata_json|smarty:nodefaults}
</script>

{include file="fdn_footer_std.tpl"}
