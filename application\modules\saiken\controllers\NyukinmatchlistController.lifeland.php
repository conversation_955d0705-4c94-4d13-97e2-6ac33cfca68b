<?php

/**
 * Saiken_NyukinmatchlistController
 *
 * 入金消込リスト出力画面　コントローラクラス
 *
 * @category   App
 * @package    controller\Saiken
 * <AUTHOR> Mogi
 * @since      2021/01/xx
 * @filesource 
 */
class Saiken_NyukinmatchlistController extends Msi_Zend_Controller_Action {

    /**
     * 入金消込リスト出力画面 コントローラクラス
     *
     * @category   App
     * @package    controller\saiken
     * <AUTHOR> Mogi
     * @since      2021/01/xx
     */
    public function indexAction() {
        // 利用 JavaScript 設定
        App_Smarty::pushJsFile([
            'app/saiken.nyukinmatchlist.js'
        ]);

        // 利用 CSS 設定
        App_Smarty::pushCssFile([
            'app/kanri.print.siji.css',
            'app/saiken.nyukinmatchlist.css',
        ]);

        //画面初期表示時のデータ取得
        $data = $this->_getInitData();
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
    }

    /**
     * 画面初期表示用データ取得
     *
     * <AUTHOR> Mogi
     * @since      2021/01/xx
     */
    private function _getInitData() {
        $db = Msi_Sys_DbManager::getMyDb();
        // 会社取得
        $dataKaisya = $db->easySelect(<<< END_OF_SQL
SELECT
    bumon_cd AS id
    ,bumon_lnm AS text
FROM bumon_mst
WHERE bumon_kbn = 0
ORDER BY bumon_cd          
END_OF_SQL
        );
        $default_kaisya = null;
        if ($dataKaisya[0]['id']) {
            $default_kaisya = $dataKaisya[0]['id'];
        }
        $hallData = App_Utils::getDfltHallArea();
        $dataApp = array(
            'kaisya_cd' => $hallData[1],
            'torikomi_ymd' => Msi_Sys_Utils::getDate(), // 取込日
        );

        $data = array(
            'dataKaisya' => $dataKaisya,
            'dataApp' => $dataApp,
        );

        return $data;
    }

}
