<?php

/**
 * Cale_YoyakuController
 *
 * 施設予約 コントローラクラス
 *
 * @category   App
 * @package    controllers\Cale
 * <AUTHOR> Sai
 * @since      2020/xx/xx
 * @filesource 
 */

/**
 * 施設予約 コントローラクラス
 *
 * @category   App
 * @package    controllers\Cale
 * <AUTHOR> Sai
 * @since      2020/xx/xx
 */
class Cale_YoyakuController extends Msi_Zend_Controller_Action {

    private $s_display_term = 18; // 表示期間（表示終了時間 - 表示開始時間）
    private $s_start_time = '06:00'; // 表示開始時間
    private $s_end_time = '24:00'; // 表示終了時間
    private $bunrui_key = '9'; // 予約分類 デフォルト9:なし（イベント等）

    /**
     * TODO
     * key  : 予約分類　(p_yoyaku_bunrui)　0:搬送 1:葬儀 2:法事 9:なし（イベント等）
     * r_kbn：
     *   key: 施設区分　1:式場 2:控室 3:会食室(お清め室) 4:霊安室 5:式場(法事) 6:保冷庫 9:エンバー室 99:その他
     *   value:利用区分 1:霊安室 2:保冷庫 3:通夜施行場所 4:通夜・葬儀控室 5:通夜会食場所 6:告別式施行場所 7:告別式会食場所 
     *                  8:法要(戻り)施行場所 9:法事施行場所 10:法事控室 11:法事会食場所 12:イベント 13:使用不可(補修等、施設利用不可)  
     * t_kbn：施設区分  1:式場 2:控室 3:会食室(お清め室) 4:霊安室 5:対面室 6:保冷庫 9:エンバー室 99:その他
     */
    private $bunrui_pair = array(
        '0' => array('r_kbn' => array('4' => array('1'), '6' => array('2')), 't_kbn' => '4,6'), // 搬送
        '1' => array('r_kbn' => array('1' => array('3', '6', '7'), '2' => array('4'), '3' => array('5', '8'), '5' => array('6')), 't_kbn' => '1,2,3'), // 葬儀
        '2' => array('r_kbn' => array('1' => array('9'), '2' => array('10'), '3' => array('11')), 't_kbn' => '1,2,3'), // 法事
        '4' => array('r_kbn' => array('4' => array('1'), '2' => array('1'), '3' => array('1'), '5' => array('1'), '6' => array('1')), 't_kbn' => '4,6'), // 安置室
        '9' => array('r_kbn' => array('1' => array('12', '13'), '2' => array('13'), '3' => array('13'), '4' => array('13')
                , '5' => array('13'), '6' => array('13'), '9' => array('13'), '99' => array('13'),), 't_kbn' => ''), // なし（イベント等）
    );

    CONST YOYAKU_DEF_PARMS = 'seko_yoyaku_def_parms';

    /**
     * cale アクション
     *
     * <AUTHOR> Sai
     * @since  2020/xx/xx
     */
    public function caleAction() {

        // 呼び出し元設定parms *は必須
        //* p_yoyaku_bunrui:　0:搬送、1:葬儀、2:法事
        //* p_seko_no:
        //* p_seko_no_sub: 搬送時の受付番号枝番
        // p_tgt_day: YYYY-MM-DD
        // p_kaijyo_type:画面の施設区分
        // p_riyo_kbn:画面の利用区分

        $req = $this->getRequest();
        $module = $req->getModuleName();
        $controller = $req->getControllerName();
        $params = Msi_Sys_Utils::webInputs();
        $url = $module . '/' . $controller . '/' . 'main' . '/br/' . $params['p_yoyaku_bunrui'];

        if (isset($params)) {
            if (!isset($params['p_kaijyo_type'])) {
                $params['p_kaijyo_type'] = $this->bunrui_pair[$params['p_yoyaku_bunrui']]['t_kbn'];
            }
            App_Utils::setSessionData(self::YOYAKU_DEF_PARMS, $params);
        }
        $this->redirect($url);
    }

    /**
     * index アクション
     *
     * <AUTHOR> Sai
     * @since  2020/xx/xx
     */
    public function indexAction() {
        $this->_deleteLastCond();
        $req = $this->getRequest();
        $module = $req->getModuleName();
        $controller = $req->getControllerName();
        $url = $module . '/' . $controller . '/' . 'main' . '/br/' . $this->bunrui_key;
        $this->redirect($url);
    }

    /**
     * main アクション
     *
     * <AUTHOR> Sai
     * @since  2020/xx/xx
     */
    public function mainAction() {
        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }
        if (!isset($this->_params['br'])) {
            throw new Msi_Sys_Exception_InputException("予約分類が不正です");
        } else {
            $this->bunrui_key = $this->_params['br'];
        }
        // no_cond_exp が設定されている場合は条件を展開しない
        if (isset($params['no_cond_exp']) && $params['no_cond_exp']) {
            $this->_no_cond_exp = true;
        } else {
            $this->_no_cond_exp = false;
        }

        // 最終検索条件の展開
        if (!$this->_no_cond_exp) {
            $this->_params = $this->_restoreLastCond($params);
        }

        App_Smarty::pushCssFile(['app/cale.yoyaku.css']);
        App_Smarty::pushJsFile(['app/cale/cale.yoyaku.js']);
        $init_day = Msi_Sys_Utils::getDate();

        $tanto_bumon_cd = App_Utils::getTantoBumonCd();
        $areaBumon = App_Utils::parBumonOfHall($tanto_bumon_cd);
        $oya_bumon_cd = null;
        if ($areaBumon) {
            $oya_bumon_cd = $areaBumon['bumon_cd'];
        }
        // 初期値
        $data = array(
            'dataApp' => array(
                's_bumon' => $oya_bumon_cd, // 対象部門
                's_ko_bumon' => null, // 対象子部門
                's_kaijyo_type' => null, // 会場タイプ
                's_display_term' => $this->s_display_term, //  表示期間
                's_start_time' => $this->s_start_time, //  表示開始時間
                's_end_time' => $this->s_end_time, //  表示終了時間
                's_tgt_day' => $init_day,
                's_riyo_kbn' => null,
                's_yoyaku_bunrui' => $this->bunrui_key,
                's_seko_no' => null,
                's_seko_no_sub' => null,
                'seko_info' => null,
                'r_reian' => Logic_SisetsuYoyaku::$riyo_kbn_reian,
                'r_horei' => Logic_SisetsuYoyaku::$riyo_kbn_horei,
                'bunrui_pair' => $this->bunrui_pair
            ),
        );
        $yoyaku_def_parms = App_Utils::getSessionData(self::YOYAKU_DEF_PARMS);
        if (!empty($yoyaku_def_parms)) {
            // p_yoyaku_bunrui,p_seko_no,p_seko_no_sub,p_kaijyo_type,p_tgt_day,p_riyo_kbn
            if (isset($yoyaku_def_parms['p_riyo_kbn']) && strlen($yoyaku_def_parms['p_riyo_kbn']) > 0) {
                $data['dataApp']['s_riyo_kbn'] = $yoyaku_def_parms['p_riyo_kbn'];
            }
            if (isset($yoyaku_def_parms['p_yoyaku_bunrui']) && strlen($yoyaku_def_parms['p_yoyaku_bunrui']) > 0) {
                $data['dataApp']['s_yoyaku_bunrui'] = $yoyaku_def_parms['p_yoyaku_bunrui'];
            }
            if (isset($yoyaku_def_parms['p_seko_no']) && strlen($yoyaku_def_parms['p_seko_no']) > 0) {
                $data['dataApp']['s_seko_no'] = $yoyaku_def_parms['p_seko_no'];
            }
            if (isset($yoyaku_def_parms['p_seko_no_sub']) && strlen($yoyaku_def_parms['p_seko_no_sub']) > 0) {
                $data['dataApp']['s_seko_no_sub'] = $yoyaku_def_parms['p_seko_no_sub'];
            }
            if (isset($yoyaku_def_parms['p_kaijyo_type']) && strlen($yoyaku_def_parms['p_kaijyo_type']) > 0) {
                $data['dataApp']['s_kaijyo_type'] = $yoyaku_def_parms['p_kaijyo_type'];
            }
            if (isset($yoyaku_def_parms['p_tgt_day']) && strlen($yoyaku_def_parms['p_tgt_day']) > 0) {
                $data['dataApp']['s_tgt_day'] = $yoyaku_def_parms['p_tgt_day'];
            }
            App_Utils::setSessionData(self::YOYAKU_DEF_PARMS, null); // クリア処理
        } else {

            if (isset($this->_params['s_bumon']) && strlen($this->_params['s_bumon']) > 0) {
                $data['dataApp']['s_bumon'] = $this->_params['s_bumon'];
            }
            if (isset($this->_params['s_ko_bumon']) && strlen($this->_params['s_ko_bumon']) > 0) {
                $data['dataApp']['s_ko_bumon'] = $this->_params['s_ko_bumon'];
            }
            if (isset($this->_params['s_kaijyo_type']) && strlen($this->_params['s_kaijyo_type']) > 0) {
                $data['dataApp']['s_kaijyo_type'] = $this->_params['s_kaijyo_type'];
            }
            if (isset($this->_params['s_current_bumon_cd']) && strlen($this->_params['s_current_bumon_cd']) > 0) {
                $data['dataApp']['s_current_bumon_cd'] = $this->_params['s_current_bumon_cd'];
            }
            if (isset($this->_params['s_seko_no']) && strlen($this->_params['s_seko_no']) > 0) {
                $data['dataApp']['s_seko_no'] = $this->_params['s_seko_no'];
            }
            if (isset($this->_params['s_yoyaku_bunrui']) && strlen($this->_params['s_yoyaku_bunrui']) > 0) {
                $data['dataApp']['s_yoyaku_bunrui'] = $this->_params['s_yoyaku_bunrui'];
            }
//        if (isset($this->_params['s_tgt_day']) && strlen($this->_params['s_tgt_day']) > 0 && empty($from_menu_day)) {
            if (isset($this->_params['s_tgt_day']) && strlen($this->_params['s_tgt_day']) > 0) {
                $data['dataApp']['s_tgt_day'] = $this->_params['s_tgt_day'];
            }
            if (isset($this->_params['s_display_term']) && strlen($this->_params['s_display_term']) > 0) {
                $data['dataApp']['s_display_term'] = $this->_params['s_display_term'];
            }
            if (isset($this->_params['s_start_time']) && strlen($this->_params['s_start_time']) > 0) {
                $data['dataApp']['s_start_time'] = $this->_params['s_start_time'];
            }
            if (isset($this->_params['s_end_time']) && strlen($this->_params['s_end_time']) > 0) {
                $data['dataApp']['s_end_time'] = $this->_params['s_end_time'];
            }
        }

        $db = Msi_Sys_DbManager::getMyDb();
        if (isset($data['dataApp']['s_seko_no']) && strlen($data['dataApp']['s_seko_no']) > 0) {
            $seko_info = DataMapper_SekoKihon::findone($db, array("seko_no" => $data['dataApp']['s_seko_no']));
            // 施行情報になければ出動依頼テーブルから取得
            if (Msi_Sys_Utils::myCount($seko_info) == 0) {
                $seko_info = DataMapper_Hanso_SyutsudoIraiInfo::findOne($db, array("uketsuke_no" => $data['dataApp']['s_seko_no']));
                if (Msi_Sys_Utils::myCount($seko_info) > 0) {
                    $seko_info['seko_no'] = $seko_info['uketsuke_no'];
                    $seko_info['k_nm'] = $seko_info['k_l_nm'];
                }
            }
            $data['dataApp']['seko_info'] = $seko_info;
        }
        $data['bumon_select2'] = Logic_Cale_ComLogic::get_bumon();
        $data['sikijyo_select2'] = Logic_Cale_ComLogic::get_bumon2();
        $data['kaijo_type_select2'] = DataMapper_MsterGetLib::GetCodeNameMst($db, '2080');
        $data['riyo_kbn_select2'] = DataMapper_MsterGetLib::GetCodeNameMst($db, '7834');
        $data['yoyaku_tanto_nm'] = App_Utils::getTantoNm();
        $data['nitei_def_time'] = DataMapper_MsterGetLib::GetCodeNameMst($db, '8230');
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
    }

//    /**
//     * setsession アクション
//     *
//     * <AUTHOR> Sai
//     * @since  2020/xx/xx
//     */
//    public function setsessionAction() {
//        $params = Msi_Sys_Utils::webInputs();
//        $this->_saveLastCond($params);
//        Msi_Sys_Utils::outJson($params);
//    }

    /**
     * 検索条件保管のセッションキー名を返す
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @return  string
     */
    private function _getSessCondKey() {
        $keySess = 'sekoYoyakuCond' . $this->bunrui_key;
        return $keySess;
    }

    /**
     * 最終検索条件の展開
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @param   array  $params     条件パラメタ
     * @return  void
     */
    private function _restoreLastCond(&$params) {
        $keySess = $this->_getSessCondKey();
        $data = Msi_Sys_Utils::getSessionVar($keySess);
        if (is_array($data)) {
            foreach ($data as $key => $val) {
                if (!isset($params[$key])) {
                    $params[$key] = $val;
                }
            }
        }
        return $params;
    }

    /**
     * 検索条件保存
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @param   array  $params     条件パラメタ
     * @return  void
     */
    private function _saveLastCond($params) {
        $data = array();
        foreach ($params as $key => $val) {
            if (Msi_Sys_Utils::beginsWith($key, 's_')) { // s_ で始まる条件が対象
                $data[$key] = $val;
            }
        }

        $keySess = $this->_getSessCondKey();
        Msi_Sys_Utils::setSessionVar($keySess, $data);
    }

    /**
     * 検索条件削除
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @return  void
     */
    private function _deleteLastCond() {
        $keySess = $this->_getSessCondKey();
        Msi_Sys_Utils::unsetSessionVar($keySess);
    }

    /**
     * 検索結果表示 アクション
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     */
    public function listAction() {

        $data = $this->getData();
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * データを得
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @return   array()
     */
    private function getData() {

        $dataApp = $this->getAppData();
        $dataEvt = $this->getEvtData();
        // ヘッダーデータを加工する
        $dataHd = $this->getHdData();
        $dataSisetsu = $this->getSisetsu();
        $data = array(
            'dataApp' => $dataApp,
            'dataHd' => $dataHd,
            'dataEvt' => $dataEvt,
            'dataSisetsu' => $dataSisetsu,
            'status' => 'OK',
        );
        // 検索条件の保存
        $this->_saveLastCond($this->_params);
        return $data;
    }

    /**
     * 施設を取得
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @return array
     */
    private function getSisetsu() {
        $db = Msi_Sys_DbManager::getMyDb();
        $cond = array(
            '__etc_orderby' => array('bumon_cd,kaijyo_cd ASC'),
//            '__etc_limit' => 300
        );
        $bumon_cd = $this->_params['s_bumon'];
        $s_ko_bumon = $this->_params['s_ko_bumon'];
        $kaijyo_type = $this->_params['s_kaijyo_type'];
        if (strlen($s_ko_bumon) > 0) {
            $cond['kaikei_bumon_cd'] = $s_ko_bumon;
        } else if (strlen($bumon_cd) > 0) {
            $cond['__x_bumon'] = DataMapper_BumonEx::findDesCond($db, $bumon_cd);
        }
        if (strlen($kaijyo_type) > 0) {
            $cond['type_kbn'] = DataMapper_Utils::condOneOf('type_kbn', $kaijyo_type, 'type_kbn_');
//            $cond['type_kbn'] = $kaijyo_type; //
        }
        $cond['kaijyo_kbn'] = 1; // 自営式場
        $sisetsu = DataMapper_Kaijyo::find($db, $cond);
        return $sisetsu;
    }

    /**
     * データ取得
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @return array
     */
    private function getAppData() {
        if (isset($this->_params)) {
            $params = $this->_params;
        } else {
            $this->_params = $params = Msi_Sys_Utils::webInputs();
        }
        $this->bunrui_key = $this->_params['s_yoyaku_bunrui'];
        $s_tgt_date = Msi_Sys_Utils::checkVarOrDefault($params['s_tgt_day'], 'DATE2', Msi_Sys_Utils::getDate());
        $tgt_date = Msi_Sys_Utils::normYYYYMMDD($s_tgt_date, "/"); // YYYY-MM-DD ⇒ YYYY/MM/DD
        $s_display_term = Msi_Sys_Utils::checkVarOrDefault($params['s_display_term'], 'DIGIT', $this->s_display_term);
//        $bumon = Msi_Sys_Utils::easyGetVar($params, 's_bumon');

        $this->_params['date_today'] = Msi_Sys_Utils::getDate();
        $this->_params['date_refresh'] = Msi_Sys_Utils::getDatetimeStd();
        $this->_params['s_tgt_day'] = $tgt_date;
        $this->_params['s_tgt_day_disp'] = Msi_Sys_Utils::getDate(Msi_Sys_Utils::getEpoch($tgt_date), 'Y/n/j');
        $this->_params['s_display_term'] = $this->s_display_term;
        $this->_params['s_start_time'] = $this->s_start_time;
        $this->_params['s_end_time'] = $this->s_end_time;
//        $this->_params['s_bumon'] = $bumon;
        return $this->_params;
    }

    /**
     * 施行ヘッダーデータを取得する
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @return array
     */
    private function getHdData() {
        $epoch = strtotime($this->_params['s_tgt_day'] . ' ' . $this->s_start_time);
        $s_display_term = $this->_params['s_display_term'];
//        $s_display_term = $this->s_display_term;
        $hdData = array();
        for ($index = 0; $index < $s_display_term; $index++) {
            $epoch_ymd = strtotime($index . " hour", $epoch);
            $ymd = Msi_Sys_Utils::getDate($epoch_ymd);
            $hm = Msi_Sys_Utils::getDate($epoch_ymd, 'H:i');
            $row['ymd'] = $ymd;
            $row['hm'] = $hm;
            $hdData[] = $row;
        }
        return $hdData;
    }

    /**
     * イベントデータ取得
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @return array
     */
    private function getEvtData() {
        $db = Msi_Sys_DbManager::getMyDb();

        $cond['__x1'] = array('x', "yoyaku_s_ymd <= :x1_1", array('x1_1' => $this->_params['s_tgt_day']));
        $cond['__x2'] = array('x', "yoyaku_e_ymd >= :x2_1", array('x2_1' => $this->_params['s_tgt_day']));
        $select = DataMapper_SisetsuYoyaku::find2($db, $cond);
        $data = array();
        $kaijyo_tree = DataMapper_SisetsuYoyaku::find($db);
        foreach ($select as $value) {
            $kaijyo_des = Logic_SisetsuYoyaku::getKaijyoDes($kaijyo_tree, $value['kaijyo_cd']); // 子会場取得
            $kaijyo_par = Logic_SisetsuYoyaku::getKaijyoPar($kaijyo_tree, $value['kaijyo_cd']); // 親会場取得
            $value['mod_flg'] = $this->getModFlg($value['cre_tanto_cd']);
            if (Msi_Sys_Utils::myCount($kaijyo_des) > 0) {
                foreach ($kaijyo_des as $kaijyoCd) {
                    $kaijyo_d = $value;
                    $kaijyo_d['kaijyo_cd'] = $kaijyoCd;
                    $kaijyo_p['kaijyo_lnm'] = '';
                    $kaijyo_d['evt_flg'] = '2'; // 1:元イベント 2:元イベントによる強制予約されるイベント
                    $kaijyo_d['re_kaijyo_cd'] = $kaijyoCd;
                    $data[] = $kaijyo_d;
                }
            }
            if (Msi_Sys_Utils::myCount($kaijyo_par) > 0) {
                foreach ($kaijyo_par as $kaijyoCd) {
                    $kaijyo_p = $value;
                    $kaijyo_p['kaijyo_cd'] = $kaijyoCd;
                    $kaijyo_p['kaijyo_lnm'] = '';
                    $kaijyo_p['evt_flg'] = '2'; // 1:元イベント 2:元イベントによる強制予約されるイベント
                    $kaijyo_p['re_kaijyo_cd'] = $kaijyoCd;
                    $data[] = $kaijyo_p;
                }
            }
            $data[] = $value;
        }
        return $data;
    }

    /**
     * 修正可能フラグ取得
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     * @param string $cre_tanto_cd 予約登録担当コード
     * @return  1:修正不可 2:修正可（作成者または権限者）
     */
    private function getModFlg($cre_tanto_cd) {
//        $flg = '1';
//        if ($cre_tanto_cd == App_Utils::getTantoCd() || App_Utils::ifRolesEqualTo('johosystem')) {
//            $flg = '2';
//        }
        // 修正可能に変更
        $flg = '2';

        return $flg;
    }

    /**
     * 予約保存 アクション
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     */
    public function saveyoyakuAction() {
        $params = Msi_Sys_Utils::webInputs();
        $dataYoyaku = Msi_Sys_Utils::json_decode($params['dataYoyaku']);
//        Msi_Sys_Utils::debug('* saveyoyaku=>' . Msi_Sys_Utils::dump($dataYoyaku));
        $db = Msi_Sys_DbManager::getMyDb();
        $data = Logic_SisetsuYoyaku::saveYoyaku($db, $dataYoyaku);
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 予約削除 アクション
     *
     * <AUTHOR> Sai
     * @since 2020/xx/xx
     */
    public function deleteyoyakuAction() {
        $params = Msi_Sys_Utils::webInputs();
        $dataYoyaku = Msi_Sys_Utils::json_decode($params['dataYoyaku']);
        $db = Msi_Sys_DbManager::getMyDb();
        $data = Logic_SisetsuYoyaku::deleteYoyaku($db, $dataYoyaku);
        Msi_Sys_Utils::outJson($data);
    }

}
