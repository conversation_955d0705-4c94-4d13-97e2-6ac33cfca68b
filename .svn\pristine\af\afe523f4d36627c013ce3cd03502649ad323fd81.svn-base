{* 2025/05/28 mihara seikyulist/main-lifeland.tpl からコピーして作成 *}
{include file="fdn_head_std.tpl"}
{include file="fdn_header_0.tpl"}

<form id="my-form-id" method="post" class="{$ctxt_readonly}">
    <div id="main">
        <div id="order" >
            
            <div class="page-title"><span>{$page_title}</span></div>   
            <div id="searchbtnarea">
                <input name="btn_search" id="btn_search" type="button" value="検索" />
                <input name="btn_clear" id="btn_clear" type="button" value="クリア" />
            </div> 
            <!-- 表示 -->
            <div class="search">
                <!-- 検索条件 -->
                <fieldset class="base_1">
                    <label for="s_oya_bumon" class="lbl_s_oya_bumon option">会社</label>
                    <input type="hidden" name="s_oya_bumon" id="s_oya_bumon" class="cls_s_oya_bumon"/>
                    <label for="s_bumon" class="lbl_s_bumon option">部門</label>
                    <input type="hidden" name="s_bumon" id="s_bumon" class="cls_s_bumon"/>
                    <label for="s_seko_tanto" class="lbl_s_seko_tanto option">施行担当者</label>
                    <input name="s_seko_tanto" id="s_seko_tanto" type="text" class="txt" value="" readonly/>
                    <div class="label dlg_common s_seko_tanto-ref"></div>
                    <label for="s_seko_no" class="lbl_s_seko_no option">施行No</label>
                    <input name="s_seko_no" id="s_seko_no" type="text" class="txt" value="" />
                    <div class="label dlg_common s_seko_no-ref"></div>
                </fieldset>
                <fieldset class="base_2">
                    <label for="s_shonin" class="lbl_s_shonin option">承認状況</label>
                    <input type="hidden" name="s_shonin" id="s_shonin" class="cls_s_shonin"/>
                    <label for="s_moushi" class="lbl_s_moushi option">申込区分</label>
                    <input type="hidden" name="s_moushi" id="s_moushi" class="cls_s_moushi"/>
{*                    <label for="s_pay_method" class="lbl_s_pay_method option">支払方法</label>*}
{*                    <input type="hidden" name="s_pay_method" id="s_pay_method" class="cls_s_pay_method"/>*}
                    <label for="s_nyukin_st" class="lbl_s_nyukin_st option option">入金状況</label>
                    <input type="hidden" name="s_nyukin_st" id="s_nyukin_st" class="cls_s_nyukin_st"/>
                    <label for="s_seikyu_den_no" class="lbl_s_seikyu_den_no option">請求伝票No</label>
                    <input name="s_seikyu_den_no" id="s_seikyu_den_no" type="text" class="txt" value="" />
                    <div class="label dlg_common s_seikyu_den_no-ref"></div>
                </fieldset>
                <fieldset class="base_2">
{*                    <label for="s_sekyu_tel" class="lbl_s_sekyu_tel option">電話番号</label>*}
{*                    <input name="s_sekyu_tel" id="s_sekyu_tel" type="text" class="txt" value="" />*}
                    <label for="s_sekyu_nm" class="lbl_s_sekyu_nm option">請求先名</label>
                    <input name="s_sekyu_nm" id="s_sekyu_nm" type="text" class="txt" value="" />
                    <label for="s_souke_nm" class="lbl_s_souke_nm option">葬家名</label>
                    <input name="s_souke_nm" id="s_souke_nm" type="text" class="txt" value="" />
                    <label for="s_k_nm" class="lbl_s_k_nm">故人名</label>
                    <input type="text" name="s_k_nm" id="s_k_nm" class="txt" value="" />
                    <label for="s_denpyo_no" class="lbl_denpyo_no option">アフター受注No</label>
                    <input name="s_denpyo_no" id="s_denpyo_no" type="text" class="txt" value=""/>
                    <div class="label dlg_common s_denpyo_no-ref cursor-pointer"></div>
                </fieldset>
                <fieldset class="base_2">
                    <label for="s_bun_gas" class="lbl_s_bun_gas option">合算分割状況</label>
                    <input type="hidden" name="s_bun_gas" id="s_bun_gas" class="cls_s_bun_gas"/>
                    <label for="s_sekyu_st" class="lbl_s_sekyu_st option">請求状況</label>
                    <input type="hidden" name="s_sekyu_st" id="s_sekyu_st" class="cls_s_sekyu_st"/>
                    <label for="s_pay_method_cd" class="lbl_s_pay_method_cd option">支払方法</label>
                    <input type="hidden" name="s_pay_method_cd" id="s_pay_method_cd" class="cls_s_pay_method_cd"/>
                </fieldset>
                <fieldset class="base_3">
                    <label for="s_kaishu_ymd" class="lbl_s_kaishu_ymd option">支払期日</label>
                    <input name="s_kaishu_ymd_from" id="s_kaishu_ymd_from" type="text" class="txt my-type-date" value="" maxlength="10"/>
                    <div class="label dlg_date"></div>
                    <div class="label day-bar">～</div>
                    <input name="s_kaishu_ymd_to" id="s_kaishu_ymd_to" type="text" class="txt my-type-date" value="" maxlength="10"/>
                    <div class="label dlg_date"></div>
                    <label for="s_keijo_ymd" class="lbl_s_keijo_ymd option">売上計上日</label>
                    <input name="s_keijo_ymd_from" id="s_keijo_ymd_from" type="text" class="txt my-type-date" value="" maxlength="10"/>
                    <div class="label dlg_date"></div>
                    <div class="label day-bar">～</div>
                    <input name="s_keijo_ymd_to" id="s_keijo_ymd_to" type="text" class="txt my-type-date" value="" maxlength="10"/>
                    <div class="label dlg_date"></div>
                </fieldset>
            </div><!-- /.search -->
            <div class="result-list">
                <div class="header">
                    <table>
                        <tr>
                            <td class="row NO-chkAllToggle" rowspan="2">選択</td>
                            <td class="seikyu_den_no">請求No</td>
                            <td class="seko_no">施行No</td>
                            <td class="moushi_nm" rowspan="1">申込区分</td>
                            <td class="shonin_st" rowspan="1">承認状況</td>
                            <td class="nyukin_st" rowspan="1">入金状況</td>
                            <td class="keijo_ymd">売上計上日</td>
                            <td class="pay_method_cd">支払方法</td>
                            <td class="sekyu_nm">請求先名</td>
                            <td class="souke_nm">葬家名</td>
                            <td class="k_nm">故人名</td>
                            <td class="sekyu_prc">請求金額</td>
                            <td class="nyukin_ymd">入金日</td>
                            <td class="seikyu_print_tanto">請求書印刷者</td>
                            <td class="seikyu_print_date">請求書<br>初回印刷日</td>
                        </tr>
                        <tr>
                            <td class="bun_gas_st">合算分割<br>状況</td>
                            <td class="juchu_denpyo_no">受注伝票No</td>
                            <td class="bumon_nm">部門</td>
                            <td class="sekyu_st">請求状況</td>
                            <td class="kaishu_ymd">支払期日</td>
                            <td class="fix_keijo_ymd">訂正計上日</td>
                            <td class="syorui_tenpu">納品書等</td>
{*                            <td class="sekyu_tel">電話番号</td>*}
                            <td class="sekyu_addr" colspan="3">請求書送付先住所</td>
                            <td class="seikyu_zan">請求残高</td>
                            <td class="nyukin_prc">入金額</td>
                            <td class="seko_tanto">施行担当者</td>
                            <td class="seikyu_hakko_ymd">請求書<br>最終印刷日</td>
                        </tr>
                    </table>
                </div><!-- /.header -->
                <div class="list"><table class='list_table'></table></div>
            </div>
            <!-- END -->
            <!-- 処理ボタン -->
            <div class="buttons sekolist-cmd-buttons">
              <input type="button" name="btn_tairyu" id="btn_tairyu" value="滞留債権管理" />
              {* <!-- 
                <input type="button" name="btn_save" id="btn_save" value="保存" />
                <input type="button" name="btn_kakutei" id="btn_kakutei" value="請求書承認" />
                <input type="button" name="btn_msi_print" id="btn_msi_print" value="確認用請求書出力" />
                <input type="button" name="btn_print" id="btn_print" value="請求書類一式発行" />
                <input type="button" name="btn_cancel" id="btn_cancel" value="承認取消" />
                <input type="button" name="btn_gas" id="btn_gas" value="供花供物請求合算" />
            <div class="air-app-div"><a href="{$app_base}air/default_badge.html" target="_blank" title="一括ダウンロード用の AIR アプリを導入します. 別画面を開きます">AIRアプリ導入<i class="glyphicon glyphicon-new-window"></i></a></div>
            --> *}
            </div><!-- /.buttons -->
        </div><!-- /#order -->
    </div><!-- /#main -->
</form><!-- /#my-form-id -->

<script id="my-data-init-id" type="application/json">
    {$mydata_json|smarty:nodefaults}
</script>
{literal}
    <script type="text/template" id="tmpl-result">
        <tr class="result-list-sel">
            <td class="row" rowspan="2"></td>
            <td class="seikyu_den_no"></td>
            <td class="seko_no"></td>
            <td class="moushi_nm" rowspan="1"></td>
            <td class="shonin_st" rowspan="1"></td>
            <td class="nyukin_st" rowspan="1"></td>
            <td class="keijo_ymd"></td>
            <td class="td_pay_method_cd">
                    <input type="hidden" name="pay_method_cd" class="pay_method_cd"/>
            </td>
            <td class="sekyu_nm"></td>
            <td class="souke_nm"></td>
            <td class="k_nm"></td>
            <td class="sekyu_prc"></td>
            <td class="nyukin_ymd"></td>
            <td class="seikyu_print_tanto"></td>
            <td class="seikyu_print_date"></td>
        </tr>
        <tr class="result-list-sel">
            <td class="bun_gas_st"></td>
            <td class="juchu_denpyo_no"></td>
            <td class="bumon_nm"></td>
            <td class="sekyu_st"></td>
            <td class="kaishu_ymd"></td>
            <td class="fix_keijo_ymd"></td>
            <td class="td_syorui_tenpu">
                <span id="syorui_tenpu" class="radio_set">
                    <input name="syorui_tenpu_<%=seikyu_den_no%>" id="syorui_tenpu_<%=seikyu_den_no%>" class="syorui_tenpu" type="checkbox" value="1" />
                    <label for="syorui_tenpu_<%=seikyu_den_no%>" class="lbl_syorui_tenpu">納品書等添付</label>
                </span>
            </td>
            <!--<td class="sekyu_tel"></td>-->
            <td class="sekyu_addr" colspan="3"></td>
            <td class="seikyu_zan"></td>
            <td class="nyukin_prc"></td>
            <td class="seko_tanto"></td>
            <td class="seikyu_hakko_ymd"></td>
        </tr>
    </script>
{/literal}


{include file="fdn_footer_std.tpl"}
