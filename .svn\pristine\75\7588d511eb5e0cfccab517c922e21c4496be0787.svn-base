<?php

/**
 * Juchu_JuchuCustomerinfo
 *
 * お客様情報クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuCustomerinfo
 * <AUTHOR> Tosaka
 * @since      2020/02/XX
 * @filesource 
 */

/**
 * お客様情報クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuCustomerinfo
 * <AUTHOR> Tosaka
 * @since      2020/02/XX
 */
class Juchu_JuchuCustomerinfo extends Juchu_JuchuCustomerinfoCom {

    /**
     * 初期情報取得処理
     *
     * <AUTHOR> Tosaka
     * @since  2020/02/XX
     * @param string $controllerName コントローラー名
     * @return array jsonData
     */
    public function getInitData($controllerName, $sidemenukey = '') {
        
        $db = Msi_Sys_DbManager::getMyDb();
        $this->_sekoNo = $this->getReqSekoNo();
        // 施行基本情報を設定する
        $this->setInitParam();
        $this->_customerCd = $this->getCustomerCd();

        // 施行基本情報を取得する
        $dataSekoKihon = $this->getSekoKihon();
        // 生年月日の値を設定する
        if (!isset($dataSekoKihon['uketuke_tanto_cd'])) {
            // お客様情報新規登録時の受付担当者にログイン者の情報を設定する
            $dataSekoKihon['uketuke_tanto_cd'] = App_Utils::getTantoCd();
            $dataSekoKihon['uketuke_tanto_nm'] = App_Utils::getTantoNm();
        }
        // 施行基本フリー情報を取得する
        $dataKihonFree = $this->getKihonFree();
        if (isset($dataKihonFree['tanto_cd1'])) {
            $dataSekoKihon['mitsu_tanto_cd'] = $dataKihonFree['tanto_cd1'];
            $dataSekoKihon['mitsu_tanto_nm'] = $dataKihonFree['tanto_nm1'];
        }
        if (!isset($dataKihonFree['ts_free1'])) {
            // 新規登録時の受付日を設定する
            $dataKihonFree['ts_free1'] = Msi_Sys_Utils::getDate(null, 'Y/m/d H:i');
            $dataKihonFree['ts_free1_date'] = Msi_Sys_Utils::getDate();
            $dataKihonFree['ts_free1_time'] = Msi_Sys_Utils::getDate(null, 'H:i');
        }
        
        if (!isset($dataSekoKihon['kaishu_ymd'])) {
            $uri = $this->getUriageKaisu();
            if (isset($uri) && count($uri) > 0 && isset($uri['kaishu_ymd'])) {
                $dataSekoKihon['kaishu_ymd'] = $uri['kaishu_ymd'];
            }
        }
        // 施行受付履歴を取得する
        $dataUketsukeInfoCol = $this->getUketsukeInfo();
        // 更新履歴を取得する
        $dataUpdateHistoryCol = $this->getUpdateHistory();
        // 施行互助会加入者を取得する
        $dataGojokaiMemberCol = $this->getGojokaiMember(15);
        // 施行日程を取得する
        $dataNiteiCol = $this->getNitei($dataSekoKihon);
        // 施行請求先情報を取得する
        $dataSekyuInfo = $this->getSekyuInfo();
        // 施行互助会情報を取得する
        $dataGojokaiInfo = $this->getGojokaiInfo();
        // 互助会コースマスタを取得する
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        // 施行契約先情報を取得する
        $dataSekoKeiyakusakiInfo = $this->getKeiyakusakiInfo();
        // 報告書タブ情報を取得する
        $dataReport = $this->getReport();
        // 式場移動情報を取得する
        $dataShikijoCol = $this->getShikijoIdo();
        // 訃報連絡情報を取得する
        $dataHuhoInfo = $this->getHuhoInfo();
        // 結果情報取得
        $dataResultInfo = $this->getResultInfo($db);
        // 施行者カード情報取得
        $dataSekoshaInfo = $this->getSekoshaInfo($db);
        // 別注品の商品マスタを取得する(プルダウン用)
        $dataBechuShohin = $this->getBechuShohin();
        // 契約種別用プルダウンを生成する
//        $dataKeiyakuCif = $this->getKeiyakuCif();
        $dataKeiyakuCif = array();
        //$dataSekoKihon['uchiawase_tanto_nm'] = $dataAssignTantoInfo['uchiawase_tanto_nm'];
        //$dataSekoKihon['reikyu_tanto_nm'] = $dataAssignTantoInfo['reikyu_tanto_nm'];
        //$dataSekoKihon['kaso_tanto_nm'] = $dataAssignTantoInfo['kaso_tanto_nm'];
        //$dataSekoKihon['reikyu_hanso_ymd'] = $dataAssignTantoInfo['reikyu_hanso_ymd'];
        // サイドメニューデータを取得する
        if (App_Utils::isMitsuInJuchu($controllerName) || App_Utils::isCustomerNewinJuchu($controllerName)) {
            Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
            $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName);
        } else {
            $sideMenuData = $this->getSideMenuData();
        }
        list($hall_cd, $area_cd) = App_Utils::getDfltHallArea();
        
        $dataKbns = $this->getDataKbns();
        // 事前相談の伝票番号を取得する(プルダウン用)
        $dataKbns['jizenDenpyo'] = $this->getCdJizenDenpyo($dataSekoKihon['consult_seko_no']);
        $modCnts = $this->getModCnts();
        $codeKbns = array(
            // ヘッダー部
            'moushi_code_kbn' => self::CODE_KBN_MOUSHI_KBN_S, // 申込区分
            'sougi_code_kbn' => self::CODE_KBN_SOUGI_KBN, // 葬儀区分
            'p_info_code_kbn' => self::CODE_P_INFO, // 個人情報保護
            'kaiin_code_kbn' => self::CODE_KBN_KAIIN_KBN, // 会員区分
            // 受付情報タブ
            'haigu_code_kbn' => self::CODE_KBN_HAIGU_KBN, // 配偶者区分
            'umu_code_kbn' => self::CODE_KBN_UMU, // 有無区分
            'pacemaker_code_kbn' => self::CODE_KBN_PACEMAKER, // ペースメーカー区分
            'infection_code_kbn' => self::CODE_KBN_INFECTION, // 感染症区分
            'zoku_code_kbn' => self::CODE_KBN_ZOKU_KBN, // 続柄区分
            'sex_code_kbn' => self::CODE_KBN_SEX_KBN, // 性別
            // 打合せ事項①
            'keishiki_code_kbn' => self::CODE_KBN_KEISHIKI_KBN, // 葬儀形式
            'syuha_code_kbn' => self::CODE_KBN_SYUHA_KBN, // 宗派区分
            'syushi_code_kbn' => self::CODE_KBN_SYUSHI_KBN, // 宗旨区分
            'kaimyo_code_kbn' => self::CODE_KBN_KAINAME1_KBN, // 戒名
            'tera_shokai_code_kbn' => self::CODE_KBN_TERA_SHOKAI, // 寺紹介者
            // 打合せ事項②
            'choi_koden_code_kbn' => self::CODE_KBN_CHOI_KODEN, // 弔意香典
            'choi_kumotu_code_kbn' => self::CODE_KBN_CHOI_KUMOTU, // 弔意供花供物
            'seika_form_code_kbn' => self::CODE_KBN_SEIKA_FORM, // 供花問い合わせ区分
        );
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        // 消費税取得処理
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        $taxInfoAll = App_ClsTaxLib::GetTaxInfoAll($db);
        $dataSekoKihon['login_bumon_cd'] = App_Utils::getTantoBumonCd(App_Utils::getTantoCd());
        // 所属部門の親部門の配下の共通部門を取得する
        if (isset($dataSekoKihon['seko_tanto_cd']) && strlen($dataSekoKihon['seko_tanto_cd']) > 0) {
            $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $this->getTantoBumonCd($dataSekoKihon['seko_tanto_cd'])));
        } else {
            $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => App_Utils::getTantoBumonCd(App_Utils::getTantoCd())));
        }
        if (count($bumonData) > 0) {
            $comBumonCd = App_Utils2::getCommonBumonCd($bumonData[0]['bumon_cd']);
        } else {
            $comBumonCd = null;
        }
        $dataSekoKihon['shikijo_bumon_cd'] = $comBumonCd;
        if (isset($dataSekoKihon['k_birth_year'])) {
            $keyIndex = array_search($dataSekoKihon['k_birth_year'], array_column($dataKbns['gengo'], 'kbn_value_cd_num'));
            $result = $dataKbns['gengo'][$keyIndex];
            $dataSekoKihon['k_wa_year'] = $result['kbn_value_snm'];
        }
        if (isset($dataSekoKihon['m_birth_year'])) {
            $keyIndex = array_search($dataSekoKihon['m_birth_year'], array_column($dataKbns['gengo'], 'kbn_value_cd_num'));
            $result = $dataKbns['gengo'][$keyIndex];
            $dataSekoKihon['m_wa_year'] = $result['kbn_value_snm'];
        }
        if (!isset($dataSekoKihon['information_cd']) && isset($dataSekoKihon['login_bumon_cd'])) {
            $seika_kbn = array_values($dataKbns['seika_form_kbn']);
            $keyIndex = array_search($dataSekoKihon['login_bumon_cd'], array_column($seika_kbn, 'kbn_value_cd'));
            if ($keyIndex !== FALSE) {
            $result = $seika_kbn[$keyIndex];
                $dataSekoKihon['information_cd'] = $result['kbn_value_cd'];
                $dataSekoKihon['information_kbn'] = $result['kbn_value_cd_num'];
                $dataSekoKihon['information_code_kbn'] = $result['code_kbn'];
                $dataSekoKihon['seika_contact'] = $result['kbn_value_snm'];
                $biko = explode(',',$result['biko']);
                $dataSekoKihon['form_tel'] = $biko[0];
                $dataSekoKihon['form_fax'] = $biko[1];   
            }
        }
        // お仕事メール
        $mail_info1 =$this->getMainInfo1($dataSekoKihon, $dataKihonFree);
        // 結果メール
        $mail_info2 =$this->getMainInfo2($dataSekoKihon, $dataKihonFree, $dataResultInfo, $dataNiteiCol);
        // 口座情報取得
        $br_koza_info = $this->getBrKozaKanriMst($db);
        
        // 画面データを設定する
        $data = array(
            'controllerName' => $controllerName,
            'dataSekoKihon' => $dataSekoKihon,
            'dataSekoKihonFree' => $dataKihonFree,
            'dataSekoMemoCol' => $dataUketsukeInfoCol,
            'dataUpdateHistoryCol' => $dataUpdateHistoryCol,
            'dataNiteiCol' => $dataNiteiCol,
            'dataSekyuInfo' => $dataSekyuInfo,
            'dataGojokaiInfo' => $dataGojokaiInfo,
            'dataSekoKeiyakusakiInfo' => $dataSekoKeiyakusakiInfo,
            'dataGojokaiMemberCol' => $dataGojokaiMemberCol,
            'dataReport' => $dataReport,
            'dataShikijo' => $dataShikijoCol,
            'dataHuhoInfo' => $dataHuhoInfo,
            'dataResultInfo' => $dataResultInfo,
            'dataSekoshaInfo' => $dataSekoshaInfo,
            'gojokaiCouseMst' => $gojokaiCouseMst,
            'dataKbns' => $dataKbns,
            'codeKbns' => $codeKbns,
            'bechuShohin' => $dataBechuShohin,
            'keiyakuCif' => $dataKeiyakuCif,
            'dataUpdateHistoryContents' => $this->getUpdateHistoryContents(),
            'sideMenuDataCol' => $sideMenuData['dataCol'],
            'sidemenukey' => $sidemenukey,
            'seko_kaisya_cd'   => Logic_Hanso_HansoUtils::guessMyKaisyaBumonCd(),
            'area_cd'   => $area_cd,
            'taxInfo' => $taxInfo,
            'taxInfoAll' => $taxInfoAll,
            'mail_info1' => $mail_info1,
            'mail_info2' => $mail_info2,
            'brKozaInfo' => $br_koza_info,
            'modCnts' => $modCnts,
        );
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }
    /**
     * 
     * お仕事メールリンク作成
     * 
     * @param type $dataSekoKihon
     * @return type
     */
    private function getMainInfo1($dataSekoKihon, $dataKihonFree){
        $uketsuke = "";
        if(isset($dataKihonFree['ts_free1'])){
            $date = new DateTime($dataKihonFree['ts_free1']);
            $timestamp = $date->getTimestamp();
            $uketsuke  = Msi_Sys_Utils::getDatetime($timestamp, 'd日H:i');
        }
        $nyudensaki_nm1  = isset($dataSekoKihon['nyudensaki_nm1']) ? $dataSekoKihon['nyudensaki_nm1'] : "";
        $nyudensha_nm    = isset($dataSekoKihon['nyudensha_nm']) ? $dataSekoKihon['nyudensha_nm'] : "";
        $k_last_nm       = isset($dataSekoKihon['k_last_nm']) ? $dataSekoKihon['k_last_nm'] : "";
        $k_first_nm      = isset($dataSekoKihon['k_first_nm']) ? $dataSekoKihon['k_first_nm'] : "";
        $k_nm            = $k_last_nm."　".$k_first_nm;
        $izoku_last_nm       = isset($dataSekoKihon['izoku_last_nm']) ? $dataSekoKihon['izoku_last_nm'] : "";
        $izoku_first_nm       = isset($dataSekoKihon['izoku_first_nm']) ? $dataSekoKihon['izoku_first_nm'] : "";
        $izoku_nm        = $izoku_last_nm."　".$izoku_first_nm;
        $homonsaki_nm    = isset($dataSekoKihon['homonsaki_nm']) ? $dataSekoKihon['homonsaki_nm'] : "";
        $shutsudo_nm11   = isset($dataSekoKihon['shutsudo_nm11']) ? $dataSekoKihon['shutsudo_nm11'] : "";
        $shutsudo_nm12   = isset($dataSekoKihon['shutsudo_nm12']) ? $dataSekoKihon['shutsudo_nm12'] : "";
        $seko_no         = isset($dataSekoKihon['seko_no']) ? substr($dataSekoKihon['seko_no'], -2) : "00";
        $mail_address = "";
        $subject = "お仕事です。";
        $body  = "●".$uketsuke."（".$seko_no."件目）%0d%0a";
        $body .= "●依頼者%0d%0a";
        if(isset($nyudensaki_nm1) && strlen($nyudensaki_nm1) > 0) {$body .= $nyudensaki_nm1."%0d%0a";}
        if(isset($nyudensha_nm) && strlen($nyudensha_nm) > 0) {$body .= $nyudensha_nm."%0d%0a";}
        $body .= "●故人氏名%0d%0a";
        if(isset($k_nm) && strlen($k_nm) > 0) {$body .= $k_nm."様%0d%0a";}
        $body .= "●遺族氏名%0d%0a";
        if(isset($izoku_nm) && strlen($izoku_nm) > 0) {$body .= $izoku_nm."様%0d%0a";}
        $body .= "●行先%0d%0a";
        if(isset($homonsaki_nm) && strlen($homonsaki_nm) > 0) {$body .= $homonsaki_nm."%0d%0a";}
        $body .= "●出動者%0d%0a";
        if(isset($shutsudo_nm11) && strlen($shutsudo_nm11) > 0) {$body .= $shutsudo_nm11."%0d%0a";}
        if(isset($shutsudo_nm12) && strlen($shutsudo_nm12) > 0) {$body .= $shutsudo_nm12."%0d%0a";}
        $mail_info = "mailto:$mail_address?subject=$subject&body=$body";
        return $mail_info;
    }
    /**
     * 
     * 結果メールリンク作成
     * 
     * @param type $dataSekoKihon
     * @param type $dataKihonFree
     * @param type $dataResultInfo
     * @param type $dataNiteiCol
     * @return type
     */
    private function getMainInfo2($dataSekoKihon, $dataKihonFree, $dataResultInfo, $dataNiteiCol){
        $tuya_nitei_ymd  = "";
        $sougi_nitei_ymd = "";
        $basho_nm = "";
        if(Msi_Sys_Utils::myCount($dataNiteiCol) > 0){
            if(isset($dataNiteiCol[3]['nitei_ymd'])){
                $nitei_ymd = strtotime($dataNiteiCol[3]['nitei_ymd']);
                $tuya_nitei_ymd = Msi_Sys_Utils::getDatetime($nitei_ymd, 'm/d');
            }
            if(isset($dataNiteiCol[4]['nitei_ymd'])){
                $nitei_ymd = strtotime($dataNiteiCol[4]['nitei_ymd']);
                $sougi_nitei_ymd = Msi_Sys_Utils::getDatetime($nitei_ymd, 'm/d');
            }
            if(isset($dataNiteiCol[4]['basho_nm'])){
                $basho_nm = $dataNiteiCol[4]['basho_nm'];
            }
        }
        
        $tanto_nm1       = isset($dataKihonFree['tanto_nm1']) ? $dataKihonFree['tanto_nm1'] : "";
        $saidanshubetsu_nm = isset($dataResultInfo['saidanshubetsu_nm']) ? $dataResultInfo['saidanshubetsu_nm'] : "";
        $saidan_kin      = isset($dataResultInfo['saidan_kin']) ? $dataResultInfo['saidan_kin'] : "";
        if(isset($saidan_kin) && strlen($saidan_kin) > 0){
            $saidan_kin = number_format($saidan_kin);
        }
        $uketsuke = "";
        if(isset($dataKihonFree['ts_free1'])){
            $date = new DateTime($dataKihonFree['ts_free1']);
            $timestamp = $date->getTimestamp();
            $uketsuke  = Msi_Sys_Utils::getDatetime($timestamp, 'd日H:i');
        }
        $nyudensaki_nm1  = isset($dataSekoKihon['nyudensaki_nm1']) ? $dataSekoKihon['nyudensaki_nm1'] : "";
        $nyudensha_nm    = isset($dataSekoKihon['nyudensha_nm']) ? $dataSekoKihon['nyudensha_nm'] : "";
        $k_last_nm       = isset($dataSekoKihon['k_last_nm']) ? $dataSekoKihon['k_last_nm'] : "";
        $k_first_nm      = isset($dataSekoKihon['k_first_nm']) ? $dataSekoKihon['k_first_nm'] : "";
        $k_nm            = $k_last_nm."　".$k_first_nm;
        $izoku_last_nm       = isset($dataSekoKihon['izoku_last_nm']) ? $dataSekoKihon['izoku_last_nm'] : "";
        $izoku_first_nm       = isset($dataSekoKihon['izoku_first_nm']) ? $dataSekoKihon['izoku_first_nm'] : "";
        $izoku_nm        = $izoku_last_nm."　".$izoku_first_nm;
        $homonsaki_nm    = isset($dataSekoKihon['homonsaki_nm']) ? $dataSekoKihon['homonsaki_nm'] : "";
        $shutsudo_nm11   = isset($dataSekoKihon['shutsudo_nm11']) ? $dataSekoKihon['shutsudo_nm11'] : "";
        $shutsudo_nm12   = isset($dataSekoKihon['shutsudo_nm12']) ? $dataSekoKihon['shutsudo_nm12'] : "";
        $seko_no         = isset($dataSekoKihon['seko_no']) ? substr($dataSekoKihon['seko_no'], -2) : "00";
        $mail_address = "";
        $subject = "結果報告です。";
        $body  = "●".$uketsuke."（".$seko_no."件目）%0d%0a";
        $body .= "●依頼者%0d%0a";
        if(isset($nyudensaki_nm1) && strlen($nyudensaki_nm1) > 0) {$body .= $nyudensaki_nm1."%0d%0a";}
        if(isset($nyudensha_nm) && strlen($nyudensha_nm) > 0) {$body .= $nyudensha_nm."%0d%0a";}
        $body .= "●故人氏名%0d%0a";
        if(isset($k_nm) && strlen($k_nm) > 0) {$body .= $k_nm."様%0d%0a";}
        $body .= "●遺族氏名%0d%0a";
        if(isset($izoku_nm) && strlen($izoku_nm) > 0) {$body .= $izoku_nm."様%0d%0a";}
        $body .= "●行先%0d%0a";
        if(isset($homonsaki_nm) && strlen($homonsaki_nm) > 0) {$body .= $homonsaki_nm."%0d%0a";}
        $body .= "●出動者%0d%0a";
        if(isset($shutsudo_nm11) && strlen($shutsudo_nm11) > 0) {$body .= $shutsudo_nm11."%0d%0a";}
        if(isset($shutsudo_nm12) && strlen($shutsudo_nm12) > 0) {$body .= $shutsudo_nm12."%0d%0a";}
        $body .= "●経過（結果）";
        $body .= "%0d%0a通夜式：";
        if(isset($tuya_nitei_ymd) && strlen($tuya_nitei_ymd) > 0) {$body .= $tuya_nitei_ymd;}
        $body .= "%0d%0a告別式：";
        if(isset($sougi_nitei_ymd) && strlen($sougi_nitei_ymd) > 0) {$body .= $sougi_nitei_ymd;}
        $body .= "%0d%0a式場：";
        if(isset($basho_nm) && strlen($basho_nm) > 0) {$body .= $basho_nm;}
        $body .= "%0d%0a祭壇種別 ";
        if(isset($saidanshubetsu_nm) && strlen($saidanshubetsu_nm) > 0) {$body .= $saidanshubetsu_nm;}
        if(isset($saidan_kin) && strlen($saidan_kin) > 0) {$body .= "　".$saidan_kin;}
        $body .= "%0d%0a●契約者";
        if(isset($tanto_nm1) && strlen($tanto_nm1) > 0) {$body .= "%0d%0a".$tanto_nm1."%0d%0a";}
        $mail_info = "mailto:$mail_address?subject=$subject&body=$body";
        return $mail_info;        
    }
    /**
     *
     * データ区分を取得する
     *
     * <AUTHOR> Mogi
     * @since  2020/10/xx
     * @param  db $db
     * @return array $dataKbns
     */
    private function getDatakbns() {
        
        $db = Msi_Sys_DbManager::getMyDb();
        // コード名称マスタデータを取得する
        $dataCodeNameMst = $this->getCodeNameMst();
        // 部門マスタを取得する(プルダウン用)
        $dataBumon = $this->getCdBumon();
        // 部門マスタを取得する(取扱店)
        $dataBumonT = $this->getCdBumonT();
        // 部門マスタを取得する(入電店)
        $dataBumonN = $this->getCdBumonN();
        $gojokai_cose = $this->GetCodeNameMst2($db, self::CODE_GOJOKAI_COSE); // ソート順のため、個別取得する
        list($td_ryokin_kbn, $shikijo_ryokin_kbn, $reikyu_ryokin_kbn) = $this->getHansoRyokin($dataCodeNameMst, self::CODE_KBN_HANSO_RYOKIN_KBN);
        $huhoTeikeidata = $this->getTeikeiDataForCdNm();
        $dataKbns = array(
            // ヘッダー部
            'moushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_MOUSHI_KBN_S), // 申込区分
            'sougi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SOUGI_KBN), // 葬儀区分
            'p_info' => $this->filter($dataCodeNameMst, self::CODE_P_INFO), // 個人情報保護
            'kaiin_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_KBN), // 会員区分
            'sex_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SEX_KBN), // 性別区分
            'est_shikijo' => $dataBumon, // 部門マスタ
            'toriatsukai_kbn' => $dataBumonT, // 取扱店
            'nyuden_kbn'      => $dataBumonN, // 入電店
            'kaiin_sbt_cd'    => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_SBT),
            'cif_status_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_CIF_STATUS), // 検索結果区分
            'anketo_soufu' => $this->filter($dataCodeNameMst, self::CODE_KBN_9700), // アンケート送付先
            'hitsugi_check' => $this->filter($dataCodeNameMst, self::CODE_KBN_9701), // 棺確認
            'dm_soufu' => $this->filter($dataCodeNameMst, self::CODE_KBN_9708), // DM送付
            // 受付情報タブ
            'haigu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HAIGU_KBN), // 配偶者区分
            'gengo' => DataMapper_EraMst::getCodeNmEra(), // 元号
            'month' => DataMapper_EraMst::getCodeNmMonth(), // 元号(月)
            'day' => DataMapper_EraMst::getCodeNmDay(), // 元号(日)
            'kinmu_kbn_k' => $this->filter($dataCodeNameMst, self::CODE_KBN_KINMU_K), // 勤務先区分(故人)
            'kinmu_kbn_m' => $this->filter($dataCodeNameMst, self::CODE_KBN_KINMU_M), // 勤務先区分(喪主)
            'umu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_UMU), // 有無区分
            'pacemaker_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_PACEMAKER), // ペースメーカー区分
            'infection_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_INFECTION), // 感染症区分
            'zoku_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_ZOKU_KBN), // 続柄区分
            'status_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_STATUS), // ステータス区分
            'category_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_CATEGORY), // 重要度区分
            'tanto_mst' =>  $this->getCdTanto(), // 部門マスタ
            'nyuden_riyu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_NYUDEN_RIYU), // 入電理由
            'hs_anchi_cd' => $this->filter($dataCodeNameMst, self::HS_ANCHI_CD), // 安置場所
            // 打合せ事項①タブ
            'keishiki_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KEISHIKI_KBN), // 葬儀形式
            'syuha_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUHA_KBN), // 宗派区分
            'syushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUSHI_KBN), // 宗旨区分
            'shonanoka_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SHONANOKA_KBN), // 初七日区分
            'kai_name' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAINAME1_KBN), // 戒名
            'tera_shokai' => $this->filter($dataCodeNameMst, self::CODE_KBN_TERA_SHOKAI), // 寺紹介者
            // 打合せ事項②
            'bechuShohinBumon' => $dataBumon,
//            'bechuShohinBumon' => $this->getBechuShohinBumon(),
            'choi_koden_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_CHOI_KODEN), // 弔意香典
            'choi_kumotu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_CHOI_KUMOTU), // 弔意供花供物
            'seika_form_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SEIKA_FORM), // 供花問い合わせ区分
            'prc_unity_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_PRC_UNITY), // 供花金額統一
            'fa_seika_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_FA_SEIKA), // 可否区分
            'fa_sikimi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_FA_SIKIMI), // 可否区分
            'fa_sakaki_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_FA_SAKAKI), // 可否区分
            'fa_hanawa_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_FA_HANAWA), // 可否区分
            'fa_kudamono_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_FA_KUDAMONO), // 可否区分
            'fa_can_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_FA_CAN), // 可否区分
            // 日程タブ
            'hs_gyomu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HS_GYOMU_KBN), // 搬送業務区分
            'sogi_basho_kbn' => $this->filter($dataCodeNameMst, self::CODE_SOGI_BASHO_KBN), // 葬儀場所
            // 互助会タブ
            'kaiin_info_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_INFO), // 会員情報区分
            'hunsitu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_9717), // 紛失区分
            'wariken_mst' => $this->filter($dataCodeNameMst, self::CODE_KBN_9851), // 割引券
            'yoto_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_YOTO_KBN), // 用途
            'yoto_kbn_OM' => $this->filter($dataCodeNameMst, self::CODE_KBN_YOTO_KBN_OM), // 用途OM
            'gojokai_cose' => $gojokai_cose, // 互助会コース
            'zei_cd' => $this->getZei_cd(), // 消費税コード
            'user_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_USER), // 利用者区分
            'kyosai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KYOSAI), // 共済組合区分
            'genkyo_cd' => $this->filter($dataCodeNameMst, self::CODE_KBN_GENKYO), // 現況コード
            // 訃報連絡タブ
            'nenrei_before' => $this->filter($dataCodeNameMst, self::CODE_KBN_NENREI_BEFORE),
            'nenrei_after' => $this->filter($dataCodeNameMst, self::CODE_KBN_NENREI_AFTER),
            'hikari_inform' => $this->filter($dataCodeNameMst, self::CODE_KBN_HIKARI_INFORM),
            'huho_teikei' => $huhoTeikeidata,
            // その他タブ
            'portrait_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_PORTRAIT), // 写真預かり区分
            'todokede_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_TODOKEDE_KBN), // 届出役所区分
            //施行者カードタブ
            'atokazari' => $this->filter($dataCodeNameMst, self::CODE_KBN_9709), // 後飾り先
            'renrakukahi' => $this->filter($dataCodeNameMst, self::CODE_KBN_9710), // 連絡可否
            'hantei' => $this->filter($dataCodeNameMst, self::CODE_KBN_9711), // 判定
            'hantei_jogai' => $this->filter($dataCodeNameMst, self::CODE_KBN_9712), // 判定除外
            'uchiwake' => $this->filter($dataCodeNameMst, self::CODE_KBN_9713), // 内訳
            'uchiwake_detail' => $this->filter($dataCodeNameMst, self::CODE_KBN_9714), // 内訳詳細
            'zanguchi' => $this->filter($dataCodeNameMst, self::CODE_KBN_9715), // 残口
            'eigyo_status' => $this->filter($dataCodeNameMst, self::CODE_KBN_9716), // 状況
            // 報告書タブ①施行引継書
            'keii_tsuya' => $this->filter($dataCodeNameMst, self::CODE_KBN_KEII_TSUYA), // 通夜時間決定経緯区分
            'keii_kokubetsu' => $this->filter($dataCodeNameMst, self::CODE_KBN_KEII_KOKUBETSU), // 告別時間決定経緯区分
            'car_type' => $this->filter($dataCodeNameMst, self::CODE_KBN_CAR_TYPE), // 車種
            'car_type2' => $this->filter($dataCodeNameMst, self::CODE_KBN_CAR_TYPE2), // 車種(喪家希望)
            'shohin_shitei' => $this->filter($dataCodeNameMst, self::CODE_KBN_SHOHIN_SHITEI), // 商品銘柄指定有無区分
            'drink_service_explain' => $this->filter($dataCodeNameMst, self::CODE_KBN_DRINK_SERVICE_EXPLAIN), // 飲食サービス料説明区分
            'tax_explain' => $this->filter($dataCodeNameMst, self::CODE_KBN_TAX_EXPLAIN), // 消費税説明区分
            'payment_deadline' => $this->filter($dataCodeNameMst, self::CODE_KBN_PAYMENT_DEADLINE), // 支払期限説明区分
            'payment_method' => $this->filter($dataCodeNameMst, self::CODE_KBN_PAYMENT_METHOD), // 支払方法説明区分
            'hand_over_box' => $this->filter($dataCodeNameMst, self::CODE_KBN_HAND_OVER_BOX), // 記帳箱渡区分
            'contact_tera' => $this->filter($dataCodeNameMst, self::CODE_KBN_CONTACT_TERA), // 宗教者連絡区分
            'sleeper_explain' => $this->filter($dataCodeNameMst, self::CODE_KBN_SLLEPER_EXPLAIN), // 寝台車追加料金説明区分
            'transit_explain' => $this->filter($dataCodeNameMst, self::CODE_KBN_TRANSIT_EXPLAIN), // 移動時間説明区分
            'condolence' => $this->filter($dataCodeNameMst, self::CODE_KBN_CONDOLENCE), // 弔辞説明区分
            'condolence_explain' => $this->filter($dataCodeNameMst, self::CODE_KBN_CONDOLENCE_EXPLAIN), // 弔辞内容説明区分
            'd_meeting' => $this->filter($dataCodeNameMst, self::CODE_KBN_D_MEETING), // 弔辞打合予定区分
            'care' => $this->filter($dataCodeNameMst, self::CODE_KBN_CARE), // 世話役説明区分
            'care_section' => $this->filter($dataCodeNameMst, self::CODE_KBN_CARE_SECTION), // 世話役説明有無区分
            'care_placard' => $this->filter($dataCodeNameMst, self::CODE_KBN_CARE_PLACARD), // 世話役説明プラカード区分
            'osonae' => $this->filter($dataCodeNameMst, self::CODE_KBN_OSONAE), // 枕元お供え品説明区分
            'flower_method' => $this->filter($dataCodeNameMst, self::CODE_KBN_FLOWER_METHOD), // 供花注文書記載方法区分
            'flower_timing' => $this->filter($dataCodeNameMst, self::CODE_KBN_FLOWER_TIMING), // 供花注文書預かりタイミング
            'flower_num_explain' => $this->filter($dataCodeNameMst, self::CODE_KBN_FLOWER_NUM_EXPLAIN), // 必要基数説明区分
            'exhibit' => $this->filter($dataCodeNameMst, self::CODE_KBN_EXIBIT), // 展示品有無区分
            'bgm' => $this->filter($dataCodeNameMst, self::CODE_KBN_BGM), // BGM有無区分
            'k_group' => $this->filter($dataCodeNameMst, self::CODE_KBN_K_GROUP), // 故人所属団体有無区分
            'cost_tera' => $this->filter($dataCodeNameMst, self::CODE_KBN_COST_TERA), // 当日経費宗教者・世話役関係区分
            'cost_kaso' => $this->filter($dataCodeNameMst, self::CODE_KBN_COST_KASO), // 当日経費火葬場関係区分
            'cost_other' => $this->filter($dataCodeNameMst, self::CODE_KBN_COST_OTHER), // 当日経費その他区分
            'cost_hall_car' => $this->filter($dataCodeNameMst, self::CODE_KBN_COST_HALL_CAR), // 当日経費式場・車両関係区分
            'lodging' => $this->filter($dataCodeNameMst, self::CODE_KBN_LODGING), // 宿泊説明区分
            'lodging_facility' => $this->filter($dataCodeNameMst, self::CODE_KBN_LODGING_FACILITY), // 宿泊施設説明区分
            'bedding' => $this->filter($dataCodeNameMst, self::CODE_KBN_BEDDING), // 貸寝具説明区分
            'lodging_cost' => $this->filter($dataCodeNameMst, self::CODE_KBN_LODGING_COST), // 宿泊金額説明区分
            'partner_lodging' => $this->filter($dataCodeNameMst, self::CODE_KBN_PARTNER_LODGING), // 提携ホテル説明区分
            'breakfast' => $this->filter($dataCodeNameMst, self::CODE_KBN_BREAKFAST), // 朝食説明区分
            'other_room' => $this->filter($dataCodeNameMst, self::CODE_KBN_OTHER_ROOM), // 別室利用希望区分
            'thanking' => $this->filter($dataCodeNameMst, self::CODE_KBN_THANKING), // 御礼渡しタイミング説明区分
            'thanking_tera' => $this->filter($dataCodeNameMst, self::CODE_KBN_THANKING_TERA), // 御礼渡しタイミング(宗教者)区分
            'thanking_care' => $this->filter($dataCodeNameMst, self::CODE_KBN_THANKING_CARE), // 御礼渡しタイミング(世話役)区分
            'greeting_tsuya' => $this->filter($dataCodeNameMst, self::CODE_KBN_GREETING_TSUYA), // 挨拶説明(通夜)区分
            'greeting_kokubetsu' => $this->filter($dataCodeNameMst, self::CODE_KBN_GREETING_KOKUBETSU), // 挨拶説明(告別式)区分
            'greeting_kenpai' => $this->filter($dataCodeNameMst, self::CODE_KBN_GREETING_KENPAI), // 挨拶説明(献杯)区分
            'greeting_sankai' => $this->filter($dataCodeNameMst, self::CODE_KBN_GREETING_SANKAI), // 挨拶説明(散会)区分
            'greeting_other' => $this->filter($dataCodeNameMst, self::CODE_KBN_GREETING_OTHER), // 挨拶説明(他)区分
            'kaso_room_addition' => $this->filter($dataCodeNameMst, self::CODE_KBN_KASO_ROOM_ADDITION), // 待合室追加可否説明区分
            'kaso_room_dish' => $this->filter($dataCodeNameMst, self::CODE_KBN_KASO_ROOM_DISH), // 料理設置可能数説明区分
            'belongings' => $this->filter($dataCodeNameMst, self::CODE_KBN_BELONGINGS), // 式場持参品説明区分
            'projection_m_explain' => $this->filter($dataCodeNameMst, self::CODE_KBN_PROJECTION_M_EXPLAIN), // プロジェクションマッピング説明区分
            'projection_m_contents' => $this->filter($dataCodeNameMst, self::CODE_KBN_PROJECTION_M_CONTENTS), // プロジェクションマッピング内容区分
            // 報告書タブ②手配依頼書
//            'kihon_reikyu' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_REIKYU), // 霊柩車区分
//            'kihon_td_schedule' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_TD_SCHEDULE), // TD処置予定区分
//            'kihon_td_noukan' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_TD_NOUKAN), // TD処置時納棺区分
//            'kihon_yukan_schedule' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_YUKAN_SCHEDULE), // 湯灌処置種別区分
//            'kihon_yukan_schedule2' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_YUKAN_SCHEDULE2), // 湯灌処置場所区分
//            'kihon_yukan_noukan' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_YUKAN_NOUKAN), // 湯灌処置時納棺区分
//            'kihon_transport' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_TRANSPORT), // 送迎サービス有無区分
//            'kihon_tsubo' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_TSUBO), // 壺彫刻有無区分
//            'kihon_placard' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_PLACARD), // 手配プラカード有無区分
//            'kihon_henrei' => $this->filter($dataCodeNameMst, self::CODE_KBN_KIHON_HENREI), // 金額別返礼有無区分
//            'td_tehai' => $this->filter($dataCodeNameMst, self::CODE_KBN_TD_TEHAI), // TD搬送手配状況区分
//            'hanso_ryokin' => $this->filter($dataCodeNameMst, self::CODE_KBN_HANSO_RYOKIN_KBN), // 搬送料金区分
//            'td_ryokin_kbn' => $td_ryokin_kbn, // 搬送料金区分（TD迎え・TD送り）
//            'shikijo_ryokin_kbn' => $shikijo_ryokin_kbn, // 搬送料金区分（寝台搬送）
//            'reikyu_ryokin_kbn' => $reikyu_ryokin_kbn, // 搬送料金区分（霊柩搬送）
//            'yukan_tehai' => $this->filter($dataCodeNameMst, self::CODE_KBN_YUKAN_TEHAI), // 湯灌手配状況区分
//            'noukan_tachiai' => $this->filter($dataCodeNameMst, self::CODE_KBN_NOUKAN_TACHIAI), // 家族立会有無区分
//            'noukan_clothing' => $this->filter($dataCodeNameMst, self::CODE_KBN_NOUKAN_CLOTHING), // 葬送衣装区分
//            'noukan_clothing_nm2' => $this->filter($dataCodeNameMst, self::CODE_KBN_NOUKAN_CLOTHING_NM2), // 葬送衣装掛け方区分
//            'setup_saidan' => $this->filter($dataCodeNameMst, self::CODE_KBN_SETUP_SAIDAN), // 祭壇設営区分
//            'setup_whole' => $this->filter($dataCodeNameMst, self::CODE_KBN_SETUP_WHOLE), // 全館設営区分
//            'setup_soto' => $this->filter($dataCodeNameMst, self::CODE_KBN_SETUP_SOTO), // 外式設営区分
//            'keeping' => $this->filter($dataCodeNameMst, self::CODE_KBN_KEEPING), // 預かり品手配有無区分
//            'burial_item' => $this->filter($dataCodeNameMst, self::CODE_KBN_BURIAL_ITEM), // 埋葬具手配有無区分
//            'f_hall_reserve' => $this->filter($dataCodeNameMst, self::CODE_KBN_F_HALL_RESERVE), // 斎場予約区分
//            'f_hall_guide' => $this->filter($dataCodeNameMst, self::CODE_KBN_F_HALL_GUIDE), // 火葬場内案内表示名区分
//            'f_hall_bone' => $this->filter($dataCodeNameMst, self::CODE_KBN_F_HALL_BONE), // 収骨案内放送名区分
//            'f_hall_farewell' => $this->filter($dataCodeNameMst, self::CODE_KBN_F_HALL_FAREWELL), // 告別式でのお別れ方法
//            'gaiji' => $this->filter($dataCodeNameMst, self::CODE_KBN_GAIJI), // 外字使用者区分       
//            'kihon_td_schedule_sub' => $this->getTdKaijyo($db),
//            'ice_status' => $this->filter($dataCodeNameMst, self::CODE_KBN_HOREIZAI_STATUS_KBN), // 外字使用者区分   
            // 結果情報
            'uketsukekeka_cd' => $this->filter($dataCodeNameMst, self::CODE_KBN_9611),
            'saidanshubetsu_cd' => $this->filter($dataCodeNameMst, self::CODE_KBN_SAIDANSHUBETSU_CD),
            'om_status_cd' => $this->filter($dataCodeNameMst, self::CODE_KBN_KEKKA_INFO_CD),
            'om_shubetsu_cd' => $this->filter($dataCodeNameMst, self::CODE_KBN_OMIMAI_KBN),
            'om_kozashubetsu_cd' => $this->filter($dataCodeNameMst, self::CODE_KBN_YOKIN_SBT),
        );
        return $dataKbns;
    }
    
    /**
     *
     * TD会場一覧を取得する（報告書タブ セレクトボックス用）
     *
     * <AUTHOR> Mogi
     * @since  2020/08/19
     * @param  db $db
     * @return array TD会場一覧
     */
    private function getTdKaijyo($db) {
        $sql = "
            SELECT 
                null AS code_kbn
                , kaijyo_lnm AS code_kbn_nm
                , kaijyo_cd AS kbn_value_cd
                , kaijyo_cd AS kbn_value_cd_num
                , kaijyo_lnm AS kbn_value_lnm
                , kaijyo_snm AS kbn_value_snm
            FROM kaijyo_mst 
            WHERE delete_flg = 0
                AND type_kbn = 9
                ";
        $select = $db->easySelect($sql);
        return $select;
    }

    /**
     *
     * 搬送料金を取得する（報告書タブ セレクトボックス用）
     *
     * <AUTHOR> Mogi
     * @since  2020/08/24
     * @param  db $db
     * @return array
     */
    private function getHansoRyokin($dataCodeNameMst, $kbn) {

        $this->_codeKbn = $kbn;
        $codeNames = array_filter($dataCodeNameMst, function ($item) {
            return $item['code_kbn'] === $this->_codeKbn;
        }
        );

        $td_ryokin_kbn = array();
        $shikijo_ryokin_kbn = array();
        $reikyu_ryokin_kbn = array();
        foreach ($codeNames as $oneRow) {
            if ($oneRow['kbn_value_cd'] === '1') {    // 寝台搬送
                $td_ryokin_kbn[] = $oneRow;
                $shikijo_ryokin_kbn[] = $oneRow;
            } else if ($oneRow['kbn_value_cd'] === '2') { // TD搬送
                $td_ryokin_kbn[] = $oneRow;
            } else if ($oneRow['kbn_value_cd'] === '3') { // 霊柩搬送
                $reikyu_ryokin_kbn[] = $oneRow;
            } else if ($oneRow['kbn_value_cd'] === '4') { // 霊柩搬送(L)
                $reikyu_ryokin_kbn[] = $oneRow;
            }
        }
        return array($td_ryokin_kbn, $shikijo_ryokin_kbn, $reikyu_ryokin_kbn);
    }

    /**
     *
     * 施行日程情報を取得する
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param array $dataSekoKihon 施行基本情報
     * @return array 施行日程情報
     */
    private function getNitei(&$dataSekoKihon) {

        $dataNitei = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                sn.seko_no          -- 施行番号
                ,sn.nitei_kbn       -- 日程区分
                ,tsi.ts_based_nm    -- 日程基本名 
                ,tsi.ts_based_nm2   -- 日程基本名2 
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD HH24:MI') AS nitei_ymd-- 日程タイムスタンプ
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD') AS nitei_date-- 日程日付のみ
                ,CASE 
                    WHEN sn.free3_kbn = 1 AND sn.nitei_kbn = 1
                    THEN NULL
                    ELSE TO_CHAR(sn.nitei_ymd ,'HH24:MI')
                END nitei_time -- 日程時刻のみ
                ,TO_CHAR(sn.nitei_ed_ymd ,'YYYY/MM/DD HH24:MI') AS nitei_ed_ymd-- 日程終了タイムスタンプ
                ,TO_CHAR(sn.nitei_ed_ymd ,'HH24:MI') AS nitei_ed_time -- 日程終了時刻のみ
                ,sn.spot_cd       -- 場所区分コード
                ,sn.basho_kbn       -- 場所区分
                ,sn.basho_cd        -- 場所コード
                ,sn.basho_nm        -- 場所名 
                ,sn.v_free1
                ,sn.v_free2 -- 当社式場未使用チェック
                ,sn.v_free3 -- 式場予約番号
                ,CASE WHEN sn.basho_kbn = 2 THEN km1.bumon_cd
                ELSE NULL END AS kaijo_bumon_cd
                ,CASE WHEN sn.basho_kbn = 2 THEN km1.zip_no
                    WHEN sn.basho_kbn IN (15) THEN km2.zip_no
                    WHEN sn.basho_kbn IN (1) THEN njm.zip_no
                ELSE NULL END AS nitei_zip
                ,CASE WHEN sn.basho_kbn = 2 THEN km1.addr1_nm
                    WHEN sn.basho_kbn IN (15) THEN km2.addr1_nm
                    WHEN sn.basho_kbn IN (1) THEN njm.addr1_nm
                ELSE NULL END AS nitei_addr1
                ,CASE WHEN sn.basho_kbn = 2 THEN km1.addr2_nm
                    WHEN sn.basho_kbn IN (15) THEN km2.addr2_nm
                    WHEN sn.basho_kbn IN (1) THEN njm.addr2_nm
                ELSE NULL END AS nitei_addr2
                ,CASE WHEN sn.basho_kbn = 2 THEN km1.tel
                    WHEN sn.basho_kbn IN (15) THEN km2.tel
                    WHEN sn.basho_kbn IN (1) THEN njm.tel
                ELSE NULL END AS nitei_tel
                ,skf.free_kbn3 AS kasouba_zuikou_kbn
                ,COALESCE(sn.v_free4, tsi.ts_based_nm) AS nitei_nm
            FROM
                seko_nitei sn
            INNER JOIN tm_schedule_info tsi
                ON sn.nitei_kbn = tsi.date_kbn
            LEFT JOIN kaijyo_mst km1    -- 自営
                ON km1.kaijyo_cd = sn.basho_cd
                AND km1.kaijyo_kbn = 1
                AND km1.delete_flg = 0
            LEFT JOIN kaijyo_mst km2    -- 他営
                ON km2.kaijyo_cd = sn.basho_cd
                AND km2.kaijyo_kbn = 2
                AND km2.delete_flg = 0
            LEFT JOIN nm_jyusho_mst njm
                ON njm.jyusho_cd = sn.basho_cd
                AND njm.jyusho_kbn = sn.basho_kbn
                AND njm.delete_flg = 0
            LEFT JOIN seko_kihon_all_free skf
            ON  skf.seko_no    = sn.seko_no
            AND skf.delete_flg = 0
            WHERE sn.seko_no = :seko_no
                AND sn.delete_flg = 0
            ORDER BY tsi.disp_no
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));

        // タイムスケジュール情報マスタ取得SQL
        $sql2 = "
            SELECT
                 tsi.ts_based_nm    -- 日程基本名
                ,tsi.ts_based_nm2   -- 日程基本名2 
                ,tsi.date_kbn       -- 日付区分 
                ,tsi.disp_no        -- 表示順
                ,tsi.ts_based_nm AS nitei_nm -- 日程基本名
            FROM tm_schedule_info tsi
            WHERE tsi.delete_flg = 0
            ORDER BY tsi.disp_no
                ";
        $select2 = $db->easySelect($sql2);
        foreach ($select2 as &$value) {
            $value['nitei_kbn'] = (int) $value['date_kbn'];
        }
        // 施行日程に存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $niteiOneRowData = array();
                $niteiOneRowData['seko_no'] = $select[$i]['seko_no'];
                $niteiOneRowData['nitei_kbn'] = (int) $select[$i]['nitei_kbn'];
                $niteiOneRowData['ts_based_nm'] = $select[$i]['ts_based_nm'];
                $niteiOneRowData['ts_based_nm2'] = $select[$i]['ts_based_nm2'];
                $niteiOneRowData['nitei_ymd'] = $select[$i]['nitei_ymd'];
                $niteiOneRowData['nitei_date'] = $select[$i]['nitei_date'];
                $niteiOneRowData['nitei_time'] = $select[$i]['nitei_time'];
                $niteiOneRowData['nitei_ed_ymd'] = $select[$i]['nitei_ed_ymd'];
                $niteiOneRowData['nitei_ed_time'] = $select[$i]['nitei_ed_time'];
                $niteiOneRowData['spot_cd'] = $select[$i]['spot_cd'];
                $niteiOneRowData['basho_kbn'] = $select[$i]['basho_kbn'];
                $niteiOneRowData['basho_cd'] = $select[$i]['basho_cd'];
                $niteiOneRowData['basho_nm'] = $select[$i]['basho_nm'];
                $niteiOneRowData['sikijo_check'] = $select[$i]['v_free2'];
                $niteiOneRowData['sikijo_yoyaku_no'] = $select[$i]['v_free3'];
                $niteiOneRowData['kaijo_bumon_cd'] = $select[$i]['kaijo_bumon_cd'];
                $niteiOneRowData['nitei_zip'] = $select[$i]['nitei_zip'];
                $niteiOneRowData['nitei_addr1'] = $select[$i]['nitei_addr1'];
                $niteiOneRowData['nitei_addr2'] = $select[$i]['nitei_addr2'];
                $niteiOneRowData['nitei_tel'] = $select[$i]['nitei_tel'];
                $niteiOneRowData['kasouba_zuikou_kbn'] = $select[$i]['kasouba_zuikou_kbn'];
                $niteiOneRowData['nitei_nm'] = $select[$i]['nitei_nm'];
                $dataNitei[$i] = $niteiOneRowData;
                if ($niteiOneRowData['nitei_kbn'] == self::NITEI_KBN_TUYA) {
                    $dataSekoKihon['tuya_check'] = $select[$i]['v_free1'];
                }
                if ($niteiOneRowData['nitei_kbn'] == self::NITEI_KBN_KASO) {
                    $dataSekoKihon['kaso_check'] = $select[$i]['v_free1'];
                }
            }
        } else {
            $dataNitei = $select2;
        }
        return $dataNitei;
    }

    /**
     * 保存処理 
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param array $req リクエスト
     */
    public function save($req) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();

        $controllerName = $req->getPost('controllerName');
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        $oldSekoKihon = $req->getPost('oldDataApp');
        $dataNiteiCol = Msi_Sys_Utils::json_decode($req->getPost('dataNiteiColJson'));
        $dataSekyuInfo = Msi_Sys_Utils::json_decode($req->getPost('dataSekyuInfoJson'));
        $dataGojokaiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiInfoJson'));
        $dataSekoKeiyakusakiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKeiyakusakiInfoJson'));
        $dataGojokaiMemberCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberColJson'));
        $dataGojokaiMemberDeleteCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberDeleteColJson'));
        $dataUketsukeInfoCol = Msi_Sys_Utils::json_decode($req->getPost('dataSekoMemoColJson'));
        $dataHuhoInfo = Msi_Sys_Utils::json_decode($req->getPost('dataHuhoInfoJson'));
        $dataResultInfo = Msi_Sys_Utils::json_decode($req->getPost('dataResultInfoJson'));
        $dataSekoshaInfo = Msi_Sys_Utils::json_decode($req->getPost('dataSekoshaInfoJson'));
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));
        $orgUpdateHistroyContents = Msi_Sys_Utils::json_decode($req->getPost('orgUpdateHistroyContents'));
        $sidemenukey = Msi_Sys_Utils::json_decode($req->getPost('sidemenukey'));
        $huho_flg = $req->getPost('huho_flg');
        $orgModCnts = $req->getPost('orgModCnts');
        $this->_sekoNo = $dataSekoKihon['seko_no'];
        $this->_customerCd = $this->getCustomerCd();
//        Msi_Sys_Utils::debug( '1=>' . $this->_sekoNo );
        if (empty($this->_sekoNo)) {
            // 廃止部門チェック
            if (App_HakuzenUtils::isBumonHaishiFlgOn($dataSekoKihon['bumon_cd'], $db)) { // 廃止フラグ=1
                $data = array(
                    'status' => 'NG',
                    'msg' => "廃止された部門が設定されています。部門を変更してください。",
                );
                Msi_Sys_Utils::outJson($data);
                return;
            }
            // 出動搬送からのコピーの場合は出動搬送の受付番号を施行番号に設定する
            if (isset($dataSekoKihon['hanso_seko_no'])) {
                // 既に施行が作成されていて論理削除されている場合は物理削除を行い新規登録を行う
                $sql = "
                SELECT k.delete_flg
                FROM seko_kihon_info k
                WHERE k.seko_no = :seko_no
                        ";
                $sekoSelect = $db->easySelect($sql, array('seko_no' => $dataSekoKihon['hanso_seko_no']));
                if (count($sekoSelect) > 0 && $sekoSelect[0]['delete_flg'] == '1') {
                    $sql = "
                        DELETE FROM seko_kihon_info
                        WHERE seko_no = :seko_no 
                            ";
                    $db->easyExecute($sql, array('seko_no' => $dataSekoKihon['hanso_seko_no']));
                } else if (count($sekoSelect) > 0 && $sekoSelect[0]['delete_flg'] == '0') {
                    $data = array(
                        'status' => 'NG',
                        'msg' => "既に引継ぎされた施行が存在します。",
                    );
                    Msi_Sys_Utils::outJson($data);
                    return;
                }
                $this->_sekoNo = $dataSekoKihon['hanso_seko_no'];
            } else {
                $this->_sekoNo = $this->getAutoSekoNo2($db, $dataSekoKihon);
            }
//            Msi_Sys_Utils::debug( '2=>' . $this->_sekoNo );
            if (is_null($this->_sekoNo)) {
                return;
            }
            // ワンタイムノンス情報設定
            $nonce = $this->genOtNonceInfo();
            $dataSekoKihon['v_free20'] = $nonce['nonce'];
            $dataSekoKihon['ts_free10'] = $nonce['expire_ts'];
        } else { // 2019/05/07 mihara keigen
            App_KeigenUtils::sougiYmdChgPre($this->_sekoNo); // sougi_ymd 変更の準備
        }

        if (empty($this->_customerCd)) {
            $this->_customerCd = $this->getAutoCustomerCd($db);
        }
        // 施行基本情報を設定する
        $this->setInitParam();
        if (empty($this->_moushiKbn)) {
            $this->_moushiKbn = $dataSekoKihon['moushi_kbn'];
        }
        if (empty($this->_mitsuTantoCd)) {
            $this->_mitsuTantoCd = $dataSekoKihon['mitsu_tanto_cd'];
        }
        if (empty($this->_sekoTantoCd) && isset($dataSekoKihon['seko_tanto_cd'])) {
            $this->_sekoTantoCd = $dataSekoKihon['seko_tanto_cd'];
        }
        // 変更回数チェック
        $curModCnts = $this->getModCnts();
        if ($curModCnts != $orgModCnts) {
            $data['status'] = 'NG';
            $data['msg'] = '既にデータが変更されています。画面を更新してください。';
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 画面とステータス区分が異なる場合はエラー
        if (!$this->checkStatusKbn($dataSekoKihon)) {
            return;
        }
        // 加入員番号重複チェック
        $msg = $this->checkGojokaiMember($dataGojokaiMemberCol);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 見積確定後部門が売上部門かどうかチェックする
//        if ($this->isMitsuKakutei() && !$this->checkUriBumon($dataSekoKihon['bumon_cd'])) {
//            $data['status'] = 'NG';
//            $data['msg'] = "売上計上部門が売上未定部門のため、更新することができません。";
//            Msi_Sys_Utils::outJson($data);
//            return;
//        }
        // 会員区分チェック
        $msg = $this->checkKaiinKbn($dataSekoKihon, $dataGojokaiMemberCol);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 加入日チェック
//        list($kanyuAlertFlg, $alertMsg, $errMsg) = $this->checkKanyu($dataSekoKihon, $dataGojokaiMemberCol, $dataNiteiCol);
//        if (isset($errMsg)) {
//            $data['status'] = 'NG';
//            $data['msg'] = $errMsg;
//            Msi_Sys_Utils::outJson($data);
//            return;
//        }
        // 契約先番号チェック
        $msg = $this->checkKeiyakusaki($db, $dataSekoKihon, $dataSekoKeiyakusakiInfo);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 施行プランコードが存在し見積式場が変わっていたら警告メッセージを出す
//        if (!empty($this->_sekoPlanCd)) {
//            if ($oldSekoKihon['est_shikijo_cd'] != $dataSekoKihon['est_shikijo_cd']) {
//                $data['status'] = 'NG';
//                $data['msg'] = "基本パターンが変更されるため、見積式場を変更することができません。処理を続行するには基本プランを一度削除する必要があります。";
//                Msi_Sys_Utils::outJson($data);
//                return;
//            }
//        }
        // 施行プランコードが存在し会員区分が変わっていたら警告メッセージを出す(見積確定前のみ)
//        if (!$this->isMitsuKakutei() && !empty($this->_sekoPlanCd)) {
//            if ($oldSekoKihon['kaiin_kbn'] != $dataSekoKihon['kaiin_kbn']) {
//                $data['status'] = 'NG';
//                $data['msg'] = "基本パターンが変更されるため、会員区分を変更することができません。処理を続行するには基本プランを一度削除する必要があります。";
//                Msi_Sys_Utils::outJson($data);
//                return;
//            }
//        }
        $gojokai_kbn_change = false; // 互助会区分変更フラグ
        if ($this->_gojokaiKbn !== "" && $this->_gojokaiKbn !== $dataSekoKihon['gojokai_kbn']) {
            $gojokai_kbn_change = true;
        }
        $denpyoNo = $this->getJuchudenpyoNo();

        // 駐車場警備依頼書を作成するか
        // $flg_irai 0:なし 1:削除のみ 2 作成と削除
        $flg_irai = $this->checkIfCreateIrai($db, $dataNiteiCol);
        if ($flg_irai > 0) {
            $this->saveSpecialHachuInfo($db, '36', $flg_irai);
        }
        // 34:寺院依頼書 35:葬儀・喪家情報確認書作成処理
        $this->saveSpecialHachuInfo2($db, $dataSekoKihon);
        // 施行日程保存処理 
        if (empty($dataSekoKihon['seko_no']) || $changeFlg['niteiChangeFlg'] || $changeFlg['kihonChangeFlg']) {
            $msg = $this->saveSekoNitei($db, $dataSekoKihon, $dataNiteiCol);
            if (isset($msg)) {
                $data['status'] = 'NG';
                $data['msg'] = $msg;
                Msi_Sys_Utils::outJson($data);
                return;
            }
        }
        // 請求先情報保存処理
        $cnt += $this->saveSekyuInfo($db,$dataSekoKihon,$dataSekyuInfo);
        // 基本情報の請求コード更新のため
        $changeFlg['kihonChangeFlg'] = true;
        $dataSekoKihon['sekyu_cd'] = $this->_sekyuCd;
        // 施行互助会情報保存処理 
        $oldGojokaiInfo = $this->getGojokaiInfo();
        if ($changeFlg['gojokaiInfoChangeFlg'] || $changeFlg['gojokaiMemberChangeFlg']) {
            $dataGojokaiInfo = $this->saveGojokaiInfo($db, $dataGojokaiInfo, $dataGojokaiMemberCol);
        }
        // 会員区分が互助会で利用コースの設定がなければメッセージを出す
//        if ($dataSekoKihon['kaiin_kbn'] == self::KAIIN_KBN_GOJO && isset($dataGojokaiInfo['nebiki_yoto_kbn']) && strlen($dataGojokaiInfo['use_cose']) == 0) {
//            $data['status'] = 'NG';
//            $data['msg'] = "ご利用コースが設定されていません。";
//            Msi_Sys_Utils::outJson($data);
//            return;
//        }
        // 施行プランコードが存在し、値引用途区分が変わっていたら警告メッセージを出す(見積確定前のみ)
//        if (!$this->isMitsuKakutei() && !empty($this->_sekoPlanCd)) {
//            // OM使用の場合は用途区分の変更で判別
//            $omYotoKbn = null;
//            foreach ($dataGojokaiMemberCol as $onerow) {
//                if ($onerow['kaiin_info_kbn'] == self::KAIIN_INFO_OM) {
//                    $omYotoKbn = $onerow['yoto_kbn'];
//                }
//            }
//            if (isset($omYotoKbn)) {
//                $oldGojokaiMemberDataOne = DataMapper_SekoGojokaiMember::findOne($db, array('seko_no' => $this->_sekoNo, 'kaiin_info_kbn' => static::KAIIN_INFO_OM));
//                if ($oldGojokaiMemberDataOne['yoto_kbn'] != $omYotoKbn) {
//                    $data['status'] = 'NG';
//                    $data['msg'] = "互助会の用途が変更されるため、更新することができません。処理を続行するには基本プランを一度削除する必要があります。";
//                    Msi_Sys_Utils::outJson($data);
//                    return;
//                }
//            } else {
//                if ((Msi_Sys_Utils::myCount($oldGojokaiInfo) > 0) && ($oldGojokaiInfo['nebiki_yoto_kbn'] != $dataGojokaiInfo['nebiki_yoto_kbn'])) {
//                    $data['status'] = 'NG';
//                    $data['msg'] = "互助会の用途が変更されるため、更新することができません。処理を続行するには基本プランを一度削除する必要があります。";
//                    Msi_Sys_Utils::outJson($data);
//                    return;
//                }                
//            }
//        }
        // 施行互助会加入者保存処理 (葬儀日変更時も対象にするため、niteiChangeFlg追加)
        if ($changeFlg['gojokaiMemberChangeFlg'] || $changeFlg['niteiChangeFlg']) {
            $cnt += $this->saveGojokaiMember($db, $dataGojokaiMemberCol);
        }
        // 施行契約先情報保存処理 
        if ($changeFlg['sekoKeiyakusakiInfoChangeFlg']) {
            $cnt = $this->saveSekoKeiyakusakiInfo($db, $dataSekoKeiyakusakiInfo);
        }
        // 施行受付履歴保存処理
        if ($changeFlg['sekoMemoChangeFlg']) {
            $cnt += $this->saveUketsukeInfo($db, $dataUketsukeInfoCol);
        }
        // 施行基本保存処理 
//        if ($changeFlg['kihonChangeFlg']) {
            //更新時のみチェック
            if ($dataSekoKihon['seko_no'] <> null) {
                //施行基本情報取得
                $select = DataMapper_SekoKihon::find2($this->_sekoNo);
                if (count($select) == 0) {
                    $data['status'] = 'NG';
                    $data['msg'] = "施行情報が削除されています。確認してください。";
                    Msi_Sys_Utils::outJson($data);
                    return;
                }
                //申込区分変更あり
                if ($select['moushi_kbn'] <> $dataSekoKihon['moushi_kbn']) {
                    //■申込区分が変更された場合、以下のチェックを行う
                    //見積確定の場合、変更不可
                    if ($select['status_kbn'] >= static::STATUS_KBN_SEKOKAKUTEI) {
                        $data['status'] = 'NG';
                        $data['msg'] = "施行金額確定済みなので申込区分は変更できません。";
                        Msi_Sys_Utils::outJson($data);
                        return;
                    }
                    //「事前相談」に変更
                    if ($dataSekoKihon['moushi_kbn'] == self::MOUSHI_KBN_SEIZEN) {
                        //変更後が「事前相談」の場合
                        //別注品の登録があれば、変更不可
                        $sql = "
                                SELECT
                                     seko_no
                                    ,COUNT(*) AS bechu_cnt
                                FROM uriage_denpyo
                                WHERE seko_no = :seko_no
                                    AND delete_flg = 0
                                GROUP BY seko_no
                                ";
                        $bechu = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
                        if ($bechu != null) {
                            if ($bechu['bechu_cnt'] != '0') {
                                $data['status'] = 'NG';
                                $data['msg'] = "当社葬家受注の登録があるため、申込区分を「事前相談」には変更はできません。";
                                Msi_Sys_Utils::outJson($data);
                                return;
                            }
                        }
                        //発注済みがあれば、変更不可
                        $sql = "
                                SELECT
                                     seko_no
                                    ,COUNT(*)   AS  hachu_cnt
                                FROM seko_hachu_info
                                WHERE seko_no = :seko_no
                                    AND data_kbn = 1
                                    AND order_flg <> 0
                                    AND delete_flg = 0
                                GROUP BY seko_no
                                ";
                        $hachu = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
                        if ($hachu != null) {
                            if ($hachu['hachu_cnt'] != '0') {
                                $data['status'] = 'NG';
                                $data['msg'] = "発注済みの商品があるため、申込区分を「事前相談」には変更はできません。";
                                Msi_Sys_Utils::outJson($data);
                                return;
                            }
                        }
                    }
                }
            }
            // 部門コード変更フラグ
            $bumonCdChangeFlg = false;
            $parBumon = App_Utils::parBumonOfHall($dataSekoKihon['bumon_cd']);
            $dataSekoKihon['oya_bumon_cd'] = $parBumon['bumon_cd'];
            $cnt = $this->saveSekoKihon($db, $dataSekoKihon, $bumonCdChangeFlg, $dataNiteiCol, $dataSekoKeiyakusakiInfo);
            if ($bumonCdChangeFlg) {
                // 伝票の部門コードを更新する。
                $cnt += $this->updateDenpyoBumoncd($db, $denpyoNo, $dataSekoKihon['bumon_cd']);
                $this->_bumonCd = $dataSekoKihon['bumon_cd'];
            }
            // 伝票の担当コードを更新する
            $cnt += $this->updateDenpyoDantocd($db, $dataSekoKihon['seko_tanto_cd']);
//       }
        if (!$this->isMitsuKakutei()) { // 見積未確定
            // 受注明細の付帯値引きの再設定を行う
//            if ($changeFlg['gojokaiInfoChangeFlg'] || $changeFlg['gojokaiMemberChangeFlg']) {
            $dataApp = array();
            $msiData = $this->getJuchuMsiData();
            App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
            $cnt += $this->saveJuchu($db, $dataApp, $msiData);
            // 事前相談受注伝票履歴番号が設定されている場合は現在のものを履歴にして書き換える
            if ($dataSekoKihon['jizen_history_no'] != $oldSekoKihon['jizen_history_no']) {
                if (isset($dataSekoKihon['jizen_history_no']) && strlen($dataSekoKihon['jizen_history_no']) > 0) {
                    $this->updateJuchuDenpyo($db, $dataSekoKihon);
                    // 再設定
                    $dataApp = array();
                    $msiData = $this->getJuchuMsiData();
                    App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
                    $cnt += $this->saveJuchu($db, $dataApp, $msiData);
                }
            }
//            }
        } else {
            // 施行金額確定されていたらスルー
            $status_kbn = $this->getSekoStatusKbn();
            if (isset($status_kbn) && $status_kbn < static::STATUS_KBN_SEKOKAKUTEI) {
                $dataApp = array();
                $delCol = array();
                $msiData = $this->getUriageMsiData();
                App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
                $cnt += $this->saveUriage($db, $dataApp, $msiData, $delCol);
            }
        }
        $denpyoNo = $this->getJuchudenpyoNo();
        // 各伝票請求先情報更新処理
        $cnt += $this->saveSekyuSakiInfo($db, $dataSekoKihon, $dataSekyuInfo);
        // 施行基本フリー保存処理 
//        if ($changeFlg['kihonFreeChangeFlg'] || $changeFlg['kihonChangeFlg']) {
            $dataSekoKihonFree = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonFreeJson'));
            $cnt += $this->saveKihonFree($db, $dataSekoKihonFree, $dataSekoKihon, $dataSekoKeiyakusakiInfo);
//        }
        // 施行引継書情報 保存処理 
        if ($changeFlg['hikitsugiChangeFlg'] || $changeFlg['kihonChangeFlg']) {
            $dataHikitsugi = Msi_Sys_Utils::json_decode($req->getPost('dataHikitsugiJson'));
            $cnt += $this->saveHikitsugi($db, $dataHikitsugi);
        }
        // 手配依頼書情報 保存処理 
        if ($changeFlg['tehaiIraiChangeFlg'] || $changeFlg['kihonChangeFlg']) {
            $dataTehaiIrai = Msi_Sys_Utils::json_decode($req->getPost('dataTehaiIraiJson'));
            $cnt += $this->saveTehaiIrai($db, $dataTehaiIrai);
        }
        // 式場移動情報 保存処理 
        if ($changeFlg['shikijoChangeFlg'] || $changeFlg['kihonChangeFlg']) {
            $dataShikijoCol = Msi_Sys_Utils::json_decode($req->getPost('dataShikijoIdoJson'));
            $cnt += $this->saveShikijoIdo($db, $dataShikijoCol);
        }
        // 手配依頼書情報（互助会会員情報から他CIF検索情報にデータを保存） 保存処理 
        if ($changeFlg['gojokaiMemberChangeFlg'] || $changeFlg['kihonChangeFlg']) {
            $cnt += $this->saveCifOther($db, $dataGojokaiMemberCol, $dataSekyuInfo, $dataSekoKihon);
        }
        // 訃報連携情報保存処理
        if ($changeFlg['huhoInfoChangeFlg']) {
            $cnt += $this->saveHuhoInfo($db, $dataHuhoInfo);
        }
        // 結果報告情報保存処理
        if($changeFlg['resultInfoChangeFlg']){
            $cnt += $this->saveResultInfo($db, $dataResultInfo);
        }
        // 施行者カード情報保存処理
        if($changeFlg['sekoshaChangeFlg']){
            $cnt += $this->saveSekoshaInfo($db, $dataSekoshaInfo);
        }
        // 
        // 更新後の更新履歴対象項目を取得する
        $newdataUpdateHistoryContents = $this->getUpdateHistoryContents();
        $this->saveUchiawaseInfo($db, $orgUpdateHistroyContents, $newdataUpdateHistoryContents);
        // 受注業務の新規作成時は施行管理情報を更新しない
        if (!App_Utils::isCustomerNewinJuchu($controllerName)) {
            $dataSbt = 2;
            if ($controllerName === self::MITSU) {
                $dataSbt = 1; // 見積書の場合
                App_Utils::saveTantoSekoInfo($this->_sekoNo);
            }
            // 施行管理情報を更新する
            App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, $dataSbt, 1);
        }

        // 2019/05/07 mihara keigen
        try {
            App_KeigenUtils::sougiYmdChgEnd($db, $this->_sekoNo, $denpyoNo); // sougi_ymd 変更時の調整
        } catch (Exception $e) {
            $data = array('status' => 'NG', 'msg' => $e->getMessage());
            Msi_Sys_Utils::outJson($data);
            return;
        }
       // 霊柩車搬送料金作成処理
        $juchuReikyusya = new Juchu_JuchuReikyusya();
        $cnt += $juchuReikyusya->saveReikyuData($db, $this->_sekoNo);
        // オーダーメイド引継商品作成処理(見積未確定時)
//        if (!$this->isMitsuKakutei() && $changeFlg['gojokaiMemberChangeFlg']) {
//            $juchuOrderMade = new Juchu_JuchuOrdermade();
//            $cnt += $juchuOrderMade->saveOrdermadeData($db, $this->_sekoNo);
//        }
        // 会員値引商品作成処理
        $juchuKaiinWari = new Juchu_JuchuKaiinwari();
        $cnt += $juchuKaiinWari->saveWaribikiData($db, $this->_sekoNo);
        // 商品部門チェック(見積式場が設定されているときのみ)
//        if (!$this->checkShohinBumon($db, $dataSekoKihon)) {
//            return;
//        }
        // 施行金額確定されていたらスルー
        if ($dataSekoKihon['status_kbn'] < static::STATUS_KBN_SEKOKAKUTEI) {
//            $reqData = $this->makeMocData($db, $dataSekoKihon, $dataGojokaiMemberCol,$dataGojokaiMemberDeleteCol);
//            if (count($reqData['ContractInfos']) > 0) {
//                $rtnData = Logic_Exkaiin_Moc2KaiinUpdSeko2::doExec($db, $reqData);
//                $msg = null; 
//                $gojokaiCouseMst = array_values($this->getGojokaiCouseMst());
//                $taxInfoAll = App_ClsTaxLib::GetTaxInfoAll($db);
//                foreach ($rtnData['ContractInfos'] as $onerow) {
//                    if (isset($onerow['UpdateStatus']) && $onerow['UpdateStatus'] == '1') {
//                        $msg =  '加入者番号('.$onerow['ContractNo'].')'.$onerow['UpdateMessage'];
//                        continue;
//                    }
//                    $keyIndex = array_search($onerow['ContractNo'], array_column($dataGojokaiMemberCol, 'v_free17'));
//                    if (!is_int($keyIndex)) {
//                        continue;
//                    }
//                    // 試算結果に更新する
//                    $gojoKeyIndex = array_search($onerow['CourseName'], array_column($gojokaiCouseMst, 'gojokai_cose_iw'));
//                    $taxKeyIndex = array_search($onerow['ContractTaxRate'], array_column($taxInfoAll, 'zei_rtu'));
//                    $dataGojokaiMemberCol[$keyIndex]['course_snm_cd'] = $gojokaiCouseMst[$gojoKeyIndex]['gojokai_cose_iw'];
////                    $dataGojokaiMemberCol[$keyIndex]['zei_cd'] = $taxInfoAll[$taxKeyIndex]['zei_cd'];
//                    $dataGojokaiMemberCol[$keyIndex]['kanyu_nm'] = $onerow['CustomerName'];
//                    $dataGojokaiMemberCol[$keyIndex]['kanyu_dt'] = $onerow['ContractDate'];
//                    $dataGojokaiMemberCol[$keyIndex]['keiyaku_gaku'] = $onerow['TerminationValue'];
////                    $dataGojokaiMemberCol[$keyIndex]['harai_no'] = $onerow['TotalPayNum'];
////                $dataGojokaiMemberCol[$keyIndex]['harai_gaku'] = $onerow['TotalPayValue'] - $onerow['BonusAmount'] - $onerow['BalanceDiscountValue'] - $onerow['PrepaymentDiscountValue'];
////                    $dataGojokaiMemberCol[$keyIndex]['wari_gaku'] = $onerow['PrepaymentDiscountValue'];
////                    $dataGojokaiMemberCol[$keyIndex]['waribiki_gaku'] = $onerow['BalanceDiscountValue'];
////                    $dataGojokaiMemberCol[$keyIndex]['early_use_cost_disp'] = $onerow['EarlyUseCost'];
////                    $dataGojokaiMemberCol[$keyIndex]['meigi_chg_cost_disp'] = $onerow['RenameCommission'];
//                    $dataGojokaiMemberCol[$keyIndex]['cur_cd'] = $onerow['ContractCode'];
//                    $dataGojokaiMemberCol[$keyIndex]['v_free10'] = $onerow['ContractStatus'];
////                    $dataGojokaiMemberCol[$keyIndex]['kanyu_tax'] = $onerow['ContractTax'];
////                    $dataGojokaiMemberCol[$keyIndex]['n_free3'] = $onerow['BonusAmount'];
////                    $dataGojokaiMemberCol[$keyIndex]['n_free4'] = $onerow['PremiumMonths'];
//                }
//                if (isset($msg)) {
//                    $data = array('status' => 'NG', 'msg' => '異常終了しました: '.$msg);
//                    Msi_Sys_Utils::outJson($data);
//                    return;
//                }
//                $Status = null;
//                if (array_key_exists('Result', $rtnData) && array_key_exists('Status', $rtnData['Result'])) {
//                    $Status = +$rtnData['Result']['Status'];
//                }
//                if ($Status == 1) { // 1:異常終了
//                    $msg = '異常終了しました: ' . $rtnData['Result']['Message'];
//                    $data = array('status' => 'NG', 'msg' => $msg);
//                    Msi_Sys_Utils::outJson($data);
//                    return;
//                }
//                // 互助会情報の再度保存処理を行う
//                $cnt += $this->saveGojokaiMember($db, $dataGojokaiMemberCol);
//            }
            $cnt += Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, '1', null, $this->_sekoNo); // 各種集計テーブル作成、更新処理
        }

        $db->commit();

        // 施行基本情報を取得する
        $dataSekoKihonNew = $this->getSekoKihon();
        // 施行基本フリー情報を取得する
        $dataKihonFreeNew = $this->getKihonFree();
        // 施行受付履歴を取得する
        $dataUketsukeInfoColNew = $this->getUketsukeInfo();
        // 更新履歴を取得する
        $dataUpdateHistoryColNew = $this->getUpdateHistory();
        // 施行日程情報を取得する
        $dataNiteiColNew = $this->getNitei($dataSekoKihonNew);
        // 施行請求先情報を取得する
        $dataSekyuInfoNew = $this->getSekyuInfo();
        // 施行互助会情報を取得する
        $dataGojokaiInfoNew = $this->getGojokaiInfo();
        // 施行互助会加入者を取得する
        $dataGojokaiMemberColNew = $this->getGojokaiMember(15);
        // 結果情報
        $dataResultInfoNew = $this->getResultInfo($db);
        // 施行者カード情報
        $dataSekoshaInfoNew = $this->getSekoshaInfo($db);
        // 施行契約先情報を取得する
        $dataSekoKeiyakusakiInfoNew = $this->getKeiyakusakiInfo();
        // 報告書情報を取得する
        $dataReportNew = $this->getReport();
        // 式場移動情報を取得する
        $dataShikijoColNew = $this->getShikijoIdo();
        // 訃報連絡情報を取得する
        $dataHuhoInfoNew = $this->getHuhoInfo();
        //$dataSekoKihonNew['uchiawase_tanto_nm'] = $dataAssignTantoInfo['uchiawase_tanto_nm'];
        //$dataSekoKihonNew['reikyu_tanto_nm'] = $dataAssignTantoInfo['reikyu_tanto_nm'];
        //$dataSekoKihonNew['kaso_tanto_nm'] = $dataAssignTantoInfo['kaso_tanto_nm'];
        //$dataSekoKihonNew['reikyu_hanso_ymd'] = $dataAssignTantoInfo['reikyu_hanso_ymd'];

        $dataSekoKihonNew['login_bumon_cd'] = App_Utils::getTantoBumonCd(App_Utils::getTantoCd());
        // 所属部門の親部門の配下の共通部門を取得する
        if (strlen($dataSekoKihonNew['seko_tanto_cd']) > 0) {
            $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $this->getTantoBumonCd($dataSekoKihonNew['seko_tanto_cd'])));
        } else {
            $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => App_Utils::getTantoBumonCd(App_Utils::getTantoCd())));
        }
        $comBumonCd = App_Utils2::getCommonBumonCd($bumonData[0]['bumon_cd']);
        $dataSekoKihonNew['shikijo_bumon_cd'] = $comBumonCd;

        // サイドメニューデータを取得する
        if (isset($sidemenukey) && strlen($sidemenukey)) {
            Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
            $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName, null, $sidemenukey);
        } else {
            if (App_Utils::isMitsuInJuchu($controllerName) || App_Utils::isCustomerNewinJuchu($controllerName)) {
                Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
                $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName);
            } else {
                $sideMenuData = $this->getSideMenuData();
            }
        }
        // お仕事メール
        $mail_info1 =$this->getMainInfo1($dataSekoKihonNew, $dataKihonFreeNew);
        // 結果メール
        $mail_info2 =$this->getMainInfo2($dataSekoKihonNew, $dataKihonFreeNew, $dataResultInfoNew, $dataNiteiColNew);
        $data = array(
            'dataSekoKihon' => $dataSekoKihonNew,
            'dataSekoKihonFree' => $dataKihonFreeNew,
            'dataUpdateHistoryCol' => $dataUpdateHistoryColNew,
            'dataNiteiCol' => $dataNiteiColNew,
            'dataSekyuInfo' => $dataSekyuInfoNew,
            'dataGojokaiInfo' => $dataGojokaiInfoNew,
            'dataGojokaiMemberCol' => $dataGojokaiMemberColNew,
            'dataSekoKeiyakusakiInfo' => $dataSekoKeiyakusakiInfoNew,
            'dataSekoMemoCol' => $dataUketsukeInfoColNew,
            'dataReport' => $dataReportNew,
            'dataShikijo' => $dataShikijoColNew,
            'dataSideMenu' => $sideMenuData,
            'dataShoudanRev' => $this->getConsultHistory(),
            'dataUpdateHistoryContents' => $newdataUpdateHistoryContents,
            'dataHuhoInfo' => $dataHuhoInfoNew,
            'dataKbns' => $this->getDataKbns(),
            'mail_info1' => $mail_info1,
            'mail_info2' => $mail_info2,
            'dataResultInfo' => $dataResultInfoNew,
            'dataSekoshaInfo' => $dataSekoshaInfoNew,
            'status' => 'OK',
            'msg' => ('更新しました'),
        );
        // フラグがあるときは訃報連携を行う
        $type = gettype($huho_flg);
        if ($type === 'boolean' ) {
            $_huho_flg = $huho_flg;
        } else {
            if ($huho_flg === 'true') {
                $_huho_flg = true;
            } else {
                $_huho_flg = false;
            }
        }
        if ($_huho_flg) {
            $returnData = $this->huhorenkei($db, $this->_sekoNo);
            if ($returnData['status'] !== 'OK') {
                Msi_Sys_Utils::outJson($returnData);
                return;
            }
            // 訃報情報は再取得する
            $dataHuhoInfoNew = $this->getHuhoInfo();
            $data['dataHuhoInfo'] = $dataHuhoInfoNew;
            $data['msg'] = $returnData['msg'];
        }
        $data['modCnts'] = $this->getModCnts();
        if ($kanyuAlertFlg) {
            $data['status'] = 'WARN';
            $data['msg'] = $alertMsg;
        }
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 保存処理(事前相談) 
     *
     * <AUTHOR> Tosaka
     * @since  2020/07/01
     * @param array $req リクエスト
     */
    public function preconsultSave($req) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();

        $controllerName = $req->getPost('controllerName');
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        $dataSekoKihonFree = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonFreeJson'));
        $dataJizen = Msi_Sys_Utils::json_decode($req->getPost('dataJizenJson'));
        $dataConsultHistory = Msi_Sys_Utils::json_decode($req->getPost('dataConsultHistoryColJson'));
        $dataGojokaiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiInfoJson'));
        $dataSekoKeiyakusakiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKeiyakusakiInfoJson'));
        $dataGojokaiMemberCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberColJson'));
        $dataGojokaiMemberDeleteCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberDeleteColJson'));
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));
        $this->_sekoNo = $dataSekoKihon['seko_no'];
        $this->_customerCd = $this->getCustomerCd();
        $orgModCnts = $req->getPost('orgModCnts');
//        Msi_Sys_Utils::debug( '1=>' . $this->_sekoNo );
        if (empty($this->_sekoNo)) {
            // 廃止部門チェック
            if (App_HakuzenUtils::isBumonHaishiFlgOn($dataSekoKihon['bumon_cd'], $db)) { // 廃止フラグ=1
                $data = array(
                    'status' => 'NG',
                    'msg' => "廃止された部門が設定されています。部門を変更してください。",
                );
                Msi_Sys_Utils::outJson($data);
                return;
            }
            $this->_sekoNo = $this->getAutoSekoNo($db, $dataSekoKihon);
//            Msi_Sys_Utils::debug( '2=>' . $this->_sekoNo );
            if (is_null($this->_sekoNo)) {
                return;
            }
        } else { // 2019/05/07 mihara keigen
            App_KeigenUtils::sougiYmdChgPre($this->_sekoNo); // sougi_ymd 変更の準備
        }

        if (empty($this->_customerCd)) {
            $this->_customerCd = $this->getAutoCustomerCd($db);
        }
        // 施行基本情報を設定する
        $this->setInitParam();
        if (empty($this->_moushiKbn)) {
            $this->_moushiKbn = $dataSekoKihon['moushi_kbn'];
        }
        if ($this->_moushiKbn != '5') {
            $data['status'] = 'NG';
            $data['msg'] = "施行データが存在しません。";
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 変更回数チェック
        $curModCnts = $this->getModCnts();
        if ($curModCnts != $orgModCnts) {
            $data['status'] = 'NG';
            $data['msg'] = '既にデータが変更されています。画面を更新してください。';
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 画面とステータス区分が異なる場合はエラー
        if (!$this->checkStatusKbn($dataSekoKihon)) {
            return;
        }
        // 画面とoidが異なる場合はエラー
        if (!$this->checkOid($dataSekoKihonFree)) {
            return;
        }
        // 加入員番号重複チェック
        $msg = $this->checkGojokaiMember($dataGojokaiMemberCol);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 会員区分チェック
        $msg = $this->checkKaiinKbn($dataSekoKihon, $dataGojokaiMemberCol);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 契約先番号チェック
        $msg = $this->checkKeiyakusaki($db, $dataSekoKihon, $dataSekoKeiyakusakiInfo);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 施行基本保存処理 
//        if ($changeFlg['kihonChangeFlg'] || $changeFlg['jizenChangeFlg'] || $changeFlg['kihonFreeChangeFlg']) {
            //更新時のみチェック
            if ($dataSekoKihon['seko_no'] <> null) {
                //施行基本情報取得
                $select = DataMapper_SekoKihon::find2($this->_sekoNo);
                if (count($select) == 0) {
                    $data['status'] = 'NG';
                    $data['msg'] = "施行情報が削除されています。確認してください。";
                    Msi_Sys_Utils::outJson($data);
                    return;
                }
            }
            // 部門コード変更フラグ
            $bumonCdChangeFlg = false;
            $parBumon = App_Utils::parBumonOfHall($dataSekoKihon['bumon_cd']);
            $dataSekoKihon['oya_bumon_cd'] = $parBumon['bumon_cd'];
            $dataNiteiCol = array();
            $cnt = $this->saveSekoKihon($db, $dataSekoKihon, $bumonCdChangeFlg, $dataNiteiCol, $dataSekoKeiyakusakiInfo);
//        }
        // 施行互助会情報保存処理 
        $oldGojokaiInfo = $this->getGojokaiInfo();
        if ($changeFlg['gojokaiInfoChangeFlg'] || $changeFlg['gojokaiMemberChangeFlg']) {
            $dataGojokaiInfo = $this->saveGojokaiInfo($db, $dataGojokaiInfo, $dataGojokaiMemberCol);
        }
        // 施行互助会加入者保存処理 (葬儀日変更時も対象にするため、niteiChangeFlg追加)
        if ($changeFlg['gojokaiMemberChangeFlg'] || $changeFlg['niteiChangeFlg']) {
            $cnt += $this->saveGojokaiMember($db, $dataGojokaiMemberCol);
        }
        // 施行契約先情報保存処理 
        if ($changeFlg['sekoKeiyakusakiInfoChangeFlg']) {
            $cnt = $this->saveSekoKeiyakusakiInfo($db, $dataSekoKeiyakusakiInfo);
        }
        // 施行基本フリー保存処理 
//        if ($changeFlg['kihonFreeChangeFlg'] || $changeFlg['kihonChangeFlg'] || $changeFlg['jizenChangeFlg']) {
            $cnt += $this->saveKihonFree($db, $dataSekoKihonFree, $dataSekoKihon,$dataSekoKeiyakusakiInfo);
//        }
        // 顧客基本情報保存処理
        if ($changeFlg['jizenChangeFlg']) {
            $this->saveCustomerBaseInfo($db, $dataJizen, $dataSekoKihon);
        }
        // 事前相談履歴更新
        if ($changeFlg['consultHistoryChangeFlg']) {
            $cnt += $this->saveReceptionHis($db, $dataConsultHistory);
        }
        // 受注業務の新規作成時は施行管理情報を更新しない
        if (!App_Utils::isCustomerNewinJuchu($controllerName)) {
            $dataSbt = 2;
            if ($controllerName === self::MITSU) {
                $dataSbt = 1; // 見積書の場合
                App_Utils::saveTantoSekoInfo($this->_sekoNo, $this->_moushiKbn);
            }
            // 施行管理情報を更新する
            App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, $dataSbt, 1);
        }
        // 受注明細の付帯値引きの再設定を行う
//            if ($changeFlg['gojokaiInfoChangeFlg'] || $changeFlg['gojokaiMemberChangeFlg']) {
        $dataApp = array();
        $msiData = $this->getJuchuMsiData();
        App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
        $cnt += $this->saveJuchu($db, $dataApp, $msiData);
        $denpyoNo = $this->getJuchudenpyoNo();
        // 2019/05/07 mihara keigen
        try {
            App_KeigenUtils::sougiYmdChgEnd($db, $this->_sekoNo, $denpyoNo); // sougi_ymd 変更時の調整
        } catch (Exception $e) {
            $data = array('status' => 'NG', 'msg' => $e->getMessage());
            Msi_Sys_Utils::outJson($data);
            return;
        }
//        $mycond = $this->makeMocData2($db, $dataSekoKihon, $dataGojokaiMemberCol);
//        if (count($mycond['s_member_no_id_arr']) > 0) {
//
//            $reqData = array( 'cond' => array(),
//                              'mycond' => $mycond );
//
//            // throw new Msi_Sys_Exception_InputException('ああああ');
//
//            $rtnData = App_HttpClient_Moc2Client2::reqJsonResJson("/mref/mmbr21exdialog/search", $reqData);
//            $msg = null;
//            $gojokaiCouseMst = array_values($this->getGojokaiCouseMst());
//            $taxInfoAll = App_ClsTaxLib::GetTaxInfoAll($db);
//            foreach ($rtnData['ContractInfos'] as $onerow) {
//                if (isset($onerow['UpdateStatus']) && $onerow['UpdateStatus'] == '1') {
//                    $msg = '加入者番号(' . $onerow['ContractNo'] . ')' . $onerow['UpdateMessage'];
//                    continue;
//                }
//                $keyIndex = array_search($onerow['ContractNo'], array_column($dataGojokaiMemberCol, 'v_free17'));
//                if (!is_int($keyIndex)) {
//                    continue;
//                }
//                // 試算結果に更新する
//                $gojoKeyIndex = array_search($onerow['CourseName'], array_column($gojokaiCouseMst, 'gojokai_cose_iw'));
//                $taxKeyIndex = array_search($onerow['ContractTaxRate'], array_column($taxInfoAll, 'zei_rtu'));
//                $dataGojokaiMemberCol[$keyIndex]['course_snm_cd'] = $gojokaiCouseMst[$gojoKeyIndex]['gojokai_cose_iw'];
////                $dataGojokaiMemberCol[$keyIndex]['zei_cd'] = $taxInfoAll[$taxKeyIndex]['zei_cd'];
//                $dataGojokaiMemberCol[$keyIndex]['kanyu_nm'] = $onerow['CustomerName'];
//                $dataGojokaiMemberCol[$keyIndex]['kanyu_dt'] = $onerow['ContractDate'];
//                $dataGojokaiMemberCol[$keyIndex]['keiyaku_gaku'] = $onerow['TerminationValue'];
////                $dataGojokaiMemberCol[$keyIndex]['harai_no'] = $onerow['TotalPayNum'];
////                $dataGojokaiMemberCol[$keyIndex]['harai_gaku'] = $onerow['TotalPayValue'] - $onerow['BonusAmount'] - $onerow['BalanceDiscountValue'] - $onerow['PrepaymentDiscountValue'];
////                $dataGojokaiMemberCol[$keyIndex]['wari_gaku'] = $onerow['PrepaymentDiscountValue'];
////                $dataGojokaiMemberCol[$keyIndex]['waribiki_gaku'] = $onerow['BalanceDiscountValue'];
////                $dataGojokaiMemberCol[$keyIndex]['early_use_cost_disp'] = $onerow['EarlyUseCost'];
////                $dataGojokaiMemberCol[$keyIndex]['meigi_chg_cost_disp'] = $onerow['RenameCommission'];
//                $dataGojokaiMemberCol[$keyIndex]['cur_cd'] = $onerow['ContractCode'];
//                $dataGojokaiMemberCol[$keyIndex]['v_free10'] = $onerow['ContractStatus'];
////                $dataGojokaiMemberCol[$keyIndex]['kanyu_tax'] = $onerow['ContractTax'];
////                $dataGojokaiMemberCol[$keyIndex]['n_free3'] = $onerow['BonusAmount'];
////                $dataGojokaiMemberCol[$keyIndex]['n_free4'] = $onerow['PremiumMonths'];
//            }
//            if (isset($msg)) {
//                $data = array('status' => 'NG', 'msg' => '異常終了しました: ' . $msg);
//                Msi_Sys_Utils::outJson($data);
//                return;
//            }
//            $Status = null;
//            if (array_key_exists('Result', $rtnData) && array_key_exists('Status', $rtnData['Result'])) {
//                $Status = +$rtnData['Result']['Status'];
//            }
//            if ($Status == 1) { // 1:異常終了
//                $msg = '異常終了しました: ' . $rtnData['Result']['Message'];
//                $data = array('status' => 'NG', 'msg' => $msg);
//                Msi_Sys_Utils::outJson($data);
//                return;
//            }
//            // 互助会情報の再度保存処理を行う
//            $cnt += $this->saveGojokaiMember($db, $dataGojokaiMemberCol);
//        }
        $cnt += Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, '1', null, $this->_sekoNo); // 各種集計テーブル作成、更新処理
        $db->commit();

        // 施行基本情報を取得する
        $dataSekoKihonNew = $this->getSekoKihon();
        // 施行基本フリー情報を取得する
        $dataKihonFreeNew = $this->getKihonFree();
        // 顧客基本情報を取得する
        $dataJizenNew = $this->getCustomerBaseInfo();
        // 相談履歴データを取得する
        $dataConsultHistoryColNew = $this->getConsultHistory();
        // 施行互助会情報を取得する
        $dataGojokaiInfoNew = $this->getGojokaiInfo();
        // 施行互助会加入者を取得する
        $dataGojokaiMemberColNew = $this->getGojokaiMember(15);
        // 施行契約先情報を取得する
        $dataSekoKeiyakusakiInfoNew = $this->getKeiyakusakiInfo();
        // サイドメニューデータを取得する
        if (App_Utils::isMitsuInJuchu($controllerName) || App_Utils::isCustomerNewinJuchu($controllerName)) {
            Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
            $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName);
        } else {
            $sideMenuData = $this->getSideMenuData();
        }
        $data = array(
            'dataSekoKihon' => $dataSekoKihonNew,
            'dataSekoKihonFree' => $dataKihonFreeNew,
            'dataJizen' => $dataJizenNew,
            'dataConsultHistoryCol' => $dataConsultHistoryColNew,
            'dataGojokaiInfo' => $dataGojokaiInfoNew,
            'dataGojokaiMemberCol' => $dataGojokaiMemberColNew,
            'dataSekoKeiyakusakiInfo' => $dataSekoKeiyakusakiInfoNew,
            'dataSideMenu' => $sideMenuData,
            'status' => 'OK',
            'msg' => ('更新しました'),
        );
        $data['modCnts'] = $this->getModCnts();
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 保存処理(オーダーメイド) 
     *
     * <AUTHOR> Tosaka
     * @since  2020/07/01
     * @param array $req リクエスト
     */
    public function ordermadeSave($req) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();

        $controllerName = $req->getPost('controllerName');
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        $dataSekoKihonFree = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonFreeJson'));
        $dataGojokaiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiInfoJson'));
        $dataSekoKeiyakusakiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKeiyakusakiInfoJson'));
        $dataGojokaiMemberCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberColJson'));
        $dataGojokaiMemberDeleteCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberDeleteColJson'));
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));
        // 加入員番号重複チェック
        $msg = $this->checkGojokaiMember($dataGojokaiMemberCol);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $this->_sekoNo = $dataSekoKihon['seko_no'];
        $this->_customerCd = $this->getCustomerCd();
//        Msi_Sys_Utils::debug( '1=>' . $this->_sekoNo );
        if (empty($this->_sekoNo)) {
            // 廃止部門チェック
            if (App_HakuzenUtils::isBumonHaishiFlgOn($dataSekoKihon['bumon_cd'], $db)) { // 廃止フラグ=1
                $data = array(
                    'status' => 'NG',
                    'msg' => "廃止された部門が設定されています。部門を変更してください。",
                );
                Msi_Sys_Utils::outJson($data);
                return;
            }
            $this->_sekoNo = $this->getAutoSekoNo2($db, $dataSekoKihon);
//            Msi_Sys_Utils::debug( '2=>' . $this->_sekoNo );
            if (is_null($this->_sekoNo)) {
                return;
            }
        } else { // 2019/05/07 mihara keigen
            App_KeigenUtils::sougiYmdChgPre($this->_sekoNo); // sougi_ymd 変更の準備
        }

        if (empty($this->_customerCd)) {
            $this->_customerCd = $this->getAutoCustomerCd($db);
        }
        // 施行基本情報を設定する
        $this->setInitParam();
        if (empty($this->_moushiKbn)) {
            $this->_moushiKbn = $dataSekoKihon['moushi_kbn'];
        }
        if (empty($this->_mitsuTantoCd)) {
            $this->_mitsuTantoCd = $dataSekoKihon['mitsu_tanto_cd'];
        }
        if (empty($this->_sekoTantoCd) && isset($dataSekoKihon['seko_tanto_cd'])) {
            $this->_sekoTantoCd = $dataSekoKihon['seko_tanto_cd'];
        }
        // 画面とステータス区分が異なる場合はエラー
        if (!$this->checkStatusKbn($dataSekoKihon)) {
            return;
        }
        // 商品部門チェック(見積式場が設定されているときのみ)
//        if (!$this->checkShohinBumon($db, $dataSekoKihon)) {
//            return;
//        }
        // 会員区分チェック
//        $msg = $this->checkKaiinKbn($dataSekoKihon, $dataGojokaiMemberCol);
//        if (isset($msg)) {
//            $data['status'] = 'NG';
//            $data['msg'] = $msg;
//            Msi_Sys_Utils::outJson($data);
//            return;
//        }
        // 契約先番号チェック
//        $msg = $this->checkKeiyakusaki($db, $dataSekoKihon, $dataSekoKeiyakusakiInfo);
//        if (isset($msg)) {
//            $data['status'] = 'NG';
//            $data['msg'] = $msg;
//            Msi_Sys_Utils::outJson($data);
//            return;
//        }
//        if ($this->_moushiKbn != '7') {
//            $data['status'] = 'NG';
//            $data['msg'] = "施行データが存在しません。";
//            Msi_Sys_Utils::outJson($data);
//            return;
//        }
        $denpyoNo = $this->getJuchudenpyoNo();
        // 施行互助会情報保存処理 
        if ($changeFlg['gojokaiInfoChangeFlg'] || $changeFlg['gojokaiMemberChangeFlg']) {
            $dataGojokaiInfo = $this->saveGojokaiInfo($db, $dataGojokaiInfo, $dataGojokaiMemberCol);
        }
        // 会員区分が互助会で利用コースの設定がなければメッセージを出す(オーダーメイドは例外)
        if ($dataSekoKihon['moushi_kbn'] != static::MOUSHI_KBN_ORDERMADE) {
            if ($dataSekoKihon['kaiin_kbn'] == self::KAIIN_KBN_GOJO && isset($dataGojokaiInfo['nebiki_yoto_kbn']) && strlen($dataGojokaiInfo['use_cose']) == 0) {
                $data['status'] = 'NG';
                $data['msg'] = "ご利用コースが設定されていません。";
                Msi_Sys_Utils::outJson($data);
                return;
            }
        }
        // 施行互助会加入者保存処理 (葬儀日変更時も対象にするため、niteiChangeFlg追加)
        if ($changeFlg['gojokaiMemberChangeFlg']) {
            $cnt += $this->saveGojokaiMember($db, $dataGojokaiMemberCol);
        }
        // 施行契約先情報保存処理 
        if ($changeFlg['sekoKeiyakusakiInfoChangeFlg']) {
            $cnt = $this->saveSekoKeiyakusakiInfo($db, $dataSekoKeiyakusakiInfo);
        }
        // 施行基本保存処理 
        if ($changeFlg['kihonChangeFlg']) {
            //更新時のみチェック
            if ($dataSekoKihon['seko_no'] <> null) {
                //施行基本情報取得
                $select = DataMapper_SekoKihon::find2($this->_sekoNo);
                if (count($select) == 0) {
                    $data['status'] = 'NG';
                    $data['msg'] = "施行情報が削除されています。確認してください。";
                    Msi_Sys_Utils::outJson($data);
                    return;
                }
            }
            // 部門コード変更フラグ
            $bumonCdChangeFlg = false;
            $parBumon = App_Utils::parBumonOfHall($dataSekoKihon['bumon_cd']);
            $dataSekoKihon['oya_bumon_cd'] = $parBumon['bumon_cd'];
            $dataNiteiCol = array();
            $cnt = $this->saveSekoKihon($db, $dataSekoKihon, $bumonCdChangeFlg, $dataNiteiCol);
            if ($bumonCdChangeFlg) {
                // 伝票の部門コードを更新する。
                $denpyoNo = $this->getJuchudenpyoNo();
                $cnt += $this->updateDenpyoBumoncd($db, $denpyoNo, $dataSekoKihon['bumon_cd']);
                $this->_bumonCd = $dataSekoKihon['bumon_cd'];
            }
            // 伝票の担当コードを更新する
            $cnt += $this->updateDenpyoDantocd($db, $dataSekoKihon['seko_tanto_cd']);
            if (!$this->isMitsuKakutei()) { // 見積未確定
                // 受注明細の付帯値引きの再設定を行う
                $dataApp = array();
                $msiData = $this->getJuchuMsiData();
                App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
                $cnt += $this->saveJuchu($db, $dataApp, $msiData);
            } else {
                $dataApp = array();
                $delCol = array();
                $msiData = $this->getUriageMsiData();
                App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
                $cnt += $this->saveUriage($db, $dataApp, $msiData, $delCol);
            }
        }
        // 施行基本フリー保存処理 
        if ($changeFlg['kihonFreeChangeFlg'] || $changeFlg['kihonChangeFlg']) {
            $cnt += $this->saveKihonFree($db, $dataSekoKihonFree, $dataSekoKihon, $dataSekoKeiyakusakiInfo);
        }
        // 受注業務の新規作成時は施行管理情報を更新しない
        if (!App_Utils::isCustomerNewinJuchu($controllerName)) {
            $dataSbt = 2;
            if ($controllerName === self::MITSU) {
                $dataSbt = 1; // 見積書の場合
                App_Utils::saveTantoSekoInfo($this->_sekoNo, $this->_moushiKbn);
            }
            // 施行管理情報を更新する
            App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, $dataSbt, 1);
        }
        try {
            App_KeigenUtils::sougiYmdChgEnd($db, $this->_sekoNo, $denpyoNo); // sougi_ymd 変更時の調整
        } catch (Exception $e) {
            $data = array('status' => 'NG', 'msg' => $e->getMessage());
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $cnt += Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, '1', null, $this->_sekoNo); // 各種集計テーブル作成、更新処理
        $db->commit();

        // 施行基本情報を取得する
        $dataSekoKihonNew = $this->getSekoKihon();
        // 施行基本フリー情報を取得する
        $dataKihonFreeNew = $this->getKihonFree();
        // 施行互助会情報を取得する
        $dataGojokaiInfoNew = $this->getGojokaiInfo();
        // 施行互助会加入者を取得する
        $dataGojokaiMemberColNew = $this->getGojokaiMember(15);
        // 施行契約先情報を取得する
        $dataSekoKeiyakusakiInfoNew = $this->getKeiyakusakiInfo();
        $dataSekoKihonNew['login_bumon_cd'] = App_Utils::getTantoBumonCd(App_Utils::getTantoCd());
        // 所属部門の親部門の配下の共通部門を取得する
        if (strlen($dataSekoKihonNew['seko_tanto_cd']) > 0) {
            $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $this->getTantoBumonCd($dataSekoKihonNew['seko_tanto_cd'])));
        } else {
            $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => App_Utils::getTantoBumonCd(App_Utils::getTantoCd())));
        }
        $comBumonCd = App_Utils2::getCommonBumonCd($bumonData[0]['bumon_cd']);
        $dataSekoKihonNew['shikijo_bumon_cd'] = $comBumonCd;
        // サイドメニューデータを取得する
        if (isset($sidemenukey)) {
            Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
            $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName, null, $sidemenukey);
        } else {
            if (App_Utils::isMitsuInJuchu($controllerName) || App_Utils::isCustomerNewinJuchu($controllerName)) {
                Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
                $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName);
            } else {
                $sideMenuData = $this->getSideMenuData();
            }
        }
        $data = array(
            'dataSekoKihon' => $dataSekoKihonNew,
            'dataSekoKihonFree' => $dataKihonFreeNew,
            'dataGojokaiInfo' => $dataGojokaiInfoNew,
            'dataGojokaiMemberCol' => $dataGojokaiMemberColNew,
            'dataSekoKeiyakusakiInfo' => $dataSekoKeiyakusakiInfoNew,
            'dataSideMenu' => $sideMenuData,
            'status' => 'OK',
            'msg' => ('更新しました'),
        );
        Msi_Sys_Utils::outJson($data);
    }
    
    /**
     * 施行基本保存処理 
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihon 画面施行基本データ
     * @param boolean &$bumonCdChangeFlg 部門コード変更フラグ
     * @param array $dataNiteiCol 画面施行日程データ
     * @param array $dataSekoKeiyakusakiInfo 画面会員情報データ
     * @return int 更新件数
     */
    private function saveSekoKihon($db, $dataSekoKihon, &$bumonCdChangeFlg, $dataNiteiCol, $dataSekoKeiyakusakiInfo=array()) {
        // 施行基本存在チェック
        $selectSekoKihon = $this->selectSekoKihon();
        $tableSekokihon = "seko_kihon_info";
        // 更新対象外項目設定
        $except = array('status_kbn', 'consult_seko_no', 'hanso_seko_no', 'k_last_nm_readonly', 'k_first_nm_readonly', 'k_file', 
                        'mitsu_tanto_cd', 'mitsu_tanto_nm', 'uketuke_tanto_nm', 'seko_tanto_nm', 'k_wa_year', 'jizen_history_no', 
                        'pacemaker_code_kbn', 'pacemaker_kbn', 'cause_death', 'infection_code_kbn', 'infection_umu_kbn', 'infection_txt', 
                        'm_sex_code_kbn', 'm_sex_kbn', 'careful_memo', 'm_file', 'm_wa_year', 'kaishu_ymd', 'kaimyo_kbn', 'kaimyo_code_kbn', 
                        'tera_shokai_kbn', 'tera_shokai_code_kbn', 'temple_person', 'shonanoka_kbn',
                        'temple_tel', 'temple_fax', 'temple_yubin_no', 'temple_addr1', 'temple_addr2', 'temple_tel2', 'temple_cd2', 'temple_nm2',
                        'ofuse', 'okuruma','irai_biko', 'temple2_yubin_no', 'temple2_addr1', 'temple2_addr2', 'temple2_tel',
                        'kaiso_cnt', 'shinzoku_cnt', 'tuya_check', 'kaso_check', 'shinzoku_cnt', 'sd_yotei_date', 'sd_yotei_time', 
                        'copy_moto_seko_no', 'copy_saki_seko_no', 'seika_contact', 'form_tel', 'form_fax', 'login_bumon_cd', 
                        'shikijo_bumon_cd', 'uchiawase_tanto_nm', 'reikyu_tanto_nm', 
                        'kaso_tanto_nm', 'reikyu_hanso_ymd', 'est_oya_bumon_cd', 'm_mail_address', 'keiyaku_wa_year', 'm_biko', 
                        'nyudensaki_cd1', 'nyudensaki_cd2', 
                        'nyudensaki_nm1', 'nyudensaki_nm2', 'shokuchi_kbn1', 'shokuchi_kbn2', 'nyudensha_nm', 'nyudensaki_tel1', 'nyudensaki_tel2', 
                        'shutsudo_cd11', 'shutsudo_cd12', 'shutsudo_cd13', 
                        'shutsudo_nm11', 'shutsudo_nm12', 'shutsudo_nm13', 'shutsudo_nm21', 'shutsudo_nm22', 'shutsudo_nm23', 'keisatsu_kbn', 'keisatsu_nm', 
                        'homonsaki_nm', 'homonsaki_tel1', 'homonsaki_tel2', 'homonsaki_zip_no', 'homonsaki_addr1', 'homonsaki_addr2', 
                        'izoku_cif_no', 'izoku_cif_status', 'dm_last_nm', 'dm_first_nm', 'dm_last_knm', 'dm_first_knm', 'dm_tel', 'dm_m_tel', 'izoku_file_nm', 'izoku_file',
                        'hs_name_input_kbn', 'seko_biko1', 'dm_yubin_no', 'dm_addr1', 'dm_addr2',
                        'todokede_kbn', 'todokede_nm',
                        'kaiin_sbt_code_cd', 'kaiin_sbt_cd',
            );
        // emptyToNull
        $dataSekoKihon = Msi_Sys_Utils::emptyToNullArr($dataSekoKihon);
        // カラムに合わせて代入
        App_Utils2::issetColumn($dataSekoKihon, 'kaiin_kbn','gojokai_kbn');
        App_Utils2::issetColumn($dataSekoKihon, 'syushi_kbn','plan_syushi_kbn');
        App_Utils2::issetColumn($dataSekoKihon, 'tera_shokai_code_kbn','free1_code_cd');
        App_Utils2::issetColumn($dataSekoKihon, 'tera_shokai_kbn','free1_kbn');
        App_Utils2::issetColumn($dataSekoKihon, 'pacemaker_code_kbn','free4_code_cd');
        App_Utils2::issetColumn($dataSekoKihon, 'pacemaker_kbn','free4_kbn');
        App_Utils2::issetColumn($dataSekoKihon, 'infection_code_kbn','free5_code_cd');
        App_Utils2::issetColumn($dataSekoKihon, 'infection_umu_kbn','free5_kbn');
        App_Utils2::issetColumn($dataSekoKihon, 'm_sex_code_kbn','free6_code_cd');
        App_Utils2::issetColumn($dataSekoKihon, 'm_sex_kbn','free6_kbn');
        App_Utils2::issetColumn($dataSekoKihon, 'cause_death','v_free2');
        App_Utils2::issetColumn($dataSekoKihon, 'infection_txt','v_free3');
        App_Utils2::issetColumn($dataSekoKihon, 'seika_contact','v_free4');
        App_Utils2::issetColumn($dataSekoKihon, 'form_tel','v_free5');
        App_Utils2::issetColumn($dataSekoKihon, 'form_fax','v_free6');
        App_Utils2::issetColumn($dataSekoKihon, 'careful_memo','v_free8');
        App_Utils2::issetColumn($dataSekoKihon, 'temple_person','n_free1');
        App_Utils2::issetColumn($dataSekoKihon, 'consult_seko_no','free3_cd');
        App_Utils2::issetColumn($dataSekoKihon, 'hanso_seko_no','free5_cd');
        App_Utils2::issetColumn($dataSekoKihon, 'kaiso_cnt','n_free5');
        App_Utils2::issetColumn($dataSekoKihon, 'shinzoku_cnt','n_free4');
        App_Utils2::issetColumn($dataSekoKihon, 'jizen_history_no','n_free8');
        if (!isset($dataSekoKihon['hs_anchi_nm'])){
            $dataSekoKihon['hs_anchi_nm'] = '';
        }
        // 入電
        $dataSekoKihon['free1_cd']  = $dataSekoKihon['nyudensaki_cd1']; // 入電先1
        $dataSekoKihon['free2_cd']  = $dataSekoKihon['nyudensaki_cd2']; // 入電先2
        //出動者
        $dataSekoKihon['iso_tanto_cd1'] = $dataSekoKihon['shutsudo_cd11'];
        $dataSekoKihon['iso_tanto_cd2'] = $dataSekoKihon['shutsudo_cd12'];
        $dataSekoKihon['iso_tanto_cd3'] = $dataSekoKihon['shutsudo_cd13'];
        // 遺族
        $dataSekoKihon['free4_cd'] = $dataSekoKihon['izoku_cif_no'];
        $dataSekoKihon['n_free14'] = $dataSekoKihon['izoku_cif_status'];
        $dataSekoKihon['v_free24'] = $dataSekoKihon['dm_last_nm'];
        $dataSekoKihon['v_free25'] = $dataSekoKihon['dm_first_nm'];
        $dataSekoKihon['v_free26'] = $dataSekoKihon['dm_last_knm'];
        $dataSekoKihon['v_free27'] = $dataSekoKihon['dm_first_knm'];
        $dataSekoKihon['v_free28'] = $dataSekoKihon['dm_tel'];
        $dataSekoKihon['v_free29'] = $dataSekoKihon['dm_m_tel'];
        $dataSekoKihon['v_free30'] = $dataSekoKihon['dm_yubin_no'];
        $dataSekoKihon['v_free31'] = $dataSekoKihon['dm_addr1'];
        $dataSekoKihon['v_free32'] = $dataSekoKihon['dm_addr2'];
        // 遺族添付画像
        $dataSekoKihon['img_free2'] = null;
        if ($dataSekoKihon['izoku_file']) {
            $dataSekoKihon['img_free2'] = $dataSekoKihon['izoku_file'];
        }
        // 連絡事項
        $dataSekoKihon['biko1'] = $dataSekoKihon['seko_biko1'];
        
        
        // 名前の連結
        if (isset($dataSekoKihon['k_last_nm'], $dataSekoKihon['k_first_nm']) && strlen($dataSekoKihon['k_last_nm']) > 0 && strlen($dataSekoKihon['k_first_nm']) > 0) {
            $dataSekoKihon['k_nm'] = $dataSekoKihon['k_last_nm'].'　'.$dataSekoKihon['k_first_nm'];
        } else if (isset($dataSekoKihon['k_last_nm']) && strlen($dataSekoKihon['k_last_nm']) > 0) {
            $dataSekoKihon['k_nm'] = $dataSekoKihon['k_last_nm'];
        } else if (isset($dataSekoKihon['k_first_nm']) && strlen($dataSekoKihon['k_first_nm']) > 0) {
            $dataSekoKihon['k_nm'] = $dataSekoKihon['k_first_nm'];
        } else {
            $dataSekoKihon['k_nm'] = null;
        }
        if (isset($dataSekoKihon['k_last_knm'], $dataSekoKihon['k_first_knm']) && strlen($dataSekoKihon['k_last_knm']) > 0 && strlen($dataSekoKihon['k_first_knm']) > 0) {
            $dataSekoKihon['k_knm'] = $dataSekoKihon['k_last_knm'].'　'.$dataSekoKihon['k_first_knm'];
        } else if (isset($dataSekoKihon['k_last_knm']) && strlen($dataSekoKihon['k_last_knm']) > 0) {
            $dataSekoKihon['k_knm'] = $dataSekoKihon['k_last_knm'];
        } else if (isset($dataSekoKihon['k_first_knm']) && strlen($dataSekoKihon['k_first_knm']) > 0) {
            $dataSekoKihon['k_knm'] = $dataSekoKihon['k_first_knm'];
        } else {
            $dataSekoKihon['k_knm'] = null;
        }
        if (isset($dataSekoKihon['m_last_nm'], $dataSekoKihon['m_first_nm']) && strlen($dataSekoKihon['m_last_nm']) > 0 && strlen($dataSekoKihon['m_first_nm']) > 0) {
            $dataSekoKihon['m_nm'] = $dataSekoKihon['m_last_nm'].'　'.$dataSekoKihon['m_first_nm'];
        } else if (isset($dataSekoKihon['m_last_nm']) && strlen($dataSekoKihon['m_last_nm']) > 0) {
            $dataSekoKihon['m_nm'] = $dataSekoKihon['m_last_nm'];
        } else if (isset($dataSekoKihon['m_first_nm']) && strlen($dataSekoKihon['m_first_nm']) > 0) {
            $dataSekoKihon['m_nm'] = $dataSekoKihon['m_first_nm'];
        } else {
            $dataSekoKihon['m_nm'] = null;
        }
        if (isset($dataSekoKihon['m_last_knm'], $dataSekoKihon['m_first_knm']) && strlen($dataSekoKihon['m_last_knm']) > 0 && strlen($dataSekoKihon['m_first_knm']) > 0) {
            $dataSekoKihon['m_knm'] = $dataSekoKihon['m_last_knm'].'　'.$dataSekoKihon['m_first_knm'];
        } else if (isset($dataSekoKihon['m_last_knm']) && strlen($dataSekoKihon['m_last_knm']) > 0) {
            $dataSekoKihon['m_knm'] = $dataSekoKihon['m_last_knm'];
        } else if (isset($dataSekoKihon['m_first_knm']) && strlen($dataSekoKihon['m_first_knm']) > 0) {
            $dataSekoKihon['m_knm'] = $dataSekoKihon['m_first_knm'];
        } else {
            $dataSekoKihon['m_knm'] = null;
        }
        if (isset($dataSekoKihon['fc_last_nm'], $dataSekoKihon['fc_first_nm']) && strlen($dataSekoKihon['fc_last_nm']) > 0 && strlen($dataSekoKihon['fc_first_nm']) > 0) {
            $dataSekoKihon['fc_nm'] = $dataSekoKihon['fc_last_nm'].'　'.$dataSekoKihon['fc_first_nm'];
        } else if (isset($dataSekoKihon['fc_last_nm']) && strlen($dataSekoKihon['fc_last_nm']) > 0) {
            $dataSekoKihon['fc_nm'] = $dataSekoKihon['fc_last_nm'];
        } else if (isset($dataSekoKihon['fc_first_nm']) && strlen($dataSekoKihon['fc_first_nm']) > 0) {
            $dataSekoKihon['fc_nm'] = $dataSekoKihon['fc_first_nm'];
        } else {
            $dataSekoKihon['fc_nm'] = null;
        } 
        if (isset($dataSekoKihon['fc_last_knm'], $dataSekoKihon['fc_first_knm']) && strlen($dataSekoKihon['fc_last_knm']) > 0 && strlen($dataSekoKihon['fc_first_knm']) > 0) {
            $dataSekoKihon['fc_knm'] = $dataSekoKihon['fc_last_knm'].'　'.$dataSekoKihon['fc_first_knm'];
        } else if (isset($dataSekoKihon['fc_last_knm']) && strlen($dataSekoKihon['fc_last_knm']) > 0) {
            $dataSekoKihon['fc_knm'] = $dataSekoKihon['fc_last_knm'];
        } else if (isset($dataSekoKihon['fc_first_knm']) && strlen($dataSekoKihon['fc_first_knm']) > 0) {
            $dataSekoKihon['fc_knm'] = $dataSekoKihon['fc_first_knm'];
        } else {
            $dataSekoKihon['fc_knm'] = null;
        }
        if (isset($dataSekoKihon['keiyaku_last_nm'],$dataSekoKihon['keiyaku_first_nm']) && strlen($dataSekoKihon['keiyaku_last_nm']) > 0 && strlen($dataSekoKihon['keiyaku_first_nm']) > 0) {
            $dataSekoKihon['keiyaku_nm'] = $dataSekoKihon['keiyaku_last_nm'].'　'.$dataSekoKihon['keiyaku_first_nm'];
        } else if (isset($dataSekoKihon['keiyaku_last_nm']) && strlen($dataSekoKihon['keiyaku_last_nm']) > 0) {
            $dataSekoKihon['keiyaku_nm'] = $dataSekoKihon['keiyaku_last_nm'];
        } else if (isset($dataSekoKihon['keiyaku_first_nm']) && strlen($dataSekoKihon['keiyaku_first_nm']) > 0) {
            $dataSekoKihon['keiyaku_nm'] = $dataSekoKihon['keiyaku_first_nm'];
        } else {
            $dataSekoKihon['keiyaku_nm'] = null;
        }
        if (isset($dataSekoKihon['keiyaku_last_knm'], $dataSekoKihon['keiyaku_first_knm']) && strlen($dataSekoKihon['keiyaku_last_knm']) > 0 && strlen($dataSekoKihon['keiyaku_first_knm']) > 0) {
            $dataSekoKihon['keiyaku_knm'] = $dataSekoKihon['keiyaku_last_knm'].'　'.$dataSekoKihon['keiyaku_first_knm'];
        } else if (isset($dataSekoKihon['keiyaku_last_knm']) && strlen($dataSekoKihon['keiyaku_last_knm']) > 0) {
            $dataSekoKihon['keiyaku_knm'] = $dataSekoKihon['keiyaku_last_knm'];
        } else if (isset($dataSekoKihon['keiyaku_first_knm']) && strlen($dataSekoKihon['keiyaku_first_knm']) > 0) {
            $dataSekoKihon['keiyaku_knm'] = $dataSekoKihon['keiyaku_first_knm'];
        } else {
            $dataSekoKihon['keiyaku_knm'] = null;
        }
        if (isset($dataSekoKihon['renraku_last_nm'], $dataSekoKihon['renraku_first_nm']) && strlen($dataSekoKihon['renraku_last_nm']) > 0 && strlen($dataSekoKihon['renraku_first_nm']) > 0) {
            $dataSekoKihon['renraku_nm'] = $dataSekoKihon['renraku_last_nm'].'　'.$dataSekoKihon['renraku_first_nm'];
        } else if (isset($dataSekoKihon['renraku_last_nm']) && strlen($dataSekoKihon['renraku_last_nm']) > 0) {
            $dataSekoKihon['renraku_nm'] = $dataSekoKihon['renraku_last_nm'];
        } else if (isset($dataSekoKihon['renraku_first_nm']) && strlen($dataSekoKihon['renraku_first_nm']) > 0) {
            $dataSekoKihon['renraku_nm'] = $dataSekoKihon['renraku_first_nm'];
        } else {
            $dataSekoKihon['renraku_nm'] = null;
        }
        if (isset($dataSekoKihon['renraku_last_knm'], $dataSekoKihon['renraku_first_knm']) && strlen($dataSekoKihon['renraku_last_knm']) > 0 && strlen($dataSekoKihon['renraku_first_knm']) > 0) {
            $dataSekoKihon['renraku_knm'] = $dataSekoKihon['renraku_last_knm'].'　'.$dataSekoKihon['renraku_first_knm'];
        } else if (isset($dataSekoKihon['renraku_last_knm']) && strlen($dataSekoKihon['renraku_last_knm']) > 0) {
            $dataSekoKihon['renraku_knm'] = $dataSekoKihon['renraku_last_knm'];
        } else if (isset($dataSekoKihon['renraku_first_knm']) && strlen($dataSekoKihon['renraku_first_knm']) > 0) {
            $dataSekoKihon['renraku_knm'] = $dataSekoKihon['renraku_first_knm'];
        } else {
            $dataSekoKihon['renraku_knm'] = null;
        }
        // 故人名添付ファイル存在するときに登録する
        App_Utils2::issetColumn($dataSekoKihon, 'k_file','k_file_nm');
        // 喪主名添付ファイル存在するときに登録する
        App_Utils2::issetColumn($dataSekoKihon, 'm_file','m_file_nm');
        // 常に施行式場を設定して自営式場以外の場合は見積式場を設定する
        $keyIndex = array_search(self::NITEI_KBN_SOUGI, array_column($dataNiteiCol, 'nitei_kbn'));
        $sougi = null;
        if (isset($dataNiteiCol[$keyIndex])) {
            $sougi = $dataNiteiCol[$keyIndex];
        }
        // 事前相談の場合はスルーする
            if ($dataSekoKihon['moushi_kbn'] != self::MOUSHI_KBN_SEIZEN) {
            $est_shikijo_cd = null;
            $seko_shikijo_cd = null;
            if ($sougi['sikijo_check'] == '1') {
                $bumonData = DataMapper_Bumon::findOne($db, array('bumon_cd' => $dataSekoKihon['bumon_cd']));
                if (Msi_Sys_Utils::myCount($bumonData) > 0) {
                    $kaikeiBumon = DataMapper_Bumon::findOne($db, array('kaikei_bumon_cd' => $bumonData['kaikei_bumon_cd'], 'bumon_kbn' => 3));
                    if (Msi_Sys_Utils::myCount($kaikeiBumon) > 0) {
                        $est_shikijo_cd = $kaikeiBumon['bumon_cd'];
                        $seko_shikijo_cd = $kaikeiBumon['bumon_cd'];
                    }
                }
            } else {
                if (isset($sougi['basho_kbn']) && $sougi['basho_kbn'] == '2') {
                    if (isset($sougi['basho_cd'])) {
                        $kaijyoData = DataMapper_Kaijyo::find($db, array('kaijyo_cd' => $sougi['basho_cd']));
                        if (isset($kaijyoData[0]['bumon_cd'])) {
                            $est_shikijo_cd = $kaijyoData[0]['bumon_cd'];
                            // 式場の親部門と請負部門の会計部門が同一ならば見積式場と施行式場は同一
                            $est_bumon = DataMapper_Bumon::find($db, array('bumon_cd' => $kaijyoData[0]['bumon_cd']));
                            $seko_bumon = DataMapper_Bumon::find($db, array('bumon_cd' => $dataSekoKihon['bumon_cd']));
                            if ($est_bumon[0]['kaikei_bumon_cd'] == $seko_bumon[0]['kaikei_bumon_cd']) {
                                $seko_shikijo_cd = $kaijyoData[0]['bumon_cd'];
                            } else {
                                // 請負部門の会計部門配下の共通部門を施行式場に設定する
                                $bumonData = DataMapper_Bumon::find($db, array('kaikei_bumon_cd' => $seko_bumon[0]['kaikei_bumon_cd'], 'bumon_kbn' => 3));
                                $seko_shikijo_cd = $bumonData[0]['bumon_cd'];
                            }
                        }
                    }
                }
            }
            $dataSekoKihon['est_shikijo_cd'] = $est_shikijo_cd;
            $dataSekoKihon['seko_shikijo_cd'] = $seko_shikijo_cd;
        }
        // 事前相談の場合の処理
        if ($dataSekoKihon['moushi_kbn'] === self::MOUSHI_KBN_SEIZEN) {
            // 相談者
            if (strlen($dataSekoKihon['m_nm']) === 0 && strlen($dataSekoKihon['m_knm']) === 0) {
                $dataSekoKihon['n_free2'] = 0;
            } else {
                // CAP連携されてるかどうか
                if (isset($this->_selectSekoKihon['n_free2'])) {
                    $dataSekoKihon['n_free2'] = 1;
                } else if (strlen($dataSekoKihon['m_cif_no'])) {
                    // CIFNoが既にある場合は更新のみ
                    $dataSekoKihon['n_free2'] = 1;
                } else {
                    $dataSekoKihon['n_free2'] = 0;
                }
            }
            // 対象者
            if (strlen($dataSekoKihon['k_nm']) === 0 && strlen($dataSekoKihon['k_knm']) === 0) {
                $dataSekoKihon['n_free3'] = 0;
            } else {
                // CAP連携されてるかどうか
                if (isset($this->_selectSekoKihon['n_free3'])) {
                    $dataSekoKihon['n_free3'] = 1;
                } else if (strlen($dataSekoKihon['k_cif_no'])) {
                    // CIFNoが既にある場合は更新のみ
                    $dataSekoKihon['n_free3'] = 1;
                } else {
                    $dataSekoKihon['n_free3'] = 0;
                }
            }
        }
        // 施行プランコードがなければNULL
        if (!isset($selectSekoKihon['seko_plan_cd'])) {
            $dataSekoKihon['plan_shikijo_cd'] = null;
        }
        if (Msi_Sys_Utils::myCount($selectSekoKihon) === 0) {
            $dataSekoKihon['seko_no'] = $this->_sekoNo;
            // 施行基本登録SQL
            list($sqlSekoKihon, $param) = DataMapper_Utils::makeInsertSQL($tableSekokihon, $dataSekoKihon, $except);
        } else {
            if ($selectSekoKihon['bumon_cd'] !== $dataSekoKihon['bumon_cd']) {
                $bumonCdChangeFlg = true;
            }
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $dataSekoKihon['seko_no'];  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 施行基本更新SQL
            list($sqlSekoKihon, $param) = DataMapper_Utils::makeUpdateSQL($tableSekokihon, $dataSekoKihon, $where, $except);
        }
        $cnt = $db->easyExecute($sqlSekoKihon, $param);
        return $cnt;
    }

    /**
     * 施行日程保存処理 
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihon 画面施行日程データ
     * @param array $dataNiteiCol 画面日程タブデータ
     * @return int 更新件数
     */
    private function saveSekoNitei($db, $dataSekoKihon, $dataNiteiCol) {
        $cnt = 0;
        $msg = null;
        // 更新対象外項目設定
        $except = array();
        array_push($except, 'nitei_date');
        array_push($except, 'nitei_time');
        array_push($except, 'ts_based_nm');
        array_push($except, 'ts_based_nm2');
        array_push($except, 'date_kbn');
        array_push($except, 'nitei_ed_time');
        array_push($except, 'sikijo_check');
        array_push($except, 'sikijo_yoyaku_no');
        array_push($except, 'kaijo_bumon_cd');
        array_push($except, 'nitei_zip');
        array_push($except, 'nitei_addr1');
        array_push($except, 'nitei_addr2');
        array_push($except, 'nitei_tel');
        array_push($except, 'kasouba_zuikou_kbn');
        array_push($except, 'nitei_nm');
        
        
        // 施行基本フリー情報を取得する
        $dataKihonFree = $this->getKihonFree();
        
        // 施行日程データがない場合、登録する
        // 施行日程がある場合、更新する
        foreach ($dataNiteiCol as $niteiRow) {
            $start_ymd = null;
            $ed_ymd = null;
            $codeMst = DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_NITEI_SOUGI, 'kbn_value_cd' => $niteiRow['nitei_kbn']));
            // 式場予約があるものは入力時間が予約の範囲内かどうかチェックする(出棺は対象外)
            if ($niteiRow['nitei_kbn'] != self::NITEI_KBN_SYUKKAN && isset($niteiRow['sikijo_yoyaku_no'])) {
                if (isset($niteiRow['nitei_time']) && strlen($niteiRow['nitei_time'])) {
                    $start_ymd = $niteiRow['nitei_ymd'];
                } else {
                    $msg .= $codeMst[0]['kbn_value_lnm']."の開始日時が未入力です。";
                }
                if ($niteiRow['nitei_kbn'] == self::NITEI_KBN_TUYA || $niteiRow['nitei_kbn'] == self::NITEI_KBN_SOUGI) {
                    if (isset($niteiRow['nitei_ed_time']) && strlen($niteiRow['nitei_ed_time'])) {
                        $ed_ymd = $niteiRow['nitei_ed_ymd'];
                    } else {
                        $msg .= $codeMst[0]['kbn_value_lnm']."の終了日時が未入力です。";
                    }
                } else {
                    if (isset($niteiRow['nitei_time']) && strlen($niteiRow['nitei_time'])) {
                        $ed_ymd = $niteiRow['nitei_ymd'];
                    }
                }
                $yoyakuData = DataMapper_SisetsuYoyaku::find2($db, array('yoyaku_seko_no' => $this->_sekoNo, 'yoyaku_id' => $niteiRow['sikijo_yoyaku_no']));
                if (count($yoyakuData) == 0) {
                    $msg .= $codeMst[0]['kbn_value_lnm']."の式場予約のデータが存在しません。画面を更新してください。";
                } else {
                    // 時間入力がない場合はエラー
                    if (strlen($start_ymd) == 0 || strlen($ed_ymd) == 0) {
                        $msg .= $codeMst[0]['kbn_value_lnm']."の日時が施設予約の範囲外です(予約時間：".$yoyakuData[0]['start_ymdhm']."～".$yoyakuData[0]['end_ymdhm'].")。";
                    } else {
                        if ($start_ymd < $yoyakuData[0]['start_ymdhm'] || $yoyakuData[0]['end_ymdhm'] < $ed_ymd) {
                            $msg .= $codeMst[0]['kbn_value_lnm']."の日時が施設予約の範囲外です(予約時間：".$yoyakuData[0]['start_ymdhm']."～".$yoyakuData[0]['end_ymdhm'].")。";
                        }   
                    }
                }
            }
            // 日程区分 1:亡日 2:移動 3:入棺 4:通夜 5:出棺 6:火葬 7:葬儀 8:壇払
            $nitei_kbn = $niteiRow["nitei_kbn"];
            if (empty($niteiRow['nitei_time'])) {
                $niteiRow['free3_kbn'] = 1;
            } else {
                $niteiRow['free3_kbn'] = null;
            }
            if (empty($niteiRow['nitei_ed_time'])) {
                $niteiRow['nitei_ed_ymd'] = null;
            }
            if ($nitei_kbn == self::NITEI_KBN_TUYA) {
                $niteiRow['v_free1'] = $dataSekoKihon['tuya_check'];
            }
            if ($nitei_kbn == self::NITEI_KBN_KASO) {
                $niteiRow['v_free1'] = $dataSekoKihon['kaso_check'];
            }
            $niteiRow['v_free2'] = Msi_Sys_Utils::emptyToNull($niteiRow['sikijo_check']);
            
            $niteiRow['v_free4'] = Msi_Sys_Utils::emptyToNull($niteiRow['nitei_nm']);
            
            // 存在チェック
            $sqlSelectNitei = "
            SELECT
                sn.seko_no          -- 施行番号
                ,sn.nitei_kbn       -- 日程区分 
            FROM seko_nitei sn
            WHERE sn.seko_no = :seko_no
                AND sn.nitei_kbn = :nitei_kbn
                AND sn.delete_flg = 0
            ORDER BY sn.nitei_kbn
                ";
            $selectNitei = $db->easySelect($sqlSelectNitei, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => $nitei_kbn));
            if (count($selectNitei) === 0) {
                if ($nitei_kbn == self::NITEI_KBN_DEATH) {
                    $niteiRow['spot_code_kbn'] = '0220'; // 搬送業務区分
                } else if ($nitei_kbn != self::NITEI_KBN_KASO) {
                    $niteiRow['spot_code_kbn'] = self::CODE_SOGI_BASHO_KBN; // 葬儀場所
                }
                $niteiRow['seko_no'] = $this->_sekoNo;
                // 施行日程登録SQL
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_nitei", $niteiRow, $except);
            } else {
                array_push($except, 'seko_no');
                // 条件部
                $where['seko_no'] = $this->_sekoNo;  // 施行番号
                $where['nitei_kbn'] = $niteiRow['nitei_kbn'];  // 日程区分
                $where['delete_flg'] = 0;  // 削除フラグ
                // 施行基本更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_nitei", $niteiRow, $where, $except);
            }
            $cnt += $db->easyExecute($sql, $param);
            // 火葬場随行保存用
            if($niteiRow['nitei_kbn'] == 6){
                if(Msi_Sys_Utils::myCount($dataKihonFree) === 0){
                    $oneRow = array();
                    $oneRow['seko_no'] = $this->_sekoNo;
                    $oneRow['free_kbn3'] = Msi_Sys_Utils::emptyToNull($niteiRow['kasouba_zuikou_kbn']);
                    list($sql2, $param2) = DataMapper_Utils::makeInsertSQL("seko_kihon_all_free", $oneRow, $except);
                }else{
                    $except2 = array();
                    array_push($except2, 'seko_no');
                    $oneRow = array();
                    $oneRow['free_kbn3'] = Msi_Sys_Utils::emptyToNull($niteiRow['kasouba_zuikou_kbn']);
                    $where = array();
                    $where['seko_no'] = $this->_sekoNo;
                    list($sql2, $param2) = DataMapper_Utils::makeUpdateSQL("seko_kihon_all_free", $oneRow, $where, $except);
                }
                $cnt += $db->easyExecute($sql2, $param2);
            }
        }
        return $msg;
    }

    /**
     *
     * currentのcss名を取得する
     * 'customer' => 'お客様情報'
     * 'schedule' => 'タイムスケジュール'
     * 'sosogirei' => '葬送儀礼'
     * 'gift' => '返礼品'
     * 'cook' => '料理'
     * 'danbarai' => '壇払'
     * 'betto' => '別途費用'
     * 'betto' => '立替費用'
     * 'nebiki' => '値引'
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @return schedule
     */
    public function getCssClassName() {
        return 'customer';
    }

    /**
     *
     * 顧客施行履歴を取得する
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @version 2017/09/25 PHYO
     * @param string $seko_history_no 施行履歴
     * @return array 顧客施行履歴データ
     */
    private function getCstSekoHst($seko_history_no = '1') {
        $db = Msi_Sys_DbManager::getMyDb();

        $sql = "
        SELECT
            csh.customer_cd         -- 顧客コード
            ,csh.seko_history_no    -- 施行履歴№
            ,csh.seko_no            -- 施行番号
            ,TO_CHAR(csh.die_date ,'YYYY/MM/DD') AS die_date        -- 死亡日
            ,TO_CHAR(csh.funeral_date ,'YYYY/MM/DD') AS funeral_date-- 葬儀日
            ,csh.customer_kbn       -- 顧客区分
            ,csh.af_houji_kbn       -- 法事案内区分
            ,csh.af_altar_cd        -- 仏壇有無コード
            ,csh.af_altar_kbn       -- 仏壇有無区分
            ,csh.af_altar_state_cd  -- 仏壇無状況コード
            ,csh.af_altar_state_kbn -- 仏壇無状況区分
            ,csh.af_altar_note      -- 仏壇関係特記事項
            ,csh.af_grave_cd        -- お墓有無コード
            ,csh.af_grave_kbn       -- お墓有無区分
            ,csh.af_grave_state_cd  -- お墓無状況コード
            ,csh.af_grave_state_kbn -- お墓無状況区分
            ,csh.af_grave_note      -- お墓関係特記事項
            ,csh.af_etc_cd          -- その他有無コード
            ,csh.af_etc_kbn         -- その他有無区分
            ,csh.af_etc_state_cd    -- その他無状況コード
            ,csh.af_etc_state_kbn   -- その他無状況区分
            ,csh.af_etc_note        -- その他特記事項
            ,csh.af_advise_kbn      -- 相談窓口紹介区分
            ,csh.af_contact_cd      -- 連絡方法コード
            ,csh.af_contact_kbn     -- 連絡方法区分
            ,csh.af_contact_note    -- 連絡方法特記事項
			,csh.k_free2            -- 位牌区分
			,csh.k_free3            -- DM
			,csh.k_free4            -- 墓地
			,csh.v_free1            -- 墓地名
			,csh.k_free5            -- 墓石
			,csh.v_free2            -- 墓石名   
			,csh.select_kbn1        -- 御飯団子
			,csh.select_kbn2        -- S写真	
			,csh.v_free3            -- メモリアル担当   
			,csh.v_free4            -- アルタ担当  
			,csh.v_free5            -- 石材 
			,csh.select_kbn3        -- 礼状校正	
			,csh.select_kbn4        -- DVD
			,TO_CHAR(csh.d_free1 ,'YYYY/MM/DD') AS d_free1  -- 位牌届
			,TO_CHAR(csh.d_free2 ,'YYYY/MM/DD') AS d_free2  -- 礼状校正(日付)
        FROM customer_seko_history csh
        WHERE csh.customer_cd = :customer_cd
            AND csh.seko_history_no = :seko_history_no
            AND csh.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('customer_cd' => $this->_customerCd, 'seko_history_no' => $seko_history_no));
        return $select;
    }

    /**
     * 顧客施行履歴保存処理 
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @version  2017/09/25  PHYO
     * @param Msi_Sys_Db $db db
     * @param array $dataCstSeko 顧客施行履歴データ
     * @return int 更新件数
     */
    private function saveCstSekoHst($db, $dataCstSeko) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataCstSeko, 'customer_cd seko_history_no die_date funeral_date customer_kbn af_houji_kbn
                                                              af_altar_cd af_altar_kbn af_altar_state_cd af_altar_state_kbn af_altar_note
                                                              af_grave_cd af_grave_kbn af_grave_state_cd af_grave_state_kbn af_grave_note
                                                              af_etc_cd af_etc_state_cd af_etc_state_kbn af_etc_note af_advise_kbn
                                                              mk_tehai_cd mk_tehai_kbn souke_info_memo memo 
                                                              af_contact_cd af_contact_kbn af_contact_note k_free2 k_free3 k_free4 k_free5 
															  select_kbn1 select_kbn2 v_free1 v_free2 v_free3 v_free4 v_free5 select_kbn3 select_kbn4 d_free1 d_free2');
        $oneRow['af_altar_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['af_altar_cd']);
        $oneRow['af_grave_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['af_grave_cd']);
        $oneRow['af_altar_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['af_altar_kbn']);
        $oneRow['af_grave_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['af_grave_kbn']);
        $oneRow['die_date'] = Msi_Sys_Utils::emptyToNull($oneRow['die_date']);
        $oneRow['funeral_date'] = Msi_Sys_Utils::emptyToNull($oneRow['funeral_date']);
        $oneRow['d_free1'] = Msi_Sys_Utils::emptyToNull($oneRow['d_free1']);
        $oneRow['d_free2'] = Msi_Sys_Utils::emptyToNull($oneRow['d_free2']);

        $except = array();
        // 存在チェック
        $select = $this->getCstSekoHst($oneRow['seko_history_no']);
        if (count($select) === 0) {
            $oneRow['customer_cd'] = $this->_customerCd;
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("customer_seko_history", $oneRow);
        } else {
            array_push($except, 'customer_cd');
            array_push($except, 'seko_history_no');
            // 条件部
            $where['customer_cd'] = $this->_customerCd;  // 顧客コード
            $where['seko_history_no'] = $oneRow['seko_history_no'];  // 施行履歴№
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("customer_seko_history", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     * 顧客コード採番処理
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param Msi_Sys_Db $db db
     * @return string 顧客コード
     */
    private function getAutocustomerCd($db) {
        $kijyunYmd = Msi_Sys_Utils::getDate();
        $customerCd = App_ClsGetCodeNo::GetCodeNo($db, 'customer_base_info', 'customer_cd', $kijyunYmd);
        return $customerCd;
    }

    /**
     *
     * 依頼書を作成フラグ判定処理
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param Msi_Sys_Db $db db
     * @param array $dataNiteiCol 
     * @return $flg  0:なし 1:削除のみ 2 作成と削除
     */
    private function checkIfCreateIrai($db, $dataNiteiCol) {
        $flg = 0;
        $nitei_kbn = '7';
        // 葬儀場所 00 自宅 01 寺院 02 ホール 03 他会館 04 公民館 05 通夜会場 06 他寺院 99 その他
        $sikijyo = null;
        // 施行日程情報
        foreach ($dataNiteiCol as $oneRow) {
            if ($oneRow['nitei_kbn'] == $nitei_kbn) { // 7:葬儀
                $sikijyo = $oneRow['spot_cd'];
                break;
            }
        }

        $old_sikijyo = null;
        $sqlSelectNitei = "
            SELECT sn.spot_cd         
            FROM seko_nitei sn
            WHERE sn.seko_no = :seko_no
                AND sn.nitei_kbn = :nitei_kbn
                AND sn.delete_flg = 0
            ORDER BY sn.nitei_kbn
                ";
        $selectNitei = $db->easySelOne($sqlSelectNitei, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => $nitei_kbn));
        if (Msi_Sys_Utils::myCount($selectNitei) > 0) {
            $old_sikijyo = $selectNitei['spot_cd'];
        }
        if ($sikijyo != $old_sikijyo) {
            $flg = 1;
            if ($sikijyo == '02') {
                $flg = 2;
            }
        }
        return $flg;
    }

    /**
     *
     * 各種依頼書を作成する
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param Msi_Sys_Db $db db
     * @param string $ha_rp_kbn 発注書区分
     * @param boolean $createFlg 作成フラグ
     * @return $cnt 処理件数
     */
    private function saveSpecialHachuInfo($db, $ha_rp_kbn, $createFlg) {
        $cnt = 0;
        // 一旦依頼書の発注管理情報を削除する
        if ($createFlg > 0) {
            $cnt = $this->deleteHachuInfoByHarpKbn($db, $ha_rp_kbn);
        }
        //依頼書の発注管理情報を作成する
        if ($createFlg == 2) {
            $cnt = $this->createHachuInfo($db, $ha_rp_kbn);
        }
        return $cnt;
    }

    /**
     *
     * 各種依頼書(34:寺院依頼書 35:葬儀・喪家情報確認書)を作成する
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihon 基本情報
     * @return $cnt 処理件数
     */
    private function saveSpecialHachuInfo2($db, $dataSekoKihon) {
        $cnt = 0;
        $jiin_irai = '34'; // 34:寺院依頼書
        $sougi_kakunin = '35'; // 35:葬儀・喪家情報確認書
        // 0:なし 1:全削除のみ 2 全削除→寺院依頼書作成　3 全削除→喪家情報確認書作成 4 全削除→全作成
        $createFlg = $this->checkIfCreateJiinIrai($dataSekoKihon);
        if ($createFlg == 1) {
            $cnt += $this->deleteHachuInfoByHarpKbn($db, $jiin_irai);
            $cnt += $this->deleteHachuInfoByHarpKbn($db, $sougi_kakunin);
        }
        if ($createFlg == 2) {
            $cnt += $this->deleteHachuInfoByHarpKbn($db, $jiin_irai);
            $cnt += $this->deleteHachuInfoByHarpKbn($db, $sougi_kakunin);
            $cnt += $this->createHachuInfo($db, $jiin_irai);
        }
        if ($createFlg == 3) {
            $cnt += $this->deleteHachuInfoByHarpKbn($db, $jiin_irai);
            $cnt += $this->deleteHachuInfoByHarpKbn($db, $sougi_kakunin);
            $cnt += $this->createHachuInfo($db, $sougi_kakunin);
        }
        if ($createFlg == 4) {
            $cnt += $this->deleteHachuInfoByHarpKbn($db, $jiin_irai);
            $cnt += $this->deleteHachuInfoByHarpKbn($db, $sougi_kakunin);
            $cnt += $this->createHachuInfo($db, $jiin_irai);
            $cnt += $this->createHachuInfo($db, $sougi_kakunin);
        }
        return $cnt;
    }

    /**
     *
     * 施行基本フリーを取得する
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @return array 施行基本フリーデータ
     */
    private function getKihonFree() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            skf.seko_no                     -- 施行番号
            ,TO_CHAR(skf.ts_free1 ,'YYYY/MM/DD HH24:MI') AS ts_free1     -- 受付日
            ,TO_CHAR(skf.ts_free1 ,'YYYY/MM/DD') AS ts_free1_date        -- 受付日日付のみ
            ,TO_CHAR(skf.ts_free1 ,'HH24:MI') AS ts_free1_time           -- 受付日時刻のみ
            ,TO_CHAR(skf.ts_free4 ,'YYYY/MM/DD HH24:MI') AS ts_free4     -- 入金日(事前)
            ,TO_CHAR(skf.ts_free4 ,'YYYY/MM/DD') AS ts_free4_date        -- 入金日日付のみ(事前)
            ,TO_CHAR(skf.ts_free4 ,'HH24:MI') AS ts_free4_time           -- 入金日時刻のみ(事前)
            ,TO_CHAR(skf.d_free3 ,'YYYY/MM/DD') AS d_free3        -- 成約日(事前)
            ,skf.tanto_cd1                  -- 見積担当者コード
            ,skf.tanto_nm1                  -- 見積担当者名
            ,skf.tanto_cd2                  -- 納棺担当者コード
            ,skf.tanto_nm2                  -- 納棺担当者名
            ,skf.tanto_cd3                  -- 通夜担当者コード
            ,skf.tanto_nm3                  -- 通夜担当者名
            ,skf.tanto_cd4                  -- 設営担当者コード
            ,skf.tanto_nm4                  -- 設営担当者名
            ,skf.tanto_cd5                  -- 司会担当者コード
            ,skf.tanto_nm5                  -- 司会担当者名
            ,skf.tanto_cd6                  -- 受注担当者コード(事前)
            ,skf.tanto_nm6                  -- 受注担当者名(事前)
            ,skf.tanto_cd7                  -- 発注担当者コード(事前)
            ,skf.tanto_nm7                  -- 発注担当者名(事前)
            ,skf.tanto_cd8                  -- 片付担当者コード
            ,skf.tanto_nm8                  -- 片付担当者名
            ,skf.tanto_nm9                  -- 接待担当者（通夜）
            ,skf.tanto_nm10                 -- 接待担当者（葬儀）
            ,skf.tanto_nm11                 -- ライフサービス（通夜）
            ,skf.tanto_nm12                 -- ライフサービス（葬儀）
            ,skf.tanto_nm13                 -- 霊柩車運転
            ,skf.tanto_nm14                 -- マイクロバス
            ,skf.tanto_cd15                  -- 預り印鑑担当者コード
            ,skf.tanto_nm15                  -- 預り印鑑担当者名
            ,skf.tanto_cd16                  -- 預り死亡診断書・死亡届担当者コード
            ,skf.tanto_nm16                  -- 預り死亡診断書・死亡届担当者名
            ,skf.tanto_cd17                  -- 預り火葬料担当者コード
            ,skf.tanto_nm17                  -- 預り火葬料担当者名
            ,skf.tanto_cd18                  -- 預り写真担当者コード
            ,skf.tanto_nm18                  -- 預り写真担当者名
            ,skf.tanto_cd19                  -- 預り加入者証担当者コード
            ,skf.tanto_nm19                  -- 預り加入者証担当者名
            ,skf.tanto_cd20                  -- 預りその他担当者コード
            ,skf.tanto_nm20                  -- 預りその他担当者名
            ,skf.tanto_cd21                  -- 預りその他担当者コード
            ,skf.tanto_nm21                  -- 預りその他担当者名
            ,skf.tanto_cd22                  -- 受取印鑑担当者コード
            ,skf.tanto_nm22                  -- 受取印鑑担当者名
            ,skf.tanto_cd23                  -- 受取火葬・埋葬許可書担当者コード
            ,skf.tanto_nm23                  -- 受取火葬・埋葬許可書担当者名
            ,skf.tanto_cd24                  -- 受取写真原版担当者コード
            ,skf.tanto_nm24                  -- 受取写真原版担当者名
            ,skf.tanto_cd25                  -- 受取火葬領収書担当者コード
            ,skf.tanto_nm25                  -- 受取火葬領収書担当者名
            ,skf.tanto_cd26                  -- 受取その他担当者コード
            ,skf.tanto_nm26                  -- 受取その他担当者名
            ,skf.tanto_cd27                  -- 受取その他担当者コード
            ,skf.tanto_nm27                  -- 受取その他担当者名
            ,skf.free2_code_cd AS renraku_zoku_code_cd  -- 連絡者続柄区分コード区分
            ,skf.free13_code_cd
            ,skf.free14_code_cd -- 紹介項目コード(事前)
            ,skf.free15_code_cd -- 結果ステータス(事前)
            ,skf.free2_kbn AS renraku_zoku_kbn  -- 連絡者続柄区分
            ,skf.free_kbn9
            ,skf.free_kbn10 -- 結果情報(事前)
            ,skf.free_kbn11 -- 仕入先名称変更区分
            ,skf.free_kbn14 -- 相談に見えた方(事前)
            ,skf.free_kbn7 AS form_answer_kbn  -- 問合せチェック
            ,skf.v_free4 AS renraku_nm      -- 連絡者お名前
            ,skf.v_free5 AS renraku_tel     -- 連絡者携帯番号
            ,skf.v_free27   -- 紹介業者コード(事前)
            ,skf.v_free28   -- 紹介業者名(事前)
            ,skf.v_free29   -- 紹介業者担当者名(事前)
            ,skf.v_free30   -- 受注部門コード(事前)
            ,skf.v_free31   -- 発注部門コード(事前)
            ,skf.v_free32   -- 成約施行番号(事前)
            ,skf.v_free33
            ,skf.v_free34
            ,skf.v_free35
            ,skf.v_free36   -- 紹介項目名(事前)
            ,skf.n_free7    -- 成約金額(事前)
            ,skf.n_free8    -- 手数料(事前)
            ,skf.f_free1
            ,skf.f_name1
            ,skf.f_free2
            ,skf.f_name2
            ,skf.f_free3
            ,skf.f_name3
            ,skf.f_free4
            ,skf.f_name4
            ,skf.f_free5
            ,skf.f_name5
        FROM seko_kihon_all_free skf
        WHERE skf.seko_no = :seko_no
        AND skf.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * 施行基本フリー保存処理
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param Msi_Sys_Db $db db
     * @param array $dataKihonFree 施行基本フリーデータ
     * @param array $dataSekoKihon 施行基本
     * @return int 更新件数
     */
    private function saveKihonFree($db, $dataKihonFree, $dataSekoKihon, $dataSekoKeiyakusakiInfo = array()) {
        $oneRow = Msi_Sys_Utils::remapArrayFlat($dataKihonFree, 
                    'tanto_cd1,tanto_nm1,tanto_cd2,tanto_nm2,tanto_cd3,tanto_nm3,tanto_cd4,tanto_nm4,tanto_cd5,tanto_nm5
                    ,tanto_cd6,tanto_nm6,tanto_cd7,tanto_nm7,tanto_cd8,tanto_nm8,tanto_nm9,tanto_nm10
                    ,tanto_nm11,tanto_nm12,tanto_nm13,tanto_nm14,ts_free1,ts_free4
                    ,tanto_cd15,tanto_nm15,tanto_cd16,tanto_nm16,tanto_cd17,tanto_nm17,tanto_cd18,tanto_nm18
                    ,tanto_cd19,tanto_nm19,tanto_cd20,tanto_nm20,tanto_cd21,tanto_nm21
                    ,tanto_cd22,tanto_nm22,tanto_cd23,tanto_nm23,tanto_cd24,tanto_nm24
                    ,tanto_cd25,tanto_nm25,tanto_cd26,tanto_nm26,tanto_cd27,tanto_nm27,d_free3
                    ,renraku_nm,renraku_tel,renraku_zoku_code_cd,renraku_zoku_kbn
                    ,form_answer_kbn
                    ,free3_cd, free5_cd
                    ,free13_code_cd,free14_code_cd,free15_code_cd
                    ,free_kbn9,free_kbn10,free_kbn11,free_kbn14
                    ,n_free7,n_free8
                    ,v_free27,v_free28,v_free29,v_free30,v_free31,v_free32,v_free33,v_free34,v_free35,v_free36'
                        , array(
                    'renraku_nm' => 'v_free4', 'renraku_zoku_code_cd' => 'free2_code_cd', 'renraku_zoku_kbn' => 'free2_kbn'
                    , 'renraku_tel' => 'v_free5', 'form_answer_kbn' => 'free_kbn7'
                        )
        );
        $oneRow = Msi_Sys_Utils::emptyToNullArr($oneRow);
        if ($this->_moushiKbn == self::MOUSHI_KBN_JIZEN) {
            $oneRow['f_free1'] = $dataKihonFree['f_free1'];
            $oneRow['f_free2'] = $dataKihonFree['f_free2'];
            $oneRow['f_free3'] = $dataKihonFree['f_free3'];
            $oneRow['f_free4'] = $dataKihonFree['f_free4'];
            $oneRow['f_free5'] = $dataKihonFree['f_free5'];
            $oneRow['f_name1'] = $dataKihonFree['f_name1'];
            $oneRow['f_name2'] = $dataKihonFree['f_name2'];
            $oneRow['f_name3'] = $dataKihonFree['f_name3'];
            $oneRow['f_name4'] = $dataKihonFree['f_name4'];
            $oneRow['f_name5'] = $dataKihonFree['f_name5'];
        }
        $oneRow['tanto_cd1'] = $dataSekoKihon['mitsu_tanto_cd'];
        $oneRow['tanto_nm1'] = $dataSekoKihon['mitsu_tanto_nm'];
        // 入電先
        $oneRow['v_free1']   = Msi_Sys_Utils::emptyToNull($dataSekoKihon['nyudensaki_nm1']);  // 入電先名1
        $oneRow['v_free2']   = Msi_Sys_Utils::emptyToNull($dataSekoKihon['nyudensaki_nm2']);  // 入電先名2
        $oneRow['v_free3']   = Msi_Sys_Utils::emptyToNull($dataSekoKihon['nyudensha_nm']);    // 入電者
        $oneRow['v_free8']   = Msi_Sys_Utils::emptyToNull($dataSekoKihon['nyudensaki_tel1']); // 入電者TEL1
        $oneRow['v_free9']   = Msi_Sys_Utils::emptyToNull($dataSekoKihon['nyudensaki_tel2']); // 入電者TEL2 
        $oneRow['free_kbn2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['keisatsu_kbn']);    // 警察扱い区分
        $oneRow['v_free11']  = Msi_Sys_Utils::emptyToNull($dataSekoKihon['keisatsu_nm']);     // 警察名
        $oneRow['v_free10']  = Msi_Sys_Utils::emptyToNull($dataSekoKihon['homonsaki_nm']);    // 訪問先名
        $oneRow['tel_no1']   = Msi_Sys_Utils::emptyToNull($dataSekoKihon['homonsaki_tel1']);  // 訪問先TEL1
        $oneRow['mobile_tel1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['homonsaki_tel2']); // 訪問先TEL2
        $oneRow['zip_no1']   = Msi_Sys_Utils::emptyToNull($dataSekoKihon['homonsaki_zip_no']); // 訪問先郵便番号
        $oneRow['addr1_1']   = Msi_Sys_Utils::emptyToNull($dataSekoKihon['homonsaki_addr1']); // 訪問先住所1
        $oneRow['addr1_2']   = Msi_Sys_Utils::emptyToNull($dataSekoKihon['homonsaki_addr2']); // 訪問先住所2
        // 初七日
        $oneRow['n_free6'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['shonanoka_kbn']);
        // 菩提寺
        $oneRow['zip_no2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_yubin_no']);
        $oneRow['addr2_1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_addr1']);
        $oneRow['addr2_2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_addr2']);
        $oneRow['tel_no2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_tel2']);
        $oneRow['free11_code_cd'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_cd2']);
        $oneRow['v_free24'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_nm2']);
        // 届出役所
        $oneRow['free12_code_cd'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['todokede_kbn']);
        $oneRow['v_free26'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['todokede_nm']);
        // 戒名
        $oneRow['n_free1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['ofuse']);   // お布施
        $oneRow['n_free2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['okuruma']); // お車代
        $oneRow['biko1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['irai_biko']); // 依頼書備考
        // 会員種別
        $oneRow['free8_code_cd'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['kaiin_sbt_code_cd']);
        $oneRow['free8_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['kaiin_sbt_cd']);
        
        $except = array();
        // 存在チェック
        $select = $this->getKihonFree();
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $oneRow['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kihon_all_free", $oneRow);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_all_free", $oneRow, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     * 寺院依頼書と喪家情報確認書作成フラグ判定処理
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param array $dataSekoKihon 
     * @return $flg 0:なし 1:全削除のみ 2 全削除→寺院依頼書作成　3 全削除→喪家情報確認書作成 4 全削除→全作成
     */
    private function checkIfCreateJiinIrai($dataSekoKihon) {
        $flg = 0;
        if (empty($dataSekoKihon['jyusho_nm'])) {
            $flg = 1;
        } else {
            $dataSekoKihonOld = $this->getSekoKihon();
            // 寺院名と導師紹介に変更がない
            if ($dataSekoKihonOld['jyusho_nm'] == $dataSekoKihon['jyusho_nm'] && $dataSekoKihonOld['tera_shokai_kbn'] == $dataSekoKihon['tera_shokai_kbn']) {
                $flg = 0;
            } else {
                if (empty($dataSekoKihon['free1_kbn'])) { // 未設定
                    $flg = 4;
                } else if ($dataSekoKihon['free1_kbn'] == 1) { // 当社紹介
                    $flg = 2;
                } else if ($dataSekoKihon['free1_kbn'] == 2) { // 施主
                    $flg = 3;
                }
            }
        }
        return $flg;
    }

    /**
     * 施行コピー処理
     *
     * <AUTHOR> Okuyama
     * @since  2017/03/17
     * @param array $req リクエスト
     */
    public function sekocopy($req) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();

        $this->_sekoNo = $req->getPost('seko_no');

        $this->setInitParam();
        if ($this->_moushiKbn != self::MOUSHI_KBN_SEIZEN) {
            $data['status'] = 'NG';
            $data['msg'] = "施行データが存在しません。";
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $sekoCopyInfo = $this->getSekoCopyInfo();
        if (count($sekoCopyInfo) > 0) {
            $data['status'] = 'NG';
            $data['msg'] = "葬儀施行コピーデータが既に存在します。" . " 施行番号：" . $sekoCopyInfo['seko_no'];
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $newSekoNo = $this->getAutoSekoNo($db);
        $newjuchuDenpyoNo = $this->getAutoDenpyoNo($db);

        // 施行基本コピー
        $cnt += $this->copySekoKihon($db, $newSekoNo);
        // 施行日程コピー
        $cnt += $this->copySekoNitei($db, $newSekoNo);
        // 受注伝票コピー
        $cnt += $this->copyJuchudenpyo($db, $newSekoNo, $newjuchuDenpyoNo);
        // 受注伝票明細コピー
        $cnt += $this->copyJuchudenpyoMsi($db, $newSekoNo, $newjuchuDenpyoNo);
        // 施行発注管理コピー
        $cnt += $this->copyHachuKanri($db, $newSekoNo, $newjuchuDenpyoNo);
        // そのほかコピー
        $cnt += $this->copyAdditional($db, $newSekoNo);
        // コピー元施行番号保存処理(free2_cd)
        $cnt += $this->upSekokihon($db, $newSekoNo);

        $db->commit();

        // 画面データを設定する
        $data = array(
            'seko_no' => $newSekoNo,
            'status' => 'OK',
            'msg' => ('新規作成しました'),
        );
        Msi_Sys_Utils::outJson($data);
    }
    
    /**
     * 事前相談情報コピー処理
     *
     * <AUTHOR> Tosaka
     * @since  2020/10/08
     * @param array $req リクエスト
     */
    public function consultcopy($req) {
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        $dataKihonFree = Msi_Sys_Utils::json_decode($req->getPost('dataKihonFreeJson'));
        $dataGojokaiMemberCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberColJson'));
        $jizenData = $this->getConsultCopyInfoData($dataSekoKihon['consult_seko_no']);
        $dataSekoKihon = array_merge($dataSekoKihon, $jizenData['dataSekoKihon']);
        $dataKihonFree = array_merge($dataKihonFree, $jizenData['dataSekoKihonFree']);
        // 互助会情報は重複があればスキップする
        $newGojoMember = array();
        $first_flg = true;
        foreach ($jizenData['dataGojokaiMemberCol'] as $one) {
            $flg = true;
            if (!isset($one['kain_no'])) {
                continue;
            }
            foreach ($dataGojokaiMemberCol as $val) {
                if (isset($val['kain_no']) && $val['kain_no'] == $one['kain_no']) {
                    $flg = false;
                }
                if ($first_flg) {
                    $newGojoMember[] = $val;
                }
            }
            if ($flg) {
                $newGojoMember[] = $one;
            }
            $first_flg = false;
        }
        while (count($newGojoMember) < 10) {
            array_push($newGojoMember, array());
        }
        $data = array(
            'sekoData' => $dataSekoKihon,
            'kihonFreeData' => $dataKihonFree,
            'dataGojokaiInfo' => $jizenData['dataGojokaiInfo'],
            'dataGojokaiMemberCol' => $newGojoMember,
            'dataSekoKeiyakusakiInfo' => $jizenData['dataSekoKeiyakusakiInfo'],
            'jizenDenpyo' => $jizenData['jizenDenpyo'],
            'status' => 'OK',
            'msg' => ('事前相談情報をコピーしました'),
        );
        Msi_Sys_Utils::outJson($data);
    }
    
    /**
     *
     * 事前相談情報を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2020/10/09
     * @return array 施行基本情報
     */
    private function getConsultCopyInfoData($sekoNo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $this->_sekoNo = $sekoNo;
        $sekoKihon = array();
        $kihonFree = array();
        $consultData = $this->getSekoKihon();
        $consultFreeData = $this->getKihonFree();
        $dataGojokaiInfo = $this->getGojokaiInfo();
        $dataGojokaiMemberCol = $this->getGojokaiMember(15);
        $dataSekoKeiyakusakiInfo = $this->getKeiyakusakiInfo();
        $sekoKihon['consult_seko_no'] = $sekoNo;
        $sekoKihon['k_cif_no'] = $consultData['k_cif_no'];
        $sekoKihon['k_cif_status'] = $consultData['k_cif_status'];
        $sekoKihon['k_last_knm'] = $consultData['k_last_knm'];
        $sekoKihon['k_first_knm'] = $consultData['k_first_knm'];
        $sekoKihon['k_knm'] = $consultData['k_knm'];
        $sekoKihon['k_last_nm'] = $consultData['k_last_nm'];
        $sekoKihon['k_first_nm'] = $consultData['k_first_nm'];
        $sekoKihon['k_nm'] = $consultData['k_nm'];
        $sekoKihon['k_sex_cd'] = $consultData['k_sex_cd'];
        $sekoKihon['k_sex_code_kbn'] = $consultData['k_sex_code_kbn'];
        $sekoKihon['k_sex_kbn'] = $consultData['k_sex_kbn'];
        $sekoKihon['k_haigu_cd'] = $consultData['k_haigu_cd'];
        $sekoKihon['k_haigu_kbn'] = $consultData['k_haigu_kbn'];
        $sekoKihon['k_haigu_code_kbn'] = $consultData['k_haigu_code_kbn'];
        $sekoKihon['k_gengo'] = $consultData['k_gengo'];
        $sekoKihon['k_seinengappi_ymd'] = $consultData['k_seinengappi_ymd'];
        $sekoKihon['k_seinengappi_ymd_y'] = $consultData['k_seinengappi_ymd_y'];
        $sekoKihon['k_birth_year'] = $consultData['k_birth_year'];
        $sekoKihon['k_birth_month'] = $consultData['k_birth_month'];
        $sekoKihon['k_birth_day'] = $consultData['k_birth_day'];
        $cdGengo = DataMapper_EraMst::getCodeNmEra();
        $keyIndex = array_search($sekoKihon['k_birth_year'], array_column($cdGengo, 'kbn_value_cd_num'));
        $result = $cdGengo[$keyIndex];
        $sekoKihon['k_wa_year'] = $result['kbn_value_snm'];
        $sekoKihon['kg_yubin_no'] = $consultData['kg_yubin_no'];
        $sekoKihon['kg_addr1'] = $consultData['kg_addr1'];
        $sekoKihon['kg_addr2'] = $consultData['kg_addr2'];
        $sekoKihon['m_zoku_code_kbn2'] = $consultData['m_zoku_code_kbn2'];
        $sekoKihon['m_zoku_cd2'] = $consultData['m_zoku_cd2'];
        $sekoKihon['m_zoku_kbn2'] = $consultData['m_zoku_kbn2'];
        $sekoKihon['m_zoku_nm2'] = $consultData['m_zoku_nm2'];
        $sekoKihon['kg_tel'] = $consultData['kg_tel'];
        $sekoKihon['kg_tel'] = $consultData['kg_tel'];
        $sekoKihon['kk_kinmusaki_kbn'] = $consultData['kk_kinmusaki_kbn'];
        $sekoKihon['kk_kinmusaki_nm'] = $consultData['kk_kinmusaki_nm'];
        $sekoKihon['kk_tel'] = $consultData['kk_tel'];
        $sekoKihon['kk_yakusyoku_nm'] = $consultData['kk_yakusyoku_nm'];
        $sekoKihon['kk_fax'] = $consultData['kk_fax'];
        $sekoKihon['careful_memo'] = $consultData['careful_memo'];
        $sekoKihon['k_file_nm'] = $consultData['k_file_nm'];
        $sekoKihon['souke_nm'] = $consultData['souke_nm'];
        $sekoKihon['souke_knm'] = $consultData['souke_knm'];
        $sekoKihon['keishiki_code_kbn'] = $consultData['keishiki_code_kbn'];
        $sekoKihon['keishiki_cd'] = $consultData['keishiki_cd'];
        $sekoKihon['keishiki_kbn'] = $consultData['keishiki_kbn'];
        $sekoKihon['syushi_code_kbn'] = $consultData['syushi_code_kbn'];
        $sekoKihon['syushi_cd'] = $consultData['syushi_cd'];
        $sekoKihon['syushi_kbn'] = $consultData['syushi_kbn'];
        $sekoKihon['syuha_code_kbn'] = $consultData['syuha_code_kbn'];
        $sekoKihon['syuha_cd'] = $consultData['syuha_cd'];
        $sekoKihon['syuha_kbn'] = $consultData['syuha_kbn'];
        $sekoKihon['syuha_nm'] = $consultData['syuha_nm'];
        $sekoKihon['syuha_knm'] = $consultData['syuha_knm'];
        $sekoKihon['jyusho_cd'] = $consultData['jyusho_cd'];
        $sekoKihon['jyusho_nm'] = $consultData['jyusho_nm'];
        $sekoKihon['jyusho_knm'] = $consultData['jyusho_knm'];
        $sekoKihon['temple_tel2'] = $consultData['temple_tel2'];
        $sekoKihon['temple_yubin_no'] = $consultData['temple_yubin_no'];
        $sekoKihon['temple_addr1'] = $consultData['temple_addr1'];
        $sekoKihon['temple_addr2'] = $consultData['temple_addr2'];
        $sekoKihon['tera_shokai_code_kbn'] = $consultData['tera_shokai_code_kbn'];
        $sekoKihon['tera_shokai_kbn'] = $consultData['tera_shokai_kbn'];
        $sekoKihon['kaimyo_code_kbn'] = $consultData['kaimyo_code_kbn'];
        $sekoKihon['kaimyo_kbn'] = $consultData['kaimyo_kbn'];
        $sekoKihon['pacemaker_code_kbn'] = $consultData['pacemaker_code_kbn'];
        $sekoKihon['pacemaker_kbn'] = $consultData['pacemaker_kbn'];
        $sekoKihon['temple_person'] = $consultData['temple_person'];
        // 施行基本フリー
        $kihonFree['renraku_zoku_code_cd'] = $consultFreeData['renraku_zoku_code_cd'];
        $kihonFree['renraku_zoku_kbn'] = $consultFreeData['renraku_zoku_kbn'];
        $kihonFree['renraku_nm'] = $consultFreeData['renraku_nm'];
        $kihonFree['renraku_tel'] = $consultFreeData['renraku_tel'];
        $data = array(
            'dataSekoKihon' => $sekoKihon,
            'dataSekoKihonFree' => $kihonFree,
            'dataGojokaiInfo' => $dataGojokaiInfo,
            'dataGojokaiMemberCol' => $dataGojokaiMemberCol,
            'dataSekoKeiyakusakiInfo' => $dataSekoKeiyakusakiInfo,
            'jizenDenpyo' => $this->getCdJizenDenpyo($sekoNo),
        );
        return $data;
    }
    
    /**
     * 搬送情報コピー処理
     *
     * <AUTHOR> Tosaka
     * @since  2020/10/08
     * @param array $req リクエスト
     */
    public function hansocopy($req) {
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        $dataKihonFree = Msi_Sys_Utils::json_decode($req->getPost('dataKihonFreeJson'));
        $hansoSekoData = $this->getHansoCopyInfoData($dataSekoKihon['hanso_seko_no']);
        // 事前相談施行番号があれば事前相談の情報を取得する
        if (isset($dataSekoKihon['consult_seko_no'])) {
            list($consultSekoData, $kihonFreeData) = $this->getConsultCopyInfoData($dataSekoKihon['consult_seko_no']);
            $dataSekoKihon = array_merge($dataSekoKihon, $consultSekoData);
            $dataKihonFree = array_merge($dataKihonFree, $kihonFreeData);
            
        }
        $dataSekoKihon = array_merge($dataSekoKihon, $hansoSekoData);
        $data = array(
            'sekoData' => $dataSekoKihon,
            'kihonFreeData' => $dataKihonFree,
            'status' => 'OK',
            'msg' => ('搬送情報をコピーしました'),
        );
        Msi_Sys_Utils::outJson($data);
    }
    
    /**
     *
     * 搬送情報を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2020/10/09
     * @return array 施行基本情報
     */
    private function getHansoCopyInfoData($sekoNo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sekoKihon = array();
        $jissekiData = array();
        $select = DataMapper_Hanso_SyutsudoJisekiEx::find($db, array('uketsuke_no' => $sekoNo, 'unkou_kbn' => 2, '__raw1' => "hanso_result IN (5,6)"));
        if (count($select) > 0) {
            $jissekiData = $select[0];
            $sekoKihon['hanso_seko_no'] = $sekoNo;
            $sekoKihon['k_cif_no'] = $jissekiData['kojin_cif_no'];
            $sekoKihon['k_cif_status'] = $jissekiData['kojin_cif_no_result'];
            $sekoKihon['k_last_knm'] = $jissekiData['k_l_knm'];
            $sekoKihon['k_first_knm'] = $jissekiData['k_f_knm'];
            $sekoKihon['k_knm'] = $sekoKihon['k_last_knm'].'　'.$sekoKihon['k_first_knm'];
            $sekoKihon['k_last_nm'] = $jissekiData['k_l_nm'];
            $sekoKihon['k_last_nm_readonly'] = $jissekiData['k_l_nm'];
            $sekoKihon['k_first_nm'] = $jissekiData['k_f_nm'];
            $sekoKihon['k_first_nm_readonly'] = $jissekiData['k_f_nm'];
            $sekoKihon['k_nm'] = $sekoKihon['k_last_nm'].'　'.$sekoKihon['k_first_nm'];
            $sekoKihon['k_sex_kbn'] = $jissekiData['k_sex_kbn'];
            $sekoKihon['k_gengo'] = $jissekiData['k_gengo'];
            $sekoKihon['k_seinengappi_ymd'] = $jissekiData['k_w_birth_date'];
            $sekoKihon['k_seinengappi_ymd_y'] = $jissekiData['k_birth_date'];
            if (strlen($sekoKihon['k_seinengappi_ymd_y']) > 0) {
                $birth = explode("/", str_replace('-','/',$sekoKihon['k_seinengappi_ymd_y']));
                $sekoKihon['k_birth_year'] = $birth[0];
                $sekoKihon['k_birth_month'] = $birth[1];
                $sekoKihon['k_birth_day'] = $birth[2];
                $cdGengo = DataMapper_EraMst::getCodeNmEra();
                $keyIndex = array_search($sekoKihon['k_birth_year'], array_column($cdGengo, 'kbn_value_cd_num'));
                $result = $cdGengo[$keyIndex];
                $sekoKihon['k_wa_year'] = $result['kbn_value_snm'];
            }
            $sekoKihon['kg_yubin_no'] = $jissekiData['k_yubin_no'];
            $sekoKihon['kg_addr1'] = $jissekiData['k_addr1'];
            $sekoKihon['kg_addr2'] = $jissekiData['k_addr2'];
            $sekoKihon['m_zoku_code_kbn2'] = $jissekiData['m_zoku_code_kbn2'];
            $sekoKihon['m_zoku_cd2'] = $jissekiData['m_zoku_cd2'];
            $sekoKihon['m_zoku_kbn2'] = $jissekiData['m_zoku_kbn2'];
            $sekoKihon['m_zoku_nm2'] = $jissekiData['m_zoku_nm2'];
            $sekoKihon['kg_tel'] = $jissekiData['k_tel'];
        }
        // 搬送結果が搬送のみの場合は申込区分を搬送依頼に設定する
        if (count($jissekiData) > 0 && $jissekiData['hanso_result'] == '5') {
            $sekoKihon['moushi_kbn'] = static::MOUSHI_KBN_HANSO;
            $sekoKihon['moushi_cd'] = static::MOUSHI_KBN_HANSO;
        } else {
            $sekoKihon['moushi_kbn'] = static::MOUSHI_KBN_SOUGI;
            $sekoKihon['moushi_cd'] = static::MOUSHI_KBN_SOUGI;
        }
        return $sekoKihon;
    }

    /**
     *
     * 施行基本コピー情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2016/02/10
     * @return array
     */
    private function getSekoCopyInfo() {
        $select2 = null;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                ski.seko_no
                ,ski.free2_cd
            FROM seko_kihon_info ski
            WHERE ski.delete_flg = 0
                AND ski.seko_no = :seko_no
                ";
        $select1 = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        if (count($select1) > 0) {
            $select2 = $db->easySelOne($sql, array('seko_no' => $select1['free2_cd']));
        }
        return $select2;
    }

    /**
     *
     * 施行基本コピー
     *
     * <AUTHOR> Sai
     * @param Msi_Sys_Db $db db
     * @param string $newSekoNo コピー先施行番号
     * @since 2016/02/10
     * @return $cnt
     */
    private function copySekoKihon($db, $newSekoNo) {
        $cnt = 0;
        $sqlsel = "
            SELECT *
            FROM seko_kihon_info ski
            WHERE ski.delete_flg = 0
                AND ski.seko_no = :seko_no
                ";
        $select = $db->easySelOne($sqlsel, array('seko_no' => $this->_sekoNo));
        $oneRow = Msi_Sys_Utils::remapArrayFlat($select, 'moushi_code_kbn sougi_code_kbn sougi_cd sougi_kbn bumon_cd uketuke_tanto_cd seko_tanto_cd p_info_code_kbn p_info_cd p_info_kbn
                                                              kaiin_code_kbn kaiin_cd kaiin_kbn souke_nm souke_knm souke_addr_cd souke_addr_kbn souke_tel keishiki_code_kbn
                                                              keishiki_cd keishiki_kbn syushi_code_kbn syushi_cd syushi_kbn syuha_code_kbn syuha_cd syuha_kbn syuha_nm syuha_knm
                                                              jyusho_kbn jyusho_cd jyusho_nm jyusho_knm biko1 biko2 k_nm k_knm k_sex_code_kbn k_sex_cd k_sex_kbn k_sex_code_kbn k_gengo k_seinengappi_ymd
                                                              k_haigu_code_kbn k_haigu_cd k_haigu_kbn kg_yubin_no kg_addr1 kg_addr2 kg_tel kg_setai_code_kbn kg_setai_cd kg_setai_kbn
                                                              kj_kbn kj_yubin_no kj_addr1 kj_addr2 kj_tel kj_setai_code_kbn kj_setai_cd kj_setai_kbn kh_kbn kh_yubin_no kh_addr1 kh_addr2
                                                              kh_hito_code_kbn kh_hito_cd kh_hito_kbn kk_kinmusaki_kbn kk_kinmusaki_nm kk_yakusyoku_nm m_gengo
                                                              kk_tel kk_fax sd_hakko_kbn sd_yotei_ymd sd_step_kbn sd_copy_cnt tk_cyonaikai_nm tk_kumicyo_nm tk_house_cnt tk_person_cnt hs_kbn
                                                              hs_gyomu_code_kbn hs_gyomu_cd hs_gyomu_kbn hs_spot_kbn hs_spot_cd hs_spot_nm hs_gyomu_code_kbn_2 hs_gyomu_cd_2 hs_gyomu_kbn_2
                                                              hs_spot_kbn_2 hs_spot_cd_2 hs_spot_nm_2 hs_gyomu_code_kbn2 hs_gyomu_cd2 hs_gyomu_kbn2 hs_anchi_kbn hs_anchi_cd hs_anchi_nm
                                                              kasoba_kbn kasoba_cd kasoba_nm gojokai_cose_cd gojokai_kbn main_pt_code_kbn main_pt_cd main_pt_kbn seko_plan_cd sk_shohin_cd 
                                                              sk_kaijyo_cd sk_kaijyo_nm shikijo_shiyou_prc ty_shohin_cd ty_kaijyo_cd ty_kaijyo_nm tuya_paku_su tuya_shikijo_tanka 
                                                              tuya_paku_su2 tuya_shikijo_tanka2 tuya_shikijo_prc
                                                              ');
        $oneRow['seko_no'] = $newSekoNo;
        $oneRow['moushi_cd'] = '1';
        $oneRow['moushi_kbn'] = '1';
        // 登録SQL
        list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kihon_info", $oneRow);
        $cnt += $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     *  施行日程情報コピー
     *
     * <AUTHOR> Sai
     * @param Msi_Sys_Db $db db
     * @param string $newSekoNo コピー先施行番号
     * @since 2016/02/10
     * @return $cnt
     */
    private function copySekoNitei($db, $newSekoNo) {
        $cnt = 0;
        $sqlsel = "
            SELECT *
            FROM seko_nitei sn
            WHERE sn.delete_flg = 0
                AND sn.seko_no = :seko_no
                ";
        $select = $db->easySelect($sqlsel, array('seko_no' => $this->_sekoNo));
        foreach ($select as $value) {
            $oneRow = Msi_Sys_Utils::remapArrayFlat($value, 'nitei_kbn disp_no spot_code_kbn spot_cd basho_kbn basho_nm nyukan_kyo syukan_kyo kaso_kyo shishikisha_ninzu
                                                              ');
            $oneRow['seko_no'] = $newSekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_nitei", $oneRow);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     *
     * 受注伝票コピー
     *
     * <AUTHOR> Okuyama
     * @param Msi_Sys_Db $db db
     * @param string $newSekoNo コピー先施行番号
     * @param string $newjuchuDenpyoNo コピー先受注伝票番号
     * @since  2017/03/17
     * @return $cnt
     */
    private function copyJuchudenpyo($db, $newSekoNo, $newjuchuDenpyoNo) {
        $cnt = 0;
        $sqlsel = "
            SELECT *
            FROM juchu_denpyo jd
            WHERE jd.delete_flg = 0
                AND jd.data_kbn = 1
                AND jd.seko_no = :seko_no
                ";
        $select = $db->easySelect($sqlsel, array('seko_no' => $this->_sekoNo));
        foreach ($select as $oneRow) {
            $oneRow['seko_no'] = $newSekoNo;
            $oneRow['denpyo_no'] = $newjuchuDenpyoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("juchu_denpyo", $oneRow);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     *
     * 受注伝票明細コピー
     *
     * <AUTHOR> Okuyama
     * @param Msi_Sys_Db $db db
     * @param string $newSekoNo コピー先施行番号
     * @param string $newjuchuDenpyoNo コピー先受注伝票番号
     * @since  2017/03/17
     * @return $cnt
     */
    private function copyJuchudenpyoMsi($db, $newSekoNo, $newjuchuDenpyoNo) {
        $cnt = 0;
        $juchuDenpyoNo = $this->getJuchudenpyoNo();
        $sqlsel = "
            SELECT *
            FROM juchu_denpyo_msi jdm
            WHERE jdm.delete_flg = 0
                AND jdm.denpyo_no = :denpyo_no
                ";
        $select = $db->easySelect($sqlsel, array('denpyo_no' => $juchuDenpyoNo));
        foreach ($select as $oneRow) {
            $oneRow['seko_no'] = $newSekoNo;
            $oneRow['denpyo_no'] = $newjuchuDenpyoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("juchu_denpyo_msi", $oneRow);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     *
     * 施行発注管理コピー
     *
     * <AUTHOR> Okuyama
     * @param Msi_Sys_Db $db db
     * @param string $newSekoNo コピー先施行番号
     * @param string $newjuchuDenpyoNo コピー先受注伝票番号
     * @since  2017/03/17
     * @return $cnt
     */
    private function copyHachuKanri($db, $newSekoNo, $newjuchuDenpyoNo) {
        $cnt = 0;
        $sqlsel = "
            SELECT *
            FROM seko_hachu_info shi
            WHERE shi.delete_flg = 0
                AND shi.data_kbn = 1
                AND shi.seko_no = :seko_no
                ";
        $select = $db->easySelect($sqlsel, array('seko_no' => $this->_sekoNo));
        foreach ($select as $oneRow) {
            $oneRow['seko_no'] = $newSekoNo;
            $oneRow['jc_denpyo_no'] = $newjuchuDenpyoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_hachu_info", $oneRow);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     *
     * コピー元施行番号保存処理(free2_cd)
     *
     * <AUTHOR> Okuyama
     * @param Msi_Sys_Db $db db
     * @param string $newSekoNo コピー先施行番号
     * @since  2017/03/17
     * @return $cnt
     */
    private function upSekokihon($db, $newSekoNo) {
        $cnt = 0;
        $sqlsel = "
            UPDATE seko_kihon_info
            SET free2_cd = :new_seko_no
            WHERE delete_flg = 0
                AND seko_no = :seko_no
                ";
        $cnt += $db->easyExecute($sqlsel, array('new_seko_no' => $newSekoNo, 'seko_no' => $this->_sekoNo));
        return $cnt;
    }

    /**
     *
     * テーブルコピー共通処理
     *
     * <AUTHOR> Okuyama
     * @param Msi_Sys_Db $db db
     * @param string $newSekoNo コピー先施行番号
     * @since  2017/03/17
     * @return $cnt
     */
    private function copyAdditional($db, $newSekoNo) {
        $cnt = 0;
        $tbls = array('seko_gojokai_info', 'seko_gojokai_member', 'seko_oshirase_info', 'seko_orei_info', 'seko_oshirase_orei_dtl', 'seko_sinzoku_info',
            'seko_keisai_info', 'seko_keisai_detail', 'seko_shasin_info', 'seko_monita_info');
        foreach ($tbls as $tnm) {
            $sqlsel = "
            SELECT *
            FROM
                {$tnm} t
            WHERE
                t.seko_no = :seko_no
                ";
            $select = $db->easySelect($sqlsel, array('seko_no' => $this->_sekoNo));
            foreach ($select as $oneRow) {
                $oneRow['seko_no'] = $newSekoNo;
                // 登録SQL
                list($sql, $param) = DataMapper_Utils::makeInsertSQL($tnm, $oneRow);
                $cnt += $db->easyExecute($sql, $param);
            }
        }
        return $cnt;
    }

    /**
     * 事前相談データ取得処理
     *
     * <AUTHOR> Sai
     * @since 2016/01/29
     * @return array jsonData
     */
    public function getconsultData($controllerName) {
        $this->_sekoNo = $this->getReqSekoNo();
        // 施行基本情報を設定する
        $this->setInitParam();

        $db = Msi_Sys_DbManager::getMyDb();
        // 施行基本情報を取得する
        $dataSekoKihon = $this->getSekoKihon();
        // 生年月日の値を設定する
        if (!isset($dataSekoKihon['uketuke_tanto_cd'])) {
            // お客様情報新規登録時の受付担当者にログイン者の情報を設定する
            $dataSekoKihon['uketuke_tanto_cd'] = App_Utils::getTantoCd();
            $dataSekoKihon['uketuke_tanto_nm'] = App_Utils::getTantoNm();
        }
        if (!isset($dataSekoKihon['seko_tanto_cd'])) {
            // お客様情報新規登録時の施行担当者にログイン者の情報を設定する
            $dataSekoKihon['seko_tanto_cd'] = App_Utils::getTantoCd();
            $dataSekoKihon['seko_tanto_nm'] = App_Utils::getTantoNm();
        }
        if (!isset($dataSekoKihon['moushi_kbn'])) {
            $dataSekoKihon['moushi_kbn'] = self::MOUSHI_KBN_JIZEN;
            $dataSekoKihon['moushi_cd'] = self::MOUSHI_KBN_JIZEN;
        }
        // 施行基本フリー情報を取得する
        $dataKihonFree = $this->getKihonFree();
        // 顧客基本情報を取得する
        $dataJizen = $this->getCustomerBaseInfo();
        if (!isset($dataKihonFree['tanto_cd1'])) {
            // 新規登録時の見積担当者にログイン者の情報を設定する
//            $dataSekoKihon['mitsu_tanto_cd'] = App_Utils::getTantoCd();
//            $dataSekoKihon['mitsu_tanto_nm'] = App_Utils::getTantoNm();
        } else {
            $dataSekoKihon['mitsu_tanto_cd'] = $dataKihonFree['tanto_cd1'];
            $dataSekoKihon['mitsu_tanto_nm'] = $dataKihonFree['tanto_nm1'];
        }
        if (!isset($dataKihonFree['ts_free1'])) {
            // 新規登録時の受付日を設定する
            $dataKihonFree['ts_free1'] = Msi_Sys_Utils::getDate(null, 'Y/m/d H:i');
            $dataKihonFree['ts_free1_date'] = Msi_Sys_Utils::getDate();
            $dataKihonFree['ts_free1_time'] = Msi_Sys_Utils::getDate(null, 'H:i');
        }
        $dataJizen['uketuke_date_disp'] = $dataKihonFree['ts_free1_date'];
        // コード名称マスタデータを取得する
        $dataCodeNameMst = $this->getCodeNameMst();
        // 部門マスタを取得する(プルダウン用)
        $dataBumon = $this->getCdBumon();
        // 寺院マスタを取得する(プルダウン用)
        $dataJiin = $this->getCdJiin();
        // 部門マスタを取得する(承り用)
        $dataBumonN = $this->getCdBumonN();
        $dataSekoKihon['login_bumon_cd'] = App_Utils::getTantoBumonCd(App_Utils::getTantoCd());
        // サイドメニューデータを取得する
        if (App_Utils::isMitsuInJuchu($controllerName) || App_Utils::isCustomerNewinJuchu($controllerName)) {
            Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
            $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName);
        } else {
            $sideMenuData = $this->getSideMenuData();
        }
        $taxInfoAll = App_ClsTaxLib::GetTaxInfoAll($db);
        $gojokai_cose = $this->GetCodeNameMst2($db, self::CODE_GOJOKAI_COSE); // ソート順のため、個別取得する
        $dataKbns = array(
            // ヘッダー部
            'moushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_MOUSHI_KBN_J), // 申込区分
            'sougi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SOUGI_KBN), // 葬儀区分
            'p_info' => $this->filter($dataCodeNameMst, self::CODE_P_INFO), // 個人情報保護
            'kaiin_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_KBN), // 会員区分
            'est_shikijo' => $dataBumon, // 部門マスタ
            // 受付情報タブ
            'haigu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HAIGU_KBN), // 配偶者区分
            'gengo' => DataMapper_EraMst::getCodeNmEra(), // 元号
            'month' => DataMapper_EraMst::getCodeNmMonth(), // 元号(月)
            'day' => DataMapper_EraMst::getCodeNmDay(), // 元号(日)
            'kinmu_kbn_k' => $this->filter($dataCodeNameMst, self::CODE_KBN_KINMU_K), // 勤務先区分(故人)
            'kinmu_kbn_m' => $this->filter($dataCodeNameMst, self::CODE_KBN_KINMU_M), // 勤務先区分(喪主)
            'umu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_UMU), // 有無区分
            'pacemaker_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_PACEMAKER), // ペースメーカー区分
            'zoku_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_ZOKU_KBN), // 続柄区分
            'cif_status_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_CIF_STATUS), // 続柄区分
            // 打合せ事項①タブ
            'keishiki_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KEISHIKI_KBN), // 葬儀形式
            'syuha_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUHA_KBN), // 宗派区分
            'syushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUSHI_KBN), // 宗旨区分
            'kai_name' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAINAME1_KBN), // 戒名
            'tera_shokai' => $this->filter($dataCodeNameMst, self::CODE_KBN_TERA_SHOKAI), // 寺紹介者
            'jizen_sougi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_9611), // 葬儀区分(受付結果)
            'kankei_bunrui_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_9646), // 関係分類
            'soudan_contents' => $this->filter($dataCodeNameMst, self::CODE_KBN_9647), // 相談項目
            'soudan_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_9648), // 相談に見えた方
            // 相談履歴
            'contact_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_JIZEN_CONTACT), // 問合せ区分
            'contact_way_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_JIZEN_CONTACTWAY), // 問合せ手段区分
            'contact_process_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_JIZEN_CONTACTPROCESS), // 問合せ経緯区分
            'contact_status_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_JIZEN_CONTACTSTATUS), // 状況区分
            'progress_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_JIZEN_PROGRESS), // 事前全体進捗区分
            'anchi_basho_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_OMUKAE), // お迎え区分
            'shikijo_mst' => $dataBumon, // 部門マスタ
            'jiin_mst' => $dataJiin, // 寺院マスタ
            'umu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_UMU), // 有無区分
            'seiyaku_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_JIZEN_SEIYAKU), // 成約状況区分
            'outai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_OUTAI), // 応対区分
            'outai_method_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_OUTAI_METHOD), // 応対方法区分
            // 互助会タブ
            'kaiin_info_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_INFO), // 会員情報区分
            'hunsitu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_9717), // 紛失区分
            'wariken_mst' => $this->filter($dataCodeNameMst, self::CODE_KBN_9851), // 割引券
            'yoto_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_YOTO_KBN), // 用途
            'yoto_kbn_OM' => $this->filter($dataCodeNameMst, self::CODE_KBN_YOTO_KBN_OM), // 用途OM
            'tanto_mst' =>  $this->getCdTanto(), // 部門マスタ
            'gojokai_cose' => $gojokai_cose, // 互助会コース
            'zei_cd' => $this->getZei_cd(), // 消費税コード
            'user_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_USER), // 利用者区分
            'kyosai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KYOSAI), // 共済組合区分
            'genkyo_cd' => $this->filter($dataCodeNameMst, self::CODE_KBN_GENKYO), // 現況コード
        );
        $codeKbns = array(
            // ヘッダー部
            'moushi_code_kbn' => self::CODE_KBN_MOUSHI_KBN_J, // 申込区分
            'sougi_code_kbn' => self::CODE_KBN_SOUGI_KBN, // 葬儀区分
            'p_info_code_kbn' => self::CODE_P_INFO, // 個人情報保護
            'kaiin_code_kbn' => self::CODE_KBN_KAIIN_KBN, // 会員区分
            'anketo_soufu_code_kbn' => self::CODE_KBN_9700, // アンケート送付先
            'hitsugi_check_code_kbn' => self::CODE_KBN_9701, // 棺確認
            'dm_soufu_code_kbn' => self::CODE_KBN_9708, // DM送付
            // 受付情報タブ
            'haigu_code_kbn' => self::CODE_KBN_HAIGU_KBN, // 配偶者区分
            'umu_code_kbn' => self::CODE_KBN_UMU, // 有無区分
            'pacemaker_code_kbn' => self::CODE_KBN_PACEMAKER, // ペースメーカー区分
            'zoku_code_kbn' => self::CODE_KBN_ZOKU_KBN, // 続柄区分
            'sex_code_kbn' => self::CODE_KBN_SEX_KBN, // 性別
            // 打合せ事項①
            'keishiki_code_kbn' => self::CODE_KBN_KEISHIKI_KBN, // 葬儀形式
            'syuha_code_kbn' => self::CODE_KBN_SYUHA_KBN, // 宗派区分
            'syushi_code_kbn' => self::CODE_KBN_SYUSHI_KBN, // 宗旨区分
            'kaimyo_code_kbn' => self::CODE_KBN_KAINAME1_KBN, // 戒名
            'tera_shokai_code_kbn' => self::CODE_KBN_TERA_SHOKAI, // 寺紹介者
        );
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        // 相談履歴データを取得する
        $dataConsultHistoryCol = $this->getConsultHistory();
        // 施行互助会加入者を取得する
        $dataGojokaiMemberCol = $this->getGojokaiMember(15);
        // 施行互助会情報を取得する
        $dataGojokaiInfo = $this->getGojokaiInfo();
        // 互助会コースマスタを取得する
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        // 施行契約先情報を取得する
        $dataSekoKeiyakusakiInfo = $this->getKeiyakusakiInfo();
        $modCnts = $this->getModCnts();
        // 画面データを設定する
        $data = array(
            'controllerName' => $controllerName,
            'dataSekoKihon' => $dataSekoKihon,
            'dataSekoKihonFree' => $dataKihonFree,
            'dataJizen' => $dataJizen,
            'dataConsultHistoryCol' => $dataConsultHistoryCol,
            'dataGojokaiInfo' => $dataGojokaiInfo,
            'dataSekoKeiyakusakiInfo' => $dataSekoKeiyakusakiInfo,
            'dataGojokaiMemberCol' => $dataGojokaiMemberCol,
            'gojokaiCouseMst' => $gojokaiCouseMst,
            'dataKbns' => $dataKbns,
            'codeKbns' => $codeKbns,
            'role_nm' => App_Utils2::getRoleNm(App_Utils::getTantoCd()),
            'sideMenuDataCol' => $sideMenuData['dataCol'],
            'modCnts' => $modCnts,
            'taxInfoAll' => $taxInfoAll,
        );
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }
    
    /**
     * 初期情報取得処理(オーダーメイド)
     *
     * <AUTHOR> Tosaka
     * @since  2021/02/XX
     * @param string $controllerName コントローラー名
     * @return array jsonData
     */
    public function getOrdermadeData($controllerName, $sidemenukey = '') {
        
        $db = Msi_Sys_DbManager::getMyDb();
        $this->_sekoNo = $this->getReqSekoNo();
        // 施行基本情報を設定する
        $this->setInitParam();
        $this->_customerCd = $this->getCustomerCd();

        // 施行基本情報を取得する
        $dataSekoKihon = $this->getSekoKihon();
        // 生年月日の値を設定する
        if (!isset($dataSekoKihon['uketuke_tanto_cd'])) {
            // お客様情報新規登録時の受付担当者にログイン者の情報を設定する
            $dataSekoKihon['uketuke_tanto_cd'] = App_Utils::getTantoCd();
            $dataSekoKihon['uketuke_tanto_nm'] = App_Utils::getTantoNm();
        }
        // 施行基本フリー情報を取得する
        $dataKihonFree = $this->getKihonFree();
        if (!isset($dataKihonFree['tanto_cd1'])) {
            // 新規登録時の見積担当者にログイン者の情報を設定する
            $dataSekoKihon['mitsu_tanto_cd'] = App_Utils::getTantoCd();
            $dataSekoKihon['mitsu_tanto_nm'] = App_Utils::getTantoNm();
        } else {
            $dataSekoKihon['mitsu_tanto_cd'] = $dataKihonFree['tanto_cd1'];
            $dataSekoKihon['mitsu_tanto_nm'] = $dataKihonFree['tanto_nm1'];
        }
        if (!isset($dataKihonFree['ts_free1'])) {
            // 新規登録時の受付日を設定する
            $dataKihonFree['ts_free1'] = Msi_Sys_Utils::getDate(null, 'Y/m/d H:i');
            $dataKihonFree['ts_free1_date'] = Msi_Sys_Utils::getDate();
            $dataKihonFree['ts_free1_time'] = Msi_Sys_Utils::getDate(null, 'H:i');
        }

        if (!isset($dataSekoKihon['kaishu_ymd'])) {
            $uri = $this->getUriageKaisu();
            if (isset($uri) && count($uri) > 0 && isset($uri['kaishu_ymd'])) {
                $dataSekoKihon['kaishu_ymd'] = $uri['kaishu_ymd'];
            }
        }
        // 施行互助会加入者を取得する
        $dataGojokaiMemberCol = $this->getGojokaiMember(15);
        // 施行互助会情報を取得する
        $dataGojokaiInfo = $this->getGojokaiInfo();
        // 互助会コースマスタを取得する
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        // 施行契約先情報を取得する
        $dataSekoKeiyakusakiInfo = $this->getKeiyakusakiInfo();
        // 別注品の商品マスタを取得する(プルダウン用)
        $dataBechuShohin = $this->getBechuShohin();
        // コード名称マスタデータを取得する
        $dataCodeNameMst = $this->getCodeNameMst();
        //$dataSekoKihon['uchiawase_tanto_nm'] = $dataAssignTantoInfo['uchiawase_tanto_nm'];
        //$dataSekoKihon['reikyu_tanto_nm'] = $dataAssignTantoInfo['reikyu_tanto_nm'];
        //$dataSekoKihon['kaso_tanto_nm'] = $dataAssignTantoInfo['kaso_tanto_nm'];
        //$dataSekoKihon['reikyu_hanso_ymd'] = $dataAssignTantoInfo['reikyu_hanso_ymd'];
        // サイドメニューデータを取得する
        if (App_Utils::isMitsuInJuchu($controllerName) || App_Utils::isCustomerNewinJuchu($controllerName)) {
            Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
            $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName);
        } else {
            $sideMenuData = $this->getSideMenuData();
        }
        list($hall_cd, $area_cd) = App_Utils::getDfltHallArea();
        
        $dataKbns = $this->getDataKbns();
        $dataKbns['moushi_kbn'] = $this->filter($dataCodeNameMst, self::CODE_KBN_MOUSHI_OM);
        $dataKbns['est_shikijo'] = $this->getCdOyaBumon();
        $dataKbns['toriatsukai_kbn'] = $this->getCdOyaBumonT();
        $dataKbns['abled_genkyo_cd'] = $this->getAbledGenkyoCd();
        // 契約種別用プルダウンを生成する
//        $dataKeiyakuCif = $this->getKeiyakuCif();
        $dataKeiyakuCif = array();
        $codeKbns = array(
            // ヘッダー部
            'moushi_code_kbn' => self::CODE_KBN_MOUSHI_OM, // 申込区分
            'sougi_code_kbn' => self::CODE_KBN_SOUGI_KBN, // 葬儀区分
            'p_info_code_kbn' => self::CODE_P_INFO, // 個人情報保護
            'kaiin_code_kbn' => self::CODE_KBN_KAIIN_KBN, // 会員区分
            // 受付情報タブ
            'haigu_code_kbn' => self::CODE_KBN_HAIGU_KBN, // 配偶者区分
            'umu_code_kbn' => self::CODE_KBN_UMU, // 有無区分
            'pacemaker_code_kbn' => self::CODE_KBN_PACEMAKER, // ペースメーカー区分
            'infection_code_kbn' => self::CODE_KBN_INFECTION, // 感染症区分
            'zoku_code_kbn' => self::CODE_KBN_ZOKU_KBN, // 続柄区分
            'sex_code_kbn' => self::CODE_KBN_SEX_KBN, // 性別
            // 打合せ事項①
            'keishiki_code_kbn' => self::CODE_KBN_KEISHIKI_KBN, // 葬儀形式
            'syuha_code_kbn' => self::CODE_KBN_SYUHA_KBN, // 宗派区分
            'syushi_code_kbn' => self::CODE_KBN_SYUSHI_KBN, // 宗旨区分
            'kaimyo_code_kbn' => self::CODE_KBN_KAINAME1_KBN, // 戒名
            'tera_shokai_code_kbn' => self::CODE_KBN_TERA_SHOKAI, // 寺紹介者
            // 打合せ事項②
            'choi_koden_code_kbn' => self::CODE_KBN_CHOI_KODEN, // 弔意香典
            'choi_kumotu_code_kbn' => self::CODE_KBN_CHOI_KUMOTU, // 弔意供花供物
            'seika_form_code_kbn' => self::CODE_KBN_SEIKA_FORM, // 供花問い合わせ区分
            'portrat_code_kbn' => self::CODE_KBN_PORTRAIT, // 済未区分
        );
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        // 消費税取得処理
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        $taxInfoAll = App_ClsTaxLib::GetTaxInfoAll($db);
        $dataSekoKihon['login_bumon_cd'] = App_Utils::getTantoBumonCd(App_Utils::getTantoCd());
        // 所属部門の親部門の配下の共通部門を取得する
        if (isset($dataSekoKihon['seko_tanto_cd']) && strlen($dataSekoKihon['seko_tanto_cd']) > 0) {
            $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => $this->getTantoBumonCd($dataSekoKihon['seko_tanto_cd'])));
        } else {
            $bumonData = DataMapper_BumonEx::find($db, array('bumon_cd' => App_Utils::getTantoBumonCd(App_Utils::getTantoCd())));
        }
        $comBumonCd = App_Utils2::getCommonBumonCd($bumonData[0]['bumon_cd']);
        $dataSekoKihon['shikijo_bumon_cd'] = $comBumonCd;
        if (isset($dataSekoKihon['k_birth_year'])) {
            $keyIndex = array_search($dataSekoKihon['k_birth_year'], array_column($dataKbns['gengo'], 'kbn_value_cd_num'));
            $result = $dataKbns['gengo'][$keyIndex];
            $dataSekoKihon['k_wa_year'] = $result['kbn_value_snm'];
        }
        if (isset($dataSekoKihon['m_birth_year'])) {
            $keyIndex = array_search($dataSekoKihon['m_birth_year'], array_column($dataKbns['gengo'], 'kbn_value_cd_num'));
            $result = $dataKbns['gengo'][$keyIndex];
            $dataSekoKihon['m_wa_year'] = $result['kbn_value_snm'];
        }
        
        // 画面データを設定する
        $data = array(
            'controllerName' => $controllerName,
            'dataSekoKihon' => $dataSekoKihon,
            'dataSekoKihonFree' => $dataKihonFree,
            'dataGojokaiInfo' => $dataGojokaiInfo,
            'dataSekoKeiyakusakiInfo' => $dataSekoKeiyakusakiInfo,
            'dataGojokaiMemberCol' => $dataGojokaiMemberCol,
            'gojokaiCouseMst' => $gojokaiCouseMst,
            'dataKbns' => $dataKbns,
            'codeKbns' => $codeKbns,
            'bechuShohin' => $dataBechuShohin,
            'keiyakuCif' => $dataKeiyakuCif,
            'dataUpdateHistoryContents' => $this->getUpdateHistoryContents(),
            'sideMenuDataCol' => $sideMenuData['dataCol'],
            'sidemenukey' => $sidemenukey,
            'seko_kaisya_cd'   => Logic_Hanso_HansoUtils::guessMyKaisyaBumonCd(),
            'area_cd'   => $area_cd,
            'taxInfo' => $taxInfo,
            'taxInfoAll' => $taxInfoAll,
            'dataSideMenu' => $sideMenuData,
            'mail_info1' => $mail_info1,
            'mail_info2' => $mail_info2,
        );
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     * 施行基本データ（事前相談用）取得処理
     *
     * <AUTHOR> Sai
     * @since 2016/02/01
     * @return array 
     */
    private function getSekoKihonShoudan() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                ski.m_nm AS customer_nm
                ,ski.m_knm AS customer_knm
                ,COALESCE(ski.mg_yubin_no ,'') || ' ' || COALESCE(ski.mg_addr1 ,'') AS addr1
                ,ski.mg_addr2 AS addr2
                ,ski.mg_tel AS tel_no
                ,ski.mg_m_tel AS mobile_tel
                ,ski.mk_kinmusaki_nm AS office_nm
                ,ski.mk_yakusyoku_nm AS office_post_nm
                ,ski.k_nm
                ,ski.k_knm
                ,cnm.kbn_value_lnm AS kaiin_kbn_nm
                ,ski.kaiin_sonota
            FROM seko_kihon_info ski
            LEFT OUTER JOIN code_nm_mst cnm
                ON ski.kaiin_cd = cnm.kbn_value_cd
                AND cnm.code_kbn = '0030'
                AND cnm.delete_flg = 0
            WHERE ski.delete_flg = 0
                AND ski.seko_no = :seko_no
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        $select['consult_tanto_cd'] = App_Utils::getTantoCd();
        $select['consult_tanto_nm'] = App_Utils::getTantoNm();
        $select['consult_date'] = Msi_Sys_Utils::getDate();
        // 会員区分設定
        $kaiin_nm = "会員区分 ： ";
        if (!empty($select['kaiin_sonota'])) {
            $kaiin_nm .= $select['kaiin_sonota'];
        } else {
            $kaiin_nm .= $select['kaiin_kbn_nm'];
        }
        $select['kaiin_nm'] = $kaiin_nm;
        return $select;
    }

    /**
     * 顧客基本情報保存処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/01
     * @param Msi_Sys_Db $db db
     * @param array $dataJizen
     * @param array $dataSekoKihon
     * @return int 更新件数
     */
    private function saveCustomerBaseInfo($db, $dataJizen, $dataSekoKihon) {
        $except = array();
        array_push($except, 'uketuke_date_disp');
        array_push($except, 'ap_date');
        array_push($except, 'ap_time');
        array_push($except, 'consult_date');
        array_push($except, 'consult_time');
        array_push($except, 'consult_tanto_nm');
        array_push($except, 'nyukai_point');
        array_push($except, 'minyukai_reason');
        array_push($except, 'syanai_renrakusaki');

        // emptyToNull
        $dataJizen = Msi_Sys_Utils::emptyToNullArr($dataJizen);
        if (isset($dataJizen['ap_time']) && strlen($dataJizen['ap_time']) > 0) {
            $dataJizen['ap_inp_kbn'] = 0;
        } else {
            $dataJizen['ap_inp_kbn'] = 1;
        }
        if (isset($dataJizen['consult_time']) && strlen($dataJizen['consult_time']) > 0) {
            $dataJizen['consult_inp_kbn'] = 0;
        } else {
            $dataJizen['consult_inp_kbn'] = 1;
        }
        if (isset($dataJizen['nyukai_point'])) {
            $dataJizen['text_free1'] = $dataJizen['nyukai_point'];
        }
        if (isset($dataJizen['minyukai_reason'])) {
            $dataJizen['text_free2'] = $dataJizen['minyukai_reason'];
        }
        if (isset($dataJizen['syanai_renrakusaki'])) {
            $dataJizen['text_free3'] = $dataJizen['syanai_renrakusaki'];
        }
        // 存在チェック
        $select = $this->getCustomerBaseInfo();
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $dataJizen['seko_no'] = $this->_sekoNo;
            $dataJizen['customer_cd'] = $this->_customerCd;
//            $oneRow['customer_kbn_cd'] = '11';
//            $oneRow['customer_kbn'] = 11;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("customer_base_info", $dataJizen, $except);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['customer_cd'] = $this->_customerCd;  // 顧客番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("customer_base_info", $dataJizen, $where, $except);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 応対履歴保存処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/06
     * @param Msi_Sys_Db $db db
     * @param array $dataConsultHistory
     * @return int 更新件数
     */
    private function saveReceptionHis($db, $dataConsultHistory) {
        // 削除→登録する
        $sql = "
            DELETE
            FROM reception_history
            WHERE seko_no = :seko_no 
                ";
        $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo));
        foreach ($dataConsultHistory as $oneRow) {
            $except = array();
            array_push($except, 'tanto_nm');
            $oneRow['seko_no'] = $this->_sekoNo;
            // emptyToNull
            $oneRow = Msi_Sys_Utils::emptyToNullArr($oneRow);
//            $oneRow['reception_date'] = Msi_Sys_Utils::emptyToNull($oneRow['reception_date']);
//            $oneRow['reception_time'] = Msi_Sys_Utils::emptyToNull($oneRow['reception_time']);
//            $oneRow['renraku_nm'] = Msi_Sys_Utils::emptyToNull($oneRow['renraku_nm']);
//            $oneRow['douseki_nm'] = Msi_Sys_Utils::emptyToNull($oneRow['douseki_nm']);
//            $oneRow['reception_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['reception_kbn']);
//            $oneRow['reception_method_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['reception_method_kbn']);
//            $oneRow['reception_memo'] = Msi_Sys_Utils::emptyToNull($oneRow['reception_memo']);
            if (isset($oneRow['reception_time'])) {
                $oneRow['reception_ymd'] = $oneRow['reception_date'].' '.$oneRow['reception_time'];
            } else {
                $oneRow['reception_ymd'] = $oneRow['reception_date'];
            }
            // 応対履歴登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("reception_history", $oneRow, $except);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     * 相談項目詳細保存処理 
     *
     * <AUTHOR> Sai
     * @since 2016/02/02
     * @param Msi_Sys_Db $db db
     * @param array $dataConsult
     * @return int 更新件数
     */
    private function saveConsultItemDtl($db, $dataConsult) {
        $cnt = 0;
        foreach ($dataConsult as $value) {
            $oneRow = Msi_Sys_Utils::remapArrayFlat($value, 'check_kbn support_kbn memo');
            $oneRow['consult_item_cd'] = $value['kbn_value_cd_num'];
            $oneRow['consult_item_kbn'] = $value['kbn_value_cd_num'];
            $oneRow['check_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['check_kbn']);
            $oneRow['support_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['support_kbn']);
            $except = array();
            // 存在チェック
            $select = $this->getConsultItemDtl($oneRow['consult_item_cd']);
            if (count($select) === 0) {
                $oneRow['seko_no'] = $this->_sekoNo;
                $oneRow['consult_history_no'] = $this->_historyNo;
                // 登録SQL
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("consult_item_detail", $oneRow);
            } else {
                array_push($except, 'seko_no');
                array_push($except, 'consult_history_no');
                array_push($except, 'consult_item_cd');
                array_push($except, 'consult_item_kbn');
                // 条件部
                $where['seko_no'] = $this->_sekoNo;  // 施行番号
                $where['consult_history_no'] = $this->_historyNo;  // 顧客番号
                $where['consult_item_cd'] = $oneRow['consult_item_cd']; // 相談項目区分
                $where['delete_flg'] = 0;  // 削除フラグ
                // 更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("consult_item_detail", $oneRow, $where, $except);
            }
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     * お渡し資料状況詳細保存処理 
     *
     * <AUTHOR> Sai
     * @since 2016/02/02
     * @param Msi_Sys_Db $db db
     * @param array $dataShiryo
     * @return int 更新件数
     */
    private function saveHelpfulPameDtl($db, $dataShiryo) {
        $cnt = 0;
        foreach ($dataShiryo as $value) {
            $oneRow = Msi_Sys_Utils::remapArrayFlat($value, 'delivery_kbn entry_kbn memo');
            $oneRow['helpful_pamph_cd'] = $value['kbn_value_cd_num'];
            $oneRow['helpful_pamph_kbn'] = $value['kbn_value_cd_num'];
            $oneRow['delivery_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['delivery_kbn']);
            $oneRow['entry_kbn'] = Msi_Sys_Utils::emptyToNull($oneRow['entry_kbn']);
            $except = array();
            // 存在チェック
            $select = $this->getHelpfulPamDtl($oneRow['helpful_pamph_cd']);
            if (count($select) === 0) {
                $oneRow['seko_no'] = $this->_sekoNo;
                $oneRow['consult_history_no'] = $this->_historyNo;
                // 登録SQL
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("helpful_pamphlet_detail", $oneRow);
            } else {
                array_push($except, 'seko_no');
                array_push($except, 'consult_history_no');
                array_push($except, 'helpful_pamph_cd');
                array_push($except, 'helpful_pamph_kbn');
                // 条件部
                $where['seko_no'] = $this->_sekoNo;  // 施行番号
                $where['consult_history_no'] = $this->_historyNo;  // 顧客番号
                $where['helpful_pamph_cd'] = $oneRow['helpful_pamph_cd']; // お渡し資料区分
                $where['delete_flg'] = 0;  // 削除フラグ
                // 更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("helpful_pamphlet_detail", $oneRow, $where, $except);
            }
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     *
     * 事前相談履歴の最大相談履歴番号+1を取得する
     *
     * <AUTHOR> Sai
     * @since 2016/02/02
     * @return array 顧客基本情報
     */
    private function getMaxHistoryNo() {
        $no = 1;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                COALESCE(MAX(consult_history_no), 0) + 1 AS no
            FROM
                advance_consult_history
            WHERE
                    delete_flg = 0
                AND customer_cd = :customer_cd
                AND seko_no = :seko_no
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'customer_cd' => $this->_customerCd));
        if (count($select) > 0) {
            $no = $select['no'];
        }
        return $no;
    }

    /**
     *
     * 相談項目詳細を取得する
     *
     * <AUTHOR> Sai
     * @param string $code_kbn
     * @since 2016/02/02
     * @return array 相談項目詳細
     */
    private function getConsultItemDtlAll($code_kbn) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                cid.seko_no
                ,cid.consult_history_no
                ,cid.consult_item_cd
                ,cid.consult_item_kbn
                ,cid.check_kbn
                ,cid.support_kbn
                ,cid.entry_kbn
                ,cid.memo
                ,cnm.kbn_value_cd
                ,cnm.kbn_value_cd_num
                ,cnm.kbn_value_lnm
            FROM code_nm_mst cnm
            LEFT OUTER JOIN consult_item_detail cid
                ON cnm.kbn_value_cd = cid.consult_item_cd
                AND cid.delete_flg = 0
                AND cid.seko_no = :seko_no
            WHERE cnm.delete_flg = 0
                AND cnm.code_kbn = :code_kbn
            ORDER BY cnm.kbn_value_cd_num            
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'code_kbn' => $code_kbn));
        return $select;
    }

    /**
     *
     * 相談項目詳細を取得する
     *
     * <AUTHOR> Sai
     * @param string $consult_item_cd 相談項目区分コード
     * @since 2016/02/02
     * @return array 相談項目詳細
     */
    private function getConsultItemDtl($consult_item_cd) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT cid.seko_no        -- 施行番号
            FROM consult_item_detail cid
            WHERE cid.delete_flg = 0
                AND cid.seko_no = :seko_no
                AND cid.consult_history_no = :consult_history_no
                AND cid.consult_item_cd = :consult_item_cd
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'consult_history_no' => $this->_historyNo, 'consult_item_cd' => $consult_item_cd));
        return $select;
    }

    /**
     *
     * お渡し資料状況詳細を取得する
     *
     * <AUTHOR> Sai
     * @param string $code_kbn
     * @since 2016/02/02
     * @return array お渡し資料状況詳細
     */
    private function getHelpfulPamDtlAll($code_kbn) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                hpd.seko_no
                ,hpd.consult_history_no
                ,hpd.helpful_pamph_cd
                ,hpd.helpful_pamph_kbn
                ,hpd.delivery_kbn
                ,hpd.support_kbn
                ,hpd.entry_kbn
                ,hpd.memo
                ,cnm.kbn_value_cd
                ,cnm.kbn_value_cd_num
                ,cnm.kbn_value_lnm
            FROM code_nm_mst cnm
            LEFT OUTER JOIN helpful_pamphlet_detail hpd
                ON cnm.kbn_value_cd = hpd.helpful_pamph_cd
                AND hpd.delete_flg = 0
                AND hpd.seko_no = :seko_no
            WHERE cnm.delete_flg = 0
                AND cnm.code_kbn = :code_kbn
            ORDER BY cnm.kbn_value_cd_num        
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo, 'code_kbn' => $code_kbn));
        return $select;
    }

    /**
     *
     * お渡し資料状況詳細を取得する
     *
     * <AUTHOR> Sai
     * @param string $helpful_pamph_cd お渡し資料区分コード
     * @since 2016/02/02
     * @return array お渡し資料状況詳細
     */
    private function getHelpfulPamDtl($helpful_pamph_cd) {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT hpd.seko_no        -- 施行番号
            FROM helpful_pamphlet_detail hpd
            WHERE hpd.delete_flg = 0
                AND hpd.seko_no = :seko_no
                AND hpd.consult_history_no = :consult_history_no
                AND hpd.helpful_pamph_cd = :helpful_pamph_cd
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'consult_history_no' => $this->_historyNo, 'helpful_pamph_cd' => $helpful_pamph_cd));
        return $select;
    }

    /**
     * 
     * 承認状況確認処理 
     *
     * <AUTHOR> Sugiyama
     * @since  2019/12/03
     * @param array $req リクエスト
     */
    public function shohincheck($req) {
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));
        $this->_sekoNo = $dataSekoKihon['seko_no'];
        // 売上伝票の承認情報を取得する
        $dateUriShonin = $this->getUriageShoninInfo();
        $hasShonin = $this->hasUriageShonin($dateUriShonin);
        $status = "OK";
        $msg = "";
        // 会員情報変更フラグチェック
        if ($changeFlg['gojokaiMemberChangeFlg']) {
            // 承認状況確認チェック
            if ($hasShonin) {
                $db = Msi_Sys_DbManager::getMyDb();
                $kaisyaInfo = DataMapper_KaisyaInfo::find($db);
                if (count($kaisyaInfo) > 0) {
                    $msg = "請求書はすでに承認済です\n" . $kaisyaInfo[0]['kain_nm'] . "消費税額が変更されますが、よろしいでしょうか";
                } else {
                    $msg = "請求書はすでに承認済です\n消費税額が変更されますが、よろしいでしょうか";
                }
                $status = "NG";
            }
        }
        // 画面データを設定する
        $data = array(
            'status' => $status,
            'msg' => $msg,
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 
     * 施行の現在情報取得処理 
     *
     * <AUTHOR> Tosaka
     * @since  2020/06/24
     * @param array $dataApp 画面データ
     */
    public function getCurData($dataApp) {
        // 施行基本情報を設定する
        $this->_sekoNo = $dataApp['seko_no'];
        $this->setInitParam();
        $dataSekoKihon = $this->getSekoKihon();
        $niteiCol = $this->getNitei($dataSekoKihon);
        $data['dataSekoKihon'] = $dataSekoKihon;
        $data['dataNiteiCol'] = $niteiCol;
        $data['modCnts'] = $this->getModCnts();
        return $data;
    }

    /**
     * 
     * 所属部門コード取得 
     *
     * <AUTHOR> Sai
     * @since  2021/08/xx
     * @param string $tanto_cd 担当者コード
     */
    private function getTantoBumonCd($tanto_cd) {
        $db = Msi_Sys_DbManager::getMyDb();
        $tanto = DataMapper_TantoMst::findOne($db, array('tanto_cd' => $tanto_cd) ,false);
        if (!is_array($tanto)) {
            $msg = sprintf("担当者CD(%s)で所属部門が取得できません", $tanto_cd);
            throw new Exception($msg);
        }
        return $tanto['bumon_cd'];
    }
    
    
}
