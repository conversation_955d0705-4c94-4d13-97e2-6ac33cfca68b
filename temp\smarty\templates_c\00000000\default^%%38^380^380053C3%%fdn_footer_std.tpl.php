<?php /* Smarty version 2.6.28, created on 2025-06-05 11:48:27
         compiled from fdn_footer_std.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'fdn_footer_std.tpl', 9, false),)), $this); ?>
</div><!-- /#wrapper -->

<?php echo $this->_tpl_vars['result_message']; ?>


<div id="my-spin"></div>
<div id="msi-app-id-fdn" style="display:none"></div>

<?php echo $this->_tpl_vars['footer_added']; ?>

<?php if (! ((is_array($_tmp=$this->_tpl_vars['js_require_js_ctxt'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/mylib/msiLast.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<?php endif; ?>
</body>
</html>