var apps = apps || {};
$(function () {
    "use strict";
    
    // 画面クラスとモデルのプロパティのオブジェクト
    apps.pro = {
    };
    var utils = window.msiBbUtils;
    var viewUtils = window.msiBbViewUtils;
    
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                s_bumon_nm         : null,  // 部門名
                s_bumon_cd         : null,  // 部門CD
                s_seko_tanto_nm    : null,  // 施行担当者名
                s_seko_tanto_cd    : null,  // 施行担当者CD
                s_ryosyu_prt_status: null,  // 領収証出力状況
                s_pay_method_cd    : null,  // 入金方法
                s_ryosyu_irai_kbn  : null,  // 領収証依頼区分
                s_nyukin_ymd_from  : null,  // 入金日開始
                s_nyukin_ymd_to    : null,  // 入金日終了
                s_nyukin_den_no    : null,  // 入金伝票No
                s_moushi_kbn       : null,  // 申込区分
                s_seko_no          : null,  // 施行番号
                s_juchu_den_no     : null,  // 受注伝票No
                s_seikyu_no        : null,  // 請求No
                s_seikyu_nm        : null,  // 請求先名
                s_seko_ymd_from    : null,  // 施行日/納入日開始
                s_seko_ymd_to      : null,  // 施行日/納入日終了
                s_ryosyu_no        : null,  // 領収証No
                _chkAll            : false,
            };
        },
        validation: {
            s_nyukin_ymd_from: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            s_nyukin_ymd_to: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            s_seko_ymd_from: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
            s_seko_ymd_to: {
                required: false,
                fn: Backbone.Validation.msi_v_fn.ymd,
            },
        },
        labels: {
        },
    }); // AppModel
    
    var AppView = Backbone.View.extend({
        el: '#my-form-id',
        events: {
            "click #btn_search"                         : "doSearch",           // 検索ボタン
            "click #btn_clear"                          : "doClear",            // クリアボタン
            "click #btn_save"                           : "doSave",             // 保存ボタン
            "click #btn_kobetu"                         : "doKobetuHako",       // 個別発行
            "click #btn_ikatu"                          : "doIkatuHako",        // 一括発行
            //"click #s_bumon_nm, .s_bumon-ref"           : "bumonHelper",        // 部門検索
            "click #s_seko_tanto_nm, .s_seko_tanto-ref" : "sekoTnatoHelper",    // 施行担当者検索
            "click .s_seko_no-ref"                      : "sekoNoHelper",       // 施行番号検索
            "click .s_juchu_den_no-ref"                 : "sekoNoHelper",       // 施行番号検索
            "click #order #btn_search_prev"             : "doPrevSearch",       // 前ページボタン
            "click #order #btn_search_next"             : "doNextSearch",       // 次ページボタン
            "click .chkAllToggle"                       : "toggleChkAll",       // 全選択ボタン
        },
        bindings: {
            '#s_bumon_nm'          : 's_bumon_nm',           // 部門名
            '#s_bumon_cd'          : $.msiJqlib.getSelect2Binding('s_bumon_cd'), // 部門名コード
            '#s_seko_tanto_nm'     : 's_seko_tanto_nm',      // 施行担当者名
            '#s_ryosyu_prt_status' : $.msiJqlib.getSelect2Binding('s_ryosyu_prt_status'), // 領収証出力状況
            '#s_pay_method_cd'     : $.msiJqlib.getSelect2Binding('s_pay_method_cd'),     // 入金方法
            '#s_ryosyu_irai_kbn'   : $.msiJqlib.getSelect2Binding('s_ryosyu_irai_kbn'),   // 領収証依頼区分
            '#s_nyukin_ymd_from'   : 's_nyukin_ymd_from',    // 入金日開始
            '#s_nyukin_ymd_to'     : 's_nyukin_ymd_to',      // 入金日終了
            '#s_nyukin_den_no'     : 's_nyukin_den_no',      // 入金伝票No
            '#s_moushi_kbn'        : $.msiJqlib.getSelect2Binding('s_moushi_kbn'), // 入金方法
            '#s_seko_no'           : 's_seko_no',            // 施行番号
            '#s_juchu_den_no'      : 's_juchu_den_no',       // 受注伝票No
            '#s_seikyu_no'         : 's_seikyu_no',          // 請求No
            '#s_seikyu_nm'         : 's_seikyu_nm',          // 請求先名
            '#s_seko_ymd_from'     : 's_seko_ymd_from',      // 施行日/納入日開始
            '#s_seko_ymd_to'       : 's_seko_ymd_to',        // 施行日/納入日終了
            '#s_ryosyu_no'         : 's_ryosyu_no',          // 領収証No
        },
        initialize: function () {
            this.listenTo(apps.resultCol, 'reset', this.addAllResultCol);
            // バリデーションチェック
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(apps.pro, "error1"));
            this.render();
        },
        render: function () {
            this.setSelect2();
            this.stickit();
            // スクロール調整
            this.scrollAdj();
            return this;
        },
        setSelect2: function (){
            // 領収証出力状況
            $.msiJqlib.setSelect2Com1(this.$("#s_ryosyu_prt_status"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.ryosyu_prt_status)}, $.msiJqlib.setSelect2Default1)));
            // 入金方法
            $.msiJqlib.setSelect2Com1(this.$("#s_pay_method_cd"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.payment_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 領収証依頼区分
            $.msiJqlib.setSelect2Com1(this.$("#s_ryosyu_irai_kbn"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.ryosyu_irai_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 申込区分
            $.msiJqlib.setSelect2Com1(this.$("#s_moushi_kbn"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.moushi_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 部門コード
            $.msiJqlib.setSelect2Com1(this.$("#s_bumon_cd"), ($.extend({data: mydata.dataKbns.oya_bumon}, $.msiJqlib.setSelect2Default1, {'allowClear':false})));
        },
        // 前検索
        doPrevSearch: function () {
            var offset = apps.appModel.get('offset') * 1;
            if (offset > 0) {
                apps.appModel.set('offset', offset - 1);
            }
            this.doSearch(1);
        },
        // 次検索
        doNextSearch: function () {
            var offset = apps.appModel.get('offset') * 1;
            apps.appModel.set('offset', offset + 1);
            this.doSearch(2);
        },
        // 検索
        doSearch: function (id) {
            if(id !== 1 && id !== 2){
                apps.appModel.set('offset', null);
            }
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/ryoshulist/search',
                data: {
                    dataAppJson: JSON.stringify(apps.appModel.toJSON()),
                    mode: 1,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        _resetData(mydata);
                        // エラーメッセージ
                        if(!$.msiJqlib.isNullEx2(mydata.msg)){
                            $(".list_table").append("<tbody><tr><td colspan='11'>"+mydata.msg+"</td></tr></tbody>");
                        }
                        // スクロール調整
                        that.scrollAdj();
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
                // error処理は共通設定を使う
            });
        },
        // クリア
        doClear: function () {
            _resetData( orgDataApp );
        },
        // 保存
        doSave: function () {
            msiLib2.clearAlert();
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/ryoshulist/save',
                data: {
                    dataAppJson: JSON.stringify(apps.appModel.toJSON()),
                    dataColJson: JSON.stringify(apps.resultCol.toJSON()),
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        msiLib2.showInfo(mydata.msg);
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
            });
        },
        // 一括発行
        doIkatuHako: function(ev) {
            msiLib2.clearAlert();
            ev.stopImmediatePropagation();
            var type        = 'doDownload2';
            var isCtrlKey   = !!ev.ctrlKey;  // Ctrlキー
            var maxDlSize   = 999;           // 取り込み最大件数
            if ( isCtrlKey ) { type = undefined; } // Ctrlキーの場合は csv とする
            if($.msiJqlib.isNullEx2(apps.resultCol)){
                msiLib2.showErr('領収証データがありません。');
                return ;
            }else if(apps.resultCol.length == 0) {
                msiLib2.showErr('選択されていません。');
                return ;
            }else{
                var check_count = 0; // 選択カウント
                var hako_count = 0;  // 発行済みカウント
                var ikatsu_fuka_count = 0; // 一括発行不可フラグカウント
                _.each(apps.resultCol.models, function(v, k){
                    var model = v;
                    if(!$.msiJqlib.isNullEx2(model.get('ryoshu_check')) && model.get('ryoshu_check') == "1"){
                        check_count++;
                        if(!$.msiJqlib.isNullEx2(model.get('ryosyu_prt_status')) && model.get('ryosyu_prt_status') == "1"){
                            hako_count++;
                        }
                        if(model.get('ikatsu_fuka_flg')){
                            ikatsu_fuka_count++;
                        }
                    }
                });
                if(check_count === 0){
                    msiLib2.showErr('選択されていません。');
                    return;
                }
                if(hako_count > 0){
                    msiLib2.showErr('発行済みの領収証です。');
                    return;
                }
                if(ikatsu_fuka_count > 0){
                    msiLib2.showErr('内金は一括発行出来ません。');
                    return;
                }
            }
            var iMsg = '選択された領収データを一括発行します';
            if ( ! confirm( iMsg + "\nよろしいですか？") ) {
                return;
            }
            var data = {
                dataAppJson : JSON.stringify(apps.appModel.toJSON()),
                dataColJson : JSON.stringify(apps.resultCol.toJSON()),
                dl_type     : type == 'doDownload2' ? 'bdl' : 'csv',
            };
            var that = this;
            msiLib2.fileDlAjax({
                    url  : $.msiJqlib.baseUrl() + '/saiken/ryoshulist/ikatuhako',
                    data : data,
                    timeout: 400000,
                },
                function(data) {
                    if ( data.status == 'OK' ) {
                        if ( data.opt && data.opt.outMsg ) {
                            var msg = data.opt.outMsg;
                            if ( data.opt.isOutMsgErr ) {
                                $.msiJqlib.showWarn2( msg );
                            } else {
                                $.msiJqlib.showInfo2( msg );
                                that.doSearch();
                            }
                            return;
                        }
                    } else {
                        $.msiJqlib.showErr2( data.msg );
                    }
                } 
            );
        },
        // 個別発行
        doKobetuHako: function() {
            msiLib2.clearAlert();
            var selected = [];
            _.each(apps.resultCol.models, function(v, k){
                var model = v;
                if(!$.msiJqlib.isNullEx2(model.get('ryoshu_check')) && model.get('ryoshu_check') == "1"){
                    selected.push(model);
                }
            });
            if(selected.length === 0){
                msiLib2.showErr('選択されていません。');
                return;
            }else if(selected.length > 1){
                msiLib2.showErr('行が複数選択されています。');
                return;
            }
            var seikyu_den_no = selected[0].get('seikyu_den_no');
            var uri_den_no = selected[0].get('uri_den_no');
            var juchu_den_no = selected[0].get('juchu_den_no');
            this.doShow(seikyu_den_no, uri_den_no, juchu_den_no);
        },
        // 別画面表示
        doShow: function(seikyu_den_no, uri_den_no, juchu_den_no) {
            var url = null;
            if($.msiJqlib.isNullEx2(seikyu_den_no) && $.msiJqlib.isNullEx2(uri_den_no)){
                url = $.msiJqlib.baseUrl() + '/saiken/ryoshu/order/jdn/' + juchu_den_no;
            }else if($.msiJqlib.isNullEx2(seikyu_den_no)){
                url = $.msiJqlib.baseUrl() + '/saiken/ryoshu/order/udn/' + uri_den_no;
            }else{
                url = $.msiJqlib.baseUrl() + '/saiken/ryoshu/order/sdn/' + seikyu_den_no;
            }
            var refreshFunc = function() { console.log('refresh searching...'); };
            msiLib2.openWinSub( refreshFunc, url );
            
        },
        // 部門 pickup
        bumonHelper: function() {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'bumon',
                onSelect: function(data) {
                    bbm.set('s_bumon_cd', data.code);
                    bbm.set('s_bumon_nm', data.name.trim());
                },
                onClear: function() {
                    bbm.set('s_bumon_cd', null);
                    bbm.set('s_bumon_nm', null);
                },
                hookSetData: function() {
                    return {
                        s_bumon_kbn: '0,1,2',
                    }
                },
            });
        },
        // 施行担当 pickup
        sekoTnatoHelper: function() {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function(data) {
                    bbm.set('s_seko_tanto_cd', data.code);
                    bbm.set('s_seko_tanto_nm', data.name);
                },
                onClear: function() {
                    bbm.set('s_seko_tanto_cd', null);
                    bbm.set('s_seko_tanto_nm', null);
                },
                hookSetData: function() {
                    return { 
                        s_bumon:'', // 部門を指定しない場合
                        mode: '1'
                    }; 
                },
            });
        },
        // 施行No pickup
        sekoNoHelper: function() {
            var bbm = this.model;
            msiLib2.celemonyDialog(
                function(data) {
                    bbm.set('s_seko_no', data.seko_no);
                } );
        },
        addResultOne: function (msi) {
            var v = new ResultView({model: msi});
            this.$(".list_table").append(v.render().el);
            v.render().$('tr').addClass(v.model.get('oddEven'));
        },
        addAllResultCol: function (collection) {
            var $msi = this.$(".list_table");
            $msi.find('tbody').remove();
            collection.each(this.addResultOne, this);
        },
        // check トグル
        toggleChkAll: function() {
            var _chkAll = !this.model.get('_chkAll');
            this.model.set('_chkAll', _chkAll);
            if ( _chkAll ) {
                this.$('.chkAllToggle').addClass('selected');
                this.checkAll();
            } else {
                this.$('.chkAllToggle').removeClass('selected');
                this.uncheckAll();
            }
        },
        // 行全選択
        checkAll: function() {
            this._checkAll(true);
        },
        // 行全選択解除
        uncheckAll: function() {
            this._checkAll(false);
        },
        // 行全設定
        _checkAll: function(isSet) {
            if(isSet){
                _.each(apps.resultCol.models, function(v, k){
                    var model = v;
                    var data_kbn = model.get('data_kbn');
                    if(data_kbn == "4"){
                        if(model.get('ryosyu_prt_status') == "0"){  // 未発行
                            if(model.get('pay_method_cd') == "1") {  // 振込
                                if(model.get('ryosyu_irai_kbn') != "0") { // 不要以外
                                    if(model.get('ryoshu_check') != "1"){ // 未選択
                                        apps.resultCol.models[k].set("ryoshu_check", "1");
                                    }
                                }
                            }
                        }
                    }
                });
            }else{
                _.each(apps.resultCol.models, function(v, k){
                    apps.resultCol.models[k].set("ryoshu_check", "0");
                });
            }
            this.model.trigger('change');
        },
        // スクロールバー表示調整
        scrollAdj: function() {
            var $list = this.$('.result-list .list'),
                $header = this.$('.result-list .header'),
                sc_of,
                sc_w,
                hh,
                cont_h = $('#order').height(),
                src_h = this.$('.search').height(),
                adj_h = 220, // button, etc.
                my_h;
            my_h = cont_h - src_h - adj_h;
            $list.height( my_h );
            if ( $list[0].scrollHeight === $list[0].clientHeight ) {
                sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
            } else {
                sc_of = 'scroll';
                hh = $header.height();
                $list.css("overflow-y", sc_of); 
                $header.css("overflow-y", sc_of);
                $header.height(hh); // for Chrome. XXX
            }
        },
    }); // AppView
    // 検索結果モデル
    var ResultModel = Backbone.Model.extend({
        defaults: function () {
            return {
                oya_bumon_nm         : null,    // 部門名
                oya_bumon_cd         : null,    // 親部門コード
                bumon_nm             : null,    // 部門名
                bumon_cd             : null,    // 部門コード
                data_kbn             : null,    // データ区分
                moushi_kbn           : null,    // 申込区分
                moushi_kbn_nm        : null,    // 申込区分
                seko_no              : null,    // 施行番号
                seko_no_sub          : null,    // 施行枝番号
                juchu_den_no         : null,    // 受注伝票番号
                sougi_ymd            : null,    // 葬儀日
                nonyu_ymd            : null,    // 納品日
                seikyu_den_no        : null,    // 請求伝票番号
                sekyu_nm             : null,    // 請求先名
                sekyu_prc            : null,    // 請求金額
                seikyu_zan           : null,    // 請求残
                nyukin_den_no        : null,    // 入金伝票番号
                nyukin_ymd           : null,    // 入金日
                pay_method_nm        : null,    // 入金方法
                pay_method_cd        : null,    // 入金方法区分（支払方法区分）
                ryosyu_irai_kbn      : null,    // 領収証依頼区分
                ryosyu_irai_kbn_nm   : null,    // 領収証依頼区分名
                ryosyu_prc_disp      : null,    // 領収額金額（表示用）
                ryosyu_prc           : null,    // 領収額金額
                ryosyu_prt_status    : null,    // 領収証出力状況
                ryosyu_prt_status_nm : null,    // 領収証出力状況
                ryoshu_check         : null,
                selected             : 0,
                est_shikijo_cd       : null,    // 見積式場コード
                est_shikijo_nm       : null,    // 見積式場名
                k_nm                 : null,    // 故人名
                ikatsu_fuka_flg      : false,   // 一括発行不可フラグ
            };
        },
        validation: {
        },
        labels: {
        }
    }); // ResultModel

    // 検索結果コレクション
    var ResultCollection = Backbone.Collection.extend({
        model: ResultModel
    });

    // 検索結果ビュー
    var ResultView = Backbone.View.extend({
        tagName: 'tbody',
        tmpl: _.template($('#tmpl-result').html()),
        events: {
            //"click tr": "setSelected",
            //"click .ryoshu_check": "ryoshuCheckClick",
        },
        bindings: {
            '.oya_bumon_nm'     : 'oya_bumon_nm',
            '.oya_bumon_cd'     : 'oya_bumon_cd',
            '.bumon_nm'         : 'bumon_nm',
            '.bumon_cd'         : 'bumon_cd',
            '.moushi_kbn'       : 'moushi_kbn_nm',
            '.seko_no'          : 'seko_no',
            '.juchu_den_no'     : 'juchu_den_no',
            '.sougi_ymd'        : 'sougi_ymd',
            '.nonyu_ymd'        : 'nonyu_ymd',
            '.seikyu_den_no'    : 'seikyu_den_no',
            '.sekyu_nm'         : 'sekyu_nm',
            '.sekyu_prc'      : {
                observe : 'sekyu_prc',
                onSet   : 'commaOmit',
                onGet   : 'commaAdd',
            },
            '.nyukin_den_no'    : 'nyukin_den_no',
            '.nyukin_ymd'       : 'nyukin_ymd',
            '.pay_method_nm'    : 'pay_method_nm',
            '.pay_method_cd'    : 'pay_method_cd',
            '.ryosyu_irai_kbn' : {
                observe: 'ryosyu_irai_kbn',
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
            '.ryosyu_irai_kbn_m' :'ryosyu_irai_kbn_nm',
            '.ryosyu_prc'     : {
                observe : 'ryosyu_prc_disp',
                onSet   : 'commaOmit',
                onGet   : 'commaAdd',
            },
            '.ryosyu_prt_status_nm': 'ryosyu_prt_status_nm',
            ".ryoshu_check"        : $.msiJqlib.getCheckBinding('ryoshu_check'),
        },
        ryoshuCheckClick: function (e) {
            _.each(apps.resultCol.models, function(v, k){
                var id  = $(e.currentTarget).attr('id');
                var idx = "ryoshu_check_" + v.get('idx');
                // クリックしたidを比較
                if(id !== idx){
                    // 選択を解除
                    apps.resultCol.models[k].set("ryoshu_check", "0");
                }
            });
        },
        initialize: function () {
            this.listenTo(this.model, 'change:ryosyu_irai_kbn', this.changeRyosyuIraiKbn);
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback({row: '.row'}, "error1"));
            this.render();
        },
        render: function () {
            this.model.set('idx', this.model.cid);
            this.$el.html(this.tmpl(this.model.toJSON()));
            this.setSelect2();
            this.$('.radio_set').buttonset();
            
            if($.msiJqlib.isNullEx2(this.model.get('seikyu_den_no')) && $.msiJqlib.isNullEx2(this.model.get('uri_den_no'))){
                this.$('tr').attr('data-juchu_den_no', this.model.get('juchu_den_no'));
                this.$('tr').addClass('juchu_den_no_' + this.model.get('juchu_den_no'));
            }else if($.msiJqlib.isNullEx2(this.model.get('seikyu_den_no'))){
                this.$('tr').attr('data-uri_den_no', this.model.get('uri_den_no'));
                this.$('tr').addClass('uri_den_no_' + this.model.get('uri_den_no'));
            }else{
                this.$('tr').attr('data-seikyu_den_no', this.model.get('seikyu_den_no'));
                this.$('tr').addClass('seikyu_den_no_' + this.model.get('seikyu_den_no'));
            }
            //選択制御
            //if(this.model.get('ryosyu_prt_status') != "0"){ // 領収証出力状況：振込
            //    this.$('.radio_set').buttonset("disable");
            //    this.$('.radio_set .lbl_ryoshu_check').addClass('change_disabled');
            //}else if(this.model.get('pay_method_cd') != "1") {  // 入金方法：振込
            //    this.$('.radio_set').buttonset("disable");
            //    this.$('.radio_set .lbl_ryoshu_check').addClass('change_disabled');
            //}else if(this.model.get('ryosyu_irai_kbn') == "0") {  // 領収証依頼区分：不要
            //    this.$('.radio_set').buttonset("disable");
            //    this.$('.radio_set .lbl_ryoshu_check').addClass('change_disabled');
            //}
            // 領収証依頼区分制御
            if(this.model.get('pay_method_cd') != "1"){    // 入金方法：振込
                this.$('.cls_ryosyu_irai_kbn').attr('disabled','disabled');
            }
            this.stickit();
            return this;
        },
        setSelect2: function (){
            // 領収証依頼区分
            $.msiJqlib.setSelect2Com1(this.$(".ryosyu_irai_kbn"), ($.extend({data: $.msiJqlib.objToArray3(mydata.dataKbns.ryosyu_irai_kbn2)}, $.msiJqlib.setSelect2Default1)));
        },
        /**
         * 領収証依頼区分変更
         */
        changeRyosyuIraiKbn: function (){
            if(this.model.get('ryosyu_prt_status') == "0"){  // 未発行
                if(this.model.get('pay_method_cd') == "1") {  // 振込
                    if(this.model.get('ryosyu_irai_kbn') == "0") {
                        this.$('.radio_set').buttonset("disable");
                        this.$('.radio_set .lbl_ryoshu_check').addClass('change_disabled');
                        this.$('.radio_set .lbl_ryoshu_check').removeClass('ui-state-active');
                        this.model.set('ryoshu_check', null);
                    }else{
                        this.$(".radio_set").buttonset("enable");
                        this.$('.radio_set .lbl_ryoshu_check').removeClass('change_disabled');
                    }
                }
            }
        },
        // 行選択
        setSelected: function (e) {
            var selected = this.model.get('selected');
            var className = e.target.className;
            var target = className.match('lbl_ryoshu_check|ryoshu_check');
            // 該当項目のカーソル時のダブルクリックは処理しない
            if (!$.msiJqlib.isNullEx2(target)) {
                return;
            }
            if (selected === '1') {
                this.$('tr').removeClass('row-selected');
                this.model.set('selected', '0')
            } else {
                $('tr').removeClass('row-selected');
                _.each(apps.resultCol.models, function (m) {
                    m.set('selected', '0');
                });
                this.$('tr').addClass('row-selected');
                this.model.set('selected', '1');
            }
        },
        commaOmit: function (val) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val) {
            return $.msiJqlib.commaAdd(val);
        },
    }); // ResultView
    
    // 初期化
    var mydata = msiLib2.getJsonFromHtml($('#my-data-init-id'));
    var orgDataApp = mydata;
    apps.resultCol = new ResultCollection();
    apps.appModel = new AppModel();
    apps.appView  = new AppView({ model:  apps.appModel});
    var _resetData= function (mydata) {
        apps.appModel.set(mydata.dataApp);
        apps.resultCol.reset(mydata.dataCol);
        _setButton(mydata);
        $('.chkAllToggle').removeClass('selected');
        //apps.appModel.set('_chkAll',false);
    }
    var _setButton = function (mydata) {
        var next_offset = mydata.next_offset;
        var prev_offset = mydata.prev_offset;
        
        if (prev_offset > 0) {
            $('#order #searchbtnarea #btn_search_prev').show();
        } else {
            $('#order #searchbtnarea #btn_search_prev').hide();

        }
        if (next_offset > 0) {
            $('#order #searchbtnarea #btn_search_next').show();
        } else {
            $('#order #searchbtnarea #btn_search_next').hide();

        }

    };    
    _resetData(mydata);
    // 初期データ退避
    orgDataApp.dataApp = apps.appModel.toJSON();
    orgDataApp.dataCol = apps.resultCol.toJSON();
    

    // リサイズ処理
    $(window).on( 'resize', function() { apps.appView.render(); } );
    
    // msiパーツの有効化 
    msiLib2.msiPrepareParts('#order');
    var siiresaki_flg = false;
    // 各種イベント設定
    $('#order')
        .on('mouseover',
            '.result-list-sel',
            function () {
                var seikyu_den_no = $(this).attr('data-seikyu_den_no');
                var uri_den_no = $(this).attr('data-uri_den_no');
                $('.result-list-sel').find('td').removeClass('my-hover');
                $(".seikyu_den_no_" + seikyu_den_no).find('td').addClass('my-hover').end().find('a').focus();
                $(".uri_den_no_" + uri_den_no).find('td').addClass('my-hover').end().find('a').focus();
                $('.result-list-sel').find('td.row').removeClass('my-hover');
            }
        )
        .on('mouseout', '.result-list-sel',
            function () {
                var seikyu_den_no = $(this).attr('data-seikyu_den_no');
                var uri_den_no = $(this).attr('data-uri_den_no');
                $(".seikyu_den_no_" + seikyu_den_no).find('td').removeClass('my-hover');
                $(".uri_den_no_" + uri_den_no).find('td').removeClass('my-hover');
            }
        )
//        .on('focus', '.result-list-sel',
//            function (e) {
//                var focus_id = $(e.target).attr('id'), nyukin_den_no;
//                $('.result-list-sel').find('td').removeClass('my-hover');
//                if (focus_id && focus_id.indexOf('a_nyukin_den_no_') === 0) {
//                    nyukin_den_no = focus_id.substr(10);
//                    $(".nyukin_den_no_" + nyukin_den_no).find('td').addClass('my-hover');
//                }
//            }
//        )
//        .on('focusout', '.result-list-sel',
//            function (e) {
//                var focus_id = $(e.target).attr('id'), nyukin_den_no;
//                if (focus_id && focus_id.indexOf('a_nyukin_den_no_') === 0) {
//                    nyukin_den_no = focus_id.substr(10);
//                    $(".nyukin_den_no_" + nyukin_den_no).find('td').removeClass('my-hover');
//                }
//            }
//        )
//        .on( 'click', '.result-list-sel',
//             function(e) {
//                 var focus_id = $(':focus').attr('id'), nyukin_den_no;
//                 if ( focus_id && focus_id.indexOf('a_nyukin_den_no_') === 0 ) {
//                     nyukin_den_no = focus_id.substr(10);
//                     setTimeout( function() { apps.appView.selectFunc(nyukin_den_no); }, 0 );
//                     return;
//                 }
//                 nyukin_den_no = $(this).attr('data-seikyu_den_no');
//                 setTimeout( function() { apps.appView.selectFunc(nyukin_den_no); }, 0 );
//            } 
//        )
        .on('dblclick', '.result-list-sel',
            function (e) {
                var seikyu_den_no = $(this).attr('data-seikyu_den_no');
                var uri_den_no = $(this).attr('data-uri_den_no');
                var juchu_den_no = $(this).attr('data-juchu_den_no');
                var className = e.target.className;
                var target = className.match('lbl_ryoshu_check|ryoshu_check');
                // 該当項目のカーソル時のダブルクリックは処理しない
                if (!$.msiJqlib.isNullEx2(target)) {
                    return;
                }
                setTimeout(function () {
                    apps.appView.doShow(seikyu_den_no, uri_den_no, juchu_den_no);
                }, 0);
                return;
            }
        );
});
