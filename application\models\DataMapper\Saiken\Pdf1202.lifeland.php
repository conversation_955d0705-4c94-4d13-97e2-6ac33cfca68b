<?php

/**
 * DataMapper_Pdf1202
 *
 * PDF出力 領収証・印紙確認一覧表 データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Sugiyama
 * @since      2020/12/xx
 * @filesource 
 */

/**
 * PDF出力 領収証・印紙確認一覧表 データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Sugiyama
 * @since      2020/12/xx
 */
class DataMapper_Saiken_Pdf1202 extends DataMapper_Abstract {
    /**
     * データ 取得
     *
     * <AUTHOR> Sugiyama
     * @since   2020/12/xx
     * @param   Msi_Sys_Db $db
     * @param   array      $keyHash  条件
     * @return  array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash=array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.ryosyu_syonin_no DESC, T.inshi_zei_prc ';
        }
        $select = $db->easySelect( <<< END_OF_SQL
SELECT 
     T.ryosyu_nm
    ,T.ryosyu_syonin_ymd
    ,T.ryosyu_syonin_no
    ,T.inshi_zei_prc
    ,SUM(T.suryo)       AS suryo
    ,SUM(T.sum)         AS sum
FROM (
    SELECT 
         TO_CHAR(rh.hako_date, 'YYYY/MM/DD')    AS hako_ymd
        ,CASE sd.data_kbn 
            WHEN 20 THEN ryosyu1.kbn_value_lnm 
                    ELSE ryosyu2.kbn_value_lnm 
         END                                    AS ryosyu_nm
        ,CASE sd.data_kbn 
            WHEN 20 THEN ryosyu1.kbn_value_snm 
                    ELSE ryosyu2.kbn_value_snm 
         END                                    AS ryosyu_syonin_ymd
        ,CASE sd.data_kbn 
            WHEN 20 THEN ryosyu1.biko 
                    ELSE ryosyu2.biko 
         END                                    AS ryosyu_syonin_no
        ,COALESCE(rh.inshi_zei_prc,0)           AS inshi_zei_prc
        ,COUNT(COALESCE(rh.inshi_zei_prc,0))    AS suryo
        ,SUM(COALESCE(rh.inshi_zei_prc,0))      AS sum
        ,bkm.oya_bumon_cd
    FROM ryosyusho_history rh
    INNER JOIN seikyu_denpyo sd
    ON  sd.seikyu_den_no = rh.uri_den_no
    AND sd.delete_flg    = 0
    LEFT JOIN bumon_kaso_mst bkm
    ON bkm.ko_bumon_cd   = sd.bumon_cd
    AND bkm.delete_flg   = 0
    LEFT JOIN code_nm_mst ryosyu1
    ON ryosyu1.code_kbn      = '8542'
    AND ryosyu1.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu1.delete_flg   = 0
    LEFT JOIN code_nm_mst ryosyu2
    ON ryosyu2.code_kbn      = '8551'
    AND ryosyu2.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu2.delete_flg   = 0
    WHERE rh.delete_flg = 0
    AND rh.hako_kbn    <> 9
    AND COALESCE(rh.inshi_zei_prc,0) <> 0
    GROUP BY 
         rh.ryosyu_syonin_no
        ,rh.inshi_zei_prc
        ,ryosyu1.kbn_value_lnm
        ,ryosyu1.kbn_value_snm
        ,ryosyu1.biko
        ,ryosyu2.kbn_value_lnm
        ,ryosyu2.kbn_value_snm
        ,ryosyu2.biko
        ,bkm.oya_bumon_cd
        ,rh.hako_date
        ,sd.data_kbn
    UNION ALL
    -- 内金用
    SELECT 
         TO_CHAR(rh.hako_date, 'YYYY/MM/DD')    AS hako_ymd
        ,CASE sd.data_kbn 
            WHEN 20 THEN ryosyu1.kbn_value_lnm 
                    ELSE ryosyu2.kbn_value_lnm 
         END                                    AS ryosyu_nm
        ,CASE sd.data_kbn 
            WHEN 20 THEN ryosyu1.kbn_value_snm 
                    ELSE ryosyu2.kbn_value_snm 
         END                                    AS ryosyu_syonin_ymd
        ,CASE sd.data_kbn 
            WHEN 20 THEN ryosyu1.biko 
                    ELSE ryosyu2.biko 
         END                                    AS ryosyu_syonin_no
        ,COALESCE(rh.inshi_zei_prc,0)           AS inshi_zei_prc
        ,COUNT(COALESCE(rh.inshi_zei_prc,0))    AS suryo
        ,SUM(COALESCE(rh.inshi_zei_prc,0))      AS sum
        ,bkm.oya_bumon_cd
    FROM ryosyusho_history_uc rh
    INNER JOIN uriage_denpyo sd
    ON  sd.uri_den_no    = rh.uri_den_no
    AND sd.delete_flg    = 0
    LEFT JOIN bumon_kaso_mst bkm
    ON bkm.ko_bumon_cd   = sd.bumon_cd
    AND bkm.delete_flg   = 0
    LEFT JOIN code_nm_mst ryosyu1
    ON ryosyu1.code_kbn      = '8542'
    AND ryosyu1.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu1.delete_flg   = 0
    LEFT JOIN code_nm_mst ryosyu2
    ON ryosyu2.code_kbn      = '8551'
    AND ryosyu2.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu2.delete_flg   = 0
    WHERE rh.delete_flg = 0
    AND rh.hako_kbn    <> 9
    AND COALESCE(rh.inshi_zei_prc,0) <> 0
    GROUP BY 
         rh.ryosyu_syonin_no
        ,rh.inshi_zei_prc
        ,ryosyu1.kbn_value_lnm
        ,ryosyu1.kbn_value_snm
        ,ryosyu1.biko
        ,ryosyu2.kbn_value_lnm
        ,ryosyu2.kbn_value_snm
        ,ryosyu2.biko
        ,bkm.oya_bumon_cd
        ,rh.hako_date
        ,sd.data_kbn
) T
WHERE $whereStr
GROUP BY
     T.ryosyu_nm
    ,T.ryosyu_syonin_ymd
    ,T.ryosyu_syonin_no
    ,T.inshi_zei_prc
 $orderBy
 $tailClause
END_OF_SQL
        , $param );
        return $select;
    }
    /**
     * データ 取得
     *
     * <AUTHOR> Sugiyama
     * @since   2020/12/xx
     * @param   Msi_Sys_Db $db
     * @param   array      $keyHash  条件
     * @return  array      該当データがない場合はarray()を返す
     */
    public static function findCsv($db, $keyHash=array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.hako_ymd, T.ryosyu_no, T.hako_kbn ASC ';
        }
        $select = $db->easySelect( <<< END_OF_SQL
SELECT 
    *
FROM (
    -- 領収証発行
    SELECT 
         TO_CHAR(rh.hako_date, 'YYYY/MM/DD')        AS hako_ymd
        ,CASE sd.data_kbn 
            WHEN 20 THEN ryosyu1.kbn_value_lnm 
                    ELSE ryosyu2.kbn_value_lnm 
         END                                        AS ryosyu_nm
        ,CONCAT(rh.ryosyu_no,'-',rh.ryosyu_no_sub)  AS ryosyu_no
        ,rh.atena
        ,rh.tadashikaki
        ,COALESCE(rh.gokei_prc,0)                   AS gokei_prc
        ,COALESCE(rh.inshi_zei_prc,0)               AS inshi_zei_prc
        ,jimu.tanto_nm                              AS jimu_tanto_nm
        ,1                                          AS hako_kbn
        ,rh.biko
        ,bkm.oya_bumon_cd
    FROM ryosyusho_history rh
    LEFT JOIN seikyu_denpyo sd
    ON  sd.seikyu_den_no     = rh.uri_den_no
    AND sd.delete_flg        = 0
    LEFT JOIN bumon_kaso_mst bkm
    ON bkm.ko_bumon_cd       = sd.bumon_cd
    AND bkm.delete_flg       = 0
    LEFT JOIN code_nm_mst ryosyu1
    ON ryosyu1.code_kbn      = '8542'
    AND ryosyu1.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu1.delete_flg   = 0
    LEFT JOIN code_nm_mst ryosyu2
    ON ryosyu2.code_kbn      = '8551'
    AND ryosyu2.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu2.delete_flg   = 0
    LEFT JOIN tanto_mst jimu
    ON  jimu.tanto_cd        = rh.jimu_tanto_cd
    AND jimu.delete_flg      = 0
    WHERE rh.delete_flg      = 0

    UNION ALL
    -- 領収証破棄
    SELECT 
         TO_CHAR(rh.hako_date, 'YYYY/MM/DD')        AS hako_ymd
        ,CASE sd.data_kbn 
            WHEN 20 THEN ryosyu1.kbn_value_lnm 
                    ELSE ryosyu2.kbn_value_lnm 
         END                                        AS ryosyu_nm
        ,CONCAT(rh.ryosyu_no,'-',rh.ryosyu_no_sub)  AS ryosyu_no
        ,rh.atena
        ,rh.tadashikaki
        ,COALESCE(rh.gokei_prc,0)                   AS gokei_prc
        ,COALESCE(rh.inshi_zei_prc,0) * -1          AS inshi_zei_prc
        ,jimu.tanto_nm                              AS jimu_tanto_nm
        ,rh.hako_kbn
        ,TRIM(CONCAT(rh.biko2,' ',rh.biko))         AS biko
        ,bkm.oya_bumon_cd
    FROM ryosyusho_history rh
    LEFT JOIN seikyu_denpyo sd
    ON  sd.seikyu_den_no     = rh.uri_den_no
    AND sd.delete_flg        = 0
    LEFT JOIN bumon_kaso_mst bkm
    ON bkm.ko_bumon_cd       = sd.bumon_cd
    AND bkm.delete_flg       = 0
    LEFT JOIN code_nm_mst ryosyu1
    ON ryosyu1.code_kbn      = '8542'
    AND ryosyu1.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu1.delete_flg   = 0
    LEFT JOIN code_nm_mst ryosyu2
    ON ryosyu2.code_kbn      = '8551'
    AND ryosyu2.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu2.delete_flg   = 0
    LEFT JOIN tanto_mst jimu
    ON  jimu.tanto_cd        = rh.jimu_tanto_cd
    AND jimu.delete_flg      = 0
    WHERE rh.delete_flg      = 0
    AND rh.biko2 IS NOT NULL    -- 破棄備考
	
    UNION ALL
    -- 領収証発行（内金）
    SELECT 
         TO_CHAR(rh.hako_date, 'YYYY/MM/DD')        AS hako_ymd
        ,CASE ud.data_kbn 
            WHEN 20 THEN ryosyu1.kbn_value_lnm 
                    ELSE ryosyu2.kbn_value_lnm 
         END                                        AS ryosyu_nm
        ,CONCAT(rh.ryosyu_no,'-',rh.ryosyu_no_sub)  AS ryosyu_no
        ,rh.atena
        ,rh.tadashikaki
        ,COALESCE(rh.gokei_prc,0)                   AS gokei_prc
        ,COALESCE(rh.inshi_zei_prc,0)               AS inshi_zei_prc
        ,jimu.tanto_nm                              AS jimu_tanto_nm
        ,1                                          AS hako_kbn
        ,rh.biko
        ,bkm.oya_bumon_cd
    FROM ryosyusho_history_uc rh
    LEFT JOIN uriage_denpyo ud
    ON  ud.uri_den_no        = rh.uri_den_no
    AND ud.delete_flg        = 0
    LEFT JOIN bumon_kaso_mst bkm
    ON bkm.ko_bumon_cd       = ud.bumon_cd
    AND bkm.delete_flg       = 0
    LEFT JOIN code_nm_mst ryosyu1
    ON ryosyu1.code_kbn      = '8542'
    AND ryosyu1.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu1.delete_flg   = 0
    LEFT JOIN code_nm_mst ryosyu2
    ON ryosyu2.code_kbn      = '8551'
    AND ryosyu2.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu2.delete_flg   = 0
    LEFT JOIN tanto_mst jimu
    ON  jimu.tanto_cd        = rh.jimu_tanto_cd
    AND jimu.delete_flg      = 0
    WHERE rh.delete_flg      = 0

    UNION ALL
    -- 領収証破棄用（内金） 
    SELECT 
         TO_CHAR(rh.hako_date, 'YYYY/MM/DD')        AS hako_ymd
        ,CASE ud.data_kbn 
            WHEN 20 THEN ryosyu1.kbn_value_lnm 
                    ELSE ryosyu2.kbn_value_lnm 
         END                                        AS ryosyu_nm
        ,CONCAT(rh.ryosyu_no,'-',rh.ryosyu_no_sub)  AS ryosyu_no
        ,rh.atena
        ,rh.tadashikaki
        ,COALESCE(rh.gokei_prc,0)                   AS gokei_prc
        ,COALESCE(rh.inshi_zei_prc,0) * -1          AS inshi_zei_prc
        ,jimu.tanto_nm                              AS jimu_tanto_nm
        ,rh.hako_kbn
        ,TRIM(CONCAT(rh.biko2,' ',rh.biko))         AS biko
        ,bkm.oya_bumon_cd
    FROM ryosyusho_history_uc rh
    LEFT JOIN uriage_denpyo ud
    ON  ud.uri_den_no        = rh.uri_den_no
    AND ud.delete_flg        = 0
    LEFT JOIN bumon_kaso_mst bkm
    ON bkm.ko_bumon_cd       = ud.bumon_cd
    AND bkm.delete_flg       = 0
    LEFT JOIN code_nm_mst ryosyu1
    ON ryosyu1.code_kbn      = '8542'
    AND ryosyu1.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu1.delete_flg   = 0
    LEFT JOIN code_nm_mst ryosyu2
    ON ryosyu2.code_kbn      = '8551'
    AND ryosyu2.kbn_value_cd = bkm.oya_bumon_cd
    AND ryosyu2.delete_flg   = 0
    LEFT JOIN tanto_mst jimu
    ON  jimu.tanto_cd        = rh.jimu_tanto_cd
    AND jimu.delete_flg      = 0
    WHERE rh.delete_flg      = 0
    AND rh.biko2 IS NOT NULL    -- 破棄備考
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
        , $param );
        return $select;
    }
}
