<?php

/**
 * PDF 入金消込リスト
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Mogi
 * @since      2021/01/xx
 * @version    2022/09/05 Kobayashi セレモア用にコピー
 * @filesource 
 */

/**
 * PDF 入金消込リスト
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Mogi
 * @since      2021/01/xx
 */
class Saiken_Pdf1204Controller extends Zend_Controller_Action {

    private static $title = '入金消込リスト';
    private static $sourceFileName = 'pdf_tmpl/1204.pdf';
    private static $row_height = 12.72;
    private static $row_count = 50;
    private static $top_title = array(0 => 'マッチングリスト', 1 => 'アンマッチリスト');
    private static $keshi_kbn = array(0 => '消込区分', 1 => '');

    /**
     * アクション
     *
     * <AUTHOR> Mogi
     * @since      2021/01/xx
     */
    public function indexAction() {
        $db = Msi_Sys_DbManager::getMyDb();
        $params = Msi_Sys_Utils::webInputs();
        $dataAppAry = Msi_Sys_Utils::json_decode($params['dataAppJson']);
        // 出力
        $pdfObj = new App_Pdf(self::$title, 'A4', 'P');
        $ret = $this->outData($pdfObj, $db, $dataAppAry);
        if ($ret != App_PdfKanriLib::STATUS_OK) {
            App_PdfKanriLib::err($ret);
            return;
        }
        $pdfObj->download();
    }

    /**
     * PDF出力メイン
     *
     * <AUTHOR> Mogi
     * @since      2021/01/xx
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db	データベース
     * @param array $dataAppAry	
     * @return	   viod
     */
    private function outData($pdfObj, $db, $dataAppAry) {
        $pdfObj->set_default_font_size(9);
        //マイナス値は赤字出力を設定
        $pdfObj->set_default_minus_font_color('red');

        // 条件
        $cond = array();
        // 会社
        if (array_key_exists('kaisya_cd', $dataAppAry) && $dataAppAry['kaisya_cd'] != '') {
            if ($dataAppAry['kaisya_cd'] != '00001') { // 00001：全社の場合は絞り込みの条件から除外
                $cond['kaisya_cd'] = $dataAppAry['kaisya_cd'];
            }
        }
        // 取込日
        if (array_key_exists('torikomi_ymd', $dataAppAry) && $dataAppAry['torikomi_ymd'] != '') {
            $cond['upload_date'] = $dataAppAry['torikomi_ymd'];
        }
        // 取込履歴番号
        if (array_key_exists('history_no', $dataAppAry) && Msi_Sys_Utils::myCount($dataAppAry['history_no']) > 0) {
            $cond['history_no'] = DataMapper_Utils::condOneOf('history_no', $dataAppAry['history_no']);
        }
        // 消込区分（0:未消化　1:消込済み　9:削除）
        $cond['payment_kbn'] = DataMapper_Utils::condOneOf('payment_kbn', array('1', '9'));

        // 部門コード順
        $cond['__etc_orderby'] = array('bumon_cd','kanjyo_date','seikyu_no'); // 部門→入金日→請求No

        $rec = DataMapper_Saiken_Pdf1204::find($db, $cond); // マッチングリスト データ取得

        unset($cond['payment_kbn']);
        unset($cond['upload_date']);
        unset($cond['history_no']);
        $cond['payment_kbn'] = '0';
        $rec2 = DataMapper_Saiken_Pdf1204::find($db, $cond); // アンマッチリスト データ取得
        // マッチングリスト・アンマッチリストのどちらもデータなしの場合はreturn
        if (count($rec) == 0 && count($rec2) === 0) {
            return App_PdfKanriLib::STATUS_NODATA;
        }
        // 会社名を取得
        $bumon_lnm = DataMapper_BumonMst::getBumonlnm($db, $dataAppAry['kaisya_cd']);
        // マッチングリストの出力
        if (count($rec) > 0) {
            $this->outDataMsi($pdfObj, $rec, 0, $bumon_lnm);
        }
        // アンマッチリストの出力
        if (count($rec2)) {
            $this->outDataMsi($pdfObj, $rec2, 1, $bumon_lnm);
        }

        if ($pdfObj->getNumPages() > 0) {
            $pdfObj->setPage(1);
        } else {
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName);
        }

        // 確認用のグリッドを出力
//        $pdfObj->test_line_out(850, 600);
        $print_ymd = date("Y年m月d日 G時i分");
        // ヘッダー出力
        $pdfObj->header_out(array(
            array('x' => 453, 'y' => 41, 'width' => 120, 'height' => 15, 'value' => $print_ymd, 'font_size' => 9), // 印刷日時
            array('x' => 5, 'y' => 810, 'width' => 600, 'height' => 15, 'type' => 'page', 'align' => 'C', 'font_size' => 9), // ページ
        ));
    }

    /**
     * カラム位置の設定
     *
     * <AUTHOR> Mogi
     * @since      2021/01/xx
     * @return	array	$set_ary	設定配列
     */
    private function getSetAry() {
        static $row_top = 94;

        $set_ary[] = array('x' => 30, 'y' => $row_top, 'width' => 45, 'height' => 11, 'align' => 'C'); // 施行番号・請求番号
        $set_ary[] = array('x' => 75, 'y' => $row_top, 'width' => 55, 'height' => 11, 'align' => 'C'); // 入金・売上計上日
        $set_ary[] = array('x' => 130, 'y' => $row_top, 'width' => 45, 'height' => 11, 'align' => 'C'); // 口座番号
        $set_ary[] = array('x' => 175, 'y' => $row_top, 'width' => 80, 'height' => 11, 'align' => 'L'); // 入金先・請求先名カナ
        $set_ary[] = array('x' => 255, 'y' => $row_top, 'width' => 80, 'height' => 11, 'align' => 'L'); // 請求先名
        $set_ary[] = array('x' => 335, 'y' => $row_top, 'width' => 60, 'height' => 11, 'align' => 'C'); // 電話番号
        $set_ary[] = array('x' => 395, 'y' => $row_top, 'width' => 55, 'height' => 11, 'type' => 'num'); // 入金・請求額
        $set_ary[] = array('x' => 450, 'y' => $row_top, 'width' => 50, 'height' => 11, 'align' => 'C'); // 入金方法
        $set_ary[] = array('x' => 500, 'y' => $row_top, 'width' => 50, 'height' => 11, 'align' => 'C'); // 消込区分
        $set_ary[] = array('x' => 500, 'y' => $row_top, 'width' => 50, 'height' => 11, 'type' => 'num'); // 差額
        $set_ary[] = array('x' => 175, 'y' => $row_top, 'width' => 50, 'height' => 11, 'type' => 'num'); // 合計件数
        return $set_ary;
    }

    /**
     * 入金消込リスト出力
     *
     * <AUTHOR> Mogi
     * @since      2021/01/xx
     * @param pdfObj $pdfObj PDFオブジェクト
     * @param array $rec レコード
     * @param string $kbn	0:マッチングリスト　1:アンマッチリスト
     * @param string $bumon_lnm 部門名
     * @return	   viod
     */
    private function outDataMsi($pdfObj, $rec, $kbn, $bumon_lnm) {
        $row_ary = array();
        $title = '';
        $bumon_cd = null;
        $count = 0; // 件数カウント
        $nyukin_total = 0; // 入金合計
        $sagaku_total = 0; // 差額計

        foreach ($rec as $row) {
            if(isset($bumon_cd) && $bumon_cd !== $row['bumon_cd']){ // 部門毎に改ページ
                if($count >= 1){
                    // 総合計出力
                    $row_ary[] = $this->setPdfArray(
                            null // 施行番号・請求番号
                            , null // 入金・請求日
                            , '　件数：' // 口座番号
                            , null // 入金先・請求先名カナ
                            , null // 請求先名
                            , '入金合計：' // 電話番号
                            , $nyukin_total // 入金・請求額
                            , '　差額計：' // 入金方法
                            , null // 消込区分
                            , $sagaku_total // 差額
                            , $count // 合計件数
                    );
                    // 最終レコード
                    $head_ary = array(
                        array('x' => 150, 'y' => 40, 'width' => 300, 'height' => 15, 'value' => $title, 'font_size' => 13, 'align' => 'C'), //タイトル
                        array('x' => 500, 'y' => 70, 'width' => 50, 'height' => 11, 'value' => self::$keshi_kbn[$kbn], 'font_size' => 8, 'align' => 'C'), //消込区分
                    );
                    $pdfObj->write_table($this->getSetAry(), self::$row_height, $row_ary, self::$row_count, __DIR__ . '/' . self::$sourceFileName, 1, $head_ary);
                }
                $row_ary = array();
                $count = 0; // 件数カウント
                $nyukin_total = 0; // 入金合計
                $sagaku_total = 0; // 差額計
            }
            ++$count;
            $nyukin_total += $row['nyukin_prc']; // 入金合計
            $sagaku_total += $row['sagaku']; // 差額計
            // 明細を出力(上段)
            $row_ary[] = $this->setPdfArray(
                    $row['seko_no'] // 施行番号・請求番号
                    , $row['kanjyo_date'] // 入金・請求日
                    , $row['kouza_no'] // 口座番号
                    , $row['furikomi_nm'] // 入金先・請求先名カナ
                    , null // 請求先名
                    , null // 電話番号
                    , $row['nyukin_prc'] // 入金・請求額
                    , $row['nyukin_kbn'] // 入金方法
                    , $row['keshikomi_kbn'] // 消込区分
                    , null // 差額
                    , null // 合計件数
            );
            // 明細を出力（下段）
            $row_ary[] = $this->setPdfArray(
                    $row['seikyu_no'] // 施行番号・請求番号
                    , $row['keijo_ymd'] // 入金・売上計上日
                    , $row['kouza_no2'] // 口座番号
                    , $row['sekyu_knm'] // 入金先・請求先名カナ
                    , $row['sekyu_nm'] // 請求先名
                    , $row['sekyu_tel'] // 電話番号
                    , $row['seikyu_prc'] // 入金・請求額
                    , $row['nyukin_kbn2'] // 入金方法
                    , null // 消込区分
                    , $row['sagaku'] // 差額
                    , null // 合計件数
            );
            $title = self::$top_title[$kbn] . '(' . $row['bumon_lnm'] . ')';
            $bumon_cd = $row['bumon_cd'];
        }

        if ($count >= 1) {
            // PDF下敷き修正時の座標確認用
//        for ($index = 0; $index < 60; $index++) {
//            $row_ary[] = $this->setPdfArray(
//                    '施行番号00000000'    // 施行番号・請求番号
//                    , '入金日00000000'    // 入金・請求日
//                    , '口座番号0000000000'    // 口座番号
//                    , '入金先あああああああああああああああああああ'  // 入金先・請求先名カナ
//                    , 'ｶﾅあああああああああああああああああああ'  // 請求先名
//                    , '電話番号00000'   // 電話番号
//                    , 999999999999999   // 入金・請求額
//                    , '入金方法あああああああああ'   // 入金方法
//                    , '消込区分あああああああああ'   // 消込区分
//                    , 999999999999999   // 差額
//                    , 999999999999999   // 差額
//            );
//            }
            // 総合計出力
            $row_ary[] = $this->setPdfArray(
                    null // 施行番号・請求番号
                    , null // 入金・請求日
                    , '　件数：' // 口座番号
                    , null // 入金先・請求先名カナ
                    , null // 請求先名
                    , '入金合計：' // 電話番号
                    , $nyukin_total // 入金・請求額
                    , '　差額計：' // 入金方法
                    , null // 消込区分
                    , $sagaku_total // 差額
                    , $count // 合計件数
            );
            $title = self::$top_title[$kbn] . '(' . $row['bumon_lnm'] . ')';
            // 最終レコード
            $head_ary = array(
                array('x' => 150, 'y' => 40, 'width' => 300, 'height' => 15, 'value' => $title, 'font_size' => 13, 'align' => 'C'), //タイトル
                array('x' => 500, 'y' => 70, 'width' => 50, 'height' => 11, 'value' => self::$keshi_kbn[$kbn], 'font_size' => 8, 'align' => 'C'), //消込区分
            );
            $pdfObj->write_table($this->getSetAry(), self::$row_height, $row_ary, self::$row_count, __DIR__ . '/' . self::$sourceFileName, 1, $head_ary);
        }
    }

    /**
     * ＰＤＦ出力処理
     *
     * <AUTHOR> Mogi
     * @since      2021/01/xx
     * @return	   array
     */
    private function setPdfArray(
    $seko_no // 施行番号・請求番号
    , $nyukin_ymd // 入金・請求日
    , $kouza_no // 口座番号
    , $nyukin_nm // 入金先・請求先名カナ
    , $nyukin_knm // 請求先名
    , $tel // 電話番号
    , $nyukin_prc // 入金・請求額
    , $payment_kbn // 入金方法
    , $keshi_kbn // 消込区分
    , $sagaku // 差額
    , $total_cnt // 合計件数
    ) {
        $row_ary = array(
            $seko_no // 施行番号・請求番号
            , $nyukin_ymd // 入金・請求日
            , $kouza_no // 口座番号
            , $nyukin_nm // 入金先・請求先名カナ
            , $nyukin_knm // 請求先名
            , $tel // 電話番号
            , $nyukin_prc // 入金・請求額
            , $payment_kbn // 入金方法
            , $keshi_kbn // 消込区分
            , $sagaku // 差額
            , $total_cnt // 合計件数
        );
        return $row_ary;
    }

}
