<?php

/**
 * DataMapper_SekyuExData
 *
 * 請求情報 データマッパークラス
 *
 * @deprecated
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mihara
 * @since      2014/xx/xx
 * @filesource 
 */

/**
 * 請求情報 データマッパークラス
 * 
 * @deprecated
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mihara
 * @since      2014/xx/xx
 */
class DataMapper_SekyuExData extends DataMapper_Abstract {

    /**
     * 請求情報を検索する
     * cf. DataMapper_UriageDenpyo
     *
     * <AUTHOR> Mihara
     * @since 2014/xx/xx
     * @version 2014/07/26 消費税差額を加算するように修正　Kayo
     * @version 2015/03/03 入金状況のステータスを修正　Kayo
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash = array(), $isDateEffective = false) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_no ';
        }

        if ($isDateEffective) {
            $dateWhere1 = ''; // " AND ( m.tekiyo_ed_date IS NULL OR m.tekiyo_ed_date >= CURRENT_DATE ) ";
            $dateWhere2 = ''; // " AND tsm.tekiyo_st_date <= CURRENT_DATE AND tsm.tekiyo_ed_date >= CURRENT_DATE ";
            $dateWhere3 = " AND b.tekiyo_st_date <= CURRENT_DATE AND b.tekiyo_ed_date >= CURRENT_DATE ";
        } else {
            $dateWhere1 = '';
            $dateWhere2 = '';
            $dateWhere3 = '';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
SELECT
		 u.uri_den_no AS seikyu_no	-- 売上伝票№
	   	,u.denpyo_no				-- 受注伝票№
        ,u.kaisya_cd                -- 会社コード   2017/03/07 ADD Kayo        
	   	,TO_CHAR(u.juchu_ymd,'YYYY/MM/DD') AS	juchu_ymd		-- 売上日
	   	,u.data_kbn					-- データ区分
--       ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd_num=u.data_kbn AND code_kbn='0920'
--         AND delete_flg=0 limit 1) as data_kbn_nm -- データ区分名
        ,CASE u.data_kbn WHEN 1 THEN '葬儀'
                         WHEN 2 THEN '法事'
                         WHEN 3 THEN '一般受注'
                         WHEN 4 THEN '当社葬家受注'
                         WHEN 5 THEN '事前相談'
                         WHEN 6 THEN 'その他施行'
                         ELSE '?' END AS data_kbn_nm -- データ区分名
	   	,u.seko_no					-- 施行番号
	   	,u.seko_no_sub				-- 施行番号（枝番）
	   	,u.bumon_cd					-- 売上部門コード
		,b.bumon_lnm				-- 売上部門名
		,b.bumon_lnm AS bumon_nm
		,b.bumon_snm				-- 売上部門名（簡略）
	   	,u.tanto_cd					-- 担当者コード
		,tt.tanto_nm				-- 担当者名		
	   	,u.gojokai_cose_cd			-- 互助会コースコード
	   	,u.seko_plan_cd				-- 施行プランコード
	   	,TO_CHAR(u.seikyu_ymd,'YYYY/MM/DD')		AS	seikyu_ymd		-- 請求日
	   	,TO_CHAR(u.zen_seikyu_ymd,'YYYY/MM/DD') AS 	zen_seikyu_ymd	-- 前回請求日
	   	,u.sekkyu_kaisu				-- 請求回数
        ,CASE 
	    WHEN u.pay_method_cd IN ('4', '5', '6') THEN 
		pay.kbn_value_lnm
	    ELSE
		CASE 
		    WHEN u.sekkyu_kaisu > 0 THEN '発行済み'
		    ELSE '未発行' 
		END
	 END AS issue_st_nm -- 請求状況
        ,CASE 
	    WHEN u.pay_method_cd IN ('4', '5', '6') THEN 
		'－'
	    ELSE
		CASE 
		    WHEN u.seikyu_zan = 0 THEN '入金済み'                         -- 2015/03/03 UPD Kayo
		    WHEN u.nyukin_prc - u.sougi_keiyaku_prc > 0  THEN '一部入金'  -- 2015/03/03 UPD Kayo
		    ELSE '未入金' 
		END
	 END AS nyukin_st_nm                          -- 入金状況
        ,CASE WHEN u.shonin_dt1 IS NULL THEN '未承認'
              WHEN u.shonin_dt2 IS NULL THEN '事務承認'
                                        ELSE '上長承認' END AS shonin_st_nm  -- 承認区分
--        ,ARRAY_TO_STRING(ARRAY(SELECT nafuda_nm FROM uriage_denpyo_msi udm
--                               WHERE udm.uri_den_no=u.uri_den_no AND udm.delete_flg=0
--                               ORDER BY disp_no, msi_no), '、')           AS nafuda -- 名札
       ,case u.data_kbn																	--2017/07/21	Okuyama	修正
        	when 3 then																		--2017/07/21	Okuyama	修正
				den_juchu.v_free2															--2017/07/21	Okuyama	修正
			else																			--2017/07/21	Okuyama	修正
        		ARRAY_TO_STRING(ARRAY(SELECT nafuda_nm FROM uriage_denpyo_msi udm			--2017/07/21	Okuyama	修正
                               WHERE udm.uri_den_no=u.uri_den_no AND udm.delete_flg=0		--2017/07/21	Okuyama	修正
                               ORDER BY disp_no, msi_no), '、')								--2017/07/21	Okuyama	修正
		END    AS nafuda -- 名札

	   	,u.kaishu_kbn				-- 回収区分
	   	,TO_CHAR(u.kaishu_ymd,'YYYY/MM/DD')		AS	kaishu_ymd		-- 回収予定日
	   	,u.nyukin_prc				-- 入金金額
	   	,u.seikyu_zan				-- 請求残高
	   	,u.sekyu_cd					-- 請求先コード
	   	,u.sekyu_nm					-- 請求先名
	   	,u.sekyu_knm				-- 請求先名カナ
	   	,u.sekyu_soufu_nm			-- 請求書送付先名
	   	,u.sekyu_yubin_no			-- 請求先郵便番号
	   	,COALESCE(u.sekyu_addr1,'') || COALESCE(u.sekyu_addr2,'')	AS seikyu_addr	-- 請求先住所
	   	,u.sekyu_tel				-- 請求先電話番号
	   	,u.sekyu_fax				-- 請求先FAX
	   	,u.uri_prc_sum				-- 売上金額合計
	   	,u.genka_prc_sum			-- 原価金額合計
	   	,u.uri_hepn_sum				-- 売上返品合計
	   	,u.uri_nebk_sum				-- 売上値引合計
	   	,u.hoshi_prc_sum			-- 奉仕料合計
                ,u.uri_prc_sum 
			+ u.uri_hepn_sum 
			+ u.uri_nebk_sum 
			+ u.hoshi_prc_sum 
			+ u.sougi_zei_sagaku_prc
                        + u.sougi_early_use_cost
			+ u.sougi_keiyaku_zei	-- 葬儀契約消費税	2016/03/26 ADD Kayo
			+ u.etc_keiyaku_zei		-- 壇払等の契約消費税 	2016/03/26 ADD Kayo
            + u.etc_early_use_cost    AS seikyu_prc	-- 請求金額		
		,u.szei_katax_taisho_prc	-- 外税課税対象額
	   	,u.uzei_katax_taisho_prc	-- 内税課税対象額
	   	,u.hitax_katax_taisho_prc	-- 非税課税対象額
	   	,u.tax_code_kbn				-- 税区分コード区分
	   	,u.tax_cd					-- 税区分コード
	   	,u.tax_kbn					-- 税区分
	   	,u.zei_cd					-- 消費税コード
	   	,u.out_zei_prc				-- 外税消費税額
	   	,u.in_zei_prc				-- 内税消費税額
		,u.out_zei_prc + u.in_zei_prc		AS	zei_prc	-- 消費税額 		
                ,d.uri_prc_sum
                + d.uri_nebk_sum	  
                + d.uri_hepn_sum
                + d.hoshi_prc_sum
                + d.out_zei_prc 
                + d.sougi_keiyaku_prc + d.sougi_harai_prc
                + d.sougi_keiyaku_zei
                + d.sougi_wari_prc
                + d.sougi_premium_service_prc
                + d.sougi_meigi_chg_cost + d.sougi_meigi_chg_cost_zei
                + d.sougi_early_use_cost + d.sougi_early_use_cost_zei
                + d.etc_harai_prc
                AS	seikyu_zei_prc	-- 請求金額（消費税込み） 		
		,u.arari_prc				-- 粗利益額
	   	,u.denpyo_biko1				-- 伝票備考１
	   	,u.denpyo_biko2				-- 伝票備考２
	   	,u.shonin_dt1				-- 承認１日時
	   	,u.shonin_dt2				-- 承認２日時
	   	,u.delete_flg				-- 削除フラグ
        ,u.status_kbn               -- ステータス
        ,CASE u.status_kbn WHEN 1 THEN '見積中'
                         WHEN 2 THEN '施行中'
                         WHEN 3 THEN '請求済'
                         WHEN 4 THEN '入金済'
                         WHEN 9 THEN '失注'
                         ELSE '-' END AS status_kbn_nm  -- ステータス表示名
--
       ,k.moushi_kbn       -- 申込区分
       ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd_num=k.moushi_kbn AND code_kbn='0010'
         AND delete_flg=0 limit 1) as moushi_kbn_nm -- 申込区分名
            ,k.sougi_cd         -- 葬儀区分
       ,(SELECT kbn_value_lnm FROM code_nm_mst WHERE kbn_value_cd=k.sougi_cd AND code_kbn='0020'
         AND delete_flg=0 limit 1) as sougi_cd_nm -- 葬儀区分名
            ,k.daicho_no_eria   -- 台帳番号（エリア）
            ,k.daicho_no_mm     -- 台帳番号（月）
            ,k.daicho_no_seq    -- 台帳番号（連番）
       ,(k.daicho_no_eria || '-' || k.daicho_no_mm || '-' || k.daicho_no_seq ) AS daicho_no -- 台帳番号
       ,k.kaiin_kbn        -- 会員区分
       ,cm3.kbn_value_lnm -- 会員区分名
       ,k.kaiin_sonota -- 会員区分（その他）
       ,k.uketuke_tanto_cd -- 受付担当者コード
       ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
       ,k.seko_tanto_cd    -- 施行担当者コード
       ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
       ,k.k_nm             -- 故人名
       ,k.m_nm       -- 喪主
       ,k.k_knm              -- 故人名カナ
       ,k.m_knm              -- 喪主名カナ
       ,k.souke_knm          -- 葬家名カナ
       ,CASE mg_kbn WHEN 0 THEN k.mg_yubin_no
                           ELSE k.kg_yubin_no END AS mg_yubin_no -- 喪主郵便番号
       ,CASE mg_kbn WHEN 0 THEN k.mg_addr1
                           ELSE k.kg_addr1 END AS mg_addr1   -- 喪主現住所1
       ,CASE mg_kbn WHEN 0 THEN k.mg_addr2
                           ELSE k.kg_addr2 END AS mg_addr2   -- 喪主現住所2
       ,CASE mg_kbn WHEN 0 THEN k.mg_tel
                           ELSE k.kg_tel END AS mg_tel       -- 喪主TEL
       ,TO_CHAR(k.sougi_ymd, 'YYYY/MM/DD') as sougi_ymd  -- 葬儀日
       ,TO_CHAR(n1.nitei_ymd, 'YYYY/MM/DD') as nakijitu -- 亡日
       ,n2.basho_cd -- 式場CD
       ,n2.basho_nm -- 式場名
       ,(SELECT juchu_prc_sum FROM juchu_denpyo  WHERE seko_no = k.seko_no
         AND delete_flg=0 limit 1) as juchu_prc_sum -- 受注金額合計
       ,(SELECT uri_prc_sum FROM uriage_denpyo  WHERE seko_no = k.seko_no
         AND delete_flg=0 limit 1) as uri_prc_sum -- 売上金額合計
       -- ,k.status_kbn -- ステータス
       -- ,CASE k.status_kbn WHEN 1 THEN '見積中'
       --                   WHEN 2 THEN '施行中'
       --                   WHEN 3 THEN '請求済み（完了）'
       --                   WHEN 9 THEN 'その他（失注）'
       --                   ELSE '-' END AS status_kbn_nm  -- ステータス表示名
       ,k.souke_nm         -- 葬家
       ,k.keishiki_kbn     -- 葬儀形式
       ,k.biko1            -- メモ（出棺経路・納骨・壇払など）
       -- ,k.bumon_cd         -- 売上部門コード
       -- ,bm.bumon_lnm  AS bumon_nm        -- 売上部門名
       -- ,bm.bumon_lnm       -- 売上正式部門名       
       -- ,bm.bumon_snm       -- 売上簡略部門名
       ,k.order_finish_flg AS order_flg -- 発注完了フラグ  0:未発注 1:発注済み
       ,CASE k.order_finish_flg WHEN 0 THEN '未発注'
                                WHEN 1 THEN '発注済み'
                                ELSE '-' END AS order_flg_nm  -- 発注完了表示名
--
       ,TO_CHAR(u.seikyu_print_date, 'YYYY/MM/DD HH24:MI:SS') AS  seikyu_print_date
       ,spt.tanto_nm                                          AS  seikyu_print_tanto_nm
       ,(SELECT max(nyukin_ymd) FROM nyukin_denpyo WHERE seikyu_no = u.uri_den_no AND delete_flg = 0) AS nyukin_ymd
       ,cnm2.kbn_value_lnm AS pay_method_cd_nm
  FROM uriage_denpyo u
  LEFT JOIN seko_kihon_info k
    ON k.delete_flg	    =   0
   AND k.seko_no		=	u.seko_no
  LEFT JOIN tanto_mst t1 
	ON k.uketuke_tanto_cd = t1.tanto_cd 
	AND t1.delete_flg = 0
  LEFT JOIN tanto_mst t2 
	ON k.seko_tanto_cd    = t2.tanto_cd 
	AND t2.delete_flg = 0
  LEFT JOIN nm_jyusho_mst jm 
	  ON k.kasoba_cd = jm.jyusho_cd   
	  AND jm.jyusho_kbn = 3 
	  AND jm.delete_flg = 0
LEFT JOIN code_nm_mst cnm2
    ON cnm2.kbn_value_cd_num=u.pay_method_cd::int
   AND cnm2.code_kbn='1130' -- 支払方法
   AND cnm2.delete_flg=0
  LEFT JOIN code_nm_mst cm3 
	ON cm3.kbn_value_cd = k.kaiin_cd 
	AND cm3.code_kbn='0030' 
	AND cm3.delete_flg=0
  LEFT JOIN code_nm_mst pay 
	ON  pay.code_kbn     = '1130' 
	AND pay.kbn_value_cd = u.pay_method_cd 
	AND pay.delete_flg   = 0
  LEFT JOIN seko_nitei n1 
	ON n1.seko_no=k.seko_no 
	AND n1.nitei_kbn=1 
	AND n1.delete_flg=0
  LEFT JOIN seko_nitei n2 
	ON n2.seko_no=k.seko_no 
	AND n2.nitei_kbn=7 
	AND n2.delete_flg=0
  LEFT JOIN bumon_mst bm 
	ON k.bumon_cd = bm.bumon_cd 
	AND bm.delete_flg=0
  LEFT JOIN tanto_mst tt
    ON tt.delete_flg	=	0
	AND u.tanto_cd		=	tt.tanto_cd   $dateWhere2
  LEFT JOIN bumon_mst b
    ON b.delete_flg		=	0
	AND u.bumon_cd		=	b.bumon_cd   $dateWhere3
LEFT JOIN tanto_mst spt
   ON	spt.delete_flg	=	0
   AND	u.seikyu_print_tanto_cd		=	spt.tanto_cd   $dateWhere2
LEFT JOIN juchu_denpyo den_juchu					--2017/07/21 Okuyama 追加
   ON	den_juchu.delete_flg	=	0				--2017/07/21 Okuyama 追加
   AND	u.denpyo_no		=	den_juchu.denpyo_no		--2017/07/21 Okuyama 追加
 WHERE u.delete_flg		=	0   $dateWhere1
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    
    /**
     * 請求情報を検索する(葬儀施行金額確定一覧)
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForSekokakutei($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT T.*
                ,T.sekyu_prc + T.bechu_prc AS sekyu_prc
                ,T.uchikin_prc + T.bechu_uchikin_prc AS uchikin_prc
                ,T.seikyu_zan + T.bechu_seikyu_zan AS seikyu_zan
            FROM (
                SELECT d.seko_no
                ,d.uri_den_no
                ,d.denpyo_no
                ,d.sekyu_cd
                ,d.data_kbn
                ,moushi.kbn_value_lnm AS moushi_nm
                ,d.sekyu_nm
                ,d.sekyu_knm
                ,d.sekyu_tel
                ,ski.souke_nm
                ,ski.souke_knm
                ,ski.k_nm
                ,ski.k_knm
                ,COALESCE(d.sekyu_addr1, '') || COALESCE(d.sekyu_addr2, '') AS sekyu_addr
                ,d.n_free1 AS tuya_sanretu
                ,d.n_free2 AS sogi_sanretu
                ,d.n_free3 AS uchi_tuya
                ,d.n_free4 AS uchi_sogi
                ,TO_CHAR(ski.sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd
                ,TO_CHAR(d.keijo_ymd, 'YYYY/MM/DD') AS keijo_ymd
                ,d.uri_prc_sum
                    + d.uri_nebk_sum	  
                    + d.uri_hepn_sum
                    + d.hoshi_prc_sum
                    + d.out_zei_prc 
                    + d.sougi_keiyaku_prc 
                    + d.sougi_harai_prc
                    + d.sougi_keiyaku_zei
                    + d.sougi_wari_prc
                    + d.sougi_wari_zei
                    + d.sougi_premium_service_prc
                    + d.sougi_meigi_chg_cost + d.sougi_meigi_chg_cost_zei
                    + d.sougi_early_use_cost + d.sougi_early_use_cost_zei
                    + d.sougi_tokuten_prc
                    + COALESCE(d.n_free9, 0) 
                    + COALESCE(d.n_free10, 0) 
                    + COALESCE(d.n_free5,0)
                AS  sekyu_prc
                ,d.seikyu_zan
                ,CASE WHEN d.seikyu_zan = 0 THEN 1
                ELSE 0 END AS nyukin_st
                ,d.uchikin_prc + d.cupon_prc AS uchikin_prc
                ,d.kouden_uchikin_prc
                ,bm.bumon_lnm AS bumon_nm
                ,stm.tanto_nm AS seko_tanto_nm
                ,kakutei.kbn_value_lnm AS kakutei_nm
                ,d.kaisya_cd
                ,ski.oya_bumon_cd
                ,d.bumon_cd
                ,ski.seko_tanto_cd
                ,d.seko_prc_kakute_kbn
                ,ski.moushi_kbn
                ,COALESCE((SELECT SUM(
                    d0.uri_prc_sum
                    + d0.uri_nebk_sum	  
                    + d0.uri_hepn_sum
                    + d0.hoshi_prc_sum
                    + d0.out_zei_prc 
                        ) 
                    FROM uriage_denpyo d0
                    WHERE d0.seko_no = d.seko_no
                        AND d0.data_kbn = 4
			AND d0.juchusaki_kbn = 1
			AND d0.delete_flg = 0), 0) AS bechu_prc
                ,COALESCE((SELECT SUM(d0.uchikin_prc + d0.cupon_prc) 
                    FROM uriage_denpyo d0
                    WHERE d0.seko_no = d.seko_no
                        AND d0.data_kbn = 4
			AND d0.juchusaki_kbn = 1
			AND d0.nyukin_prc = 0
			AND d0.delete_flg = 0), 0) AS bechu_uchikin_prc
                ,COALESCE((SELECT SUM(d0.seikyu_zan) 
                    FROM uriage_denpyo d0
                    WHERE d0.seko_no = d.seko_no
                        AND d0.data_kbn = 4
			AND d0.juchusaki_kbn = 1
			AND d0.delete_flg = 0), 0) AS bechu_seikyu_zan 
                ,(SELECT TO_CHAR(MAX(nyukin.nyukin_ymd), 'YYYY/MM/DD') AS nyukin_ymd 
                    FROM uriage_denpyo d0
                    INNER JOIN nyukin_denpyo nyukin
                        ON nyukin.uri_den_no = d0.uri_den_no
                        AND nyukin.seko_no = d0.seko_no
                        AND nyukin.data_kbn = d0.data_kbn
                        AND nyukin.seikyu_no = '0000000000' -- 内金はオール0
                        AND nyukin.delete_flg = 0
                    WHERE d0.uri_den_no = d.uri_den_no 
                        AND d0.delete_flg = 0)  AS nyukin_ymd
                ,ski.kaiin_kbn
                ,sn4.v_free1 AS tuya_check
                ,skaf.tanto_cd1
                FROM uriage_denpyo d
                LEFT JOIN seko_kihon_info ski
                    ON ski.seko_no = d.seko_no
                    AND ski.delete_flg = 0
                LEFT JOIN seko_kihon_all_free skaf
                    ON skaf.seko_no = ski.seko_no
                    AND skaf.delete_flg = 0
                LEFT JOIN code_nm_mst moushi
                    ON moushi.code_kbn = '8562'
                    AND moushi.kbn_value_cd = ski.moushi_cd
                    AND moushi.delete_flg = 0
                LEFT JOIN code_nm_mst kakutei
                    ON kakutei.code_kbn = '7989'
                    AND kakutei.kbn_value_cd_num = d.seko_prc_kakute_kbn
                    AND kakutei.delete_flg = 0
                LEFT JOIN bumon_mst bm
                    ON bm.bumon_cd = d.bumon_cd
                    AND bm.delete_flg = 0
                LEFT JOIN tanto_mst stm
                    ON stm.tanto_cd = ski.seko_tanto_cd
                    AND stm.delete_flg = 0
                LEFT JOIN seko_nitei sn4
                    ON sn4.nitei_kbn = 4
                    AND sn4.seko_no = ski.seko_no
                    AND sn4.delete_flg = 0
                WHERE d.delete_flg = 0
                    AND d.data_kbn IN (1,2)
            ) AS T
            WHERE 
            $whereStr
            $orderBy
            $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    
    /**
     * 請求情報を検索する(請求書承認一覧)
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForSeikyulist($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seikyu_den_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT T.*
                ,COALESCE(COALESCE(T.sekyu_nyukin_ymd, T.uchi_nyukin_ymd), T.d_free2) AS nyukin_ymd
            FROM (
                SELECT d.seikyu_den_no
                    ,bungas.kbn_value_lnm AS bun_gas_st
                    ,ud.uri_den_no 
                    ,ud.denpyo_no AS juchu_denpyo_no
                    ,moushi.kbn_value_lnm AS moushi_nm
                    ,nyukin.kbn_value_lnm AS nyukin_st
                    ,TO_CHAR(d.kaishu_ymd, 'YYYY/MM/DD') AS kaishu_ymd
                    ,TO_CHAR(d.keijo_ymd, 'YYYY/MM/DD') AS keijo_ymd
                    ,CASE WHEN sssi.free_kbn1 = 1 THEN sssi.sekyu_nm
                        ELSE COALESCE(sssi.sekyu_nm1,'') || COALESCE(sssi.sekyu_nm2,'') END AS sekyu_nm
                    ,CASE WHEN sssi.free_kbn1 = 1 THEN sssi.sekyu_nm
                        ELSE COALESCE(sssi.sekyu_knm1,'') || COALESCE(sssi.sekyu_knm2,'') END AS sekyu_knm
                    ,d.sekyu_tel
                    ,ski.souke_nm
                    ,ski.souke_knm
                    ,ski.k_nm
                    ,ski.k_knm
                    ,COALESCE(sssi.soufu_addr1, '') || COALESCE(sssi.soufu_addr2, '') AS sekyu_addr
                    ,d.uri_prc_sum
                    + d.uri_nebk_sum	  
                    + d.uri_hepn_sum
                    + d.hoshi_prc_sum
                    + d.out_zei_prc 
                    + d.sougi_keiyaku_prc + d.sougi_harai_prc
                    + d.sougi_keiyaku_zei
                    + d.sougi_wari_prc + d.sougi_wari_zei
                    + d.sougi_premium_service_prc
                    + d.sougi_meigi_chg_cost + d.sougi_meigi_chg_cost_zei
                    + d.sougi_early_use_cost + d.sougi_early_use_cost_zei
                    + d.sougi_tokuten_prc
                    + COALESCE(d.n_free9, 0) 
                    + COALESCE(d.n_free10, 0) 
                    + COALESCE(d.n_free5, 0) 
                    AS  sekyu_prc
                    ,d.seikyu_zan
                    ,d.nyukin_prc + d.uchikin_prc + d.kouden_uchikin_prc + d.cupon_prc AS nyukin_prc
                    ,d.nyukin_prc + d.cupon_prc AS org_nyukin_prc
                    ,bm.bumon_lnm AS bumon_nm
                    ,CASE WHEN d.data_kbn IN (1,2,4) THEN ski.seko_tanto_cd 
                        ELSE d.tanto_cd END AS seko_tanto_cd
                    ,sekyu.kbn_value_lnm AS sekyu_st
                    ,CASE WHEN d.data_kbn IN (1,2,4) THEN stm.tanto_nm 
                        ELSE tm.tanto_nm END AS seko_tanto
                    ,sekyu.kbn_value_lnm AS sekyu_st
                    ,TO_CHAR(COALESCE(d.seikyu_ymd, COALESCE(ski.sougi_ymd, d.nonyu_dt)), 'YYYY/MM/DD') AS seikyu_ymd
                    ,TO_CHAR(d.seikyu_print_date, 'YYYY/MM/DD') AS seikyu_print_date
                    ,ptm.tanto_nm AS seikyu_print_tanto
                    ,COALESCE(ski.oya_bumon_cd, bkm.oya_bumon_cd) AS oya_bumon_cd
                    ,d.bumon_cd
                    ,CASE WHEN d.data_kbn = 3 THEN d.ref_seko_no 
                        ELSE d.seko_no END AS seko_no
                    ,CASE WHEN d.data_kbn = 1 THEN ski.moushi_kbn
                        ELSE d.data_kbn END AS data_kbn
                    ,d.data_kbn AS denpyo_data_kbn
                    ,pay.kbn_value_lnm AS pay_method_nm
                    ,d.nyukin_status
                    ,d.seikyu_post_kbn
                    ,d.bun_gas_kbn_num
                    ,d.bun_gas_seikyu_den_no
                    ,d.sekkyu_kaisu
                    ,d.seikyu_approval_status
                    ,sssi.syorui_tenpu_kbn
                    ,CASE WHEN sssi.syorui_tenpu_kbn = 1 THEN '要添付'
                    ELSE NULL 
                    END AS nohinsho
                    ,sekyu_shonin.kbn_value_lnm AS shonin_st
                    ,d.juchusaki_kbn
                    ,d.k_free2 AS yuso_check
                    ,d.ryoshusho_syurui_kbn AS msi_check
                    ,TRIM(d.pay_method_cd) AS pay_method_cd
                    ,CASE WHEN d.bun_gas_seikyu_den_no IS NOT NULL
                        THEN (SELECT TO_CHAR(max(nd.nyukin_ymd), 'YYYY/MM/DD') 
                        FROM seikyu_denpyo sd 
                        INNER JOIN nyukin_denpyo nd
                        ON nd.uri_den_no = sd.uri_den_no
                        AND nd.nyu_kbn = 88
                        AND nd.delete_flg = 0
                        WHERE sd.seikyu_den_no = d.bun_gas_seikyu_den_no)
                    ELSE 
                        (SELECT TO_CHAR(max(nyukin_ymd), 'YYYY/MM/DD') 
                        FROM nyukin_denpyo 
                        WHERE uri_den_no = d.uri_den_no
                        AND nyu_kbn = 88
                        AND delete_flg = 0) 
                    END AS uchi_nyukin_ymd
                    ,(SELECT TO_CHAR(max(nyukin_ymd), 'YYYY/MM/DD') 
                        FROM nyukin_denpyo 
                        WHERE seikyu_no = d.seikyu_den_no
                        AND delete_flg = 0) AS sekyu_nyukin_ymd
                    ,'#286EA6' AS text_color
                    ,'white' AS back_color
                    ,d.bad_debt_loss_prc
                    ,ud.zaimu_rendo_denno
                    ,ud.zaimu_rendo_kbn
                    ,TO_CHAR(d.d_free1, 'YYYY/MM/DD') AS seikyu_hakko_ymd
                    ,cnm_9640.kbn_value_lnm AS format_nm
                    ,ud.denpyo_no
                    ,sssi.v_free1 AS kokyaku_no
                    ,d.d_import_kbn
                    ,TO_CHAR(d.d_free2, 'YYYY/MM/DD') AS d_free2
                    ,CASE WHEN d.data_kbn IN (1,2) THEN 2 
                    WHEN d.data_kbn = 4 THEN sssi.houjin_kbn
                    ELSE 1 END AS houjin_flg
                FROM seikyu_denpyo d
                LEFT JOIN uriage_denpyo ud
                    ON ud.uri_den_no = d.uri_den_no
                    AND ud.delete_flg = 0
                LEFT JOIN seko_kihon_info ski
                    ON ski.seko_no = d.seko_no
                    AND ski.delete_flg = 0
                LEFT JOIN seikyu_sekyu_saki_info sssi
                    ON sssi.seikyu_den_no = d.seikyu_den_no
                    AND sssi.delete_flg = 0
                LEFT JOIN code_nm_mst moushi
                    ON moushi.code_kbn = '7991'
                    AND moushi.kbn_value_cd_num = (CASE WHEN d.data_kbn = 1 THEN ski.moushi_kbn ELSE d.data_kbn END)
                    AND moushi.delete_flg = 0
                LEFT JOIN code_nm_mst bungas
                    ON bungas.code_kbn = '7990'
                    AND bungas.kbn_value_cd_num = d.bun_gas_kbn_num
                    AND bungas.delete_flg = 0
                LEFT JOIN code_nm_mst nyukin
                    ON nyukin.code_kbn = '1060'
                    AND nyukin.kbn_value_cd_num = d.nyukin_status
                    AND nyukin.delete_flg = 0
                LEFT JOIN code_nm_mst sekyu
                    ON sekyu.code_kbn = '1050'
                    AND (CASE WHEN d.sekkyu_kaisu = 0 THEN 0 
                        ELSE 1 END) = sekyu.kbn_value_cd_num
                    AND sekyu.delete_flg = 0
                LEFT JOIN code_nm_mst sekyu_shonin
                    ON sekyu_shonin.code_kbn = '1070'
                    AND sekyu_shonin.kbn_value_cd_num = d.seikyu_approval_status
                    AND sekyu_shonin.delete_flg = 0
                LEFT JOIN code_nm_mst pay
                    ON pay.code_kbn = '7812'
                    AND pay.kbn_value_cd = TRIM(d.pay_method_cd)
                    AND pay.delete_flg = 0
                LEFT JOIN bumon_mst bm
                    ON bm.bumon_cd = d.bumon_cd
                    AND bm.delete_flg = 0
                LEFT JOIN tanto_mst tm
                    ON tm.tanto_cd = d.tanto_cd
                    AND tm.delete_flg = 0
                LEFT JOIN bumon_kaso_mst bkm
                    ON bkm.ko_bumon_cd = d.bumon_cd
                    AND bkm.delete_flg = 0
                LEFT JOIN tanto_mst stm
                    ON stm.tanto_cd = ski.seko_tanto_cd
                    AND stm.delete_flg = 0
                LEFT JOIN tanto_mst ptm
                    ON ptm.tanto_cd = d.seikyu_print_tanto_cd
                    AND ptm.delete_flg = 0
                LEFT JOIN code_nm_mst cnm_9640
                    ON cnm_9640.code_kbn = '9640'
                    AND cnm_9640.kbn_value_cd_num = sssi.syorui_tenpu_kbn
                    AND cnm_9640.delete_flg = 0
                WHERE d.delete_flg = 0
                    AND d.bun_gas_kbn_num IN (0,2,20)   -- 通常・分割先・合算先
            ) AS T
            WHERE 
            $whereStr
            $orderBy
            $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    
    /**
     * 請求伝票情報を取得（都度請求・請求確定）
     *
     * <AUTHOR> Kobayashi
     * @since   2021/01/xx
     * @param   Msi_Sys_Db $db
     * @param   array      $keyHash  条件
     * @param   boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return  array      該当データがない場合はarray()を返す
     */
    public static function findForSeikyutsudo($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.data_kbn, T.seikyu_den_no, T.nyukin_den_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT 
                T.*
            FROM (
                SELECT 
                    sd.seikyu_den_no                                                    -- 請求番号
                    ,CASE WHEN sd.seikyu_kbn = 0 AND sd.seikyu_approval_status = 0 THEN ''
                         ELSE sd.seikyu_den_no END               AS seikyu_no           -- 請求番号(表示用)
                    ,TO_CHAR(sd.keijo_ymd, 'YYYY/MM/DD')         AS urikaku_ymd         -- 売上金額確定日
                    ,sd.seko_no                                                         -- 施行番号
                    ,sd.data_kbn                                                        -- データ区分
                    ,moushi_d.kbn_value_lnm                      AS moushi_kbn_nm       -- 申込区分名
                    ,sd.seikyu_kbn                                                      -- 請求区分(月次・都度)
                    ,seikyu_k.kbn_value_lnm                      AS seikyu_kbn_nm       -- 請求区分名
                    ,sssi.sekyu_cd                                                      -- 請求コード
                    ,COALESCE(sssi.sekyu_nm1, '') || ' ' 
                        || COALESCE(sssi.sekyu_nm2, '')          AS sekyu_nm            -- 請求先名
                    ,sssi.sekyu_tel                                                     -- 請求先電話番号
                    ,COALESCE(sssi.sekyu_addr1, '') || ' '
                        || COALESCE(sssi.sekyu_addr2, '')        AS sekyu_addr          -- 請求先住所
                    ,COALESCE(sii.k_l_nm, '') || ' ' 
                        || COALESCE(sii.k_f_nm, '')              AS k_nm                -- 故人名
                    ,hd.sekyu_distance || 'km'                   AS tekiyo              -- 摘要
                    ,sd.seikyu_zan + sd.nyukin_prc               AS sekyu_prc           -- 請求金額
                    ,TO_CHAR(sd.kaishu_ymd, 'YYYY/MM/DD')        AS kaishu_ymd          -- 支払期日
                    ,sd.seikyu_zan                                                      -- 請求残
                    ,sd.nyukin_prc                                                      -- 入金額
                    ,CASE WHEN sd.bun_gas_kbn_num = '10' 
                        THEN (SELECT max(nyukin_ymd)
                                FROM nyukin_denpyo 
                                WHERE seikyu_no = sd_saki.seikyu_den_no 
                                AND delete_flg = 0)
                        ELSE (SELECT max(nyukin_ymd)
                                FROM nyukin_denpyo 
                                WHERE seikyu_no = sd.seikyu_den_no 
                                AND delete_flg = 0)
                     END                                         AS nyukin_ymd          -- 入金日
                    ,oya_bm.oya_bumon_cd                                                -- 親部門コード
                    ,oya_nm.bumon_lnm                            AS oya_bumon_nm        -- 親部門名
                    ,sd.bumon_cd                                                        -- 部門コード
                    ,bm.bumon_lnm                                AS bumon_nm            -- 部門名
                    ,sd.tanto_cd                                                        -- 担当者コード
                    ,tanto.tanto_nm                              AS tanto_nm            -- 担当者
                    ,CASE WHEN sd.bun_gas_kbn_num = '10' THEN
                        CASE WHEN sd_saki.sekkyu_kaisu > 0 THEN '発行済み'
                            ELSE '未発行' END
                     ELSE
                        CASE WHEN sd.sekkyu_kaisu > 0 THEN '発行済み'
                            ELSE '未発行' END
                     END                                         AS issue_st            -- 請求状況
                    ,CASE WHEN sd.bun_gas_kbn_num = '10' 
                        THEN nyukin_s_saki.kbn_value_lnm
                        ELSE nyukin_s.kbn_value_lnm
                     END                                         AS nyukin_st           -- 入金状況
                    ,sekyu_s.kbn_value_lnm                       AS shonin_st           -- 請求承認
                    ,CASE WHEN sd.bun_gas_kbn_num = '10' 
                        THEN TO_CHAR(sd_saki.seikyu_print_date, 'YYYY/MM/DD')
                        ELSE TO_CHAR(sd.seikyu_print_date, 'YYYY/MM/DD')
                     END                                         AS seikyu_print_ymd    -- 請求書印刷日
                    ,CASE WHEN sd.bun_gas_kbn_num = '10'
                        THEN sd_saki.seikyu_print_tanto_cd
                        ELSE sd.seikyu_print_tanto_cd
                     END                                         AS print_tanto_cd      -- 請求書印刷者コード
                    ,CASE WHEN sd.bun_gas_kbn_num = '10'
                        THEN print_tanto_saki.tanto_nm
                        ELSE print_tanto.tanto_nm
                     END                                         AS seikyu_print_tanto  -- 請求書印刷者
                   ,TRIM(sd.pay_method_cd)                       AS pay_method_cd       -- 入金方法区分（支払方法区分）
                   ,n_kbn.kbn_value_lnm                          AS pay_method_nm       -- 入金方法
                FROM seikyu_denpyo sd
                LEFT JOIN seikyu_denpyo sd_saki -- 合算先伝票
                    ON sd_saki.seikyu_den_no = sd.bun_gas_seikyu_den_no
                    AND sd_saki.bun_gas_kbn_num = '20'
                    AND sd_saki.delete_flg = 0
                LEFT JOIN seikyu_sekyu_saki_info sssi
                    ON sssi.seikyu_den_no = sd.seikyu_den_no
                    AND sssi.seq_no = 1
                    AND sssi.delete_flg = 0
                LEFT JOIN syutsudo_irai_info sii
                    ON sii.uketsuke_no = sd.seko_no
                    AND sii.delete_flg = 0
                LEFT JOIN hanso_denpyo hd
                    ON hd.uketsuke_no = sd.seko_no
                    AND hd.delete_flg = 0
                LEFT JOIN bumon_mst bm
                    ON bm.bumon_cd = sd.bumon_cd
                    AND bm.delete_flg = 0
                LEFT JOIN bumon_kaso_mst oya_bm
                    ON oya_bm.ko_bumon_cd = sd.bumon_cd
                    AND oya_bm.delete_flg = 0
                LEFT JOIN bumon_mst oya_nm
                    ON oya_nm.bumon_cd = oya_bm.oya_bumon_cd
                    AND oya_nm.delete_flg = 0
                LEFT JOIN tanto_mst tanto
                    ON tanto.tanto_cd = sd.tanto_cd
                    AND tanto.delete_flg = 0
                LEFT JOIN code_nm_mst moushi_d
                    ON moushi_d.code_kbn = '8556'
                    AND moushi_d.kbn_value_cd_num = sd.data_kbn
                    AND moushi_d.delete_flg = 0
                LEFT JOIN code_nm_mst seikyu_k
                    ON seikyu_k.code_kbn = '8310'
                    AND seikyu_k.kbn_value_cd_num = sd.seikyu_kbn
                    AND seikyu_k.delete_flg = 0
                LEFT JOIN code_nm_mst nyukin_s
                    ON nyukin_s.code_kbn = '1060'
                    AND nyukin_s.kbn_value_cd_num = COALESCE(sd.nyukin_status, 0)
                    AND nyukin_s.delete_flg = 0
                LEFT JOIN code_nm_mst nyukin_s_saki
                    ON nyukin_s_saki.code_kbn = '1060'
                    AND nyukin_s_saki.kbn_value_cd_num = COALESCE(sd_saki.nyukin_status, 0)
                    AND nyukin_s_saki.delete_flg = 0
                LEFT JOIN code_nm_mst sekyu_s
                    ON sekyu_s.code_kbn = '1070'
                    AND sekyu_s.kbn_value_cd_num = sd.seikyu_approval_status
                    AND sekyu_s.delete_flg = 0
                LEFT JOIN code_nm_mst n_kbn
                    ON n_kbn.code_kbn = '8526'
                    AND n_kbn.kbn_value_cd = TRIM(sd.pay_method_cd)
                    AND n_kbn.delete_flg = 0
                LEFT JOIN tanto_mst print_tanto
                    ON print_tanto.tanto_cd = sd.seikyu_print_tanto_cd
                    AND print_tanto.delete_flg = 0
                LEFT JOIN tanto_mst print_tanto_saki
                    ON print_tanto_saki.tanto_cd = sd_saki.seikyu_print_tanto_cd
                    AND print_tanto_saki.delete_flg = 0
                WHERE sd.bun_gas_kbn_num IN ('0', '10')
                    AND sd.delete_flg = 0
            ) T
            WHERE 
            $whereStr
            $orderBy
            $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    
    
    /**
     * 請求伝票情報を取得（領収証発行一覧）
     *
     * <AUTHOR> Sugiyama
     * @since 2020/12/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForRyoshulist($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.data_kbn, T.seikyu_den_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT 
                T.*
               ,moushi.kbn_value_lnm AS moushi_kbn_nm
               ,false                AS ikatsu_fuka_flg -- 一括発行不可フラグ
            FROM (
                SELECT 
                     oya_nm.bumon_lnm                                           AS oya_bumon_nm             -- 部門名
                    ,oya_bm.oya_bumon_cd                                                                    -- 親部門コード
                    ,bm.bumon_lnm                                               AS bumon_nm                 -- 部門名
                    ,bm.bumon_cd                                                                            -- 部門コード
                    ,sd.data_kbn                                                                            -- データ区分
                    ,CASE WHEN sd.data_kbn = 4 AND sd.juchusaki_kbn = 2 THEN sd.data_kbn 
                          WHEN sd.data_kbn = 3                          THEN sd.data_kbn 
                          ELSE ski.moushi_kbn
                     END                                                        AS moushi_kbn               -- 申込区分
                    ,COALESCE(ud1.ref_seko_no,ud2.ref_seko_no)                  AS ref_seko_no
                    ,sd.seko_no                                                                             -- 施行番号
                    ,sd.seko_no_sub                                                                         -- 施行枝番号
                    ,CASE WHEN sd.data_kbn = 3  THEN jd.denpyo_no
                          ELSE sd.seko_no
                     END                                                        AS seko_juchu_no            -- 施行番号 OR 受注番号
                    ,CASE WHEN sd.bun_gas_kbn_num = 2 THEN bun_uriage.denpyo_no
                          ELSE jd.denpyo_no
                     END                                                        AS juchu_den_no             -- 受注伝票番号                   
                    ,TO_CHAR(ski.sougi_ymd,'YYYY/MM/DD')                        AS sougi_ymd                -- 葬儀日
                    ,TO_CHAR(sd.nonyu_dt,'YYYY/MM/DD')                          AS nonyu_ymd                -- 納品日
                    ,CASE sd.data_kbn WHEN 3 THEN TO_CHAR(sd.nonyu_dt  , 'YYYY/MM/DD') 
                                      ELSE        TO_CHAR(ski.sougi_ymd, 'YYYY/MM/DD') 
                     END                                                        AS taisyo_ymd               -- 葬儀日/納品日検索用
                    ,sd.seikyu_den_no                                                                       -- 請求伝票番号
                    ,CONCAT(seikyu.sekyu_nm1,' ',seikyu.sekyu_nm2)              AS sekyu_nm                 -- 請求先名
                    ,sd.seikyu_zan + sd.nyukin_prc + sd.uchikin_prc + sd.cupon_prc 
                                                                                AS sekyu_prc                -- 請求金額
                    ,sd.seikyu_zan                                                                          -- 請求残
                    ,TRIM(sd.pay_method_cd)                                     AS pay_method_cd            -- 支払方法区分（支払方法区分）
                    ,n_kbn.kbn_value_lnm                                        AS pay_method_nm            -- 支払方法（検索用）
                    ,COALESCE(rhu.ryosyu_prc,0) + COALESCE(rhu_uc.ryosyu_prc,0) AS ryosyu_prc_disp          -- 領収証金額（表示用）
                    ,COALESCE(sd.seikyu_zan,0) + COALESCE(nyukin.nyukin_prc,0)  AS ryosyu_prc               -- 領収証金額
                    ,CASE WHEN COALESCE(rhu.ryosyu_prc,0) + COALESCE(rhu_uc.ryosyu_prc,0) = 0 THEN 0
                          ELSE 1
                     END                                                        AS ryosyu_prt_status        -- 領収証出力状況
                    ,CASE WHEN COALESCE(rhu.ryosyu_prc,0) + COALESCE(rhu_uc.ryosyu_prc,0) = 0 THEN '未発行' 
                          ELSE '発行済み'
                     END                                                        AS ryosyu_prt_status_nm     -- 領収証出力状況名
                    ,COALESCE(rhu.ryosyu_prc,0) + COALESCE(rhu_uc.ryosyu_prc,0) AS hako_ryosyu_prc          -- 領収証発行済み金額
                    ,ski.seko_tanto_cd                                                                      -- 施行担当者コード
                    ,seikyu.rs_print_kbn                                                                    -- 領収書発行区分 1:要　 2:不要
                    ,seikyu.rs_soufu_kbn                                                                    -- 領収書用紙区分 1:郵送 2:不要
                    ,sd.est_shikijo_cd                                                                      -- 見積式場コード
                    ,CASE WHEN sd.data_kbn = 3  THEN NULL
                          WHEN sd.data_kbn = 20 THEN NULL
                          WHEN sd.data_kbn = 1 AND ski.moushi_kbn = 9 THEN NULL 
                          WHEN sd.data_kbn = 2 THEN sn_houji.basho_nm
                          ELSE sn_sougi.basho_nm 
                     END                                                        AS est_shikijo_nm           -- 見積式場名
                    ,ski.k_nm                                                                               -- 故人名
                    ,seikyu.ryosyusyo_meigi                                     AS ryosyu_meigi             -- 領収証名義
                    ,seikyu.ryosyusyo_soufu_nm                                  AS ryosyu_soufu_nm          -- 領収証送付先名
                    ,seikyu.ryosyusyo_soufu_yubin_no                            AS ryosyu_soufu_yubin_no    -- 領収証送付先郵便番号
                    ,seikyu.ryosyusyo_soufu_addr1                               AS ryosyu_soufu_addr1       -- 領収証送付先住所1
                    ,seikyu.ryosyusyo_soufu_addr2                               AS ryosyu_soufu_addr2       -- 領収証送付先住所2
                    ,sd.uri_prc_sum
                    ,ud.uri_den_no
                    ,sd.status_kbn
                    ,(ARRAY_TO_STRING(ARRAY(
                         SELECT ryosyu_no FROM ryosyusho_history rh WHERE rh.uri_den_no = sd.seikyu_den_no AND rh.delete_flg = 0
                     ),','))                                                    AS ryosyu_no                -- 領収証番号 売上伝票に紐づく領収証全て
                    ,sd.bun_gas_kbn_num
                FROM seikyu_denpyo sd
                LEFT JOIN seko_kihon_info ski
                ON ski.seko_no = sd.seko_no
                AND ski.delete_flg = 0
                LEFT JOIN uriage_denpyo ud
                ON ud.uri_den_no = sd.uri_den_no
                AND ud.delete_flg = 0
                LEFT JOIN uriage_denpyo ud1
                ON  ud1.uri_den_no    = sd.uri_den_no
                AND ud1.delete_flg    = 0
                LEFT JOIN seikyu_denpyo sd1
                ON  sd1.seikyu_den_no = sd.bun_gas_seikyu_den_no
                AND sd1.delete_flg    = 0
                LEFT JOIN uriage_denpyo ud2
                ON  ud2.uri_den_no    = sd1.uri_den_no
                AND ud2.delete_flg    = 0
                LEFT JOIN juchu_denpyo jd
                ON jd.denpyo_no = ud.denpyo_no
                AND jd.delete_flg = 0
                LEFT JOIN seikyu_sekyu_saki_info seikyu
                ON seikyu.seikyu_den_no = sd.seikyu_den_no
                AND seikyu.seq_no = 1
                AND seikyu.delete_flg = 0
                LEFT JOIN bumon_mst bm
                ON bm.bumon_cd = sd.bumon_cd
                AND bm.delete_flg = 0
                LEFT JOIN bumon_kaso_mst oya_bm
                ON oya_bm.ko_bumon_cd = sd.bumon_cd
                AND oya_bm.delete_flg = 0
                LEFT JOIN bumon_mst oya_nm
                ON oya_nm.bumon_cd = oya_bm.oya_bumon_cd
                AND oya_nm.delete_flg = 0
                LEFT JOIN bumon_mst est_shikijo
                ON est_shikijo.bumon_cd = sd.est_shikijo_cd
                AND est_shikijo.delete_flg = 0
                LEFT JOIN sekyu_saki_info ssi
                ON ssi.sekyu_cd = sd.sekyu_cd
                AND ssi.delete_flg = 0
                LEFT JOIN seko_nitei sn_sougi
                ON sn_sougi.seko_no     = ski.seko_no
                AND sn_sougi.nitei_kbn  = 11
                AND sn_sougi.delete_flg = 0
                LEFT JOIN seko_nitei_houji sn_houji
                ON sn_houji.seko_no     = ski.seko_no
                AND sn_houji.nitei_kbn  = 1
                AND sn_houji.delete_flg = 0
                LEFT JOIN seikyu_denpyo bun_seikyu  -- 分割元請求伝票
                ON bun_seikyu.seikyu_den_no = sd.bun_gas_seikyu_den_no
                AND bun_seikyu.delete_flg = 0
                LEFT JOIN uriage_denpyo bun_uriage  -- 分割元売上伝票
                ON bun_uriage.uri_den_no = bun_seikyu.uri_den_no
                AND bun_uriage.delete_flg = 0
                -- 領収証発行金額を取得
                LEFT JOIN (
                    SELECT 
                         uri_den_no
                        ,SUM(gokei_prc) AS ryosyu_prc
                    FROM ryosyusho_history
                    WHERE delete_flg = 0
                    AND hako_kbn IN (0, 1)
                    GROUP BY uri_den_no
                ) rhu
                ON rhu.uri_den_no = sd.seikyu_den_no
                -- 領収証発行金額（内金）を取得
                LEFT JOIN (
                    SELECT 
                         uri_den_no
                        ,SUM(gokei_prc) AS ryosyu_prc
                    FROM ryosyusho_history_uc
                    WHERE delete_flg = 0
                    AND hako_kbn IN (0, 1)
                    GROUP BY uri_den_no
                ) rhu_uc
                ON rhu_uc.uri_den_no = ud.denpyo_no
                LEFT JOIN (
                    SELECT 
                         seikyu_no
                        ,SUM(nyukin_prc) AS nyukin_prc
                    FROM nyukin_denpyo
                    WHERE delete_flg = 0
                    GROUP BY seikyu_no
                ) nyukin
                ON  nyukin.seikyu_no = sd.seikyu_den_no
                LEFT JOIN code_nm_mst n_kbn
                ON  n_kbn.code_kbn     = '9757'
                AND n_kbn.kbn_value_cd = TRIM(sd.pay_method_cd)
                AND n_kbn.delete_flg   = 0
                WHERE sd.delete_flg = 0
                AND sd.bun_gas_kbn_num NOT IN (1,10)
            ) T
            LEFT JOIN code_nm_mst moushi
            ON moushi.code_kbn = '8527'
            AND moushi.kbn_value_cd_num = T.moushi_kbn
            AND moushi.delete_flg = 0
            WHERE 
            $whereStr
            AND CASE WHEN T.data_kbn IN(1,2) THEN CASE WHEN T.status_kbn > 3 THEN TRUE
                                                       ELSE FALSE END 
                     WHEN T.data_kbn = 3 THEN TRUE
                     WHEN T.data_kbn = 4 THEN TRUE
                     ELSE FALSE END
            $orderBy
            $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    /**
     * 請求伝票情報(内金)を取得（領収証発行一覧）
     *
     * <AUTHOR> Sugiyama
     * @since 2020/12/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForRyoshulistJuchuUchikin($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.data_kbn, T.juchu_den_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT 
                T.*
               ,moushi.kbn_value_lnm AS moushi_kbn_nm
               ,true                 AS ikatsu_fuka_flg -- 一括発行不可フラグ
            FROM (
                SELECT 
                     oya_nm.bumon_lnm                                           AS oya_bumon_nm             -- 部門名
                    ,oya_bm.oya_bumon_cd                                                                    -- 親部門コード
                    ,bm.bumon_lnm                                               AS bumon_nm                 -- 部門名
                    ,bm.bumon_cd                                                                            -- 部門コード
                    ,jd.data_kbn                                                                            -- データ区分
                    ,ski.moushi_kbn                                                                         -- 申込区分
                    ,null                                                       AS ref_seko_no
                    ,jd.seko_no                                                                             -- 施行番号
                    ,jd.seko_no_sub                                                                         -- 施行枝番号
                    ,jd.seko_no                                                 AS seko_juchu_no            -- 施行番号 OR 受注番号
                    ,null                                                       AS uri_den_no
                    ,jd.denpyo_no                                               AS juchu_den_no             -- 受注伝票番号
                    ,TO_CHAR(ski.sougi_ymd,'YYYY/MM/DD')                        AS sougi_ymd                -- 葬儀日
                    ,TO_CHAR(jd.nonyu_dt,'YYYY/MM/DD')                          AS nonyu_ymd                -- 納品日
                    ,CASE jd.data_kbn WHEN 3 THEN TO_CHAR(jd.nonyu_dt  , 'YYYY/MM/DD') 
                                      ELSE        TO_CHAR(ski.sougi_ymd, 'YYYY/MM/DD') 
                     END                                                        AS taisyo_ymd               -- 葬儀日/納品日検索用
                    ,null                                                       AS seikyu_den_no            -- 請求伝票番号
                    ,CONCAT(jssi.sekyu_nm1,' ',jssi.sekyu_nm2)                  AS sekyu_nm                 -- 請求先名
                    ,null                                                       AS sekyu_prc                -- 請求金額
                    ,0                                                          AS seikyu_zan
                    ,null                                                       AS pay_method_cd            -- 入金方法区分（支払方法区分）
                    ,null                                                       AS pay_method_nm            -- 入金方法区分名
                    ,COALESCE(rhu.ryosyu_prc,0)                                 AS ryosyu_prc_disp          -- 領収証金額（表示用）
                    ,0                                                          AS ryosyu_prc               -- 領収証金額
                    ,CASE WHEN COALESCE(rhu.ryosyu_prc,0) = 0 THEN 0
                          ELSE                                     1
                     END                                                        AS ryosyu_prt_status        -- 領収証出力状況
                    ,CASE WHEN COALESCE(rhu.ryosyu_prc,0) = 0 THEN '未発行' 
                          ELSE                                     '発行済み'
                     END                                                        AS ryosyu_prt_status_nm     -- 領収証出力状況名
                    ,COALESCE(rhu.ryosyu_prc, 0)                                AS hako_ryosyu_prc          -- 領収証発行済み金額
                    ,ski.seko_tanto_cd                                                                      -- 施行担当者コード
                    ,jssi.rs_print_kbn                                                                      -- 領収書発行区分 1:要　 2:不要
                    ,jssi.rs_soufu_kbn                                                                      -- 領収書用紙区分 1:郵送 2:不要
                    ,jd.est_shikijo_cd                                                                      -- 見積式場コード
                    ,CASE WHEN jd.data_kbn = 1 AND ski.moushi_kbn = 9 THEN NULL 
                          WHEN jd.data_kbn = 2                        THEN sn_houji.basho_nm
                          ELSE sn_sougi.basho_nm 
                     END                                                        AS est_shikijo_nm           -- 見積式場名
                    ,ski.k_nm                                                                               -- 故人名
                    ,jssi.ryosyusyo_meigi                                       AS ryosyu_meigi             -- 領収証名義
                    ,jssi.ryosyusyo_soufu_nm                                    AS ryosyu_soufu_nm          -- 領収証送付先名
                    ,jssi.ryosyusyo_soufu_yubin_no                              AS ryosyu_soufu_yubin_no    -- 領収証送付先郵便番号
                    ,jssi.ryosyusyo_soufu_addr1                                 AS ryosyu_soufu_addr1       -- 領収証送付先住所1
                    ,jssi.ryosyusyo_soufu_addr2                                 AS ryosyu_soufu_addr2       -- 領収証送付先住所2
                    ,0                                                          AS uri_prc_sum
                    ,null                                                       AS uri_den_no
                    ,ski.status_kbn
                    ,COALESCE(rhu2.ryosyu_cnt, 0)                               AS ryosyu_cnt               -- 領収証発行＆破棄回数
                    ,(ARRAY_TO_STRING(ARRAY(
                         SELECT ryosyu_no FROM ryosyusho_history_uc rh WHERE rh.uri_den_no = jd.denpyo_no AND rh.delete_flg = 0
                     ),','))                                                    AS ryosyu_no                -- 領収証番号 請求伝票に紐づく領収証全て
                FROM juchu_denpyo jd
                LEFT JOIN (
                    SELECT 
                         uri_den_no
                        ,SUM(nyukin_prc) AS nyukin_prc
                    FROM nyukin_denpyo
                    WHERE delete_flg = 0
                    AND nyu_kbn = 88
                    GROUP BY uri_den_no
                ) nyukin
                ON  nyukin.uri_den_no = jd.denpyo_no
                LEFT JOIN seko_kihon_info ski
                ON  ski.seko_no         = jd.seko_no
                AND ski.delete_flg      = 0
                LEFT JOIN juchu_sekyu_saki_info jssi
                ON  jssi.denpyo_no      = jd.denpyo_no
                AND jssi.seq_no         = 1
                AND jssi.delete_flg     = 0
                LEFT JOIN bumon_mst bm
                ON  bm.bumon_cd         = jd.bumon_cd
                AND bm.delete_flg       = 0
                LEFT JOIN bumon_kaso_mst oya_bm
                ON  oya_bm.ko_bumon_cd  = jd.bumon_cd
                AND oya_bm.delete_flg   = 0
                LEFT JOIN bumon_mst oya_nm
                ON  oya_nm.bumon_cd     = oya_bm.oya_bumon_cd
                AND oya_nm.delete_flg   = 0
                LEFT JOIN code_nm_mst n_kbn
                ON  n_kbn.code_kbn      = '9757'
                AND n_kbn.kbn_value_cd  = TRIM(jd.pay_method_cd)
                AND n_kbn.delete_flg    = 0
                LEFT JOIN seko_nitei sn_sougi
                ON sn_sougi.seko_no     = ski.seko_no
                AND sn_sougi.nitei_kbn  = 11
                AND sn_sougi.delete_flg = 0
                LEFT JOIN seko_nitei_houji sn_houji
                ON sn_houji.seko_no     = ski.seko_no
                AND sn_houji.nitei_kbn  = 1
                AND sn_houji.delete_flg = 0
                LEFT JOIN (
                    SELECT 
                         uri_den_no
                        ,SUM(gokei_prc) AS ryosyu_prc
                    FROM ryosyusho_history_uc
                    WHERE delete_flg = 0
                    AND hako_kbn IN (0, 1)
                    GROUP BY uri_den_no
                ) rhu
                ON rhu.uri_den_no = jd.denpyo_no
                LEFT JOIN ( -- 内金領収証発行件数
                    SELECT 
                         uri_den_no
                        ,COUNT(uri_den_no) AS ryosyu_cnt
                    FROM ryosyusho_history_uc
                    WHERE delete_flg = 0
                    AND data_kbn  IN (1, 2) -- データ区分   1:葬儀 2:法事
                    GROUP BY uri_den_no
                ) rhu2
                ON rhu2.uri_den_no = jd.denpyo_no
                WHERE jd.data_kbn  IN (1, 2)  -- データ区分   1:葬儀 2:法事
                AND ski.moushi_kbn IN (1, 2)  -- 申込区分     1:葬儀 2:法事
                AND jd.delete_flg   = 0
            ) T
            LEFT JOIN code_nm_mst moushi
            ON moushi.code_kbn = '8527'
            AND moushi.kbn_value_cd_num = T.moushi_kbn
            AND moushi.delete_flg = 0
            WHERE 
            $whereStr
            AND CASE WHEN T.status_kbn IN (1,2) THEN TRUE   -- 新規作成から施行金額確定前
                     WHEN T.status_kbn IN (4) AND T.ryosyu_cnt <> 0 THEN TRUE   -- 請求承認後かつ内金領収証発行済み
                     ELSE FALSE END
             ORDER BY T.data_kbn, T.juchu_den_no 
            limit 301 offset 0 
END_OF_SQL
                , $param);
        return $select;
    }
    /**
     * 請求伝票情報(内金)を取得（領収証発行一覧）
     *
     * <AUTHOR> Sugiyama
     * @since 2020/12/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $isDateEffective  st_date,ed_date を抽出条件にいれるか
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findForRyoshulistUchikin($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.data_kbn, T.seikyu_den_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
            SELECT 
                T.*
               ,moushi.kbn_value_lnm AS moushi_kbn_nm
               ,true                 AS ikatsu_fuka_flg -- 一括発行不可フラグ
            FROM (
                SELECT 
                     oya_nm.bumon_lnm                                           AS oya_bumon_nm             -- 部門名
                    ,oya_bm.oya_bumon_cd                                                                    -- 親部門コード
                    ,bm.bumon_lnm                                               AS bumon_nm                 -- 部門名
                    ,bm.bumon_cd                                                                            -- 部門コード
                    ,ud.data_kbn                                                                            -- データ区分
                    ,ski.moushi_kbn                                                                         -- 申込区分
                    ,null                                                       AS ref_seko_no
                    ,ud.seko_no                                                                             -- 施行番号
                    ,ud.seko_no_sub                                                                         -- 施行枝番号
                    ,ud.seko_no                                                 AS seko_juchu_no            -- 施行番号 OR 受注番号
                    ,ud.uri_den_no
                    ,ud.bun_gas_kbn_num
                    ,ud.denpyo_no                                               AS juchu_den_no             -- 受注伝票番号
                    ,TO_CHAR(ski.sougi_ymd,'YYYY/MM/DD')                        AS sougi_ymd                -- 葬儀日
                    ,TO_CHAR(ud.nonyu_dt,'YYYY/MM/DD')                          AS nonyu_ymd                -- 納品日
                    ,CASE ud.data_kbn WHEN 3 THEN TO_CHAR(ud.nonyu_dt  , 'YYYY/MM/DD') 
                                      ELSE        TO_CHAR(ski.sougi_ymd, 'YYYY/MM/DD') 
                     END                                                        AS taisyo_ymd               -- 葬儀日/納品日検索用
                    ,null                                                       AS seikyu_den_no            -- 請求伝票番号
                    ,CONCAT(ussi.sekyu_nm1,' ',ussi.sekyu_nm2)                  AS sekyu_nm                 -- 請求先名
                    ,ud.seikyu_zan  + 
                     ud.nyukin_prc  + 
                     ud.uchikin_prc + 
                     ud.cupon_prc                                               AS sekyu_prc                -- 請求金額
                    ,ud.seikyu_zan                                                                          -- 請求残
                    ,TRIM(ud.pay_method_cd)                                     AS pay_method_cd            -- 入金方法区分（支払方法区分）
                    ,n_kbn.kbn_value_lnm                                        AS pay_method_nm            -- 入金方法区分名
                    ,COALESCE(rhu.ryosyu_prc,0)                                 AS ryosyu_prc_disp          -- 領収証金額（表示用）
                    ,COALESCE(ud.seikyu_zan,0) + COALESCE(nyukin.nyukin_prc,0)  AS ryosyu_prc               -- 領収証金額
                    ,CASE WHEN COALESCE(rhu.ryosyu_prc,0) = 0 THEN 0
                          ELSE                                     1
                     END                                                        AS ryosyu_prt_status        -- 領収証出力状況
                    ,CASE WHEN COALESCE(rhu.ryosyu_prc,0) = 0 THEN '未発行' 
                          ELSE                                     '発行済み'
                     END                                                        AS ryosyu_prt_status_nm     -- 領収証出力状況名
                    ,COALESCE(rhu.ryosyu_prc, 0)                                AS hako_ryosyu_prc          -- 領収証発行済み金額
                    ,ski.seko_tanto_cd                                                                      -- 施行担当者コード
                    ,ussi.rs_print_kbn                                                                      -- 領収書発行区分 1:要　 2:不要
                    ,ussi.rs_soufu_kbn                                                                      -- 領収書用紙区分 1:郵送 2:不要
                    ,ud.est_shikijo_cd                                                                      -- 見積式場コード
                    ,CASE WHEN ud.data_kbn = 1 AND ski.moushi_kbn = 9 THEN NULL 
                          WHEN ud.data_kbn = 2                        THEN sn_houji.basho_nm
                          ELSE sn_sougi.basho_nm 
                     END                                                        AS est_shikijo_nm           -- 見積式場名
                    ,ski.k_nm                                                                               -- 故人名
                    ,ussi.ryosyusyo_meigi                                       AS ryosyu_meigi             -- 領収証名義
                    ,ussi.ryosyusyo_soufu_nm                                    AS ryosyu_soufu_nm          -- 領収証送付先名
                    ,ussi.ryosyusyo_soufu_yubin_no                              AS ryosyu_soufu_yubin_no    -- 領収証送付先郵便番号
                    ,ussi.ryosyusyo_soufu_addr1                                 AS ryosyu_soufu_addr1       -- 領収証送付先住所1
                    ,ussi.ryosyusyo_soufu_addr2                                 AS ryosyu_soufu_addr2       -- 領収証送付先住所2
                    ,ud.uri_prc_sum
                    ,ud.uri_den_no
                    ,ski.status_kbn
                    ,COALESCE(rhu2.ryosyu_cnt, 0)                               AS ryosyu_cnt               -- 領収証発行＆破棄回数
                    ,(ARRAY_TO_STRING(ARRAY(
                         SELECT ryosyu_no FROM ryosyusho_history_uc rh WHERE rh.uri_den_no = ud.uri_den_no AND rh.delete_flg = 0
                     ),','))                                                    AS ryosyu_no                -- 領収証番号 請求伝票に紐づく領収証全て
                FROM uriage_denpyo ud
                LEFT JOIN (
                    SELECT 
                         uri_den_no
                        ,SUM(nyukin_prc) AS nyukin_prc
                    FROM nyukin_denpyo
                    WHERE delete_flg = 0
                    GROUP BY uri_den_no
                ) nyukin
                ON  nyukin.uri_den_no = ud.uri_den_no
                LEFT JOIN seko_kihon_info ski
                ON  ski.seko_no         = ud.seko_no
                AND ski.delete_flg      = 0
                LEFT JOIN uriage_sekyu_saki_info ussi
                ON  ussi.uri_den_no     = ud.uri_den_no
                AND ussi.seq_no         = 1
                AND ussi.delete_flg     = 0
                LEFT JOIN bumon_mst bm
                ON  bm.bumon_cd         = ud.bumon_cd
                AND bm.delete_flg       = 0
                LEFT JOIN bumon_kaso_mst oya_bm
                ON  oya_bm.ko_bumon_cd  = ud.bumon_cd
                AND oya_bm.delete_flg   = 0
                LEFT JOIN bumon_mst oya_nm
                ON  oya_nm.bumon_cd     = oya_bm.oya_bumon_cd
                AND oya_nm.delete_flg   = 0
                LEFT JOIN code_nm_mst n_kbn
                ON  n_kbn.code_kbn      = '1130'
                AND n_kbn.kbn_value_cd  = TRIM(ud.pay_method_cd)
                AND n_kbn.delete_flg    = 0
                LEFT JOIN seko_nitei sn_sougi
                ON sn_sougi.seko_no     = ski.seko_no
                AND sn_sougi.nitei_kbn  = 11
                AND sn_sougi.delete_flg = 0
                LEFT JOIN seko_nitei_houji sn_houji
                ON sn_houji.seko_no     = ski.seko_no
                AND sn_houji.nitei_kbn  = 1
                AND sn_houji.delete_flg = 0
                LEFT JOIN (
                    SELECT 
                         uri_den_no
                        ,SUM(gokei_prc) AS ryosyu_prc
                    FROM ryosyusho_history_uc
                    WHERE delete_flg = 0
                    AND hako_kbn IN (0, 1)
                    GROUP BY uri_den_no
                ) rhu
                ON rhu.uri_den_no = ud.uri_den_no
                LEFT JOIN (
                    SELECT 
                         uri_den_no
                        ,COUNT(uri_den_no) AS ryosyu_cnt
                    FROM ryosyusho_history_uc
                    WHERE delete_flg = 0
                    GROUP BY uri_den_no
                ) rhu2
                ON rhu2.uri_den_no = ud.uri_den_no
                WHERE ud.data_kbn  IN (1, 2)  -- データ区分   1:葬儀 2:法事
                AND ski.moushi_kbn IN (1, 2)  -- 申込区分     1:葬儀 2:法事
                AND ud.delete_flg   = 0
            ) T
            LEFT JOIN code_nm_mst moushi
            ON moushi.code_kbn = '8527'
            AND moushi.kbn_value_cd_num = T.moushi_kbn
            AND moushi.delete_flg = 0
            WHERE 
            $whereStr
            AND CASE WHEN T.status_kbn > 2 AND T.ryosyu_cnt <> 0 THEN TRUE
                     WHEN T.status_kbn <= 2  THEN TRUE
                     ELSE FALSE END
            $orderBy
            $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    /**
     * 
     * 領収証個別発行分を取得
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     * 
     */
    public static function findForRyoshulistEx1($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = '';
        }
        $select = $db->easySelect(<<< END_OF_SQL
            SELECT 
                T.*
            FROM (
                SELECT DISTINCT
                     ud.seko_no
                    ,ud.uri_den_no
                    ,ud.denpyo_no                           AS juchu_den_no
                    ,COALESCE(udm.k_free1,0)                AS ryosyu_check             -- 領収証個別区分
                    ,uriage_info.ryosyu_prc                 AS ryosyu_prc               -- 領収証金額
                    ,uriage_info.ryosyusyo_meigi            AS ryosyu_meigi             -- 領収証名義
                    ,uriage_info.ryosyusyo_soufu_nm         AS ryosyu_soufu_nm          -- 領収証送付先名
                    ,uriage_info.ryosyusyo_soufu_yubin_no   AS ryosyu_soufu_yubin_no    -- 領収証送付先郵便番号
                    ,uriage_info.ryosyusyo_soufu_addr1      AS ryosyu_soufu_addr1       -- 領収証送付先住所1
                    ,uriage_info.ryosyusyo_soufu_addr2      AS ryosyu_soufu_addr2       -- 領収証送付先住所2
                FROM uriage_denpyo ud
                LEFT JOIN uriage_denpyo_msi udm
                ON  udm.uri_den_no = ud.uri_den_no
                AND udm.delete_flg = 0
                LEFT JOIN uriage_sekyu_saki_info uriage_info
                ON  uriage_info.uri_den_no = ud.uri_den_no
                AND uriage_info.delete_flg = 0
                WHERE ud.delete_flg  = 0
                AND ud.data_kbn      = 4 -- データ区分 
                AND ud.juchusaki_kbn = 1 -- 受注先区分
                AND udm.k_free1      = 1 -- 領収証個別区分
            ) T
            WHERE 
            $whereStr
            $orderBy
            $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
    /**
     * 
     * 領収証個別発行以外分を取得
     * 
     * @param type $db
     * @param type $keyHash
     * @return type
     * 
     */
    public static function findForRyoshulistEx2($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = DataMapper_Utils::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = DataMapper_Utils::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = '';
        }
        $select = $db->easySelect(<<< END_OF_SQL
            SELECT 
                 T.seko_no
                ,SUM(T.ryosyu_prc) AS ryosyu_prc
            FROM (
                SELECT DISTINCT
                     ud.seko_no
                    ,ud.data_kbn
                    ,ud.uri_den_no
                    ,COALESCE(ud.juchusaki_kbn,1)           AS juchusaki_kbn
                    ,ud.denpyo_no                           AS juchu_den_no
                    ,CASE WHEN ud.data_kbn = 4 THEN uriage_info.ryosyu_prc                 
                          ELSE ud.uri_prc_sum + ud.uri_hepn_sum + ud.uri_nebk_sum + ud.out_zei_prc + ud.hoshi_prc_sum
                     END                                    AS ryosyu_prc
                    ,COALESCE(udm.k_free1,0)                AS k_free1
                    ,uriage_info.ryosyusyo_meigi            AS ryosyu_meigi             -- 領収証名義
                    ,uriage_info.ryosyusyo_soufu_nm         AS ryosyu_soufu_nm          -- 領収証送付先名
                    ,uriage_info.ryosyusyo_soufu_yubin_no   AS ryosyu_soufu_yubin_no    -- 領収証送付先郵便番号
                    ,uriage_info.ryosyusyo_soufu_addr1      AS ryosyu_soufu_addr1       -- 領収証送付先住所1
                    ,uriage_info.ryosyusyo_soufu_addr2      AS ryosyu_soufu_addr2       -- 領収証送付先住所2
                FROM uriage_denpyo ud
                LEFT JOIN uriage_denpyo_msi udm
                ON  udm.uri_den_no = ud.uri_den_no
                AND udm.delete_flg = 0
                LEFT JOIN uriage_sekyu_saki_info uriage_info
                ON  uriage_info.uri_den_no = ud.uri_den_no
                AND uriage_info.delete_flg = 0
                WHERE ud.delete_flg  = 0 
                    AND COALESCE(ud.juchusaki_kbn,1) = 1
                    AND COALESCE(udm.k_free1,0)      = 0
                AND ud.data_kbn IN(1,2,4)
            ) T
            WHERE 
            $whereStr
            GROUP BY T.seko_no
            $orderBy
            $tailClause
END_OF_SQL
                , $param);
        return $select;
    }
}
