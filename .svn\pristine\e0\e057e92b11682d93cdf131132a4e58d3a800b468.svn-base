<?php

/**
 * Logic_UploadFurikomiMake
 *
 * 入金マッチ処理
 *
 * @category	 App
 * @package	 models\Logic
 * <AUTHOR> Kino
 * @since 	 2021/01/XX
 * @filesource 
 */

/**
 * 入金マッチ処理
 * 
 * @category	 App
 * @package	 models\Logic
 * <AUTHOR> Kino
 * @since 	 2021/01/XX
 */
class Logic_UploadFurikomiMake {

    static protected $_denpyo_kbn_fk = 2;      // 入金種別 2:振込　（コード名称：0640）
    static protected $_denpyo_kbn_ch = 7;      // 入金種別 7:手数料（コード名称：0640）
    static protected $_file_type_fb = 1;       // ファイル種別 1：振込
    static protected $_file_type_cv = 2;       // ファイル種別 2：コンビニ
    static protected $_chg_code_kbn = 8557;    // コード区分 8557:銀行振込手数料科目
    static protected $_nyu_code_kbn_fb = 9762;    // コード区分 9762:入金科目(預り金)
    static protected $_nyu_code_kbn = 8558;    // コード区分 8558:入金科目(収納代行会社)
    static protected $_pl_code_kbn = 8565;    // コード区分 8565:消費税区分(課税仕入)
    static protected $_type_sokuho = '01';     // 種別 01:速報
    static protected $_type_kakuho = '02';     // 種別 02:確報
    static protected $_print_kbn_vr = '3';     // 請求書印刷区分：3=>仮想口座

    /**
     * 入金マッチ処理メイン
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	データベース
     * @param	   array  $upload_data  アップロードレコード
     * 　　　　　　　　　　　　　　　　　　(ファイル毎に配列が分かれています)
     * @param	   string $torikomi_ymd 取込日
     * @return	   array  $rtn_data_arr INSERT情報(PDF出力時に使用)
     */
    public static function Main($db, $upload_data, $torikomi_ymd) {
        
        App_DevCoverage_Manager::easyStart();
        $cnt = 0;
        $rtn_data_arr = array();
        foreach ($upload_data as $rec) {
            $rtn_data_arr[$rec['kaisya_cd']] = null;
            // 振込入金ファイル取込履歴・明細等を作成
            $cnt += static::UploadFurikomiNyukin($db, $rec, $torikomi_ymd);
            // トランザクションはファイル毎にコミット
            $db->commit();
        }
        
        // INSERT情報取得(PDF出力時に使用)
        $match_history = static::getMatchHistoryNo($db);
        foreach($match_history as $mRec){
            $kaisya_cd = trim($mRec['kaisya_cd']);
            $rtn_data_arr[$kaisya_cd][] = $mRec['history_no'];
        }
        
        return $rtn_data_arr;
    }
    
    /**
     * 振込入金ファイル取込を作成
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	    データベース
     * @param	   array      $data         アップロードレコード
     * @param	   string     $torikomi_ymd 取込日
     * @return	   integer    $cnt          更新件数
     */
    private static function UploadFurikomiNyukin($db, $data, $torikomi_ymd) {

        $cnt = 0;
        
        $history = array();
        $history['kaisya_cd'] = $data['kaisya_cd'];               // 会社コード
        $history['file_type'] = $data['file_type'];               // ファイル種別
        $history['upload_date'] = $torikomi_ymd;                  // 取込日
        $history['file_nm'] = $data['file_nm'];                   // ファイル名
        // 振込銀行
        if($history['file_type'] == static::$_file_type_fb){
            $file_create_date = static::warekiToSeirekiFromYYMMDD($data['file_create_date']);
        }
        // コンビニ
        else if($history['file_type'] == static::$_file_type_cv){
            $file_create_date = static::seirekiFromYYYYMMDD($data['file_create_date']);
        }
        $history['file_create_date'] = $file_create_date;            // ファイル作成日

        // 振込入金ファイル取込履歴を作成
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('upload_furikomi_nyukin_history', $history);
        $cnt += $db->easyExecute($sql, $param);
        $history['history_no'] = $db->getSequenceCurrVal('upload_furikomi_nyukin_history_history_no_seq');
        
        // 振込入金ファイル取込明細を作成
        foreach($data['all_data'] as $rec){
            $cnt += static::UploadFurikomiNyukinMsi($db, $rec, $history);
        }
        
        // マッチング判定処理
        static::doMachuCheck($db, $history['history_no']);
        
        return $cnt;
    }

    /**
     * 振込入金ファイル取込明細を作成
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	  データベース
     * @param	   array      $msi        データレコード
     * @param	   string     $history    取込履歴情報
     * @return	   integer    $cnt        更新件数
     */
    private static function UploadFurikomiNyukinMsi($db, $msi, $history) {
        
        $cnt = 0;
        
        // 振込入金ファイル取込明細を保存する
        $info = array();
        $info['history_no'] = $history['history_no'];                 // 取込履歴番号
        $info['file_type'] = $history['file_type'];                   // ファイル種別
        $info['kaisya_cd'] = $history['kaisya_cd'];                   // 会社コード
        $info['msi_no'] = static::getMsiNo($db, $info['history_no']);   // 取込明細番号

        // 振込銀行
        if($info['file_type'] == static::$_file_type_fb){

            // 月次チェック (勘定日 = 入金日 case:振込銀行)
            $kanjyo = str_pad($msi['kanjyo_date'], 6, '0', STR_PAD_LEFT);
            $kanjyo_date = static::warekiToSeirekiFromYYMMDD($kanjyo);
            $fixFlg = static::checkDataFix($db, $kanjyo_date, 3); // 3:入金確定
            if($fixFlg){
                throw new Msi_Logic_ExceptionInput('月次確定されているため、取込ができません。');
            }

            // 同一の明細番号・勘定日・会社コードを持ったファイルを取り込んだ場合は重複レコードをスキップ (true:重複有,false:重複無)
            $shokai_no = static::doReplace($msi['shokai_no']);
            $chkShokaiNo = static::chkShokaiNo($db, $shokai_no, $kanjyo, $history['kaisya_cd']);
            if($chkShokaiNo){
                return;
            }
            $info['shokai_no'] = $shokai_no;                                               // 照会番号
            $info['kanjyo_date'] = static::doReplace($msi['kanjyo_date']);                 // 勘定日
            $info['data_kbn'] = static::doReplace($msi['data_kbn']);                       // データ区分
            $info['azukeire_date'] = static::doReplace($msi['azukeire_date']);             // 起算日
//            $info['iribarai_kbn'] = static::doReplace($msi['iribarai_kbn']);               // 入払区分
//            $info['tori_kbn'] = static::doReplace($msi['tori_kbn']);                       // 取引区分
            $info['tori_kingaku'] = static::doReplace($msi['tori_kingaku']);               // 取引金額
            $info['taten_kenkingaku'] = static::doReplace($msi['taten_kenkingaku']);       // うち他店券金額
//            $info['koukan_date'] = static::doReplace($msi['koukan_date']);                 // 交換呈示日
//            $info['huwatari_date'] = static::doReplace($msi['huwatari_date']);             // 不渡返還日
//            $info['tegata_kbn'] = static::doReplace($msi['tegata_kbn']);                   // 手形・小切手区分
//            $info['tegata_no'] = static::doReplace($msi['tegata_no']);                     // 手形・小切手番号
//            $info['ryoten_no'] = static::doReplace($msi['ryoten_no']);                     // 僚店番号
            $info['furikomi_rq_code'] = static::doReplace($msi['furikomi_rq_code']);       // 振込依頼人コード
            $info['furikomi_rq_nm'] = static::doReplace($msi['furikomi_rq_nm']);           // 振込依頼人名または契約者番号
            $info['shimuke_bk_nm'] = static::doReplace($msi['shimuke_bk_nm']);             // 仕向銀行名
            $info['shimuke_ten_nm'] = static::doReplace($msi['shimuke_ten_nm']);           // 仕向店名
//            $info['tekiyo'] = static::doReplace($msi['tekiyo']);                           // 摘要内容
            $info['torikeshi_kbn'] = static::doReplace($msi['torikeshi_kbn']);             // 取消区分
            $info['edi_info'] = static::doReplace($msi['edi_info']);                       // EDI情報
            $info['seikyu_no'] = '0000000000';                                             // 請求伝票No

            $furikomi_rq_code_mb = static::doReplace(mb_substr($info['furikomi_rq_code'], 3, 7));
            $furikomi_rq_nm_mb = static::doReplace(mb_substr($info['furikomi_rq_nm'], 3, 7));
            $furikomi_rq_nm_mb2 = static::doReplace(mb_substr($info['furikomi_rq_nm'], 10));
            //「振込依頼人コード」のデータがある場合
            //「振込依頼人コード」の4桁目から7桁の値を口座番号を口座番号とし、「振込依頼人名または契約者番号」を振込依頼人名とする。
            if($info['furikomi_rq_code']){
                $info['kouza_no'] = $furikomi_rq_code_mb;                                 // バーチャル口座番号
                $info['furikomi_nm'] = $info['furikomi_rq_nm'];                           // 振込依頼人名
            }
            //「振込依頼人コード」のデータがない かつ 「振込依頼人名または契約者番号」の4桁目から7桁の値が数値である場合
            //「振込依頼人名または契約者番号」の4桁目から7桁の値を口座番号とし、8桁目以降値を振込依頼人名とする。
            else if(!$info['furikomi_rq_code'] && is_numeric($furikomi_rq_nm_mb)){
                $info['kouza_no'] = $furikomi_rq_nm_mb;                                  // バーチャル口座番号
                $info['furikomi_nm'] = $furikomi_rq_nm_mb2;                              // 振込依頼人名
            }
        }
        // コンビニ
        else if($info['file_type'] == static::$_file_type_cv){

            // 種別 01：速報 のみ取得
            $type = static::doReplace($msi['type']);
            if( $type !== static::$_type_kakuho){
                return;
            }

            // 月次チェック  ( 店舗収納日 = 入金日 case:コンビニ)
            $shop_syuno = str_pad($msi['shop_syuno_date'], 8, '0', STR_PAD_LEFT);
            $shop_syuno_date = static::seirekiFromYYYYMMDD($shop_syuno);
            $fixFlg = static::checkDataFix($db, $shop_syuno_date, 3); // 3:入金確定
            if($fixFlg){
                return;
            }

            // 取込時にバーコード番号を保存し、同一の番号を持ったファイルを取り込んだ場合は
            // 重複レコードをスキップ (true:重複有,false:重複無)
            $barcode_info = static::doReplace($msi['barcode_info']);
            $chkBarcodeInfo = static::chkBarcodeInfo($db, $barcode_info);
            if($chkBarcodeInfo){
                return;
            }

            // バーコード値・入金金額が完全一致しているか確認
            $sqlSeikyu = <<< END_OF_SQL
                SELECT *
                  FROM seikyu_denpyo
                 WHERE cvs_bar_cd = :barcode_info
                   AND seikyu_zan = :seikyu_zan
                   AND delete_flg = 0
END_OF_SQL;
            $shiharai_prc = ltrim($msi['shiharai_prc'], '0');
            $selSeikyu = $db->easySelOne($sqlSeikyu, array('barcode_info' => $msi['barcode_info']
                                                          ,'seikyu_zan'   => $shiharai_prc) );
            if(empty($selSeikyu)){
                $selSeikyu['seikyu_den_no'] = null;
            }

            $info['seikyu_no'] = $selSeikyu['seikyu_den_no'];                             // 請求伝票No
            $info['record_kbn'] = static::doReplace($msi['record_kbn']);                  // レコード区分
            $info['type'] = $type;                                                        // 種別
            $info['shop_syuno_date'] = static::doReplace($msi['shop_syuno_date']);        // 店舗収納日
            $info['shop_syuno_time'] = static::doReplace($msi['shop_syuno_time']);        // 店舗収納時間
            $info['barcode_type'] = static::doReplace($msi['barcode_type']);              // バーコード種別
            $info['barcode_info'] = $barcode_info;                                        // バーコード情報
            $info['syuno_shop_code'] = static::doReplace($msi['syuno_shop_code']);        // 収納店舗コード
            $info['kyakuso_code'] = static::doReplace($msi['kyakuso_code']);              // 客層コード
            $info['data_get_date'] = static::doReplace($msi['data_get_date']);            // データ取得年月日
            $info['furikomi_yotei'] = static::doReplace($msi['furikomi_yotei']);          // 振込予定日
            $info['keiri_syori_date'] = static::doReplace($msi['keiri_syori_date']);      // 経理処理年月日
            $info['cvs_code'] = static::doReplace($msi['cvs_code']);                      // CVSコード
            $info['kouza_no'] = mb_substr($barcode_info, 12, 7, "utf-8");                 // バーチャル口座番号
            $info['shiharai_prc'] = static::doReplace($msi['shiharai_prc']);              // 支払金額
        }

        list($sql, $param) = DataMapper_Utils::makeInsertSQL('upload_furikomi_nyukin_msi', $info);
        $cnt += $db->easyExecute($sql, $param);

        return $cnt;
    }
    
    /**
     * マッチング判定処理
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	   データベース
     * @param	   string     $history_no  取込履歴番号
     * @return	   integer $cnt 更新件数
     */
    private static function doMachuCheck($db, $history_no){
        
        $param = array('history_no' => $history_no);
        $sqlUpload = <<< END_OF_SQL
        SELECT *
          FROM upload_furikomi_nyukin_msi
         WHERE history_no =:history_no
           AND delete_flg = 0
END_OF_SQL;
        $selUpload = $db->easySelect($sqlUpload, $param);
        
        foreach($selUpload as $uRec){
            
            $doMach = array();
            // 口座番号がない場合はスキップ
            if(!$uRec['kouza_no']){
                continue;
            }
            
            // 振込銀行
            // BR口座番号・請求残高で判定
            if($uRec['file_type'] == static::$_file_type_fb){
            
                // BR口座番号が完全一致しているか確認
                $sqlSeikyu = <<< END_OF_SQL
                    SELECT s.*
                         , c.kbn_value_snm AS chg_cost
                      FROM seikyu_denpyo s
                      LEFT JOIN code_nm_mst c
                        ON c.code_kbn = '8552' -- 振込入金差異
                       AND c.kbn_value_cd = :bumon_cd
                       AND c.kaisya_cd = s.kaisya_cd
                       AND c.delete_flg = 0
                     WHERE s.br_koza_no = :br_koza_no
                       AND s.delete_flg = 0
                     ORDER BY s.seikyu_den_no
END_OF_SQL;
                $selSeikyu = $db->easySelect($sqlSeikyu, array('br_koza_no' => trim($uRec['kouza_no']) // 同一の口座番号は複数の請求伝票に割り振られない
                                                              ,'bumon_cd'   => trim($uRec['kaisya_cd'])) );

                if($selSeikyu){
                    foreach($selSeikyu as $sRec){
//                        $seikyuSekyuSakiInfo = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('seikyu_den_no' => $sRec['seikyu_den_no']));
//                        if (count($seikyuSekyuSakiInfo) > 0) {
//                            $sekyu_knm = self::changName($seikyuSekyuSakiInfo[0]['sekyu_knm1'].$seikyuSekyuSakiInfo[0]['sekyu_knm2']);
//                        } else {
//                            $sekyu_knm = self::changName($sRec['sekyu_knm']);
//                        }
//                        // 請求先名カナが完全一致しているか確認
//                        $furikomi_nm = self::changName($uRec['furikomi_nm']);
//                        if($furikomi_nm !== $sekyu_knm){
//                            continue;
//                        }

                        // 請求残高が対象範囲内か確認
                        $judgePrc = $sRec['seikyu_zan'] - $uRec['tori_kingaku']; // 請求残 - 取引金額
                        if($judgePrc <= $sRec['chg_cost']){
                            // 「取引金額 = 請求残」の場合の手数料
                            if($uRec['tori_kingaku'] == $sRec['seikyu_zan']){
                                $chg_cost = 0;
                            }
                            // 「取引金額 < 請求残」の場合の手数料
                            else if($uRec['tori_kingaku'] < $sRec['seikyu_zan']){
                                $chg_cost = $judgePrc;
                            }
                            // 請求金額対象範囲外
                            else{
                                continue;
                            }
                            $doMach = $sRec;
                        }
                    }
                }
            }
            // コンビニ
            // バーコード値、請求残高で判定（＝seikyu_den_noが存在する）
            else if($uRec['file_type'] == static::$_file_type_cv){
                
                // 請求伝票情報を取得
                $sqlSeikyu = <<< END_OF_SQL
                    SELECT s.*
                      FROM seikyu_denpyo s
                     WHERE s.seikyu_den_no = :seikyu_den_no
                       AND s.delete_flg = 0
END_OF_SQL;
                $selSeikyu = $db->easySelOne($sqlSeikyu, array('seikyu_den_no' => $uRec['seikyu_no']));
                
                if($selSeikyu && $uRec['seikyu_no']){
                    $doMach = $selSeikyu;
                    $furikomi_nm = null;
                    $chg_cost = 0;
                }
            }
            
            // マッチの場合
            if(count($doMach) > 0){
                $cnt = static::doMatch($db, $uRec, $doMach, $furikomi_nm, $chg_cost);
            }
            // アンマッチの場合
            else{
                $cnt = static::doUnMatch($db, $uRec);
            }
        }
    }
    
    /**
     * マッチ処理
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param     Msi_Sys_Db $db                  データベース
     * @param     array      $uploadFurikomiData  振込入金ファイル取込明細
     * @param     array      $seikyuDenpyoData    請求伝票
     * @param     string     $furikomi_nm         補正後の請求先名
     * @param     int        $chg_cost            手数料
     * @return	   integer $cnt 更新件数
     */
    private static function doMatch($db, $uploadFurikomiData, $seikyuDenpyoData, $furikomi_nm=null, $chg_cost=0){
        
        $cnt = 0;
        
        // 入金伝票を作成
        $nyukin_data = static::NyukinDenpyoMake($db, $uploadFurikomiData, $seikyuDenpyoData, $chg_cost);
        // 入金伝票明細を作成
        $nyukin_msi = static::NyukinDenpyoMsiMake($db, $uploadFurikomiData, $seikyuDenpyoData, $nyukin_data, $chg_cost);
        
        // 振込入金消込情報を作成
        $furikomi_data = static::FurikomiNyukinMake($db, $uploadFurikomiData, $chg_cost);
        // 振込入金消込情報明細を作成
        $furikomi_msi = static::FurikomiNyukinMsiMake($db, $uploadFurikomiData, $seikyuDenpyoData, $nyukin_data, $furikomi_nm);

        // 入金伝票から売上伝票の入金金額、請求残高を修正して更新する
        Logic_SyukeiTblUpdateSub::updNyukinPrcSeikyu2($db, $seikyuDenpyoData['seikyu_den_no']);
        
        return $cnt;
    }
    
    /**
     * アンマッチ処理
     *
     * @authsor   MSI Kino
     * @since     2021/01/XX
     * @param     Msi_Sys_Db $db                  データベース
     * @param     array      $uploadFurikomiData  振込入金ファイル取込明細
     * @return	   integer $cnt 更新件数
     */
    private static function doUnMatch($db, $uploadFurikomiData){
        // 振込入金消込情報を作成
        
        // 振込銀行
        if($uploadFurikomiData['file_type'] == static::$_file_type_fb){
             $mode = 1; // 0:マッチ(default)、1:アンマッチ、2:消込
             $furikomi_data = static::FurikomiNyukinMake($db, $uploadFurikomiData, 0, $mode);
        }
        // コンビニ
        else if($uploadFurikomiData['file_type'] == static::$_file_type_cv){
            $mode = 2; // 0:マッチ(default)、1:アンマッチ、2:消込
            $seikyuDenpyoData = array();
            // 入金伝票を作成
            $nyukin_data = static::NyukinDenpyoMake($db, $uploadFurikomiData, $seikyuDenpyoData);
            // 入金伝票明細を作成
            $nyukin_msi = static::NyukinDenpyoMsiMake($db, $uploadFurikomiData, $seikyuDenpyoData, $nyukin_data);

            // 振込入金消込情報を作成
            $furikomi_data = static::FurikomiNyukinMake($db, $uploadFurikomiData, 0, $mode);
            // 振込入金消込情報明細を作成
            $furikomi_msi = static::FurikomiNyukinMsiMake($db, $uploadFurikomiData, $seikyuDenpyoData, $nyukin_data);
        }
       
        return 1;
    }
    
    /**
     * 入金伝票を作成
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	   データベース
     * @param	   array  $upload_furikomi 振込入金ファイル取込明細情報
     * @param	   array  $upload_seikyu   請求伝票情報
     * @param     int    $chg_cost        手数料
     * @return	   array  $hdrData         入金伝票レコード
     */
    private static function NyukinDenpyoMake($db, $upload_furikomi, $upload_seikyu, $chg_cost=0) {

        $nyuDenpyo = array();
        // 請求伝票情報を取得
        $seikyuData = DataMapper_SeikyuDenpyo::findOne($db,array('seikyu_den_no' => $upload_seikyu['seikyu_den_no']));
        // 存在しない入金伝票番号を取得
        $denpyo_no = App_ClsGetCodeNo::GetCodeNo($db, 'nyukin_denpyo', 'denpyo_no', null);
        $nyuDenpyo['denpyo_no'] = $denpyo_no;                                        // 伝票NO
        if($upload_seikyu){
            $nyuDenpyo['data_kbn'] = $upload_seikyu['data_kbn'];                     // データ区分
            $nyuDenpyo['seko_no'] = $upload_seikyu['seko_no'];                       // 施行番号
            $nyuDenpyo['seko_no_sub'] = $upload_seikyu['seko_no_sub'];               // 施行番号（枝番）
            $nyuDenpyo['seikyu_no'] = $upload_seikyu['seikyu_den_no'];               // 請求書№
            $nyuDenpyo['kaisya_cd'] = $upload_seikyu['kaisya_cd'];                   // 会社コード
            $nyuDenpyo['bumon_cd'] = $upload_seikyu['bumon_cd'];                     // 部門コード
        }else{
            $nyuDenpyo['seko_no'] = '0000000000';                                    // 施行番号
            $nyuDenpyo['seko_no_sub'] = '00';                                        // 施行番号（枝番）
            $nyuDenpyo['seikyu_no'] = '0000000000';                                  // 請求書№
            $nyuDenpyo['bumon_cd'] = trim($upload_furikomi['kaisya_cd']);            // 部門コード
        }
        $nyuDenpyo['tanto_cd'] = App_Utils::getTantoCd();                            // 担当者コード
        $nyuDenpyo['uri_history_no'] = 0;                                            // 売上履歴番号
        // 振込銀行
        if($upload_furikomi['file_type'] == static::$_file_type_fb){
            $kanjyo = str_pad($upload_furikomi['kanjyo_date'], 6, '0', STR_PAD_LEFT);
            $_kanjyo_date = static::warekiToSeirekiFromYYMMDD($kanjyo);
            // マッチした請求の売上計上日と入金日を比較して入金日を決定する。
            // 入金日が売上計上日以前(計上日を含まない)の場合は、売上計上日を入金日とする（計上日前に売掛を減らさないようにするため）
            // (上記ELSE)入金日が売上計上日以降(計上日を含む)の場合は、FBの勘定日をを入金日とする
            $keijo_date = new DateTime($seikyuData['keijo_ymd']);
            $kanjyo_date = new DateTime($_kanjyo_date);
            $nyukin_ymd = null;
            if ($keijo_date > $kanjyo_date) {
                $nyukin_ymd = $keijo_date->format('Y/m/d');
            } else {
                $nyukin_ymd = $kanjyo_date->format('Y/m/d');
            }
            $nyuDenpyo['nyukin_ymd'] = $nyukin_ymd;                                 // 入金日
            $nyuDenpyo['nyu_kbn'] = 1;                                               // 入金区分(1:FB入金)
            $nyuDenpyo['nyukin_prc'] = $upload_furikomi['tori_kingaku'] + $chg_cost; // 入金金額合計 (取引金額 + 手数料 + 仮受金)
        }
        // コンビニ
        else if($upload_furikomi['file_type'] == static::$_file_type_cv){
            $shop_syuno = str_pad($upload_furikomi['shop_syuno_date'], 8, '0', STR_PAD_LEFT);
            $shop_syuno_date = static::seirekiFromYYYYMMDD($shop_syuno);
            $nyuDenpyo['nyukin_ymd'] = $shop_syuno_date;                             // 入金日
            $nyuDenpyo['nyu_kbn'] = 3;                                               // 入金区分(3:コンビニ入金)
            $nyuDenpyo['nyukin_prc'] = $upload_furikomi['shiharai_prc'];             // 入金金額合計
        }
        if($nyuDenpyo['nyukin_ymd']){
            $ZeiTbl = App_ClsTaxLib::GetTaxInfo($db, $nyuDenpyo['nyukin_ymd']);
            $TaxPrc = App_ClsTaxLib::CalcTax($chg_cost, 1, $ZeiTbl['zei_rtu'], $ZeiTbl['zei_hasu_kbn']);
            $nyuDenpyo['in_zei_prc'] = $TaxPrc['ZeiPrc'];                            // 内税消費税額
            
        }
        
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('nyukin_denpyo', $nyuDenpyo);
        $cnt = $db->easyExecute($sql, $param);

        return $nyuDenpyo;
    }

    /**
     * 入金伝票明細を作成
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	データベース
     * @param	   array  $upload_furikomi 振込入金ファイル取込明細情報
     * @param	   array  $upload_seikyu   請求伝票情報
     * @param	   array  $nyukin_data     入金伝票レコード
     * @param     int    $chg_cost        手数料
     * @return	   array  $msi             入金伝票明細レコード
     */
    private static function NyukinDenpyoMsiMake($db, $upload_furikomi, $upload_seikyu, $nyukin_data, $chg_cost=0) {
        
        $nyuDenpyoMsi = array();
        $count = 1;
        $nyuDenpyoMsi['denpyo_no'] = $nyukin_data['denpyo_no'];            // 伝票NO
        $nyuDenpyoMsi['msi_no'] = $count;                                  // 明細№
        $nyuDenpyoMsi['disp_no'] = $count;                                 // 表示順
        $nyuDenpyoMsi['denpyo_kbn'] = static::$_denpyo_kbn_fk;             // 伝票区分
        $nyuDenpyoMsi['nyukn_kbn'] = 0;                                    // 入金区分 0:入金
        if($upload_seikyu){
            $nyuDenpyoMsi['kaisya_cd'] = $upload_seikyu['kaisya_cd'];      // 会社コード
            $nyuDenpyoMsi['bumon_cd'] = $upload_seikyu['bumon_cd'];        // 部門コード
        }else{
            $nyuDenpyoMsi['bumon_cd'] = trim($upload_furikomi['kaisya_cd']); // 部門コード
        }
        $nyuDenpyoMsi['shoihi_zei_cd'] = 0;                                // 消費税コード
        $nyuDenpyoMsi['out_zei_prc'] = 0;                                  // 外税消費税額
        $nyuDenpyoMsi['in_zei_prc'] = 0;                                   // 内税消費税額
        // 振込銀行
        if($upload_furikomi['file_type'] == static::$_file_type_fb){
            $kamokuArr = static::getKamoku($db, array('code_kbn' => static::$_nyu_code_kbn_fb));
            if($kamokuArr){
                $nyuDenpyoMsi['kamoku_cd'] = $kamokuArr['kamoku_cd'];          // 入金科目コード
                $nyuDenpyoMsi['hojo_cd'] = $kamokuArr['biko'];              // 補助科目コード
            }
            $nyuDenpyoMsi['nyukin_prc'] = $upload_furikomi['tori_kingaku'];    // 入金金額
        }
        // コンビニ
        else if($upload_furikomi['file_type'] == static::$_file_type_cv){
            $kamokuArr = static::getKamoku($db, array('code_kbn' => static::$_nyu_code_kbn));
            if($kamokuArr){
                $nyuDenpyoMsi['kamoku_cd'] = $kamokuArr['kamoku_cd'];          // 入金科目コード
                $nyuDenpyoMsi['hojo_cd'] = $kamokuArr['hojo_cd'];              // 補助科目コード
            }
            $nyuDenpyoMsi['nyukin_prc'] = $upload_furikomi['shiharai_prc'];    // 入金金額
        }
        if(array_key_exists('kamoku_cd' ,$nyuDenpyoMsi)){
            $zei_cd = static::getZeiCd($db, $nyuDenpyoMsi['kamoku_cd'], 0);
            // 税入力区分が「あり」以外は税関連コードはNULLにする
            if (isset($zei_cd)) {
                $nyuDenpyoMsi['zei_cd'] = $zei_cd;
            } else {
                $nyuDenpyoMsi['shoihi_zei_cd'] = null;
            }
        }
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('nyukin_denpyo_msi', $nyuDenpyoMsi);
        $cnt = $db->easyExecute($sql, $param);
        
        // 手数料がある場合は明細を作成
        // (上記の明細と差分があるところのみ書き換え)
        if($chg_cost !== 0){
            $count = 2;
            $nyuDenpyoMsi['msi_no'] = $count;                                  // 明細№
            $nyuDenpyoMsi['disp_no'] = $count;                                 // 表示順
            $nyuDenpyoMsi['denpyo_kbn'] = static::$_denpyo_kbn_ch;             // 伝票区分
            $nyuDenpyoMsi['nyukin_prc'] = $chg_cost;                           // 入金金額
            // 消費税コード
            $ZeiTbl = App_ClsTaxLib::GetTaxInfo($db, $nyukin_data['nyukin_ymd']);
            $nyuDenpyoMsi['shoihi_zei_cd'] = $ZeiTbl['zei_cd'];     
             // 内税消費税額
            $TaxPrc = App_ClsTaxLib::CalcTax($chg_cost, 1, $ZeiTbl['zei_rtu'], $ZeiTbl['zei_hasu_kbn']);
            $nyuDenpyoMsi['in_zei_prc'] = $TaxPrc['ZeiPrc'];
            // 科目コード
            $kamokuArr = static::getKamoku($db, array('code_kbn' => static::$_chg_code_kbn));
            if($kamokuArr){
                $nyuDenpyoMsi['kamoku_cd'] = $kamokuArr['kamoku_cd'];          // 入金科目コード
                $nyuDenpyoMsi['hojo_cd'] = $kamokuArr['hojo_cd'];              // 補助科目コード
                // 税コード
                $zei_cd = static::getZeiCd($db, $nyuDenpyoMsi['kamoku_cd'], $nyuDenpyoMsi['shoihi_zei_cd']);
                // 税入力区分が「あり」以外は税関連コードはNULLにする
                if (isset($zei_cd)) {
                    $nyuDenpyoMsi['zei_cd'] = $zei_cd;
                } else {
                    $nyuDenpyoMsi['shoihi_zei_cd'] = null;     
                }
            }
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('nyukin_denpyo_msi', $nyuDenpyoMsi);
            $cnt += $db->easyExecute($sql, $param);
        }

        return $cnt;
    }
    
    /**
     * 振込入金消込情報を作成
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	データベース
     * @param	   array $upload_rec    振込入金ファイル取込明細情報
     * @param     int   $chg_cost      手数料
     * @param     int   $mode          モード 0:マッチ(default)、1:アンマッチ
     * @return	   array $furikomiNyu   振込入金消込レコード
     */
    private static function FurikomiNyukinMake($db, $upload_rec, $chg_cost, $mode=0) {

        $furikomiNyu = array();
        $furikomiNyu['history_no'] = $upload_rec['history_no'];   // 取込履歴番号
        $furikomiNyu['msi_no'] = $upload_rec['msi_no'];           // 取込明細番号
        $furikomiNyu['file_type'] = $upload_rec['file_type'];     // ファイル種別
        $furikomiNyu['kaisya_cd'] = $upload_rec['kaisya_cd'];     // 会社コード
        $selHistory = static::getHistory($db); // 同じトランザクション内であれば取込日は1つしか設定できないため
        $furikomiNyu['upload_date'] = $selHistory['upload_date']; // 取込日
        if($mode == 0){ // マッチの場合
            $auto_payment_flg = 1;
            $payment_kbn = 1;
        }else if($mode == 1){ // アンマッチの場合
            $auto_payment_flg = 0;
            $payment_kbn = 0;
        }else if($mode == 2){ // 削除の場合
            $auto_payment_flg = 1;
            $payment_kbn = 9;
        }
        $furikomiNyu['auto_payment_flg'] = $auto_payment_flg;     // 自動消込フラグ (0：自動未消込(アンマッチ) 1：自動消込(マッチ))
        $furikomiNyu['payment_kbn'] = $payment_kbn;               // 消込区分 (0：未消込 1：消込済み 9：削除)
        $furikomiNyu['kouza_no'] = $upload_rec['kouza_no'];       // 口座番号
        $furikomiNyu['furikomi_chg_cost'] = $chg_cost;            // 振込手数料
        $furikomiNyu['kariuke_prc'] = 0;                          // 仮受金
        // 振込銀行
        if($furikomiNyu['file_type'] == static::$_file_type_fb){
            $kanjyo = str_pad($upload_rec['kanjyo_date'], 6, '0', STR_PAD_LEFT);
            $kanjyo_date = self::warekiToSeirekiFromYYMMDD($kanjyo);
            $furikomiNyu['kanjyo_date'] = $kanjyo_date;               // 入金日
            $furikomiNyu['furikomi_nm'] = $upload_rec['furikomi_nm']; // 振込依頼人名
            $furikomiNyu['nyukin_prc'] = $upload_rec['tori_kingaku']; // 入金額
        }
        // コンビニ
        else if($furikomiNyu['file_type'] == static::$_file_type_cv){
            $shop_syuno = str_pad($upload_rec['shop_syuno_date'], 8, '0', STR_PAD_LEFT);
            $shop_syuno_date = static::seirekiFromYYYYMMDD($shop_syuno);
            $furikomiNyu['kanjyo_date'] = $shop_syuno_date;          // 入金日
            $furikomiNyu['nyukin_prc'] = $upload_rec['shiharai_prc'];// 入金額
        }
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('furikomi_payment_info', $furikomiNyu);
        $cnt = $db->easyExecute($sql, $param);

        return $furikomiNyu;
    }

    /**
     * 振込入金消込情報明細を作成
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	   データベース
     * @param	   array  $upload_furikomi 振込入金ファイル取込明細情報
     * @param	   array  $upload_seikyu   請求伝票情報
     * @param	   int    $nyu_denpyo      入金伝票No
     * @param	   string $furikomi_nm     補正後請求先カナ名
     * @return	   array  $furikomiNyuMsi  振込入金消込情報明細レコード
     */
    private static function FurikomiNyukinMsiMake($db, $upload_furikomi, $upload_seikyu, $nyu_denpyo, $furikomi_nm=null) {

        $furikomiNyuMsi = array();
        $furikomiNyuMsi['history_no'] = $upload_furikomi['history_no'];   // 取込履歴番号
        $furikomiNyuMsi['msi_no'] = $upload_furikomi['msi_no'];           // 取込明細番号
        $furikomiNyuMsi['nyukin_den_no'] = $nyu_denpyo['denpyo_no'];      // 入金伝票No
        if($upload_seikyu){
            $furikomiNyuMsi['seikyu_no'] = $upload_seikyu['seikyu_den_no'];   // 請求伝票No
        }
        // 振込銀行
        if($upload_furikomi['file_type'] == static::$_file_type_fb){
            $furikomiNyuMsi['hosei_seikyu_nm'] = $furikomi_nm;            // 補正請求先名
        }
        list($sql, $param) = DataMapper_Utils::makeInsertSQL('furikomi_payment_info_msi', $furikomiNyuMsi);
        $cnt = $db->easyExecute($sql, $param);

        return $furikomiNyuMsi;
    }
    
    /**
     * 振込入金ファイル取込履歴取得処理
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	データベース
     * @return	   int $historyNo
     */
    private static function getHistory($db){
        $req_id = $db->getOneVal( "SELECT CURRVAL('x_req_id_seq')" );
        $param = array('_req_id' => $req_id);
        
        $sql = <<< END_OF_SQL
        SELECT *
          FROM upload_furikomi_nyukin_history
         WHERE _req_id = :_req_id
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        return $select;
    }
    
    /**
     * マッチ済みの取込履歴番号を取得
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   Msi_Sys_Db $db	データベース
     * @return	   int $historyNo
     */
    private static function getMatchHistoryNo($db){
        $req_id = $db->getOneVal( "SELECT CURRVAL('x_req_id_seq')" );
        $param = array('_req_id' => $req_id);
        
        $sql = <<< END_OF_SQL
        SELECT ufm.kaisya_cd, ufm.history_no
          FROM upload_furikomi_nyukin_msi ufm
          LEFT JOIN furikomi_payment_info fp
            ON fp.history_no = ufm.history_no
           AND fp.msi_no = ufm.msi_no
           AND fp.delete_flg = 0
         WHERE ufm._req_id = :_req_id
           AND ufm.delete_flg = 0
           AND fp.auto_payment_flg = 1 -- 0：自動未消込(アンマッチ) 1：自動消込(マッチ)
         GROUP BY ufm.kaisya_cd, ufm.history_no
END_OF_SQL;
        $select = $db->easySelect($sql, $param);
        return $select;
    }
    
    /**
     * 振込入金ファイル取込明細の明細№取得処理
     * 
     * <AUTHOR> Kino
     * @since     2021/01/XX
     * @param	   Msi_Sys_Db $db         データベース
     * @param	   int        $history_no 取込履歴番号
     * @return    int        $msi_no     最大№
     */
    private static function getMsiNo($db, $history_no) {
        $msi_no = 0;
        $param = array('history_no' => $history_no);
        
        $sql = <<< END_OF_SQL
        SELECT COALESCE(MAX(msi_no),0) AS msi_no
          FROM upload_furikomi_nyukin_msi
         WHERE history_no = :history_no
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        
        if (count($select) > 0) {
            $msi_no = $select['msi_no'];
        }
        return $msi_no + 1;
    }
    
    /**
     * 税コード取得処理
     * (科目マスタの税コード入力区分が1の場合、税コードマスタを参照)
     * 
     * <AUTHOR> Kino
     * @since     2021/01/XX
     * @param	   Msi_Sys_Db $db             データベース
     * @param	   int        $kamoku_cd      科目コード
     * @param	   int        $shoihi_zei_cd  消費税コード
     * @return    int        $zei_cd         税コード
     */
    private static function getZeiCd($db, $kamoku_cd, $shoihi_zei_cd) {
        
        $zei_cd = null;
        // 税コード入力区分を確認
        $param = array('kamoku_cd'      => $kamoku_cd
                      ,'zei_cd_inp_kbn' => 1);
        $sql = <<< END_OF_SQL
        SELECT *
          FROM kamoku_mst
         WHERE kamoku_cd = :kamoku_cd
           AND zei_cd_inp_kbn = :zei_cd_inp_kbn
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        
        if(empty($select)){
            return $zei_cd;
        }
        
        // 性格区分によってコード区分を取得する
        if ($select['seikaku_kbn'] === '2') {
            $codeMst = DataMapper_CodeNmMst::find($db, array('code_kbn' => static::$_pl_code_kbn, 'kbn_value_cd' => $shoihi_zei_cd));
            if (count($codeMst) > 0) {
                $zei_cd = $codeMst[0]['kbn_value_cd_num'];
            }
        }
        return $zei_cd;
    }
    
    /**
     * 科目・補助科目コードの取得処理
     * 区分値正式名に科目、区分値コードに補助科目
     * 
     * <AUTHOR> Kino
     * @since     2021/01/XX
     * @param	   Msi_Sys_Db $db         データベース
     * @return    array      $select
     */
    private static function getKamoku($db, $param) {
        
        $sql = <<< END_OF_SQL
        SELECT kbn_value_lnm AS kamoku_cd -- 区分値正式名に科目
             , kbn_value_cd_num AS hojo_cd    -- 区分値コードに補助科目
             , biko -- 9762の場合はこれが補助科目になる
          FROM code_nm_mst
         WHERE code_kbn = :code_kbn
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        
        return $select;
    }
    
    /**
     * 前後のスペースを削除
     * （スペース削除後、空文字になった場合はnullとする）
     *
     * <AUTHOR> Kino
     * @since	   2021/01/XX
     * @param	   string $txt
     * @return	   string $txt
     */
    private static function doReplace($txt){
        
        // 前後のスペースを削除
        $txt = trim($txt);
        // 文字がない場合はnull
        if($txt == ''){
            $txt = null;
        }
        return $txt;

    }
    
    /**
     * 名前の補正処理
     * ・半角、全角スペースを削除
     * ・半角カナを全角カナに変換
     * ・長音記号はハイフンに変換
     * ・カタカナ小文字を大文字へ変換
     * ・会社表記を削除
     * (振込入金消込, 入金アンマッチ消込画面で使用)
     *
     * @authsor   MSI Kino
     * @since     2021/01/XX
     * @param     string $nm
     * @return    string $name
     */
    public static function changName($nm){
        
        $name = $nm;
        
        //・半角、全角スペースを削除
        $name = str_replace(' ', '' , $name);         // 半角スペースをカット
        $name = str_replace('　', '', $name);         // 全角スペースをカット
        
        // 濁点・半濁点が別々になっていた場合、一文字に変換
        $name = mb_convert_kana($name, mb_strtolower('K'), 'UTF-8');
        $name = str_replace(["゛", "゜"], ["ﾞ", "ﾟ"], $name); // U+309B,U+309C
        $name = mb_convert_kana($name, 'K' . 'V', 'UTF-8');
        
        //・半角カナを全角カナに変換
        $name = mb_convert_kana($name, 'KV', 'UTF-8'); // 全角カナに変換
        
        //・長音はASCIIのハイフンに変換
        $name = str_replace('ｰ', '-' , $name);         // 半角カナの長音
        $name = str_replace('ー', '-' , $name);        // 全角の長音
        $name = str_replace('－', '-' , $name);        // 全角のマイナス
        
        //・カタカナ小文字を大文字へ変換
        $search = array('ァ','ィ','ゥ','ェ','ォ','ッ','ャ','ュ','ョ','ヮ');
        $replace = array('ア','イ','ウ','エ','オ','ツ','ヤ','ユ','ヨ','ワ');
        $name = str_replace($search, $replace, $name);
        
        // 記号は全角に変換しないため半角に戻す
        $name = str_replace('「', '｢' , $name);
        $name = str_replace('」', '｣' , $name);
        $name = str_replace('、', '､' , $name);
        $name = str_replace('。', '｡' , $name);
        $name = str_replace('・', '･' , $name);
        
        //・会社表記を削除
        $kakko = array(array('（', ')'), array('(', '）')
                     , array('(', ')'), array('(', ''), array('', ')')      // 半角カッコ
                     , array('（', '）'), array('（', ''), array('', '）')); // 全角カッコ
        foreach($kakko as $kRec){
            $search = array( $kRec[0].'カイジヨウ'.$kRec[1]
                           , $kRec[0].'キヨウサイレン'.$kRec[1]
                           , $kRec[0].'ギヨキヨウ'.$kRec[1]
                           , $kRec[0].'キヨウサイ'.$kRec[1]
                           , $kRec[0].'キヨウクミ'.$kRec[1]
                           , $kRec[0].'ギヨレン'.$kRec[1]
                           , $kRec[0].'ケイザイレン'.$kRec[1]
                           , $kRec[0].'ケンボ'.$kRec[1]
                           , $kRec[0].'コクキヨウレン'.$kRec[1]
                           , $kRec[0].'コクホレン'.$kRec[1]
                           , $kRec[0].'コウネン'.$kRec[1]
                           , $kRec[0].'シヨクハンキヨヴ'.$kRec[1]
                           , $kRec[0].'ジユウクミ'.$kRec[1]
                           , $kRec[0].'シヨクアン'.$kRec[1]
                           , $kRec[0].'シヤキヨウ'.$kRec[1]
                           , $kRec[0].'セイキヨウ'.$kRec[1]
                           , $kRec[0].'セイメイ'.$kRec[1]
                           , $kRec[0].'トクヨウ'.$kRec[1]
                           , $kRec[0].'ノウキヨウレン'.$kRec[1]
                           , $kRec[0].'ユウクミ'.$kRec[1]
                           , $kRec[0].'ロウクミ'.$kRec[1]
                           , $kRec[0].'チドク'.$kRec[1]
                           , $kRec[0].'カサイ'.$kRec[1]
                           , $kRec[0].'コクホ'.$kRec[1]
                           , $kRec[0].'カンリ'.$kRec[1]
                           , $kRec[0].'チユウ'.$kRec[1]
                           , $kRec[0].'ロウム'.$kRec[1]
                           , $kRec[0].'トクヒ'.$kRec[1]
                           , $kRec[0].'シヤホ'.$kRec[1]
                           , $kRec[0].'シユウ'.$kRec[1]
                           , $kRec[0].'シユツ'.$kRec[1]
                           , $kRec[0].'シホウ'.$kRec[1]
                           , $kRec[0].'ザイ'.$kRec[1]
                           , $kRec[0].'ギヨ'.$kRec[1]
                           , $kRec[0].'ケン'.$kRec[1]
                           , $kRec[0].'シヤ'.$kRec[1]
                           , $kRec[0].'シツ'.$kRec[1]
                           , $kRec[0].'ガク'.$kRec[1]
                           , $kRec[0].'ゼイ'.$kRec[1]
                           , $kRec[0].'ダイ'.$kRec[1]
                           , $kRec[0].'ドク'.$kRec[1]
                           , $kRec[0].'ノウ'.$kRec[1]
                           , $kRec[0].'フク'.$kRec[1]
                           , $kRec[0].'ベン'.$kRec[1]
                           , $kRec[0].'ホゴ'.$kRec[1]
                           , $kRec[0].'モク'.$kRec[1]
                           , $kRec[0].'エイ'.$kRec[1]
                           , $kRec[0].'レン'.$kRec[1]
                           , $kRec[0].'イ'.$kRec[1]
                           , $kRec[0].'ド'.$kRec[1]
                           , $kRec[0].'シ'.$kRec[1]
                           , $kRec[0].'メ'.$kRec[1]
                           , $kRec[0].'ユ'.$kRec[1]
                           , $kRec[0].'ソ'.$kRec[1]
                           , $kRec[0].'カ'.$kRec[1]
                            );
            $name = str_replace($search, '', $name);
        }
        return $name;
    }
  
    /**
     * 月次確定チェック処理
     *
     * <AUTHOR> Kino
     * @since  2021/01/XX
     * @param  Msi_Sys_Db $db      データベース
     * @param  string     $ymd     対象日 (計上日、入金日etc...)
     * @param  string     $fix_kbn 確定区分 1:売上, 2:仕入, 3:入金 
     * @return boolean    true:確定済, false:未確定
     */
    private static function checkDataFix($db, $ymd, $fix_kbn) {
        // 月次確定チェック
        $flg = false;
        $fixInfo = DataMapper_DataFixTable::findOne($db, array('fix_kbn' => $fix_kbn));
        if (!empty($fixInfo) && count($fixInfo) > 0) {
            if ($ymd <= $fixInfo['fix_date_ymd']) {
                $flg = true;
            }
        }
        return $flg;
    }
    
    /**
     * 同一の照会番号があるか判定
     * 
     * <AUTHOR> Kino
     * @since     2021/01/XX
     * @param	   Msi_Sys_Db $db         データベース
     * @param	   string        $shokai_no  照会番号
     * @param	   string        $kanjyo_date  勘定日
     * @param	   string        $kaisya_cd  会社コード
     * @return    boolean
     */
    private static function chkShokaiNo($db, $shokai_no, $kanjyo_date, $kaisya_cd) {
        
        $param = array('shokai_no' => $shokai_no, 'kaisya_cd' => $kaisya_cd, 'kanjyo_date' => $kanjyo_date);
        
        $sql = <<< END_OF_SQL
        SELECT *
          FROM upload_furikomi_nyukin_msi
         WHERE shokai_no = :shokai_no
           AND trim(kaisya_cd) = :kaisya_cd
           AND kanjyo_date = :kanjyo_date
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        
        if (empty($select)) {
            return false;
        }
        return true;
    }
    
    /**
     * 同一のバーコード情報があるか判定
     * 
     * <AUTHOR> Kino
     * @since     2021/01/XX
     * @param	   Msi_Sys_Db $db           データベース
     * @param	   int        $barcode_info バーコード情報
     * @return    boolean
     */
    private static function chkBarcodeInfo($db, $barcode_info) {
        
        $param = array('barcode_info' => $barcode_info);
        
        $sql = <<< END_OF_SQL
        SELECT *
          FROM upload_furikomi_nyukin_msi
         WHERE barcode_info = :barcode_info
           AND delete_flg = 0
END_OF_SQL;
        $select = $db->easySelOne($sql, $param);
        
        if (empty($select)) {
            return false;
        }
        return true;
    }
    
    /**
     * 元号なし和暦から西暦を返す
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     * @param      string $yymmdd
     * @return     string $sereki
     */
    public static function warekiToSeirekiFromYYMMDD($yymmdd) {
        
        $ymd = trim($yymmdd);
        if($ymd == '000000' || !$ymd){
            return $yymmdd;
        }
        
        $db = Msi_Sys_DbManager::getMyDb();
        if (strlen($ymd) > 0) {
            // 「全角」英数字を「半角」に変換
            $ymd = mb_convert_kana($ymd, 'a', "UTF-8");
        }
        $y = substr($ymd, 0, 2); //YY
        $m = substr($ymd, 2, 2); //MM
        $d = substr($ymd, 4, 2); //DD
        $sereki = null;
        // 最新の和暦年から比較していく
        $gengoData = DataMapper_EraMst::find($db, array('__etc_orderby' => 'era_cd DESC'));
        $curDate = New DateTime(Msi_Sys_Utils::getDate());
        if (count($gengoData) === 0) {
            throw new Msi_Logic_ExceptionInput('元号のデータが存在しません。');
        }
        foreach ($gengoData as $value) {
            $gengoDate = App_DateCalc::convJtGDate($value['era_nm']. $y . '年' . $m . '月' . $d . '日');
            if (!$gengoDate) {
                continue;
            } 
            $dataDate = New DateTime(App_DateCalc::convJtGDate($value['era_nm']. $y . '年' . $m . '月' . $d . '日'));
            $diff_y = $dataDate->diff($curDate)->y;
            // 差が一年以下だったらその元号を使用して西暦に変換する
            if ($dataDate <= $curDate || $diff_y == 0) {
                $sereki = App_DateCalc::convJtGDate($value['era_nm']. $y . '年' . $m . '月' . $d . '日');
                break;
            }
        }
        return $sereki;
    }
    
     /**
     * 西暦を返す
     *
     * <AUTHOR> Kino
     * @since      2021/01/XX
     * @param      string $yymmdd
     * @return     string $sereki
     */
    private static function seirekiFromYYYYMMDD($yymmdd) {
        $ymd = $yymmdd;
        if (strlen($ymd) > 0) {
            $ymd = mb_convert_kana($ymd, 'a', "UTF-8"); // 「全角」英数字を「半角」に変換します
        }
        $y = substr($ymd, 0, 4); //YY
        $m = substr($ymd, 4, 2); //MM
        $d = substr($ymd, 6, 2); //DD
        $sereki = $y.'/'.$m.'/'.$d;
        return $sereki;
    }
}
