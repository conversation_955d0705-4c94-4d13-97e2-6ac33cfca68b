<?php
/**
 * Logic_FileTrans_FileTransGenLogic
 *
 * ファイル転送 ファイル登録
 *
 * ・サブクラスで  _myLogic() を実装してファイルを登録してください.
 * ・_myLogic() 内エラー時は、Exception を挙げてください.
 * ・doExec() 後、$db->commit() を呼んでください.
 *
 * @category   App
 * @package    models\Logic\FocApi
 * <AUTHOR> Mihara
 * @since      2025/04/07
 * @filesource 
 */

/**
 * ファイル転送 ファイル登録
 * 
 * @category   App
 * @package    models\Logic\FocApi
 * <AUTHOR> Mihara
 * @since      2025/04/07
 */
abstract class Logic_FileTrans_FileTransGenLogic
{
    /**
     * ファイル転送 ファイル登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @param      Msi_Sys_Db $db
     * @param      array      $param     パラメタ
     * @return     mixed(array|string)   array:生成ファイル番号  string:エラーメッセージ
     */
    public static function doExec( $db, $param )
    {
        $myObj = new static( $db, $param );
        $rtn = $myObj->_doExec();
        return $rtn;
    }

    /**
     * コンストラクタ
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @param      Msi_Sys_Db $db db
     * @param      array      $param      パラメタ
     */
    protected function __construct( $db, $param )
    {
        $this->_db  = $db;
        $this->_param = $param;
    }

    /**
     * ファイル転送生成ファイル 登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @return     mixed(array|string)   array:生成ファイル番号  string:エラーメッセージ
     */
    protected function _doExec()
    {
        $db = $this->_db;
        $param = $this->_param;

        $this->_ft_type  = $ft_type  = Msi_Sys_Utils::arrDefinedOrDefault($param, 'ft_type', 'sample');
        $this->_exe_mode = $exe_mode = Msi_Sys_Utils::arrDefinedOrDefault($param, 'exe_mode', 'batch');

        try { 
            // ロック取得
            $bool = Logic_FileTrans_FileTransManUpd::lockTransFile($db, $ft_type);

            $pushObj = new Logic_FileTrans_FileTransGenPushFiles( $db, $ft_type, $exe_mode, $param );

            $this->_myLogic($pushObj, $db);

            $aFileIds = $pushObj->finish();

            return $aFileIds;
        } 
        catch ( Exception $e ) {
            $err = $e->getMessage();
            $errcode = 'err-file-gen';
            $err_detail=$err;
            Logic_FileTrans_FileTransGenErrRirekiErr::regErrDone($db, $errcode, $err_detail, $ft_type, $exe_mode, 'dummy', $param);
            return $err;
        }
    }

    /**
     * ファイル作成 本処理
     * $pushObj->pushBuf(), $pushObj->pushFile() でファイルを登録します
     *
     * <AUTHOR> Mihara
     * @since      2025/04/07
     * @param      Logic_FileTrans_FileTransGenPushFiles $pushObj
     * @param      Msi_Sys_Db $db
     * @return     void
     */
    protected function _myLogic($pushObj, $db)
    {
        throw new Exception( "サブクラスで実装してください" );
    }

}
