<?php

/**
 * Juchu_JuchuEstimate
 *
 * 見積書クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuEstimate
 * <AUTHOR> Sai
 * @since      2014/03/26
 * @version    2025/02/xx Tosaka bellmony版よりコピー
 * @filesource 
 */

/**
 * 見積書クラス
 *
 * @category   App
 * @package    controllers\Juchu\Juchu_JuchuEstimate
 * <AUTHOR>
 * @since      2014/03/26
 */
class Juchu_JuchuEstimate extends Juchu_JuchuAbstract {

    /** コード区分: 0450=>用途 */
    const CODE_KBN_YOTO_KBN = '0450';

    /** コード区分: 7802=>会員情報区分 */
    const CODE_KBN_KAIIN_INFO = '7802';

    /** コード区分: 1610=>互助会コース */
    const CODE_GOJOKAI_COSE = '1610';

    /** 分割合算区分: 0=>通常 */
    const BUN_GAS_KBN_NOM = '0';

    /** 分割合算区分: 1=>分割元 */
    const BUN_GAS_KBN_BUNMOTO = '1';

    /** 分割合算区分: 10=>合算元 */
    const BUN_GAS_KBN_GASMOTO = '10';

    /**
     * 初期情報取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/03/26
     * @return array jsonData
     */
    public function getInitData() {
//        App_DevCoverage_Manager::easyStart();
        $this->_sekoNo = $this->getReqSekoNo();
        // 施行基本情報を設定する
        $this->setInitParam();

        $data = $this->getData();
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     *
     * 見積書データを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/04/04
     * @return array 見積書データ
     */
    protected function getData() {
        $db = Msi_Sys_DbManager::getMyDb();
        // APPデータを取得する
        $dataApp = $this->getAppData();
        // 承認データを取得する
        $dataApp['shonin'] = $this->getShoninData();
        $dataApp['role_kbn'] = App_Utils::ifRolesEqualTo('sysman') || App_Utils::ifRolesEqualTo('jimu') || App_Utils::ifRolesEqualTo('manager');
        // 請求データを取得する
        $dataApp['seikyu'] = $this->getSeikyuData();
        // 商品分類データを取得する
        $dataBunrui = $this->getBunruiData();
        // 葬送儀礼～その他の明細データを取得する
        $dataCol = $this->getDetailData();
        // 伝票履歴データを取得する
        $dataHistoryCol = $this->getHistoryData();
        // 互助会データを取得する
        $dataGojokaiCol = $this->getGojokaiData();
        // コース施行のフラグ
        $courseFlg = false;
        $jutoFlg = false;
        if (count($dataGojokaiCol) > 0) {
            foreach ($dataGojokaiCol as $onerow) {
                if ($onerow['yoto_kbn'] == static::YOTO_KBN_COURSE) {
                    $courseFlg = true;
                } else if ($onerow['yoto_kbn'] == static::YOTO_KBN_JUTO) {
                    $jutoFlg = true;
                }
            }
        }
        $dataApp['courseFlg'] = $courseFlg;
        $dataApp['jutoFlg'] = $jutoFlg;
        $this->adjColData($dataCol);
        $kumotu_zei_prc = 0;
        $bechuData = $this->getBechuData($dataApp['uri_den_no']);
        if (count($bechuData) > 0) {
            foreach ($bechuData as $value) {
                $kumotu_zei_prc += $value['out_zei_prc'];
                $dataCol[] = $value;
            }
        }
        $dataApp['shohizei']['zei_prc'] += $kumotu_zei_prc;
        DataMapper_Pdf0113::adjMeisaiData($dataCol, $this->_gojokaiKbn, array('service_kbn' => 'nebiki_kbn'));
        //ログイン者の権限データを取得する
        $role = Msi_Sys_Utils::getAuthInfo('roles_arr');
        if (count($role) > 0) {
            $dataApp['login_role'] = $role[0];
        } else {
            $dataApp['login_role'] = null;
        }
        // 奉仕料のオプション設定
        $houshiOption = 0;
        $houshiOptionData = DataMapper_CodeNmMst::find($db, array('code_kbn' => '9750', 'kbn_value_cd' => static::SYS_OPTION_HOUSHI));
        if (count($houshiOptionData) > 0 && isset($houshiOptionData[0]['kbn_value_cd_num'])) {
            $houshiOption = $houshiOptionData[0]['kbn_value_cd_num'];
        }
        $dataApp['houshi_option'] = $houshiOption;
        $dataKbns = array('yoto_kbn' => DataMapper_CodeNmMst::find($db, array('code_kbn' => static::CODE_KBN_YOTO_KBN))
            , 'kaiin_info_kbn' => DataMapper_CodeNmMst::find($db, array('code_kbn' => static::CODE_KBN_KAIIN_INFO))
            , 'gojokai_cose' => DataMapper_CodeNmMst::find($db, array('code_kbn' => static::CODE_GOJOKAI_COSE)));
        // 画面データを設定する
        $data = array(
            'dataApp' => $dataApp,
            'dataCol' => $dataCol,
            'dataHistoryCol' => $dataHistoryCol,
            'dataGojokaiCol' => $dataGojokaiCol,
            'dataBunrui' => $dataBunrui,
            'dataKbns' => $dataKbns,
        );
        return $data;
    }

    /**
     *
     * APPデータを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/26
     * @return array APPデータ
     */
    private function getAppData() {
        $db = Msi_Sys_DbManager::getMyDb();
        // 見積基本情報を取得する
        $estimate = $this->selectSekoKihon();
        if (isset($estimate['om_est_harai_no'])) {
            $estimate['om_est_input'] = $estimate['om_est_harai_no'];
        } else if (isset($estimate['om_est_kake_prc'])) {
            $estimate['om_est_input'] = $estimate['om_est_kake_prc'];
        }
        // 奉仕率情報を取得する
        $hoshi = $this->getHosiritu();
        // 消費税情報を取得する
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        // 互助会加入情報を取得する
        $gojokai = $this->getGojokaiMember(10);
        // 消費税等情報を取得する
        if (App_Utils::isMitsuInJuchu() || App_Utils::isHoujiInJuchu() || App_Utils::isOrdermadeInJuchu()) {
            $shohizei = App_MitsuLib::getKingaku($db, $this->_sekoNo, $this->_sekoNoSub, $this->getDataKbn());
            $denpyoData = DataMapper_JuchuDenpyo::findDenpyo($db,array('denpyo_no' => $this->getJuchudenpyoNo()));
        } else {
            $shohizei = App_SeikyuLib::getKingaku($db, $this->_sekoNo, $this->_sekoNoSub, $this->getDataKbn());
            $denpyoData = DataMapper_UriageDenpyo::find($db,array('uri_den_no' => $this->getUriagedenpyoNo()));
        }
        $kaihiData = array('keiyaku_prc'=>0,'harai_prc'=>0,'wari_prc'=>0,'zan_prc'=>0,'kaihi_zei_prc'=>0,'early_use_prc'=>0,'early_uze_zei'=>0,'warimashi_prc'=>0,'juto_prc'=>0,'juto_zan_prc'=>0);
        if (count($denpyoData) > 0) {
            $kaihiData['keiyaku_prc'] = $denpyoData[0]['sougi_keiyaku_prc'];
            $kaihiData['harai_prc'] = $denpyoData[0]['sougi_harai_prc'];
            $kaihiData['wari_prc'] = $denpyoData[0]['sougi_wari_prc'];
            $kaihiData['zan_prc'] = $denpyoData[0]['sougi_keiyaku_prc'] + $denpyoData[0]['sougi_harai_prc'] + $denpyoData[0]['sougi_wari_prc'];
            $kaihiData['kaihi_zei_prc'] = $denpyoData[0]['sougi_keiyaku_zei'] + $denpyoData[0]['sougi_wari_zei'];
            $kaihiData['early_use_prc'] = $denpyoData[0]['sougi_early_use_cost'];
            $kaihiData['early_use_zei'] = $denpyoData[0]['sougi_early_use_cost_zei'];
            $kaihiData['warimashi_prc'] = $denpyoData[0]['sougi_premium_service_prc'];
            $kaihiData['juto_prc'] = $denpyoData[0]['sougi_tokuten_prc'] + $denpyoData[0]['n_free9'] + $denpyoData[0]['n_free10'];
            $kaihiData['juto_zan_prc'] = $denpyoData[0]['n_free5'];
        }

        $estimate['hoshi_ritu_cd'] = $hoshi['hoshi_ritu_cd'];
        $estimate['hoshi_ritu_org'] = $hoshi['zei_rtu_org'];
        $estimate['hoshi_ritu'] = $hoshi['zei_rtu'];
        $estimate['hasu_kbn'] = $hoshi['hasu_kbn'];
        $estimate['shohi_zei_rtu'] = (int) $taxInfo['zei_rtu'];
        $estimate['shohi_zei_cd'] = (int) $taxInfo['zei_cd'];
        $estimate['sosogorei_plan_a_nm'] = $this->getChubunruiName('0020'); // 0020:【Ａ】葬送儀礼費用基本プラン
        $estimate['sosogorei_plan_b_nm'] = $this->getChubunruiName('0030'); // 0030:【Ｂ】葬送儀礼費用プラン外選択商品
        $estimate['kaiin_no'] = $this->_kaiinNo;
        $estimate['seko_plan_cd'] = $this->_sekoPlanCd;
        $estimate['no_seko_plan_msg'] = '基本プランが選択されていないため、見積書を作成することができません。';
        $estimate['gojokai'] = $gojokai;
        $estimate['gojokai_zeirtu'] = DataMapper_SekoGojokaiMember::getZeirtu($db, array('seko_no' => $this->_sekoNo));
        $estimate['early_zeirtu'] = DataMapper_SekoGojokaiMember::getEarlyZeirtu($db, array('seko_no' => $this->_sekoNo));
        $estimate['meigi_zeirtu'] = DataMapper_SekoGojokaiMember::getMeigiZeirtu($db, array('seko_no' => $this->_sekoNo));
        $estimate['kake_zeirtu'] = DataMapper_SekoGojokaiMember::getKakeZeirtu($db, array('seko_no' => $this->_sekoNo));
        $estimate['shohizei'] = $shohizei;
        $estimate['kaihiData'] = $kaihiData;
        $early_zei_rtu = null;
        $meigi_zei_rtu = null;
        if (count($gojokai) > 0) {
            if (isset($gojokai[0]['early_zei_rtu'])) {
                $early_zei_rtu = $gojokai[0]['early_zei_rtu'];
            }
            if (isset($gojokai[0]['meigi_zei_rtu'])) {
                $meigi_zei_rtu = $gojokai[0]['meigi_zei_rtu'];
            }
        }
        $estimate['shohizei']['early_zei_rtu'] = $early_zei_rtu;
        $estimate['shohizei']['meigi_zei_rtu'] = $meigi_zei_rtu;
        // コード名称マスタデータを取得する
        $estimate['printKbn'] = DataMapper_MsterGetLib::GetCodeNameMst($db, '0910');
        // 会社情報会員名取得する
        $estimate['kain_nm'] = App_Utils::getKainNm();
        // 消費税名または掛金消費税差額かのフラグを取得する
        $estimate['zei_flg'] = App_Utils::getZeiNmFlg($this->_sekoNo);
        // コンビニ入金票発行区分
        $estimate['is_konbini_hako_kbn'] = App_Utils::KonbiniHakoKbn();
        $estimate['uri_den_no'] = $this->getUriagedenpyoNo();
        return $estimate;
    }

    /**
     *
     * 葬送儀礼中分類名を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/26
     * @param string $chubunruiCd 中分類コード
     * @return string 中分類名
     */
    private function getChubunruiName($chubunruiCd) {
        $chubunruiNm = null;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                cbm.chu_bunrui_nm -- 中分類名
            FROM
                shohin_chu_bunrui_mst cbm 
            WHERE
                cbm.delete_flg = 0
            AND cbm.chu_bunrui_cd = :chu_bunrui_cd
                ";
        $select = $db->easySelOne($sql, array('chu_bunrui_cd' => $chubunruiCd));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $chubunruiNm = $select['chu_bunrui_nm'];
        }
        return $chubunruiNm;
    }

    /**
     *
     * 承認情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/16
     * @return array 承認情報
     */
    private function getShoninData() {
        // 施行担当印鑑
        $dataShonin["inkan_oid"] = App_Utils::getTantoInkanOid($this->_selectSekoKihon['jichu_kakute_cd']);
        // 見積確定
        $dataShonin["juchukakutei_dt"] = Msi_Sys_Utils::getDatetime(App_DateCalc::strToEpoch($this->_juchuKakuteiYMD), 'm/d H:i');
        // 売上伝票の承認情報を取得する
        $dateUriShonin = $this->getUriageShoninInfo();
        $dataShonin["hasShonin"] = $this->hasUriageShonin($dateUriShonin);
        $dataShonin["hasShoninAll"] = $this->hasUriageShonin($dateUriShonin, 'ALL');
        return array_merge($dataShonin, $dateUriShonin);
    }

    /**
     *
     * 請求情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/4/18
     * @return array 請求情報
     */
    protected function getSeikyuData() {
        return array();
    }

    /**
     *
     * 葬送儀礼～その他の明細データを取得する
     *  ※SQL修正する場合、Saiken_SeikyuShoninのSQLも修正が必要
     * <AUTHOR> Sai
     * @since 2014/3/26
     * @version 2015/05/03 施行発注管理情報のJOINにデータ区分を追加 Kayo
     * @return array 明細データ
     */
    protected function getDetailData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            dm.msi_no -- 明細番号
            ,dm.dai_bunrui_cd -- 大分類コード
            ,dm.chu_bunrui_cd -- 中分類コード
            ,cbm.chu_bunrui_nm -- 中分類名
            ,dm.shohin_kbn -- 商品区分
            ,dm.shohin_kbn_nm-- 商品区分名
            ,dm.shohin_cd -- 商品コード
            ,dm.shohin_nm -- 商品名
            ,dm.shohin_tkiyo_nm -- 商品摘要名
            ,dm.mokuteki_kbn -- 目的区分
            ,dm.juchu_suryo -- 数量
            ,dm.juchu_tnk -- 単価
            ,dm.juchu_suryo * juchu_tnk AS juchu_prc -- 受注金額
            ,dm.gojokai_nebiki_prc -- 付帯値引き
            ,dm.nebiki_prc -- 値引き
            ,dm.juchu_suryo * juchu_tnk + dm.gojokai_nebiki_prc + dm.nebiki_prc AS juchu_prc_kei -- 受注金額差引計
            ,dm.hoshi_umu_kbn -- 奉仕料有無区分
            ,dm.hoshi_prc -- 奉仕料
            ,sm.tnk_chg_kbn-- 売上単価変更区分
            ,sm.nm_input_kbn-- 名称入力区分
            ,sbm.hachu_kbn -- 発注書区分
            ,sm.tani_cd-- 単位コード
            ,sm.uri_kamoku_cd-- 科目コード
            ,dm.zei_kbn -- 消費税区分
            ,dm.gen_tnk-- 原価単価
            ,CASE  -- タブ区分 1:葬送儀礼, 2:返礼品, 3:飲食費, 4:立替, 5:その他タブ
                WHEN dm.dai_bunrui_cd = '0010' THEN '1'
                WHEN dm.dai_bunrui_cd = '0020' THEN '2'
                WHEN dm.dai_bunrui_cd = '0030' THEN '3'
                WHEN dm.dai_bunrui_cd = '0040' THEN '3'
                WHEN dm.dai_bunrui_cd = '0060' THEN '4'
                WHEN dm.dai_bunrui_cd = '0050' THEN '5'
                WHEN dm.dai_bunrui_cd = '0070' THEN '5'
            END tab_kbn
            ,CASE 
                spsm.service_kbn 
                WHEN 1 THEN '1' 
                WHEN 2 THEN '2' 
                ELSE '0' 
            END nebiki_kbn -- 値引き区分 0:通常商品 1:互助会 2:互助会値引き
            ,'1' AS checkable_kbn -- 行選択可能区分 0:選択不可, 1:選択可
            ,CASE dm.chu_bunrui_cd -- 選択行下に行追加可能区分 0:追加不可, 1:追加可
                WHEN '0020' THEN '0'
                ELSE '1'
            END add_down_kbn
            ,CASE dm.chu_bunrui_cd -- 行削除可能区分 0:削除不可, 1:削除可
                WHEN '0020' THEN '0'
                ELSE '1'
            END del_kbn
            ,dm.add_kbn -- 追加区分 0:トランデータ, 1:見積画面新規入力データ
            ,COALESCE(shi.siire_cd, dm.siire_cd) AS siire_cd       -- 仕入コード
            ,COALESCE(siirem.siire_lnm, dm.siire_lnm) AS siire_lnm -- 仕入名     
            ,shi.order_flg      -- 発注済み 0:未発注 1:発注済み
            ,sbm.print_group_cd
            ,dm.out_zei_prc
            ,dm.in_zei_prc
            ,dm.zei_cd  -- 2019/04/30 mihara keigen
            ,dm.reduced_tax_rate --  2019/04/30 mihara keigen
            ,dm.shohin_bumon_cd
            ,dm.upgrade_kbn
            ,dm.n_free2
            ,dm.tani_nm
        FROM
            (
                SELECT
                    m1.msi_no
                    ,m1.denpyo_kbn
                    ,m1.seko_no
                    ,h1.juchu_ymd
                    ,m1.disp_no
                    ,m1.add_kbn
                    ,m1.dai_bunrui_cd
                    ,m1.chu_bunrui_cd
                    ,m1.shohin_kbn
                    ,skm.shohin_kbn_nm
                    ,m1.shohin_cd
                    ,m1.shohin_nm
                    ,m1.shohin_tkiyo_nm
                    ,m1.juchu_suryo
                    ,m1.juchu_tnk
                    ,m1.hoshi_umu_kbn
                    ,m1.hoshi_prc
                    ,m1.gojokai_nebiki_prc
                    ,m1.nebiki_prc
                    ,m1.zei_kbn        
                    ,m1.gen_tnk
                    ,m1.mokuteki_kbn
                    ,m1.siire_cd
                    ,m1.siire_lnm
                    ,m1.out_zei_prc
                    ,m1.in_zei_prc
                    ,m1.zei_cd  -- 2019/04/30 mihara keigen
                    ,m1.reduced_tax_rate -- 2019/04/30 mihara keigen
                    ,m1.shohin_bumon_cd
                    ,h1.data_kbn		
                    ,m1.upgrade_kbn
                    ,m1.n_free2 -- 割引率
                    ,tani.tani_nm
                FROM juchu_denpyo h1		
                LEFT OUTER JOIN juchu_denpyo_msi m1
                    ON h1.denpyo_no = m1.denpyo_no
                    AND 0 = m1.delete_flg	
                INNER JOIN shohin_kbn_mst skm
                    ON  (m1.shohin_kbn = skm.shohin_kbn
                        AND skm.delete_flg = 0
                        )	
                LEFT JOIN tani_mst tani
                    ON m1.tani_cd = tani.tani_cd
                    AND 0 = tani.delete_flg
                WHERE h1.delete_flg = 0
                    AND h1.seko_no = :seko_no
                    AND h1.seko_no_sub = :seko_no_sub
                    AND h1.data_kbn IN (1,2)
                    --AND m1.data_sbt IN (1, 2, 3, 4, 5, 6, 7) --データ種別 1：葬送儀礼 2：返礼品　3：料理 4：壇払い 5：別途費用 6：立替金 7：値引き
            ) dm
            INNER JOIN shohin_chu_bunrui_mst cbm
                ON  (dm.chu_bunrui_cd = cbm.chu_bunrui_cd
                    AND cbm.delete_flg = 0
                    )
            INNER JOIN shohin_mst sm
                ON  (dm.shohin_cd = sm.shohin_cd
                    AND dm.shohin_bumon_cd = sm.bumon_cd
                    AND sm.hihyoji_kbn = 0
                    AND sm.delete_flg = 0
                    )
            LEFT OUTER JOIN shohin_bunrui_mst sbm
                ON  (dm.dai_bunrui_cd = sbm.dai_bunrui_cd
                    AND dm.chu_bunrui_cd  = sbm.chu_bunrui_cd
                    AND dm.shohin_kbn  = sbm.shohin_kbn
                    AND dm.shohin_cd  = sbm.shohin_cd
                    AND dm.shohin_bumon_cd  = sbm.bumon_cd
                    AND sbm.delete_flg = 0
                )
            LEFT OUTER JOIN seko_plan_smsi_mst spsm
                ON  (dm.dai_bunrui_cd = spsm.dai_bunrui_cd
                    AND dm.chu_bunrui_cd  = spsm.chu_bunrui_cd
                    AND dm.shohin_kbn  = spsm.shohin_kbn
                    AND dm.shohin_cd  = spsm.shohin_cd
                    AND dm.shohin_bumon_cd = spsm.bumon_cd
                    AND dm.msi_no = spsm.seko_plan_uchiwk_no
                    AND TO_CHAR(spsm.tekiyo_st_date,'YYYY/MM/DD') <= TO_CHAR(dm.juchu_ymd,'YYYY/MM/DD')
                    AND TO_CHAR(spsm.tekiyo_ed_date,'YYYY/MM/DD') >= TO_CHAR(dm.juchu_ymd,'YYYY/MM/DD')
                    AND spsm.seko_plan_cd = :seko_plan_cd
                    AND spsm.delete_flg = 0
                )
            LEFT OUTER JOIN seko_hachu_info shi
                ON  (dm.seko_no = shi.seko_no
                    AND dm.msi_no = shi.jc_msi_no
                    AND dm.shohin_cd = shi.shohin_cd
                    AND dm.data_kbn = shi.data_kbn		-- 2015/05/03 ADD Kayo
                    AND shi.hachu_no_moto IS NULL
                    AND shi.delete_flg = 0
                )
            LEFT OUTER JOIN siire_mst siirem			
                ON shi.siire_cd = siirem.siire_cd		
                AND 0 = siirem.delete_flg
            ORDER BY 
                dm.dai_bunrui_cd
                --,CASE WHEN dm.upgrade_kbn IN (1,2) THEN 1 ELSE 2 END
                ,sbm.mitumori_print_seq
                ,dm.disp_no
                ";
        $select = $db->easySelect($sql, array(
            'seko_no' => $this->_sekoNo,
            'seko_no_sub' => $this->_sekoNoSub,
            'seko_plan_cd' => $this->_sekoPlanCd,
        ));
        return $select;
    }

    /**
     *
     * 伝票履歴データを取得する
     * <AUTHOR> Tosaka
     * @since 2020/3/30
     * @return array 明細データ
     */
    protected function getHistoryData() {

        $data_kbn = $this->getDataKbn();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT T.*
            FROM (
                SELECT 
                    '最新' AS denpyo_status
                    ,NULL AS cre_ts
                    ,est_bm.bumon_lnm AS est_shikijo_nm
                    ,NULL AS disp_no
                    ,jd.juchu_prc_sum 
                    + jd.juchu_hepn_sum 
                    + jd.juchu_nebk_sum 
                    + jd.hoshi_prc_sum 
                    + jd.out_zei_prc
                    + jd.sougi_keiyaku_prc + jd.sougi_harai_prc
                    + jd.sougi_keiyaku_zei + jd.sougi_wari_zei  -- 会費消費税
                    + jd.sougi_wari_prc
                    + jd.sougi_premium_service_prc
                    + jd.sougi_early_use_cost 
                    + jd.sougi_early_use_cost_zei   -- 早期利用費
                    + jd.sougi_meigi_chg_cost       -- 名義変更手数料
                    + jd.sougi_meigi_chg_cost_zei   -- 名義変更手数料
                    + jd.etc_harai_prc              -- 金額充当
                    + jd.sougi_tokuten_prc + jd.n_free9 + jd.n_free10 -- 完納充当
                    + jd.n_free5 -- 完納充当(残額)
                    AS juchu_prc
                FROM juchu_denpyo jd
                LEFT JOIN bumon_mst est_bm
                    ON est_bm.bumon_cd = jd.est_shikijo_cd
                    AND est_bm.delete_flg = 0
                WHERE jd.delete_flg = 0
                    AND jd.seko_no = :seko_no
                    AND jd.data_kbn = :data_kbn
                UNION ALL
                SELECT 
                    CAST(jdh.history_no AS VARCHAR) AS denpyo_status
                    ,TO_CHAR(jdh._cre_ts, 'YYYY/MM/DD HH24:MI') AS cre_ts
                    ,est_bm.bumon_lnm AS est_shikijo_nm
                    ,jdh.history_no AS disp_no
                    ,jdh.juchu_prc_sum 
                    + jdh.juchu_hepn_sum 
                    + jdh.juchu_nebk_sum 
                    + jdh.hoshi_prc_sum 
                    + jdh.out_zei_prc
                    + jdh.sougi_keiyaku_prc + jdh.sougi_harai_prc
                    + jdh.sougi_keiyaku_zei + jdh.sougi_wari_zei    -- 会費消費税
                    + jdh.sougi_wari_prc
                    + jdh.sougi_premium_service_prc
                    + jdh.sougi_early_use_cost 
                    + jdh.sougi_early_use_cost_zei   -- 早期利用費
                    + jdh.sougi_meigi_chg_cost       -- 名義変更手数料
                    + jdh.sougi_meigi_chg_cost_zei   -- 名義変更手数料
                    + jdh.etc_harai_prc              -- 金額充当
                    + jdh.sougi_tokuten_prc + jdh.n_free9 + jdh.n_free10 -- 完納充当
                    + jdh.n_free5 -- 完納充当(残額)
                    AS juchu_prc
                FROM juchu_denpyo_history jdh
                LEFT JOIN bumon_mst est_bm
                    ON est_bm.bumon_cd = jdh.est_shikijo_cd
                    AND est_bm.delete_flg = 0
                WHERE jdh.delete_flg = 0
                    AND jdh.seko_no = :seko_no
                    AND jdh.data_kbn = :data_kbn
            ) AS T
            ORDER BY T.disp_no DESC
                ";
        $select = $db->easySelect($sql, array(
            'seko_no' => $this->_sekoNo,
            'data_kbn' => $data_kbn,
        ));
        return $select;
    }

    /**
     *
     * 互助会データを取得する
     * <AUTHOR> Tosaka
     * @since 2020/3/30
     * @return array 明細データ
     */
    protected function getGojokaiData() {

        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT T.*
            FROM (
                SELECT 
                    sgm.*
                    ,free_kbn1 AS kaiin_info_kbn
                    ,TO_CHAR(sgm.kanyu_dt, 'YYYY/MM/DD') AS kanyu_dt
                    ,sgm.harai_gaku + sgm.wari_gaku AS use_gaku
                    ,CASE WHEN sgm.yoto_kbn = 4 THEN '0' ELSE '1' END AS change_check
                FROM seko_gojokai_member sgm
                WHERE sgm.delete_flg = 0
                    AND sgm.seko_no = :seko_no
            ) AS T
                ";
        $select = $db->easySelect($sql, array(
            'seko_no' => $this->_sekoNo,
        ));
        return $select;
    }

    /**
     * 保存処理
     *
     * <AUTHOR> Sai
     * @since 2014/03/31
     */
    public function save() {
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        if ($this->isKakuteiOutNg()) {
            return;
        }
        // 明細削除データ
        $dataDelCol = Msi_Sys_Utils::json_decode($req->getPost('dataDelColJson'));
        // データ加工処理
        $dataCol = $this->getDataCol($req);

        $db = Msi_Sys_DbManager::getMyDb();
        // 発注済みのチェック 2018/12/21 ADD Kayo
        $msg = App_HachuLib::chkHachuiOrder($db, $this->_sekoNo, $dataCol, $dataDelCol);
        if (strlen($msg) > 0) {
            $data = array(
                'status' => 'NG',
                'msg' => $msg
            );
            Msi_Sys_Utils::outJson($data);
            return;
        }

//        Msi_Sys_Utils::profilerMark('saveJuchu-start');
        // 受注伝票を保存する
        $cnt = $this->saveJuchu($db, $dataApp, $dataCol, $dataDelCol);
//        Msi_Sys_Utils::profilerMark('saveJuchu-end');
        // 施行発注管理情報を保存する
        $cnt += $this->saveHachuInfo($db, $dataApp, $dataCol, $dataDelCol);
        // 互助会値引再設定を行う
        if (!$this->isMitsuKakutei()) { // 見積未確定
            $dataApp = array();
            $msiData = $this->getJuchuMsiData();
            App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
            $cnt += $this->saveJuchu($db, $dataApp, $msiData);
        } else {
            // 施行金額確定されていたらスルー
            $status_kbn = $this->getSekoStatusKbn();
            if (isset($status_kbn) && $status_kbn < static::STATUS_KBN_SEKOKAKUTEI) {
                $dataApp = array();
                $delCol = array();
                $msiData = $this->getUriageMsiData();
                App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
                $cnt += $this->saveUriage($db, $dataApp, $msiData, $delCol);
            }
        }

        $sbt = 1;
        $item = 12;
        if ($this->_moushiKbn === self::MOUSHI_KBN_HOUJI) {
            $sbt = 4;
            $item = 15;
        }
        // 施行管理情報を更新する
        App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, $sbt, $item);
        $db->commit();
        $data = $this->getData();
        // 画面データを設定する
        $data['dataSideMenu'] = $this->getSideMenuData();
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '更新しました。';
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 見積データ加工処理
     *
     * <AUTHOR> Sai
     * @since 2014/03/31
     * @param request $req リクエスト
     */
    private function getDataCol($req) {
        $dataCol = array();
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));
        $dataCol1 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson1')); // 葬送儀礼
        $dataCol2 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson2')); // 返礼品
        $dataCol3_1 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson3_1')); // 飲食費(料理) 中分類コード:0070
        $dataCol3_2 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson3_2')); // 飲食費(壇払い) 中分類コード:0080
        $dataCol4 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson4')); // 立替金
        $dataCol5_1 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson5_1')); // その他(別途費用) 中分類コード:0090
        $dataCol5_2 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson5_2')); // その他(値引き) 中分類コード:0120
        $dataCol6 = Msi_Sys_Utils::json_decode($req->getPost('dataColJson6')); // パックプラン
        // 葬送儀礼処理
        if ($changeFlg['meisaiChangeFlg1']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol1);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol1);
        }
        // 返礼品処理
        if ($changeFlg['meisaiChangeFlg2']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol2);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol2);
        }
        // 飲食費処理
        if ($changeFlg['meisaiChangeFlg3']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol3_1);
            $this->setMitsuInfo($dataCol3_2);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol3_1, $dataCol3_2);
        }
        // 立替金処理
        if (isset($changeFlg['meisaiChangeFlg4']) && $changeFlg['meisaiChangeFlg4']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol4);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol4);
        }
        // その他処理
        if (isset($changeFlg['meisaiChangeFlg5']) && $changeFlg['meisaiChangeFlg5']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol5_1);
            $this->setMitsuInfo($dataCol5_2);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol5_1, $dataCol5_2);
        }
        // パックプラン
        if (isset($changeFlg['meisaiChangeFlg6']) && $changeFlg['meisaiChangeFlg6']) {
            // 見積情報を設定する
            $this->setMitsuInfo($dataCol6);
            // マージする
            $dataCol = array_merge($dataCol, $dataCol6);
        }
        return $dataCol;
    }

    /**
     * 見積の受注伝票明細情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/03/31
     * @param array &$dataCol 受注伝票明細
     */
    private function setMitsuInfo(&$dataCol) {
        for ($i = 0; $i < count($dataCol); $i++) {
            // 表示順(連番)
            $dataCol[$i]['disp_no'] = $i + 1;
            // 伝票区分 1：受注
            if (!isset($dataCol[$i]['denpyo_kbn'])) {
                $dataCol[$i]['denpyo_kbn'] = 1;
            } else if ($dataCol[$i]['denpyo_kbn'] === '2') {
                // 返品の場合
            }
            // 部門コード
            if (!isset($dataCol[$i]['bumon_cd'])) {
                $dataCol[$i]['bumon_cd'] = $this->_bumonCd;
            }
            // 値引き額
            if (!isset($dataCol[$i]['nebiki_prc'])) {
                $dataCol[$i]['nebiki_prc'] = 0;
            }
            // 納入先情報デフォルト
            $dataCol[$i]['nonyu_cd'] = null;
            $dataCol[$i]['nonyu_nm'] = null;
            $dataCol[$i]['nonyu_knm'] = null;
            $dataCol[$i]['nonyu_yubin_no'] = null;
            $dataCol[$i]['nonyu_addr1'] = null;
            $dataCol[$i]['nonyu_addr2'] = null;
            $dataCol[$i]['nonyu_tel'] = null;
            $dataCol[$i]['nonyu_fax'] = null;
            $dataCol[$i]['nonyu_dt'] = null;
            // 仕入先情報デフォルト
            if (!isset($dataCol[$i]['siire_cd'])) {
                $dataCol[$i]['siire_cd'] = null;
            }
            if (!isset($dataCol[$i]['siire_lnm'])) {
                $dataCol[$i]['siire_lnm'] = null;
            }
        }
    }

    /**
     *
     * データ種別を取得する
     * 1 => '葬送儀礼', 2 => '返礼品', 3 => '料理', 4 => '壇払い', 5 => '別途費用', 6 => '立替金', 7 => '値引き'
     *
     * <AUTHOR> Sai
     * @since 2014/3/17
     * @param array $record 受注伝票明細
     * @return string 3:料理
     */
    protected function getDataSbt($record) {
        $datasbt = '';
        $dabunrui = $record['dai_bunrui_cd'];
        if ($dabunrui === '0010') { // 葬儀
            $datasbt = '1';
        } else if ($dabunrui === '0020') {  // 返礼品
            $datasbt = '2';
        } else if ($dabunrui === '0030') {  // 料理
            $datasbt = '4';
        } else if ($dabunrui === '0050') {  // 別途費用
            $datasbt = '5';
        } else if ($dabunrui === '0060') {  // 立替金
            $datasbt = '6';
        } else if ($dabunrui === '0070') {
            $datasbt = '7';
        }
        return $datasbt;
    }

    /**
     *
     * 受注伝票明細テーブル共通情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/4/1
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @return array 受注伝票明細情報
     */
    public function setJuchudenpyoMsiComInfo($dataApp, $record) {
        $juchuDenpyoMsi = array();
        if ($record['upsert'] === 1) { // 登録の場合
            $juchuDenpyoMsi = parent::setJuchudenpyoMsiComInfo($dataApp, $record);
            $juchuDenpyoMsi['add_kbn'] = 1; // 追加区分 
            $juchuDenpyoMsi['data_sbt'] = $this->getDataSbt($juchuDenpyoMsi); // データ種別 
        } else { // 更新の場合
            // 追加区分 0:トランデータ, 1:見積画面新規入力データ
            if ($record['add_kbn'] === '0') {
                // 見積書画面以外データは表示順のみ更新する
                $juchuDenpyoMsi['disp_no'] = $record['disp_no'];  // 表示順
                $juchuDenpyoMsi['gojokai_nebiki_prc'] = $record['gojokai_nebiki_prc'];  // 互助会値引額
                // 受注金額が0円かつ付帯特典、割引額がいずれも0円の場合はNULL
                if ($juchuDenpyoMsi['uri_prc'] == 0 && $juchuDenpyoMsi['nebiki_prc'] == 0 && $juchuDenpyoMsi['gojokai_nebiki_prc'] == 0) {
                    $juchuDenpyoMsi['n_free2'] = null;
                } else {
                    $juchuDenpyoMsi['n_free2'] = $record['n_free2'];
                }
            } else {
                $juchuDenpyoMsi['disp_no'] = $record['disp_no'];  // 表示順
                $juchuDenpyoMsi['bumon_cd'] = $record['bumon_cd'];  // 売上部門コード
                $juchuDenpyoMsi['shohin_bumon_cd'] = $record['shohin_bumon_cd'];  // 売上部門コード
                $juchuDenpyoMsi['mokuteki_kbn'] = $record['mokuteki_kbn'];  // 使用目的区分
                $juchuDenpyoMsi['dai_bunrui_cd'] = $record['dai_bunrui_cd'];  // 大分類コード
                $juchuDenpyoMsi['chu_bunrui_cd'] = $record['chu_bunrui_cd'];  // 中分類コード
                $juchuDenpyoMsi['shohin_kbn'] = $record['shohin_kbn'];  // 商品区分
                $juchuDenpyoMsi['shohin_cd'] = $record['shohin_cd'];  // 商品コード
                $juchuDenpyoMsi['shohin_nm'] = $record['shohin_nm'];  // 商品名
                $juchuDenpyoMsi['shohin_tkiyo_nm'] = $record['shohin_tkiyo_nm'];  // 商品摘要名
                $juchuDenpyoMsi['juchu_suryo'] = $record['juchu_suryo'];  // 商品数量
                $juchuDenpyoMsi['juchu_tnk'] = $record['juchu_tnk'];  // 単価
                $juchuDenpyoMsi['juchu_prc'] = $this->getPrc($record); // 受注金額
                $juchuDenpyoMsi['nebiki_prc'] = $record['nebiki_prc'];  // 値引額
                $juchuDenpyoMsi['gojokai_nebiki_prc'] = $record['gojokai_nebiki_prc'];  // 互助会値引額
                $juchuDenpyoMsi['gen_tnk'] = $record['gen_tnk'];  // 原価単価
                $juchuDenpyoMsi['gen_gaku'] = $this->getGenka($record);  // 原価金額
                $juchuDenpyoMsi['arari_gaku'] = $this->getAari($record); // 粗利益額
                $juchuDenpyoMsi['hoshi_prc'] = $record['hoshi_prc'];  // 奉仕料金額
                $juchuDenpyoMsi['hoshi_umu_kbn'] = $record['hoshi_umu_kbn'];  // 奉仕有無区分
                $juchuDenpyoMsi['data_sbt'] = $this->getDataSbt($juchuDenpyoMsi); // データ種別 
                $juchuDenpyoMsi['siire_cd'] = $record['siire_cd'];  // 仕入先コード
                $juchuDenpyoMsi['siire_lnm'] = $record['siire_lnm'];  // 仕入先名
                // 受注金額が0円かつ付帯特典、割引額がいずれも0円の場合はNULL
                if ($juchuDenpyoMsi['juchu_prc'] == 0 && $juchuDenpyoMsi['nebiki_prc'] == 0 && $juchuDenpyoMsi['gojokai_nebiki_prc'] == 0) {
                    $juchuDenpyoMsi['n_free2'] = null;
                } else {
                    $juchuDenpyoMsi['n_free2'] = $record['n_free2'];
                }
                // 消費税項目を設定する
                $this->setZeiInfo($dataApp, $juchuDenpyoMsi, $record, $juchuDenpyoMsi['juchu_prc'] + $juchuDenpyoMsi['hoshi_prc'] + $juchuDenpyoMsi['gojokai_nebiki_prc'] + $juchuDenpyoMsi['nebiki_prc']);
            }
        }
        return $juchuDenpyoMsi;
    }

    /**
     *
     * 施行発注管理共通情報を設定する
     *
     * <AUTHOR> Sai
     * @since 2014/9/24
     * @param array $dataApp 画面の情報
     * @param array $record  画面のグリッドデータ(一行)
     * @param array $reportKanri レポート管理マスタ
     * @return array 施行発注管理情報
     */
    public function setHachuComInfo($dataApp, $record, $reportKanri) {

        $hachuInfo = array();
        if (isset($record['upsert']) && $record['upsert'] === 1) { // 登録の場合
            $hachuInfo = parent::setHachuComInfo($dataApp, $record, $reportKanri);
        } else {
            $hachuInfo['report_cd'] = $reportKanri['report_cd'];  // 帳票コード
            $hachuInfo['ha_rp_cd'] = $reportKanri['ha_rp_cd'];  // 発注書区分コード
            $hachuInfo['ha_rp_kbn'] = $record['hachu_kbn'];  // 発注書区分
            $hachuInfo['ha_entry_kbn'] = $reportKanri['ha_entry_kbn'];  // 発注書入力区分
            $hachuInfo['ha_syori_kbn'] = $reportKanri['ha_syori_kbn'];  // 発注書処理区分
            $hachuInfo['dai_bunrui_cd'] = $record['dai_bunrui_cd'];  // 大分類コード
            $hachuInfo['chu_bunrui_cd'] = $record['chu_bunrui_cd'];  // 中分類コード
            $hachuInfo['shohin_kbn'] = $record['shohin_kbn'];  // 商品区分
            $hachuInfo['shohin_cd'] = $record['shohin_cd'];  // 商品コード
            $hachuInfo['shohin_bumon_cd'] = $record['shohin_bumon_cd'];  // 商品部門コード
            $hachuInfo['shohin_nm'] = $record['shohin_nm'];  // 商品名
            $hachuInfo['shohin_tkiyo_nm'] = $record['shohin_tkiyo_nm'];  // 商品摘要名
            $hachuInfo['hanbai_tnk'] = $record['juchu_tnk'];  // 販売単価
            $hachuInfo['hachu_tnk'] = $record['gen_tnk'];  // 発注単価
            $hachuInfo['hachu_suryo'] = $record['juchu_suryo'];  // 商品数量
            $hachuInfo['hachu_prc'] = $record['gen_tnk'] * $record['juchu_suryo'];  // 発注金額
            $hachuInfo['tani_cd'] = $record['tani_cd'];  // 単位コード
            $hachuInfo['siire_cd'] = $record['siire_cd'];  // 仕入先コード
            // 見積確定されている場合は売上伝票Noと明細Noを設定する
            if (isset($this->_juchuKakuteiYMD)) {
                $hachuInfo['uri_denpyo_no'] = $this->getUriagedenpyoNo();  // 売上伝票No
                $hachuInfo['uri_msi_no'] = $record['msi_no'];  // 売上伝票明細No
            } else {
                $hachuInfo['uri_denpyo_no'] = null;  // 売上伝票No
                $hachuInfo['uri_msi_no'] = null;  // 売上伝票明細No
            }
        }
        return $hachuInfo;
    }

    /**
     * 見積確定処理
     *
     * <AUTHOR> Sai
     * @since 2014/04/04
     */
    public function mitsukakutei() {
//        App_DevCoverage_Manager::easyStart();
        $req = Msi_Sys_Utils::getRequestObject();
        $db = Msi_Sys_DbManager::getMyDb();
        try {
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            // データ加工処理
            $dataCol = $this->getDataCol($req);
            $this->_sekoNo = $dataApp['seko_no'];
            $cnt = 0;
            // 施行基本情報を設定する
            $this->setInitParam();
            // 施行日程情報チェック
            list($errFlg, $errMsg) = $this->checkNitei($db);
            if (!$errFlg) {
                $this->outWarnJson(trim($errMsg, ',') . "が設定されていないため、見積確定ができません。");
                return;
            }
            //見積式場と施行日程の式場が一致しない場合は見積確定ができない
            // 法事はスルーする
            if ($this->_moushiKbn != static::MOUSHI_KBN_HOUJI) {
                if (!$this->checkShikijo()) {
                    $this->outWarnJson('葬儀・告別式の施設と見積式場が一致しないため、見積を確定することができません。');
                    return;
                }
            }
            // 会員区分が互助会の時と企業の場合のチェック処理
            list($flg, $msg) = $this->checkKaiinKbn();
            if (!$flg) {
                $this->outWarnJson($msg);
                return;
            }
            // 搬送施行チェック処理
//            list($flg, $msg) = $this->checkHansoData();
//            if (!$flg) {
//                $this->outWarnJson($msg);
//                return;
//            }
            // 明細チェック処理
            $msg = '見積を確定することができません。';
            list($flg, $msg) = $this->checkMsi($msg);
            if (!$flg) {
                $this->outWarnJson($msg);
                return;
            }
            // 会員区分とプランが一致していなければ見積確定ができない
            list($flg, $msg) = $this->checkGojokaiPlan();
            if (!$flg) {
                $this->outWarnJson($msg);
                return;
            }
            // 見積担当者チェック
            if (!isset($this->_mitsuTantoCd)) {
                $this->outWarnJson("見積担当者が入力されていません。");
                return;
            }
            // 受付結果チェック
            // 法事はスルーする
            if ($this->_moushiKbn != static::MOUSHI_KBN_HOUJI) {
                $kihonFree = DataMapper_SekoKihonAllFree::find($db, array('seko_no' => $this->_sekoNo));
                if (count($kihonFree) > 0 && !isset($kihonFree[0]['free_kbn4'])) {
                    $this->outWarnJson("受付結果が入力されていません。");
                    return;
                }
            }
            // プランの式場チェック
            if (!$this->checkPlanShikijo()) {
                $this->outWarnJson('選択された見積式場のプランが選択されていません。');
                return;
            }
            // 部門が売上部門かどうかチェックする
//            if (!$this->checkUriBumon($this->_bumonCd)) {
//                $this->outWarnJson('売上計上部門が売上未定部門のため、見積を確定することができません。');
//                return;
//            }
            // 未確定の場合
            if (!$this->isMitsuKakutei()) {
                // 売上伝票を作成前に受注伝票明細の部門コードを更新する
                $this->updBumon($db);
                // 売上伝票を作成する
                $cnt += $this->makeUriage($db);
                $denpyoNo = $this->getJuchudenpyoNo();
                $data_kbn = $this->getDataKbn();
                // MOC連携処理
//                $dataSekoKihon = $this->getSekoKihon();
//                $dataGojokaiMemberCol = $this->getGojokaiMember(10);
//                $newGojokaiMemberCol = array();
//                foreach ($dataGojokaiMemberCol as $oneRow) {
//                    if (isset($oneRow['kain_no'])) {    
//                        $newGojokaiMemberCol[] = $oneRow;
//                    }
//                }
//                if ($data_kbn == '2') {
//                    $dataKojinInfoCol = DataMapper_SekoKojinInfoHouji::find($db, array('seko_no' => $this->_sekoNo));
//                    if (isset($dataKojinInfoCol[0]['hk_nm'])) {
//                        $dataSekoKihon['k_nm'] = $dataKojinInfoCol[0]['hk_nm']; 
//                    }
//                }
//                $reqData = $this->makeMocData($db, $dataSekoKihon, $newGojokaiMemberCol);
//                if (count($reqData['ContractInfos']) > 0) {
//                    $rtnData = Logic_Exkaiin_Moc2KaiinUpdSeko2::doExec($db, $reqData);
//                    $msg = null;
//                    $gojokaiCouseMst = array_values($this->getGojokaiCouseMst());
//                    $taxInfoAll = App_ClsTaxLib::GetTaxInfoAll($db);
//                    foreach ($rtnData['ContractInfos'] as $onerow) {
//                        if (isset($onerow['UpdateStatus']) && $onerow['UpdateStatus'] == '1') {
//                            $msg = '加入者番号(' . $onerow['ContractNo'] . ')' . $onerow['UpdateMessage'];
//                            continue;
//                        }
//                        $keyIndex = array_search($onerow['ContractNo'], array_column($newGojokaiMemberCol, 'kain_no'));
//                        // 試算結果に更新する
//                        $gojoKeyIndex = array_search($onerow['CourseName'], array_column($gojokaiCouseMst, 'gojokai_cose_iw'));
//                        $taxKeyIndex = array_search($onerow['ContractTaxRate'], array_column($taxInfoAll, 'zei_rtu'));
//                        $newGojokaiMemberCol[$keyIndex]['course_snm_cd'] = $gojokaiCouseMst[$gojoKeyIndex]['gojokai_cose_iw'];
////                        $newGojokaiMemberCol[$keyIndex]['zei_cd'] = $taxInfoAll[$taxKeyIndex]['zei_cd'];
//                        $newGojokaiMemberCol[$keyIndex]['kanyu_nm'] = $onerow['CustomerName'];
//                        $newGojokaiMemberCol[$keyIndex]['kanyu_dt'] = $onerow['ContractDate'];
//                        $newGojokaiMemberCol[$keyIndex]['keiyaku_gaku'] = $onerow['TerminationValue'];
////                        $newGojokaiMemberCol[$keyIndex]['harai_no'] = $onerow['TotalPayNum'];
////                $newGojokaiMemberCol[$keyIndex]['harai_gaku'] = $onerow['TotalPayValue'] - $onerow['BonusAmount'] - $onerow['BalanceDiscountValue'] - $onerow['PrepaymentDiscountValue'];
////                        $newGojokaiMemberCol[$keyIndex]['wari_gaku'] = $onerow['PrepaymentDiscountValue'];
////                        $newGojokaiMemberCol[$keyIndex]['waribiki_gaku'] = $onerow['BalanceDiscountValue'];
////                        $newGojokaiMemberCol[$keyIndex]['early_use_cost_disp'] = $onerow['EarlyUseCost'];
////                        $newGojokaiMemberCol[$keyIndex]['meigi_chg_cost_disp'] = $onerow['RenameCommission'];
//                        $newGojokaiMemberCol[$keyIndex]['cur_cd'] = $onerow['ContractCode'];
//                        $newGojokaiMemberCol[$keyIndex]['v_free10'] = $onerow['ContractStatus'];
////                        $newGojokaiMemberCol[$keyIndex]['kanyu_tax'] = $onerow['ContractTax'];
////                        $newGojokaiMemberCol[$keyIndex]['n_free3'] = $onerow['BonusAmount'];
////                        $newGojokaiMemberCol[$keyIndex]['n_free4'] = $onerow['PremiumMonths'];
//                     }
//                    if (isset($msg)) {
//                        $this->outWarnJson('異常終了しました: ' . $msg);
//                        return;
//                    }
//                    $Status = null;
//                    if (array_key_exists('Result', $rtnData) && array_key_exists('Status', $rtnData['Result'])) {
//                        $Status = +$rtnData['Result']['Status'];
//                    }
//                    if ($Status == 1) { // 1:異常終了
//                        $this->outWarnJson('異常終了しました: ' . $msg);
//                        return;
//                    }
//                    // 互助会情報の再度保存処理を行う
//                    $cnt += $this->saveGojokaiMember($db, $newGojokaiMemberCol);
//                }
                $cnt += Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, $data_kbn); // 各種集計テーブル作成、更新処理
                // 施行基本情報を設定する
                $kihon = array(
                    'jichu_kakute_ymd' => Msi_Sys_Utils::getDatetimeStd(),
                    'jichu_kakute_cd' => App_Utils::getTantoCd(),
                    'status_kbn' => 2,
                );
                $where = array();
                $where['seko_no'] = $this->_sekoNo;
                $where['delete_flg'] = 0;
                // 施行基本情報を更新する
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihon, $where);
                $cnt += $db->easyExecute($sql, $param);
                $this->_juchuKakuteiYMD = $kihon['jichu_kakute_ymd'];
                // 入庫出庫明細売上伝票明細番号設定処理
                $cnt += $this->setNyukoShukoUriInfo($db);
                // 施行発注管理情報を保存する
                $dataDelCol = array();
                $cnt += $this->saveHachuInfo($db, $dataApp, $dataCol, $dataDelCol);
                $cnt += Logic_SisetsuYoyaku::updateAnchiYoyaku($db, $this->_sekoNo);
                $db->commit();
            }

            $data = $this->getData();
            // 画面データを設定する
            $data['dataSideMenu'] = $this->getSideMenuData();
            $data['cnt'] = $cnt;
            $data['status'] = 'OK';
            $data['msg'] = '見積を確定しました';
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $data = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
        }
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 売上伝票作成処理
     *
     * <AUTHOR> Sai
     * @param Msi_Sys_Db $db db
     * @since 2014/04/04
     */
    private function makeUriage($db) {
        $cnt = 0;

        // 売上伝票番号採番処理
        $uriageDenpyoNo = $this->getAutoUriageDenpyoNo($db);
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();

        // 売上伝票登録SQL
        $sql1 = "
            SELECT
                denpyo_no
                ,juchu_ymd
                ,data_kbn
                ,seko_no
                ,seko_no_sub
                ,bumon_cd
                ,tanto_cd
                ,gojokai_cose_cd
                ,seko_plan_cd
                ,null AS seikyu_ymd   -- 請求日
                ,null AS zen_seikyu_ymd  -- 前回請求日
                ,0 AS sekkyu_kaisu     -- 請求回数
                ,kaishu_kbn
                ,kaishu_ymd
                ,0 AS nyukin_prc -- 入金金額
                ,juchu_prc_sum 
                    + juchu_hepn_sum 
                    + juchu_nebk_sum 
                    + hoshi_prc_sum 
                    + out_zei_prc
                    + sougi_keiyaku_prc + sougi_harai_prc
                    + sougi_keiyaku_zei + sougi_wari_zei    -- 会費消費税
                    + sougi_wari_prc
                    + sougi_premium_service_prc
                    + sougi_early_use_cost + sougi_early_use_cost_zei   -- 早期利用費
                    + sougi_meigi_chg_cost + sougi_meigi_chg_cost_zei   -- 名義変更手数料
                    + etc_harai_prc                                     -- 金額充当
                    + n_free9
                    + n_free10
                AS seikyu_zan -- 請求残高
                ,sekyu_cd
                ,sekyu_nm
                ,sekyu_knm
                ,sekyu_soufu_nm
                ,sekyu_yubin_no
                ,sekyu_addr1
                ,sekyu_addr2
                ,sekyu_tel
                ,sekyu_fax
                -- ,delivery_kbn 売上伝票にない
                ,nonyu_cd
                ,nonyu_nm
                ,nonyu_knm
                ,nonyu_yubin_no
                ,nonyu_addr1
                ,nonyu_addr2
                ,nonyu_tel
                ,nonyu_fax
                ,nonyu_dt
                ,juchu_prc_sum AS uri_prc_sum
                ,genka_prc_sum
                ,juchu_hepn_sum AS uri_hepn_sum
                ,juchu_nebk_sum AS uri_nebk_sum
                ,hoshi_prc_sum
                ,szei_katax_taisho_prc
                ,uzei_katax_taisho_prc
                ,hitax_katax_taisho_prc
                ,tax_code_kbn
                ,tax_cd
                ,tax_kbn
                ,zei_cd
                ,out_zei_prc
                ,in_zei_prc
                ,arari_prc
                ,denpyo_biko1
                ,denpyo_biko2
                ,null AS inkan_img1 -- 承認１印鑑イメージ
                ,null AS inkan_img2 -- 承認２印鑑イメージ
                ,null AS shonin_dt1 -- 承認１日時
                ,null AS shonin_dt2 -- 承認２日時
                ,2    AS status_kbn -- ステータス
                ,delete_flg
                ,main_pt_cd
                ,main_pt_kbn
                ,seko_kbn
                ,syushi_kbn
                ,saidan_sbt
                ,plan_shikijo_cd
                ,est_shikijo_cd
                ,gojokai_kbn
                ,cupon_prc
                ,k_free1
                ,k_free2
                ,k_free3
                ,k_free4
                ,k_free5
                ,v_free1
                ,v_free2
                ,v_free3
                ,v_free4
                ,v_free5
                ,v_free6
                ,v_free7
                ,v_free8
                ,v_free9
                ,v_free10
                ,1 AS pay_method_cd
                ,n_free9
                ,n_free10
            FROM
                juchu_denpyo
            WHERE
                denpyo_no = :denpyo_no
            AND delete_flg = 0
                ";

        // 売上伝票明細登録SQL
        $sql2 = "
            SELECT
                msi_no
                ,disp_no
                ,add_kbn
                ,data_sbt
                ,bumon_cd
                ,shohin_bumon_cd
                ,seko_no
                ,seko_no_sub
                ,juchu_ymd
                ,denpyo_kbn
                ,mokuteki_kbn
                ,dai_bunrui_cd
                ,chu_bunrui_cd
                ,shohin_kbn
                ,shohin_cd
                ,shohin_nm
                ,shohin_tkiyo_nm
                ,juchu_suryo
                ,tani_cd
                ,juchu_tnk AS uri_tnk
                ,juchu_prc AS uri_prc
                ,nebiki_prc
                ,gojokai_nebiki_prc
                ,gen_tnk
                ,gen_gaku
                ,arari_gaku
                ,zei_Kbn
                ,zei_cd
                ,reduced_tax_rate -- mihara keigen
                ,out_zei_prc
                ,in_zei_prc
                ,nafuda_nm
                ,hoshi_umu_kbn
                ,hoshi_ritu_cd
                ,hoshi_prc
                --,delivery_kbn -- 納品場所区分 売上伝票にない
                ,nonyu_cd
                ,nonyu_nm
                ,nonyu_knm
                ,nonyu_yubin_no
                ,nonyu_addr1
                ,nonyu_addr2
                ,nonyu_tel
                ,nonyu_fax
                ,nonyu_dt
                ,msi_biko1
                ,msi_biko2
                ,siire_cd
                ,siire_lnm
                ,uri_kamoku_cd
                ,refer_uchiwk_no
                ,select_shohin_cd
                ,shuko_status_kbn
                ,upgrade_kbn
                ,plan_shohin_cd
                ,plan_shohin_nm
                ,plan_shohin_bumon_cd
                ,plan_shohin_suryo
                ,plan_shohin_tnk
                ,delete_flg
                ,k_free1
                ,k_free2
                ,k_free3
                ,k_free4
                ,k_free5
                ,v_free1
                ,v_free2
                ,v_free3
                ,v_free4
                ,v_free5
                ,n_free1
                ,n_free2
                ,n_free3
                ,n_free4
                ,n_free5
                ,d_free1
                ,d_free2
                ,d_free3
                ,ts_free1
                ,ts_free2
                ,ts_free3
            FROM
                juchu_denpyo_msi
            WHERE
                denpyo_no = :denpyo_no
            AND delete_flg = 0
                ";
        $select1 = $db->easySelect($sql1, array('denpyo_no' => $juchuDenpyoNo));
        $select2 = $db->easySelect($sql2, array('denpyo_no' => $juchuDenpyoNo));
        foreach ($select1 as $data) {
            $data['uri_den_no'] = $uriageDenpyoNo;
            $data['juchu_ymd'] = $this->_selectSekoKihon['sougi_ymd'];
            $data['keijo_ymd'] = $this->_selectSekoKihon['sougi_ymd'];
            // 売上伝票登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('uriage_denpyo', $data);
            $cnt += $db->easyExecute($sql, $param);
        }
        foreach ($select2 as $data) {
            $data['uri_den_no'] = $uriageDenpyoNo;
            $data['juchu_ymd'] = $this->_selectSekoKihon['sougi_ymd'];
            // 売上伝票明細登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('uriage_denpyo_msi', $data);
            $cnt += $db->easyExecute($sql, $param);
        }

        // 売上請求先情報作成処理
        $juchuSekyuInfo = DataMapper_SekyuSakiInfo::findJuchusekyu($db, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn()));
        DataMapper_SekyuSakiInfo::uriageFromJuchuSekyuUpsert($db, $uriageDenpyoNo, $juchuSekyuInfo);
        return $cnt;
    }

    /**
     * 履歴作成処理
     *
     * <AUTHOR> Tosaka
     * @since 2020/03/30
     */
    public function historymake() {
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        $db = Msi_Sys_DbManager::getMyDb();
        $cnt = 0;
        // 施行基本情報を設定する
        $this->setInitParam();
        $cnt += $this->makeDenpyoRireki($db);
        $msg = '見積履歴を作成しました';
        $db->commit();
        $data = $this->getData();
        // 画面データを設定する
        $data['dataSideMenu'] = $this->getSideMenuData();
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = $msg;
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 履歴作成処理
     *
     * <AUTHOR> Tosaka
     * @since 2020/03/30
     */
    public function historycopy() {

        $params = Msi_Sys_Utils::webInputs();
        $dataApp = Msi_Sys_Utils::json_decode($params['dataAppJson']);
        $this->_sekoNo = $dataApp['seko_no'];
        $this->_historyNo = $params['history_no'];
        if (strlen($this->_historyNo) == 0) {
            $msg = '履歴データが参照できません。';
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $cnt = 0;
        // 施行基本情報を設定する
        $this->setInitParam();
        // 現在の伝票の履歴を作成→履歴を受注伝票にコピー
        $cnt += $this->makeDenpyoRireki($db);
        $cnt += $this->copyHistoryData($db, $dataApp);
        $db->commit();
        $data = $this->getData();
        // 画面データを設定する
        $data['dataSideMenu'] = $this->getSideMenuData();
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = '履歴から見積データを作成しました。';
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 見積取消処理
     *
     * <AUTHOR> Sai
     * @since 2014/04/04
     * @version 2015/03/03 施行発注管理情報の更新、削除 処理を追加 Kayo
     * @version 2015/06/08 再見積フラグ追加
     */
    public function mitsutorikesi($flg_saimitsu = false) {
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        // データ加工処理
        $dataCol = $this->getDataCol($req);
        $this->_sekoNo = $dataApp['seko_no'];
        $cnt = 0;
        // 施行基本情報を設定する
        $this->setInitParam();
        $db = Msi_Sys_DbManager::getMyDb();

        // 内金領収証発行済み
        if (App_Utils2::hasRyosyuUc($dataApp['uri_den_no'])) {
            $this->outWarnJson('内金領収証が発行済みのため、見積を取り消すことができません。');
            return false;
        }
        // 入金があれば見積確定不可
        if ($this->hasNyukinData()) {
            $this->outWarnJson('入金があるため、見積を取り消すことができません。');
            return false;
        }
        // 承認されたら見積確定不可
        if ($this->hasUriageShonin()) {
            $this->outWarnJson('承認済みのため、見積を取り消すことができません。');
            return false;
        }
        // 発注済商品があったら見積確定不可
        if (App_Utils2::hasHachuShohin($this->_sekoNo)) {
            $this->outWarnJson('発注済みの商品があるため、見積を取り消すことができません。');
            return false;
        }
        // 出庫済商品があったら見積確定不可
        if (App_Utils2::hasSyukkoShohin($this->_sekoNo, $this->getDataKbn())) {
            $this->outWarnJson('出庫済みの商品があるため、見積を取り消すことができません。');
            return false;
        }
        // 売上伝票履歴が財務連動済なら見積確定不可
        if (App_Utils2::hasZaimuRendo($this->getUriagedenpyoNo())) {
            $this->outWarnJson('財務連動連携済のため、見積を取り消すことができません。');
            return false;
        }
        $sql = "SELECT seko_no
                FROM hikitori_denpyo_msi 
                WHERE seko_no = :seko_no 
                AND delete_flg = 0;
                            ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        // 存在すればエラー
        if (count($select) > 0) {
            $this->outWarnJson('引取返金が登録されているため、見積確定取消ができません。');
            return false;
        }
        $msg = '見積確定を取り消しました';
        // 確定済みの場合
        if ($this->isMitsuKakutei()) {

            // 売上伝票を論理削除する
            $cnt = $this->deleteUriage($db);
            // 受注確定→未確定に戻した場合、入金伝票が残ってしまう場合があるので、念のため	2014/06/29 ADD Kayo
            $cnt += Logic_GojokaiNyukinDenpyo::NyukinDenDelete($db, $this->_sekoNo, '00'); // 入金伝票（互助会）削除 処理 受注確定→未確定にされた場合、入金伝票を削除する
            // 施行発注管理情報の更新、削除 処理 2015/03/03 ADD Kayo
            $cnt += Logic_SekoHachuInfoUpdate::HachuInfoUpdate($db, $this->_sekoNo, '00');
            // 施行基本情報を設定する
            $kihon = array(
                'jichu_kakute_ymd' => null,
                'jichu_kakute_cd' => null,
                'status_kbn' => 1,
            );
            $where['seko_no'] = $this->_sekoNo;
            $where['delete_flg'] = 0;
            // 施行基本情報を更新する
            //$sql = $this->makeUpdateSQL("seko_kihon_info", $kihon, $where);
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihon, $where);
            $cnt += $db->easyExecute($sql, $param);
            $this->_juchuKakuteiYMD = null;
            // 入庫出庫明細売上伝票明細番号クリア処理
            $cnt += $this->clearNyukoShukoUriInfo($db);
            // 施行発注管理情報の売上伝票項目をNULLにする
            $sql = "
                UPDATE seko_hachu_info
                SET uri_denpyo_no = NULL, uri_msi_no = NULL
                WHERE seko_no = :seko_no 
                AND data_kbn = :data_kbn 
                    ";
            $cnt += $db->easyExecute($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn()));
            // コピーデータは論理削除する
            $sql = "
                UPDATE seko_hachu_info
                SET delete_flg = 1
                WHERE seko_no = :seko_no 
                AND data_kbn = :data_kbn 
                AND hachu_no_moto IS NOT NULL
                    ";
            $cnt += $db->easyExecute($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn()));
//            $dataDelCol = array();
//            $cnt += $this->saveHachuInfo($db, $dataApp, $dataCol, $dataDelCol);
            if ($flg_saimitsu) {
                $cnt += $this->makeDenpyoRireki($db);
                $msg = '見積履歴を作成しました';
            }
            $db->commit();
        }

        $data = $this->getData();
        // 画面データを設定する
        $data['dataSideMenu'] = $this->getSideMenuData();
        $data['cnt'] = $cnt;
        $data['status'] = 'OK';
        $data['msg'] = $msg;
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 再見積処理
     *
     * <AUTHOR> Sai
     * @since 2014/06/08
     */
    public function saimitsu() {
        $this->mitsutorikesi(true);
    }

    /**
     * 見積履歴作成処理
     *
     * <AUTHOR> Sai
     * @param Msi_Sys_Db $db db
     * @since 2014/06/08
     */
    public function makeDenpyoRireki($db) {
        $cnt = 0;
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();

        // 受注伝票履歴登録SQL
        $sql1 = "
            SELECT
                *
                ,now() AS history_dt
            FROM juchu_denpyo
            WHERE denpyo_no = :denpyo_no
                AND delete_flg = 0
                ";

        // 受注伝票明細履歴登録SQL
        $sql2 = "
            SELECT
                *
            FROM juchu_denpyo_msi
            WHERE denpyo_no = :denpyo_no
                AND delete_flg = 0
                ";
        $select1 = $db->easySelect($sql1, array('denpyo_no' => $juchuDenpyoNo));
        $select2 = $db->easySelect($sql2, array('denpyo_no' => $juchuDenpyoNo));
        $except = array("_req_id", "_cre_user", "_cre_ts", "_mod_user", "_mod_ts", "_mod_cnt", "convert_kbn");
        $history_no = DataMapper_Pdf0113::getDenpyoRirekiNo($db, $juchuDenpyoNo);
        $history_no += 1;
        require_once 'Pdf0113Controller.lifeland.php';
        $params = array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn(), 'moushi_kbn' => $this->_moushiKbn, 'printKbn' => 1, 'preview' => 'save', 'rireki_no' => $history_no);
        $buf = Juchu_Pdf0113Controller::indexAction($params);
        $temp_file = Msi_Sys_Utils::tempnam();
        Msi_Sys_Utils::put_contents($temp_file, $buf);
        $oid = $db->writeBlob($temp_file);
        foreach ($select1 as $data) {
            // 受注伝票履歴登録SQL
            $data['history_no'] = $history_no;
            $data['f_free1'] = $oid;
            $data['f_name1'] = $this->_sekoNo . '-' . $history_no . $this->_selectSekoKihon['souke_nm'] . '家見積書';
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo_history', $data, $except);
            $cnt += $db->easyExecute($sql, $param);
        }
        foreach ($select2 as $data) {
            // 受注伝票明細履歴登録SQL
            $data['history_no'] = $history_no;
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo_msi_history', $data, $except);
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     * 見積履歴作成処理
     *
     * <AUTHOR> Tosaka
     * @param Msi_Sys_Db $db db
     * @param array $dataApp 画面データ
     * @since 2020/04/01
     */
    public function copyHistoryData($db, $dataApp) {

        // DELETE→INSERTを行う
        $cnt = 0;
        // 受注伝票番号取得する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();

        // 受注伝票削除SQL
        $sql = "
            DELETE FROM juchu_denpyo
            WHERE denpyo_no = :denpyo_no 
                ";
        $cnt += $db->easyExecute($sql, array('denpyo_no' => $juchuDenpyoNo));
        // 受注伝票明細削除SQL
        $sql = "
            DELETE FROM juchu_denpyo_msi
            WHERE denpyo_no = :denpyo_no 
                ";
        $cnt += $db->easyExecute($sql, array('denpyo_no' => $juchuDenpyoNo));
        // 施行発注情報削除SQL
        $sql = "
            DELETE FROM seko_hachu_info
            WHERE seko_no = :seko_no 
                AND data_kbn = :data_kbn 
                ";
        $cnt += $db->easyExecute($sql, array('seko_no' => $this->_sekoNo, 'data_kbn' => $this->getDataKbn()));

        // 受注伝票登録SQL
        $sql1 = "
            SELECT
                *
            FROM
                juchu_denpyo_history
            WHERE denpyo_no = :denpyo_no
                AND history_no = :history_no
                AND delete_flg = 0
                ";
        // 受注伝票明歴登録SQL
        $sql2 = "
            SELECT
                m.*
                ,sbm.hachu_kbn
            FROM
                juchu_denpyo_msi_history m
            LEFT JOIN shohin_bunrui_mst sbm
            ON sbm.shohin_cd = m.shohin_cd
            AND sbm.bumon_cd = m.shohin_bumon_cd
            AND sbm.dai_bunrui_cd = m.dai_bunrui_cd
            AND sbm.chu_bunrui_cd = m.chu_bunrui_cd
            AND sbm.shohin_kbn = m.shohin_kbn
            AND sbm.delete_flg = 0
            WHERE m.denpyo_no = :denpyo_no
                AND m.history_no = :history_no
                AND m.delete_flg = 0
            ORDER BY m.msi_no
                ";
        $select1 = $db->easySelect($sql1, array('denpyo_no' => $juchuDenpyoNo, 'history_no' => $this->_historyNo));
        $select2 = $db->easySelect($sql2, array('denpyo_no' => $juchuDenpyoNo, 'history_no' => $this->_historyNo));
        $except = array("history_no", "history_dt", "_req_id", "_cre_user", "_cre_ts", "_mod_user", "_mod_ts", "_mod_cnt", "convert_kbn", "f_free1", "f_name1");
        $except2 = array("hachu_kbn", "history_no", "history_dt", "_req_id", "_cre_user", "_cre_ts", "_mod_user", "_mod_ts", "_mod_cnt", "convert_kbn", "f_free1", "f_name1");
        $history_no = DataMapper_Pdf0113::getDenpyoRirekiNo($db, $juchuDenpyoNo);
        $history_no += 1;
        foreach ($select1 as $data) {
            // 受注伝票登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo', $data, $except);
            $cnt += $db->easyExecute($sql, $param);
            // 施行基本情報更新
            $where = array('seko_no' => $this->_sekoNo);
            $kihon = array();
            $selectKihon = DataMapper_SekoKihonInfo::find($db, array('seko_no' => $this->_sekoNo));
            if (count($selectKihon) > 0) {
                $kaiinKbn = DataMapper_CodeNmMst::find($db, array('code_kbn' => $selectKihon[0]['kaiin_code_kbn'], 'kbn_value_cd' => $data['v_free3']));
                $syushiKbn = DataMapper_CodeNmMst::find($db, array('code_kbn' => $selectKihon[0]['syushi_code_kbn'], 'kbn_value_cd_num' => $data['syushi_kbn']));
            }
            if (count($kaiinKbn) > 0 && isset($kaiinKbn[0])) {
                $kihon['kaiin_kbn'] = $kaiinKbn[0]['kbn_value_cd_num'];      // 会員コード
            }
            if (count($syushiKbn) > 0 && isset($syushiKbn[0])) {
                $kihon['syushi_cd'] = $syushiKbn[0]['kbn_value_cd'];    // 宗旨コード
            }
            $kihon['kaiin_cd'] = $data['v_free3'];                 // 会員区分
            $kihon['main_pt_cd'] = $data['main_pt_cd'];               // 基本パターン区分
            $kihon['main_pt_kbn'] = $data['main_pt_kbn'];               // 基本パターン区分
            $kihon['saidan_sbt'] = $data['saidan_sbt'];                 // 祭壇種別
            $kihon['plan_syushi_kbn'] = $data['syushi_kbn'];                // 宗旨区分
            $kihon['gojokai_kbn'] = $data['gojokai_kbn'];               // 互助会区分
            $kihon['seko_plan_cd'] = $data['seko_plan_cd'];                   // 施行プランコード
            $kihon['plan_shikijo_cd'] = $data['plan_shikijo_cd'];                   // プラン用式場コード
            $kihon['est_shikijo_cd'] = $data['est_shikijo_cd'];                   // 見積式場コード
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_info", $kihon, $where);
            $cnt += $db->easyExecute($sql, $param);
            $dataApp['denpyo_no'] = $data['denpyo_no'];
        }
        foreach ($select2 as $data) {
            // 受注伝票明細登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL('juchu_denpyo_msi', $data, $except2);
            $cnt += $db->easyExecute($sql, $param);
        }
        // 施行発注管理情報を保存する
        $dataDelCol = array();
        $cnt += $this->saveHachuInfo($db, $dataApp, $select2, $dataDelCol);
        return $cnt;
    }

    /**
     * 商品情報取得処理
     *
     * <AUTHOR> Tosaka
     * @since 2021/xx/xx
     */
    public function getShohinInfo() {
        $req = Msi_Sys_Utils::getRequestObject();
        $params = Msi_Sys_Utils::webInputs();
        $wheredata = $params['wheredata'];
        $this->_sekoNo = $wheredata['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        $db = Msi_Sys_DbManager::getMyDb();
        if (isset($wheredata['kijun_ymd']) && strlen($wheredata['kijun_ymd']) > 0) {
            $kijunYmd = $wheredata['kijun_ymd'];
        } else {
            $kijunYmd = Msi_Sys_Utils::getDate();
        }
        $kihonInfo = $this->_selectSekoKihon;
        $bumon_cd = $kihonInfo['est_shikijo_cd'];
        $cond = array();
        $cond['shohin_cd'] = array('~', $wheredata['shohin_cd']);
        $cond['dai_bunrui_cd'] = DataMapper_Utils::condOneOf('dai_bunrui_cd', $wheredata['s_dai_bunrui_cd'], 'dbc_');
        $cond['chu_bunrui_cd'] = DataMapper_Utils::condOneOf('chu_bunrui_cd', $wheredata['s_chu_bunrui_cd'], 'cbc_');
        $cond['hihyoji_kbn'] = DataMapper_Utils::condOneOf('hihyoji_kbn', $wheredata['s_hihyoji_kbn'], 'hihyoji_kbn_');
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $bumon_cd));
        $bumonCds = null;
        if (Count($bumonData) > 0 && $bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
            //自部門商品＋親部門商品(親部門はさかのぼる)
            $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $bumon_cd, null);
            foreach ($allOyaBumon as $oneBumon) {
                if ($oneBumon['bumon_shohin_sel_kbn'] === '1') {
                    $bumonCds .= "'" . $oneBumon['ko_bumon_cd'] . "',";
                }
            }
        } else {
            // 会計部門と違う場合は会計部門の親部門を取得する
            if (isset($bumonData[0]['kaikei_bumon_cd'])) {
                $bumonData2 = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $bumonData[0]['kaikei_bumon_cd']));
                if ($bumonData2[0]['bumon_shohin_sel_kbn'] === '1') {
                    $bumonCds .= "'" . $bumonData[0]['kaikei_bumon_cd'] . "',";
                }
                $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $bumonData[0]['kaikei_bumon_cd'], null);
            } else {
                // 親部門商品(親部門はさかのぼる)のみ
                $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $bumon_cd, null);
            }
            foreach ($allOyaBumon as $oneBumon) {
                if ($oneBumon['bumon_kbn'] === '0') {
                    $bumonCds .= "'" . $oneBumon['bumon_cd'] . "',";
                }
            }
        }
        if (strlen($bumonCds) > 0) {
            $cond['__raw_bumon_cds'] = "bumon_cd IN (" . trim($bumonCds, ',') . ")";
        }
        $cond['__etc_orderby'] = array('shohin_kbn ASC');
        $cond['__raw_shohinMst'] = "T.hanbai_st_ymd <= '" . $kijunYmd . "' AND T.hanbai_end_ymd >= '" . $kijunYmd . "'";
        $select = DataMapper_Shohin0::find($db, $cond, $kijunYmd, false);
        if (count($select) > 0) {
            // 単価がなければエラー 
            if (!isset($select[0]['hanbai_tnk'])) {
                $data['status'] = 'NG';
                $data['data'] = array();
                $data['msg'] = '単価が適用期間外です。';
                Msi_Sys_Utils::outJson($data);
                return;
            }
            $select[0]['shohin_bumon_cd'] = $select[0]['bumon_cd'];
            $select[0]['tanka'] = Msi_Sys_Utils::filterComma($select[0]['hanbai_tnk']);
            $select[0]['juchu_suryo'] = 1;
            $select[0]['juchu_prc'] = Msi_Sys_Utils::filterComma($select[0]['hanbai_tnk'] * $wheredata['shohin_suryo']);
            $select[0]['genka'] = Msi_Sys_Utils::filterComma($select[0]['siire_tnk']);
            // 互助会値引を設定する
            App_Utils2::setGojokaiNebikiOne($select[0], $this->_sekoNo);
            $select[0]['gojokai_nebiki_prc'] = Msi_Sys_Utils::filterComma($select[0]['gojokai_nebiki_prc']);
            $data['status'] = 'OK';
            $data['data'] = $select[0];
        } else {
            $data['status'] = 'NG';
            $data['data'] = array();
            $data['msg'] = '商品データが存在しません。';
        }
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * オーダーメイド計算処理
     *
     * <AUTHOR> Tosaka
     * @since 2021/xx/xx
     */
    public function estCalc() {
        $req = Msi_Sys_Utils::getRequestObject();
        $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
        $gojokaiCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiColJson'));
        $this->_sekoNo = $dataApp['seko_no'];
        // 施行基本情報を設定する
        $this->setInitParam();
        $db = Msi_Sys_DbManager::getMyDb();
        $omHaraiPrc = 0;
        $gojoHaraiPrc = 0;
        $gojoWariPrc = 0;
        $gojoUsePrc = 0;
        $pContractNos = array();
        if (count($gojokaiCol) > 0) {
            foreach ($gojokaiCol as $onerow) {
                $gojokaiUpd = array();
                // 変更対象のみ
                if ($onerow['change_check'] == '1') {
                    $pContractNos[] = trim($onerow['kain_no']);
                    $gojoHaraiPrc += $onerow['harai_gaku'];
                    $gojoWariPrc += $onerow['wari_gaku'];
                    $gojoUsePrc += $onerow['use_gaku'];
                    $gojokaiUpd['yoto_kbn'] = '2';
                } else {
                    $gojokaiUpd['yoto_kbn'] = '4';
                }
                $gojokaiWhere['seko_no'] = $this->_sekoNo;
                $gojokaiWhere['kain_no'] = $onerow['kain_no'];
                // 更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seko_gojokai_member', $gojokaiUpd, $gojokaiWhere);
                $db->easyExecute($sql, $param);
            }
        }
        // 検索条件を作成
        $_aContractNoList = array_map(function($v) {
            return array('ContractNo' => $v);
        }, $pContractNos);
        $_reqData = array('ContractNoList' => $_aContractNoList);
        list($_errMsg, $_aResult, $_resData) = Logic_HakuApi_LogicCrsRec::search($_reqData);
        // 画面データ保存処理
        $kihon = array();
        $kihonwhere = array('seko_no' => $this->_sekoNo);
        $kihon['om_est_method_kbn'] = $dataApp['om_est_method_kbn'];
        if ($dataApp['om_est_method_kbn'] === '1') {
            $kihon['om_est_harai_no'] = $dataApp['om_est_input'];
            $kihon['om_est_kake_prc'] = null;
        } else {
            $kihon['om_est_harai_no'] = null;
            $kihon['om_est_kake_prc'] = $dataApp['om_est_input'];
        }
        // 更新SQL
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seko_kihon_info', $kihon, $kihonwhere);
        $db->easyExecute($sql, $param);
        list($errMsg, $aResult, $resData) = $this->getOmSimulationData();
        if (isset($errMsg)) {
            $data['status'] = 'NG';
            $data['msg'] = '会員管理連携：' . $errMsg;
        } else if ($resData['Result']['Status'] === static::SIM_STATUS_COMP) {
            // 施行申込が完了している場合はエラーを返す
            $data['status'] = 'NG';
            $data['msg'] = '申込が完了しているため変更できません。';
        } else {
            $omHaraiPrc += $aResult['TerminationValue'] - $aResult['PrePayValue']; // 完納額-充当金額
//            Msi_Sys_Utils::profilerMark('estCalc-start');
//            Msi_Sys_Utils::info('会員管理連携：試算==>' . Msi_Sys_Utils::dump(array('登録データ' => Msi_Sys_Utils::json_encode($capData), '返却データ' => $resData)));
            // 施行基本情報更新処理
            $kihon = array();
            $kihonwhere = array('seko_no' => $this->_sekoNo);
            $kihon['om_plan_cd'] = $resData['PlanCode'];
            $kihon['om_keiyaku_prc'] = $aResult['TerminationValue'];    // APIの戻り値
            $kihon['om_kake_prc'] = $gojoHaraiPrc;    // FDNのデータ
            $kihon['om_wari_prc'] = $gojoWariPrc;   // FDNのデータ
            $kihon['om_use_prc'] = $aResult['PrePayValue'];    // APIの戻り値
            $kihon['om_harai_prc'] = $omHaraiPrc;  // APIの戻り値
            $kihon['om_harai_no'] = $aResult['PaymentNum']; // APIの戻り値
            $kihon['om_first_kake_prc'] = $aResult['InitialPayValue'];  // APIの戻り値
            $kihon['om_monthly_kake_prc'] = $aResult['Installment'];    // APIの戻り値
            $kihon['om_fee_prc'] = $aResult['OrderMadeCommission'] + $aResult['OrderMadeCommissionTax'];    // APIの戻り値
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seko_kihon_info', $kihon, $kihonwhere);
            $db->easyExecute($sql, $param);
            $db->commit();
            $data = $this->getData();
            // 画面データを設定する
            $data['dataSideMenu'] = $this->getSideMenuData();
            $data['status'] = 'OK';
            $data['fdn_use_prc'] = $gojoUsePrc;
            $data['api_juto_prc'] = $aResult['PrePayValue'];
            $data['msg'] = '試算を行いました。';
        }

        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 売上伝票論理削除処理
     *
     * <AUTHOR> Sai
     * @version 2014/09/07 売上伝票、明細履歴テーブルの削除処理を追加 Kayo
     * @param Msi_Sys_Db $db db
     * @since 2014/04/04
     */
    private function deleteUriage($db) {
        $cnt = 0;
        // 万が一複数データが作成された場合を考慮する
        $juchuDenpyoNo = $this->getJuchudenpyoNo();
        $uriData = DataMapper_UriageDenpyo::findDenpyo2($db, array('denpyo_no' => $juchuDenpyoNo));
        foreach ($uriData as $val) {
            // 売上伝票番号取得する
            $uriageDenpyoNo = $val['uri_den_no'];
            $sql1 = "
            UPDATE
                uriage_denpyo
            SET 
                delete_flg = 1
            WHERE
                uri_den_no = :uri_den_no
            AND delete_flg = 0
                    ";
            $sql2 = "
            UPDATE
                uriage_denpyo_msi
            SET 
                delete_flg = 1
            WHERE
                uri_den_no = :uri_den_no
            AND delete_flg = 0
                    ";
            $sql3 = "
            UPDATE uriage_sekyu_saki_info
            SET delete_flg = 1
            WHERE uri_den_no = :uri_den_no
                AND delete_flg = 0
                    ";

            $cnt += $db->easyExecute($sql1, array('uri_den_no' => $uriageDenpyoNo));
            $cnt += $db->easyExecute($sql2, array('uri_den_no' => $uriageDenpyoNo));
            $cnt += $db->easyExecute($sql3, array('uri_den_no' => $uriageDenpyoNo));
            // 売上伝票、売上伝票明細履歴の削除処理
            $ret = Logic_DenpyoHistoryMake::UriageHistoryDelete($db, $uriageDenpyoNo);
        }
        return $cnt;
    }

    /**
     * サイドメニューデータ取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/07/17
     * @return array サイドメニューデータ
     */
    protected function getSideMenuData() {
        Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub, $this->_moushiKbn);
        // 画面データを設定する
        $dataSideMenu = Juchu_Utils::getSideMenuData('estimate');
        return $dataSideMenu;
    }

    /**
     * 入庫出庫明細売上伝票明細番号設定処理(受注伝票明細番号を売上伝票明細番号にコピーする)
     *
     * <AUTHOR> Sai
     * @since 2014/12/24
     * @param Msi_Sys_Db $db db
     * @return int 処理件数
     */
    private function setNyukoShukoUriInfo($db) {
        $cnt = 0;
        $msiZai = $this->selectNyukoShukoMsi($db);
        // 入庫出庫明細売上伝票明細番号設定処理
        foreach ($msiZai as $rec) {
            $denpyoMsi['uriage_den_msi_no'] = $rec['juchu_den_msi_no'];
            $where['denpyo_no'] = $rec['denpyo_no'];
            $where['msi_no'] = $rec['msi_no'];
            $where['bunkatu_no'] = $rec['bunkatu_no'];
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("nyuko_shuko_msi", $denpyoMsi, $where);
            $cnt += $db->easyExecute($sqlUp, $param);
        }
        // 入庫出庫伝票売上伝票番号設定処理
        if (count($msiZai) > 0) {
            $denpyo_no = $msiZai[0]['denpyo_no'];
            $uri_den_no = $this->getUriagedenpyoNo();
            $denpyo['uriage_den_no'] = $uri_den_no;
            $where2['denpyo_no'] = $denpyo_no;
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("nyuko_shuko_denpyo", $denpyo, $where2);
            $cnt += $db->easyExecute($sqlUp, $param);
        }
    }

    /**
     * 入庫出庫明細売上伝票明細番号クリア処理
     *
     * <AUTHOR> Sai
     * @since 2014/12/24
     * @param Msi_Sys_Db $db db
     * @return int 処理件数
     */
    private function clearNyukoShukoUriInfo($db) {
        $cnt = 0;
        $msiZai = $this->selectNyukoShukoMsi($db);
        // 入庫出庫明細売上伝票明細番号クリア処理
        foreach ($msiZai as $rec) {
            $denpyoMsi['uriage_den_msi_no'] = null;
            $where['denpyo_no'] = $rec['denpyo_no'];
            $where['msi_no'] = $rec['msi_no'];
            $where['bunkatu_no'] = $rec['bunkatu_no'];
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("nyuko_shuko_msi", $denpyoMsi, $where);
            $cnt += $db->easyExecute($sqlUp, $param);
        }
        // 入庫出庫伝票売上伝票番号クリア処理
        if (count($msiZai) > 0) {
            $denpyo_no = $msiZai[0]['denpyo_no'];
            $denpyo['uriage_den_no'] = null;
            $where2['denpyo_no'] = $denpyo_no;
            list($sqlUp, $param) = DataMapper_Utils::makeUpdateSQL("nyuko_shuko_denpyo", $denpyo, $where2);
            $cnt += $db->easyExecute($sqlUp, $param);
        }
    }

    /**
     * 入庫出庫明細取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/12/24
     * @param Msi_Sys_Db $db db
     * @return array 入庫出庫明細
     */
    private function selectNyukoShukoMsi($db) {
        $sql = '
                SELECT 
                    nsm.denpyo_no
                    ,nsm.msi_no
                    ,nsm.bunkatu_no
                    ,nsm.juchu_den_msi_no
                FROM 
                    nyuko_shuko_msi nsm
                    INNER JOIN nyuko_shuko_denpyo ns
                    ON 
                        (
                        nsm.denpyo_no = ns.denpyo_no
                        )
                WHERE 
                    nsm.delete_flg=0
                    AND ns.delete_flg=0
                    AND ns.seko_no=:seko_no';
        $msiZai = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        return $msiZai;
    }

    /**
     * 互助会区分が6の36万円コースの場合自動作成のサービス区分を変更する
     *
     * <AUTHOR> Sai
     * @since 2014/12/24
     * @param array $dataCol
     * @return array $dataCol
     */
    private function adjColData(&$dataCol) {
        if ($this->_gojokaiKbn === '6') {
            foreach ($dataCol as &$value) {
                if ($value['nebiki_kbn'] === '1' && $value['add_kbn'] === self::ADD_KBN_AUTO) {
                    $value['nebiki_kbn'] = '0';
                }
            }
        }
    }

    private function getBunruiData() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = '
            SELECT 
                dai_bunrui_cd,chu_bunrui_cd
            FROM 
                shohin_bunrui_mst
            WHERE delete_flg  = 0 -- 2016/10/06 ADD Kayo
            GROUP BY 
                dai_bunrui_cd,chu_bunrui_cd
            ORDER BY 
                    dai_bunrui_cd,chu_bunrui_cd';
        $select = $db->easySelect($sql);
        return $select;
    }

    /**
     *
     * 見積式場と施行日程の葬儀式場が一致してるかどうかの判定
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/07
     * @return boolean 
     */
    protected function checkShikijo() {
        $flg = true;
        $keishiki_kbn = null;
        if (isset($this->_selectSekoKihon['keishiki_kbn'])) {
            $keishiki_kbn = $this->_selectSekoKihon['keishiki_kbn'];    // 形式コード
        }
        if (strlen($this->_estShikijoCd) == 0) {
            return $flg = false;
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $estBumonData = DataMapper_Bumon::find($db, array('bumon_cd' => $this->_estShikijoCd));
        if ($this->_moushiKbn === static::MOUSHI_KBN_HOUJI) {
            $niteiData = DataMapper_SekoNiteiEx::findHouji($db, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => static::NITEI_KBN_HOYO));
        } else {
            $niteiData = DataMapper_SekoNitei::find($db, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => static::NITEI_KBN_SOUGI));
            $niteiDataTuya = DataMapper_SekoNitei::find($db, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => static::NITEI_KBN_TUYA));
        }
        // 火葬式のときは見積式場が部門区分が共通部門かどうかを判別する
        if (isset($keishiki_kbn) && $keishiki_kbn == static::KEISHIKI_KBN_KASO) {
            if (count($estBumonData) > 0 && $estBumonData[0]['bumon_kbn'] != static::BUMON_KBN_KYOTU) {
                $flg = false;
            }
        } else {
            // 当社式場を未使用の場合は共通部門かどうかを判別する
            if (count($niteiData) > 0 && $niteiData[0]['v_free2'] == '1') {
                if (count($estBumonData) > 0 && $estBumonData[0]['bumon_kbn'] != static::BUMON_KBN_KYOTU) {
                    // 違った場合は通夜が式場使用していれば確認する
                    if ($this->_moushiKbn !== static::MOUSHI_KBN_HOUJI && count($niteiDataTuya) > 0 && (!isset($niteiDataTuya[0]['v_free2']) || $niteiDataTuya[0]['v_free2'] != '1')) {
                        $kaijyoData = DataMapper_Kaijyo::find($db, array('kaijyo_cd' => $niteiDataTuya[0]['basho_cd']));
                        if (count($kaijyoData) > 0 && $this->_estShikijoCd != $kaijyoData[0]['bumon_cd']) {
                            $flg = false;
                        }
                    } else {
                        $flg = false;
                    }
                }
            } else {
                // 当社式場の場合は会場マスタの部門と一致するかどうか判別する
                $kaijyoData = DataMapper_Kaijyo::find($db, array('kaijyo_cd' => $niteiData[0]['basho_cd']));
                if (count($kaijyoData) > 0 && $this->_estShikijoCd != $kaijyoData[0]['bumon_cd']) {
                    $flg = false;
                }
            }
        }
        return $flg;
    }

    /**
     *
     * 施行日程情報チェック処理
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/07
     * @return boolean 
     */
    protected function checkNitei($db) {
        $flg = true;
        $sougiflg = true;
        $msg = null;
        // 施行日程情報を取得する
        $niteiData = $this->getNiteiData();
        // 葬儀の場合は通夜・葬儀・火葬がチェック対象
        if ($this->_moushiKbn != self::MOUSHI_KBN_HOUJI) {
            foreach ($niteiData as $niteiOne) {
                switch ($niteiOne['nitei_kbn']) {
                    case self::NITEI_KBN_TUYA:
                        // 通夜無しチェックになければ設定をチェックする
                        if ($niteiOne['v_free1'] != '1') {
                            // 当社式場を使用している場合は施設予約番号あるかチェックする
                            if ($niteiOne['v_free2'] != '1') {
                                if (strlen($niteiOne['v_free3']) == 0) {
                                    $flg = false;
                                    $msg .= '通夜の施設,';
                                }
                            } else {
                                // 式場未使用の場合は場所が設定されているかチェックする
                                if (isset($niteiOne['basho_kbn'])) {
                                    if ($niteiOne['basho_kbn'] != self::SOUGI_BASHO_HOME && strlen($niteiOne['basho_nm']) == 0) {
                                        $flg = false;
                                        $msg .= '通夜の施設,';
                                    }
                                } else {
                                    $flg = false;
                                    $msg .= '通夜の施設,';
                                }
                            }
                            // 日付が入力されているかチェックする
                            if (strlen($niteiOne['nitei_ymd']) == 0) {
                                $flg = false;
                                $msg .= '通夜の日程,';
                            }
                        }
                        break;
                    case self::NITEI_KBN_KASO:
                        // 火葬無しチェックになければ設定をチェックする
                        if ($niteiOne['v_free1'] != '1') {
                            // 日付が入力されているかチェックする
                            if (strlen($niteiOne['nitei_ymd']) == 0) {
                                $flg = false;
                                $msg .= '火葬の日程,';
                            }
                            // 火葬場所が入力されているかチェックする
                            if (strlen($niteiOne['basho_nm']) == 0) {
                                $flg = false;
                                $msg .= '火葬の施設,';
                            }
                        }
                        break;
                    case self::NITEI_KBN_SOUGI:
                        // 当社式場を使用している場合は施設予約番号あるかチェックする
                        if ($niteiOne['v_free2'] != '1') {
                            if (strlen($niteiOne['v_free3']) == 0) {
                                $flg = false;
                                $msg .= '葬儀の施設,';
                            }
                        } else {
                            // 式場未使用の場合は場所が設定されているかチェックする
                            if (isset($niteiOne['basho_kbn'])) {
                                if ($niteiOne['basho_kbn'] != self::SOUGI_BASHO_HOME && strlen($niteiOne['basho_nm']) == 0) {
                                    $flg = false;
                                    $sougiflg = false;
                                    $msg .= '葬儀の施設,';
                                }
                            } else {
                                $flg = false;
                                $sougiflg = false;
                                $msg .= '葬儀の施設,';
                            }
                        }
                        // 日付が入力されているかチェックする
                        if (strlen($niteiOne['nitei_ymd']) == 0) {
                            $flg = false;
                            $sougiflg = false;
                            $msg .= '葬儀の日程,';
                        }
                        // 葬儀日が設定されていなければ更新する
                        if ($sougiflg && !isset($this->_selectSekoKihon['sougi_ymd'])) {
                            list($sql, $param) = DataMapper_Utils::makeUpdateSQL('seko_kihon_info', array('sougi_ymd' => $niteiOne['nitei_ymd']), array('seko_no' => $this->_sekoNo));
                            $db->easyExecute($sql, $param);
                        }
                        break;
                }
            }
        } else if ($this->_moushiKbn == self::MOUSHI_KBN_HOUJI) {
            foreach ($niteiData as $niteiOne) {
                switch ($niteiOne['nitei_kbn']) {
                    case self::NITEI_KBN_HOUJI_SEKO:
                        // 日付が入力されているかチェックする
                        if (strlen($niteiOne['nitei_ymd']) == 0) {
                            $flg = false;
                            $msg .= '施行日の日程,';
                        }
                        break;
                }
            }
        }
        return array($flg, $msg);
    }

    /**
     *
     * 弔意対応商品の商品部門コードが見積式場ORその上位部門と一致するか判定
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/07
     * @return boolean 
     */
    protected function checkChoiShohin() {
        $flg = true;
        // 弔意対応商品が設定されていなければスルー
        if (!isset($this->_selectSekoKihon['choi_shohin_cd'])) {
            return $flg = true;
        }
        if (($this->_selectSekoKihon['choi_shohin_bumon_cd'] != $this->_selectSekoKihon['est_shikijo_cd']) && ($this->_selectSekoKihon['choi_shohin_bumon_cd'] != $this->_selectSekoKihon['est_oya_bumon_cd'])) {
            $flg = false;
        }
        return $flg;
    }

    /**
     * 会員区分チェック処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @param array $dataSekoKihon 施行基本データ
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return boolean 成功可否
     */
    protected function checkKaiinKbn() {

        $db = Msi_Sys_DbManager::getMyDb();
        $flg = true;
        $msg = null;
        $yoto_kbn_arr = array();    // 用途区分ごとの配列
        $dataSekoKihon = $this->getSekoKihon();
        $dataGojokaiMemberCol = DataMapper_SekoGojokaiMember::find($db, array('seko_no' => $this->_sekoNo));
        $dataSekoKeiyakusakiInfo = $this->getKeiyakusakiInfo();
        foreach ($dataGojokaiMemberCol as $one) {
//            switch ($dataSekoKihon['kaiin_kbn']) {
//                case static::KAIIN_KBN_GOJO :   // 互助会
//                    // 区分が他社で用途が金額充当OR使用しない以外に設定されているものはエラー
//                    if ($one['kaiin_info_kbn'] == self::KAIIN_INFO_TASYA 
//                        && ($one['yoto_kbn'] != self::YOTO_KBN_JUTO && $one['yoto_kbn'] != self::YOTO_KBN_NOUSE)) {
//                        $msg .= '互助会情報の入力に誤りがあります。会員情報/加入状況の「他社」区分では用途に「金額充当」または「使用しない」を選択してください。';
//                        $flg = false;
//                        break;
//                    } else if ($one['kaiin_info_kbn'] == self::KAIIN_INFO_OM
//                        && ($one['yoto_kbn'] != self::YOTO_KBN_COURSE && $one['yoto_kbn'] != self::YOTO_KBN_PLAN && $one['yoto_kbn'] != self::YOTO_KBN_NOUSE)) {
//                        $msg .= '互助会情報の入力に誤りがあります。会員情報/加入状況の「OM」区分では用途に「コース施行」または「使用しない」を選択してください。';
//                        $flg = false;
//                        break;
//                    }
//                    break;
//            }
            $yoto_kbn_arr[$one['yoto_kbn']] = $one['yoto_kbn'];
        }
        if (isset($msg)) {
            return array($flg, $msg);
        }
        // 会員区分ごとにチェック処理を行う
        switch ($dataSekoKihon['kaiin_kbn']) {
            case static::KAIIN_KBN_GOJO :   // 互助会
//                if (isset($yoto_kbn_arr[static::YOTO_KBN_COURSE]) && isset($yoto_kbn_arr[static::YOTO_KBN_PLAN])) {
//                    $msg .= '互助会情報の入力に誤りがあります。会員情報/加入状況の用途には「コース施行」または「プラン施行」のいずれかを選択してください。';
//                    $flg = false;
//                    break;
//                }
//                if (isset($yoto_kbn_arr[static::YOTO_KBN_KAIYAKU])) {
//                    $msg .= '互助会情報の入力に誤りがあります。会員情報/加入状況の用途「解約指図払い」は一般の場合のみ指定できます。';
//                    $flg = false;
//                    break;
//                }
                // 葬儀のみ
                if ($this->_moushiKbn != static::MOUSHI_KBN_HOUJI) {
                    if (!isset($yoto_kbn_arr[static::YOTO_KBN_COURSE]) && !isset($yoto_kbn_arr[static::YOTO_KBN_PLAN])) {
                        $msg .= '互助会情報が設定されていません。会員情報/加入状況の項目を設定してください。';
                        $flg = false;
                        break;
                    }
                }
                break;
            case static::KAIIN_KBN_CO :    // 企業
            case static::KAIIN_KBN_NET :    // コプセ
            case static::KAIIN_KBN_LIFE :    // ライフコミュニティ
                if (isset($yoto_kbn_arr[static::YOTO_KBN_COURSE]) || isset($yoto_kbn_arr[static::YOTO_KBN_PLAN]) || isset($yoto_kbn_arr[static::YOTO_KBN_JUTO]) || isset($yoto_kbn_arr[static::YOTO_KBN_KAIYAKU])
                ) {
                    $msg .= '会員区分が互助会以外で互助会利用の入力があります。会員情報/加入状況の用途を確認してください。';
                    $flg = false;
                    break;
                }
                if (strlen($dataSekoKeiyakusakiInfo['partner_cd']) == 0) {
                    $msg .= '会員情報/その他加入確認の契約先番号を入力してください。';
                    $flg = false;
                    break;
                }
                break;
        }
        return array($flg, $msg);
    }

    /**
     * 搬送データチェック処理 
     *
     * <AUTHOR> Tosaka
     * @since 2020/07/14
     * @return boolean 成功可否
     */
    protected function checkHansoData() {

        $flg = true;
        $msg = '';
        $db = Msi_Sys_DbManager::getMyDb();
        // 搬送からの引き継ぎ施行でなければスルー
        $kihonInfo = DataMapper_SekoKihon::find2($this->_sekoNo);
        if (Msi_Sys_Utils::myCount($kihonInfo) == 0) {
            $flg = false;
            $msg = '施行データが存在しません。';
            return array($flg, $msg);
        }
        if (isset($kihonInfo['free5_cd'])) {
            // 搬送伝票存在チェック
            $hansoData = DataMapper_Hanso_HansoDenpyo::find($db, array('uketsuke_no' => $kihonInfo['free5_cd'], 'hanso_kbn' => '1'));
            if (Msi_Sys_Utils::myCount($hansoData) == 0) {
                $flg = false;
                $msg = '搬送伝票データが存在しません。';
                return array($flg, $msg);
            } else {
                // 搬送料金等を反映させたかチェックする
                if (isset($hansoData[0]['hanso_hanei_flg']) && $hansoData[0]['hanso_hanei_flg'] == '1') {
                    $flg = true;
                } else {
                    $flg = false;
                    $msg = '搬送料金が保存されていません。';
                    return array($flg, $msg);
                }
            }
        }
        return array($flg, $msg);
    }

    /**
     * 部門コード更新処理
     *
     * <AUTHOR> Tosaka
     * @since 2021/xx/xx
     */
    protected function updBumon($db) {

        $upd = array();
        $where = array();
        $upd['bumon_cd'] = $this->_bumonCd;
        $where['denpyo_no'] = $this->getJuchudenpyoNo();
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo", $upd, $where);
        $db->easyExecute($sql, $param);
        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo_msi", $upd, $where);
        $db->easyExecute($sql, $param);
//        // 喪家外供花供の仮登録データを取得
//        $sql = "
//            SELECT j.denpyo_no
//            FROM juchu_denpyo j
//            LEFT JOIN uriage_denpyo u
//                ON u.denpyo_no = j.denpyo_no
//                AND u.delete_flg = 0
//            WHERE j.delete_flg = 0
//                AND j.data_kbn = 4
//                AND j.juchusaki_kbn = 2
//                AND u.uri_den_no IS NULL
//                AND j.seko_no = :seko_no
//                ";
//        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
//        if (count($select) > 0) {
//            foreach ($select as $val) {
//                $upd = array();
//                $where = array();
//                $upd['bumon_cd'] = $this->_bumonCd;
//                $where['denpyo_no'] = $val['denpyo_no'];
//                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo", $upd, $where);
//                $db->easyExecute($sql, $param);
//                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo_msi", $upd, $where);
//                $db->easyExecute($sql, $param);
//            }
//        }
        // 喪家外供花供の本登録かつ未承認のデータを取得
//        $sql = "
//            SELECT j.denpyo_no,u.uri_den_no,s.seikyu_den_no,s.bun_gas_kbn_num
//            FROM juchu_denpyo j
//            INNER JOIN uriage_denpyo u
//                ON u.denpyo_no = j.denpyo_no
//                AND u.delete_flg = 0
//            INNER JOIN seikyu_denpyo s
//                ON s.uri_den_no = u.uri_den_no
//                AND s.seikyu_approval_status = 0
//                AND s.delete_flg = 0
//            WHERE j.delete_flg = 0
//                AND j.data_kbn = 4
//                AND j.juchusaki_kbn = 2
//                AND j.seko_no = :seko_no
//                ";
//        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
//        if (count($select) > 0) {
//            foreach ($select as $val) {
//                $upd = array();
//                $jwhere = array();
//                $uwhere = array();
//                $swhere = array();
//                $upd['bumon_cd'] = $this->_bumonCd;
//                $jwhere['denpyo_no'] = $val['denpyo_no'];
//                $uwhere['uri_den_no'] = $val['uri_den_no'];
//                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo", $upd, $jwhere);
//                $db->easyExecute($sql, $param);
//                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("juchu_denpyo_msi", $upd, $jwhere);
//                $db->easyExecute($sql, $param);
//                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo", $upd, $uwhere);
//                $db->easyExecute($sql, $param);
//                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("uriage_denpyo_msi", $upd, $uwhere);
//                $db->easyExecute($sql, $param);
//                $swhere['seikyu_den_no'] = $val['seikyu_den_no'];
//                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $upd, $swhere);
//                $db->easyExecute($sql, $param);
//                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo_msi", $upd, $swhere);
//                $db->easyExecute($sql, $param);
//                // 分割OR合算されていれば分割先を更新
//                if (isset($val['bun_gas_kbn_num']) && $val['bun_gas_kbn_num'] == static::BUN_GAS_KBN_BUNMOTO) {
//                    $seikyuDen = DataMapper_SeikyuDenpyo::find($db, array('bun_gas_seikyu_den_no' => $val['seikyu_den_no'], 'seikyu_approval_status' => 0));
//                    foreach ($seikyuDen as $one) {
//                        $swhere['seikyu_den_no'] = $one['seikyu_den_no'];
//                        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $upd, $swhere);
//                        $db->easyExecute($sql, $param);
//                        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo_msi", $upd, $swhere);
//                        $db->easyExecute($sql, $param);
//                    }
//                } else if (isset($val['bun_gas_kbn_num']) && $val['bun_gas_kbn_num'] == static::BUN_GAS_KBN_GASMOTO) {
//                    $seikyuDen = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $val['bun_gas_seikyu_den_no'], 'seikyu_approval_status' => 0));
//                    foreach ($seikyuDen as $one) {
//                        $swhere['seikyu_den_no'] = $one['seikyu_den_no'];
//                        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo", $upd, $swhere);
//                        $db->easyExecute($sql, $param);
//                        list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seikyu_denpyo_msi", $upd, $swhere);
//                        $db->easyExecute($sql, $param);
//                    }
//                }
//            }
//        }
        return;
    }

    /**
     *
     * 見積式場とプランの式場が一致してるかどうかの判定
     *
     * <AUTHOR> Tosaka
     * @since 2025/02/xx
     * @return boolean 
     */
    protected function checkPlanShikijo() {
        $flg = false;
        $msg = null;
        // プランが設定されていないときはスルー
        if (!isset($this->_sekoPlanCd)) {
            return array($flg, $msg);
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $planData = DataMapper_SekoPlan::find($db, array('seko_plan_cd' => $this->_sekoPlanCd), false);
        $shikijo_cd = $planData[0]['shikijo_cd'];
        $curKaisyaCd = App_Utils::getCtxtKaisyaEasy();
        $bumonData = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $this->_estShikijoCd));
        if (Count($bumonData) > 0 && $bumonData[0]['bumon_shohin_sel_kbn'] === '1') {
            //自部門商品＋親部門商品(親部門はさかのぼる)
            $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $this->_estShikijoCd, null);
            foreach ($allOyaBumon as $oneBumon) {
                if ($oneBumon['bumon_shohin_sel_kbn'] === '1') {
                    if ($shikijo_cd == $oneBumon['ko_bumon_cd']) {
                        $flg = true;
                    }
                }
            }
        } else {
            // 会計部門と違う場合は会計部門の親部門を取得する
            if (isset($bumonData[0]['kaikei_bumon_cd'])) {
                $bumonData2 = DataMapper_Bumon::find($db, array('kaisya_cd' => $curKaisyaCd, 'bumon_cd' => $bumonData[0]['kaikei_bumon_cd']));
                if ($bumonData2[0]['bumon_shohin_sel_kbn'] === '1') {
                    if ($shikijo_cd == $bumonData[0]['kaikei_bumon_cd']) {
                        $flg = true;
                    }
                }
                $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $bumonData[0]['kaikei_bumon_cd'], null);
            } else {
                // 親部門商品(親部門はさかのぼる)のみ
                $allOyaBumon = DataMapper_BumonEx::findAllOyabumon($db, $this->_estShikijoCd, null);
            }
            foreach ($allOyaBumon as $oneBumon) {
                if ($oneBumon['bumon_kbn'] === '0') {
                    if ($shikijo_cd == $oneBumon['bumon_cd']) {
                        $flg = true;
                    }
                }
            }
        }
        return $flg;
    }

    /**
     *
     * 別注品（喪家未精算分）を取得する
     *
     * <AUTHOR> Tosaka
     * @since 2018/4/2
     * @return array データ
     */
    protected function getBechuData($uri_den_no) {
        return array();
    }
    
    /**
     *
     * 見積チェック処理
     *
     * <AUTHOR> Tosaka
     * @since 2018/4/2
     * @return array データ
     */
    public function checkmitsu() {
        $req = Msi_Sys_Utils::getRequestObject();
        try {
            $dataApp = Msi_Sys_Utils::json_decode($req->getPost('dataAppJson'));
            $funcMsg = $req->getPost('funcMsg');
            $this->_sekoNo = $dataApp['seko_no'];
            // 施行基本情報を設定する
            $this->setInitParam();
            // すでにステータスが承認済以上ならスルーする
            if ($this->_selectSekoKihon['status_kbn'] < static::STATUS_KBN_SHONIN) {
                // 明細チェック処理
                list($flg, $msg) = $this->checkMsi($funcMsg);
                if (!$flg) {
                    $this->outWarnJson($msg);
                    return;
                }
            }
            $data = array(
                'status' => 'OK',
                'msg' => '',
            );
        } catch (Exception $e) {
            $err = $e->getMessage();
            $userMsg = Msi_Sys_MsgIf::errMsgForUser($err, $e);
            $data = array(
                'status' => 'NG',
                'msg' => $userMsg,
            );
        }
        Msi_Sys_Utils::outJson($data);
    }

}
