<?php

/**
 * Juchu_JuchuCustomerinfoCom
 *
 * 葬儀と法事のお客様情報共通クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuCustomerinfoCom
 * <AUTHOR> Sai
 * @since      2014/06/27
 * @version    2019/06/xx sai 軽減税率対応
 * @filesource 
 */

/**
 * 葬儀と法事のお客様情報共通クラス
 *
 * @category   App
 * @package    controllers\Juchu\JuchuCustomerinfoCom
 * <AUTHOR>
 * @since      2014/06/27
 */
abstract class Juchu_JuchuCustomerinfoCom extends Juchu_JuchuhenkoAbstract {

    /** コード区分: 0010=>申込区分 */
    const CODE_KBN_MOUSHI_KBN = '0010';

    /** コード区分: 0020=>葬儀区分 */
    const CODE_KBN_SOUGI_KBN = '0020';

    /** コード区分: 0030=>会員区分 */
    const CODE_KBN_KAIIN_KBN = '0030';

    /** コード区分: 0070=>配偶者 */
    const CODE_KBN_HAIGU_KBN = '0070';

    /** コード区分: 0440=>元号 */
    const CODE_KBN_GENGO = '0440';

    /** コード区分: 0060=>世帯主 */
    const CODE_KBN_SETAI_KBN = '0060';

    /** コード区分: 0050=>筆頭者 */
    const CODE_KBN_HITO_KBN = '0050';

    /** コード区分: 0040=>葬儀形式 */
    const CODE_KBN_KEISHIKI_KBN = '0040';

    /** コード区分: 0470=>入棺経 */
    const CODE_KBN_NYUKAN_KYO = '0470';

    /** コード区分: 0480=>出棺経 */
    const CODE_KBN_SYUKAN_KYO = '0480';

    /** コード区分: 0490=>火葬経 */
    const CODE_KBN_KASO_KYO = '0490';

    /** コード区分: 0210=>加入団体 */
    const CODE_KBN_KANYU_DANTAI = '0210';

    /** コード区分: 0450=>用途 */
    const CODE_KBN_YOTO_KBN = '0450';

    /** コード区分: 0220=>搬送業務区分 */
    const CODE_KBN_HS_GYOMU_KBN = '0220';

    /** コード区分: 0200=>宗派区分 */
    const CODE_KBN_SYUHA_KBN = '0200';

    /** コード区分: 0240=>宗旨区分 */
    const CODE_KBN_SYUSHI_KBN = '0240';

    /** コード区分: 0190=>続柄区分 */
    const CODE_KBN_ZOKU_KBN = '0190';

    /** コード区分: 0600=>搬送区分 */
    const HS_ANCHI_KBN = '0600';

    /** コード区分: 0670=>葬儀場所 */
    const CODE_SOGI_BASHO_KBN = '0670';

    /** コード区分: 0680=>壇払会場 */
    const CODE_DAN_KAIJO_KBN = '0680';

    /** コード区分: 0500=>納品場所区分 */
    const CODE_DELIVERY_KBN = '0500';

    /** コード区分: 0990=>申込区分(葬儀) */
    const CODE_KBN_MOUSHI_KBN_S = '0990';

    /** コード区分: 1000=>申込区分(法事) */
    const CODE_KBN_MOUSHI_KBN_H = '1000';

    /** コード区分: 1180=>目録手配対象エリア */
    const CODE_MT_AREA_KBN = '1180';

    /** コード区分: 1160=>目録手配品目 */
    const CODE_MT_ITEM_KBN = '1160';

    /** コード区分: 1190=>個人情報保護 */
    const CODE_P_INFO = '1190';

    /** コード区分: 1200=>自宅祭壇有 */
    const CODE_HIKI_1 = '1200';

    /** コード区分: 1210=>下駄 */
    const CODE_HIKI_2 = '1210';

    /** コード区分: 1220=>袖 */
    const CODE_HIKI_3 = '1220';

    /** コード区分: 1230=>自宅祭壇無 */
    const CODE_HIKI_4 = '1230';

    /** コード区分: 1240=>通夜祭壇 */
    const CODE_HIKI_5 = '1240';

    /** コード区分: 1250=>供物など */
    const CODE_HIKI_6 = '1250';

    /** コード区分: 1260=>仏花 */
    const CODE_HIKI_7 = '1260';

    /** コード区分: 1270=>自宅幕張 */
    const CODE_HIKI_8 = '1270';

    /** コード区分: 1280=>画鋲 */
    const CODE_HIKI_9 = '1280';

    /** コード区分: 1290=>前飾り */
    const CODE_HIKI_10 = '1290';

    /** コード区分: 1300=>柩 */
    const CODE_HIKI_11 = '1300';

    /** コード区分: 1310=>幅 */
    const CODE_HIKI_12 = '1310';

    /** コード区分: 1320=>寺祭壇 */
    const CODE_HIKI_13 = '1320';

    /** コード区分: 1330=>湯灌 */
    const CODE_HIKI_14 = '1330';

    /** コード区分: 1340=>年金受給者 */
    const CODE_HIKI_15 = '1340';

    /** コード区分: 1350=>同意書１ */
    const CODE_HIKI_16 = '1350';

    /** コード区分: 1360=>同意書２ */
    const CODE_HIKI_17 = '1360';

    /** コード区分: 1370=>写真預かり */
    const CODE_HIKI_18 = '1370';

    /** コード区分: 1380=>枕経 */
    const CODE_HIKI_19 = '1380';

    /** コード区分: 1390=>寺院連絡 */
    const CODE_HIKI_20 = '1390';

    /** コード区分: 1400=>隣組集合 */
    const CODE_HIKI_21 = '1400';

    /** コード区分: 1410=>持参品 */
    const CODE_HIKI_22 = '1410';

    /** コード区分: 1420=>書物 */
    const CODE_HIKI_23 = '1420';

    /** コード区分: 1430=>掛図 */
    const CODE_HIKI_24 = '1430';

    /** コード区分: 1440=>枕花有無 */
    const CODE_HIKI_25 = '1440';

    /** コード区分: 1450=>遺体衛生保全１ */
    const CODE_HIKI_26 = '1450';

    /** コード区分: 1460=>遺体衛生保全２ */
    const CODE_HIKI_27 = '1460';

    /** コード区分: 1470=>目録備考 */
    const CODE_MT_BIKO = '1470';

    /** コード区分: 1480=>葬家住所 */
    const CODE_SK_ADDR = '1480';

    /** コード区分: 1490=>用途法事 */
    const CODE_KBN_YOTO_HOUJI = '1490';

    /** コード区分: 1570=>連絡方法 */
    const CODE_KBN_RENRAKU = '1570';

    /** コード区分: 1580=>相談内容 */
    const CODE_KBN_CONTENT1 = '1580';

    /** コード区分: 1590=>確認内容 */
    const CODE_KBN_CONTENT2 = '1590';

    /** コード区分: 1600=>××がない場合 */
    const CODE_KBN_IF_NASHA = '1600';
    
    /** コード区分: 1602=>相続紹介カード送付先*/
    const CODE_KBN_S_SOFU = '1602';

    /** コード区分: 0890=>時間 */
    const CODE_HIKI_HOUR = '0890';

    /** コード区分: 0900=>時刻 */
    const CODE_HIKI_MIN = '0900';

    /** コード区分: 1700=>リン鐘 */
    const CODE_HIKI_RIN = '1700';

    /** コード区分: 1710=>団子セット */
    const CODE_HIKI_DANGO = '1710';

    /** コード区分: 1720=>忌中紙・訃報紙 */
    const CODE_HIKI_KITYU = '1720';

    /** コード区分: 1730=>予約の有無 */
    const CODE_HIKI_YOYAKU = '1730';

    /** コード区分: 1610=>互助会コース */
    const CODE_GOJOKAI_COSE = '1610';

    /** コード区分: 1620=>加入団体（特約） */
    const CODE_KANYU_TOKU = '1620';

    /** コード区分: 1790=>元号 */
    const CODE_KBN_GENGO2 = '1790';

    /** コード区分: 1810=>納棺着替え */
    const CODE_KBN_KIGAE = '1810';

    /** コード区分: 1820=>初七日 */
    const CODE_KBN_NANOKA = '1820';

    /** コード区分: 1830=>菩提寺紹介者 */
    const CODE_KBN_TERA_SHOKAI = '1830';

    /** コード区分: 1930=>移送者区分 */
    const CODE_KBN_ISO_KBN = '1930';

    /** コード区分: 2010=>施行場所 */
    const CODE_KBN_SEKO_BASHO = '2010';

    /** コード区分: 2000=>会場利用 */
    const CODE_KBN_KAIJO_USE = '2000';

    /** コード区分: 2400=>各方面よりお問合せのあった場合、お答えして良い項目は */
    const CODE_KBN_IRAI_KAKUNIN1 = '2400';

    /** コード区分: 2410=>各方面よりお供物(生花等)のご注文があった場合、承りは */
    const CODE_KBN_IRAI_KAKUNIN2 = '2410';

    /** コード区分: 火葬場の式場 */
    const CODE_KBN_SIKIJO = '2500';

    /** コード区分: 事前相談 */
    const CODE_KBN_JIZEN_SODAN = '2660';

    /** コード区分: 事前相談場所 */
    const CODE_KBN_JIZEN_SODAN_BASHO = '2970';
    
    /** コード区分: 位牌区分1 */
    const CODE_KBN_IHAI_KBN_1 = '5960';
    /** コード区分: 位牌区分2 */
    const CODE_KBN_IHAI_KBN_2 = '5970';
    
    /** 施行依頼区分 */
    const CODE_KBN_SEKO_IRAI = '6700';
    
    /** 紹介区分 */
    const CODE_KBN_SHOKAI = '6690';
    
    /** 通夜他・葬儀他人数 */
    const CODE_KBN_TSU_SOU_NINZU = '6910';

    /** コード区分:一時参照用 */
    protected $_codeKbn = '';

    /** 顧客コード */
    protected $_customerCd = '';

    /** 値引き互助会区分 */
    protected $_nebikiGojokaiKbn = null;

    /**
     * コード名称マスタ取得処理
     * 
     * <AUTHOR> Sai
     * @since      2014/02/13
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return  array コード名称マスタ
     */
    protected function getCodeNameMst() {
        $db = Msi_Sys_DbManager::getMyDb();
        // コード名称マスタを取得する

        $sql = "
        SELECT
            code_kbn            -- コード区分
            ,code_kbn_nm        -- コード区分名
            ,kbn_value_cd       -- 区分値コード
            ,kbn_value_cd_num   -- 区分値コード数値
            ,kbn_value_lnm      -- 区分値正式名
            ,kbn_value_snm      -- 区分値簡略名
        FROM
            code_nm_mst
        WHERE
            delete_flg = 0
        AND code_kbn IN (
             ?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?
            ,?,?,?,?,?,?,?,?,?,?,?,?
            ,?,?,?,?,?,?,?,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?
            ,?,?,?,?,?,?
            ,?,?
            ,?,?,?,?
            ,?,?,?
            ,?,?,?,?
            ,?,?,?,?,?,?
                        )
        ORDER BY
            code_kbn
            ,disp_nox
            ,kbn_value_cd_num
            ,kbn_value_cd
                ";

        $select = $db->easySelect($sql, array(
            self::CODE_KBN_MOUSHI_KBN,
            self::CODE_KBN_SOUGI_KBN,
            self::CODE_KBN_KAIIN_KBN,
            self::CODE_KBN_HAIGU_KBN,
            self::CODE_KBN_GENGO,
            self::CODE_KBN_KEISHIKI_KBN,
            self::CODE_KBN_SETAI_KBN,
            self::CODE_KBN_HITO_KBN,
            self::CODE_KBN_NYUKAN_KYO,
            self::CODE_KBN_SYUKAN_KYO,
            self::CODE_KBN_KASO_KYO,
            self::CODE_KBN_KANYU_DANTAI,
            self::CODE_KBN_YOTO_KBN,
            self::CODE_KBN_HS_GYOMU_KBN,
            self::CODE_KBN_SYUHA_KBN,
            self::CODE_KBN_SYUSHI_KBN,
            self::CODE_KBN_ZOKU_KBN,
            self::HS_ANCHI_KBN,
            self::CODE_SOGI_BASHO_KBN,
            self::CODE_DAN_KAIJO_KBN,
            self::CODE_DELIVERY_KBN,
            self::CODE_KBN_MOUSHI_KBN_S,
            self::CODE_KBN_MOUSHI_KBN_H,
            self::CODE_MT_AREA_KBN,
            self::CODE_MT_ITEM_KBN,
            self::CODE_P_INFO,
            self::CODE_HIKI_1, self::CODE_HIKI_2, self::CODE_HIKI_3, self::CODE_HIKI_4, self::CODE_HIKI_5, self::CODE_HIKI_6,
            self::CODE_HIKI_7, self::CODE_HIKI_8, self::CODE_HIKI_9, self::CODE_HIKI_10, self::CODE_HIKI_11, self::CODE_HIKI_12,
            self::CODE_HIKI_13, self::CODE_HIKI_14, self::CODE_HIKI_15, self::CODE_HIKI_16, self::CODE_HIKI_17, self::CODE_HIKI_18,
            self::CODE_HIKI_19, self::CODE_HIKI_20, self::CODE_HIKI_21, self::CODE_HIKI_22, self::CODE_HIKI_23, self::CODE_HIKI_24,
            self::CODE_HIKI_25, self::CODE_HIKI_26, self::CODE_HIKI_27,
            self::CODE_MT_BIKO, self::CODE_SK_ADDR,
            self::CODE_KBN_RENRAKU, self::CODE_KBN_IF_NASHA, self::CODE_KBN_S_SOFU,
            self::CODE_HIKI_HOUR, self::CODE_HIKI_MIN, self::CODE_HIKI_RIN, self::CODE_HIKI_DANGO, self::CODE_HIKI_KITYU, self::CODE_HIKI_YOYAKU,
            self::CODE_GOJOKAI_COSE, self::CODE_KANYU_TOKU,
            self::CODE_KBN_GENGO2, self::CODE_KBN_KIGAE, self::CODE_KBN_NANOKA, self::CODE_KBN_TERA_SHOKAI,
            self::CODE_KBN_ISO_KBN, self::CODE_KBN_SEKO_BASHO, self::CODE_KBN_KAIJO_USE,
            self::CODE_KBN_IRAI_KAKUNIN1, self::CODE_KBN_IRAI_KAKUNIN2, self::CODE_KBN_SIKIJO, self::CODE_KBN_JIZEN_SODAN,
            self::CODE_KBN_JIZEN_SODAN_BASHO, self::CODE_KBN_IHAI_KBN_1, self::CODE_KBN_IHAI_KBN_2, self::CODE_KBN_SHOKAI, self::CODE_KBN_SEKO_IRAI,self::CODE_KBN_TSU_SOU_NINZU
        ));
        return $select;
    }

    /**
     *
     * 施行互助会加入者を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 施行互助会加入者情報
     */
    protected function getGojokaiMember($row = 5) {

        $dataGojokaiMember = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                gm.seko_no          -- 施行番号
                ,gm.kain_no         -- 会員番号
                ,gm.gojokai_cose_cd -- 互助会コースコード
                ,gm.kanyu_nm        -- 加入者名
                ,gm.az_gojokai_kbn  -- 互助会証書
                ,gm.yoto_kbn        -- 用途
                ,gm.harai_gaku      -- 払込金額
                ,gm.wari_gaku       -- 前納割引額
                ,gm.harai_no        -- 払込回数
                ,TO_CHAR(gm.kanyu_dt ,'YYYY/MM/DD') AS kanyu_dt-- 加入年月日
                ,TO_CHAR(gm.zei_kijyn_ymd ,'YYYY/MM/DD') AS zei_kijyn_ymd-- 消費税基準日
                ,gm.zei_cd          -- 消費税コード
                ,gm.keiyaku_gaku    -- 契約金額
                ,gm.cose_chg_gaku   -- コース変更差額金
                ,gm.early_use_cost  -- 早期利用費
                ,gm.course_snm_cd   -- 互助会コース名（イニシャル）
                ,gm.apply_no        -- 申込番号
                ,gm.plan_convert_gaku  -- プラン換算金額
                ,gcm.join_convert_cnt  -- 互助会加入換算口数
            FROM
                seko_gojokai_member gm
                LEFT JOIN gojokai_couse_mst gcm
                    ON
                    (
                        gm.gojokai_cose_cd = gcm.gojokai_cose_cd
                    AND CURRENT_DATE BETWEEN gcm.tekiyo_st_date AND gcm.tekiyo_ed_date
                    AND gcm.delete_flg = 0
                    )
            WHERE
                    gm.seko_no = :seko_no
                AND gm.delete_flg = 0
            ORDER BY
                gm.yoto_kbn 
                ,gm.kanyu_dt
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        // 施行互助会加入者に存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $oneRowData = array();
                $oneRowData['seko_no'] = $select[$i]['seko_no'];
                $oneRowData['kain_no'] = trim($select[$i]['kain_no']);
                $oneRowData['gojokai_cose_cd'] = $select[$i]['gojokai_cose_cd'];
                $oneRowData['kanyu_nm'] = $select[$i]['kanyu_nm'];
                $oneRowData['az_gojokai_kbn'] = $select[$i]['az_gojokai_kbn'];
                $oneRowData['yoto_kbn'] = $select[$i]['yoto_kbn'];
                $oneRowData['harai_gaku'] = $select[$i]['harai_gaku'];
                $oneRowData['wari_gaku'] = $select[$i]['wari_gaku'];
                $oneRowData['harai_no'] = $select[$i]['harai_no'];
                $oneRowData['kanyu_dt'] = $select[$i]['kanyu_dt'];
//                $kanyu_dt = $select[$i]['kanyu_dt'];
//                list( $y, $m, $d ) = $this->sliceYmd($kanyu_dt);
//                if (isset($y) && isset($m) && isset($d)) {
//                    list( $gen, $year, $mon, $day ) = Msi_Sys_Utils::arrSeirekiToWareki($y, $m, $d, false);
//                    $oneRowData['kanyu_dt_gen'] = $gen;
//                    $oneRowData['kanyu_dt'] = sprintf("%02d/%02d/%02d", $year, $mon, $day);
//                }
                $oneRowData['zei_kijyn_ymd'] = $select[$i]['zei_kijyn_ymd'];
                $oneRowData['keiyaku_gaku'] = $select[$i]['keiyaku_gaku'];
                $oneRowData['cose_chg_gaku'] = $select[$i]['cose_chg_gaku'];
                $oneRowData['early_use_cost'] = $select[$i]['early_use_cost'];
                $oneRowData['course_snm_cd'] = $select[$i]['course_snm_cd'];
                $oneRowData['apply_no'] = $select[$i]['apply_no'];
                $oneRowData['plan_convert_gaku'] = $select[$i]['plan_convert_gaku'];
                $oneRowData['join_convert_cnt'] = $select[$i]['join_convert_cnt'];
                $dataGojokaiMember[$i] = $oneRowData;
            }
        }
        // 5レコード返す
        while (count($dataGojokaiMember) < $row) {
            array_push($dataGojokaiMember, array());
        }
        return $dataGojokaiMember;
    }

    /**
     *
     * 施行請求先情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/3/10
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 施行請求先情報
     */
    protected function getSekyuInfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
             si.seko_no             -- 施行番号
            ,si.sekyu_cd            -- 請求先コード
            ,si.sekyu_nm            -- 請求先名
            ,si.sekyu_knm           -- 請求先名カナ
            ,si.sekyu_file_nm       -- 添付ファイル名
            ,si.moshu_kankei_kbn AS sekyu_moshu_kankei_kbn  -- 喪主との関係
            ,si.moshu_kankei AS sekyu_moshu_kankei          -- 関係
            ,si.yubin_no AS sekyu_yubin_no                  -- 請求先郵便番号
            ,si.addr1 AS sekyu_addr1                        -- 請求先住所1
            ,si.addr2 AS sekyu_addr2                        -- 請求先住所2
            ,si.tel AS sekyu_tel                            -- 請求先電話番号
            ,si.mobile_tel                                  -- 請求先携帯番号
            ,si.fax AS sekyu_fax                            -- 請求先FAX
            ,si.biko1 AS sekyu_biko1                        -- 請求先備考１
            ,si.kojin_kankei_kbn                            -- 故人からみた続柄
        FROM
            sekyu_saki_info si
        WHERE
            si.seko_no = :seko_no
        AND si.sekyu_cd = :sekyu_cd
        AND si.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo, 'sekyu_cd' => $this->_sekyuCd));
        return $select;
    }

    /**
     *
     * 施行互助会情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 施行互助会情報
     */
    protected function getGojokaiInfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
             gi.seko_no             -- 施行番号
            ,gi.rireki_kbn          -- 施行履歴有無
            ,gi.sodan_kbn           -- 事前相談有無
            ,gi.kanyu_kakunin_kbn   -- お客様加入確認有無
            ,gi.join_use_kbn        -- 加入団体利用有無
            ,gi.spec_agent_cd       -- 特約区分コード
            ,gi.spec_agent_kbn      -- 特約区分
            ,gi.kanyu_dantai_cd    -- 加入団体コード
            ,gi.kanyu_dantai_kbn    -- 加入団体区分
            ,gi.kanyu_dantai_ext    -- 加入団体（その他）
            ,gi.hoyu_kbn            -- 保有互助会有無
            ,gi.kazoku_kbn          -- 同居家族有無
            ,gi.meigi_kbn           -- 互助会名義区分
            ,gi.info_kbn            -- 名義変更説明有無
            ,gi.riyu_memo           -- 名義変更未説明理由
            ,gi.plan_use_prc        -- プラン利用金額
            ,gi.plan_use_kbn        -- プラン利用区分
            ,gi.plan_change_prc     -- プラン変更差額
        FROM
            seko_gojokai_info gi
        WHERE
            gi.seko_no = :seko_no
        AND gi.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     *
     * 互助会コースマスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/21
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return array 互助会コースマスタ
     */
    protected function getGojokaiCouseMst() {

        $dataGojokaiCouseMst = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                gcm.gojokai_cose_cd     -- 互助会コースコード
                ,gcm.gojokai_cose_iw    -- 互助会コースコード頭文字
                ,gojokai_prc            -- 互助会金額
                ,harai_yotei_cnt        -- 回数
                ,gojokai_kbn            -- 互助会区分
                ,yoto1_flg              -- 葬送儀礼
                ,yoto2_flg              -- 返礼品充当
                ,yoto3_flg              -- 壇払い充当
                ,yoto4_flg              -- 法事
                ,yoto5_flg              -- 予備１
                ,yoto6_flg              -- 予備２
                ,yoto7_flg              -- 予備３
                ,yoto8_flg              -- 予備４
                ,yoto9_flg              -- 予備５
                ,multiple1_flg          -- 葬送儀礼
                ,multiple2_flg          -- 返礼品充当
                ,multiple3_flg          -- 壇払い充当
                ,multiple4_flg          -- 法事
                ,multiple5_flg          -- 予備１
                ,multiple6_flg          -- 予備２
                ,multiple7_flg          -- 予備３
                ,multiple8_flg          -- 予備４
                ,multiple9_flg          -- 予備５
                ,plan_convert_prc       -- 互助会プラン換算金額
                ,join_convert_cnt       -- 互助会加入換算口数
                ,cnm.kbn_value_cd_num AS gojokai_group_no -- 互助会グループ番号　
                ,gcm.monthly_gaku       -- 月額
            FROM
                gojokai_couse_mst gcm
            LEFT JOIN 
                code_nm_mst cnm ON
                        gcm.gojokai_cose_iw = cnm.kbn_value_cd
                    AND cnm.code_kbn = '1610'
            WHERE
                    CURRENT_DATE BETWEEN gcm.tekiyo_st_date AND gcm.tekiyo_ed_date
                AND gcm.delete_flg = 0
                ";
        $select = $db->easySelect($sql);
        // 施行互助会加入者に存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $dataGojokaiCouseMst[$select[$i]['gojokai_cose_iw']] = $select[$i];
            }
        }
        return $dataGojokaiCouseMst;
    }

    /**
     *
     * 互助会金額マスタを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/08/27
     * @return array 互助会金額マスタ
     */
    protected function getGojokaiPrcMst() {

        $dataGojokaiPrcMst = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                grm.price_no            -- 管理No
                ,grm.course_price       -- コース金額
                ,grm.gojokai_kbn        -- 互助会区分
            FROM
                gojokai_price_mst grm
            WHERE
                    CURRENT_DATE BETWEEN grm.tekiyo_st_date AND grm.tekiyo_ed_date
                AND grm.delete_flg = 0
                ";
        $select = $db->easySelect($sql);
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $dataGojokaiPrcMst[$select[$i]['course_price']] = $select[$i];
            }
        }
        return $dataGojokaiPrcMst;
    }

    /**
     *
     * 互助会金額マスタを取得する(佐野商店カスタマイズ用)
     *
     * <AUTHOR> Sai
     * @since 2014/01/15
     * @return array 互助会金額マスタ
     */
    protected function getGojokaiPrcMst2() {

        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                grm.price_no            -- 管理No
                ,grm.course_price       -- コース金額
                ,grm.gojokai_kbn        -- 互助会区分
                ,grm.course_price_used  -- 互助会コースプラン利用金額
                ,grm.course_priority    -- 互助会コース優位性
                ,grm.gojokai_group_no   -- 互助会グループ番号
            FROM
                gojokai_price_mst grm
            WHERE
                    CURRENT_DATE BETWEEN grm.tekiyo_st_date AND grm.tekiyo_ed_date
                AND grm.delete_flg = 0
            ORDER BY
                course_priority DESC
                ";
        $select = $db->easySelect($sql);
        return $select;
    }

    /**
     * コード名称マスタフィルター処理
     * 
     * <AUTHOR> Sai
     * @since      2014/02/14
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param $dataCodeNameMst コード名称マスタ配列
     * @param $kbn 区分値コード数値
     * @param $key 通常はkbn_value_cd_numをキーにtureの場合はkbn_value_cdをキーに、
     * @return  array コード名称マスタ
     */
    protected function filter($dataCodeNameMst, $kbn) {
        $this->_codeKbn = $kbn;
        $codeNames = array_filter($dataCodeNameMst, function ($item) {
            return $item['code_kbn'] === $this->_codeKbn;
        }
        );
        return $codeNames;
    }

    /**
     *
     * 互助会完納を求める（2:返礼品と3:壇払が対象）
     *
     * <AUTHOR> Sai
     * @since 2014/06/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return int $ret 1：済み 2:返礼品未 3:壇払未
     */
    protected function checkHenreiDanbariKannou($dataGojokaiMemberCol) {
        $ret = '1';
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        foreach ($dataGojokaiMemberCol as $oneRow) {
            $yotoKbn = $oneRow['yoto_kbn'];
            if ($yotoKbn === '3') {
//                list( $y, $m, $d ) = $this->sliceYmd($oneRow['kanyu_dt']);
//                $oneRow['kanyu_dt'] = $this->wareki2seireki($oneRow['kanyu_dt_gen'], $y, $m, $d);
                $compFlg = $this->getTkKanNoFlg($oneRow, $gojokaiCouseMst);
                if ($compFlg === 0) {
                    $ret = $yotoKbn;
                    break;
                }
            }
        }
        return $ret;
    }

    /**
     *
     * 互助会特典の完納フラグを求める
     *
     * <AUTHOR> Sai
     * @since 2014/06/13
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param array $oneRow 施行互助会加入情報
     * @param array $gojokaiCouseMst 施行互助会マスタ
     * @return int $compFlg 0：未 1：済み
     */
    protected function getTkKanNoFlg($oneRow, $gojokaiCouseMst) {
        // 完納フラグ 0：未 1：済み
        $compFlg = 0;
        // コース変更差額金が存在したら 0:未を返す
        if ($oneRow['cose_chg_gaku'] > 0) {
            return $compFlg;
        }
        $haraiCnt = null; // 支払予定回数
        $kaiinNo = $oneRow['kain_no']; // 会員番号
        $kanyuYmd = $oneRow['kanyu_dt']; // 加入年月日
        // 支払予定回数を取得する
        foreach ($gojokaiCouseMst as $coseOneRow) {
            if (strpos($kaiinNo, $coseOneRow['gojokai_cose_iw']) === 0) {
                $haraiCnt = $coseOneRow['harai_yotei_cnt'];
                break;
            }
        }
        if ($haraiCnt !== null) {
            $diffMonth = $this->diffMonth($this->getZeiKijunYmd(), $kanyuYmd);
            if ($diffMonth >= $haraiCnt) {
                $compFlg = 1;
            }
        }
        return $compFlg;
    }

    /**
     *
     * 日付の月の差を求める
     *
     * <AUTHOR> Sai
     * @since 2014/06/13
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param string $ymd1 日付１
     * @param string $ymd2 日付２
     * @return int $mon $ymd1から$ymd2を引いた月数
     */
    private function diffMonth($ymd1, $ymd2) {
        $ptn = '/^(\d{4,4})(\D)?(\d{1,2})(\D)/';
        $match1 = null;
        $match2 = null;
        if (!preg_match($ptn, $ymd1, $match1)) {
            return null;
        }
        if (!preg_match($ptn, $ymd2, $match2)) {
            return null;
        }
        $y1 = (int) $match1[1];
        $m1 = (int) $match1[3];
        $y2 = (int) $match2[1];
        $m2 = (int) $match2[3];
        $mon = ($y1 - $y2) * 12 + $m1 - $m2;
        return $mon;
    }

    /**
     *
     * 互助会完納NGメッセージを出力する（2:返礼品と3:壇払が対象）
     *
     * <AUTHOR> Sai
     * @since 2014/06/27
     * @param string $flg 
     * @return 
     */
    protected function gojokaiKannoNgOutJson($flg) {

        $nm = '';
        $data['status'] = 'NG';
        if ($flg === '2') {
            $nm = '返礼品';
        } else if ($flg === '3') {
            $nm = '壇払い';
        }
        $data['msg'] = "払込金額が完納されていないため、" . $nm . "使用することができません。";
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 請求先情報保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/03/10
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @param array $dataSekyuInfo 請求先情報データ
     * @return int 更新件数
     */
    protected function saveSekyuInfo($db, $dataSekyuInfo) {
        // 請求番号
        $this->_sekyuCd = $dataSekyuInfo['sekyu_cd'];
        // 請求先共通情報設定
        $sekyuInfo = array();
        $sekyuInfo['sekyu_nm'] = $dataSekyuInfo['sekyu_nm'];  // 請求先名
        $sekyuInfo['sekyu_knm'] = $dataSekyuInfo['sekyu_knm'];  // 請求先名カナ
        $sekyuInfo['sekyu_file_nm'] = $dataSekyuInfo['sekyu_file_nm'];  // 添付ファイル名
        $sekyuInfo['moshu_kankei_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekyuInfo['sekyu_moshu_kankei_kbn']);  // 喪主との関係
        $sekyuInfo['moshu_kankei'] = $dataSekyuInfo['sekyu_moshu_kankei'];  // 関係
        $sekyuInfo['yubin_no'] = $dataSekyuInfo['sekyu_yubin_no'];  // 請求先郵便番号
        $sekyuInfo['addr1'] = $dataSekyuInfo['sekyu_addr1'];  // 請求先住所1
        $sekyuInfo['addr2'] = $dataSekyuInfo['sekyu_addr2'];  // 請求先住所2
        $sekyuInfo['tel'] = $dataSekyuInfo['sekyu_tel'];  // 請求先電話番号
        $sekyuInfo['mobile_tel'] = $dataSekyuInfo['mobile_tel'];  // 請求先携帯番号
        $sekyuInfo['fax'] = $dataSekyuInfo['sekyu_fax'];  // 請求先FAX
        $sekyuInfo['biko1'] = $dataSekyuInfo['sekyu_biko1'];  // 請求先備考１
        if (isset($dataSekyuInfo['kojin_kankei_kbn'])) {
            $sekyuInfo['kojin_kankei_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekyuInfo['kojin_kankei_kbn']);  // 故人からみた続柄
        }
        // 請求先名添付ファイル存在するときに登録する
        if ($dataSekyuInfo['sekyu_file']) {
            $sekyuInfo['sekyu_file_nm'] = $dataSekyuInfo['sekyu_file'];
        }
        if (empty($this->_sekyuCd)) {
            // 請求番号自動採番
            $this->_sekyuCd = $this->getAutoSekyuNo($db);
            // 請求先登録情報設定
            $sekyuInfo['seko_no'] = $this->_sekoNo;  // 施行番号
            $sekyuInfo['sekyu_cd'] = $this->_sekyuCd;  // 請求先コード
            // 請求先情報登録SQL
            //$sql = $this->makeInsertSQL('sekyu_saki_info', $sekyuInfo);
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("sekyu_saki_info", $sekyuInfo);
        } else {
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['sekyu_cd'] = $this->_sekyuCd;  // 請求先コード
            $where['delete_flg'] = 0;  // 削除フラグ
            // 請求先情報更新SQL
            //$sql = $this->makeUpdateSQL('sekyu_saki_info', $sekyuInfo, $where);
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("sekyu_saki_info", $sekyuInfo, $where);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     *
     *  請求先番号採番処理
     *
     * <AUTHOR> Sai
     * @since 2014/3/10
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @return string 請求先番号
     */
    private function getAutoSekyuNo($db) {
        $kijyunYmd = Msi_Sys_Utils::getDate();
        $sekyuCd = App_ClsGetCodeNo::GetCodeNo($db, 'sekyu_saki_info', 'sekyu_cd', $kijyunYmd);
        return $sekyuCd;
    }

    /**
     * 施行互助会情報保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @param array $dataGojokaiInfo 施行互助会情報データ
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return int 更新件数
     */
    protected function saveGojokaiInfo($db, $dataGojokaiInfo, $dataGojokaiMemberCol) {
        // 更新対象外項目設定
        $except = array();
        $dataGojokaiInfo['hoyu_kbn'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['hoyu_kbn']);
        $dataGojokaiInfo['kazoku_kbn'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['kazoku_kbn']);
        $dataGojokaiInfo['meigi_kbn'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['meigi_kbn']);
        $dataGojokaiInfo['info_kbn'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['info_kbn']);
        $dataGojokaiInfo['sodan_kbn'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['sodan_kbn']);
        array_push($except, 'gojokai_kbn');
        array_push($except, 'gojokai_group_no');
        array_push($except, 'plan_use_cnt');
//        $this->setNebikiGojokaiInfo($dataGojokaiInfo, $dataGojokaiMemberCol);
        // 施行互助会情報存在チェック
        $selectGojokaiInfo = $this->getGojokaiInfo();
        if (Msi_Sys_Utils::myCount($selectGojokaiInfo) === 0) {
            $dataGojokaiInfo['seko_no'] = $this->_sekoNo;
            // 施行互助会登録SQL
            //$sql = $this->makeInsertSQL('seko_gojokai_info', $dataGojokaiInfo, $except);
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_gojokai_info", $dataGojokaiInfo, $except);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $dataGojokaiInfo['seko_no'];  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 施行互助会更新SQL
            //$sql = $this->makeUpdateSQL('seko_gojokai_info', $dataGojokaiInfo, $where, $except);
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_gojokai_info", $dataGojokaiInfo, $where, $except);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 施行互助会加入者保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/20
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @param array $dataSekoKihonFree 施行基本フリーデータ
     * @return int 更新件数
     */
    protected function saveGojokaiMember($db, $dataGojokaiMemberCol, $dataSekoKihonFree = null) {

        // 削除→登録する
        $sql = "
            DELETE
                FROM
                    seko_gojokai_member
            WHERE
                    seko_no = :seko_no 
                ";
        $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo));
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        // keigen 契約金額の消費税は受注日で計算する
        $juchu_ymd = Msi_Sys_Utils::getDate();
        if (isset($dataSekoKihonFree) && !empty($dataSekoKihonFree['ts_free1_date'])) {
            $juchu_ymd = $dataSekoKihonFree['ts_free1_date'];
        }
        foreach ($dataGojokaiMemberCol as $oneRow) {
            // 消費税コード取得する
//            $kanyu_dt = $oneRow['kanyu_dt'];
//            list( $y, $m, $d ) = $this->sliceYmd($kanyu_dt);
//            $oneRow['kanyu_dt'] = $this->wareki2seireki($oneRow['kanyu_dt_gen'], $y, $m, $d);

            $oneRow['harai_gaku'] = $oneRow['keiyaku_gaku'] - $oneRow['zankin'];
            $oneRow['kanyu_dt'] = $juchu_ymd;
            $oneRow['zei_cd'] = $this->getZeiCd($oneRow);
            $oneRow['seko_no'] = $this->_sekoNo;
            $oneRow['complet_flg'] = $this->getTkKanNoFlg($oneRow, $gojokaiCouseMst);
            // emptyToNull
            $oneRow['zei_kijyn_ymd'] = Msi_Sys_Utils::emptyToNull($oneRow['zei_kijyn_ymd']);
//            $oneRow['kanyu_dt'] = Msi_Sys_Utils::emptyToNull($oneRow['kanyu_dt']);
            // 施行互助会加入者登録SQL
            //$sql = $this->makeInsertSQL("seko_gojokai_member", $oneRow, array("gojokai_kbn"));
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_gojokai_member", $oneRow, array("gojokai_kbn", "kanyu_dt_gen", "join_convert_cnt", "zankin", "shohizei_1", "shohizei_2"));
            $cnt += $db->easyExecute($sql, $param);
        }
        return $cnt;
    }

    /**
     * 加入年月日または消費税基準日より消費税コードを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/02/21
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param array $gojokaiMember 施行互助会加入者データ
     * @return string 消費税コード
     */
    private function getZeiCd($gojokaiMember) {
        // 一旦基準日に加入年月日を設定
        $kijynYmd = $gojokaiMember['kanyu_dt'];
        $zeiKijynYmd = $gojokaiMember['zei_kijyn_ymd'];
        // 消費税基準日が存在したら基準日に設定する
        if (Msi_Sys_Utils::isValid_DATE2($zeiKijynYmd)) {
            $kijynYmd = $zeiKijynYmd;
        }
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                zei_cd
            FROM
                zei_gojo_mst
            WHERE
                     '{$kijynYmd}' BETWEEN tekiyo_st_date AND tekiyo_ed_date
                AND delete_flg = 0
                ";
        $select = $db->easySelOne($sql);
        return $select['zei_cd'];
    }

    /**
     *
     * 施行基本論理削除処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/27
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @return string SQL
     */
    protected function deleteSekokihon() {
        $sql = "
        UPDATE
            seko_kihon_info
        SET 
            delete_flg = 1
        WHERE
            seko_no = :seko_no
        AND delete_flg = 0
                ";
        return $sql;
    }

    /**
     *
     * 会員区分を求める。
     *
     * <AUTHOR> Sai
     * @since 2014/05/15
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param array $dataGojokaiInfo 施行互助会情報
     * @param array $dataGojokaiMemberCol 施行互助会加入情報
     * @return array[kaiin_cd]        => 会員区分コード
     *          array[kaiin_kbn]       => 会員区分
     */
    protected function calcKaiinKbn($dataGojokaiInfo, $dataGojokaiMemberCol) {
        $kaiin = array();
        $kaiin_cd = null; // 会員区分コード
        $kaiin_kbn = null; // 会員区分
        $isGojokai = false;
        foreach ($dataGojokaiMemberCol as $value) {
            if ($value['yoto_kbn'] !== '4' && $value['yoto_kbn'] !== '12') { // 4：使用しない　12：解約充当
                $isGojokai = true;
                break;
            }
        }
        if ($isGojokai) {
            $kaiin_cd = '1'; // 1:互助会
            $kaiin_kbn = '1'; // 1:互助会
//        } else if (isset($dataGojokaiInfo['kanyu_dantai_kbn']) && $dataGojokaiInfo['kanyu_dantai_kbn'] !== '11') { // 加入団体区分あり かつ　安心クラブ以外
//            $kaiin_cd = '9'; // 9:その他
//            $kaiin_kbn = '9'; // 9:その他
        } else {
            $kaiin_cd = '2'; // 2:一般
            $kaiin_kbn = '2'; // 2:一般
        }
        $kaiin['kaiin_cd'] = $kaiin_cd; // 会員区分コード
        $kaiin['kaiin_kbn'] = $kaiin_kbn; // 会員区分
        return $kaiin;
    }

    /**
     *
     * 伝票の部門コードを更新する
     *
     * <AUTHOR> Sai
     * @since 2014/3/19
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param Msi_Sys_Db $db db
     * @param string $denpyoNo 伝票番号
     * @param string $bumonCd 部門コード
     * @return int 更新件数
     */
    protected function updateDenpyoBumoncd($db, $denpyoNo, $bumonCd) {
        $oldBumonCd = $this->_selectSekoKihon['bumon_cd'];
        // 受注明細テーブル更新SQL
        $sql1 = <<< END_OF_SQL
UPDATE 
    juchu_denpyo_msi
SET
    bumon_cd = :bumon_cd
WHERE
        denpyo_no = :denpyo_no 
    AND bumon_cd = :old_bumon_cd
    AND delete_flg = 0
END_OF_SQL;
        // 受注テーブル更新SQL
        $sql2 = <<< END_OF_SQL
UPDATE 
    juchu_denpyo
SET
    bumon_cd = :bumon_cd
WHERE
        denpyo_no = :denpyo_no 
    AND delete_flg = 0
END_OF_SQL;
        $cnt = $db->easyExecute($sql1, array('bumon_cd' => $bumonCd, 'old_bumon_cd' => $oldBumonCd, 'denpyo_no' => $denpyoNo));
        $cnt += $db->easyExecute($sql2, array('bumon_cd' => $bumonCd, 'denpyo_no' => $denpyoNo));

        // 売上明細テーブル更新SQL
        $sql3 = <<< END_OF_SQL
UPDATE 
    uriage_denpyo_msi
SET
    bumon_cd = :bumon_cd
WHERE
        uri_den_no = :uri_den_no 
    AND bumon_cd = :old_bumon_cd
    AND delete_flg = 0
END_OF_SQL;
        // 売上テーブル更新SQL
        $sql4 = <<< END_OF_SQL
UPDATE 
    uriage_denpyo
SET
    bumon_cd = :bumon_cd
WHERE
        uri_den_no = :uri_den_no 
    AND delete_flg = 0
END_OF_SQL;

        $uriDenpyoNo = $this->getUriagedenpyoNo();
        if ($uriDenpyoNo) {
            $cnt += $db->easyExecute($sql3, array('bumon_cd' => $bumonCd, 'old_bumon_cd' => $oldBumonCd, 'uri_den_no' => $uriDenpyoNo));
            $cnt += $db->easyExecute($sql4, array('bumon_cd' => $bumonCd, 'uri_den_no' => $uriDenpyoNo));
        }
        return $cnt;
    }

    /**
     *
     * 台帳番号採番処理
     *
     * <AUTHOR> Sai
     * @since 2014/2/18
     * @version 2014/05/02 Juchu_JuchuAbstractより引越し
     * @since 2014/6/27 Juchu_JuchuCustomerinfoより移動
     * @param string $sougiymd 葬儀日
     * @return array 台帳番号
     */
    protected function getAutoDaichoNo($sougiymd) {
        $daicho = array();
        $db = Msi_Sys_DbManager::getMyDb();
        // 台帳番号（エリア）
        $area_cd = App_Utils::getSessionData('ctxt_area_cd');
        if (empty($area_cd)) {
            $area_cd = $this->_bumonCd;
        }
        if (!empty($area_cd)) {
            $daicho['daicho_no_eria'] = substr($area_cd, 0, 2);
        } else {
            $daicho['daicho_no_eria'] = null;
        }
        $mm = App_DateCalc::normMM($sougiymd);
        if (isset($mm)) {
            // 台帳番号（月）
            $daicho['daicho_no_mm'] = $mm;
        } else {
            $select1 = $db->easySelOne("SELECT TO_CHAR(CURRENT_DATE,'MM') AS daicho_no_mm");
            // 台帳番号（月）
            $daicho['daicho_no_mm'] = $select1['daicho_no_mm'];
        }

        $sql = "
            SELECT
                COALESCE(MAX(daicho_no_seq), '0') AS daicho_no_seq
            FROM
                seko_kihon_info
            WHERE
                    daicho_no_eria = :daicho_no_eria
                AND daicho_no_mm = :daicho_no_mm
                AND delete_flg = 0
                ";
        $select2 = $db->easySelOne($sql, array('daicho_no_eria' => $daicho['daicho_no_eria'], 'daicho_no_mm' => $daicho['daicho_no_mm']));
        if (count($select2) > 0) {
            $daichoNoSeqInt = (int) $select2['daicho_no_seq'] + 1;
            $daichoNoSeq = str_pad($daichoNoSeqInt, 3, '0', STR_PAD_LEFT);
            $daicho['daicho_no_seq'] = $daichoNoSeq;
        } else {
            $daicho['daicho_no_seq'] = '0001';
        }
        return $daicho;
    }

    /**
     * 故人名添付ファイル名登録処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/24
     * @param resource $file 故人名添付ファイル
     * @return int 更新件数
     */
    public function writeBlob($file) {
        $db = Msi_Sys_DbManager::getMyDb();
        $oid = $db->writeBlob($file);
        $db->commit();
        $data = array(
            'oid' => $oid,
            'status' => 'OK',
            'msg' => ('更新しました'),
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 画面より削除ボタン押下による施行基本論理削除処理 
     *
     * <AUTHOR> Sai
     * @since 2014/02/27
     * @param array $req リクエスト
     */
    public function delete($req) {
        $result = '';
        $msg = '';
        $this->_sekoNo = $req->getPost('seko_no');
        // 施行基本情報を設定する
        $this->setInitParam();
        $db = Msi_Sys_DbManager::getMyDb();
        $hasDenpyo = $this->hasJuchuDenpyoMsi();
        if (!$hasDenpyo) {
            $sql = $this->deleteSekokihon();
            // 施行基本情報を論理削除する
            $cnt = $db->easyExecute($sql, array('seko_no' => $this->_sekoNo));
            if ($cnt > 0) {
                App_Utils::deleteTantoSekoInfo($this->_sekoNo, $this->_moushiKbn);
                App_Utils::setSessionData('seko_no_mitsu', null);
                App_Utils::setSessionData('seko_no', null);
                App_Utils::setSessionData('seko_no_mitsu_houji', null);
                App_Utils::setSessionData('seko_no_houji', null);
                $db->commit();
                $result = 'OK';
                $msg = '削除処理が正常に終了しました。';
            } else {
                $msg = '削除処理が失敗しました。';
            }
        } else {
            $msg = '見積明細が存在するため、削除することができません。';
        }
        $data = array(
            'status' => $result,
            'msg' => $msg,
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 日付を年・月・日にスライスする
     *
     * <AUTHOR> Sai
     * @since 2014/07/08
     * @param string $ymd 日付
     * @return array 年・月・日
     */
    private function sliceYmd($ymd) {
        $match = $y = $m = $d = null;
        if (preg_match('/^(\d{1,4})\D(\d{1,2})\D(\d{1,2}+)/', $ymd, $match)) {
            list( $y, $m, $d ) = array_slice($match, 1, 3);
        }
        return array($y, $m, $d);
    }

    /**
     * 和暦を西暦に変換するする
     *
     * <AUTHOR> Sai
     * @since 2014/07/08
     * @param string $gen 元号
     * @param string $y 年
     * @param string $m 月
     * @param string $d 日
     */
    private function wareki2seireki($gen, $y, $m, $d) {
        $sereki = null;
        if (isset($gen) && isset($y) && isset($m) && isset($d)) {
            $sereki = Msi_Sys_Utils::warekiToSeireki($gen, $y, $m, $d);
        }
        return $sereki;
    }

    /**
     * 施行互助会加入者重複チェック処理 
     *
     * <AUTHOR> Sai
     * @since 2014/07/17
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return boolean 成功可否
     */
    protected function checkGojokaiMember($dataGojokaiMemberCol) {
        foreach ($dataGojokaiMemberCol as $oneRow1) {
            $count = 0;
            $kainNo1 = $oneRow1['kain_no'];
            $applyNo1 = $oneRow1['apply_no'];
            foreach ($dataGojokaiMemberCol as $oneRow2) {
                $kainNo2 = $oneRow2['kain_no'];
                $applyNo2 = $oneRow2['apply_no'];
                if ($kainNo1 === $kainNo2 && $applyNo1 === $applyNo2) {
                    $count++;
                }
            }
            if ($count > 1) {
                $data['status'] = 'NG';
                $data['msg'] = "加入員番号が重複しているため、更新することができません。";
                Msi_Sys_Utils::outJson($data);
                return false;
            }
        }
        return true;
    }

    /**
     *
     * 伝票の担当コードを更新する
     *
     * <AUTHOR> Sai
     * @since 2014/9/2
     * @param Msi_Sys_Db $db db
     * @param string $denpyoNo 伝票番号
     * @param string $bumonCd 部門コード
     * @return int 更新件数
     */
    protected function updateDenpyoDantocd($db, $sekoTantoCd) {
        $cnt = 0;
        $oldSekoTantoCd = $this->_sekoTantoCd;
        if ($sekoTantoCd != $oldSekoTantoCd) {
            $denpyoNo = $this->getJuchudenpyoNo();
            $uriDenpyoNo = $this->getUriagedenpyoNo();
            // 受注テーブル更新SQL
            $sql1 = "
            UPDATE 
                juchu_denpyo
            SET
                tanto_cd = :tanto_cd
            WHERE
                    denpyo_no = :denpyo_no 
                AND delete_flg = 0";
            // 受注テーブル更新SQL
            $sql2 = "
            UPDATE 
                uriage_denpyo
            SET
                tanto_cd = :tanto_cd
            WHERE
                    uri_den_no = :uri_den_no 
                AND delete_flg = 0";
            if (!empty($denpyoNo) && !empty($sekoTantoCd)) {
                $cnt += $db->easyExecute($sql1, array('denpyo_no' => $denpyoNo, 'tanto_cd' => $sekoTantoCd));
            }
            if (!empty($uriDenpyoNo) && !empty($sekoTantoCd)) {
                $cnt += $db->easyExecute($sql2, array('uri_den_no' => $uriDenpyoNo, 'tanto_cd' => $sekoTantoCd));
            }
        }
        return $cnt;
    }

    /**
     * GetCodeNameMst(コード名称マスタを取得）
     * 
     * <AUTHOR> Sai
     * @since      2015/01/16
     * @param   $db DB
     * @param   string $code_kbn
     * @return  array $aryData
     */
    protected function GetCodeNameMst2($db, $code_kbn) {
        // コード名称マスタ
        $select = $db->easySelect(<<< END_OF_SQL
SELECT
    code_kbn			-- コード区分
   ,code_kbn_nm         -- コード区分名
   ,kbn_value_cd		-- 区分値コード
   ,kbn_value_cd_num	-- 区分値コード数値
   ,kbn_value_lnm		-- 区分値正式名
   ,kbn_value_snm		-- 区分値簡略名
   FROM    CODE_NM_MST
   WHERE   delete_flg = 0
   AND     code_kbn    =   :code_kbn	-- コード区分
   ORDER BY kbn_value_cd            
END_OF_SQL
                , array($code_kbn));

        return $select;
    }

    /**
     *
     * 顧客基本情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2015/1/22
     * @return string 顧客コード
     */
    protected function getCustomerCd() {
        $customerCd = '';
        $select = $this->getCustomerBaseInfo();
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $customerCd = $select['customer_cd'];
        }
        return $customerCd;
    }

    /**
     *
     * 顧客基本情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2015/1/22
     * @return array 顧客基本情報
     */
    protected function getCustomerBaseInfo() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                cbi.customer_cd     -- 顧客コード
                ,cbi.seko_no        -- 施行番号
            FROM
                customer_base_info cbi
            WHERE
                    cbi.delete_flg = 0
                AND cbi.seko_no = :seko_no
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     *
     * 施行基本情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/1/30
     * @return array 施行基本情報
     */
    protected function getSekoKihon() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            k.seko_no           -- 施行番号
            ,k.bumon_cd         -- 部門コード
            ,k.moushi_cd        -- 申込コード
            ,k.moushi_kbn       -- 申込区分
            ,k.sougi_cd         -- 葬儀コード
            ,k.sougi_kbn        -- 葬儀区分
            ,ltrim(rtrim(daicho_no_eria))   AS daicho_no_eria	-- 台帳番号（エリア）
            ,ltrim(rtrim(k.daicho_no_mm))	AS daicho_no_mm		-- 台帳番号（月）
            ,ltrim(rtrim(k.daicho_no_seq))  AS daicho_no_seq	-- 台帳番号（連番）
            ,k.p_info_cd        -- 個人情報保護コード
            ,k.p_info_kbn       -- 個人情報保護区分
            ,k.kaiin_cd         -- 会員コード
            ,k.kaiin_kbn        -- 会員区分
            ,k.kaiin_sonota     -- 会員区分その他
            ,trim(k.free2_cd) AS free2_cd  -- カーニバル番号
            ,k.uketuke_tanto_cd -- 受付担当者コード
            ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
            ,k.seko_tanto_cd    -- 施行担当者コード
            ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
            ,k.k_nm             -- 故人名
            ,k.k_file_nm        -- 故人名添付ファイル
            ,k.k_sex_cd         -- 性別コード
            ,k.k_sex_kbn        -- 性別区分
            ,k.k_haigu_cd       -- 配偶者コード
            ,k.k_haigu_kbn      -- 配偶者区分
            ,k.k_knm            -- 故人カナ名
            ,k.k_gengo          -- 生年月日元号
            ,TRIM(k.k_seinengappi_ymd) AS k_seinengappi_ymd  -- 生年月日
            ,k.k_nenrei_man     -- 故人年齢
            ,k.k_nenrei_kyounen -- 享年
            ,k.kg_yubin_no      -- 現住所郵便番号
            ,k.kg_addr1         -- 現住所1
            ,k.kg_tel           -- 現住所TEL
            ,k.kg_addr2         -- 現住所2
            ,k.kg_setai_cd      -- 世帯主コード
            ,k.kg_setai_kbn     -- 世帯主区分
            ,k.kj_kbn           -- 住民登録住所の現住所に同じチェックボックス
            ,k.kj_yubin_no      -- 住民登録住所郵便番号
            ,k.kj_addr1         -- 住民登録住所1
            ,k.kj_tel           -- 住民登録住所TEL
            ,k.kj_addr2         -- 住民登録住所2
            ,k.kj_setai_cd      -- 住民登録住所世帯主コード
            ,k.kj_setai_kbn     -- 住民登録住所世帯主区分
            ,k.kh_kbn           -- 本籍の現住所に同じチェックボックス
            ,k.kh_yubin_no      -- 本籍郵便番号
            ,k.kh_addr1         -- 本籍住所1
            ,k.kh_hito_cd       -- 筆頭者コード
            ,k.kh_hito_kbn      -- 筆頭者区分
            ,k.kh_addr2         -- 本籍住所2
            ,k.kk_kinmusaki_kbn -- 勤務先
            ,k.kk_kinmusaki_nm  -- 勤務先名
            ,k.kk_tel           -- 勤務先TEL
            ,k.kk_yakusyoku_nm  -- 役職／職種
            ,k.kk_fax           -- 勤務先FAX
            ,k.souke_nm         -- 葬家
            ,k.souke_knm        -- 葬家カナ
            ,k.souke_addr_cd    -- 葬家住所コード
            ,k.souke_addr_kbn   -- 葬家住所区分
            ,k.souke_tel        -- 葬家TEL
            ,k.keishiki_cd      -- 葬儀形式コード
            ,k.keishiki_kbn     -- 葬儀形式区分
            ,k.syushi_cd       -- 宗旨コード
            ,k.syushi_kbn       -- 宗旨区分
            ,k.syuha_cd        -- 宗派コード
            ,k.syuha_kbn        -- 宗派区分
            ,k.syuha_nm         -- 宗派名
            ,k.syuha_knm        -- 宗派カナ名
            ,k.jyusho_cd        -- 寺院コード
            ,k.jyusho_nm        -- 寺院名
            ,k.jyusho_knm       -- 寺院カナ名
            ,k.v_free8          -- 注意事項
            ,k.biko1            -- メモ（出棺経路・納骨・壇払など）
            ,k.sd_hakko_kbn     -- 診断書発行区分
            ,k.sd_step_kbn      -- 診断書手続
            ,k.sd_yotei_ymd     -- 診断書発行予定時刻
            ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD HH24:MI') AS sd_yotei_ymd-- 診断書発行予定スタンプ
            ,TO_CHAR(k.sd_yotei_ymd ,'YYYY/MM/DD') AS sd_yotei_date-- 診断書発行予定時日付のみ
            ,TO_CHAR(k.sd_yotei_ymd ,'HH24:MI') AS sd_yotei_time-- 診断書発行予定時刻のみ
            ,k.sd_copy_cnt      -- 診断書コピー枚数
            ,k.tk_cyonaikai_nm  -- 町内会名
            ,k.tk_kumicyo_nm    -- 隣組長
            ,k.tk_house_cnt     -- 軒数
            ,k.tk_person_cnt    -- 人数
            ,k.hs_kbn           -- 搬送区分
            ,k.hs_gyomu_cd      -- 搬送業務コード
            ,k.hs_gyomu_kbn     -- 搬送業務区分
            ,k.hs_spot_kbn      -- お伺い先区分
            ,k.hs_spot_cd       -- お伺い先コード
            ,k.hs_spot_nm       -- お伺い先
            ,k.iso_accept_cd_1  -- 移送者区分コード1
            ,k.iso_accept_kbn_1 -- 移送者区分1
            ,CASE k.iso_accept_kbn_1 WHEN 1 THEN k.iso_tanto_cd1 WHEN 2 THEN sm1.siire_cd ELSE NULL END iso_tanto_cd1 -- 移送担当コード1
            ,CASE k.iso_accept_kbn_1 WHEN 1 THEN t3.tanto_nm WHEN 2 THEN sm1.siire_lnm ELSE NULL END iso_tanto_nm1 -- 施行担当者名1
            ,kf.tanto_cd2 AS iso_tanto_cd1_2 -- 移送担当コード1_2
            ,t5.tanto_nm AS iso_tanto_nm1_2 -- 施行担当者名1_2
            ,k.iso_accept_cd_2  -- 移送者区分コード2
            ,k.iso_accept_kbn_2 -- 移送者区分2
            ,CASE k.iso_accept_kbn_2 WHEN 1 THEN k.iso_tanto_cd2 WHEN 2 THEN sm2.siire_cd ELSE NULL END iso_tanto_cd2 -- 移送担当コード2
            ,CASE k.iso_accept_kbn_2 WHEN 1 THEN t4.tanto_nm WHEN 2 THEN sm2.siire_lnm ELSE NULL END iso_tanto_nm2 -- 施行担当者名2
            ,kf.tanto_cd3 AS iso_tanto_cd2_2 -- 移送担当コード2_2
            ,t6.tanto_nm AS iso_tanto_nm2_2 -- 施行担当者名2_2
            ,k.iso_accept_cd_3  -- 移送者区分コード3
            ,k.iso_accept_kbn_3 -- 移送者区分3
            ,CASE k.iso_accept_kbn_3 WHEN 1 THEN k.iso_tanto_cd3 WHEN 2 THEN sm3.siire_cd ELSE NULL END iso_tanto_cd3 -- 移送担当コード3
            ,CASE k.iso_accept_kbn_3 WHEN 1 THEN t7.tanto_nm WHEN 2 THEN sm3.siire_lnm ELSE NULL END iso_tanto_nm3 -- 施行担当者名3
            ,kf.tanto_cd4 AS iso_tanto_cd3_2 -- 移送担当コード3_2
            ,t8.tanto_nm AS iso_tanto_nm3_2 -- 施行担当者名3_2
            ,k.iso_accept_cd_4  -- 移送者区分コード4
            ,k.iso_accept_kbn_4 -- 移送者区分4
            ,CASE k.iso_accept_kbn_4 WHEN 1 THEN k.iso_tanto_cd4 WHEN 2 THEN sm4.siire_cd ELSE NULL END iso_tanto_cd4 -- 移送担当コード4
            ,CASE k.iso_accept_kbn_4 WHEN 1 THEN t9.tanto_nm WHEN 2 THEN sm4.siire_lnm ELSE NULL END iso_tanto_nm4 -- 施行担当者名4
            ,kf.tanto_cd5 AS iso_tanto_cd4_2 -- 移送担当コード4_2
            ,t10.tanto_nm AS iso_tanto_nm4_2 -- 施行担当者名4_2
            ,k.iso_accept_cd_5  -- 移送者区分コード5
            ,k.iso_accept_kbn_5 -- 移送者区分5
            ,CASE k.iso_accept_kbn_5 WHEN 1 THEN k.iso_tanto_cd5 WHEN 2 THEN sm5.siire_cd ELSE NULL END iso_tanto_cd5 -- 移送担当コード5
            ,CASE k.iso_accept_kbn_5 WHEN 1 THEN t11.tanto_nm WHEN 2 THEN sm5.siire_lnm ELSE NULL END iso_tanto_nm5 -- 施行担当者名5
            ,kf.tanto_cd6 AS iso_tanto_cd5_2 -- 移送担当コード5_2
            ,t12.tanto_nm AS iso_tanto_nm5_2 -- 施行担当者名5_2
            ,k.hs_gyomu_cd_2    -- 搬送業務コード
            ,k.hs_gyomu_kbn_2   -- 搬送業務区分
            ,k.hs_spot_kbn_2    -- お伺い先区分2
            ,k.hs_spot_cd_2     -- お伺い先コード2
            ,k.hs_spot_nm_2     -- お伺い先2
            ,k.hs_gyomu_cd2     -- 搬送業務コード2
            ,k.hs_gyomu_kbn2    -- 搬送業務区分2
            ,k.hs_anchi_kbn     -- 安置先区分
            ,k.hs_anchi_cd      -- 安置先コード
            ,k.hs_anchi_nm      -- 安置先
            ,k.kasoba_cd        -- 火葬場コード
            ,k.kasoba_nm        -- 火葬場名
            --,jm.jyusho_lnm AS kasoba_nm -- 火葬場名
            ,k.az_death_cnt     -- 死亡診断書枚数
            ,k.az_inkan_kbn     -- 印鑑
            ,k.az_photo_cnt     -- 御写真枚数
            ,k.az_gojokai_nm    -- 互助会証書名称
            ,k.m_nm             -- 喪主名
            ,k.m_knm            -- 喪主名カナ
            ,k.m_file_nm        -- 喪主添付ファイル
            ,k.m_zoku_cd        -- 喪主続柄コード
            ,k.m_zoku_kbn       -- 喪主続柄区分
            ,k.m_zoku_cd2       -- 喪主からみた続柄コード
            ,k.m_zoku_kbn2      -- 喪主からみた続柄区分
            ,k.m_gengo          -- 喪主生年月日元号
            ,TRIM(k.m_seinengappi_ymd) AS m_seinengappi_ymd-- 喪主生年月日
            ,k.m_nenrei_man     -- 喪主年齢
            ,k.mg_kbn           -- 喪主 故人に同じ
            ,k.mg_yubin_no      -- 喪主現住所郵便番号
            ,k.mg_addr1         -- 喪主現住所1
            ,k.mg_m_tel         -- 喪主携帯
            ,k.mg_tel           -- 喪主現住所TEL
            ,k.mg_addr2         -- 喪主現住所2
            ,k.mj_kbn           -- 喪主 住民登録住所の故人に同じ
            ,k.mj_yubin_no      -- 喪主住民登録住所郵便番号
            ,k.mj_addr1         -- 喪主住民登録住所1
            ,k.mj_tel           -- 喪主住民登録住所TEL
            ,k.mj_addr2         -- 喪主住民登録住所2
            ,k.mh_kbn           -- 喪主 本籍の故人に同じ
            ,k.mh_yubin_no      -- 喪主本籍住所郵便番号
            ,k.mh_addr1         -- 喪主本籍住所1
            ,k.mh_addr2         -- 喪主本籍住所2
            ,k.mk_kinmusaki_kbn -- 喪主勤務先
            ,k.mk_kinmusaki_nm  -- 喪主勤務先名
            ,k.mk_tel           -- 喪主勤務先TEL
            ,k.mk_yakusyoku_nm  -- 喪主役職／職種
            ,k.mk_fax           -- 喪主勤務先FAX
            ,k.sekyu_kbn        -- 請求先の喪主に同じ
            ,k.sekyu_cd         -- 請求先コード
            ,k.jichu_kakute_ymd -- 受注確定日
            ,k.mt_irai_kbn      -- 依頼区分
            ,k.mt_area          -- 対象エリア
            ,k.mt_item          -- 目録手配品目
            ,TO_CHAR(k.mt_hannyu_start ,'YYYY/MM/DD HH24:MI') AS mt_hannyu_start -- 搬入開始日時
            ,TO_CHAR(k.mt_hannyu_end ,'YYYY/MM/DD HH24:MI') AS mt_hannyu_end -- 搬入終了日時
            ,TO_CHAR(k.mt_hannyu_start ,'YYYY/MM/DD') AS mt_hannyu_ymd -- 搬入日
            ,TO_CHAR(k.mt_hannyu_start ,'HH24:MI') AS mt_hannyu_start_time -- 搬入開始時刻
            ,TO_CHAR(k.mt_hannyu_end ,'HH24:MI') AS mt_hannyu_end_time -- 搬入終了時刻
            ,k.mt_hannyu_cd     -- 搬入場所コード
            ,k.hs_delivery_cd   -- 納品場所コード
            ,k.hs_delivery_kbn  -- 納品場所区分
            ,k.mt_memo_cd       -- 目録手配備考コード
            ,k.mt_memo_kbn      -- 目録手配備考区分
            ,k.mt_memo_detail   -- 目録手配備考
            ,k.mt_hannyu_nm     -- 搬入場所名
            ,k.sd_shohin_cd     -- 寝台車商品コード
            ,k.shindaisya_prc   -- 寝台車金額
            ,k.sk_shohin_cd     -- 式場商品コード
            ,k.sk_kaijyo_cd     -- 式場場所コード
            ,k.sk_kaijyo_nm     -- 式場場所名称
            ,k.shikijo_shiyou_prc-- 式場使用料
            ,k.ty_shohin_cd     -- 通夜会場商品コード
            ,k.ty_kaijyo_cd     -- 通夜会場場所コード
            ,k.ty_kaijyo_nm     -- 通夜会場場所名称
            ,k.tuya_paku_su     -- 通夜会場使用泊数
            ,k.tuya_shikijo_tanka-- 通夜会場使用単価
            ,k.tuya_paku_su2     -- 通夜会場使用泊数2
            ,k.tuya_shikijo_tanka2-- 通夜会場使用単価2
            ,k.tuya_shikijo_prc -- 通夜会場使用料金額
            ,cm.chiku_lnm -- 地区名
            ,km.kumi_lnm -- 組名
            ,k.chiku_cd -- 地区コード
            ,k.kumi_cd -- 組コード
            ,k.free1_code_cd    -- 寺紹介者コード
            ,k.free1_kbn        -- 寺紹介者区分
            ,k.n_free1          -- 施行運営費
            ,k.v_free1          -- 御写真ファイル名
            ,k.img_free1        -- 御写真OID
            ,k.free3_code_cd    -- 会館利用コード
            ,k.free3_kbn        -- 会館利用区分
            ,k.free5_code_cd    -- 各方面よりお問合せのあった場合、お答えして良い項目コード
            ,k.free5_kbn        -- 各方面よりお問合せのあった場合、お答えして良い項目区分
            ,k.free6_code_cd    -- 各方面よりお供物(生花等)のご注文があった場合、承りはコード
            ,k.free6_kbn        -- 各方面よりお供物(生花等)のご注文があった場合、承りは区分
            ,k.v_free5          -- 条件付で可の条件
            ,k.v_free6          -- 特記事項
            ,k.free4_kbn        -- ペースメーカ
            ,k.n_free4          -- 親族人数
            ,TO_CHAR(k.ts_free2 ,'YYYY/MM/DD HH24:MI') AS iso_ymd-- 移送日時
            ,TO_CHAR(k.ts_free2 ,'YYYY/MM/DD') AS iso_date-- 移送日時日付のみ
            ,TO_CHAR(k.ts_free6 ,'HH24:MI') AS iso_time-- 移送日時時刻のみ
            ,COALESCE(k.v_free12, '御布施') AS v_free12  -- 御布施
            ,k.n_free10         -- 御布施金額
            ,k.n_free5          -- 会葬者人数
            -- 追加項目
            ,k.v_free21         -- 泊まり
            ,TO_CHAR(k.ts_free8 ,'HH24:MI') AS ts_free8     -- 通夜入り開始時間
            ,TO_CHAR(k.ts_free9 ,'HH24:MI') AS ts_free9     -- 通夜入り終了時間
            ,k.n_free11         -- 通夜依頼人数
            ,TO_CHAR(k.ts_free10 ,'HH24:MI') AS ts_free10   -- 告別式入り開始時間
            ,TO_CHAR(k.ts_free11 ,'HH24:MI') AS ts_free11   -- 告別式入り終了時間
            ,k.n_free12         -- 告別式依頼人数
        FROM
            seko_kihon_info k
            LEFT OUTER JOIN tanto_mst t1
            ON  (
                    k.uketuke_tanto_cd = t1.tanto_cd
                AND t1.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t2
            ON  (
                    k.seko_tanto_cd = t2.tanto_cd
                AND t2.delete_flg = 0
                )
            LEFT OUTER JOIN nm_jyusho_mst jm
            ON  (
                    k.kasoba_cd = jm.jyusho_cd
                AND jm.jyusho_kbn = 3
                AND jm.delete_flg = 0
                )
            LEFT OUTER JOIN chiku_mst cm
            ON  (
                    k.chiku_cd = cm.chiku_cd
                AND cm.delete_flg = 0
                )
            LEFT OUTER JOIN kumi_mst km
            ON  (
                    k.chiku_cd = km.chiku_cd
                AND k.kumi_cd = km.kumi_cd
                AND km.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t3
            ON  (
                    k.iso_tanto_cd1 = t3.tanto_cd
                AND t3.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t4
            ON  (
                    k.iso_tanto_cd2 = t4.tanto_cd
                AND t4.delete_flg = 0
                )
            LEFT OUTER JOIN siire_mst sm1
            ON  (
                    k.iso_siire_cd1 = sm1.siire_cd
                AND sm1.transfer_kbn = 1
                AND sm1.delete_flg = 0
                )
            LEFT OUTER JOIN siire_mst sm2
            ON  (
                    k.iso_siire_cd2 = sm2.siire_cd
                AND sm2.transfer_kbn = 1
                AND sm2.delete_flg = 0
                )
            LEFT OUTER JOIN siire_mst sm3
            ON  (
                    k.iso_siire_cd3 = sm3.siire_cd
                AND sm3.transfer_kbn = 1
                AND sm3.delete_flg = 0
                )
            LEFT OUTER JOIN siire_mst sm4
            ON  (
                    k.iso_siire_cd4 = sm4.siire_cd
                AND sm4.transfer_kbn = 1
                AND sm4.delete_flg = 0
                )
            LEFT OUTER JOIN siire_mst sm5
            ON  (
                    k.iso_siire_cd5 = sm5.siire_cd
                AND sm5.transfer_kbn = 1
                AND sm5.delete_flg = 0
                )
            LEFT OUTER JOIN seko_kihon_all_free kf
            ON  (
                    k.seko_no = kf.seko_no
                AND kf.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t5
            ON  (
                    kf.tanto_cd2 = t5.tanto_cd
                AND t5.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t6
            ON  (
                    kf.tanto_cd3 = t6.tanto_cd
                AND t6.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t7
            ON  (
                    k.iso_tanto_cd3 = t7.tanto_cd
                AND t7.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t8
            ON  (
                    kf.tanto_cd4 = t8.tanto_cd
                AND t8.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t9
            ON  (
                    k.iso_tanto_cd4 = t9.tanto_cd
                AND t9.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t10
            ON  (
                    kf.tanto_cd5 = t10.tanto_cd
                AND t10.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t11
            ON  (
                    k.iso_tanto_cd5 = t11.tanto_cd
                AND t11.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t12
            ON  (
                    kf.tanto_cd6 = t12.tanto_cd
                AND t12.delete_flg = 0
                )
        WHERE
            k.seko_no = :seko_no
        AND k.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * 値引き互助会情報設定処理 
     *
     * <AUTHOR> Sai
     * @since 2015/06/16
     * @param Msi_Sys_Db $db db
     * @param array $dataGojokaiInfo 施行互助会情報データ
     * @param array $dataGojokaiMemberCol 施行互助会加入者データ
     * @return int 更新件数
     */
    protected function setNebikiGojokaiInfo(&$dataGojokaiInfo, $dataGojokaiMemberCol) {
        $flg1 = false; // 用途が葬送儀礼またはプラン値引き
        $flg2 = false; // 用途が葬送儀礼かつyoto5_flg=1　のコースが含まれているか
        $flg3 = false; // 用途が用途が葬送儀礼またはプラン値引きかつyoto5_flg=1　のコースが含まれているか
        $dataGojokaiInfo['nebiki_join_cnt'] = null;
        $dataGojokaiInfo['nebiki_group_no'] = null;
        $dataGojokaiInfo['nebiki_gojokai_kbn'] = null;
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        foreach ($dataGojokaiMemberCol as $oneRow) {
            if ($oneRow['yoto_kbn'] === '1') {
                $flg1 = true;
                $couseMstOne = $this->getCouseMstOne($oneRow['course_snm_cd'], $gojokaiCouseMst);
                if (!is_null($couseMstOne)) {
                    $flg2 = true;
                    $flg3 = true;
                    $dataGojokaiInfo['nebiki_join_cnt'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['plan_use_cnt']);
                    $dataGojokaiInfo['nebiki_group_no'] = Msi_Sys_Utils::emptyToNull($couseMstOne['gojokai_group_no']);
                    $dataGojokaiInfo['nebiki_gojokai_kbn'] = $this->getNebikiGojokaiKbn($dataGojokaiInfo['nebiki_group_no'], $dataGojokaiInfo['nebiki_join_cnt']);
                    $this->_nebikiGojokaiKbn = $dataGojokaiInfo['nebiki_gojokai_kbn'];
                    break;
                }
            } else if ($oneRow['yoto_kbn'] === '11' && !$flg2) { // プラン値引き
                $flg1 = true;
                $couseMstOne = $this->getCouseMstOne($oneRow['course_snm_cd'], $gojokaiCouseMst);
                if (!is_null($couseMstOne)) {
                    $flg3 = true;
                    $dataGojokaiInfo['nebiki_join_cnt'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['plan_use_cnt']);
                    $dataGojokaiInfo['nebiki_group_no'] = Msi_Sys_Utils::emptyToNull($couseMstOne['gojokai_group_no']);
                    $dataGojokaiInfo['nebiki_gojokai_kbn'] = $this->getNebikiGojokaiKbn($dataGojokaiInfo['nebiki_group_no'], $dataGojokaiInfo['nebiki_join_cnt']);
                }
            }
        }
        // 葬送儀礼の用途がありかつyoto5_flg=1のコースが含まれていない場合
        // nebiki_group_noは'99999’　の固定とし、抽出して、
        // nebiki_group_no、nebiki_join_cnt、nebiki_gojokai_kbn　の項目を設定
        if ($flg1 && !$flg3) {
            $dataGojokaiInfo['nebiki_join_cnt'] = Msi_Sys_Utils::emptyToNull($dataGojokaiInfo['plan_use_cnt']);
            $dataGojokaiInfo['nebiki_group_no'] = '99999';
            $dataGojokaiInfo['nebiki_gojokai_kbn'] = $this->getNebikiGojokaiKbn($dataGojokaiInfo['nebiki_group_no'], $dataGojokaiInfo['nebiki_join_cnt']);
        }
    }

    /**
     * 互助会コースマスタワンレコード抽出処理 
     *
     * <AUTHOR> Sai
     * @since 2015/06/16
     * @param Msi_Sys_Db $db db
     * @param array $course_snm_cd 互助会コースコード
     * @param array $gojokaiCouseMst 互助会コースマスタ
     * @return int 更新件数
     */
    private function getCouseMstOne($course_snm_cd, $gojokaiCouseMst) {
        $couseMstOne = null;
        foreach ($gojokaiCouseMst as $coseOneRow) {
            if ($course_snm_cd == $coseOneRow['gojokai_cose_iw'] && $coseOneRow['yoto5_flg'] == '1') {
                $couseMstOne = $coseOneRow;
                break;
            }
        }
        return $couseMstOne;
    }

    /**
     * 値引会員区分取得処理 
     *
     * <AUTHOR> Sai
     * @since 2015/06/11
     * @param string $nebiki_group_no 互助会グループ番号
     * @param string $nebiki_join_cnt プラン利用口数
     * @return int 値引会員区分
     */
    private function getNebikiGojokaiKbn($nebiki_group_no, $nebiki_join_cnt) {
        $nebiki_gojokai_kbn = null;
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                nmcm.nebiki_gojokai_kbn     -- 顧客コード
            FROM
                nebiki_member_calc_mst nmcm
            WHERE
                    nmcm.delete_flg = 0
                AND nmcm.nebiki_group_no = :nebiki_group_no
                AND nmcm.nebiki_join_cnt = :nebiki_join_cnt
                ";
        $select = $db->easySelOne($sql, array('nebiki_group_no' => $nebiki_group_no, 'nebiki_join_cnt' => $nebiki_join_cnt));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $nebiki_gojokai_kbn = $select['nebiki_gojokai_kbn'];
        }
        return $nebiki_gojokai_kbn;
    }

}
