<?php

/**
 * DataMapper_Pdf1101
 *
 * PDF出力 請求書 データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Tosaka
 * @since      2020/xx/xx
 * @filesource 
 */

/**
 * PDF出力 請求書 データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Tosaka
 * @since      2020/xx/x
 */
class DataMapper_Pdf1101 extends DataMapper_Abstract {

    /**
     * PDF出力 請求書 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
SELECT
     s.sougi_ymd
    ,s.souke_nm     -- 葬家
    ,(
        SELECT
            kbn_value_lnm
        FROM
            code_nm_mst
        WHERE
            code_kbn = '0950'
        AND kbn_value_cd = s.sk_houyo_cd
        AND delete_flg = 0
    ) AS sk_houyo_nm -- 施行法要名
    ,snh2.basho_nm  -- 場所名
    ,snh3.basho_nm AS kaishokujo_nm  -- 会食場名
    ,snh2.spot_cd AS basho_kbn_hoyo
    ,snh3.spot_cd AS basho_kbn_hoen
    ,TO_CHAR(snh1.nitei_ymd ,'YYYY/MM/DD') AS nohin_date -- 納品日付
    ,TO_CHAR(snh2.nitei_ymd ,'YYYY/MM/DD') AS riyo_date -- 利用日付
    ,syushi.kbn_value_lnm AS syushi_nm    -- 宗旨名
    ,s.syushi_cd
    ,s.syuha_nm    -- 宗派名
    ,s.jyusho_nm   -- 寺院名
    ,h.data_kbn
    ,h.seko_no
    ,h.seko_no_sub
    ,tanto.tanto_nm
    ,tanto.inkan_img
    ,goj_mem.kain_no
    ,gojo_co.gojokai_cose_nm
    ,gojo_co.gojokai_prc
    ,s.free7_kbn    -- 2:法事 12:催事
    ,s.v_free10     -- 催事の件名
  FROM seko_kihon_info s
  LEFT JOIN seikyu_denpyo h 
      ON s.seko_no = h.seko_no
      AND h.delete_flg = 0
  LEFT JOIN tanto_mst tanto 
      ON s.seko_tanto_cd = tanto.tanto_cd 
      AND tanto.delete_flg = 0
  LEFT JOIN seko_gojokai_member goj_mem 
      ON s.seko_no = goj_mem.seko_no 
      AND goj_mem.yoto_kbn = 1 
      AND goj_mem.delete_flg = 0
  LEFT JOIN gojokai_couse_mst gojo_co 
      ON goj_mem.gojokai_cose_cd = gojo_co.gojokai_cose_cd 
      AND goj_mem.kanyu_dt BETWEEN gojo_co.tekiyo_st_date AND gojo_co.tekiyo_ed_date 
      AND gojo_co.delete_flg = 0
  LEFT JOIN seko_nitei_houji snh1
      ON s.seko_no = snh1.seko_no
      AND snh1.nitei_kbn = 0 -- 納品
      AND snh1.delete_flg = 0
  LEFT JOIN seko_nitei_houji snh2
      ON s.seko_no = snh2.seko_no
      AND snh2.nitei_kbn = 1 -- 法要
      AND snh2.delete_flg = 0
  LEFT JOIN seko_nitei_houji snh3
      ON s.seko_no = snh3.seko_no
      AND snh3.nitei_kbn = 3 -- 法宴
      AND snh3.delete_flg = 0
  LEFT JOIN code_nm_mst syushi 
        ON syushi.code_kbn = '0240' 
        AND s.syushi_cd = syushi.kbn_value_cd 
        AND syushi.delete_flg = 0
 WHERE s.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * PDF出力 請求書 取得
     *
     * <AUTHOR> Kobayashi
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findHead($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
SELECT
     sd.seikyu_den_no
    ,sd.uri_den_no
    ,ud.denpyo_no AS juchu_den_no
    ,sd.bumon_cd
    ,sd.seko_no
    ,sd.data_kbn
    ,sd.seikyu_kbn
    ,sd.seikyu_approval_status
    ,sd.sekkyu_kaisu
    ,COALESCE(sd.nyukin_status, 0) AS nyukin_status
    ,sd.bun_gas_kbn_num
    ,sd.bun_gas_seikyu_den_no
    ,sd.nonyu_nm
    ,sd.partner_cd
    ,sd.cvs_bar_cd
    ,sd.kaishu_ymd
    ,TO_CHAR(sd.kaishu_ymd,'FMMM月FMDD日') AS kaishu_ymd_disp
    ,sd.seikyu_post_kbn
    ,TRIM(sd.pay_method_cd) AS pay_method_cd
    ,TO_CHAR(sd.nonyu_dt,'YYYY/MM/DD') AS nonyu_ymd
    ,s.moushi_kbn
    ,s.souke_nm
    ,s.k_nm
    ,tanto.tanto_nm
    ,TO_CHAR(s.sougi_ymd,'YYYY/MM/DD') AS sougi_ymd
    ,sd.est_shikijo_cd                       -- 見積式場コード
    ,bm.f_free1 AS bumon_logo
    ,bm.char_free2 AS bumon_kaisya_nm
    ,cm6852.kbn_value_snm AS bumon_number
    ,cm9750.kbn_value_cd_num AS kaisya_disp
    ,CASE WHEN sssi.houjin_kbn IS NOT NULL AND sssi.houjin_sbt IS NOT NULL THEN 
       (CASE WHEN sssi.houjin_kbn = 2 THEN CONCAT(cm7810.kbn_value_lnm, sssi.sekyu_nm1, sssi.sekyu_nm2)
             WHEN sssi.houjin_kbn = 3 THEN CONCAT(sssi.sekyu_nm1, sssi.sekyu_nm2, cm7810.kbn_value_lnm)
        ELSE CONCAT(sssi.sekyu_nm1, sssi.sekyu_nm2) END)
        ELSE CONCAT(sssi.sekyu_nm1, sssi.sekyu_nm2) END AS sekyu_nm
    ,sssi.sekyu_yubin_no
    ,sssi.sekyu_addr1 AS sekyu_addr1
    ,sssi.sekyu_addr2 AS sekyu_addr2
    ,sssi.sekyu_tel AS sekyu_tel
    ,sssi.sekyu_soufu_nm
    ,sssi.v_free2 AS sekyu_soufu_nm2
    ,sssi.soufu_yubin_no
    ,sssi.soufu_addr1
    ,sssi.soufu_addr2
    ,sssi.soufu_kbn
    ,sssi.syorui_tenpu_kbn
    ,sssi.pay_kbn
    ,sd.d_free1
    ,sd.k_free9 -- 銀行明細番号
    ,sd.br_koza_no
    -- 金額関係
    ,COALESCE(sd.kouden_uchikin_prc, 0) AS kouden_uchikin_prc -- 香典内金額
    ,COALESCE(sd.uchikin_prc, 0) AS uchikin_prc -- 内金入金額
    ,COALESCE(sd.cupon_prc, 0) AS cupon_prc -- クーポン利用額
    ,COALESCE(sd.nyukin_prc, 0) AS nyukin_prc -- 入金金額
    ,COALESCE(sd.seikyu_zan, 0) AS seikyu_zan -- 請求残額
    ,COALESCE(sd.sougi_zei_sagaku_prc, 0) AS sougi_zei_sagaku_prc -- 掛金消費税差額
    ,COALESCE(sd.nyukin_prc, 0) + COALESCE(sd.seikyu_zan, 0)  AS seikyu_prc -- 請求金額
    ,COALESCE(sd.out_zei_prc, 0) + COALESCE(sd.in_zei_prc, 0) AS zei_prc    -- 消費税額
  FROM seikyu_denpyo sd
  LEFT JOIN uriage_denpyo ud
        ON ud.uri_den_no = sd.uri_den_no
        AND ud.delete_flg = 0
  LEFT JOIN seko_kihon_info s
        ON s.seko_no = sd.seko_no
        AND s.delete_flg = 0
  LEFT JOIN bumon_mst bm
        ON  sd.bumon_cd = bm.bumon_cd
        AND bm.delete_flg = 0
  LEFT JOIN tanto_mst tanto 
      ON s.seko_tanto_cd = tanto.tanto_cd 
      AND tanto.delete_flg = 0
  LEFT JOIN seikyu_sekyu_saki_info sssi 
        ON sssi.seikyu_den_no = sd.seikyu_den_no 
        AND sssi.delete_flg = 0
  LEFT JOIN sekyu_saki_info ssi 
        ON ssi.sekyu_cd = sd.sekyu_cd 
        AND ssi.delete_flg = 0
  LEFT JOIN seko_nitei_houji snh1
        ON  snh1.seko_no = COALESCE(ud.ref_seko_no, sd.seko_no)
        AND snh1.nitei_kbn = 1 -- 法要
        AND snh1.delete_flg = 0
  LEFT JOIN code_nm_mst cm8320
        ON cm8320.code_kbn = '8320'
        AND cm8320.kbn_value_cd_num = sd.data_kbn
        AND cm8320.delete_flg = 0
  LEFT JOIN code_nm_mst cm0010
        ON cm0010.code_kbn = '0010'
        AND cm0010.kbn_value_cd_num = s.moushi_kbn
        AND cm0010.delete_flg = 0
  LEFT JOIN code_nm_mst cm6852
        ON cm6852.code_kbn = '6852' -- 登録番号(部門別)
        AND cm6852.kbn_value_cd = sd.bumon_cd
        AND cm6852.delete_flg = 0
  LEFT JOIN code_nm_mst cm7810
        ON cm7810.code_kbn = '7810'
        AND cm7810.kbn_value_cd_num = sssi.houjin_sbt
        AND cm7810.delete_flg = 0
  LEFT JOIN code_nm_mst cm9750 -- 正式会社名表示
        ON cm9750.code_kbn = '9750'
        AND cm9750.kbn_value_cd = '10006'
        AND cm9750.delete_flg = 0
 WHERE sd.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }
    
    /**
     * PDF出力 請求書 取得
     *
     * <AUTHOR> Kobayashi
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findHeadForTemp($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
SELECT
     sd.temp_seikyu_den_no AS seikyu_den_no
    ,sd.uri_den_no
    ,ud.denpyo_no AS juchu_den_no
    ,sd.bumon_cd
    ,sd.seko_no
    ,sd.data_kbn
    ,sd.seikyu_kbn
    ,sd.seikyu_approval_status
    ,sd.sekkyu_kaisu
    ,COALESCE(sd.nyukin_status, 0) AS nyukin_status
    ,sd.bun_gas_kbn_num
    ,sd.bun_gas_seikyu_den_no
    ,sd.nonyu_nm
    ,sd.partner_cd
    ,sd.cvs_bar_cd
    ,sd.kaishu_ymd
    ,sd.seikyu_post_kbn
    ,TRIM(sd.pay_method_cd) AS pay_method_cd
    ,TO_CHAR(sd.nonyu_dt,'YYYY/MM/DD') AS nonyu_ymd
    ,s.moushi_kbn
    ,s.souke_nm
    ,s.k_nm
    ,tanto.tanto_nm
    ,TO_CHAR(s.sougi_ymd,'YYYY/MM/DD') AS sougi_ymd
    ,sd.est_shikijo_cd                       -- 見積式場コード
    ,bm.f_free1 AS bumon_logo
    ,bm.char_free2 AS bumon_kaisya_nm
    ,cm6852.kbn_value_snm AS bumon_number
    ,CASE WHEN sssi.houjin_kbn IS NOT NULL AND sssi.houjin_sbt IS NOT NULL THEN 
       (CASE WHEN sssi.houjin_kbn = 2 THEN CONCAT(cm7810.kbn_value_lnm, sssi.sekyu_nm1, sssi.sekyu_nm2)
             WHEN sssi.houjin_kbn = 3 THEN CONCAT(sssi.sekyu_nm1, sssi.sekyu_nm2, cm7810.kbn_value_lnm)
        ELSE CONCAT(sssi.sekyu_nm1, sssi.sekyu_nm2) END)
        ELSE CONCAT(sssi.sekyu_nm1, sssi.sekyu_nm2) END AS sekyu_nm
    ,sssi.sekyu_yubin_no
    ,sssi.sekyu_addr1 AS sekyu_addr1
    ,sssi.sekyu_addr2 AS sekyu_addr2
    ,sssi.sekyu_soufu_nm
    ,sssi.v_free2 AS sekyu_soufu_nm2
    ,sssi.soufu_yubin_no
    ,sssi.soufu_addr1
    ,sssi.soufu_addr2
    ,sssi.soufu_kbn
    ,sssi.syorui_tenpu_kbn
    ,sssi.pay_kbn
    ,sd.d_free1
    -- 金額関係
    ,COALESCE(sd.kouden_uchikin_prc, 0) AS kouden_uchikin_prc -- 香典内金額
    ,COALESCE(sd.uchikin_prc, 0) AS uchikin_prc -- 内金入金額
    ,COALESCE(sd.cupon_prc, 0) AS cupon_prc -- クーポン利用額
    ,COALESCE(sd.nyukin_prc, 0) AS nyukin_prc -- 入金金額
    ,COALESCE(sd.seikyu_zan, 0) AS seikyu_zan -- 請求残額
    ,COALESCE(sd.sougi_zei_sagaku_prc, 0) AS sougi_zei_sagaku_prc -- 掛金消費税差額
    ,COALESCE(sd.nyukin_prc, 0) + COALESCE(sd.seikyu_zan, 0)  AS seikyu_prc -- 請求金額
    ,COALESCE(sd.out_zei_prc, 0) + COALESCE(sd.in_zei_prc, 0) AS zei_prc    -- 消費税額
  FROM temp_seikyu_denpyo sd
  LEFT JOIN uriage_denpyo ud
        ON ud.uri_den_no = sd.uri_den_no
        AND ud.delete_flg = 0
  LEFT JOIN seko_kihon_info s
        ON s.seko_no = sd.seko_no
        AND s.delete_flg = 0
  LEFT JOIN bumon_mst bm
        ON  sd.bumon_cd = bm.bumon_cd
        AND bm.delete_flg = 0
  LEFT JOIN tanto_mst tanto 
      ON s.seko_tanto_cd = tanto.tanto_cd 
      AND tanto.delete_flg = 0
  LEFT JOIN seikyu_sekyu_saki_info sssi 
        ON sssi.seikyu_den_no = sd.temp_seikyu_den_no 
        AND sssi.delete_flg = 0
  LEFT JOIN sekyu_saki_info ssi 
        ON ssi.sekyu_cd = sd.sekyu_cd 
        AND ssi.delete_flg = 0
  LEFT JOIN seko_nitei sn11
        ON  sn11.seko_no = COALESCE(ud.ref_seko_no, sd.seko_no)
        AND sn11.nitei_kbn = 11 -- 告別式
        AND sn11.delete_flg = 0
  LEFT JOIN seko_nitei_houji snh1
        ON  snh1.seko_no = COALESCE(ud.ref_seko_no, sd.seko_no)
        AND snh1.nitei_kbn = 1 -- 法要
        AND snh1.delete_flg = 0
  LEFT JOIN code_nm_mst cm8320
        ON cm8320.code_kbn = '8320'
        AND cm8320.kbn_value_cd_num = sd.data_kbn
        AND cm8320.delete_flg = 0
  LEFT JOIN code_nm_mst cm0010
        ON cm0010.code_kbn = '0010'
        AND cm0010.kbn_value_cd_num = s.moushi_kbn
        AND cm0010.delete_flg = 0
  LEFT JOIN code_nm_mst cm6852
        ON cm6852.code_kbn = '6852' -- 登録番号(部門別)
        AND cm6852.kbn_value_cd = sd.bumon_cd
        AND cm6852.delete_flg = 0
  LEFT JOIN code_nm_mst cm7810
        ON cm7810.code_kbn = '7810'
        AND cm7810.kbn_value_cd_num = sssi.houjin_sbt
        AND cm7810.delete_flg = 0
 WHERE sd.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * PDF出力 請求書 明細書 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $print_kbn  印刷区分 0:互助会別 1:明細複合
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findMsi($db, $keyHash = array(), $print_kbn = 0) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T2');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T3');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            if(isset($keyHash['data_kbn']) && $keyHash['data_kbn'] === '2'){ // 法事
                $orderBy = "ORDER BY T3.dai_bunrui_cd, T3.chu_bunrui_cd, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.n_free1 ELSE T3.mitumori_print_seq END";
            } else {
               $orderBy = "ORDER BY T3.dai_bunrui_cd, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.n_free1 ELSE T3.mitumori_print_seq END";
            }
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
FROM (
SELECT
     T2.record_kbn
    ,T2.seikyu_den_no
    ,T2.data_kbn
    ,T2.seko_no
    ,T2.seko_no_sub
    --  ,T2.disp_no -- 一行にまとめない
    ,MAX(T2.add_kbn)        AS  add_kbn
    ,T2.chu_bunrui_cd
    ,T2.shohin_nm
    ,T2.shohin_tkiyo_nm
    ,T2.hoshi_disp
    ,T2.zei_kbn
    ,T2.zei_cd
    ,T2.zei_disp
    ,T2.denpyo_kbn
    ,SUM(T2.juchu_suryo)    AS  juchu_suryo
    ,T2.uri_tnk
    ,SUM(T2.uri_prc)      AS  uri_prc
    ,T2.chu_bunrui_nm
    ,T2.tani_nm
    ,T2.koumoku
    ,T2.service_kbn
    ,T2.dai_bunrui_cd
    ,T2.dai_bunrui_nm
    ,T2.shohin_kbn
    ,T2.shohin_cd
    ,T2.siire_tnk
    ,T2.hachu_kbn
    ,T2.print_group_cd         --20151207追加
    ,T2.mitumori_print_seq     --20151207追加
    ,T2.gojokai_nebiki_prc     --20151207追加
    ,T2.nebiki_prc             --20151207追加
    ,T2.n_free1
    ,T2.nebiki_ritu            -- 割引率
    ,SUM(T2.out_zei_prc) AS out_zei_prc
    ,SUM(T2.in_zei_prc) AS in_zei_prc
    ,T2.reduced_tax_rate AS reduced_tax_rate   -- 20190430 mihara keigen
    ,T2.gazo_img
    ,T2.kashidashi_kbn
    ,T2.upgrade_kbn
    ,T2.plan_shohin_cd           --アップデート元商品コード
    ,T2.plan_shohin_nm           --アップデート元商品名
    ,T2.denpyo_kbn_nm
    ,T2.bun_gas_kbn_num
    ,T2.bun_gas_seikyu_den_no
    ,T2.nyukin_prc
    ,T2.seikyu_zan
    ,T2.seikyu_prc
    ,T2.nafuda_nm
    ,T2.nafuda_nm2
    ,T2.nafuda_nm3
    ,T2.nafuda_nm4
FROM (
SELECT
     CASE WHEN plan_smsi.service_kbn IN (4,5) THEN 2 ELSE 0 END AS  record_kbn
    ,h.seikyu_den_no
    ,h.data_kbn
    ,h.seko_no
    ,h.seko_no_sub
    --  ,d.disp_no
    ,d.add_kbn
    ,d.denpyo_kbn
    ,d.chu_bunrui_cd
    ,d.shohin_kbn
    ,d.shohin_cd
    ,CASE WHEN shohin.nm_input_kbn in (2,4) THEN d.shohin_nm
        ELSE shohin.shohin_nm END AS shohin_nm --商品名
    ,COALESCE(d.shohin_tkiyo_nm, '') AS  shohin_tkiyo_nm
    ,d.zei_kbn
    ,d.zei_cd
    ,CASE WHEN d.hoshi_prc <> 0 THEN 'サービス料対象' ELSE '' END AS  hoshi_disp
    ,CASE WHEN d.zei_Kbn = 0 THEN '非' WHEN d.zei_Kbn = 1 THEN '内' ELSE '' END AS  zei_disp
    ,d.juchu_suryo
    ,d.uri_tnk
    ,d.uri_prc
    ,chu_bunrui.chu_bunrui_nm
    ,tani.tani_nm
    ,sho_kbn.shohin_kbn_nm AS koumoku
    ,CASE WHEN kihon.gojokai_kbn = 6 AND plan_smsi.service_kbn = 1 AND d.add_kbn = 9 THEN 0 ELSE plan_smsi.service_kbn END service_kbn
    ,d.dai_bunrui_cd
    ,dai_bunrui.dai_bunrui_nm
    ,stm.siire_tnk
    ,sbm.hachu_kbn
    ,sbm.print_group_cd         --20151207追加
    ,sbm.mitumori_print_seq     --20151207追加
    ,d.gojokai_nebiki_prc       --20151207追加
    ,d.nebiki_prc               --20151207追加
    ,d.n_free1 -- 売上伝票明細No(供花供物用)
    ,d.n_free2 AS nebiki_ritu   -- 割引率
    ,d.out_zei_prc
    ,d.in_zei_prc  
    ,d.reduced_tax_rate   -- 20190430 mihara keigen
    ,shohin.gazo_img
    ,shohin.kashidashi_kbn
    ,d.upgrade_kbn
    ,d.plan_shohin_cd           --アップデート元商品コード
    ,p_shohin.shohin_nm AS plan_shohin_nm --アップデート元商品名
    ,CASE WHEN d.denpyo_kbn = 11 THEN '追加' 
        ELSE NULL END AS denpyo_kbn_nm
    ,h.bun_gas_kbn_num
    ,h.bun_gas_seikyu_den_no
    ,COALESCE(h.nyukin_prc, 0) AS nyukin_prc
    ,COALESCE(h.seikyu_zan, 0) AS seikyu_zan
    ,COALESCE(h.nyukin_prc, 0) + COALESCE(h.seikyu_zan, 0)  AS seikyu_prc
    ,COALESCE(udm.nafuda_nm, '')  AS  nafuda_nm        -- 名札1
    ,COALESCE(udm.nafuda_nm2, '') AS  nafuda_nm2       -- 名札2
    ,COALESCE(udm.nafuda_nm3, '') AS  nafuda_nm3       -- 名札3
    ,COALESCE(udm.nafuda_nm4, '') AS  nafuda_nm4       -- 名札4
  FROM seikyu_denpyo h
  LEFT JOIN seikyu_denpyo_msi d
        ON  h.seikyu_den_no = d.seikyu_den_no
        AND d.delete_flg = 0
  LEFT JOIN uriage_denpyo_msi udm 
        ON  udm.seko_no = h.seko_no 
        AND udm.data_sbt = 8 
        AND udm.n_free1 = d.n_free1
  LEFT JOIN seko_kihon_info kihon
        ON h.seko_no            = kihon.seko_no
        AND kihon.delete_flg    = 0
  LEFT JOIN shohin_chu_bunrui_mst chu_bunrui 
        ON d.chu_bunrui_cd = chu_bunrui.chu_bunrui_cd
        AND chu_bunrui.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn
        ON  d.shohin_kbn = sho_kbn.shohin_kbn
        AND sho_kbn.delete_flg = 0
  LEFT JOIN shohin_mst shohin 
        ON d.shohin_cd = shohin.shohin_cd
        AND d.shohin_bumon_cd = shohin.bumon_cd
        AND shohin.hihyoji_kbn  = 0
        AND shohin.delete_flg   = 0
  LEFT JOIN shohin_mst p_shohin 
        ON d.plan_shohin_cd = p_shohin.shohin_cd
        AND d.plan_shohin_bumon_cd = p_shohin.bumon_cd
        AND p_shohin.hihyoji_kbn  = 0
        AND p_shohin.delete_flg   = 0
  LEFT JOIN tani_mst tani 
        ON d.tani_cd = tani.tani_cd
        AND tani.delete_flg = 0
  LEFT JOIN seko_plan_smsi_mst plan_smsi 
        ON h.seko_plan_cd = plan_smsi.seko_plan_cd
        AND d.msi_no = plan_smsi.seko_plan_uchiwk_no
        AND d.shohin_bumon_cd = plan_smsi.bumon_cd
        AND d.dai_bunrui_cd = plan_smsi.dai_bunrui_cd
        AND d.chu_bunrui_cd = plan_smsi.chu_bunrui_cd
        AND d.shohin_kbn    = plan_smsi.shohin_kbn
        AND d.shohin_cd     = plan_smsi.shohin_cd
        AND TO_CHAR(plan_smsi.tekiyo_st_date,'YYYY/MM/DD') <= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND TO_CHAR(plan_smsi.tekiyo_ed_date,'YYYY/MM/DD') >= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND kihon.gojokai_kbn = plan_smsi.gojokai_kbn
        AND plan_smsi.delete_flg = 0
  LEFT JOIN shohin_dai_bunrui_mst dai_bunrui 
        ON d.dai_bunrui_cd = dai_bunrui.dai_bunrui_cd
        AND dai_bunrui.delete_flg = 0
  LEFT JOIN 
        shohin_bunrui_mst sbm
        ON  (
            d.dai_bunrui_cd = sbm.dai_bunrui_cd
        AND d.chu_bunrui_cd  = sbm.chu_bunrui_cd
        AND d.shohin_bumon_cd = sbm.bumon_cd
        AND d.shohin_kbn  = sbm.shohin_kbn
        AND d.shohin_cd  = sbm.shohin_cd
        AND sbm.delete_flg = 0
        )
  LEFT JOIN 
        shohin_tanka_mst stm
        ON  d.shohin_bumon_cd = stm.bumon_cd
        AND d.shohin_cd  = stm.shohin_cd
        AND stm.delete_flg = 0
        AND TO_CHAR(d.juchu_ymd,'YYYY/MM/DD') BETWEEN TO_CHAR(stm.tekiyo_st_date,'YYYY/MM/DD') AND TO_CHAR(stm.tekiyo_ed_date,'YYYY/MM/DD')
 WHERE d.juchu_suryo <> 0 AND h.delete_flg = 0
UNION ALL
-- 奉仕料
SELECT
     1                  AS  record_kbn
    ,T1.seikyu_den_no
    ,T1.data_kbn
    ,T1.seko_no
    ,T1.seko_no_sub
    --  ,NULL               AS  disp_no
    ,NULL               AS  add_kbn
    ,NULL               AS  denpyo_kbn
    ,T1.chu_bunrui_cd
    ,NULL AS shohin_kbn
    ,NULL AS shohin_cd
    ,(select service_nm from kaisya_info limit 1) || '（' || hos.zei_rtu || '％）' AS shohin_nm
    ,NULL AS shohin_tkiyo_nm
    ,NULL               AS  hoshi_disp　
    ,NULL               AS  zei_kbn
    ,NULL               AS  zei_cd
    ,NULL               AS  zei_disp
    ,NULL               AS  juchu_suryo
    ,NULL               AS  uri_tnk
    ,T1.hoshi_prc       AS  uri_prc
    ,chu_bunrui.chu_bunrui_nm
    ,NULL               AS  tani_nm
    ,NULL               AS  koumoku
    ,NULL               AS  service_kbn
    ,T1.dai_bunrui_cd
    ,dai_bunrui.dai_bunrui_nm
    ,T1.siire_tnk
    ,T1.hachu_kbn
    ,T1.print_group_cd         --20151207追加
    ,T1.mitumori_print_seq     --20151207追加
    ,T1.gojokai_nebiki_prc     --20151207追加
    ,T1.nebiki_prc             --20151207追加
    ,NULL               AS n_free1
    ,NULL               AS nebiki_ritu
    ,NULL               AS out_zei_prc 
    ,NULL               AS in_zei_prc
    ,NULL AS reduced_tax_rate   -- 20190430 mihara keigen
    ,NULL               AS  gazo_img
    ,0                  AS  kashidashi_kbn
    ,NULL AS upgrade_kbn
    ,NULL AS plan_shohin_cd           --アップデート元商品コード
    ,NULL AS plan_shohin_nm           --アップデート元商品名
    ,NULL AS denpyo_kbn_nm
    ,NULL AS bun_gas_kbn_num
    ,NULL AS bun_gas_seikyu_den_no
    ,NULL AS nyukin_prc
    ,NULL AS seikyu_zan
    ,NULL AS seikyu_prc
    ,NULL AS nafuda_nm
    ,NULL AS nafuda_nm2
    ,NULL AS nafuda_nm3
    ,NULL AS nafuda_nm4
  FROM (
    SELECT
      h.seikyu_den_no
     ,h.data_kbn
     ,h.seko_no
     ,h.seko_no_sub
     ,d.chu_bunrui_cd
     ,d.dai_bunrui_cd
     ,d.hoshi_ritu_cd
     ,SUM(d.hoshi_prc) AS   hoshi_prc
     ,SUM( CASE WHEN d.hoshi_umu_kbn = 1 THEN d.uri_prc ELSE 0 END) AS uri_prc
     ,0 AS siire_tnk
     ,0 AS hachu_kbn
     ,0 AS print_group_cd        --20151207追加
     ,MAX(mitumori_print_seq) AS mitumori_print_seq     --20151207追加
     ,0 AS gojokai_nebiki_prc    --20151207追加
     ,0 AS nebiki_prc            --20151207追加
    FROM seikyu_denpyo h
    LEFT JOIN seikyu_denpyo_msi d 
        ON h.seikyu_den_no = d.seikyu_den_no 
        AND d.delete_flg = 0
    LEFT JOIN shohin_mst shohin 
        ON d.shohin_cd = shohin.shohin_cd 
        AND d.shohin_bumon_cd = shohin.bumon_cd
        AND shohin.hihyoji_kbn = 0
        AND shohin.delete_flg = 0
    LEFT JOIN 
          shohin_bunrui_mst sbm
          ON  (
              d.dai_bunrui_cd = sbm.dai_bunrui_cd
          AND d.chu_bunrui_cd  = sbm.chu_bunrui_cd
          AND d.shohin_bumon_cd = sbm.bumon_cd
          AND d.shohin_kbn  = sbm.shohin_kbn
          AND d.shohin_cd  = sbm.shohin_cd
          AND sbm.delete_flg = 0
          )
    WHERE   h.delete_flg = 0
   -- AND     d.uri_prc <> 0
   -- AND     d.uri_tnk <> 0 
    GROUP BY h.data_kbn
        , h.seikyu_den_no
        , h.seko_no
        , h.seko_no_sub
        , d.chu_bunrui_cd
        , d.dai_bunrui_cd
        , d.hoshi_ritu_cd
    HAVING SUM(d.hoshi_prc) <> 0
  ) T1
  LEFT JOIN hoshi_ritu_mst hos 
      ON    T1.hoshi_ritu_cd = hos.hoshi_ritu_cd
      AND   0                = hos.delete_flg       
  LEFT JOIN shohin_chu_bunrui_mst chu_bunrui 
      ON T1.chu_bunrui_cd = chu_bunrui.chu_bunrui_cd 
      AND chu_bunrui.delete_flg = 0
  LEFT JOIN shohin_dai_bunrui_mst dai_bunrui 
        ON T1.dai_bunrui_cd = dai_bunrui.dai_bunrui_cd
        AND dai_bunrui.delete_flg = 0
) T2
 WHERE $whereStr
 GROUP BY
     T2.record_kbn
    ,T2.seikyu_den_no
    ,T2.data_kbn
    ,T2.seko_no
    ,T2.seko_no_sub
    ,T2.chu_bunrui_cd
    ,T2.shohin_cd
    ,T2.shohin_nm
    ,T2.shohin_tkiyo_nm
    ,T2.uri_tnk
    ,T2.chu_bunrui_nm
    ,T2.tani_nm
    ,T2.koumoku
    ,T2.service_kbn
    ,T2.dai_bunrui_cd
    ,T2.dai_bunrui_nm
    ,T2.hoshi_disp
    ,T2.zei_kbn
    ,T2.zei_cd
    ,T2.zei_disp
    ,T2.shohin_kbn
    ,T2.shohin_cd
    ,T2.siire_tnk
    ,T2.hachu_kbn
    ,T2.print_group_cd         --20151207追加
    ,T2.mitumori_print_seq     --20151207追加
    ,T2.gojokai_nebiki_prc     --20151207追加
    ,T2.nebiki_prc             --20151207追加    
    ,T2.n_free1
    ,T2.nebiki_ritu
    --  ,T2.disp_no
    ,T2.denpyo_kbn
    ,T2.reduced_tax_rate        -- 20190430 mihara keigen
    ,T2.gazo_img
    ,T2.kashidashi_kbn
    ,T2.upgrade_kbn
    ,T2.plan_shohin_cd           --アップデート元商品コード
    ,T2.plan_shohin_nm           --アップデート元商品名
    ,T2.denpyo_kbn_nm
    ,T2.bun_gas_kbn_num
    ,T2.bun_gas_seikyu_den_no
    ,T2.nyukin_prc
    ,T2.seikyu_zan
    ,T2.seikyu_prc
    ,T2.nafuda_nm
    ,T2.nafuda_nm2
    ,T2.nafuda_nm3
    ,T2.nafuda_nm4
) T3  
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }
    
    /**
     * PDF出力 請求書 明細書 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $print_kbn  印刷区分 0:互助会別 1:明細複合
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findMsiForTemp($db, $keyHash = array(), $print_kbn = 0) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T2');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T3');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            if(isset($keyHash['data_kbn']) && $keyHash['data_kbn'] === '2'){ // 法事
                $orderBy = "ORDER BY T3.dai_bunrui_cd, T3.chu_bunrui_cd, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.n_free1 ELSE T3.mitumori_print_seq END, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.mitumori_print_seq  ELSE T3.disp_no END";
            } else {
               $orderBy = "ORDER BY T3.dai_bunrui_cd, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.n_free1 ELSE T3.mitumori_print_seq END, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.mitumori_print_seq  ELSE T3.disp_no END";
            }
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
FROM (
SELECT
     T2.record_kbn
    ,T2.seikyu_den_no
    ,T2.data_kbn
    ,T2.seko_no
    ,T2.seko_no_sub
    ,T2.disp_no -- 一行にまとめない
    ,MAX(T2.add_kbn)        AS  add_kbn
    ,T2.chu_bunrui_cd
    ,T2.shohin_nm
    ,T2.shohin_tkiyo_nm
    ,T2.hoshi_disp
    ,T2.zei_kbn
    ,T2.zei_cd
    ,T2.zei_disp
    ,T2.denpyo_kbn
    ,SUM(T2.juchu_suryo)    AS  juchu_suryo
    ,T2.uri_tnk
    ,SUM(T2.uri_prc)      AS  uri_prc
    ,T2.chu_bunrui_nm
    ,T2.tani_nm
    ,T2.koumoku
    ,T2.service_kbn
    ,T2.dai_bunrui_cd
    ,T2.dai_bunrui_nm
    ,T2.shohin_kbn
    ,T2.shohin_cd
    ,T2.siire_tnk
    ,T2.hachu_kbn
    ,T2.print_group_cd         --20151207追加
    ,T2.mitumori_print_seq     --20151207追加
    ,T2.gojokai_nebiki_prc     --20151207追加
    ,T2.nebiki_prc             --20151207追加
    ,T2.n_free1 -- 売上伝票明細No(供花供物用)
    ,T2.nebiki_ritu            -- 割引率
    ,SUM(T2.out_zei_prc) AS out_zei_prc
    ,SUM(T2.in_zei_prc) AS in_zei_prc
    ,T2.reduced_tax_rate AS reduced_tax_rate   -- 20190430 mihara keigen
    ,T2.gazo_img
    ,T2.kashidashi_kbn
    ,T2.upgrade_kbn
    ,T2.plan_shohin_cd           --アップデート元商品コード
    ,T2.plan_shohin_nm           --アップデート元商品名
    ,T2.denpyo_kbn_nm
    ,T2.bun_gas_kbn_num
    ,T2.bun_gas_seikyu_den_no
    ,T2.nyukin_prc
    ,T2.seikyu_zan
    ,T2.seikyu_prc
    ,T2.nafuda_nm
    ,T2.nafuda_nm2
    ,T2.nafuda_nm3
    ,T2.nafuda_nm4
FROM (
SELECT
     CASE WHEN plan_smsi.service_kbn IN (4,5) THEN 2 ELSE 0 END AS  record_kbn
    ,h.temp_seikyu_den_no AS seikyu_den_no
    ,h.data_kbn
    ,h.seko_no
    ,h.seko_no_sub
    ,d.disp_no
    ,d.add_kbn
    ,d.denpyo_kbn
    ,d.chu_bunrui_cd
    ,d.shohin_kbn
    ,d.shohin_cd
    ,CASE WHEN shohin.nm_input_kbn in (2,4) THEN d.shohin_nm
        ELSE shohin.shohin_nm END AS shohin_nm --商品名
    ,COALESCE(d.shohin_tkiyo_nm, '') AS  shohin_tkiyo_nm
    ,d.zei_kbn
    ,d.zei_cd
    ,CASE WHEN d.hoshi_prc <> 0 THEN 'サービス料対象' ELSE '' END AS  hoshi_disp
    ,CASE WHEN d.zei_Kbn = 0 THEN '非' WHEN d.zei_Kbn = 1 THEN '内' ELSE '' END AS  zei_disp
    ,d.juchu_suryo
    ,d.uri_tnk
    ,d.uri_prc
    ,chu_bunrui.chu_bunrui_nm
    ,tani.tani_nm
    ,sho_kbn.shohin_kbn_nm AS koumoku
    ,CASE WHEN kihon.gojokai_kbn = 6 AND plan_smsi.service_kbn = 1 AND d.add_kbn = 9 THEN 0 ELSE plan_smsi.service_kbn END service_kbn
    ,d.dai_bunrui_cd
    ,dai_bunrui.dai_bunrui_nm
    ,stm.siire_tnk
    ,sbm.hachu_kbn
    ,sbm.print_group_cd         --20151207追加
    ,sbm.mitumori_print_seq     --20151207追加
    ,d.gojokai_nebiki_prc       --20151207追加
    ,d.nebiki_prc               --20151207追加
    ,d.n_free1
    ,d.n_free2 AS nebiki_ritu   -- 割引率
    ,d.out_zei_prc
    ,d.in_zei_prc  
    ,d.reduced_tax_rate   -- 20190430 mihara keigen
    ,shohin.gazo_img
    ,shohin.kashidashi_kbn
    ,d.upgrade_kbn
    ,d.plan_shohin_cd           --アップデート元商品コード
    ,p_shohin.shohin_nm AS plan_shohin_nm --アップデート元商品名
    ,CASE WHEN d.denpyo_kbn = 11 THEN '追加' 
        ELSE NULL END AS denpyo_kbn_nm
    ,h.bun_gas_kbn_num
    ,h.bun_gas_seikyu_den_no
    ,COALESCE(h.nyukin_prc, 0) AS nyukin_prc
    ,COALESCE(h.seikyu_zan, 0) AS seikyu_zan
    ,COALESCE(h.nyukin_prc, 0) + COALESCE(h.seikyu_zan, 0)  AS seikyu_prc
    ,COALESCE(udm.nafuda_nm, '')  AS  nafuda_nm        -- 名札1
    ,COALESCE(udm.nafuda_nm2, '') AS  nafuda_nm2       -- 名札2
    ,COALESCE(udm.nafuda_nm3, '') AS  nafuda_nm3       -- 名札3
    ,COALESCE(udm.nafuda_nm4, '') AS  nafuda_nm4       -- 名札4
  FROM temp_seikyu_denpyo h
  LEFT JOIN temp_seikyu_denpyo_msi d 
        ON  h.temp_seikyu_den_no = d.temp_seikyu_den_no
        AND d.delete_flg = 0
  LEFT JOIN uriage_denpyo_msi udm 
        ON  udm.seko_no = h.seko_no 
        AND udm.data_sbt = 8 
        AND udm.n_free1 = d.n_free1
  LEFT JOIN seko_kihon_info kihon
        ON h.seko_no            = kihon.seko_no
        AND kihon.delete_flg    = 0
  LEFT JOIN shohin_chu_bunrui_mst chu_bunrui 
        ON d.chu_bunrui_cd = chu_bunrui.chu_bunrui_cd
        AND chu_bunrui.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn
        ON  d.shohin_kbn = sho_kbn.shohin_kbn
        AND sho_kbn.delete_flg = 0
  LEFT JOIN shohin_mst shohin 
        ON d.shohin_cd = shohin.shohin_cd
        AND d.shohin_bumon_cd = shohin.bumon_cd
        AND shohin.hihyoji_kbn  = 0
        AND shohin.delete_flg   = 0
  LEFT JOIN shohin_mst p_shohin 
        ON d.plan_shohin_cd = p_shohin.shohin_cd
        AND d.plan_shohin_bumon_cd = p_shohin.bumon_cd
        AND p_shohin.hihyoji_kbn  = 0
        AND p_shohin.delete_flg   = 0
  LEFT JOIN tani_mst tani 
        ON d.tani_cd = tani.tani_cd
        AND tani.delete_flg = 0
  LEFT JOIN seko_plan_smsi_mst plan_smsi 
        ON h.seko_plan_cd = plan_smsi.seko_plan_cd
        AND d.msi_no = plan_smsi.seko_plan_uchiwk_no
        AND d.shohin_bumon_cd = plan_smsi.bumon_cd
        AND d.dai_bunrui_cd = plan_smsi.dai_bunrui_cd
        AND d.chu_bunrui_cd = plan_smsi.chu_bunrui_cd
        AND d.shohin_kbn    = plan_smsi.shohin_kbn
        AND d.shohin_cd     = plan_smsi.shohin_cd
        AND TO_CHAR(plan_smsi.tekiyo_st_date,'YYYY/MM/DD') <= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND TO_CHAR(plan_smsi.tekiyo_ed_date,'YYYY/MM/DD') >= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND kihon.gojokai_kbn = plan_smsi.gojokai_kbn
        AND plan_smsi.delete_flg = 0
  LEFT JOIN shohin_dai_bunrui_mst dai_bunrui 
        ON d.dai_bunrui_cd = dai_bunrui.dai_bunrui_cd
        AND dai_bunrui.delete_flg = 0
  LEFT JOIN 
        shohin_bunrui_mst sbm
        ON  (
            d.dai_bunrui_cd = sbm.dai_bunrui_cd
        AND d.chu_bunrui_cd  = sbm.chu_bunrui_cd
        AND d.shohin_bumon_cd = sbm.bumon_cd
        AND d.shohin_kbn  = sbm.shohin_kbn
        AND d.shohin_cd  = sbm.shohin_cd
        AND sbm.delete_flg = 0
        )
  LEFT JOIN 
        shohin_tanka_mst stm
        ON  d.shohin_bumon_cd = stm.bumon_cd
        AND d.shohin_cd  = stm.shohin_cd
        AND stm.delete_flg = 0
        AND TO_CHAR(d.juchu_ymd,'YYYY/MM/DD') BETWEEN TO_CHAR(stm.tekiyo_st_date,'YYYY/MM/DD') AND TO_CHAR(stm.tekiyo_ed_date,'YYYY/MM/DD')
 WHERE d.juchu_suryo <> 0 AND h.delete_flg = 0
UNION ALL
-- 奉仕料
SELECT
     1                  AS  record_kbn
    ,T1.seikyu_den_no
    ,T1.data_kbn
    ,T1.seko_no
    ,T1.seko_no_sub
    ,NULL               AS  disp_no
    ,NULL               AS  add_kbn
    ,NULL               AS  denpyo_kbn
    ,T1.chu_bunrui_cd
    ,NULL AS shohin_kbn
    ,NULL AS shohin_cd
    ,(select service_nm from kaisya_info limit 1) || '（' || hos.zei_rtu || '％）' AS shohin_nm
    ,NULL AS shohin_tkiyo_nm
    ,NULL               AS  hoshi_disp　
    ,NULL               AS  zei_kbn
    ,NULL               AS  zei_cd
    ,NULL               AS  zei_disp
    ,NULL               AS  juchu_suryo
    ,NULL               AS  uri_tnk
    ,T1.hoshi_prc       AS  uri_prc
    ,chu_bunrui.chu_bunrui_nm
    ,NULL               AS  tani_nm
    ,NULL               AS  koumoku
    ,NULL               AS  service_kbn
    ,T1.dai_bunrui_cd
    ,dai_bunrui.dai_bunrui_nm
    ,T1.siire_tnk
    ,T1.hachu_kbn
    ,T1.print_group_cd         --20151207追加
    ,T1.mitumori_print_seq     --20151207追加
    ,T1.gojokai_nebiki_prc     --20151207追加
    ,T1.nebiki_prc             --20151207追加
    ,NULL               AS n_free1
    ,NULL               AS nebiki_ritu
    ,NULL               AS out_zei_prc 
    ,NULL               AS in_zei_prc
    ,NULL AS reduced_tax_rate   -- 20190430 mihara keigen
    ,NULL               AS  gazo_img
    ,0                  AS  kashidashi_kbn
    ,NULL AS upgrade_kbn
    ,NULL AS plan_shohin_cd           --アップデート元商品コード
    ,NULL AS plan_shohin_nm           --アップデート元商品名
    ,NULL AS denpyo_kbn_nm
    ,NULL AS bun_gas_kbn_num
    ,NULL AS bun_gas_seikyu_den_no
    ,NULL AS nyukin_prc
    ,NULL AS seikyu_zan
    ,NULL AS seikyu_prc
    ,NULL AS nafuda_nm
    ,NULL AS nafuda_nm2
    ,NULL AS nafuda_nm3
    ,NULL AS nafuda_nm4
  FROM (
    SELECT
      h.temp_seikyu_den_no AS seikyu_den_no
     ,h.data_kbn
     ,h.seko_no
     ,h.seko_no_sub
     ,d.chu_bunrui_cd
     ,d.dai_bunrui_cd
     ,d.hoshi_ritu_cd
     ,SUM(d.hoshi_prc) AS   hoshi_prc
     ,SUM( CASE WHEN d.hoshi_umu_kbn = 1 THEN d.uri_prc ELSE 0 END) AS uri_prc
     ,0 AS siire_tnk
     ,0 AS hachu_kbn
     ,0 AS print_group_cd        --20151207追加
     ,MAX(mitumori_print_seq) AS mitumori_print_seq     --20151207追加
     ,0 AS gojokai_nebiki_prc    --20151207追加
     ,0 AS nebiki_prc            --20151207追加
    FROM temp_seikyu_denpyo h
    LEFT JOIN temp_seikyu_denpyo_msi d 
        ON h.temp_seikyu_den_no = d.temp_seikyu_den_no 
        AND d.delete_flg = 0
    LEFT JOIN shohin_mst shohin 
        ON d.shohin_cd = shohin.shohin_cd 
        AND d.shohin_bumon_cd = shohin.bumon_cd
        AND shohin.hihyoji_kbn = 0
        AND shohin.delete_flg = 0
    LEFT JOIN 
          shohin_bunrui_mst sbm
          ON  (
              d.dai_bunrui_cd = sbm.dai_bunrui_cd
          AND d.chu_bunrui_cd  = sbm.chu_bunrui_cd
          AND d.shohin_bumon_cd = sbm.bumon_cd
          AND d.shohin_kbn  = sbm.shohin_kbn
          AND d.shohin_cd  = sbm.shohin_cd
          AND sbm.delete_flg = 0
          )
    WHERE   h.delete_flg = 0
   -- AND     d.uri_prc <> 0
   -- AND     d.uri_tnk <> 0 
    GROUP BY h.data_kbn
        , h.temp_seikyu_den_no
        , h.seko_no
        , h.seko_no_sub
        , d.chu_bunrui_cd
        , d.dai_bunrui_cd
        , d.hoshi_ritu_cd
    HAVING SUM(d.hoshi_prc) <> 0
  ) T1
  LEFT JOIN hoshi_ritu_mst hos 
      ON    T1.hoshi_ritu_cd = hos.hoshi_ritu_cd
      AND   0                = hos.delete_flg       
  LEFT JOIN shohin_chu_bunrui_mst chu_bunrui 
      ON T1.chu_bunrui_cd = chu_bunrui.chu_bunrui_cd 
      AND chu_bunrui.delete_flg = 0
  LEFT JOIN shohin_dai_bunrui_mst dai_bunrui 
        ON T1.dai_bunrui_cd = dai_bunrui.dai_bunrui_cd
        AND dai_bunrui.delete_flg = 0
) T2
 WHERE $whereStr
 GROUP BY
     T2.record_kbn
    ,T2.seikyu_den_no
    ,T2.data_kbn
    ,T2.seko_no
    ,T2.seko_no_sub
    ,T2.chu_bunrui_cd
    ,T2.shohin_nm
    ,T2.shohin_tkiyo_nm
    ,T2.uri_tnk
    ,T2.chu_bunrui_nm
    ,T2.tani_nm
    ,T2.koumoku
    ,T2.service_kbn
    ,T2.dai_bunrui_cd
    ,T2.dai_bunrui_nm
    ,T2.hoshi_disp
    ,T2.zei_kbn
    ,T2.zei_cd
    ,T2.zei_disp
    ,T2.shohin_kbn
    ,T2.shohin_cd
    ,T2.siire_tnk
    ,T2.hachu_kbn
    ,T2.print_group_cd         --20151207追加
    ,T2.mitumori_print_seq     --20151207追加
    ,T2.gojokai_nebiki_prc     --20151207追加
    ,T2.nebiki_prc             --20151207追加    
    ,T2.n_free1
    ,T2.nebiki_ritu
    ,T2.disp_no
    ,T2.denpyo_kbn
    ,T2.reduced_tax_rate        -- 20190430 mihara keigen
    ,T2.gazo_img
    ,T2.kashidashi_kbn
    ,T2.upgrade_kbn
    ,T2.plan_shohin_cd           --アップデート元商品コード
    ,T2.plan_shohin_nm           --アップデート元商品名
    ,T2.denpyo_kbn_nm
    ,T2.bun_gas_kbn_num
    ,T2.bun_gas_seikyu_den_no
    ,T2.nyukin_prc
    ,T2.seikyu_zan
    ,T2.seikyu_prc
    ,T2.nafuda_nm
    ,T2.nafuda_nm2
    ,T2.nafuda_nm3
    ,T2.nafuda_nm4
) T3  
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * PDF出力 請求書 明細書 取得
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $print_kbn  印刷区分 0:互助会別 1:明細複合
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findMsiAfter($db, $keyHash = array(), $print_kbn = 0) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            if(isset($keyHash['data_kbn']) && $keyHash['data_kbn'] === '2'){ // 法事
               $orderBy = 'ORDER BY T.nonyu_dt, T.dai_bunrui_cd, T.chu_bunrui_cd, CASE WHEN T.upgrade_kbn IN (1,2) THEN 1 ELSE 2 END, T.mitumori_print_seq, T.disp_no ';
            } else {
               $orderBy = 'ORDER BY T.nonyu_dt, T.dai_bunrui_cd, CASE WHEN T.upgrade_kbn IN (1,2) THEN 1 ELSE 2 END, T.mitumori_print_seq, T.disp_no ';
            }
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
FROM (
    SELECT
     CASE WHEN plan_smsi.service_kbn IN (4,5) THEN 2 ELSE 0 END AS  record_kbn
    ,h.seikyu_den_no
    ,h.data_kbn
    ,h.seko_no
    ,h.seko_no_sub
    ,kihon.souke_nm
    ,d.disp_no
    ,d.add_kbn
    ,d.denpyo_kbn
    ,d.chu_bunrui_cd
    ,d.shohin_kbn
    ,d.shohin_cd
    ,CASE WHEN shohin.nm_input_kbn in (2,4) THEN d.shohin_nm
        ELSE shohin.shohin_nm END AS shohin_nm --商品名
    ,COALESCE(d.shohin_tkiyo_nm, '') AS  shohin_tkiyo_nm
    ,d.zei_kbn
    ,d.zei_cd
    ,CASE WHEN d.hoshi_prc <> 0 THEN 'サービス料対象' ELSE '' END AS  hoshi_disp
    ,CASE WHEN d.zei_Kbn = 0 THEN '非' WHEN d.zei_Kbn = 1 THEN '内' ELSE '' END AS  zei_disp
    ,d.juchu_suryo
    ,d.uri_tnk
    ,d.uri_prc
    ,chu_bunrui.chu_bunrui_nm
    ,tani.tani_nm
    ,sho_kbn.shohin_kbn_nm AS koumoku
    ,CASE WHEN kihon.gojokai_kbn = 6 AND plan_smsi.service_kbn = 1 AND d.add_kbn = 9 THEN 0 ELSE plan_smsi.service_kbn END service_kbn
    ,d.dai_bunrui_cd
    ,dai_bunrui.dai_bunrui_nm
    ,stm.siire_tnk
    ,sbm.hachu_kbn
    ,sbm.print_group_cd         --20151207追加
    ,sbm.mitumori_print_seq     --20151207追加
    ,d.gojokai_nebiki_prc       --20151207追加
    ,d.nebiki_prc               --20151207追加
    ,d.n_free2 AS nebiki_ritu   -- 割引率
    ,d.out_zei_prc
    ,d.in_zei_prc  
    ,d.reduced_tax_rate   -- 20190430 mihara keigen
    ,shohin.gazo_img
    ,shohin.kashidashi_kbn
    ,d.upgrade_kbn
    ,d.plan_shohin_cd           --アップデート元商品コード
    ,p_shohin.shohin_nm AS plan_shohin_nm --アップデート元商品名
    ,CASE WHEN denpyo_kbn = 11 THEN '追加' 
        ELSE NULL END AS denpyo_kbn_nm
    ,h.bun_gas_kbn_num
    ,h.bun_gas_seikyu_den_no
    ,COALESCE(h.nyukin_prc, 0) AS nyukin_prc
    ,COALESCE(h.seikyu_zan, 0) AS seikyu_zan
    ,COALESCE(h.nyukin_prc, 0) + COALESCE(h.seikyu_zan, 0)  AS seikyu_prc
    -- アフター用項目
    ,h.nonyu_dt         -- 納入日
    ,h.nonyu_nm         -- 納入先名
    ,COALESCE(d.nafuda_nm, '')  AS  nafuda_nm        -- 名札1
    ,COALESCE(d.nafuda_nm2, '') AS  nafuda_nm2       -- 名札2
    ,COALESCE(d.nafuda_nm3, '') AS  nafuda_nm3       -- 名札3
    ,COALESCE(d.nafuda_nm4, '') AS  nafuda_nm4       -- 名札4
  FROM seikyu_denpyo h
  LEFT JOIN seikyu_denpyo_msi d 
        ON  h.seikyu_den_no = d.seikyu_den_no
        AND d.delete_flg = 0
  LEFT JOIN seko_kihon_info kihon
        ON h.seko_no            = kihon.seko_no
        AND kihon.delete_flg    = 0
  LEFT JOIN shohin_chu_bunrui_mst chu_bunrui 
        ON d.chu_bunrui_cd = chu_bunrui.chu_bunrui_cd
        AND chu_bunrui.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn
        ON  d.shohin_kbn = sho_kbn.shohin_kbn
        AND sho_kbn.delete_flg = 0
  LEFT JOIN shohin_mst shohin 
        ON d.shohin_cd = shohin.shohin_cd
        AND d.shohin_bumon_cd = shohin.bumon_cd
        AND shohin.hihyoji_kbn  = 0
        AND shohin.delete_flg   = 0
  LEFT JOIN shohin_mst p_shohin 
        ON d.plan_shohin_cd = p_shohin.shohin_cd
        AND d.plan_shohin_bumon_cd = p_shohin.bumon_cd
        AND p_shohin.hihyoji_kbn  = 0
        AND p_shohin.delete_flg   = 0
  LEFT JOIN tani_mst tani 
        ON d.tani_cd = tani.tani_cd
        AND tani.delete_flg = 0
  LEFT JOIN seko_plan_smsi_mst plan_smsi 
        ON h.seko_plan_cd = plan_smsi.seko_plan_cd
        AND d.msi_no = plan_smsi.seko_plan_uchiwk_no
        AND d.shohin_bumon_cd = plan_smsi.bumon_cd
        AND d.dai_bunrui_cd = plan_smsi.dai_bunrui_cd
        AND d.chu_bunrui_cd = plan_smsi.chu_bunrui_cd
        AND d.shohin_kbn    = plan_smsi.shohin_kbn
        AND d.shohin_cd     = plan_smsi.shohin_cd
        AND TO_CHAR(plan_smsi.tekiyo_st_date,'YYYY/MM/DD') <= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND TO_CHAR(plan_smsi.tekiyo_ed_date,'YYYY/MM/DD') >= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND kihon.gojokai_kbn = plan_smsi.gojokai_kbn
        AND plan_smsi.delete_flg = 0
  LEFT JOIN shohin_dai_bunrui_mst dai_bunrui 
        ON d.dai_bunrui_cd = dai_bunrui.dai_bunrui_cd
        AND dai_bunrui.delete_flg = 0
  LEFT JOIN 
        shohin_bunrui_mst sbm
        ON  (
            d.dai_bunrui_cd = sbm.dai_bunrui_cd
        AND d.chu_bunrui_cd  = sbm.chu_bunrui_cd
        AND d.shohin_bumon_cd = sbm.bumon_cd
        AND d.shohin_kbn  = sbm.shohin_kbn
        AND d.shohin_cd  = sbm.shohin_cd
        AND sbm.delete_flg = 0
        )
  LEFT JOIN 
        shohin_tanka_mst stm
        ON  d.shohin_bumon_cd = stm.bumon_cd
        AND d.shohin_cd  = stm.shohin_cd
        AND stm.delete_flg = 0
        AND TO_CHAR(d.juchu_ymd,'YYYY/MM/DD') BETWEEN TO_CHAR(stm.tekiyo_st_date,'YYYY/MM/DD') AND TO_CHAR(stm.tekiyo_ed_date,'YYYY/MM/DD')
 WHERE d.juchu_suryo <> 0 AND h.delete_flg = 0
 ) T 
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }    
    
    /**
     * PDF出力 請求書 明細書 取得(売上伝票)
     *
     * <AUTHOR> Tosaka
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @param      boolean    $print_kbn  印刷区分 0:互助会別 1:明細複合
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findUriageMsi($db, $keyHash = array(), $print_kbn = 0) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T2');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T3');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
           if(isset($keyHash['data_kbn']) && $keyHash['data_kbn'] === '2'){ // 法事
               $orderBy = "ORDER BY T3.dai_bunrui_cd, T3.chu_bunrui_cd, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.disp_no ELSE T3.mitumori_print_seq END, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.mitumori_print_seq  ELSE T3.disp_no END";
            } else {
               $orderBy = "ORDER BY T3.dai_bunrui_cd, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.n_free1 ELSE T3.mitumori_print_seq END, CASE WHEN T3.dai_bunrui_cd = '0080' THEN T3.mitumori_print_seq  ELSE T3.disp_no END";
            }
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
FROM (
SELECT
     T2.record_kbn
    ,T2.data_kbn
    ,T2.seko_no
    ,T2.seko_no_sub
    ,T2.disp_no -- 一行にまとめない
    ,MAX(T2.add_kbn)        AS  add_kbn
    ,T2.chu_bunrui_cd
    ,T2.shohin_nm
    ,T2.shohin_tkiyo_nm
    ,T2.hoshi_disp
    ,T2.zei_disp
    ,T2.denpyo_kbn
    ,SUM(T2.juchu_suryo)    AS  juchu_suryo
    ,T2.uri_tnk
    ,SUM(T2.uri_prc)      AS  uri_prc
    ,T2.chu_bunrui_nm
    ,T2.tani_nm
    ,T2.koumoku
    ,T2.service_kbn
    ,T2.dai_bunrui_cd
    ,T2.dai_bunrui_nm
    ,T2.shohin_kbn
    ,T2.shohin_cd
    ,T2.siire_tnk
    ,T2.hachu_kbn
    ,T2.print_group_cd         --20151207追加
    ,T2.mitumori_print_seq     --20151207追加
    ,T2.gojokai_nebiki_prc     --20151207追加
    ,T2.nebiki_prc             --20151207追加
    ,T2.n_free1
    ,SUM(T2.out_zei_prc) AS out_zei_prc
    ,SUM(T2.in_zei_prc) AS in_zei_prc
    ,T2.reduced_tax_rate AS reduced_tax_rate   -- 20190430 mihara keigen
    ,T2.gazo_img
    ,T2.upgrade_kbn
    ,T2.plan_shohin_cd           --アップデート元商品コード
    ,T2.plan_shohin_nm           --アップデート元商品名
    ,T2.denpyo_kbn_nm           
FROM (
SELECT
     CASE WHEN plan_smsi.service_kbn IN (4,5) THEN 2 ELSE 0 END AS  record_kbn
    ,h.data_kbn
    ,h.seko_no
    ,h.seko_no_sub
    ,d.disp_no
    ,d.add_kbn
    ,d.denpyo_kbn
    ,d.chu_bunrui_cd
    ,d.shohin_kbn
    ,d.shohin_cd
    ,CASE WHEN shohin.nm_input_kbn in (2,4) THEN d.shohin_nm
        ELSE shohin.shohin_nm END AS shohin_nm --商品名
    ,COALESCE(d.shohin_tkiyo_nm, '') AS  shohin_tkiyo_nm
    ,CASE WHEN d.hoshi_prc <> 0 THEN 'サービス料対象' ELSE '' END AS  hoshi_disp
    ,CASE WHEN d.zei_Kbn = 0 THEN '非' WHEN d.zei_Kbn = 1 THEN '内' ELSE '' END AS  zei_disp
    ,d.juchu_suryo
    ,d.uri_tnk
    ,d.uri_prc
    ,chu_bunrui.chu_bunrui_nm
    ,tani.tani_nm
    ,sho_kbn.shohin_kbn_nm AS koumoku
    ,CASE WHEN kihon.gojokai_kbn = 6 AND plan_smsi.service_kbn = 1 AND d.add_kbn = 9 THEN 0 ELSE plan_smsi.service_kbn END service_kbn
    ,d.dai_bunrui_cd
    ,dai_bunrui.dai_bunrui_nm
    ,stm.siire_tnk
    ,sbm.hachu_kbn
    ,sbm.print_group_cd         --20151207追加
    ,sbm.mitumori_print_seq     --20151207追加
    ,d.gojokai_nebiki_prc       --20151207追加
    ,d.nebiki_prc               --20151207追加
    ,d.n_free1 -- 売上伝票明細No(供花供物用)
    ,d.out_zei_prc
    ,d.in_zei_prc  
    ,d.reduced_tax_rate   -- 20190430 mihara keigen
    ,shohin.gazo_img
    ,d.upgrade_kbn
    ,d.plan_shohin_cd           --アップデート元商品コード
    ,p_shohin.shohin_nm AS plan_shohin_nm --アップデート元商品名
    ,CASE WHEN denpyo_kbn = 11 THEN '追加' 
    ELSE NULL END AS denpyo_kbn_nm
  FROM uriage_denpyo h
  INNER JOIN uriage_denpyo_msi d 
        ON  h.uri_den_no = d.uri_den_no
        AND d.delete_flg = 0
  INNER JOIN seko_kihon_info kihon
        ON h.seko_no            = kihon.seko_no
        AND kihon.delete_flg    = 0
  LEFT JOIN shohin_chu_bunrui_mst chu_bunrui 
        ON d.chu_bunrui_cd = chu_bunrui.chu_bunrui_cd
        AND chu_bunrui.delete_flg = 0
  LEFT JOIN shohin_kbn_mst sho_kbn
        ON  d.shohin_kbn = sho_kbn.shohin_kbn
        AND sho_kbn.delete_flg = 0
  INNER JOIN shohin_mst shohin 
        ON d.shohin_cd = shohin.shohin_cd
        AND d.shohin_bumon_cd = shohin.bumon_cd
        AND shohin.hihyoji_kbn  = 0
        AND shohin.delete_flg   = 0
  LEFT JOIN shohin_mst p_shohin 
        ON d.plan_shohin_cd = p_shohin.shohin_cd
        AND d.plan_shohin_bumon_cd = p_shohin.bumon_cd
        AND p_shohin.hihyoji_kbn  = 0
        AND p_shohin.delete_flg   = 0
  LEFT JOIN tani_mst tani 
        ON d.tani_cd = tani.tani_cd
        AND tani.delete_flg = 0
  LEFT JOIN seko_plan_smsi_mst plan_smsi 
        ON h.seko_plan_cd = plan_smsi.seko_plan_cd
        AND d.msi_no = plan_smsi.seko_plan_uchiwk_no
        AND d.shohin_bumon_cd = plan_smsi.bumon_cd
        AND d.dai_bunrui_cd = plan_smsi.dai_bunrui_cd
        AND d.chu_bunrui_cd = plan_smsi.chu_bunrui_cd
        AND d.shohin_kbn    = plan_smsi.shohin_kbn
        AND d.shohin_cd     = plan_smsi.shohin_cd
        AND TO_CHAR(plan_smsi.tekiyo_st_date,'YYYY/MM/DD') <= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND TO_CHAR(plan_smsi.tekiyo_ed_date,'YYYY/MM/DD') >= TO_CHAR(h.juchu_ymd,'YYYY/MM/DD')
        AND kihon.gojokai_kbn = plan_smsi.gojokai_kbn
        AND plan_smsi.delete_flg = 0
  LEFT JOIN shohin_dai_bunrui_mst dai_bunrui 
        ON d.dai_bunrui_cd = dai_bunrui.dai_bunrui_cd
        AND dai_bunrui.delete_flg = 0
  LEFT JOIN 
        shohin_bunrui_mst sbm
        ON  (
            d.dai_bunrui_cd = sbm.dai_bunrui_cd
        AND d.chu_bunrui_cd  = sbm.chu_bunrui_cd
        AND d.shohin_bumon_cd = sbm.bumon_cd
        AND d.shohin_kbn  = sbm.shohin_kbn
        AND d.shohin_cd  = sbm.shohin_cd
        AND sbm.delete_flg = 0
        )
  LEFT JOIN 
        shohin_tanka_mst stm
        ON  d.shohin_bumon_cd = stm.bumon_cd
        AND d.shohin_cd  = stm.shohin_cd
        AND stm.delete_flg = 0
        AND TO_CHAR(d.juchu_ymd,'YYYY/MM/DD') BETWEEN TO_CHAR(stm.tekiyo_st_date,'YYYY/MM/DD') AND TO_CHAR(stm.tekiyo_ed_date,'YYYY/MM/DD')
 WHERE d.juchu_suryo <> 0 AND h.delete_flg = 0
UNION ALL
-- 奉仕料
SELECT
     1                  AS  record_kbn
    ,T1.data_kbn
    ,T1.seko_no
    ,T1.seko_no_sub
    ,NULL               AS  disp_no
    ,NULL               AS  add_kbn
    ,NULL               AS  denpyo_kbn
    ,T1.chu_bunrui_cd
    ,NULL AS shohin_kbn
    ,NULL AS shohin_cd
    ,(select service_nm from kaisya_info limit 1) || '（' || hos.zei_rtu || '％）' AS shohin_nm
    ,NULL AS shohin_tkiyo_nm
    ,NULL               AS  hoshi_disp　
    ,NULL               AS  zei_disp
    ,NULL               AS  juchu_suryo
    ,NULL               AS  uri_tnk
    ,T1.hoshi_prc       AS  uri_prc
    ,chu_bunrui.chu_bunrui_nm
    ,NULL               AS  tani_nm
    ,NULL               AS  koumoku
    ,NULL               AS  service_kbn
    ,T1.dai_bunrui_cd
    ,dai_bunrui.dai_bunrui_nm
    ,T1.siire_tnk
    ,T1.hachu_kbn
    ,T1.print_group_cd         --20151207追加
    ,T1.mitumori_print_seq     --20151207追加
    ,T1.gojokai_nebiki_prc     --20151207追加
    ,T1.nebiki_prc             --20151207追加
    ,NULL               AS n_free1
    ,NULL               AS out_zei_prc 
    ,NULL               AS in_zei_prc
    ,NULL AS reduced_tax_rate   -- 20190430 mihara keigen
    ,NULL               AS  gazo_img
    ,NULL AS upgrade_kbn
    ,NULL AS plan_shohin_cd           --アップデート元商品コード
    ,NULL AS plan_shohin_nm           --アップデート元商品名
    ,NULL AS denpyo_kbn_nm           
  FROM (
    SELECT
      h.data_kbn
     ,h.seko_no
     ,h.seko_no_sub
     ,d.chu_bunrui_cd
     ,d.dai_bunrui_cd
     ,d.hoshi_ritu_cd
     ,SUM(d.hoshi_prc) AS   hoshi_prc
     ,SUM( CASE WHEN d.hoshi_umu_kbn = 1 THEN d.uri_prc ELSE 0 END) AS uri_prc
     ,0 AS siire_tnk
     ,0 AS hachu_kbn
     ,0 AS print_group_cd        --20151207追加
     ,MAX(mitumori_print_seq) AS mitumori_print_seq     --20151207追加
     ,0 AS gojokai_nebiki_prc    --20151207追加
     ,0 AS nebiki_prc            --20151207追加
    FROM uriage_denpyo h
    INNER JOIN uriage_denpyo_msi d 
        ON h.uri_den_no = d.uri_den_no 
        AND d.delete_flg = 0
    INNER JOIN shohin_mst shohin 
        ON d.shohin_cd = shohin.shohin_cd 
        AND d.shohin_bumon_cd = shohin.bumon_cd
        AND shohin.hihyoji_kbn = 0
        AND shohin.delete_flg = 0
    INNER JOIN 
          shohin_bunrui_mst sbm
          ON  (
              d.dai_bunrui_cd = sbm.dai_bunrui_cd
          AND d.chu_bunrui_cd  = sbm.chu_bunrui_cd
          AND d.shohin_bumon_cd = sbm.bumon_cd
          AND d.shohin_kbn  = sbm.shohin_kbn
          AND d.shohin_cd  = sbm.shohin_cd
          AND sbm.delete_flg = 0
          )
    WHERE   h.delete_flg = 0
 --   AND     d.uri_prc <> 0
 --   AND     d.uri_tnk <> 0 
    GROUP BY h.data_kbn
        , h.seko_no
        , h.seko_no_sub
        , d.chu_bunrui_cd
        , d.dai_bunrui_cd
        , d.hoshi_ritu_cd
    HAVING SUM(d.hoshi_prc) <> 0
  ) T1
  LEFT JOIN hoshi_ritu_mst hos 
      ON    T1.hoshi_ritu_cd = hos.hoshi_ritu_cd
      AND   0                = hos.delete_flg       
  LEFT JOIN shohin_chu_bunrui_mst chu_bunrui 
      ON T1.chu_bunrui_cd = chu_bunrui.chu_bunrui_cd 
      AND chu_bunrui.delete_flg = 0
  LEFT JOIN shohin_dai_bunrui_mst dai_bunrui 
        ON T1.dai_bunrui_cd = dai_bunrui.dai_bunrui_cd
        AND dai_bunrui.delete_flg = 0
) T2
 WHERE $whereStr
 GROUP BY
     T2.record_kbn
    ,T2.data_kbn
    ,T2.seko_no
    ,T2.seko_no_sub
    ,T2.chu_bunrui_cd
    ,T2.shohin_nm
    ,T2.shohin_tkiyo_nm
    ,T2.uri_tnk
    ,T2.chu_bunrui_nm
    ,T2.tani_nm
    ,T2.koumoku
    ,T2.service_kbn
    ,T2.dai_bunrui_cd
    ,T2.dai_bunrui_nm
    ,T2.hoshi_disp
    ,T2.zei_disp
    ,T2.shohin_kbn
    ,T2.shohin_cd
    ,T2.siire_tnk
    ,T2.hachu_kbn
    ,T2.print_group_cd         --20151207追加
    ,T2.mitumori_print_seq     --20151207追加
    ,T2.gojokai_nebiki_prc     --20151207追加
    ,T2.nebiki_prc             --20151207追加    
    ,T2.n_free1
    ,T2.disp_no
    ,T2.denpyo_kbn
    ,T2.reduced_tax_rate        -- 20190430 mihara keigen
    ,T2.gazo_img
    ,T2.upgrade_kbn
    ,T2.plan_shohin_cd           --アップデート元商品コード
    ,T2.plan_shohin_nm           --アップデート元商品名
    ,T2.denpyo_kbn_nm          
) T3  
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 施行基本情報 データ 取得
     *
     * <AUTHOR> Sato
     * @since      2014/05/13
     * @param      Msi_Sys_Db $db
     * @param      string $seko_no
     * @return     array      該当データがない場合はnullを返す
     */
    public static function getSekoKihonInfo($db, $seko_no) {
        $select = $db->easySelOne(<<< END_OF_SQL
SELECT
    status_kbn
  FROM seko_kihon_info
 WHERE seko_no = '$seko_no' AND delete_flg = 0
END_OF_SQL
        );
        return $select;
    }

    /**
     * PDF出力 請求書(鑑) 取得
     *
     * <AUTHOR> Sai
     * @since      2015/01/28
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findKagami($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if ( strlen($tailClause) <= 0 ) {
            $tailClause = '';
        }
        if ( strlen($orderBy) <= 0 ) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect( <<< END_OF_SQL
SELECT
    *
FROM
    (
        SELECT
            sk.seko_no
            ,sk.moushi_kbn
            ,sk.souke_nm
            ,TO_CHAR (sn1.nitei_ymd ,'YYYY/MM/DD HH24:MI') AS kaso_date -- 火葬時間
            ,TO_CHAR (sn2.nitei_ymd ,'YYYY/MM/DD') AS tsuya_ymd -- 通夜時間
            ,TO_CHAR (sk.sougi_ymd ,'YYYY/MM/DD') AS sougi_ymd -- 告別式時間
            ,sk.k_knm -- 故人名カナ
            ,sk.k_nm -- 故人名
            ,sk.k_sex_cd -- 性別
            ,sk.k_nenrei_man -- 年齢（満）
            ,sk.k_nenrei_kyounen -- 年齢（享年）
            ,sk.k_gengo -- 故人生年月日元号
            ,sk.k_seinengappi_ymd -- 故人生年月日
            ,sk.kg_yubin_no -- 故人住所郵便番号
            ,sk.kg_addr1 -- 故人住所1
            ,sk.kg_addr2 -- 故人住所2
            ,sk.kg_tel -- 故人電話番号
            ,TO_CHAR (sn3.nitei_ymd ,'YYYY/MM/DD') AS sibou_ymd -- 死亡時間
            ,CASE
                WHEN sn3.basho_kbn = '0' THEN '自宅'
                ELSE sn3.basho_nm
            END sibou_basho -- 死亡場所
            ,t4.tanto_nm AS mitsu_tanto -- 見積担当者
            ,t3.tanto_nm AS seko_tanto -- 施行担当者
            ,CASE sk.iso_accept_kbn_1 WHEN 1 THEN tm1.tanto_nm WHEN 2 THEN sm1.siire_lnm ELSE iso1.kbn_value_lnm END iso_tanto_nm1 -- 移送担当者名1
            ,t5.tanto_nm AS iso_tanto_nm1_2 -- 移送担当者名1_2
            ,CASE sk.iso_accept_kbn_2 WHEN 1 THEN tm2.tanto_nm WHEN 2 THEN sm2.siire_lnm ELSE iso2.kbn_value_lnm END iso_tanto_nm2 -- 移送担当者名2
            ,t6.tanto_nm AS iso_tanto_nm2_2 -- 移送担当者名2_2
            ,m_knm -- 喪主名カナ
            ,m_nm -- 喪主名
            ,zk.kbn_value_lnm AS m_zoku_nm -- 喪主から見た続柄
            ,sk.mg_yubin_no -- 喪主住所郵便番号
            ,sk.mg_addr1 -- 喪主住所1
            ,sk.mg_addr2 -- 喪主住所2
            ,sk.mg_tel -- 喪主電話番号
            ,sk.mg_m_tel -- 喪主携帯番号
            ,mg_kbn -- 故人に同じ区分
            ,ssi.sekyu_nm -- 請求先名
            ,ssi.sekyu_knm -- 請求先名カナ
            ,CASE
                WHEN ssi.moshu_kankei_kbn = 1 THEN '本人'
                ELSE ssi.moshu_kankei
            END moshu_kankei
            ,ssi.yubin_no AS s_yubin_no -- 請求先郵便番号
            ,ssi.addr1 AS s_addr1 -- 請求先住所1
            ,ssi.addr2 AS s_addr2 -- 請求先住所2
            ,ssi.tel AS s_tel -- 請求先電話番号
            ,ssi.mobile_tel AS s_mobile_tel -- 請求先携帯番号
            ,sk.sekyu_kbn -- 請求先喪主に同じ
            ,sk.jyusho_nm AS t_nm -- 寺院名
            ,sk.jyusho_knm AS t_knm -- 寺院名カナ
            ,sk.syushi_kbn -- 宗旨区分
            ,sk.syuha_nm -- 宗派名
            ,sk.syuha_knm -- 宗派名カナ
            --,tera.jyusho_lknm AS t_knm -- 寺カナ
            --,tera.jyusho_lnm AS t_nm -- 寺
            ,tera.zip_no AS t_zip_no -- 寺住所郵便番号
            ,tera.addr1_nm AS t_addr1 -- 寺住所1
            ,tera.addr2_nm AS t_addr2 -- 寺住所2
            ,tera.tel AS t_tel -- 寺電話番号
            ,tera.fax AS t_fax -- 寺FAX番号
            ,tsk.kbn_value_lnm AS tera_shokai -- 寺紹介
            ,sn4.nitei_ymd AS kokubetsu_ymd
            ,sk.jichu_kakute_ymd
            ,CASE sn4.basho_kbn
                WHEN 0 THEN '自宅'              -- 0:自宅
                WHEN 1 THEN tera2.jyusho_lnm    -- 1:寺院
                WHEN 2 THEN km.kaijyo_lnm       -- 2:自営式場
                WHEN 9 THEN sn4.basho_nm        -- 9:その他
                WHEN 15 THEN kmt.kaijyo_lnm     -- 15:他営業式場
                ELSE ''
            END shikijyo -- 式場名
            ,CASE sn4.basho_kbn
                WHEN 0 THEN sk.kg_yubin_no
                WHEN 1 THEN tera2.zip_no
                WHEN 2 THEN km.zip_no
                WHEN 15 THEN kmt.zip_no
                ELSE ''
            END shikijyo_zip -- 式場郵便番号
            ,CASE sn4.basho_kbn
                WHEN 0 THEN sk.kg_addr1
                WHEN 1 THEN tera2.addr1_nm
                WHEN 2 THEN km.addr1_nm
                WHEN 15 THEN kmt.addr1_nm
                ELSE ''
            END shikijyo_addr1 -- 式場住所1
            ,CASE sn4.basho_kbn
                WHEN 0 THEN sk.kg_addr2
                WHEN 1 THEN tera2.addr2_nm
                WHEN 2 THEN km.addr2_nm
                WHEN 15 THEN kmt.addr2_nm
                ELSE ''
            END shikijyo_addr2 -- 式場住所2
            ,CASE sn4.basho_kbn
                WHEN 0 THEN sk.kg_tel
                WHEN 1 THEN tera2.tel
                WHEN 2 THEN km.tel
                WHEN 15 THEN kmt.tel
                ELSE '' -- 式場電話番号
            END shikijyo_tel
            ,CASE sn4.basho_kbn
                WHEN 0 THEN ''
                WHEN 1 THEN tera2.fax
                WHEN 2 THEN km.fax
                WHEN 15 THEN kmt.fax
                ELSE ''
            END shikijyo_fax -- 式場FAX
            ,sn1.basho_nm AS kasoba_nm -- 火葬場
            ,nnk.kbn_value_lnm AS nanoka -- 初七日
            ,kf.v_free26 AS todokede -- 届出役所
            ,kf.v_free12 AS keiyaku_nm -- 契約団体名1
            ,sk.free5_kbn
            ,sk.free6_kbn
            ,sk.v_free5
            ,sk.bumon_cd
            ,bm.bumon_lnm
            ,bm.zip_no AS bumon_zip_no
            ,bm.addr1_nm AS bumon_addr1_nm
            ,bm.addr2_nm AS bumon_addr2_nm
            ,bm.tel AS bumon_tel
            ,bm.fax AS bumon_fax
            ,bm_oya.zip_no AS kaisya_zip_no
            ,bm_oya.addr1_nm AS kaisya_addr1
            ,bm_oya.addr2_nm AS kaisya_addr2
            ,bm_oya.tel AS kaisya_tel
            ,bm_oya.f_free1 AS kaisya_logo
            ,sk.seko_plan_cd
            ,spm.seko_plan_nm
            ,spm.seko_prc
            ,spm.plan_summary_nm
            ,sk.om_plan_cd
            -- 法事
            ,skih.hk_nm -- 故人名
            ,skih.hkg_yubin_no -- 故人住所郵便番号
            ,skih.hkg_addr1 -- 故人住所1
            ,skih.hkg_addr2 -- 故人住所2
            ,skih.hkg_tel -- 故人電話番号
            ,skih.hk_nenrei_man -- 年齢（満）
            ,skih.hk_death_ymd -- 死亡日
            ,snh1.nitei_ymd AS houyou_ymd -- 法要日
            ,CASE snh1.basho_kbn
                WHEN 0 THEN '自宅'              -- 0:自宅
                WHEN 1 THEN h_tera.jyusho_lnm   -- 1:寺院
                WHEN 2 THEN h_km.kaijyo_lnm     -- 2:自営式場
                WHEN 9 THEN snh1.basho_nm       -- 9:その他
                WHEN 15 THEN h_kmt.kaijyo_lnm   -- 15:他営業式場
                ELSE ''
            END h_shikijyo -- 式場名
            ,CASE snh1.basho_kbn
                WHEN 0 THEN sk.kg_yubin_no
                WHEN 1 THEN h_tera.zip_no
                WHEN 2 THEN h_km.zip_no
                WHEN 15 THEN h_kmt.zip_no
                ELSE ''
            END h_shikijyo_zip -- 式場郵便番号
            ,CASE snh1.basho_kbn
                WHEN 0 THEN sk.kg_addr1
                WHEN 1 THEN h_tera.addr1_nm
                WHEN 2 THEN h_km.addr1_nm
                WHEN 15 THEN h_kmt.addr1_nm
                ELSE ''
            END h_shikijyo_addr1 -- 式場住所1
            ,CASE snh1.basho_kbn
                WHEN 0 THEN sk.kg_addr2
                WHEN 1 THEN h_tera.addr2_nm
                WHEN 2 THEN h_km.addr2_nm
                WHEN 15 THEN h_kmt.addr2_nm
                ELSE ''
            END h_shikijyo_addr2 -- 式場住所2
        FROM
            seko_kihon_info sk
            LEFT OUTER JOIN
                seko_nitei sn1
            ON  (
                    sk.seko_no = sn1.seko_no
                AND sn1.nitei_kbn = 6 -- 火葬
                AND sn1.delete_flg = 0
                )
            LEFT OUTER JOIN
                seko_nitei sn2
            ON  (
                    sk.seko_no = sn2.seko_no
                AND sn2.nitei_kbn = 4 -- 通夜
                AND sn2.delete_flg = 0
                )
            LEFT OUTER JOIN
                seko_nitei sn3
            ON  (
                    sk.seko_no = sn3.seko_no
                AND sn3.nitei_kbn = 1 -- 死亡日
                AND sn3.delete_flg = 0
                )
            LEFT OUTER JOIN
                code_nm_mst zk
            ON  (
                    sk.m_zoku_kbn2 = zk.kbn_value_cd_num
                AND zk.code_kbn = '0190' -- 続柄名
                AND zk.delete_flg = 0
                )
            LEFT OUTER JOIN
                nm_jyusho_mst tera
            ON  (
                    sk.jyusho_cd = tera.jyusho_cd
                AND tera.jyusho_kbn = '1' -- 寺院
                AND tera.delete_flg = 0
                )
            LEFT OUTER JOIN
                tanto_mst tm1
            ON  (
                    sk.iso_tanto_cd1 = tm1.tanto_cd
                AND tm1.delete_flg = 0
                )
            LEFT OUTER JOIN
                tanto_mst tm2
            ON  (
                    sk.iso_tanto_cd2 = tm2.tanto_cd
                AND tm2.delete_flg = 0
                )
            LEFT OUTER JOIN
                sekyu_saki_info ssi
            ON  (
                    sk.seko_no = ssi.seko_no
                AND sk.sekyu_cd = ssi.sekyu_cd
                AND ssi.delete_flg = 0
                )
            LEFT OUTER JOIN
                code_nm_mst tsk
            ON  (
                    sk.free1_kbn = tsk.kbn_value_cd_num
                AND tsk.code_kbn = '1830' -- 菩提寺紹介者
                AND tsk.delete_flg = 0
                )
            LEFT OUTER JOIN
                seko_nitei sn4
            ON  (
                    sk.seko_no = sn4.seko_no
                AND sn4.nitei_kbn = 11 -- 告別式
                AND sn4.delete_flg = 0
                )
            LEFT OUTER JOIN
                nm_jyusho_mst tera2
            ON  (
                    sn4.basho_cd = tera2.jyusho_cd
                AND tera2.jyusho_kbn = '1' -- 1 寺院
                AND sn4.basho_kbn = 1 -- 1 寺院
                AND tera2.delete_flg = 0
                )
            LEFT OUTER JOIN -- 自営式場
                kaijyo_mst km
            ON  (
                    sn4.basho_cd = km.kaijyo_cd
                AND km.kaijyo_kbn = '1' -- 1 式場
                AND sn4.basho_kbn = 2 -- 2 自社会館
                AND km.delete_flg = 0
                )
            LEFT OUTER JOIN -- 他営式場
                kaijyo_mst kmt
            ON  (
                    sn4.basho_cd = kmt.kaijyo_cd
                AND kmt.kaijyo_kbn = '2' -- 他式場
                AND sn4.basho_kbn = 15 -- 他営式場
                AND kmt.delete_flg = 0
                )
            LEFT OUTER JOIN
                seko_nitei sn5
            ON  (
                    sk.seko_no = sn5.seko_no
                AND sn5.nitei_kbn = 8 -- 初七日
                AND sn5.delete_flg = 0
                )
            LEFT OUTER JOIN
                code_nm_mst nnk
            ON  (
                    sn5.free2_kbn = nnk.kbn_value_cd_num
                AND nnk.code_kbn = '1820' -- 初七日
                AND nnk.delete_flg = 0
                )
            LEFT OUTER JOIN
                seko_nitei_houji snh1
            ON  (
                    sk.seko_no = snh1.seko_no
                AND snh1.nitei_kbn = 1 -- ご法要
                AND snh1.delete_flg = 0
                )
            LEFT OUTER JOIN
                nm_jyusho_mst h_tera
            ON  (
                    snh1.basho_cd = h_tera.jyusho_cd
                AND h_tera.jyusho_kbn = '1' -- 1 寺院
                AND snh1.basho_kbn = 1 -- 1 寺院
                AND h_tera.delete_flg = 0
                )
            LEFT OUTER JOIN -- 自営式場
                kaijyo_mst h_km
            ON  (
                    snh1.basho_cd = h_km.kaijyo_cd
                AND h_km.kaijyo_kbn = '1' -- 1 式場
                AND snh1.basho_kbn = 2 -- 2 自社会館
                AND h_km.delete_flg = 0
                )
            LEFT OUTER JOIN -- 他営式場
                kaijyo_mst h_kmt
            ON  (
                    snh1.basho_cd = h_kmt.kaijyo_cd
                AND h_kmt.kaijyo_kbn = '2' -- 他式場
                AND snh1.basho_kbn = 15 -- 他営式場
                AND h_kmt.delete_flg = 0
                )
            LEFT OUTER JOIN kaisya_info ki			
                 ON ki.delete_flg = 0
            LEFT OUTER JOIN siire_mst sm1
            ON  (
                    sk.iso_siire_cd1 = sm1.siire_cd
                AND sm1.transfer_kbn = 1
                AND sm1.delete_flg = 0
                )
            LEFT OUTER JOIN siire_mst sm2
            ON  (
                    sk.iso_siire_cd2 = sm2.siire_cd
                AND sm2.transfer_kbn = 1
                AND sm2.delete_flg = 0
                )
            LEFT OUTER JOIN seko_kihon_all_free kf
            ON  (
                    sk.seko_no = kf.seko_no
                AND kf.delete_flg = 0
                )
           LEFT OUTER JOIN tanto_mst t3
            ON  (
                    sk.seko_tanto_cd = t3.tanto_cd
                AND t3.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t4
            ON  (
                    kf.tanto_cd1 = t4.tanto_cd
                AND t4.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t5
            ON  (
                    kf.tanto_cd2 = t5.tanto_cd
                AND t5.delete_flg = 0
                )
            LEFT OUTER JOIN tanto_mst t6
            ON  (
                    kf.tanto_cd3 = t6.tanto_cd
                AND t6.delete_flg = 0
                )
            LEFT OUTER JOIN
                code_nm_mst iso1
            ON  (
                    sk.iso_accept_kbn_1 = iso1.kbn_value_cd_num
                AND iso1.code_kbn = '1930' -- 移送者区分
                AND iso1.delete_flg = 0
                )
            LEFT OUTER JOIN
                code_nm_mst iso2
            ON  (
                    sk.iso_accept_kbn_2 = iso2.kbn_value_cd_num
                AND iso2.code_kbn = '1930' -- 移送者区分
                AND iso2.delete_flg = 0
                )
            LEFT OUTER JOIN
                bumon_mst bm
            ON  (
                    sk.bumon_cd = bm.bumon_cd
                AND bm.delete_flg = 0
                )
            LEFT OUTER JOIN
                bumon_kaso_mst bkm
            ON  (
                    sk.bumon_cd = bkm.ko_bumon_cd
                AND bkm.delete_flg = 0
                )
            LEFT OUTER JOIN
                bumon_mst bm_oya
            ON  (
                    bkm.oya_bumon_cd = bm_oya.bumon_cd
                AND bm_oya.delete_flg = 0
                )
            LEFT OUTER JOIN
                seko_plan_mst spm
            ON  (
                    spm.seko_plan_cd = sk.seko_plan_cd
                AND spm.delete_flg = 0
                )
            LEFT OUTER JOIN seko_kojin_info_houji skih
            ON  (
                    sk.seko_no = skih.seko_no
                AND skih.seq_no = 1
                AND kf.delete_flg = 0
                )
    ) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
        , $param );

        return $select;
    }

    /**
     * PDF出力 請求書(搬送業務) 取得
     *
     * <AUTHOR> Kobayashi
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findHanso($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.seko_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
SELECT
     sd.seikyu_den_no
    ,sd.keijo_ymd                       -- 売上計上日
    ,CONCAT(sii.k_l_nm, ' ', sii.k_f_nm) AS k_nm -- 故人名
    ,hd.sekyu_distance                  -- 請求メーター値(請求)
    ,'km' AS tani                       -- 単位
    ,sd.uri_prc_sum                     -- 単価
    ,sd.uri_prc_sum                     -- 金額
    ,sd.seko_no                         -- 施行番号
    ,sd.bun_gas_kbn_num                 -- 分割合算区分
    ,sd.bun_gas_seikyu_den_no           -- 分割合算請求伝票No
  FROM seikyu_denpyo sd
  LEFT JOIN syutsudo_irai_info sii
      ON sii.uketsuke_no = sd.seko_no
      AND sii.delete_flg = 0
  LEFT JOIN hanso_denpyo hd
      ON hd.uketsuke_no = sd.seko_no
      AND hd.hanso_kbn = '1'
      AND hd.delete_flg = 0
 WHERE sd.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }
    
    /**
     * 契約先振込口座情報 取得
     *
     * <AUTHOR> Kobayashi
     * @since      2020/xx/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $keyHash  条件
     * @return     array      該当データがない場合はarray()を返す
     */
    public static function findKeiyakusakiBankInfo($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');

        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {
            $orderBy = ' ORDER BY T.koza_no ';
        }

        $select = $db->easySelect(<<< END_OF_SQL
SELECT *
  FROM (
SELECT
    km.partner_cd         -- 契約先コード
    ,km.koza_no           -- 口座番号
    ,km.bank_cd           -- 銀行コード
    ,bnm.bank_nm          -- 銀行名
    ,km.shiten_cd         -- 支店コード
    ,snm.shiten_nm        -- 支店名
    ,km.yokin_sbt         -- 預金種別コード
    ,yokin.kbn_value_lnm AS yokin_sbt_nm -- 預金種別名
  FROM keiyakusaki_mst km
  LEFT JOIN bank_nm_mst bnm 
        ON  bnm.bank_cd = km.bank_cd
        AND bnm.delete_flg = 0
  LEFT JOIN shiten_nm_mst snm 
        ON  snm.bank_cd = km.bank_cd
        AND snm.shiten_cd = km.shiten_cd
        AND snm.delete_flg = 0
  LEFT JOIN code_nm_mst yokin 
        ON  yokin.code_kbn = '0830' 
        AND yokin.kbn_value_cd_num = km.yokin_sbt
        AND yokin.delete_flg = 0
 WHERE km.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);

        return $select;
    }

    /**
     * 見積履歴番号取得処理
     *
     * <AUTHOR> Sai
     * @param Msi_Sys_Db $db db
     * @param string $seko_no 施行番号
     * @since 2014/06/08
     */
    public static function getDenpyoRirekiNo($db, $seko_no) {
        $history_no = 0;
        $select = $db->easySelOne("
            SELECT 
                COALESCE(MAX(history_no), 0) AS history_no 
            FROM seikyu_denpyo_history 
            WHERE seko_no = :seko_no
            AND data_kbn = 1", array('seko_no' => $seko_no));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $history_no = $select['history_no'];
        }
        return $history_no;
    }

    /**
     * サービス区分がnullの場合、互助会値引きマスタを見て再設定する
     *
     * <AUTHOR> Sai
     * @param array $dataCol 明細データ
     * @param string $gojokaiKbn 互助会区分
     * @since 2015/08/19
     */
    public static function adjMeisaiData(&$dataCol, $gojokaiKbn, $keyMapperP = null) {
        $keyMapper = array('service_kbn' => 'service_kbn');

        if (is_array($keyMapperP)) {
            $keyMapper = array_merge($keyMapper, $keyMapperP);
        }
        $service_kbn_key = $keyMapper['service_kbn'];
        $db = Msi_Sys_DbManager::getMyDb();
        $gnmst = $db->easySelect("
            SELECT 
                *
            FROM 
                gojokai_nebiki_mst 
            WHERE 
                gojokai_kbn = :gojokai_kbn
            AND CURRENT_DATE BETWEEN tekiyo_st_date AND tekiyo_ed_date
            AND delete_flg = 0", array('gojokai_kbn' => $gojokaiKbn));

        foreach ($dataCol as &$value) {
            if (empty($value[$service_kbn_key]) || $value[$service_kbn_key] == '0') {
                $value[$service_kbn_key] = static::getServiceKbn($dataCol, $value, $gnmst);
            }
        }
    }

    /**
     * 互助会値引きマスタよりサービス区分を求める
     *
     * <AUTHOR> Sai
     * @param array $dataCol 明細データ
     * @param array $row 明細一行データ
     * @param array $gnmst 互助会値引きマスタ
     * @since 2015/08/19
     */
    public static function getServiceKbn($dataCol, $row, $gnmst) {
        $serviceKbn = '0';
        foreach ($gnmst as $mstone) {
            // 互助会商品の場合
            if ($row['shohin_cd'] == $mstone['shohin_cd']) {
                $serviceKbn = '1'; // 互助会商品
                break;
            }
            // 互助会値引き商品の場合
            if ($row['dai_bunrui_cd'] == $mstone['tg_dai_bunrui_cd'] && $row['chu_bunrui_cd'] == $mstone['tg_chu_bunrui_cd'] && $row['shohin_kbn'] == $mstone['tg_shohin_kbn'] && $row['shohin_cd'] == $mstone['tg_shohin_cd']) {
                if (static::verifyMsiData($mstone['shohin_cd'], $dataCol)) {
                    $serviceKbn = '2'; // 互助会値引き商品
                    break;
                }
            }
        }
        return $serviceKbn;
    }

    /**
     * 互助会値引きデータかどうかの整合性チェックを行う
     *
     * <AUTHOR> Sai
     * @param string $shohin_cd 商品コード
     * @param array $dataCol 明細データ
     * @since 2015/08/19
     */
    public static function verifyMsiData($shohin_cd, $dataCol) {
        $exist = false;
        foreach ($dataCol as $value) {
            if ($value['shohin_cd'] == $shohin_cd) {
                $exist = true;
                break;
            }
        }
        return $exist;
    }

    /**
     * 互助会区分取得処理
     *
     * <AUTHOR> Sai
     * @param string $seko_no 施行番号
     * @since 2015/08/19
     */
    public static function getGojokaiKbn($seko_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $gojokai_kbn = null;
        $select = $db->easySelOne("
            SELECT 
                gojokai_kbn 
            FROM seko_kihon_info 
            WHERE seko_no = :seko_no
            ", array('seko_no' => $seko_no));
        if (Msi_Sys_Utils::myCount($select) > 0) {
            $gojokai_kbn = $select['gojokai_kbn'];
        }
        return $gojokai_kbn;
    }

    /**
     * 互助会番号取得処理
     *
     * <AUTHOR> Sai
     * @param string $seko_no 施行番号
     * @since 2015/10/27
     */
    public static function getGojokaiNo($seko_no) {
        $db = Msi_Sys_DbManager::getMyDb();
        $select = $db->easySelect("
            SELECT 
                sgm.kain_no
                ,sgm.apply_no
                ,sgm.kain_no || '-'  || apply_no AS k_no
                ,sgm.kanyu_nm
                ,sgm.course_snm_cd
                ,gcm.harai_yotei_cnt
                ,gcm.harai_yotei_cnt - sgm.harai_no AS harai_zan
            FROM seko_gojokai_member sgm
            INNER JOIN gojokai_couse_mst gcm
            ON(     sgm.course_snm_cd = gcm.gojokai_cose_iw
                 AND CURRENT_DATE BETWEEN gcm.tekiyo_st_date AND gcm.tekiyo_ed_date)
            WHERE sgm.seko_no = :seko_no
            AND sgm.yoto_kbn IN (1, 10, 11, 12)
            ORDER BY 
                sgm.kain_no
                ,sgm.apply_no
            ", array('seko_no' => $seko_no));
        return $select;
    }

    /**
     * 初七日を 取得
     *
     * <AUTHOR> Sai
     * @since      2015/12/28
     * @param      Msi_Sys_Db $db
     * @param      string      $seko_no  施行番号
     * @return     初七日
     */
    public static function findSyonanoka($db, $seko_no) {
        $retNanoka = '';
        //初七日
        $recNitei = DataMapper_Pdf0602::find_nittei($db, array("seko_no" => $seko_no, "nitei_kbn" => 8));
        if (Msi_Sys_Utils::myCount($recNitei) > 0) {
            $selCodeNm = DataMapper_CodeNmMst::find($db, array('code_kbn' => '1820'));
            $v_free3 = $recNitei[0]['v_free3'];
            $nanokas = explode(',', $v_free3);
            foreach ($nanokas as $nanoka) {
                foreach ($selCodeNm as $cdnm) {
                    if ($nanoka == $cdnm['kbn_value_cd_num']) {
                        $retNanoka .= ' ' . $cdnm['kbn_value_lnm'];
                        break;
                    }
                }
            }
        }
        return $retNanoka;
    }

    /**
     * 供花データを別配列に分ける処理
     *
     * <AUTHOR> Sai
     * @param array $dataCol 明細データ
     * @param array $kyoka 供花
     * @since 2016/04/11
     */
    public static function adjKyoukaData(&$dataCol, &$kyoka) {
        foreach ($dataCol as $key => $value) {
            if ($value['print_group_cd'] == '99') { // 供花・供物
                $kyoka['price'] += $value['uri_prc'];
                $kyoka['nebiki_prc'] += $value['nebiki_prc'];
                $kyoka['gojokai_nebiki_prc'] += $value['gojokai_nebiki_prc'];
                $kyoka['zei'] += $value['out_zei_prc'] + $value['in_zei_prc'];
                unset($dataCol[$key]);
            }
        }
    }

    /**
     * 奉仕料取得処理
     *
     * <AUTHOR> Sai
     * @param  Msi_Sys_Db $db	データベース
     * @param string $seikyu_den_no 請求伝票番号
     * @since 2021/09/xx
     */
    public static function getHoshiryo($db, $seikyu_den_no) {
        $select = $db->easySelect("
            SELECT 
                seikyu_den_no
                ,dai_bunrui_cd
                ,zei_kbn
                ,zei_cd
                ,SUM(hoshi_prc) AS hoshi_prc
            FROM 
                seikyu_denpyo_msi 
            WHERE 
                    seikyu_den_no = :seikyu_den_no
                AND hoshi_prc <> 0
                AND delete_flg = 0
            GROUP BY
                seikyu_den_no
                ,dai_bunrui_cd
                ,zei_kbn
                ,zei_cd
            ", array('seikyu_den_no' => $seikyu_den_no));
        return $select;
    }

    /**
     * 奉仕料取得処理
     *
     * <AUTHOR> Sai
     * @param  Msi_Sys_Db $db	データベース
     * @param string $seikyu_den_no 請求伝票番号
     * @since 2021/09/xx
     */
    public static function getHoshiryoForTemp($db, $seikyu_den_no) {
        $select = $db->easySelect("
            SELECT 
                temp_seikyu_den_no AS seikyu_den_no
                ,dai_bunrui_cd
                ,zei_kbn
                ,zei_cd
                ,SUM(hoshi_prc) AS hoshi_prc
            FROM 
                temp_seikyu_denpyo_msi 
            WHERE 
                    temp_seikyu_den_no = :temp_seikyu_den_no
                AND hoshi_prc <> 0
                AND delete_flg = 0
            GROUP BY
                temp_seikyu_den_no
                ,dai_bunrui_cd
                ,zei_kbn
                ,zei_cd
            ", array('temp_seikyu_den_no' => $seikyu_den_no));
        return $select;
    }
    /**
     * 振込口座情報取得
     *
     * <AUTHOR> Kobayashi
     * @param  Msi_Sys_Db $db	データベース
     * @param string $bumon_cd 部門コード
     * @since 2023/05/12
     */
    public static function getBankInfo($db, $bumon_cd) {
        $select = $db->easySelect("
            SELECT 
                bm.*
                ,cm1980.kbn_value_snm AS yokin_sbt_nm -- 預金種別名
            FROM 
                br_koza_kanri_mst bm
            LEFT JOIN code_nm_mst cm1980
                ON cm1980.code_kbn = '1980'
                AND cm1980.kbn_value_cd_num = bm.yokin_sbt
                AND cm1980.delete_flg = 0
            WHERE 
                    bm.bumon_cd = :bumon_cd
                AND bm.delete_flg = 0
            ORDER BY
                bm.transfer_bank_cd
            ", array('bumon_cd' => $bumon_cd));
        return $select;
    }
    /**
     * 振込口座情報取得2(msi_noから取得)
     *
     * <AUTHOR> Kobayashi
     * @param  Msi_Sys_Db $db	データベース
     * @param string $msi_no 明細No
     * @since 2025/05/27
     */
    public static function getBankInfo2($db, $msi_no) {
        $select = $db->easySelect("
            SELECT 
                bm.*
                ,cm1980.kbn_value_snm AS yokin_sbt_nm -- 預金種別名
            FROM 
                br_koza_kanri_mst bm
            LEFT JOIN code_nm_mst cm1980
                ON cm1980.code_kbn = '1980'
                AND cm1980.kbn_value_cd_num = bm.yokin_sbt
                AND cm1980.delete_flg = 0
            WHERE 
                    bm.msi_no = :msi_no
                AND bm.delete_flg = 0
            ORDER BY
                bm.transfer_bank_cd
            ", array('msi_no' => $msi_no));
        return $select;
    }
}
