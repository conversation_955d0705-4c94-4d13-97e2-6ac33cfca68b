CREATE TABLE IF NOT EXISTS public.TAIRYU_SAIKEN_KANRI_INFO (
    seikyu_den_no                   CHAR(10)                                              NOT NULL,
    seq_no                          NUMERIC(4,0)                    DEFAULT 0             NOT NULL,
    discuss_date                    DATE                                                  ,
    contents                        TEXT                                                  ,
    seikyu_tanto_cd                 VARCHAR(10)                                           ,
    kaishu_prc                      NUMERIC(10,0)                                         ,
    seko_prc_zan                    NUMERIC(10,0)                                         ,
    kaihi_zan                       NUMERIC(10,0)                                         ,
    delete_flg                      NUMERIC(1,0)                    DEFAULT 0             NOT NULL,
    _req_id                         BIGINT                                                NOT NULL,
    _cre_user                       VARCHAR(64)                                           NOT NULL,
    _cre_ts                         TIMESTAMPTZ                     NOT NULL,
    _mod_user                       VARCHAR(64)                                           NOT NULL,
    _mod_ts                         TIMESTAMPTZ                                           NOT NULL,
    _mod_cnt                        BIGINT                          DEFAULT 0             NOT NULL,

    PRIMARY KEY ( seikyu_den_no,seq_no )
            USING INDEX TABLESPACE pg_default
) TABLESPACE pg_default;;


CREATE TRIGGER trg__stamp BEFORE INSERT OR UPDATE OR DELETE ON TAIRYU_SAIKEN_KANRI_INFO
    FOR EACH ROW EXECUTE PROCEDURE x_stamp_func();

COMMENT ON TABLE public.TAIRYU_SAIKEN_KANRI_INFO IS '滞留債権管理履歴';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO.seikyu_den_no IS '請求伝票No.';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO.seq_no IS '連番';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO.discuss_date IS '確認日付';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO.contents IS '内容';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO.seikyu_tanto_cd IS '請求対策者';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO.kaishu_prc IS '回収金額';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO.seko_prc_zan IS '施行代金残';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO.kaihi_zan IS '会費残';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO.delete_flg IS '削除フラグ';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO._req_id IS '処理要求ID';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO._cre_user IS '作成者';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO._cre_ts IS '作成日時';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO._mod_user IS '最終更新者';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO._mod_ts IS '最終更新日時';
COMMENT ON COLUMN public.TAIRYU_SAIKEN_KANRI_INFO._mod_cnt IS '更新回数';
