<?php /* Smarty version 2.6.28, created on 2025-06-05 11:48:36
         compiled from file:system/skaisya/main_tab_zenpan.tpl */ ?>
<div class="tab-contents-main">
  <fieldset class="base_1">
    <label for="kaisya_snm" class="requireXX">簡略会社名</label>
    <input name="kaisya_snm" id="kaisya_snm" type="text" class="txt ime-off" value="" maxlength = "15" 
           style="width:75%"
           placeholder=""/>
  </fieldset>
  <fieldset class="base_1">
    <label for="kaisya_lknm" class="requireXX">正式カナ名</label>
    <input name="kaisya_lknm" id="kaisya_lknm" type="text" class="txt ime-off" value="" maxlength = "60" 
           style="width:75%"
           placeholder=""/>
  </fieldset>
  <fieldset class="base_1">
    <label for="kaisya_sknm" class="requireXX">簡略カナ名</label>
    <input name="kaisya_sknm" id="kaisya_sknm" type="text" class="txt ime-off" value="" maxlength = "15" 
           style="width:75%"
           placeholder=""/>
  </fieldset>

  <fieldset class="base_1">
    <label for="st_date" class="">利用開始日</label>
    <input name="st_date" id="st_date" type="text" class="txt ime-off my-type-date with-dlg" style="width:23%" value="" />
    <div class="label dlg_date"></div>
    <label for="ed_date" class="">利用終了日</label>
    <input name="ed_date" id="ed_date" type="text" class="txt ime-off my-type-date with-dlg" style="width:22%" value="" />
    <div class="label dlg_date"></div>
  </fieldset>

  <fieldset class="base_1">
    <label for="raisensu_su" class="">ライセンス数</label>
    <input name="raisensu_su" id="raisensu_su" type="text" class="txt" style="width:28%;text-align:right" value="" />
  </fieldset>

  <fieldset class="base_1">
    <label for="cstm_key" class="">カスタマイズキー</label>
    <input name="cstm_key" id="cstm_key" type="text" class="txt" style="width:75%" value="" />
  </fieldset>


  <fieldset class="base_1">
    <label for="is_databackup" class="">DBバックアップ対象</label>
    <input type="hidden" name="is_databackup" id="is_databackup" class="is_databackup-cls sel2-picker" style="width:28%"/>
    <label for="no_del_flg" class="">削除不可フラグ</label>
    <input type="hidden" name="no_del_flg" id="no_del_flg" class="no_del_flg-cls sel2-picker" style="width:27%"/>
  </fieldset>

  <fieldset class="base_1">
    <label for="sa_or_cc" class="" title="発信IPアドレスチェックがNGならClient認証を行う">発信IPチェック代替</label>
    <input type="hidden" name="sa_or_cc" id="sa_or_cc" class="sa_or_cc-cls sel2-picker" style="width:28%"/>
    <label for="auth_type" class="">認証タイプ</label>
    <input type="hidden" name="auth_type" id="auth_type" class="auth_type-cls sel2-picker" style="width:27%"/>
  </fieldset>


  <fieldset class="base_1">
    <label for="status" class="">状態</label>
    <input name="status" id="status" type="text" class="txt" value="" title="変更できません" style="width:28%" readonly />
  </fieldset>

  <fieldset class="memo" style="margin-top:5px">
    <label for="biko" class="lbl_biko">メモ</label>
    <textarea name="biko" id="biko" class="txt" cols="100" rows="10"></textarea>
  </fieldset>
</div><!-- /#input-tab-main -->
