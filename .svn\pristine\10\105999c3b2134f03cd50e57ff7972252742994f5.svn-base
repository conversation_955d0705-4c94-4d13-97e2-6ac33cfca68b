<?php
  /**
   * PDF 請求書
   *
   * @category   App
   * @package    controller
   * <AUTHOR> Sato
   * @since      2014/02/24
   * @version    2017/04/17 Matsuyama 法事請求書の税抜・税込小計の印字を修正
   * @version   ページ数表示不具合（"n/m"の表示が正しくない）対応（法事、単品、別注品）
   * @version    2019/10/xx mihara 軽減税率対応  -49 は葬儀見積書鑑の見積金額を左に寄せた分の調整.
   * @filesource 
   */

  /**
   * PDF 請求書
   *
   * @category   App
   * @package    controller
   * <AUTHOR> Sato
   * @since      2014/02/24
   */
class Saiken_Pdf1101Controller extends Zend_Controller_Action
{
	// ファイル出力形式
	const OUTTYPE_MULTIFILE = 0;	// 異なる請求書を別ファイルとする
	const OUTTYPE_SINGLEFILE = 1;	// 全ての請求書を１ファイルとする

	private static $title = '請求書';
//    private static $sourceFileName = array('', 'pdf_tmpl/1101_01.pdf','pdf_tmpl/1101_01.pdf','pdf_tmpl/1101_03.pdf','pdf_tmpl/1101_03.pdf');
//    private static $sourceFileNameDetail = array('', 'pdf_tmpl/1101_01D.pdf','pdf_tmpl/1101_01D.pdf','pdf_tmpl/1101_03D.pdf','pdf_tmpl/1101_03D.pdf');
    private static $sourceFileName = array('', 'pdf_tmpl/1101_01.pdf','pdf_tmpl/1101_01_other.pdf','pdf_tmpl/1101_03.pdf','pdf_tmpl/1101_03.pdf','','pdf_tmpl/1101_01.pdf');
    private static $sourceFileNameDetail = array('', 'pdf_tmpl/1101_01D.pdf','pdf_tmpl/1101_01D_other.pdf','pdf_tmpl/1101_03D.pdf','pdf_tmpl/1101_03D.pdf','','pdf_tmpl/1101_01D.pdf');
    private static $sourceFileHyosi = 'pdf_tmpl/1101_H.pdf';
    private static $sourceFileGuide = 'pdf_tmpl/1101_G.pdf';
    private static $sourceFileHenpin = 'pdf_tmpl/1101_01_henpin.pdf';
    private static $sourceFileSaiji= 'pdf_tmpl/1101_01_other2.pdf';
    private static $color = array(84, 130, 53);
    
    private static $furikomi_str = "*振込名義人様（お名前）の後にお客様番号をご記入（ご入力）お願い致します";

    const CHU_BUNRUI_CD_A = '0020';
    const CHU_BUNRUI_CD_B = '0030';
    const CHU_BUNRUI_CD_PLAN = '0040';
    const CHU_BUNRUI_CD_OKUNI = '0050';
    const CHU_BUNRUI_CD_C = '0060';
    const CHU_BUNRUI_CD_D = '0070';
    const CHU_BUNRUI_CD_E = '0080';
    const CHU_BUNRUI_CD_F = '0100';
    const CHU_BUNRUI_CD_G = '0090';
    const CHU_BUNRUI_CD_H = '0120';
    const ADD_KBN_GOJYOKAITOKUTEN = 8;
    const SERVICE_KBN_GOJYOKAI = 1;
    const SERVICE_KBN_GOJYOKAI_NEBIKI = 2;

    /** 大分類コード：0005=>プラン　中分類コード0050プランを　大分類0005とみなす（SQLで処理済） */
    const DAI_BUNRUI_P = "0005";
    /** 大分類コード：0010=>葬送儀礼 */
    const DAI_BUNRUI_1 = "0010"; 
    /** 大分類コード：0020=>会葬返礼 */
    const DAI_BUNRUI_2 = "0020"; 
    /** 大分類コード：0030=>料理 */
    const DAI_BUNRUI_3 = "0030"; 
    /** 大分類コード：0050=>別途費用 */
    const DAI_BUNRUI_5 = "0050"; 
    /** 大分類コード：0060=>立替費用 */
    const DAI_BUNRUI_6 = "0060"; 
    /** 大分類コード：0070=>値引 */
    const DAI_BUNRUI_7 = "0070"; 
    
    const SUMID_KEIYAKUGAKU = 'keiyaku_gaku';

    const SUMID_A_PLAN = 'a_plan';
    const SUMID_A_GOJOKAI = 'a_gojokai';
    const SUMID_A_GOJOKAINEBIKI = 'a_gojokai_nebiki';
    const SUMID_A_NEBIKI_PRC = 'a_nebiki';
    const SUMID_A_GOJOKAIGAI = 'a_gojokai_gai';
    const SUMID_AZUKARI_DAN = 'azukari_dan';
    const SUMID_AZUKARI_HEN = 'azukari_hen';
    const SUMID_MEIGICGCOST = 'mg_chg_cost';
    const SUMID_EARLYUSE = 'early_use';
    const SUMID_EARLYUSEZEI = 'early_use_zei';
    const SUMID_COSECHGGAKU = 'cose_chg_gaku';
    const SUMID_GOJOHARAI = 'gojo_harai';
    const SUMID_ZENNOWARI = 'zen_wari';
    const SUMID_KAKEZEISAGAKU = 'kake_zei_sagaku';
    const SUMID_KAKEZAN = 'zan_gaku';
    const SUMID_KAKEZANZEI = 'zan_gaku_zei';

    private static $sum_name = array(
        self::SUMID_A_PLAN => '葬送儀礼',
        self::SUMID_A_GOJOKAI => '互助会品目合計',
        self::SUMID_A_GOJOKAINEBIKI => '会員値引き',
        self::SUMID_A_NEBIKI_PRC => '割引金額',
//        self::SUMID_A_GOJOKAINEBIKI => '互助会会員値引き',
        self::SUMID_A_GOJOKAIGAI => '互助会外プラン品目合計',
        self::SUMID_AZUKARI_DAN => '預り金（壇払い）',
        self::SUMID_AZUKARI_HEN => '預り金（返礼品）',
        self::SUMID_MEIGICGCOST => '名義変更手数料',
        self::SUMID_EARLYUSE => '早期利用費',
        self::SUMID_EARLYUSEZEI => '早期利用費消費税',
        self::SUMID_KAKEZAN => '会費残額',
        self::SUMID_KAKEZANZEI => '会費消費税',
        self::SUMID_COSECHGGAKU => 'コース変更差額',
        self::SUMID_GOJOHARAI => '互助会払込金額',
        self::SUMID_ZENNOWARI => '前納割引',
        self::SUMID_KAKEZEISAGAKU => '掛金消費税差額'
    );

	private static $msi_name_gojokai = 'A-1（互助会）';
	private static $msi_name_gojokaitokuten = '互助会会員特典';
	private static $msi_name_gojokaigai = 'A-2（プラン選択商品）';

        /** 申込区分 1：葬儀 2：法事 5：生前依頼 6：その他依頼*/
        private $_moushiKbn = null;
        
    /** 中分類コード */
    const CHU_BUNRUI_CD_JIGO       = '1000'; // 事後
    const CHU_BUNRUI_CD_IHAI       = '1010'; // 位牌
    const CHU_BUNRUI_CD_BUTSUDAN   = '1020'; // 仏壇
    const CHU_BUNRUI_CD_BOSHO      = '1030'; // 墓所・手元供養
    const CHU_BUNRUI_CD_ANNAI      = '1040'; // 案内状
    const CHU_BUNRUI_CD_SAIDAN     = '1050'; // 祭壇・式場
    const CHU_BUNRUI_CD_05BOSHO    = '1060'; // 05墓所・手元供養
    const CHU_BUNRUI_CD_HOUYOU     = '1070'; // 法要
    const CHU_BUNRUI_CD_HAKAMAIRI  = '1080'; // 墓参り
    const CHU_BUNRUI_CD_HENREI     = '1090'; // 返礼品
    const CHU_BUNRUI_CD_HOUJI      = '1110'; // 法事・催事
    const CHU_BUNRUI_CD_KAISHOKU   = '1120'; // 会食
    const CHU_BUNRUI_CD_HIKAZEI    = '1130'; // 非課税
    const CHU_BUNRUI_CD_BETTOHIYOU = '1140'; // 別途費用 
    const CHU_BUNRUI_CD_NEBIKI     = '1150'; // 値引き
    const CHU_BUNRUI_CD_NIIBON_1   = '2070'; // 
    const CHU_BUNRUI_CD_NIIBON_3   = '2080'; // 
    const CHU_BUNRUI_CD_NIIBON_2   = '2090'; // 
    const CHU_BUNRUI_CD_NIIBON_4   = '2130'; // 
    const CHU_BUNRUI_CD_NIIBON_5   = '2140'; // 
    const CHU_BUNRUI_CD_NIIBON_6   = '2150'; // 
    
    private static $houyou_hakamairi = [
         self::CHU_BUNRUI_CD_JIGO
        ,self::CHU_BUNRUI_CD_IHAI
        ,self::CHU_BUNRUI_CD_BUTSUDAN
        ,self::CHU_BUNRUI_CD_BOSHO
        ,self::CHU_BUNRUI_CD_ANNAI
        ,self::CHU_BUNRUI_CD_SAIDAN
        ,self::CHU_BUNRUI_CD_05BOSHO
        ,self::CHU_BUNRUI_CD_HOUYOU
        ,self::CHU_BUNRUI_CD_HAKAMAIRI
        ,self::CHU_BUNRUI_CD_HENREI
        ,self::CHU_BUNRUI_CD_HOUJI
        ,self::CHU_BUNRUI_CD_KAISHOKU
        ,self::CHU_BUNRUI_CD_HIKAZEI
        ,self::CHU_BUNRUI_CD_BETTOHIYOU
        ,self::CHU_BUNRUI_CD_NEBIKI
        ,self::CHU_BUNRUI_CD_NIIBON_1
        ,self::CHU_BUNRUI_CD_NIIBON_3
        ,self::CHU_BUNRUI_CD_NIIBON_2
        ,self::CHU_BUNRUI_CD_NIIBON_4
        ,self::CHU_BUNRUI_CD_NIIBON_5
        ,self::CHU_BUNRUI_CD_NIIBON_6
    ];
    
    private static $omotenasi = [
    ];
    
    private static $sonota = [
    ];
    
    /** サービス区分 */
    const SERVICE_KBN_NM = 1520;
    private $MSI_PAGE_NUM = 0;
    
    /**
     * アクション
     *
     * <AUTHOR> Sato
     * @since      2014/02/24
     */
    public function indexAction()
    {
        $params  = Msi_Sys_Utils::webInputs();
        @ $this->_bumonCd = $params['bumon_cd'];
        $out_type = self::OUTTYPE_SINGLEFILE;
/*        if (isset($params['preview'])) {
            $preview = htmlspecialchars($params['preview']) == 'on';
        } else {
            $preview = false;
        }*/
		if (isset($params['out_type'])) { $out_type = $params['out_type']; }

        $db = Msi_Sys_DbManager::getMyDb();

		// 売上伝票No.指定
		if (isset($params['uri_den_no'])) {
            if (is_string($params['uri_den_no'])) {
                $uri_den_no_ary[] = htmlspecialchars($params['uri_den_no']);
            } else if (is_array($params['uri_den_no'])) {
                foreach ($params['uri_den_no'] as $value) {
                    $uri_den_no_ary[] = htmlspecialchars($value);
                }
            }
            if (!isset($params['output_kbn'])) {    // 2015/03/24 ADD Kayo
                foreach ($uri_den_no_ary as $uri_den_no) {
                    $param_ary[] = DataMapper_Pdf1101::changeUriDenNoToParam($db, $uri_den_no);
                }
            } else {
                // 別注品一覧から印刷された場合
                foreach ($uri_den_no_ary as $uri_den_no) {
                    $param_ary[] = DataMapper_Pdf1101::changeUriDenNoToParam2($db, $uri_den_no);
                }
            }    
		} else {
			$seko_no = htmlspecialchars($params['seko_no']);          // 施行番号
                        $this->_moushiKbn = DataMapper_PdfCommon::getSekoMoushiKbn($seko_no);        // 申込区分　1：葬儀 2：法事 5：生前依頼 6：その他依頼
			if (array_key_exists('seko_no_sub', $params)) { $seko_no_sub = htmlspecialchars($params['seko_no_sub']); } // 施行番号（枝番）
			else { $seko_no_sub = '00'; }
			$data_kbn = htmlspecialchars($params['data_kbn']);        // データ区分　1：葬儀 2：法事 3：単品 4：別注品
			$param_ary = DataMapper_Pdf1101::changeSekoNoToParam($db, $data_kbn, $seko_no, $seko_no_sub);
		}

        if (isset($params['printKbn'])) {	// 並び替え区分 0:明細複合 1:互助会別
            $print_kbn = $params['printKbn'];
        } else {
            $print_kbn = 0;
        }

        $issue_date = date('Y/m/d');

		if ($out_type == self::OUTTYPE_MULTIFILE) {
			foreach ($param_ary as $param) {
				$pdfObj = new App_Pdf($this->getFileName($db, $param['seko_no'], self::$title));
				$this->outData($pdfObj, $db, $issue_date, $param['uri_den_no'], $param['data_kbn'], $param['seko_no'], $param['seko_no_sub'], $print_kbn);
				$pdfObj->downloadAjaxPush();
			}
			$pdfObj->downloadAjaxFlush();
		} else {
                        if (count($param_ary) === 1) {
                            $pdfObj = new App_Pdf($this->getFileName($db, $param_ary[0]['seko_no'], self::$title));
                        } else {
                            $pdfObj = new App_Pdf(self::$title.'(複数)');
                        }
			foreach ($param_ary as $param) {
				$this->outData($pdfObj, $db, $issue_date, $param['uri_den_no'], $param['data_kbn'], $param['seko_no'], $param['seko_no_sub'], $print_kbn);
			}
			$pdfObj->download();
		}
        // 別注品の場合は、発行済みに更新する。
        if (isset($params['output_kbn'])) {    // 2015/03/24 ADD Kayo
            $uri_ary = array();
            foreach ($param_ary as $uri_den_no) {
                $uri_ary[] = $uri_den_no['uri_den_no'];
            }
            Logic_SeikyuEtc::seikyuhakkoMulti( $uri_ary );
        }    
    }

	private function outData($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn)
    {
                // 軽減税率対応  keigen   基準日の設定
                $this->_prepKeigen($seko_no, $seko_no_sub, $data_kbn);
		switch ($data_kbn) {
			// 葬儀
			case '1':
				$this->addHyoshi($pdfObj, $db, $seko_no);
				$this->outData01($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn);
				break;
			// 法事
			case '2':
				$this->outData02($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn);
				break;
			// 単品
			case '3':
				$this->outData03($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn);
				break;
			// 別注品
			case '4':
				$this->outData03($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn);
				break;
			// その他施工
			case '6':
                $this->addHyoshi($pdfObj, $db, $seko_no);
				$this->outData01($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn);
				break;
		}
	}

    /**
     * 請求書（葬儀）
     * @param type $pdfObj
     * @param type $db
     * @param type $issue_date
     * @param type $seko_no
     * @return type
     */
    private function outData01($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn)
    {
        $numPagesCur = $pdfObj->getNumPages();	// 現在の総ページ数

        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$this->getSourceFileIndex($data_kbn)]);
        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');

        // 確認用のグリッドを出力
        // $pdfObj->test_line_out(600, 1000);

        $recCmn = DataMapper_PdfCommon::find( $db, array( "seko_no" => $seko_no ) );
        if (count($recCmn) == 0) {
            return;
        }

        // 軽減税率対応  事業者登録番号  keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $toroku_bango = App_KeigenUtils::getJigyoshaTorokuBango();
            if ( $toroku_bango ) {
                $bango_str = sprintf("事業者番号 %s", $toroku_bango);
                $pdfObj->write_string(array('x'=>445,'y'=>142,'width'=>137,'height'=>10,'font_size'=>10,'align'=>'R'), $bango_str);
            }
        }

        // 日付
        $pdfObj->write_date(array('type' => 'ymd', 'x' =>445, 'y' => 45, 'width' =>138, 'height' => 15, 'align' => 'R'), $issue_date, 'Y年m月d日');

        // 社判
        $pdfObj->syaban_out($db, 1, 389, 70,40);

        // 会社ロゴ
        $logo = null;
        if (isset($recCmn[0]['syaban4_img'])) {
            $logo = $db->readBlobCont($recCmn[0]['syaban4_img']);
        }
        $kaisyalogo = array(
                'logo' => $logo,
                'nm' => '',
                'bumon_nm' => $recCmn[0]['bumon_lnm'],
                'bumon_tel' => $recCmn[0]['tel']
            );

        $recWk = DataMapper_Pdf1101::find( $db, array( "uri_den_no" => $uri_den_no, "seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub ) );
        if (count($recWk) == 0) {
            return;
        }
        $rec = $recWk[0];
        
        // 請求先
        $sekyu = App_Utils::getSekoSekyuInfo($seko_no);
        $sekyu_nm = '';
        if (isset($sekyu)) {
            $pdfObj->write_string(array('x' => 32, 'y' => 62, 'width' =>240, 'height' => 15, 'font_size' => 15, 'align' => 'C'), $sekyu['sekyu_nm']);
            $pdfObj->write_string(array('x' => 32, 'y' => 83, 'width' =>255, 'height' => 10, 'font_size' => 10), $sekyu['yubin_no'].'　'.$sekyu['addr1']);
            $pdfObj->write_string(array('x' => 32, 'y' => 93, 'width' =>255, 'height' => 10, 'font_size' => 10), $sekyu['addr2']);
            $pdfObj->write_string(array('x' =>202, 'y' =>102, 'width' => 85, 'height' => 15), $sekyu['tel']);
            $sekyu_nm = $sekyu['sekyu_nm'];
        }
        $pdfObj->write_date(array('type' => 'ymd', 'x' =>202, 'y' =>113, 'width' => 85, 'height' => 15), $rec['sougi_ymd'], 'Y年m月d日');

        // ヘッダ金額
        $kumotsu_prc = 0;
        if (isset($rec['n_free5'])) {
            $kumotsu_prc = $rec['n_free5'];
        }

        // 入金情報
        $this->outNyukin02($pdfObj, $db, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, 715);

        // 印鑑
        if (isset($rec['inkan_img'])) {
            $img = $db->readBlobCont($rec['inkan_img']);
            $pdfObj->seal_out(528, 688, $img);
        }
        // 事務
        if (isset($rec['inkan_img1'])) {
            $img = $db->readBlobCont($rec['inkan_img1']);
            $this->seal_out($pdfObj, $rec['shonin_dt1'], 481, 688, $img);
//            $pdfObj->seal_out(491, 710, $img);
        }
        // 所属長
        if (isset($rec['inkan_img2'])) {
            $img = $db->readBlobCont($rec['inkan_img2']);
            $this->seal_out($pdfObj, $rec['shonin_dt2'], 436, 688, $img);
//            $pdfObj->seal_out(445, 710, $img);
        }
        // 互助会利用明細
        $recGojokai = DataMapper_Pdf0101::find3( $db, array( "seko_no" => $seko_no) );
        $disp_count = 0;
        $gy = 771.5;
        foreach ($recGojokai as $gojokai) {
            if($gojokai['yoto_kbn'] !== '99'){
                $pdfObj->write_string(array('x' =>  32, 'y' => $gy, 'width' => 60, 'height' => 15,  'align' => 'L',),$gojokai['kain_no']);
                $pdfObj->write_string(array('x' =>  94, 'y' => $gy, 'width' => 80, 'height' => 15,  'align' => 'L',),$gojokai['kanyu_nm']);
                $pdfObj->write_string(array('x' => 172, 'y' => $gy, 'width' => 47, 'height' => 15,  'align' => 'L', 'font_size' => 8),$gojokai['kaiin_nm']);
                $pdfObj->write_num(   array('x' => 217, 'y' => $gy, 'width' => 55, 'height' => 15,),$gojokai['keiyaku_gaku']);
                $pdfObj->write_num(   array('x' => 272, 'y' => $gy, 'width' => 50, 'height' => 15,),$gojokai['harai_gaku']);
                $pdfObj->write_num(   array('x' => 318, 'y' => $gy, 'width' => 48, 'height' => 15,),$gojokai['keiyaku_gaku'] - $gojokai['harai_gaku']);
                $gy += 12.2;
                $disp_count++;
                if($disp_count === 3) break;                    
            }

        }
        // 受付部門
        $pdfObj->set_default_font_family_h('kozgopromedium');
        $pdfObj->write_string(array('x' => 419.5, 'y' => 68, 'width' =>164, 'height' => 15, 'font_size' => 13,'align' => 'C'), "アイパルグループ");
        $pdfObj->write_string(array('x' => 419.5, 'y' => 82, 'width' =>164, 'height' => 15, 'font_size' => 20), $recCmn[0]['bumon_lnm']);
        $pdfObj->set_default_font_family_h('kozminproregular');
        $pdfObj->write_multi(
            array(
                array('type' => 'string', 'x' =>442, 'y' =>730, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $recCmn[0]['bumon_lnm']),
                array('type' => 'string', 'x' =>442, 'y' =>744, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $rec['tanto_nm']),
                array('type' =>    'ymd', 'x' =>442, 'y' =>758, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $issue_date, 'format' => 'Y年m月d日'),
                array('type' => 'string', 'x' =>442, 'y' =>771.5, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $seko_no),
                array('type' => 'string', 'x' =>442, 'y' =>784, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $rec['uri_den_no']),
                // 右上
                array('type' => 'string', 'x' => 419.5, 'y' => 111, 'width' => 164, 'height' => 15, 'font_size' => 10, 'value' => $recCmn[0]['bmn_addr1']),
                array('type' => 'string', 'x' => 388.5, 'y' => 123, 'width' => 25, 'height' => 15, 'font_size' => 10, 'value' => 'TEL'),
                array('type' => 'string', 'x' => 410  , 'y' => 123, 'width' => 73, 'height' => 15, 'font_size' => 10,'align' => 'R', 'value' => $recCmn[0]['tel']),
                array('type' => 'string', 'x' => 488.5, 'y' => 123, 'width' => 25, 'height' => 15, 'font_size' => 10, 'value' => 'FAX'),
                array('type' => 'string', 'x' => 510  , 'y' => 123, 'width' => 73, 'height' => 15, 'font_size' => 10,'align' => 'R', 'value' => $recCmn[0]['fax']),
            )
        );
        
        // 会社情報会員名取得する 互助会の文字列を置き換え
        $kain_nm = App_Utils::getKainNm();
        self::$sum_name[self::SUMID_A_GOJOKAI]          = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAI]); 
        self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]    = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]); 
        self::$sum_name[self::SUMID_A_GOJOKAIGAI]       = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAIGAI]); 
        self::$sum_name[self::SUMID_GOJOHARAI]          = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_GOJOHARAI]); 
        self::$msi_name_gojokai                         = str_replace('互助会', $kain_nm, self::$msi_name_gojokai); 
        self::$msi_name_gojokaitokuten                  = str_replace('互助会', $kain_nm, self::$msi_name_gojokaitokuten); 

        // 明細書
        $sumArr = $this->outDataDetail($pdfObj, $db, $uri_den_no, $seko_no, $data_kbn, $seko_no_sub, $rec['kain_no'], $print_kbn);

        // 表紙の金額
        $pdfObj->setPage(2);

        $kingaku = App_SeikyuLib::getKingaku($db, $seko_no, $seko_no_sub, $data_kbn);
        $sumArr['tax'] = array('name' => '消費税', 'sum' => $kingaku[App_SeikyuLib::ID_ZEIPRC]);
        $sumArr[self::SUMID_KEIYAKUGAKU] = array('sum' => $kingaku[App_SeikyuLib::ID_KEIYAKUGAKU]);
        $sumArr[self::SUMID_AZUKARI_DAN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_DAN], 'sum' => $kingaku[App_SeikyuLib::ID_AZUKARI_DAN]);
        $sumArr[self::SUMID_AZUKARI_HEN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_HEN], 'sum' => $kingaku[App_SeikyuLib::ID_AZUKARI_HEN]);
        $sumArr[self::SUMID_MEIGICGCOST] = array('name' => self::$sum_name[self::SUMID_MEIGICGCOST], 'sum' => $kingaku[App_MitsuLib::ID_MEIGICGCOST]);
        $sumArr[self::SUMID_EARLYUSE] = array('name' => self::$sum_name[self::SUMID_EARLYUSE], 'sum' => $kingaku[App_SeikyuLib::ID_EARLYUSE]);
        $sumArr[self::SUMID_EARLYUSEZEI] = array('name' => self::$sum_name[self::SUMID_EARLYUSEZEI], 'sum' => $kingaku[App_SeikyuLib::ID_EARLYUSEZEI]);
        $sumArr[self::SUMID_KAKEZAN] = array('name' => self::$sum_name[self::SUMID_KAKEZAN], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZAN]);
        $sumArr[self::SUMID_KAKEZANZEI] = array('name' => self::$sum_name[self::SUMID_KAKEZANZEI], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZANZEI]);
        $sumArr[self::SUMID_COSECHGGAKU] = array('name' => self::$sum_name[self::SUMID_COSECHGGAKU], 'sum' => $kingaku[App_SeikyuLib::ID_COSECHGGAKU]);
        $sumArr[self::SUMID_GOJOHARAI] = array('name' => self::$sum_name[self::SUMID_GOJOHARAI], 'sum' => $kingaku[App_SeikyuLib::ID_GOJOHARAI]);
        $sumArr[self::SUMID_ZENNOWARI] = array('name' => self::$sum_name[self::SUMID_ZENNOWARI], 'sum' => $kingaku[App_SeikyuLib::ID_ZENNOWARI]);
        $sumArr[self::SUMID_KAKEZEISAGAKU] = array('name' => self::$sum_name[self::SUMID_KAKEZEISAGAKU], 'sum' => $kingaku[App_SeikyuLib::ID_KAKEZEISAGAKU]);

        $sum = App_Utils::getSeikyuKingaku($seko_no, $seko_no_sub, $data_kbn);
        // 軽減税率対応のためデータ設定  keigen
        $this->_p_tgt_type    = 'normal';
        $this->_p_seko_no     = $seko_no;
        $this->_p_seko_no_sub = $seko_no_sub;
        $this->_p_data_kbn    = $data_kbn;
        $this->_p_history_no  = null;
        $this->_p_uri_den_no  = $uri_den_no;
        $sumArr['total'] = array('name' => '御請求金額', 'sum' => $sum);

        // 表紙の金額を出力
        $recKingaku = $this->outDataFaceSum01($pdfObj, $sumArr, $rec['gojokai_cose_nm'], $recWk, $seko_no);  
        //$this->outKaiinInfo($db, $pdfObj, $seko_no);
        
        $seikyu_zan = $recKingaku;
        $sekyu_sum = $seikyu_zan + $kumotsu_prc;
        $pdfObj->write_num(array('x' =>  45, 'y' => 157, 'width' => 70, 'height' => 15), $seikyu_zan);  // 葬儀施行金額
        $pdfObj->write_num(array('x' => 131, 'y' => 157, 'width' => 70, 'height' => 15), $kumotsu_prc);       // 供物未清算
        $pdfObj->write_num(array('x' => 219, 'y' => 157, 'width' => 70, 'height' => 15), $rec['nyukin_prc']);    // 御入金金額
        $pdfObj->write_num(array('x' => 299, 'y' => 157, 'width' => 80, 'height' => 15), $sekyu_sum - $rec['nyukin_prc']);    // 今回御請求金額
        
        
        // 供物未清算
        $pdfObj->write_num(array('x' => 295, 'y' => 648, 'width' => 80, 'height' => 15), $kumotsu_prc);
        // 備考
        $pdfObj->write_string(array('x' => 378, 'y' => 648, 'width' => 171, 'height' => 15), $rec['v_free9']);
        
        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);

			// 確認用のグリッドを出力
//            $pdfObj->test_line_out(600, 1000);

            $pdfObj->write_string(array('x' => 45, 'y' => 825, 'width' => 530, 'height' =>  15, 'align' => 'C'), ($page-1).'/'.($numPages-1));

            // 明細書
            if ($page > $numPagesCur + 1) {
                // 請求先名
                $pdfObj->write_string(array('x' => 31, 'y' => 55, 'width' =>180, 'height' => 15, 'font_size' => 15, 'align' => 'C'), $sekyu_nm);

                // 会社ロゴ
                //$pdfObj->kaisyalogo_ary_out(400, 35, $kaisyalogo, 200, 140, 14, 12, 14);

                // 発行日
                $pdfObj->write_date(array('type' => 'ymd', 'x' =>396, 'y' =>799, 'width' =>115, 'height' => 9, 'font_size' => 9), $issue_date, 'Y年m月d日');

                // 施行No.
                //$pdfObj->write_string(array('x' =>350, 'y' =>809.5, 'width' =>115, 'height' => 9, 'font_size' => 9), $seko_no);
                $pdfObj->write_string(array('x' =>296, 'y' =>809, 'width' =>115, 'height' => 9, 'font_size' => 9), $seko_no);
                
                // 請求No.
                $pdfObj->write_string(array('x' =>396, 'y' =>809, 'width' =>115, 'height' => 9, 'font_size' => 9), $rec['uri_den_no']);
 
                // 軽減税率対応 凡例   keigen
                if ( $this->_isKeigenAppliedCtxt() ) { // &&
                    // isset($this->_is_reduced_tax_rate_row_exists) && $this->_is_reduced_tax_rate_row_exists ) {
                    $pdfObj->write_string(array('x'=>31, 'y'=>797.5+2, 'width'=>180, 'height'=>9, 'font_size'=>9, 'align' => 'L'),
                                          '税欄の＊は軽減税率対象');
                }
            }
        }
        // 振込み案内ページ追加
        //$this->addFurikomiGuide($pdfObj, $db, $rec['souke_nm'], $recKingaku, $rec['uri_den_no']);
        $this->addFurikomiGuide($pdfObj, $db, $rec['souke_nm'], $sekyu_sum - $rec['nyukin_prc'], $rec['uri_den_no']);
    }
    
    /**
     * 請求書（法事）
     * @param type $pdfObj
     * @param type $db
     * @param type $issue_date
     * @param type $uri_den_no
     * @param type $data_kbn
     * @param type $seko_no
     * @param type $seko_no_sub
     * @param type $print_kbn
     * @return type
     */
    private function outData02($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $print_kbn)
    {
        $numPagesCur = $pdfObj->getNumPages();	// 現在の総ページ数

        $this->_moushiKbnHouji  = self::getSekoMoushiKbn($seko_no);        // 申込区分　1：葬儀 2：法事 5：生前依頼 6：その他依頼
        $recWk = DataMapper_Pdf1101::findhouji( $db, array( "uri_den_no" => $uri_den_no, "seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub ) );
        if (count($recWk) == 0) {
            return;
        }
        $rec = $recWk[0];
        
        if ($rec['free7_kbn'] == '12') { // 催事
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileSaiji);
        } else {
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$this->getSourceFileIndex($data_kbn)]);
        }
        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');

        // 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);

        $recCmn = DataMapper_PdfCommon::find( $db, array( "seko_no" => $seko_no ) );
        if (count($recCmn) == 0) {
            return;
        }

        // 軽減税率対応  事業者登録番号  keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $toroku_bango = App_KeigenUtils::getJigyoshaTorokuBango();
            if ( $toroku_bango ) {
                $bango_str = sprintf("事業者番号 %s", $toroku_bango);
                $pdfObj->write_string(array('x'=>445,'y'=>25,'width'=>137,'height'=>10,'font_size'=>10,'align'=>'R'), $bango_str);
            }
        }

        // 社判
        $pdfObj->syaban_out($db, 1, 389, 45,40);
        $pdfObj->set_default_font_family_h('kozgopromedium');
        $pdfObj->write_string(array('x' => 419.5, 'y' => 42, 'width' =>164, 'height' => 15, 'font_size' => 13,'align' => 'C'), "アイパルグループ");
        $pdfObj->write_string(array('x' => 419.5, 'y' => 56, 'width' =>164, 'height' => 15, 'font_size' => 20), $recCmn[0]['bumon_lnm']);
        $pdfObj->set_default_font_family_h('kozminproregular');
        $pdfObj->write_string(array('type' => 'string', 'x' => 419.5, 'y' => 85, 'width' => 164, 'height' => 15, 'font_size' => 10),$recCmn[0]['bmn_addr1']);
        $pdfObj->write_string(array('type' => 'string', 'x' => 388.5, 'y' => 97, 'width' => 25, 'height' => 15, 'font_size' => 10),'TEL');
        $pdfObj->write_string(array('type' => 'string', 'x' => 410  , 'y' => 97, 'width' => 73, 'height' => 15, 'font_size' => 10,'align' => 'R'),$recCmn[0]['tel']);
        $pdfObj->write_string(array('type' => 'string', 'x' => 488.5, 'y' => 97, 'width' => 25, 'height' => 15, 'font_size' => 10),'FAX');
        $pdfObj->write_string(array('type' => 'string', 'x' => 510  , 'y' => 97, 'width' => 73, 'height' => 15, 'font_size' => 10,'align' => 'R'),$recCmn[0]['fax']);
        $logo = null;
        if (isset($recCmn[0]['syaban4_img'])) {
            $logo = $db->readBlobCont($recCmn[0]['syaban4_img']);
        }
        $kaisyalogo = array(
                'logo' => $logo,
                'nm' => '',
                'bumon_nm' => $recCmn[0]['bumon_lnm'],
                'bumon_tel' => $recCmn[0]['tel']
            );
        
        // 台帳番号
        $daicho_no = "";
        $daicho_no_eria = "　";
        $daicho_no_mm   = "　";
        $daicho_no_seq  = "　";
        if(isset($rec['daicho_no_eria']) && strlen($rec['daicho_no_eria']) > 0) {
            $daicho_no_eria = $rec['daicho_no_eria'];
        }
        if(isset($rec['daicho_no_mm']) && strlen($rec['daicho_no_mm']) > 0) {
            $daicho_no_mm = trim($rec['daicho_no_mm']);
        }
        if(isset($rec['daicho_no_seq']) && strlen($rec['daicho_no_seq']) > 0) {
            $daicho_no_seq = $rec['daicho_no_seq'];
        }
        $daicho_no = $daicho_no_eria ." - ". $daicho_no_mm ." - ". $daicho_no_seq;
        $pdfObj->write_string(array('x' => 60, 'y' => 31, 'width' => 120, 'height' => 24, 'font_size' => 10), $daicho_no);
        
        // 葬家名
        $pdfObj->write_string(array('x' => 115, 'y' => 16, 'width' => 120, 'height' => 35, 'font_size' => 20, 'align' => 'R'), $rec['souke_nm']);
        // 施行No.
        $pdfObj->write_string(array('x' => 62, 'y' => 47, 'width' => 150, 'height' => 20, 'font_size' => 10), $seko_no);
        // 請求番号
        $pdfObj->write_string(array('x' => 62, 'y' => 62, 'width' => 150, 'height' => 20, 'font_size' => 10), $rec['uri_den_no']);
        // 担当者
        $pdfObj->write_string(array('x' => 458, 'y' => 121, 'width' => 180, 'height' => 27, 'font_size' => 10), $rec['tanto_nm']);

        // 請求先
        $sekyu = App_Utils::getSekoSekyuInfo($seko_no);
        $sekyu_nm = '';
        if (isset($sekyu)) {
            // お客様名
            $pdfObj->write_string(array('x' => 110, 'y' => 147, 'width' => 230, 'height' => 27, 'font_size' => 10), $sekyu['sekyu_nm'] . ' 様');
            // お客様住所
            $pdfObj->write_string(array('x' => 110, 'y' => 166, 'width' => 230, 'height' => 27, 'font_size' => 10), $sekyu['addr1'] . $sekyu['addr2']);
            // 電話番号
            $s_tel = $sekyu['tel'];
            if (empty($s_tel)) {
                $s_tel = $sekyu['mobile_tel'];
            }
            $pdfObj->write_string(array('x' => 428, 'y' => 147, 'width' => 150, 'height' => 27), $s_tel);
            $sekyu_nm = $rec['souke_nm'];
        }
        // 項目名判別
        $koumoku_nm = "法事";
        if ($rec['free7_kbn'] == '8') { // 新盆
            $koumoku_nm = "新盆";
        }
        // 項目
        $pdfObj->write_string(array('x' => 370, 'y' => 121, 'width' => 50, 'height' => 27, 'font_size' => 10), $koumoku_nm);
        // ご利用場所
        $pdfObj->write_string(array('x' => 112, 'y' => 186, 'width' => 240, 'height' => 27), $rec['basho_nm']);
        // ご会食場
        $pdfObj->write_string(array('x' => 112, 'y' => 206, 'width' => 240, 'height' => 27), $rec['kaishokujo_nm']);
        // 申込区分
        $pdfObj->write_string(array('x' => 428, 'y' => 166, 'width' => 150, 'height' => 27), $rec['sk_houyo_nm']);
        // ご利用日付(法宴日)
        $pdfObj->write_date(array('type' => 'ymd', 'x' => 428, 'y' => 206, 'width' => 150, 'height' => 27), $rec['hoen_date'], 'Y年m月d日');
        // ご納品日付
        $pdfObj->write_date(array('type' => 'ymd', 'x' => 428, 'y' => 186, 'width' => 150, 'height' => 27), $rec['nohin_date'], 'Y年m月d日');
        
        if ($rec['free7_kbn'] == '12') { // 催事
            // 件名
            $pdfObj->write_string(array('x' => 110, 'y' => 121, 'width' => 230, 'height' => 27, 'font_size' => 10), $rec['v_free10']);
            if (isset($sekyu)) {
                // 備考
                $pdfObj->write_string(array('x' => 110, 'y' => 226, 'width' => 460, 'height' => 27, 'font_size' => 10), $sekyu['biko1']);
            }
        } else {
            // ご宗旨
            $syuha_nm = $rec['syuha_nm'];
            if (empty($syuha_nm)) {
                $syuha_nm = $rec['syushi_nm'];
            }
            $pdfObj->write_string(array('x' => 112, 'y' => 226, 'width' => 240, 'height' => 27), $syuha_nm);
            // 寺院名
            $pdfObj->write_string(array('x' => 428, 'y' => 226, 'width' => 150, 'height' => 27), $rec['jyusho_nm']);
        }
        
//        $pdfObj->write_date(array('type' => 'ymd', 'x' =>40, 'y' =>100, 'width' => 85, 'height' => 15), $rec['sougi_ymd'], 'Y年m月d日');

        // 請求金額
        if($this->_moushiKbnHouji == 2){
            $pdfObj->write_num(array('x' => 22, 'y' => 287, 'width' => 100, 'height' => 25, 'font_size' => 14), $rec['seikyu_prc'] + $rec['zei_prc']);  // 御請求金額
            $pdfObj->write_num(array('x' =>222, 'y' => 287, 'width' => 100, 'height' => 25, 'font_size' => 14), $rec['seikyu_zan']);    // 今回御請求金額
        }
        $pdfObj->write_num(array('x' =>122, 'y' => 287, 'width' => 100, 'height' => 25, 'font_size' => 14), $rec['nyukin_prc']);    // 御入金金額
        if($this->_moushiKbnHouji == 8){
            // 口座情報表示
            $bumon = DataMapper_Pdf1101::getUriBumon($db, $uri_den_no);
            $this->outKouza02($pdfObj, $db, $bumon, $uri_den_no);
        }
        // 入金情報
        $this->outNyukin03($pdfObj, $db, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, 750, -10, 13.5);
        
        // 印鑑
        if (isset($rec['inkan_img'])) {
            $img = $db->readBlobCont($rec['inkan_img']);
            $pdfObj->seal_out(528, 724, $img);
        }
        // 所属長
//        if (isset($rec['inkan_img2'])) {
//            $img = $db->readBlobCont($rec['inkan_img2']);
//            $this->seal_out($pdfObj, $rec['shonin_dt2'], 533, 724, $img);
////            $pdfObj->seal_out(533, 724, $img);
//        }

        // 受付部門
        $pdfObj->write_multi(
            array(
                array('type' => 'string', 'x' =>430, 'y' =>790, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $recCmn[0]['bumon_lnm']),
                array('type' =>    'ymd', 'x' =>430, 'y' =>805, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $issue_date, 'format' => 'Y年m月d日')
            )
        );
        
        // 会社情報会員名取得する 互助会の文字列を置き換え
        $kain_nm = App_Utils::getKainNm();
        self::$sum_name[self::SUMID_A_GOJOKAI]          = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAI]); 
        self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]    = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]); 
        self::$sum_name[self::SUMID_A_GOJOKAIGAI]       = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_A_GOJOKAIGAI]); 
        self::$sum_name[self::SUMID_GOJOHARAI]          = str_replace('互助会', $kain_nm, self::$sum_name[self::SUMID_GOJOHARAI]); 
        self::$msi_name_gojokai                         = str_replace('互助会', $kain_nm, self::$msi_name_gojokai); 
        self::$msi_name_gojokaitokuten                  = str_replace('互助会', $kain_nm, self::$msi_name_gojokaitokuten); 

        // 明細書
        $sumArr = $this->outDataDetail02($pdfObj, $db, $uri_den_no, $seko_no, $data_kbn, $seko_no_sub, $print_kbn);

        // 御請求金額を印字するページを指定
        $pdfObj->setPage($numPagesCur + 1);

        $kingaku = App_SeikyuLib::getKingaku($db, $seko_no, $seko_no_sub, $data_kbn);
        $sumArr['tax'] = array('name' => '消費税', 'sum' => $kingaku[App_SeikyuLib::ID_ZEIPRC]);
        $sumArr[self::SUMID_KEIYAKUGAKU] = array('sum' => $kingaku[App_SeikyuLib::ID_KEIYAKUGAKU]);
        $sumArr[self::SUMID_AZUKARI_DAN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_DAN], 'sum' => $kingaku[App_SeikyuLib::ID_AZUKARI_DAN]);
        $sumArr[self::SUMID_AZUKARI_HEN] = array('name' => self::$sum_name[self::SUMID_AZUKARI_HEN], 'sum' => $kingaku[App_SeikyuLib::ID_AZUKARI_HEN]);
        $sumArr[self::SUMID_EARLYUSE] = array('name' => self::$sum_name[self::SUMID_EARLYUSE], 'sum' => $kingaku[App_SeikyuLib::ID_EARLYUSE]);
        $sumArr[self::SUMID_EARLYUSEZEI] = array('name' => self::$sum_name[self::SUMID_EARLYUSEZEI], 'sum' => $kingaku[App_SeikyuLib::ID_EARLYUSEZEI]);
        $sumArr[self::SUMID_KAKEZAN] = array('name' => self::$sum_name[self::SUMID_KAKEZAN], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZAN]);
        $sumArr[self::SUMID_KAKEZANZEI] = array('name' => self::$sum_name[self::SUMID_KAKEZANZEI], 'sum' => $kingaku[App_MitsuLib::ID_KAKEZANZEI]);
        $sumArr[self::SUMID_COSECHGGAKU] = array('name' => self::$sum_name[self::SUMID_COSECHGGAKU], 'sum' => $kingaku[App_SeikyuLib::ID_COSECHGGAKU]);
        $sumArr[self::SUMID_GOJOHARAI] = array('name' => self::$sum_name[self::SUMID_GOJOHARAI], 'sum' => $kingaku[App_SeikyuLib::ID_GOJOHARAI]);
        $sumArr[self::SUMID_ZENNOWARI] = array('name' => self::$sum_name[self::SUMID_ZENNOWARI], 'sum' => $kingaku[App_SeikyuLib::ID_ZENNOWARI]);
        $sumArr[self::SUMID_KAKEZEISAGAKU] = array('name' => self::$sum_name[self::SUMID_KAKEZEISAGAKU], 'sum' => $kingaku[App_SeikyuLib::ID_KAKEZEISAGAKU]);

        $sum = App_Utils::getSeikyuKingaku($seko_no, $seko_no_sub, $data_kbn);
        // 軽減税率対応のためデータ設定  keigen
        $this->_p_tgt_type    = 'normal';
        $this->_p_seko_no     = $seko_no;
        $this->_p_seko_no_sub = $seko_no_sub;
        $this->_p_data_kbn    = $data_kbn;
        $this->_p_history_no  = null;
        $this->_p_uri_den_no  = $uri_den_no;
        // 御請求金額の合計
        if($this->_moushiKbnHouji == 2){
            $pdfObj->write_num(array('x' => 435, 'y' => 671, 'width' => 130, 'height' => 30, 'font_size' => 15), $sum);
        }

        // 御請求金額を印字
        $this->outDataFaceSum01_houji($pdfObj, $sumArr, $rec['gojokai_cose_nm']);
        
        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);

            // 確認用のグリッドを出力
//            $pdfObj->test_line_out(600, 1000);

            //$pdfObj->write_string(array('x' => 45, 'y' => 815, 'width' => 530, 'height' =>  15, 'align' => 'C'), ($page).'/'.($numPages));
            $pdfObj->write_string(array('x' => 45, 'y' => 815, 'width' => 530, 'height' =>  15, 'align' => 'C'), ($page - $numPagesCur).'/'.($numPages - $numPagesCur));

            // 明細書
            if ($page > $numPagesCur + 1) {
                // 施行No.
                $pdfObj->write_string(array('x' =>60, 'y' =>33, 'width' =>130, 'height' => 20, 'font_size' => 10), $seko_no);
                // 請求No.
                $pdfObj->write_string(array('x' =>60, 'y' =>45, 'width' =>130, 'height' => 20, 'font_size' => 10), $rec['uri_den_no']);
                // 請求先名
                $pdfObj->write_string(array('x' => 155, 'y' => 14, 'width' =>130, 'height' => 20, 'font_size' => 20, 'align' => 'C'), $sekyu_nm);
//                // 会社ロゴ
//                $pdfObj->kaisyalogo_ary_out(410, 25, $kaisyalogo, 190, 140, 14, 12, 16);
                // 受付部門
                $pdfObj->write_string(array('x' => 462, 'y' => 788, 'width' => 115, 'height' => 10, 'font_size' => 10), $recCmn[0]['bumon_lnm']);
                // 発行日
                $pdfObj->write_date(array('type' => 'ymd', 'x' => 462, 'y' => 803, 'width' => 115, 'height' => 10, 'font_size' => 10), $issue_date, 'Y年m月d日');

                // 軽減税率対応 凡例   keigen
                if ( $this->_isKeigenAppliedCtxt() ) { // &&
                    // isset($this->_is_reduced_tax_rate_row_exists) && $this->_is_reduced_tax_rate_row_exists ) {
                    $pdfObj->write_string(array('x' => 17, 'y' => 788, 'width' => 180, 'height' => 9, 'font_size' => 9, 'align' => 'L'), 
                            '＊は軽減税率対象');
                    // 明細行までは出力する
                    if ($this->MSI_PAGE_NUM >= $page) {
                        $pdfObj->write_string(array('x' => 17, 'y' => 798, 'width' => 180, 'height' => 9, 'font_size' => 9, 'align' => 'L'), 
                                '※は奉仕料対象');
                    }
                }
            }
        }
        // 新盆以外は振込み案内ページ追加
        if ($this->_moushiKbnHouji != 8) {
            $this->addFurikomiGuideHouji($pdfObj, $db, $rec['souke_nm'], $rec['seikyu_zan'], $rec['uri_den_no'],$recCmn[0]['bumon_cd']);
        }
    }

    /**
     * 請求書（単品・別注品）
     * @version 2014/11/06 伝票備考２は入金備考に変更したので、印刷しないように修正 2014/11/06 Kayo
     * @version 2014/12/19 請求先名がない場合は、「様」を印刷しないように修正。
     * @param type $pdfObj
     * @param type $db
     * @param type $issue_date
     * @param type $uri_den_no
     * @param type $data_kbn
     * @return type
     */
    private function outData03($pdfObj, $db, $issue_date, $uri_den_no, $data_kbn)
    {
        $numPagesCur = $pdfObj->getNumPages();	// 現在の総ページ数

		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$this->getSourceFileIndex($data_kbn)]);
        $pdfObj->set_default_minus_font_color('red');
        $pdfObj->set_default_minus_chartype('t');

        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

		$kaisya = DataMapper_PdfCommon::getKaisya($db, array('kaisya_lnm', 'syaban4_img'));
		$bumon = DataMapper_Pdf1101::getUriBumon($db, $uri_den_no);
		

        // 軽減税率対応  事業者登録番号  keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $toroku_bango = App_KeigenUtils::getJigyoshaTorokuBango();
            if ( $toroku_bango ) {
                $bango_str = sprintf("事業者番号 %s", $toroku_bango);
                $pdfObj->write_string(array('x'=>445,'y'=>142,'width'=>137,'height'=>10,'font_size'=>10,'align'=>'R'), $bango_str);
            }
        }
        
        // 社判
        //$pdfObj->syaban_out($db, 3, 360, 70);
        $pdfObj->syaban_out($db, 1, 389, 70,40);

        // 会社ロゴ
        $logo = null;
        if (isset($kaisya['syaban4_img'])) {
            $logo = $db->readBlobCont($kaisya['syaban4_img']);
        }
        $kaisyalogo = array(
                'logo' => $logo,
                'nm' => '',
                'bumon_nm' => $bumon['bumon_nm'],
                'bumon_tel' => $bumon['tel']
            );

		// 単品
		if ($data_kbn == 3) {
			$recWk = DataMapper_Pdf1101::findTanpin($db, array("uri_den_no" => $uri_den_no));
                        // 日付
                        $pdfObj->write_date(array('type' => 'ymd', 'x' =>445, 'y' => 45, 'width' =>138, 'height' => 15, 'align' => 'R'), $issue_date, 'Y年m月d日');
		} else {
			$recWk = DataMapper_Pdf1101::findBechu( $db, array( "uri_den_no" => $uri_den_no ) );
                        if (count($recWk) > 0){
                            // 葬儀日
                            $pdfObj->write_date(array('type' => 'ymd', 'x' =>445, 'y' => 45, 'width' =>138, 'height' => 15, 'align' => 'R'), $recWk[0]['sougi_ymd'], 'Y年m月d日');
                        }
		}
        if (count($recWk) == 0) { return; }
        $rec = $recWk[0];

		// 別注品
		if ($data_kbn == 4) {
			// 別注品ではお届け先に喪主を出力
			$rec['nonyu_nm'] = $rec['souke_nm'];
		}

        // 請求先
		$sekyu['sekyu_nm'] = $rec['sekyu_nm'];
		$sekyu['sekyu_soufu_nm'] = $rec['sekyu_soufu_nm'];
		$sekyu['yubin_no'] = $rec['sekyu_yubin_no'];
		$sekyu['addr1'] = $rec['sekyu_addr1'];
		$sekyu['addr2'] = $rec['sekyu_addr2'];
		$sekyu['tel'] = $rec['sekyu_tel'];
        if (strlen($sekyu['yubin_no'])>0 || strlen($sekyu['addr1'])>0 || strlen($sekyu['addr1'])>0) {
            $pdfObj->write_string(array('x' => 75, 'y' => 50, 'width' =>175, 'height' => 10, 'font_size' => 10), '〒'.$sekyu['yubin_no']);
            $pdfObj->write_string(array('x' => 75, 'y' => 62, 'width' =>175, 'height' => 10, 'font_size' => 10), $sekyu['addr1']);
            $pdfObj->write_string(array('x' => 75, 'y' => 74, 'width' =>175, 'height' => 10, 'font_size' => 10), $sekyu['addr2']);
        }
        if (strlen($sekyu['sekyu_soufu_nm']) > 0) {
            $pdfObj->write_string(array('x' => 75, 'y' => 88, 'width' => 135, 'height' => 15, 'font_size' => 15, 'align' => 'C'), $sekyu['sekyu_soufu_nm']);
            $pdfObj->write_string(array('x' => 215, 'y' => 90, 'width' => 25, 'height' => 15, 'font_size' => 11), '様');
            if (strlen($sekyu['sekyu_nm']) > 0) {
                $pdfObj->write_string(array('x' => 45, 'y' => 145, 'width' => 165, 'height' => 15, 'font_size' => 13, 'align' => 'L'), $sekyu['sekyu_nm']);
                $pdfObj->write_string(array('x' => 215, 'y' => 145, 'width' => 25, 'height' => 15, 'font_size' => 13), '様');
            }
        } else {
            if (strlen($sekyu['sekyu_nm']) > 0) {
                $pdfObj->write_string(array('x' => 75, 'y' => 88, 'width' => 135, 'height' => 15, 'font_size' => 15, 'align' => 'C'), $sekyu['sekyu_nm']);
                $pdfObj->write_string(array('x' => 215, 'y' => 90, 'width' => 25, 'height' => 15, 'font_size' => 11), '様');
            }
        }
//        $pdfObj->write_string(array('x' => 460, 'y' => 191, 'width' => 100, 'height' => 15, 'font_size' => 10), $sekyu['tel']);

        // お届け先
        if (isset($rec['nonyu_nm'])) {
            if (strlen($rec['nonyu_nm'])>0) {
                $pdfObj->write_string(array('x' => 372, 'y' => 160, 'width' =>  50, 'height' => 15, 'font_size' => 10), 'お届け先');
                $pdfObj->write_string(array('x' => 415, 'y' => 172, 'width' => 140, 'height' => 15, 'font_size' => 12, 'align' => 'C'), $rec['nonyu_nm']);
                $pdfObj->write_string(array('x' => 557, 'y' => 172, 'width' =>  20, 'height' => 15, 'font_size' => 12), '家');
                $pdfObj->write_line(array('x1' => 374, 'y1' => 187, 'x2' => 575, 'y2' => 187, 'width' => 1, 'color' => self::$color, 'type' => 'line'));
            }    
		}

        // ヘッダ金額
        $pdfObj->write_num(array('x' => 50, 'y' => 210, 'width' => 76, 'height' => 15, 'font_size' => 15), $rec['seikyu_prc'] + $rec['zei_prc']);  // 御請求金額
        $pdfObj->write_num(array('x' =>135, 'y' => 210, 'width' => 76, 'height' => 15, 'font_size' => 15), $rec['nyukin_prc']);    // 御入金金額
        $pdfObj->write_num(array('x' =>215, 'y' => 210, 'width' => 86, 'height' => 15, 'font_size' => 15), $rec['seikyu_zan']);    // 今回御請求金額

        // 入金情報
        $this->outNyukin($pdfObj, $db, $uri_den_no, $data_kbn, $rec['seko_no'], $rec['seko_no_sub'], 613.5);

        // 印鑑
        if (isset($rec['inkan_img'])) {
            $img = $db->readBlobCont($rec['inkan_img']);
            $pdfObj->seal_out(529, 728, $img);
        }
        
        // 受付部門
        $pdfObj->set_default_font_family_h('kozgopromedium');
        $pdfObj->write_string(array('x' => 419.5, 'y' => 68, 'width' =>164, 'height' => 15, 'font_size' => 13, 'align' => 'C'), "アイパルグループ");
        $pdfObj->write_string(array('x' => 419.5, 'y' => 82, 'width' =>164, 'height' => 15, 'font_size' => 20), $bumon['bumon_nm']);
        $pdfObj->set_default_font_family_h('kozminproregular');
        $pdfObj->write_multi(
            array(
                array('type' => 'string', 'x' =>460, 'y' =>769, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $bumon['bumon_nm']),
                array('type' => 'string', 'x' =>460, 'y' =>783, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $rec['tanto_nm']),
                array('type' =>    'ymd', 'x' =>460, 'y' =>797, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $issue_date, 'format' => 'Y年m月d日'),
                array('type' => 'string', 'x' =>460, 'y' =>811, 'width' =>115, 'height' => 15, 'font_size' => 10, 'value' => $rec['uri_den_no']),
                //array('type' => 'string', 'x' => 440, 'y' => 80, 'width' => 150, 'height' => 15, 'font_size' => 15, 'value' => $bumon['bumon_nm']),
                array('type' => 'string', 'x' => 419.5, 'y' => 111, 'width' => 164, 'height' => 15, 'font_size' => 10, 'value' => $bumon['addr1_nm']),
                array('type' => 'string', 'x' => 388.5, 'y' => 123, 'width' => 25, 'height' => 15, 'font_size' => 10, 'value' => 'TEL'),
                array('type' => 'string', 'x' => 410  , 'y' => 123, 'width' => 73, 'height' => 15, 'font_size' => 10, 'value' => $bumon['tel']),
                array('type' => 'string', 'x' => 488.5, 'y' => 123, 'width' => 25, 'height' => 15, 'font_size' => 10, 'value' => 'FAX'),
                array('type' => 'string', 'x' => 510  , 'y' => 123, 'width' => 73, 'height' => 15, 'font_size' => 10, 'value' => $bumon['fax']),
            )
        );

        // 備考
        //$pdfObj->write_strings(array('x' => 50, 'y' => 700, 'width' => 320, 'height' => 85), $rec['denpyo_biko1'].PHP_EOL.$rec['denpyo_biko2']);
        $pdfObj->write_strings(array('x' => 50, 'y' => 698, 'width' => 320, 'height' => 85, 'font_size' => 8), $rec['denpyo_biko1']);
        // 口座情報表示
        $this->outKouza($pdfObj, $db, $bumon, $uri_den_no);
        // 明細書
//		$zei_prc = DataMapper_SeikyuCalc::getZeiPrc($db, array('uri_den_no' => $uri_den_no));
//        $this->outDataDetail03($pdfObj, $db, $uri_den_no, $data_kbn, $rec['tax_kbn'],
//                array('uri_prc_sum' => $rec['uri_prc_sum'] + $rec['uri_hepn_sum'] + $rec['uri_nebk_sum'], 'zei_prc' => $zei_prc['out_zei_prc'] + $zei_prc['in_zei_prc']));
        $this->outDataDetail03($pdfObj, $db, $uri_den_no, $data_kbn);
        // ページ等を出力
        $numPages = $pdfObj->getNumPages();
        for ($i = $numPagesCur; $i < $numPages; $i++) {
            $page = $i + 1;
            $pdfObj->setPage($page);
            //$pdfObj->write_string(array('x' => 45, 'y' => 815, 'width' => 530, 'height' =>  15, 'align' => 'C'), ($page).'/'.($numPages));
            $pdfObj->write_string(array('x' => 45, 'y' => 805, 'width' => 530, 'height' =>  15, 'font_size' => 9, 'align' => 'C'), ($page - $numPagesCur).'/'.($numPages - $numPagesCur));
            
            // 明細書
            if ($page > $numPagesCur + 1) {
                // 請求先名
                $pdfObj->write_string(array('x' => 45, 'y' => 59, 'width' =>180, 'height' => 15, 'font_size' => 15, 'align' => 'C'), $sekyu['sekyu_nm']);

                // 会社ロゴ
                //$pdfObj->kaisyalogo_ary_out(400, 35, $kaisyalogo, 200, 140, 14, 12, 16);

                // 発行日
                $pdfObj->write_date(array('type' => 'ymd', 'x' =>460, 'y' =>791, 'width' =>115, 'height' => 9, 'font_size' => 9), $issue_date, 'Y年m月d日');
                        
                // 請求No.
                $pdfObj->write_string(array('x' =>460, 'y' =>802, 'width' =>115, 'height' => 9, 'font_size' => 9), $rec['uri_den_no']);
            }
            // 軽減税率対応 凡例 keigen
            if ( $this->_isKeigenAppliedCtxt() ) { // &&
                // isset($this->_is_reduced_tax_rate_row_exists) && $this->_is_reduced_tax_rate_row_exists ) {
                $pdfObj->write_string(array('x'=>40, 
                                            'y'=> ($page==1 ? 558 : 792), // OK
                                            // 'y'=> 792,
                                            'width'=>180, 'height'=>9, 'font_size'=>9, 'align' => 'L'),
                                      '＊は軽減税率対象');
            }
        }
    }
    /**
     * 
     * 口座情報表示
     * 
     * @param type $pdfObj
     * @param type $db
     * @param type $bumon
     */
    private function outKouza($pdfObj, $db, $bumon, $seikyu_no) {
        $bank = $this->getBankInfo($db, $bumon['bumon_cd']);
        if(count($bank) > 0){
            $pdfObj->write_string( array('x' => 50, 'y' => 712, 'width' => 320, 'height' => 10, 'font_size' => 8), "振込先：口座名義");
            $pdfObj->write_string( array('x' => 50, 'y' => 727, 'width' => 320, 'height' => 10, 'font_size' => 8), $bank[0]['koza_meigi_nm']);
            $bank_nm = $bank[0]['bank_nm']." ".$bank[0]['shiten_nm']." ".$bank[0]['yokin_nm']." ".$bank[0]['kouza_no'];
            $pdfObj->write_string( array('x' => 50, 'y' => 737, 'width' => 320, 'height' => 10, 'font_size' => 8), $bank_nm);
            $pdfObj->write_string( array('x' => 50, 'y' => 753, 'width' => 120, 'height' => 10, 'font_size' => 8, 'align' => 'C'), "お客様番号　".$seikyu_no);
            $pdfObj->write_line( array('x1' => 55, 'y1' => 763, 'x2' => 165, 'y2' => 763));
            $pdfObj->write_string( array('x' => 50, 'y' => 771, 'width' => 320, 'height' => 10, 'font_size' => 8), self::$furikomi_str);
        }
    }
    /**
     * 
     * 口座情報表示（新盆）
     * 
     * @param type $pdfObj
     * @param type $db
     * @param type $bumon
     */
    private function outKouza02($pdfObj, $db, $bumon, $seikyu_no) {
        $bank = $this->getBankInfo($db, $bumon['bumon_cd']);
        if(count($bank) > 0){
            $bank_nm = $bank[0]['bank_nm']." ".$bank[0]['shiten_nm']." ".$bank[0]['yokin_nm']." ".$bank[0]['kouza_no'];
            $pdfObj->write_string( array('x' =>330, 'y' => 255, 'width' => 245, 'height' => 10, 'font_size' =>11), "振込口座名 ");
            $pdfObj->write_string( array('x' =>330, 'y' => 270, 'width' => 245, 'height' => 10, 'font_size' =>12), $bank[0]['koza_meigi_nm']." ".$bank_nm);
            $pdfObj->write_string( array('x' =>330, 'y' => 285, 'width' => 245, 'height' => 10, 'font_size' =>12, 'align' => 'L'), "お客様番号　".$seikyu_no);
            $pdfObj->write_line( array('x1'  =>330, 'y1' => 300, 'x2' => 500, 'y2' => 300));
            $pdfObj->write_string( array('x' =>330, 'y' => 303, 'width' => 245, 'height' => 10, 'font_size' => 7), self::$furikomi_str);
        }
    }
    private function getBankInfo($db, $bumon_cd) {
        $sql = "
            SELECT 
                 --cnm.kbn_value_snm AS yokin_nm
                 CASE bm.yokin_sbt WHEN 0 THEN '(普)'
                                   ELSE '(当)'
                 END               AS yokin_nm
                ,bm.* 
            FROM 
                bank_mst bm
            LEFT JOIN code_nm_mst cnm
              ON cnm.code_kbn = '1980'
             AND cnm.kbn_value_cd_num = bm.yokin_sbt
             AND cnm.delete_flg = 0
            WHERE 
                bm.delete_flg = 0 
            AND bm.bumon_cd   = :bumon_cd
            ORDER BY bm.print_seq ASC;
                ";
        $select = $db->easySelect($sql,array('bumon_cd' => $bumon_cd));
        return $select;        
    }
    /**
     * 会員番号を取得
     * @param type $db
     * @param type $seko_no
     * @param type $yoto_kbn
     * @return null
     */
    private function getKainNo($db, $seko_no, $yoto_kbn)
    {
        return DataMapper_SekoGojokaiMember::find( $db, array( "seko_no" => $seko_no, "yoto_kbn" => $yoto_kbn ) );
    }

    /**
     * 明細を出力
     * 
     * @param type $pdfObj
     * @param type $db
     * @param type $seko_no
     * @param type $data_kbn
     * @param type $print_kbn
     * @return array    明細金額を集計した配列
     */
    private function outDataDetail($pdfObj, $db, $uri_den_no, $seko_no, $data_kbn, $seko_no_sub, $kain_no, $print_kbn)
    {
        static $meisai_top = 95;
        static $meisai_row_height = 19.09;
        static $meisai_row_count = 37;
        $pdfObj->set_default_font_size(9);

        // コード名称取得
        $code_ary = $this->getCodeNm($db,self::SERVICE_KBN_NM);
        
        // 明細金額を集計した配列
        $sumArr = array();
        $sumArr[self::SUMID_A_PLAN] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_PLAN]);
        $sumArr[self::SUMID_A_GOJOKAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAI]);
        $sumArr[self::SUMID_A_GOJOKAINEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        $sumArr[self::SUMID_A_NEBIKI_PRC] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_NEBIKI_PRC]);
        $sumArr[self::SUMID_A_GOJOKAIGAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAIGAI]);

        $set_arr[] = array('x' => 31, 'y' => $meisai_top, 'width' => 100, 'height' => 15); // 項目1 摘要がない場合
        $set_arr[] = array('x' => 31, 'y' => $meisai_top - 6, 'width' => 100, 'height' => 15); // 項目2 摘要がある場合
        $set_arr[] = array('x' => 31, 'y' => $meisai_top + 5, 'width' => 100, 'height' => 15); // 摘要
        $set_arr[] = array('x' => 137, 'y' => $meisai_top, 'width' => 40, 'height' => 15); // プラン内
        $set_arr[] = array('x' => 165, 'y' => $meisai_top, 'width' => 40, 'height' => 15); // グレードアップ
        $set_arr[] = array('x' => 187, 'y' => $meisai_top, 'width' => 22, 'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 201, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 単価
        $set_arr[] = array('x' => 252, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 金額
        $set_arr[] = array('x' => 301, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 付帯・割引
        $set_arr[] = array('x' => 351, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 割引金額
        $set_arr[] = array('x' => 401, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'type' => 'num'); // 請求金額
        $set_arr[] = array('x' => 447, 'y' => $meisai_top, 'width' => 25, 'height' => 15, 'align' => 'C'); // 税
        $set_arr[] = array('x' => 464, 'y' => $meisai_top, 'width' => 25, 'height' => 15, 'align' => 'C'); // サ
        $set_arr[] = array('x' => 486, 'y' => $meisai_top, 'width' => 75, 'height' => 15, 'align' => 'L'); // 摘要
        $set_arr[] = array('x' => 301, 'y' => $meisai_top, 'width' => 50, 'height' => 15); // コースを含む表示用 


        $breakKey = '';
        $dai_bunrui_nm = '';
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total = 0;
        $row_arr = array();
        $chuCount = 0;  // 中分類毎にカウント
        $itemAry = array();
        $beforeRow = null;
        $kbn_nm = '';
        $shokei_ippan = 0; // 項目ごとの金額小計
        $shokei_nebiki1 = 0;// 項目ごとの互助会値引き小計
        $shokei_nebiki2 = 0;// 項目ごとの割引小計

//        $chubunrui_cd_a = self::CHU_BUNRUI_CD_A;
//        if ($data_kbn == 2){
//            $chubunrui_cd_a = self::CHU_BUNRUI_CD_H_B;
//        }
        // 【A】葬送儀礼費用基本プランの項目
        $kainNo = $this->getKainNo($db, $seko_no, 1);
		if (count($kainNo) > 0) {
	        $itemAry[self::DAI_BUNRUI_1][] = self::$msi_name_gojokai;
		//	$itemAry[$chubunrui_cd_a][] = '('.$kainNo[0]['kain_no'].')';
                    $i = 0;
                    while ($i < count($kainNo)) { 
                        $itemAry[self::DAI_BUNRUI_1][] = '('.$kainNo[$i]['kain_no'].')';
                        $i++;
                    }                
		}

//        // 【E】飲食費（壇払い）の項目
//        $kainNo = $this->getKainNo($db, $seko_no, 3);
//        if (count($kainNo) > 0) $itemAry[self::DAI_BUNRUI_3][] = '('.$kainNo[0]['kain_no'].')';

        // 施行コース付帯品詳細フラグ
        $futai_flg = true;
        // 施行コース外品目詳細
        $plan_out_flg = true;
        $plan_out_sum_flg = true;
        // 小計フラグ
        $sum_flg = true;
        $print_g2_juchu_prc_sum = 0;
        $print_g2_gojokai_nebiki_prc_sum = 0;
        $print_g2_nebiki_prc_sum = 0;
        $print_g2_seikyu_prc_sum = 0;

        $print_g3_juchu_prc_sum = 0;
        $print_g3_gojokai_nebiki_prc_sum = 0;
        $print_g3_nebiki_prc_sum = 0;
        $print_g3_seikyu_prc_sum = 0;

        $print2_1_count = 0;
        $print3_1_count = 0;
        
        $ryori_syokei = 0;
        $ryori_total = 0;
        $ryori_nebiki1 = 0;
        $ryori_nebiki2 = 0;
        $old_shohin_kbn = "";
        
        $plan_sum = 0;
        
        $koumoku = "";
        
        $recMsi = DataMapper_Pdf1101::findMsi3( $db, array( "uri_den_no" => $uri_den_no, "seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub ), $print_kbn );
        $gojokai_kbn = DataMapper_Pdf0113::getGojokaiKbn($seko_no);
        // グレードアップ取得
        $gradeUpShohinCd = DataMapper_Pdf0113::getGradeUp($seko_no,$gojokai_kbn);
        //DataMapper_Pdf0113::adjMeisaiData($recMsi, $gojokai_kbn);
        $print2_count = DataMapper_Pdf1101::getSyokeiGroupCount($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, 'syoukei_group_cd' => '2','dai_bunrui_cd' => '0010'));
        $print3_count = DataMapper_Pdf1101::getSyokeiGroupCount($db, array("seko_no" => $seko_no, "data_kbn" => $data_kbn, "seko_no_sub" => $seko_no_sub, 'syoukei_group_cd' => '3','dai_bunrui_cd' => '0010'));
        foreach ($recMsi as $value) {
            // ブレーク
            if ($breakKey != $value['dai_bunrui_cd']) {
                if (count($row_arr) > 1) {
                    if ($shokei_nebiki1 == 0) {
                        $shokei_nebiki1 = null;
                    }
                    if ($shokei_nebiki2 == 0) {
                        $shokei_nebiki2 = null;
                    }
                    if($breakKey != '0010'){
                        // 飲食小計
                        if($breakKey ==='0030' && $koumoku !== $value['koumoku'] && $value['record_kbn'] !== 1 && !empty($koumoku)){
                            $row_arr[] = array('　　 ' . $koumoku . '小計', null, null, null, null, null, null, $ryori_syokei, $ryori_nebiki1, $ryori_nebiki2, $ryori_total, null, null);
                        }
                        $row_arr[] = array('　　' . $dai_bunrui_nm . '小計', null,null, null, null, null, null, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, null, null);
                    }
                    // 合計
//                    $row_arr[] = null;
//                    $row_arr[] = array('【' . $dai_bunrui_nm . '合計】', null, null, null, null, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, null, null);
                    $sumArr[$breakKey]['name'] = $dai_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
//                    $row_arr[] = null;
                }
                //if($value['dai_bunrui_cd']  == '0070') $value['dai_bunrui_nm'] = 'サービス券';// 施行申込書
                // 項目
                $row_arr[] = array('◆' .$value['dai_bunrui_nm'] . '◆', null, null,null, null, null, null, null, null, null, null, null, null);

                // 【C】返礼品
//                if ($value['dai_bunrui_cd'] == self::DAI_BUNRUI_2) {
//                    $kain_no_ary = $this->getKainNo($db, $seko_no, 2);
//                    foreach ($kain_no_ary as $kain_no) {
//                        $row_arr[] = array('　　('.$kain_no['kain_no'].')');
//                    }
//                }

                $breakKey = $value['dai_bunrui_cd'];
                $dai_bunrui_nm = $value['dai_bunrui_nm'];
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $chuCount = 0;
                
                $shokei_ippan = 0; // 項目ごとの金額小計
                $shokei_nebiki1 = 0; // 項目ごとの互助会値引き小計
                $shokei_nebiki2 = 0; // 項目ごとの割引小計
            }

            $gojokai_nebiki_prc = $value['gojokai_nebiki_prc']; // 付帯割引
            $nebiki_prc = $value['nebiki_prc']; // 割引金額
            $seikyu_prc = $value['uri_prc'] + $gojokai_nebiki_prc + $nebiki_prc; // 請求金額
            if ($gojokai_nebiki_prc == 0) {
                $gojokai_nebiki_prc = null;
            }
            if ($nebiki_prc == 0) {
                $nebiki_prc = null;
            }
            // 印刷グループの小計
            // 固定文言
            if($value['syoukei_group_cd'] == '2' && $futai_flg){
                $row_arr[] = array("【施行コース付帯品詳細】", null,null, null, null, null, null, null, null, null, null, null, null);
                $futai_flg = false;
                $print_g2_juchu_prc_sum += $value['uri_prc'];
                $print_g2_gojokai_nebiki_prc_sum += $gojokai_nebiki_prc;
                $print_g2_nebiki_prc_sum += $nebiki_prc;
                $print_g2_seikyu_prc_sum += $seikyu_prc;
                $print2_1_count++;
            }else if($value['syoukei_group_cd'] == '2'){
                $print_g2_juchu_prc_sum += $value['uri_prc'];
                $print_g2_gojokai_nebiki_prc_sum += $gojokai_nebiki_prc;
                $print_g2_nebiki_prc_sum += $nebiki_prc;
                $print_g2_seikyu_prc_sum += $seikyu_prc;
                $print2_1_count++;
            }else
            if($value['syoukei_group_cd'] == '3' && $plan_out_flg){
                // フラン付帯費用小計
                $row_arr[] = array("【施行コース外品目詳細】", null, null,null, null, null, null, null, null, null, null, null, null);
                $plan_out_flg = false;
                $print_g3_juchu_prc_sum += $value['uri_prc'];
                $print_g3_gojokai_nebiki_prc_sum += $gojokai_nebiki_prc;
                $print_g3_nebiki_prc_sum += $nebiki_prc;
                $print_g3_seikyu_prc_sum += $seikyu_prc;
                $print3_1_count++;
            }else if($value['syoukei_group_cd'] == '3'){
                $print_g3_juchu_prc_sum += $value['uri_prc'];
                $print_g3_gojokai_nebiki_prc_sum += $gojokai_nebiki_prc;
                $print_g3_nebiki_prc_sum += $nebiki_prc;
                $print_g3_seikyu_prc_sum += $seikyu_prc;
                $print3_1_count++;
            }
            
            // プラン内
            $plan = "";
            if($value['service_kbn'] === '0'){
                $plan = "○";
            }else if($value['service_kbn'] === '1'){
                $plan = "◎";
            }
            // グレードアップ
            $greade = "";
            foreach ($gradeUpShohinCd as $shohinCd) {
                if($shohinCd['shohin_cd'] == $value['shohin_cd'] && $value['service_kbn'] !== '9'){
                    $greade = "○";
                    break;
                }
            }
            // 追加分ドライアイス
            if($value['shohin_cd'] == '1001056'){
                $ice_prc = $value['uri_prc'] - $value['uri_tnk'];
                if($ice_prc > 0){
                    $greade = "○";
                }
            }
            // 飲食
            if($value['dai_bunrui_cd'] === '0030'){
                // 飲食小計
                if($koumoku !== $value['koumoku'] && $value['record_kbn'] !== 1 && !empty($koumoku)){
                    $row_arr[] = array('　　 ' . $koumoku . '小計', null, null, null, null, null, null, $ryori_syokei, $ryori_nebiki1, $ryori_nebiki2, $ryori_total, null, null);
                }
                if($old_shohin_kbn !== $value['shohin_kbn']){
                    $koumoku = $value['koumoku'];
                    $row_arr[] = array("【" . $koumoku . "】", null, null,null,null, null, null, null, null, null, null, null, null);
                    $ryori_syokei = 0;
                    $ryori_total = 0;
                    $ryori_nebiki1 = 0;
                    $ryori_nebiki2 = 0;
                }
                $ryori_syokei  += $value['uri_prc'];
                $ryori_nebiki1 += $value['gojokai_nebiki_prc'];
                $ryori_nebiki2 += $value['nebiki_prc'];
                $ryori_total   += $value['uri_prc'] + $value['gojokai_nebiki_prc'] + $value['nebiki_prc'];
                $old_shohin_kbn = $value['shohin_kbn'];
            }            
            $shohin_nm = '　' . $value['shohin_nm'];

            // 軽減税率対応  keigen
            if ( $this->_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    $this->_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['zei_disp'] = $value['zei_disp'] . '＊';
                }
            }

            // 印刷グループが１の商品のみ表示
            if($value['print_group_cd'] != '1'){
                if($value['service_kbn'] === '9'){
                    $uri_prc = $value['uri_prc'];
                    if(isset($gojokai_nebiki_prc)){
                        $uri_prc += $gojokai_nebiki_prc;
                    }
                    $row_arr[] = array($shohin_nm, null, null,$plan,$greade, $value['juchu_suryo'], null, null, $uri_prc, $nebiki_prc, null, $value['zei_disp'], $value['hoshi_disp'],$value['shohin_tkiyo_nm']);
                    $plan_nebiki = $nebiki_prc;
                    $plan_sum = $uri_prc;
                }else if($value['service_kbn'] === '1'){
                    $row_arr[] = array($shohin_nm, null, null,$plan,$greade, $value['juchu_suryo'], $value['uri_tnk'], $value['uri_prc'], null, $nebiki_prc, $seikyu_prc, $value['zei_disp'], $value['hoshi_disp'],$value['shohin_tkiyo_nm'], "コースに含む");
                }else{
                    if(!empty($gojokai_nebiki_prc)){
                        $row_arr[] = array($shohin_nm, null, null,$plan,$greade, $value['juchu_suryo'], $value['uri_tnk'], $value['uri_prc'], null, $nebiki_prc, $seikyu_prc, $value['zei_disp'], $value['hoshi_disp'],$value['shohin_tkiyo_nm'], "コースに含む");
                    }else{
                        $row_arr[] = array($shohin_nm, null, null,$plan,$greade, $value['juchu_suryo'], $value['uri_tnk'], $value['uri_prc'], $gojokai_nebiki_prc, $nebiki_prc, $seikyu_prc, $value['zei_disp'], $value['hoshi_disp'],$value['shohin_tkiyo_nm']);
                    }
                }
            }
            if($print2_1_count == $print2_count && !$futai_flg && $print2_count > 0){
                if(isset($plan_nebiki)){
                    $print_g2_nebiki_prc_sum += $plan_nebiki;
                }
                $row_arr[] = array("　　施行コース付帯品小計", null, null, null, null, null, null, $print_g2_juchu_prc_sum, $plan_sum, $print_g2_nebiki_prc_sum, $print_g2_seikyu_prc_sum, null);
                $futai_flg = true;
                $this->_planSum = $print_g2_juchu_prc_sum - $print_g2_seikyu_prc_sum;
            }
            if($print3_1_count == $print3_count && $plan_out_sum_flg && $print3_count > 0){
                $row_arr[] = array("　　施行コース外品目詳細小計", null, null, null, null, null, null, $print_g3_juchu_prc_sum, $print_g3_gojokai_nebiki_prc_sum, $print_g3_nebiki_prc_sum, $print_g3_seikyu_prc_sum, null);
                $plan_out_sum_flg = false;
            }
            
            // 葬送儀礼
            if ($value['dai_bunrui_cd'] == self::DAI_BUNRUI_1) {
                $sumArr[self::SUMID_A_PLAN]['sum'] += $value['uri_prc']; // 基本費用
                $sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] += $value['gojokai_nebiki_prc']; // 付帯特典
                $sumArr[self::SUMID_A_NEBIKI_PRC]['sum'] += $value['nebiki_prc']; // 割引金額
            }
            if ($value['print_group_cd'] == '99') { // 枕花は供花・供物に加算
                $total_kyoka+= $value['uri_prc'] + $gojokai_nebiki_prc + $nebiki_prc;
            } else {
                $total_ippan += $value['uri_prc'] ;
                $total_tokuten += $gojokai_nebiki_prc + $nebiki_prc;
                $total += $seikyu_prc;
            }
            $shokei_ippan += $value['uri_prc']; // 項目ごとの金額小計
            $shokei_nebiki1 += $value['gojokai_nebiki_prc']; // 項目ごとの互助会値引き小計
            $shokei_nebiki2 += $value['nebiki_prc']; // 項目ごとの割引小計
            $chuCount++;
            $beforeRow = $value;
        }
        if (count($recMsi) > 0) {
            // 合計
//            $row_arr[] = null;
            if ($shokei_nebiki1 == 0) {
                $shokei_nebiki1 = null;
            }
            if ($shokei_nebiki2 == 0) {
                $shokei_nebiki2 = null;
            }
            if($breakKey != '0010'){
                if($breakKey ==='0030' && !empty($koumoku)){
                    $row_arr[] = array('　　 ' . $koumoku . '小計', null, null, null, null, null, null, $ryori_syokei, $ryori_nebiki1, $ryori_nebiki2, $ryori_total, null, null);
                }
                $row_arr[] = array('　　' . $dai_bunrui_nm . '小計', null, null,null, null, null, null, $shokei_ippan, $shokei_nebiki1, $shokei_nebiki2, $total, null, null);
            }
            $rec = $recMsi[count($recMsi)-1];
            $sumArr[$rec['dai_bunrui_cd']]['name'] = $rec['dai_bunrui_nm'];
            $sumArr[$rec['dai_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['dai_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['dai_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['dai_bunrui_cd']]['sum_k'] = $total_kyoka;
        }

        $pdfObj->write_table($set_arr, $meisai_row_height, $row_arr, $meisai_row_count, __DIR__ . '/' . self::$sourceFileNameDetail[$this->getSourceFileIndex($data_kbn)]);

        return $sumArr;
    }
    
    /**
     * 明細出力(請求書)
     * 
     * @param type $pdfObj
     * @param type $db
     * @param type $uri_den_no
     * @param type $seko_no
     * @param type $data_kbn
     * @param type $seko_no_sub
     * @param type $print_kbn
     * @return array 明細金額を集計した配列
     */
    private function outDataDetail02($pdfObj, $db, $uri_den_no, $seko_no, $data_kbn, $seko_no_sub, $print_kbn)
    {
        static $meisai_top = 95;
        static $meisai_row_height = 20.3;
        static $meisai_row_count = 34;

        // 明細金額を集計した配列
        $sumArr = array();
        $sumArr[self::SUMID_A_PLAN] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_PLAN]);
        $sumArr[self::SUMID_A_GOJOKAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAI]);
        $sumArr[self::SUMID_A_GOJOKAINEBIKI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAINEBIKI]);
        $sumArr[self::SUMID_A_NEBIKI_PRC] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_NEBIKI_PRC]);
        $sumArr[self::SUMID_A_GOJOKAIGAI] = array('sum' => 0, 'name' => self::$sum_name[self::SUMID_A_GOJOKAIGAI]);

        $set_arr[] = array('x' =>  30, 'y' => $meisai_top, 'width' => 190, 'height' => 15); // 品名
        $set_arr[] = array('x' => 220, 'y' => $meisai_top, 'width' => 50,  'height' => 15, 'type' => 'num'); // 数量
        $set_arr[] = array('x' => 270, 'y' => $meisai_top, 'width' => 65,  'height' => 15, 'type' => 'num'); // 単価
        $set_arr[] = array('x' => 340, 'y' => $meisai_top, 'width' => 80,  'height' => 15, 'type' => 'num'); // 金額
        $set_arr[] = array('x' => 420, 'y' => $meisai_top, 'width' => 65,  'height' => 15, 'type' => 'num'); // 割引金額
        $set_arr[] = array('x' => 485, 'y' => $meisai_top, 'width' => 85, 'height' => 15, 'type' => 'num'); // 請求金額
        $set_arr[] = array('x' => 572, 'y' => $meisai_top, 'width' => 20, 'height' => 15);      // 軽減税率対象マーク keigen
        $set_arr[] = array('x' => 495, 'y' => $meisai_top, 'width' => 20, 'height' => 15);      // 奉仕料マーク ※

        $breakKey = '';
        $chu_bunrui_nm = '';
        $row_arr = array();
        $row_arr_henpin = array();
        $total = 0;
        $total_ippan = 0;
        $total_tokuten = 0;
        $total_kyoka = 0;
        $total_tax = 0;
        $total_tax_in = 0;
        $shokei_ippan = 0; // 項目ごとの金額小計
        $shokei_nebiki = 0;// 項目ごとの値引き小計

        $recMsi = DataMapper_Pdf1101::findMsi($db
					    , array("uri_den_no" => $uri_den_no
						  , "seko_no" => $seko_no
						  , "data_kbn" => $data_kbn
						  , "seko_no_sub" => $seko_no_sub
						  , '__etc_orderby' => array('dai_bunrui_cd ASC'
									    ,'chu_bunrui_cd ASC'
									    ,'mitumori_print_seq ASC'
									    ,'disp_no ASC'
									    )
						    )
					    , $print_kbn
				    );
        foreach ($recMsi as $value) {
            // 中分類が切替る場合は必要に応じて見出しと合計の追加を行う
            if ($breakKey != $value['chu_bunrui_cd']) {
                if (count($row_arr) > 1) {
                    if ($shokei_nebiki == 0) {
                        $shokei_nebiki = null;
                    }
                    $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, $shokei_ippan, $shokei_nebiki, $total);
                    $sumArr[$breakKey]['name'] = $chu_bunrui_nm;
                    $sumArr[$breakKey]['sum'] = $total;
                    $sumArr[$breakKey]['sum_i'] = $total_ippan;
                    $sumArr[$breakKey]['sum_t'] = $total_tokuten;
                    $sumArr[$breakKey]['sum_k'] = $total_kyoka;
                    $sumArr[$breakKey]['tax'] = $total_tax;
                    $sumArr[$breakKey]['tax_in'] = $total_tax_in;
                }
                $row_arr[] = array('◆' .$value['chu_bunrui_nm'] . '◆', null, null, null, null, null);
                
                $breakKey = $value['chu_bunrui_cd'];
                $chu_bunrui_nm = $value['chu_bunrui_nm'];                
                $total = 0;
                $total_ippan = 0;
                $total_tokuten = 0;
                $total_kyoka = 0;
                $total_tax	= 0;
                $total_tax_in	= 0;
                
                $shokei_ippan = 0; // 項目ごとの金額小計
                $shokei_nebiki = 0;// 項目ごとの値引き小計
            }

            // 付帯割引
            $gojokai_nebiki_prc = $value['gojokai_nebiki_prc']; 
            // 割引金額
            $nebiki_prc = $value['nebiki_prc'];
            // 請求金額
            $seikyu_prc = $value['uri_prc'] + $gojokai_nebiki_prc + $nebiki_prc; 
            // 消費税
            $total_tax += $value['out_zei_prc'];
            $total_tax_in += $value['in_zei_prc'];
            
            if ($gojokai_nebiki_prc == 0) {
                $gojokai_nebiki_prc = null;
            }
            if ($nebiki_prc == 0) {
                $nebiki_prc = null;
            }
            $nebiki_sum = $gojokai_nebiki_prc + $nebiki_prc;
            if ($nebiki_sum == 0) {
                $nebiki_sum = null;
            }
            $shohin_nm = '　' . $value['shohin_nm'];

            // 軽減税率対応  keigen
            $value['reduced_tax_rate_txt'] = '';
            if ( $this->_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    $this->_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['reduced_tax_rate_txt'] = '＊';
                }
            }
            // 奉仕料マーク
            $hoshi_disp = "";
            if(isset($value['hoshi_disp']) && strlen($value['hoshi_disp']) > 0){
                $hoshi_disp = "※";
            }
            $row_arr[] = array($shohin_nm
				, $value['juchu_suryo']
				, $value['uri_tnk']
				, $value['uri_prc']
				, $nebiki_sum
                                , $seikyu_prc
                                , $value['reduced_tax_rate_txt'] // keigen
                                , $hoshi_disp); 
                if($value['chu_bunrui_cd'] == self::CHU_BUNRUI_CD_NIIBON_2){
                    $row_arr_henpin[] = array($shohin_nm
				, null
				, $value['uri_tnk']
				, null
				, null
                                , null
                                , $value['reduced_tax_rate_txt'] // keigen
                                , null);
                }
            // 葬送儀礼
            if ($value['dai_bunrui_cd'] == self::DAI_BUNRUI_1) {
                // 基本費用
                $sumArr[self::SUMID_A_PLAN]['sum'] += $value['uri_prc'];
                // 付帯特典
                $sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] += $value['gojokai_nebiki_prc'];
                // 割引金額
                $sumArr[self::SUMID_A_NEBIKI_PRC]['sum'] += $value['nebiki_prc'];
            }
            
            // 枕花は供花・供物に加算する
            if ($value['print_group_cd'] == '99') {
                $total_kyoka += $value['uri_prc'] + $gojokai_nebiki_prc + $nebiki_prc;
            } else {
                $total_ippan += $value['uri_prc'] ;
                $total_tokuten += $gojokai_nebiki_prc + $nebiki_prc;
                $total += $seikyu_prc;
            }
            $shokei_ippan += $value['uri_prc']; // 項目ごとの金額小計
            $shokei_nebiki += $gojokai_nebiki_prc + $nebiki_prc; // 項目ごとの値引き小計
        }

        if (count($recMsi) > 0) {
            if ($shokei_nebiki == 0) {
                $shokei_nebiki = null;
            }
            // 最後の中分類の合計を追加
            $row_arr[] = array('【' . $chu_bunrui_nm . '合計】', null, null, $shokei_ippan, $shokei_nebiki, $total);
            $rec = $recMsi[count($recMsi)-1];
            $sumArr[$rec['chu_bunrui_cd']]['name'] = $rec['chu_bunrui_nm'];
            $sumArr[$rec['chu_bunrui_cd']]['sum'] = $total;
            $sumArr[$rec['chu_bunrui_cd']]['sum_i'] = $total_ippan;
            $sumArr[$rec['chu_bunrui_cd']]['sum_t'] = $total_tokuten;
            $sumArr[$rec['chu_bunrui_cd']]['sum_k'] = $total_kyoka;
            $sumArr[$rec['chu_bunrui_cd']]['tax']   = $total_tax;
            $sumArr[$rec['chu_bunrui_cd']]['tax_in']   = $total_tax_in;
        }
        if($this->_moushiKbnHouji == 8){
            if (count($row_arr_henpin) > 0) {
                $row_arr_henpin_title[] = array('◆返品◆'
                    , null
                    , null
                    , null
                    , null
                    , null
                    , null
                    , null);

                $row_arr = array_merge($row_arr, $row_arr_henpin_title, $row_arr_henpin);
                $row_arr[] = array("【返品合計】"
                    , null
                    , null
                    , null
                    , null
                    , null
                    , null
                    , null);
            }
        }
        $pdfObj->write_table($set_arr, $meisai_row_height, $row_arr, $meisai_row_count, __DIR__ . '/' . self::$sourceFileNameDetail[$this->getSourceFileIndex($data_kbn)]);
        // 返品明細
//        $this->MSI_PAGE_NUM = $pdfObj->getNumPages();	// 現在の総ページ数
//        if($this->_moushiKbnHouji == 8){
//            if(count($row_arr_henpin) > 0){
//                $row_arr_henpin[] = array("【返品合計】"
//                                    , null
//                                    , null
//                                    , null
//                                    , null
//                                    , null
//                                    , null
//                                    , null);
//                $pdfObj->write_table($set_arr, $meisai_row_height, $row_arr_henpin, $meisai_row_count, __DIR__ . '/' . self::$sourceFileHenpin);
//            }
//        }
        return $sumArr;
    }

    /**
     * 明細を出力（単品・別注品）
     * 
     * @param type $pdfObj
     * @param type $db
     * @param type $kaisyalogo
     * @param type $seko_no
     * @param type $data_kbn
     * @param type $tax_kbn
     * @param type $sumAry
     * @return なし
     */
    //private function outDataDetail03($pdfObj, $db, $uri_den_no, $data_kbn, $tax_kbn, $sumAry)
    private function outDataDetail03($pdfObj, $db, $uri_den_no, $data_kbn)
    {
        static $meisai_top1 = 260;
        static $meisai_top2 = 120;
        static $y1 = 10;	// 1行
        static $y1_1 = 3;	// 1行の1行目
        static $y1_2 = 17;	// 1行の2行目
        static $meisai_row_height = 37.3;
        static $meisai_row_count1 = 8;
        static $meisai_row_count2 = 18;

        $set_arr[] = array('x' =>  45, 'y' => $meisai_top1+$y1,   'width' =>  20, 'height' => 15, 'type' => 'num'); // 0 No.
        $set_arr[] = array('x' =>  75, 'y' => $meisai_top1+$y1,   'width' => 250, 'height' => 15);   // 1 品名（摘要なし）
        $set_arr[] = array('x' =>  75, 'y' => $meisai_top1+$y1_1, 'width' => 250, 'height' => 15);   // 2 品名（摘要あり）
        $set_arr[] = array('x' =>  75, 'y' => $meisai_top1+$y1_2, 'width' => 250, 'height' => 15);   // 3 摘要
        $set_arr[] = array('x' => 335, 'y' => $meisai_top1+$y1, 'width' =>  50, 'height' => 15, 'type' => 'num');   // 4数量
        $set_arr[] = array('x' => 390, 'y' => $meisai_top1+$y1, 'width' =>  37, 'height' => 15, 'align' => 'C');    // 5単位
        $set_arr[] = array('x' => 430, 'y' => $meisai_top1+$y1, 'width' =>  60, 'height' => 15, 'type' => 'num');   // 6単価
        $set_arr[] = array('x' => 500, 'y' => $meisai_top1+$y1, 'width' =>  70, 'height' => 15, 'type' => 'num');   // 7金額（値引きなし）
        $set_arr[] = array('x' => 500, 'y' => $meisai_top1+$y1_2, 'width' =>  70, 'height' => 15, 'type' => 'num'); // 8金額（値引き後）
        $set_arr[] = array('x' => 492, 'y' => $meisai_top1+$y1_1, 'width' =>  27, 'height' => 15, 'font_size' => 7);// 9（値引）
        $set_arr[] = array('x' => 515, 'y' => $meisai_top1+$y1_1, 'width' =>  55, 'height' => 15, 'type' => 'num'); // 10 値引額
	$set_arr[] = array('x' => 31, 'y' => $meisai_top1 + $y1, 'width' => 20, 'height' => 15); // 軽減税率対象マーク  keigen

        $row_arr = array();
        $rec = DataMapper_Pdf1101::findMsi03( $db, array( "uri_den_no" => $uri_den_no ) );
        for ($index = 0; $index < count($rec); $index++) {
            $value = $rec[$index];
            // 1行目出力
            $arr = array();
            $arr[0] = $index + 1;
            $edit_flg = 0;  
            if (strlen($value['shohin_tkiyo_nm']) > 0) {
                // 商品摘要あり
                $arr[1] = null;
                $arr[2] = $value['shohin_nm'];
                $arr[3] = $value['shohin_tkiyo_nm'];
                $edit_flg = 1;  
            } else if (strlen($value['nafuda_nm']) > 0) {
                // 名札あり
                $arr[1] = null;
                $arr[2] = $value['shohin_nm'];
                $arr[3] = $value['nafuda_nm'];
                $edit_flg = 2;  
            } else if (strlen($value['msi_biko1']) > 0) {
                // 明細備考１あり
                $arr[1] = null;
                $arr[2] = $value['shohin_nm'];
                $arr[3] = $value['msi_biko1'];
                $edit_flg = 3;  
            } else if (strlen($value['msi_biko2']) > 0) {
                // 明細備考１あり
                $arr[1] = null;
                $arr[2] = $value['shohin_nm'];
                $arr[3] = $value['msi_biko2'];
                $edit_flg = 4;  
            }  else {   
                // なし
                $arr[1] = $value['shohin_nm'];
                $arr[2] = null;
                $arr[3] = null;
                $edit_flg = 5;  
            }
            $arr[4] = $value['juchu_suryo'];
            $arr[5] = $value['tani_nm'];
            $arr[6] = $value['uri_tnk'];
            if (isset($value['nebiki_prc']) && $value['nebiki_prc'] != 0) {
                $arr[7] = NULL;
                $arr[8] = $value['uri_prc'] + $value['nebiki_prc'];
                $arr[9] = '（値引）';
                $arr[10] = $value['nebiki_prc'];
            } else {
                $arr[7] = $value['uri_prc'];
                $arr[8] = NULL;
                $arr[9] = NULL;
                $arr[10] = NULL;
            }

            // 軽減税率対応  keigen
            $value['reduced_tax_rate_txt'] = '';
            if ( $this->_isKeigenAppliedCtxt() ) {
                if ( isset($value['reduced_tax_rate']) && $value['reduced_tax_rate'] == 2 ) { // 軽減税率適用
                    $this->_is_reduced_tax_rate_row_exists = true; // 軽減税率適用行が存在した
                    $value['reduced_tax_rate_txt'] = '＊';
                }
            }
            $arr[11] = $value['reduced_tax_rate_txt'];
            $row_arr[] = $arr;
            $out_flg   = 1;
            if ($edit_flg >= 1) {    // 商品摘要の編集まであり  
                if (strlen($value['nafuda_nm']) > 0
                ||  strlen($value['msi_biko1']) > 0    
                ||  strlen($value['msi_biko2']) > 0) {
                    // 2行目出力
                    $arr = array();
                    $arr[0] = null;
                    if ($edit_flg == 1) { // 商品摘要編集あり
                        if (strlen($value['nafuda_nm']) > 0) {
                            if (strlen($value['msi_biko1']) > 0) {
                                $arr[1] = null;
                                $arr[2] = $value['nafuda_nm'];
                                $arr[3] = $value['msi_biko1'];
                            } else if (strlen($value['msi_biko2']) > 0) {
                                $arr[1] = null;
                                $arr[2] = $value['nafuda_nm'];
                                $arr[3] = $value['msi_biko2'];
                            } else {
                                $arr[1] = $value['nafuda_nm'];
                                $arr[2] = null;
                                $arr[3] = null;
                            }    
                        } else if (strlen($value['msi_biko1']) > 0) {
                            if (strlen($value['msi_biko2']) > 0) {
                                $arr[1] = null;
                                $arr[2] = $value['msi_biko1'];
                                $arr[3] = $value['msi_biko2'];
                            } else {     
                                $arr[1] = $value['msi_biko1'];
                                $arr[2] = null;
                                $arr[3] = null;
                            }    
                        } else {
                            $arr[1] = $value['msi_biko2'];
                            $arr[2] = null;
                            $arr[3] = null;
                        }   
                    }
                    if ($edit_flg == 2) { // 名札編集あり
                        if (strlen($value['msi_biko1']) > 0) {
                            if (strlen($value['msi_biko2']) > 0) {
                                $arr[1] = null;
                                $arr[2] = $value['msi_biko1'];
                                $arr[3] = $value['msi_biko2'];
                            } else {     
                                $arr[1] = $value['msi_biko1'];
                                $arr[2] = null;
                                $arr[3] = null;
                            }    
                        } else if (strlen($value['msi_biko2']) > 0) {
                            $arr[1] = $value['msi_biko2'];
                            $arr[2] = null;
                            $arr[3] = null;
                        } else {
                            $out_flg   = 0;
                            $arr[1] = null;
                            $arr[2] = null;
                            $arr[3] = null;
                        }
                    }    
                    if ($edit_flg == 3) { // 明細備考１編集あり
                        if (strlen($value['msi_biko2']) > 0) {
                            $arr[1] = $value['msi_biko2'];
                            $arr[2] = null;
                            $arr[3] = null;
                        } else {     
                            $out_flg   = 0;
                            $arr[1] = null;
                            $arr[2] = null;
                            $arr[3] = null;
                        }    
                    }    
                    $arr[4] = null;
                    $arr[5] = null;
                    $arr[6] = null;
                    $arr[7] = NULL;
                    $arr[8] = null;
                    $arr[9] = null;
                    $arr[10] = null;
                    if ($out_flg   == 1) {
                        $row_arr[] = $arr;
                    }    
                }
            }    
		}

        // 明細（表紙）
        $row_arrWk = array_slice($row_arr, 0, $meisai_row_count1);
        $pdfObj->write_table_simple($set_arr, $meisai_row_height, $row_arrWk);
            // 明細１枚の場合、明細計を出力
        // 常に１ページ目に合計を印刷する。2014/10/26 Kayo
        // 内税の場合、消費税額の見出しに（内）を出力
//        if ($tax_kbn == 1) {
//           $pdfObj->write_string(array('x' => 465, 'y' => 571, 'width' => 30, 'height' => 15), '（内）');
//        }

        // 合計
        // 　外税：売上金額合計＋消費税額
        // 　非課税・内税：売上金額合計
//        if ($tax_kbn == 2) {
//            $total = $sumAry['uri_prc_sum'] + $sumAry['zei_prc'];
//        } else {
//            $total = $sumAry['uri_prc_sum'];
//        }
//        $pdfObj->write_num(array('x' => 500, 'y' => 558, 'width' => 70, 'height' => 15), $sumAry['uri_prc_sum']);  // 小計
//        $pdfObj->write_num(array('x' => 500, 'y' => 572, 'width' => 70, 'height' => 15), $sumAry['zei_prc']);    // 消費税額
//        $pdfObj->write_num(array('x' => 500, 'y' => 584, 'width' => 70, 'height' => 15, 'font_size' => 14), $total);    // 御請求金額

        // 消費税金額の内訳出力  keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $this->outZeiUchiwake03($pdfObj, $uri_den_no);
        }

        if (count($row_arr) <= $meisai_row_count1) {
        } else {
            // 明細（２ページ目～）
            for ($index = 0; $index < count($set_arr); $index++) {
                $set_arr[$index]['y'] = $set_arr[$index]['y'] - $meisai_top1 + $meisai_top2;
            }

            $row_arrWk = array_slice($row_arr, $meisai_row_count1);
            $pdfObj->write_table($set_arr, $meisai_row_height, $row_arrWk, $meisai_row_count2, __DIR__ . '/' . self::$sourceFileNameDetail[$this->getSourceFileIndex($data_kbn)]);
            
            // 確認用のグリッドを出力
            //$pdfObj->test_line_out(600, 1000);
        }
    }

    /**
     * 表紙の金額を出力
     * 
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    public function outDataFaceSum01($pdfObj, $sumArr, $coseNm, $recWk, $seko_no)         
    {
        static $row_height = 14;
        static $item_indent = '　　';
        // 施行情報
        $recSekoKihon =  DataMapper_SekoKihon::find2($seko_no);

        //互助会複数対応
        if (count($recWk) > 1) {
            $coseNm = "";
            $coseNm_bk = "";
            $coseNm_dub = null;
            $cose_ary = array();
            $i = 0;
            while ($i < count($recWk)) {                
                if (isset($recWk[$i]['gojokai_cose_nm'])){
                    $pos = array_search($recWk[$i]['gojokai_cose_nm'], $cose_ary);                     
                    
                    //同コースが2回目の場合はスキップ
                    if ($coseNm_bk == $recWk[$i]['gojokai_cose_nm']){               
                        $coseNm_dub = $recWk[$i]['gojokai_cose_nm'];
                    }
                    //前回とコース名称が違う場合はコース名に追記する
                    else if ($coseNm_bk != $recWk[$i]['gojokai_cose_nm']){
                        if (strlen($coseNm) > 0) {
                            $coseNm = $coseNm.'＋';    
                        }
                        $coseNm = $coseNm.$recWk[$i]['gojokai_cose_nm'];                        
                    }
                    //前回分のコース名称確保
                    $coseNm_bk = $recWk[$i]['gojokai_cose_nm'];
                    $cose_ary[] = $recWk[$i]['gojokai_cose_nm'];
                }                
                $i++;
            }            
        }
        
        $set_arr[] = array('x' =>  37, 'width' => 290, 'height' => 15); // 項目
        $set_arr[] = array('x' => 293, 'width' =>  80, 'height' => 15, 'type' => 'num');    // 金額
        $set_arr[] = array('x' => 378, 'width' => 184, 'height' => 15); // 備考
        $set_arr[] = array('x' => 237, 'width' =>  90, 'height' => 15); // 項目（２列目）
        $set_arr[] = array('x1' => 33, 'y1' => -1, 'x2' => 575, 'y2' => -1, 'width' => 1, 'color' => self::$color, 'type' => 'line'); // 上線

        //
        // 葬送儀礼費用
        //
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 205;
        }

        $total1 = 0;
        $row_arr = null;
        // 葬儀プラン金額取得
        $recSougiPlan = DataMapper_Pdf0113::getSougiPlan2($seko_no,$recSekoKihon['gojokai_kbn']);
        $row_arr[] = array('葬儀プラン', $recSougiPlan['sum']);
        // プラン品目合計
        //$recPlanIn = DataMapper_Pdf0113::getPlanInSum($seko_no,$recSekoKihon['gojokai_kbn']);
        $pdfObj->write_string(array('x' => 378, 'y' => 205, 'width' => 80, 'height' => 16,),'施行コース品目合計');
        $pdfObj->write_num(array('x' => 463, 'y' => 205, 'width' => 50, 'height' => 16,),$this->_planSum);
        // グレードアップ
        $recGradeUp = DataMapper_Pdf0113::getGradeUpSum2($seko_no,$recSekoKihon['gojokai_kbn']);
        $row_arr[] = array('グレードアップ', $recGradeUp['sum']);
        // 小計（ア）
        $row_arr[] = array('　　　　　　小計（ア）', $recSougiPlan['sum']+$recGradeUp['sum']);
        $row_arr[] = array();
        // 施行コース外品目合計
        $recPlanOut = DataMapper_Pdf0113::getPlanOutSum2($seko_no,$recSekoKihon['gojokai_kbn']);
        $row_arr[] = array('施行コース外品目合計（イ）', $recPlanOut['sum']);
        // 値引き
        $row_arr[] = array('値引き（ウ）', $sumArr['a_nebiki']['sum']);

        // 空行を設定
        for ($index = count($row_arr); $index < 5; $index++) {
            $row_arr[] = null;
        }
        $row_arr[] = array();
        // 葬送儀礼費用合計
        $fld_no_syokei = null;
        $total1 = $recSougiPlan['sum'] + $recGradeUp['sum'] + $recPlanOut['sum'] + $sumArr['a_nebiki']['sum'];
        $row_arr[] = array('', $total1, null, $fld_no_syokei);

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // おもてなし費用
        //
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 350;
        }

        $total2 = 0;
        $row_arr = null;

        // 【C】返礼品
        if (isset($sumArr[self::DAI_BUNRUI_2])) {
            $value = $sumArr[self::DAI_BUNRUI_2];
            $row_arr[] = array($value['name'], $value['sum_i']);
            if ($value['sum_t'] != 0) {
                $row_arr[] = array($value['name'].'特典', $value['sum_t']);
            }
            $total2 += $value['sum'];
        }

        // 【D】飲食費
        if (isset($sumArr[self::DAI_BUNRUI_3])) {
            $value = $sumArr[self::DAI_BUNRUI_3];
            $row_arr[] = array($value['name'], $value['sum_i']);
            if ($value['sum_t'] != 0) {
                $row_arr[] = array($value['name'].'特典', $value['sum_t']);
            }
            $total2 += $value['sum'];
        }

        // 空行を設定
        for ($index = count($row_arr); $index < 4; $index++) {
            $row_arr[] = null;
        }

        // おもてなし費用合計
        $row_arr[] = array('', $total2);

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // その他費用
        //
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 460;
        }

        $total3 = 0;
        $row_arr = array();

        // 【G】別途費用
        if (isset($sumArr[self::DAI_BUNRUI_5])) {
            $value = $sumArr[self::DAI_BUNRUI_5];
            if ($value['sum'] != 0) {
                $row_arr[] = array($value['name'], $value['sum']);
                $total3 += $value['sum'];
            }
            if ($value['sum_k'] != 0) {
                $row_arr[] = array('供花・供物費用', $value['sum_k']);
                $total3 += $value['sum_k'];
            }
        }

        // 【H】値引き
        if (isset($sumArr[self::DAI_BUNRUI_7])) {
            $value = $sumArr[self::DAI_BUNRUI_7];
            $row_arr[] = array($value['name'], $value['sum']);
            $total3 += $value['sum'];
        }

        // 空行を設定
        for ($index = count($row_arr); $index < 2; $index++) {
            $row_arr[] = null;
        }

        // その他費用合計
        $row_arr[] = array('', $total3);
        $row_heigh_other = 10.5;
        $pdfObj->write_table_simple($set_arr, $row_heigh_other, $row_arr);

        //
        // 御見積金額
        //
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 528;
        }

        $row_arr = array();
        // 合計④
        $row_arr[] = array($item_indent.'合計④＝小計①＋小計②＋小計③', $total1 + $total2 + $total3);
        $total4 = $total1 + $total2 + $total3;

        // 合計④の消費税
        $row_arr[] = array($item_indent . '合計④の消費税', $sumArr['tax']['sum']);

        // 受注伝票取得
        $denpyo = DataMapper_JuchuDenpyo::findDenpyo2(Msi_Sys_DbManager::getMyDb(), array('seko_no' => $seko_no, 'data_kbn' => '1'));
        $tomo_cose = 0;
        $atokake = 0;
        $zei_sagaku = 0;
        if(count($denpyo) > 0){
            $tomo_cose = $denpyo[0]['sougi_keiyaku_prc'] * -1;   // アイパル倶楽部利用コース
            $atokake = $denpyo[0]['sougi_keiyaku_prc'] + $denpyo[0]['sougi_harai_prc']; // 後掛金
            $zei_sagaku = $denpyo[0]['sougi_zei_sagaku_prc']; // 旧税率適用差額
        }
        // 値引き額取得
        //$nebiki = $this->getNebiki($seko_no);
        $row_arr[] = array($item_indent . 'アイパル倶楽部利用コース', $tomo_cose);
        $row_arr[] = array($item_indent . '後掛金', $atokake);
        $row_arr[] = array($item_indent . '旧税率適用差額', $zei_sagaku);
        //$row_arr[] = array($item_indent . '値引き', $nebiki['sum']);
        // 空行を設定
        for ($index = count($row_arr); $index < 7; $index++) {
            $row_arr[] = null;
        }
        // 御見積金額
        $row_arr[] = array('', $total4 + $tomo_cose + $atokake + $zei_sagaku + $sumArr['tax']['sum']);

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        // 消費税金額の内訳出力     keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $this->outZeiUchiwake($pdfObj, '1'); // , $this->_p_circle_num);
        }

        return $total4 + $tomo_cose + $atokake + $zei_sagaku + $sumArr['tax']['sum'];
    }
    /**
     * 表紙の金額を出力
     * 
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    public static function outDataFaceSum01_other($pdfObj, $sumArr, $coseNm)
    {
        static $row_height = 14.2;
        static $item_indent = '　　';

        $set_arr[] = array('x' =>  50, 'width' => 290, 'height' => 15); // 項目
        $set_arr[] = array('x' => 350, 'width' =>  80, 'height' => 15, 'type' => 'num');    // 金額
        $set_arr[] = array('x' => 440, 'width' => 135, 'height' => 15); // 備考
        $set_arr[] = array('x' => 250, 'width' =>  90, 'height' => 15); // 項目（２列目）
        $set_arr[] = array('x1' => 45, 'y1' => -1, 'x2' => 575, 'y2' => -1, 'width' => 1, 'color' => self::$color, 'type' => 'line'); // 上線

        //
        // 葬送儀礼費用
        //
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 214;
        }

        $total1 = 0;
        $row_arr = null;

        // コース変更
        if ($sumArr[self::SUMID_COSECHGGAKU]['sum'] != 0) {
            $biko_cose_chg = floor($sumArr[self::SUMID_KEIYAKUGAKU]['sum'] / 10000).'万コース→'.
                floor(($sumArr[self::SUMID_KEIYAKUGAKU]['sum']+$sumArr[self::SUMID_COSECHGGAKU]['sum']) / 10000).'万コース';
        } else {
            $biko_cose_chg = null;
        }

		// 葬送儀礼費用基本プラン　小計　　　　　　　　　　　　　（ア）
		// 葬送儀礼費用基本プラン　葬送儀礼費用プラン外選択品目　（イ）
		// 葬送儀礼費用プラン外選択品目　　　　　　　　　　　　　（ウ）
		// 葬送儀礼費用　小計　　　　　　（ア）＋（イ）＋（ウ）
		// の（ア）・（イ）・（ウ）を出力する
		static $fld_no = array('（ア）', '（イ）', '（ウ）');
		$fld_no_index = 0;

        // 【A】葬送儀礼費用基本プラン
        if (isset($sumArr[self::DAI_BUNRUI_1])) {
            $value = $sumArr[self::DAI_BUNRUI_1];
//            $row_arr[] = array($value['name'], '');
            if ($sumArr[self::SUMID_A_PLAN]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_PLAN];
//                $row_arr[] = array($item_indent.$value['name'], $value['sum']);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_GOJOKAI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAI];
//                $row_arr[] = array($item_indent.$value['name'], $value['sum'], $biko_cose_chg, $coseNm);
                $total1 += $value['sum'];
            }
            if ($sumArr[self::SUMID_A_GOJOKAINEBIKI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAINEBIKI];
//                $row_arr[] = array($item_indent.$value['name'], $value['sum']);
                $total1 += $value['sum'];
            }
//            $row_arr[] = array($item_indent.'小計', $total1, null, $fld_no[$fld_no_index++]);
            if ($sumArr[self::SUMID_A_GOJOKAIGAI]['sum'] != 0) {
                $value = $sumArr[self::SUMID_A_GOJOKAIGAI];
//                $row_arr[] = array($item_indent.$value['name'], $value['sum'], null, $fld_no[$fld_no_index++], true);
                $total1 += $value['sum'];
            }
//            $row_arr[] = null;
        }

        // 【B】葬送儀礼費用プラン外選択品目
//        if (isset($sumArr[self::CHU_BUNRUI_CD_B])) {
//            $value = $sumArr[self::CHU_BUNRUI_CD_B];
// //           $row_arr[] = array($value['name'], $value['sum'], null, $fld_no[$fld_no_index++]);
//            $total1 += $value['sum'];
//        }

        // 空行を設定
 //       for ($index = count($row_arr); $index < 7; $index++) {
 //           $row_arr[] = null;
 //       }

        // 葬送儀礼費用合計
		$fld_no_syokei = null;
		for ($index = 0; $index < $fld_no_index; $index++) {
			if (isset($fld_no_syokei)) { $fld_no_syokei .= '＋'; }
			$fld_no_syokei .= $fld_no[$index];
		}
        $fld_no2 = array('計①', '計②', '計③');
        $fld_total = '';
        if($total1 != 0) {
            $kei = array_shift($fld_no2);
            $fld_total .= $kei;
            $row_arr[] = array('葬送儀礼費用　 ' . $kei, $total1, null, $fld_no_syokei);
        }

//        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // おもてなし費用
        //
//        for ($index = 0; $index < count($set_arr); $index++) {
//            $set_arr[$index]['y'] = 228;
//        }

        $total2 = 0;
//        $row_arr = null;

        // 【C】返礼品
        if (isset($sumArr[self::DAI_BUNRUI_2])) {
            $value = $sumArr[self::DAI_BUNRUI_2];
//            $row_arr[] = array($value['name'], $value['sum']);
            $total2 += $value['sum'];
        }

        // 【D】飲食費
        if (isset($sumArr[self::DAI_BUNRUI_3])) {
            $value = $sumArr[self::DAI_BUNRUI_3];
//            $row_arr[] = array($value['name'], $value['sum']);
            $total2 += $value['sum'];
        }

        // 【E】飲食費（壇払い）
//        if (isset($sumArr[self::CHU_BUNRUI_CD_E])) {
//            $value = $sumArr[self::CHU_BUNRUI_CD_E];
////            $row_arr[] = array($value['name'], $value['sum']);
//            $total2 += $value['sum'];
//        }

        // 空行を設定
//        for ($index = count($row_arr); $index < 4; $index++) {
//            $row_arr[] = null;
//        }

        // おもてなし費用合計
        if($total2 != 0) {
            $kei = array_shift($fld_no2);
            if (empty($fld_total)) {
                $fld_total = $kei;
            } else {
                $fld_total .= '＋' . $kei;
            }
            $row_arr[] = array('おもてなし費用 ' . $kei, $total2);
        }

//        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // その他費用
        //
//        for ($index = 0; $index < count($set_arr); $index++) {
//            $set_arr[$index]['y'] = 242;
//        }

        $total3 = 0;
//        $row_arr = null;

        // 【G】別途費用
        if (isset($sumArr[self::DAI_BUNRUI_5])) {
            $value = $sumArr[self::DAI_BUNRUI_5];
 //           $row_arr[] = array($value['name'], $value['sum']);
            $total3 += $value['sum'];
        }

        // 【H】値引き
        if (isset($sumArr[self::DAI_BUNRUI_7])) {
            $value = $sumArr[self::DAI_BUNRUI_7];
//            $row_arr[] = array($value['name'], $value['sum']);
            $total3 += $value['sum'];
        }

        // 空行を設定
 //       for ($index = count($row_arr); $index < 3; $index++) {
 //           $row_arr[] = null;
 //       }

        // その他費用合計
        if($total3 != 0) {
            $kei = array_shift($fld_no2);
            if (empty($fld_total)) {
                $fld_total = $kei;
            } else {
                $fld_total .= '＋' . $kei;
            }
            $row_arr[] = array('その他費用 　　' . $kei, $total3);
        }
//        $row_arr[] = array('', $total3);

//        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        //
        // 御見積金額
        //
//        for ($index = 0; $index < count($set_arr); $index++) {
//            $set_arr[$index]['y'] = 270;
//        }

        $total5 = 0;
        $total6 = 0;
        $total7 = 0;
        $total8 = 0;
//        $row_arr = null;
        // 空行を追加
        $row_arr[] = null;

        $total_kei = '合計';
        if (!empty($fld_total)) {
            $total_kei = $total_kei . '＝' . $fld_total;
        }
        // 合計④
//        $row_arr[] = array($item_indent.'合計＝計①＋計②＋計③　　　（ア）', $total1 + $total2 + $total3);
        $row_arr[] = array($item_indent. $total_kei, $total1 + $total2 + $total3 , null ,'（ア）');
        $total4 = $total1 + $total2 + $total3;

        //$total_nm = '合計金額＝合計④';
        $total_nm = '合計金額＝（ア）';
        static $circle_num = array(5 => '⑤', 6 => '⑥', 7 => '⑦', 8 => '⑧');
        $num = 5;

        // 合計④の消費税
        if (isset($sumArr['tax'])) {
            $value = $sumArr['tax'];
//            $row_arr[] = array($item_indent.'合計の消費税　　　　　　　　（イ）', $value['sum']);
            $row_arr[] = array($item_indent.'合計の消費税', $value['sum'], null , '（イ）'); 
            $total5 = $value['sum'];

            //$total_nm .= '＋'.$circle_num[$num];
            $total_nm .= '＋（イ）';
            $num++;
        }

        // 【F】立替金
        if (isset($sumArr[self::DAI_BUNRUI_6])) {
            $value = $sumArr[self::DAI_BUNRUI_6];
            //$row_arr[] = array($value['name'].'　'.$circle_num[$num], $value['sum']);
//            $row_arr[] = array($item_indent.'立替金　　　　　　　　　　　（ウ）', $value['sum']);
            $row_arr[] = array($item_indent.'立替金', $value['sum'], null , '（ウ）');
            $total6 = $value['sum'];

            //$total_nm .= '＋'.$circle_num[$num];
            $total_nm .= '＋（ウ）';
            $num++;
        }

        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            //$row_arr[] = array($item_indent.$value['name'].'　'.$circle_num[$num], $value['sum']);
//            $row_arr[] = array($item_indent.$value['name'].'　　　　　　（エ）', $value['sum']);
            $row_arr[] = array($item_indent.$value['name'], $value['sum'] , null , '（エ）');
            $total7 = $value['sum'];

            //$total_nm .= '＋'.$circle_num[$num];
            $total_nm .= '＋（エ）';
            $num++;
        }

        // 預り金（返礼品）
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            //$row_arr[] = array($item_indent.$value['name'].'　'.$circle_num[$num], $value['sum']);
//            $row_arr[] = array($item_indent.$value['name'].'　　　　　　（オ）', $value['sum']);
            $row_arr[] = array($item_indent.$value['name'], $value['sum'], null , '（オ）');
            $total8 = $value['sum'];

            //$total_nm .= '＋'.$circle_num[$num];
            $total_nm .= '＋（オ）';
            $num++;
        }

        // 合計金額
        $row_arr[] = array($item_indent.$total_nm, $total4 + $total5 + $total6 + $total7 + $total8);
        $total_sum = $total4 + $total5 + $total6 + $total7 + $total8;

        // 早期利用費
        if ($sumArr[self::SUMID_EARLYUSE]['sum'] != 0) {
            $value = $sumArr[self::SUMID_EARLYUSE];
            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
        }

        // 早期利用費の消費税
        if ($sumArr[self::SUMID_EARLYUSEZEI]['sum'] != 0) {
            $value = $sumArr[self::SUMID_EARLYUSEZEI];
            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
        }

        // 掛金残金
        if ($sumArr[self::SUMID_KAKEZAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_KAKEZAN];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }

        // 掛金残金の消費税
        if ($sumArr[self::SUMID_KAKEZANZEI]['sum'] != 0) {
            $value = $sumArr[self::SUMID_KAKEZANZEI];
            $row_arr[] = array($item_indent . $value['name'], $value['sum']);
        }
        // コース変更差額
//        if ($sumArr[self::SUMID_COSECHGGAKU]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_COSECHGGAKU];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }

//        // 互助会払込金額
//        if ($sumArr[self::SUMID_GOJOHARAI]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_GOJOHARAI];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
//
//        // 前納割引
//        if ($sumArr[self::SUMID_ZENNOWARI]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_ZENNOWARI];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }
//
//        // 掛金消費税差額
//        if ($sumArr[self::SUMID_KAKEZEISAGAKU]['sum'] != 0) {
//            $value = $sumArr[self::SUMID_KAKEZEISAGAKU];
//            $row_arr[] = array($item_indent.$value['name'], $value['sum']);
//        }

        // 空行を設定
        for ($index = count($row_arr); $index < 15; $index++) {
            $row_arr[] = null;
        }

        // 御見積金額
        if (isset($sumArr['total'])) {
            $value = $sumArr['total'];
            $row_arr[] = array('', $value['sum']);
        }

        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        /* // 消費税金額の内訳出力     keigen */
        /* if ( $this->_isKeigenAppliedCtxt() ) { */
        /*     $this->outZeiUchiwake($pdfObj, '1', $this->_p_circle_num); */
        /* } */
    }

    /**
     * 表紙の金額を出力（法事)
     * 
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    public function outDataFaceSum01_houji_del($pdfObj, $sumArr, $coseNm)
    {
        static $row_height = 20;

        $set_arr[] = array('x' => 30,  'width' => 65,  'height' => 27, 'align' => 'C');   // 項目
        $set_arr[] = array('x' => 90,  'width' => 100, 'height' => 27, 'type'  => 'num'); // 金額
        $set_arr[] = array('x' => 156, 'width' => 100, 'height' => 27, 'type'  => 'num'); // 値引き(付帯特典・割引金額)
        $set_arr[] = array('x' => 256, 'width' => 100, 'height' => 27, 'type'  => 'num'); // 御見積金額
        $set_arr[] = array('x' => 350, 'width' => 90,  'height' => 27, 'type'  => 'num'); // 消費税
        $set_arr[] = array('x' => 450, 'width' => 135, 'height' => 27); // 備考
        for ($index = 0; $index < count($set_arr); $index++) {
            $set_arr[$index]['y'] = 360;
        }

        $hoyo_sum    = 0;	    // 法要・墓参り費用合計
        $zei_nuki_nebiki_sum  = 0;  // 値引き合計(税抜項目)
	$zei_komi_nebiki_sum  = 0;  // 値引き合計(税込項目)
        $omote_sum   = 0;	    // おもてなし費用合計
        $sonota_sum  = null;	    // その他費用合計
        $hikazei     = 0;	    // 非課税
        $azukari_dan = 0;	    // 預金(壇払い)
        $azukari_hen = 0;	    // 預金(返礼品)
        $row_arr = null;
        
        // 法要・墓参り費用内訳
        foreach (self::$houyou_hakamairi as $key) {
            if (isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0) {
                $value = $sumArr[$key];
                $hoyo_sum += $value['sum_i'];
                $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
                $zei_nuki_nebiki_sum += $nebiki;
                $row_arr[] = array($sumArr[$key]['name'] // 中分類
                                , $sumArr[$key]['sum_i'] // 金額
                                , $nebiki                // 値引き額
                                , $sumArr[$key]['sum']   // 見積金額
                                , null			 // 消費税
                                , null                   // 備考
                            );
            }
        }
        
        // おもてなし費用内訳
        foreach (self::$omotenasi as $key) {
            if (isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0) {
                $omote_sum += $sumArr[$key]['sum_i'];
                $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
                $zei_nuki_nebiki_sum += $nebiki;
                $row_arr[] = array($sumArr[$key]['name']  // 中分類
                                 , $sumArr[$key]['sum_i'] // 金額
                                 , $nebiki                // 値引き額
                                 , $sumArr[$key]['sum']   // 見積金額
                                 , null			 // 消費税
                                 , null                   // 備考
                             );
            }
        }
        
        // その他費用内訳
        foreach (self::$sonota as $key) {
            if (isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0) {
                $sonota_sum += $sumArr[$key]['sum_i'];
                $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
                $zei_nuki_nebiki_sum += $nebiki;
                $row_arr[] = array($sumArr[$key]['name']  // 中分類
                                 , $sumArr[$key]['sum_i'] // 金額
                                 , $nebiki                // 値引き額
                                 , $sumArr[$key]['sum']   // 見積金額
                                 , null			 // 消費税
                                 , null                   // 備考
                             );
            }
        }

        // 税込項目
        if (isset($sumArr[self::CHU_BUNRUI_CD_HIKAZEI])) {
            $value = $sumArr[self::CHU_BUNRUI_CD_HIKAZEI];
	    $nebiki = $value['sum_t'] == 0 ? null : $value['sum_t'];
	    $zei_komi_nebiki_sum += $nebiki;
            $row_arr[] = array($value['name']
                             , $value['sum_i']	// 金額
                             , $nebiki		// 値引き額
                             , $value['sum']	// 見積金額
                             , null		// 消費税
                             , null		// 備考
                         );
            $hikazei = $value['sum_i'];
        }

        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            $row_arr[] = array($value['name']
                             , $value['sum'] // 金額
                             , null          // 値引き額
                             , $value['sum'] // 見積金額
                             , null          // 消費税
                             , null          // 備考
                         );
            $azukari_dan = $value['sum'];
        }

        // 預り金（返礼品）
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            $row_arr[] = array($value['name']
                             , $value['sum'] // 金額
                             , null          // 値引き額
                             , $value['sum'] // 見積金額
                             , null          // 消費税
                             , null          // 備考
                         );
            $azukari_hen = $value['sum'];
        }

        // 空行を設定
        for ($index = count($row_arr); $index < 12; $index++) {
            $row_arr[] = null;
        }

	$zei_nuki_sum = $hoyo_sum + $omote_sum + $sonota_sum + $azukari_dan + $azukari_hen;
	$zei_komi_sum = $hikazei;
        $kingaku_sum  = $zei_nuki_sum + $zei_komi_sum;
	$nebiki_sum   = $zei_nuki_nebiki_sum + $zei_komi_nebiki_sum;
        $zei = null;
        if (isset($sumArr['tax'])) {
            $zei = $sumArr['tax']['sum'];
        }
	
	// 税抜項目小計
        $row_arr[] = array(null
                         , $zei_nuki_sum != 0 ? $zei_nuki_sum : ''						// 金額
                         , $zei_nuki_nebiki_sum < 0 ? $zei_nuki_nebiki_sum : ''					// 値引き額
                         , $zei_nuki_sum + $zei_nuki_nebiki_sum != 0 ? $zei_nuki_sum + $zei_nuki_nebiki_sum : ''// 請求金額
                         , $zei	!= 0 ? $zei : ''								// 消費税
                         , null											// 備考
                     );
	
	// 税込項目小計
        $row_arr[] = array(null
                         , $zei_komi_sum != 0 ? $zei_komi_sum : ''						  // 金額
                         , $zei_komi_nebiki_sum < 0 ? $zei_komi_nebiki_sum : ''					  // 値引き額
                         , $zei_komi_sum + $zei_komi_nebiki_sum != 0 ? $zei_komi_sum + $zei_komi_nebiki_sum : ''  // 請求金額
                         , null											  // 消費税
                         , null											  // 備考
                     );

        // 合計
        $row_arr[] = array(''
			, $kingaku_sum != 0 ? $kingaku_sum : ''				    // 金額
			, $nebiki_sum < 0 ? $nebiki_sum : ''				    // 値引き額
			, $kingaku_sum + $nebiki_sum != 0 ? $kingaku_sum + $nebiki_sum : '' // 見積金額
			, $zei != 0 ? $zei : ''						    // 消費税
			, null								    // 備考
		    );
	
        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

        // 消費税金額の内訳出力     keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $this->outZeiUchiwake($pdfObj, 2); // 2:法事
        }
    }
    
  
    /**
     * 表紙の金額を出力（法事)
     * 
     * @param type $pdfObj
     * @param array $sumArr
     * @param string $coseNm
     */
    public function outDataFaceSum01_houji($pdfObj, $sumArr, $coseNm)
    {
        static $row_height = 20;

        $x_pos = 282;
        $set_arr1[] = array('x' => 27,  'width' => 65,  'height' => 27, 'align' => 'C');   // 項目
        $set_arr1[] = array('x' => 85,  'width' => 100, 'height' => 27, 'type'  => 'num'); // 金額
        $set_arr1[] = array('x' => 151, 'width' => 100, 'height' => 27, 'type'  => 'num'); // 値引き(付帯特典・割引金額)
        $set_arr1[] = array('x' => 251, 'width' => 100, 'height' => 27, 'type'  => 'num'); // 御見積金額
        $set_arr1[] = array('x' => 345, 'width' => 90,  'height' => 27, 'type'  => 'num'); // 消費税
        for ($index = 0; $index < count($set_arr1); $index++) {
            $set_arr1[$index]['y'] = 360;
        }
        $set_arr2[] = array('x' => 27 + $x_pos,  'width' => 65,  'height' => 27, 'align' => 'C');   // 項目
        $set_arr2[] = array('x' => 85 + $x_pos,  'width' => 100, 'height' => 27, 'type'  => 'num'); // 金額
        $set_arr2[] = array('x' => 151 + $x_pos, 'width' => 100, 'height' => 27, 'type'  => 'num'); // 値引き(付帯特典・割引金額)
        $set_arr2[] = array('x' => 251 + $x_pos, 'width' => 100, 'height' => 27, 'type'  => 'num'); // 御見積金額
        $set_arr2[] = array('x' => 345 + $x_pos, 'width' => 90,  'height' => 27, 'type'  => 'num'); // 消費税
        for ($index = 0; $index < count($set_arr2); $index++) {
            $set_arr2[$index]['y'] = 360;
        }
        
        $hoyo_sum    = 0;	    // 法要・墓参り費用合計
        $zei_nuki_nebiki_sum  = 0;  // 値引き合計(税抜項目)
	$zei_komi_nebiki_sum  = 0;  // 値引き合計(税込項目)
        $omote_sum   = 0;	    // おもてなし費用合計
        $sonota_sum  = null;	    // その他費用合計
        $hikazei     = 0;	    // 非課税
        $azukari_dan = 0;	    // 預金(壇払い)
        $azukari_hen = 0;	    // 預金(返礼品)
        $row_arr = null;
        
        // 法要・墓参り費用内訳
        foreach (self::$houyou_hakamairi as $key) {
            if (isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0) {
                $value = $sumArr[$key];
                $hoyo_sum += $value['sum_i'];
                $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
                $zei_nuki_nebiki_sum += $nebiki;
                $name = $sumArr[$key]['name'];
                if($this->_moushiKbnHouji == 8){
                    if($key == self::CHU_BUNRUI_CD_NIIBON_5){
                        $name = "端数値引き";
                    }
                }
                $row_arr[] = array($name // 中分類
                                , $sumArr[$key]['sum_i'] // 金額
                                , $nebiki                // 値引き額
                                , $sumArr[$key]['sum']   // 見積金額
                                , $sumArr[$key]['tax']   // 消費税
                                , null                   // 備考
                            );
                if($this->_moushiKbnHouji == 8){
                    if($key == self::CHU_BUNRUI_CD_NIIBON_2){
                        $row_arr[] = array("返品");
                    }
                }
            }else if($this->_moushiKbnHouji == 8){
                if($key == self::CHU_BUNRUI_CD_NIIBON_5){
                    $name = "端数値引き";
                    $row_arr[] = array($name);
                }
            }
        }
        
        // おもてなし費用内訳
        foreach (self::$omotenasi as $key) {
            if (isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0) {
                $omote_sum += $sumArr[$key]['sum_i'];
                $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
                $zei_nuki_nebiki_sum += $nebiki;
                $row_arr[] = array($sumArr[$key]['name']  // 中分類
                                 , $sumArr[$key]['sum_i'] // 金額
                                 , $nebiki                // 値引き額
                                 , $sumArr[$key]['sum']   // 見積金額
                                 , $sumArr[$key]['tax']   // 消費税
                                 , null                   // 備考
                             );
            }
        }
        
        // その他費用内訳
        foreach (self::$sonota as $key) {
            if (isset($sumArr[$key]) && $sumArr[$key]['sum'] != 0) {
                $sonota_sum += $sumArr[$key]['sum_i'];
                $nebiki = $sumArr[$key]['sum_t'] == 0 ? null : $sumArr[$key]['sum_t'];
                $zei_nuki_nebiki_sum += $nebiki;
                $row_arr[] = array($sumArr[$key]['name']  // 中分類
                                 , $sumArr[$key]['sum_i'] // 金額
                                 , $nebiki                // 値引き額
                                 , $sumArr[$key]['sum']   // 見積金額
                                 , $sumArr[$key]['tax']   // 消費税
                                 , null                   // 備考
                             );
            }
        }

        // 税込項目
//        if (isset($sumArr[self::CHU_BUNRUI_CD_HIKAZEI])) {
//            $value = $sumArr[self::CHU_BUNRUI_CD_HIKAZEI];
//	    $nebiki = $value['sum_t'] == 0 ? null : $value['sum_t'];
//	    $zei_komi_nebiki_sum += $nebiki;
//            $row_arr[] = array($value['name']
//                             , $value['sum_i']	// 金額
//                             , $nebiki		// 値引き額
//                             , $value['sum']	// 見積金額
//                             , null		// 消費税
//                             , null		// 備考
//                         );
//            $hikazei = $value['sum_i'];
//        }

        // 預り金（壇払い）
        if ($sumArr[self::SUMID_AZUKARI_DAN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_DAN];
            $row_arr[] = array($value['name']
                             , $value['sum'] // 金額
                             , null          // 値引き額
                             , $value['sum'] // 見積金額
                             , null          // 消費税
                             , null          // 備考
                         );
            $azukari_dan = $value['sum'];
        }

        // 預り金（返礼品）
        if ($sumArr[self::SUMID_AZUKARI_HEN]['sum'] != 0) {
            $value = $sumArr[self::SUMID_AZUKARI_HEN];
            $row_arr[] = array($value['name']
                             , $value['sum'] // 金額
                             , null          // 値引き額
                             , $value['sum'] // 見積金額
                             , null          // 消費税
                             , null          // 備考
                         );
            $azukari_hen = $value['sum'];
        }

        // 空行を設定
        for ($index = count($row_arr); $index < 12; $index++) {
            $row_arr[] = null;
        }
	$row_arr1 = array(); // 左項目
	$row_arr2 = array(); // 右項目
        for ($index = 0; $index < count($row_arr); $index++) {
            //if ($index < 6) {
                $row_arr1[] = $row_arr[$index];
//            } else {
//                $row_arr2[] = $row_arr[$index];
//            }
        }
        
        $pdfObj->write_table_simple($set_arr1, $row_height, $row_arr1);
        $pdfObj->write_table_simple($set_arr2, $row_height, $row_arr2);

	$zei_nuki_sum = $hoyo_sum + $omote_sum + $sonota_sum + $azukari_dan + $azukari_hen;
	$zei_komi_sum = $hikazei;
        $kingaku_sum  = $zei_nuki_sum + $zei_komi_sum;
	$nebiki_sum   = $zei_nuki_nebiki_sum + $zei_komi_nebiki_sum;
        $zei = null;
        if (isset($sumArr['tax'])) {
            $zei = $sumArr['tax']['sum'];
        }
        $zei_in = 0;
        foreach ($sumArr as $value) {
            if (isset($value['tax_in'])) {
                $zei_in = $value['tax_in'];
            }
        }
        
        $set_arr_sum = array();
        $row_arr_sum = array();
        $set_arr_sum[] = array('x' => 27, 'y' => 501,  'width' => 65,  'height' => 27, 'align' => 'C');   // 項目
        $set_arr_sum[] = array('x' => 27, 'y' => 501,  'width' => 65,  'height' => 27, 'align' => 'C', 'color' => '#224f03',);   // 項目(税抜項目小計、税込項目小計)
        $set_arr_sum[] = array('x' => 85, 'y' => 501,  'width' =>100, 'height' => 27, 'type'  => 'num'); // 金額
        $set_arr_sum[] = array('x' => 151, 'y' => 501, 'width' =>100, 'height' => 27, 'type'  => 'num'); // 値引き(付帯特典・割引金額)
        $set_arr_sum[] = array('x' => 251, 'y' => 501, 'width' =>100, 'height' => 27, 'type'  => 'num'); // 御見積金額
        $set_arr_sum[] = array('x' => 335, 'y' => 501, 'width' =>100, 'height' => 27, 'type'  => 'num'); // 外消費税
        $set_arr_sum[] = array('x' => 370, 'y' => 501, 'width' =>100, 'height' => 27, 'type'  => 'num'); // 内消費税
        
        $uri_den_no = $this->_p_uri_den_no;
        $db = Msi_Sys_DbManager::getMyDb();
        $shohizei = App_KeigenUtils::getSeikyuShohizeiEasy5($db, $uri_den_no);
        
        $zei_kbn_break_key = null;
        $shohizei_index = 0;
        // 小計用の金額初期化
        $shokei_zei_komi_sum = 0;
        $shokei_zei_komi_nebiki_sum = 0;
        $shokei_zei_in = 0;
        $shokei_zei_nuki_sum = 0;
        $shokei_zei_nuki_nebiki_sum = 0;
        $shokei_zei = 0;
        foreach ($shohizei as $value) {
            $shohizei_index++;
            if (isset($zei_kbn_break_key) && $zei_kbn_break_key != $value['zei_kbn']) {
                if ($zei_kbn_break_key == 1) {
                    // 税込項目小計
                    $row_arr_sum[] = array(null
//                        ,'税込項目小計'
//                        , $shokei_zei_komi_sum != 0 ? $shokei_zei_komi_sum : ''        // 金額
//                        , $shokei_zei_komi_nebiki_sum < 0 ? $shokei_zei_komi_nebiki_sum : ''       // 値引き額
//                        , $shokei_zei_komi_sum + $shokei_zei_komi_nebiki_sum != 0 ? $shokei_zei_komi_sum + $shokei_zei_komi_nebiki_sum : ''  // 見積金額
//                        , null             // 外消費税
//                        , $shokei_zei_in != 0 ? $shokei_zei_in : ''          // 内消費税
                         , null
                         , null
                         , null
                         , null
                         , null
                         , null
                    );
                } else if ($zei_kbn_break_key == 2) {
                    // 税抜項目小計
                    $row_arr_sum[] = array(null
//                        ,'税抜項目小計'
//                        , $shokei_zei_nuki_sum != 0 ? $shokei_zei_nuki_sum : ''       // 金額
//                        , $shokei_zei_nuki_nebiki_sum < 0 ? $shokei_zei_nuki_nebiki_sum : ''      // 値引き額
//                        , $shokei_zei_nuki_sum + $shokei_zei_nuki_nebiki_sum != 0 ? $shokei_zei_nuki_sum + $shokei_zei_nuki_nebiki_sum : '' // 見積金額
//                        , $shokei_zei != 0 ? $shokei_zei : ''         // 外消費税
//                        , null            // 内消費税
                         , null
                         , null
                         , null
                         , null
                         , null
                         , null
                    );
                }
            }
            if(isset($value['zei_kbn'])){
                if ($value['zei_kbn'] == 0) {
                    $row_arr_sum[] = array($value['cap']
                       ,null
                       , $value['price_i'] // 金額
                       , $value['price_n'] != 0 ? $value['price_n'] : '' // 値引金額
                       , $value['price'] // 見積金額
                       , null           // 外消費税
                       , null           // 内消費税
                   );
                } else if ($value['zei_kbn'] == 1) {
                    $row_arr_sum[] = array($value['cap']
                       ,null
                       , $value['price_i'] // 金額
                       , $value['price_n'] != 0 ? $value['price_n'] : '' // 値引金額
                       , $value['price'] // 見積金額
                       , null            // 外消費税
                       , $value['tax'] // 内消費税
                   );
                    $shokei_zei_komi_sum        += $value['price_i'];
                    $shokei_zei_komi_nebiki_sum += $value['price_n'];
                    $shokei_zei_in              += $value['tax'];
                } else if ($value['zei_kbn'] == 2) {
                    $row_arr_sum[] = array($value['cap']
                       ,null
                       , $value['price_i'] // 金額
                       , $value['price_n'] != 0 ? $value['price_n'] : '' // 値引金額
                       , $value['price'] // 見積金額
                       , $value['tax'] // 外消費税
                       , null           // 内消費税
                   );
                    $shokei_zei_nuki_sum        += $value['price_i'];
                    $shokei_zei_nuki_nebiki_sum += $value['price_n'];
                    $shokei_zei                 += $value['tax'];
                }
                $zei_kbn_break_key = $value['zei_kbn'];
            }
        }
        if ($zei_kbn_break_key == 1) {
            // 税込項目小計
            $row_arr_sum[] = array(null
//                ,'税込項目小計'
//                , $shokei_zei_komi_sum != 0 ? $shokei_zei_komi_sum : ''        // 金額
//                , $shokei_zei_komi_nebiki_sum < 0 ? $shokei_zei_komi_nebiki_sum : ''       // 値引き額
//                , $shokei_zei_komi_sum + $shokei_zei_komi_nebiki_sum != 0 ? $shokei_zei_komi_sum + $shokei_zei_komi_nebiki_sum : ''  // 見積金額
//                , null             // 外消費税
//                , $shokei_zei_in != 0 ? $shokei_zei_in : ''          // 内消費税
                  , null
                  , null
                  , null
                  , null
                  , null
                  , null
            );
        } else if ($zei_kbn_break_key == 2) {
            // 税抜項目小計
            $row_arr_sum[] = array(null
//                ,'税抜項目小計'
//                , $shokei_zei_nuki_sum != 0 ? $shokei_zei_nuki_sum : ''       // 金額
//                , $shokei_zei_nuki_nebiki_sum < 0 ? $shokei_zei_nuki_nebiki_sum : ''      // 値引き額
//                , $shokei_zei_nuki_sum + $shokei_zei_nuki_nebiki_sum != 0 ? $shokei_zei_nuki_sum + $shokei_zei_nuki_nebiki_sum : '' // 見積金額
//                , $shokei_zei != 0 ? $shokei_zei : ''         // 外消費税
//                , null            // 内消費税
                  , null
                  , null
                  , null
                  , null
                  , null
                  , null
            );
        }
        // 空行を設定
        for ($index = count($row_arr_sum); $index < 7; $index++) {
            $row_arr_sum[] = null;
        }
        
	if($this->_moushiKbnHouji == 2){
            // 合計
            $row_arr_sum[] = array(''
                            ,''
                            , $kingaku_sum != 0 ? $kingaku_sum : ''				    // 金額
                            , $nebiki_sum < 0 ? $nebiki_sum : ''				    // 値引き額
                            , $kingaku_sum + $nebiki_sum != 0 ? $kingaku_sum + $nebiki_sum : '' // 見積金額
                            , $zei != 0 ? $zei : ''						    // 外消費税
                            , null								    // 内消費税
                        );
        }
        // 消費税金額の内訳出力     keigen
        if ( $this->_isKeigenAppliedCtxt() ) {
            $this->outZeiUchiwake($pdfObj, $this->_moushiKbnHouji);
        }        
        $pdfObj->write_table_simple($set_arr_sum, $row_height, $row_arr_sum);
    }
    
    /**
     * 入金情報を出力
     * 
     * @param type $pdfObj
     * @param type $db
     * @param type $kaisyalogo
     * @param type $seko_no
     * @param type $meisai_top
     * @param type $add_x
     * @param type $row_height
     * @return なし
     */
    private function outNyukin($pdfObj, $db, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $meisai_top, $add_x = 0, $row_height = 14.1)
    {
        static $meisai_row_count = 5;

        $set_arr[] = array('x' => 45  + $add_x, 'y' => $meisai_top-28, 'width' =>  60, 'height' => 15, 'type' => 'ymd', 'format' => 'Y/m/d', 'align' => 'C'); // 入金日
        $set_arr[] = array('x' => 110 + $add_x, 'y' => $meisai_top-28, 'width' => 185, 'height' => 15); // 品目
        $set_arr[] = array('x' => 305 + $add_x, 'y' => $meisai_top-28, 'width' =>  65, 'height' => 15, 'type' => 'num'); // 入金金額

        $row_arr = array();
        $total = 0;

        $rec = DataMapper_Pdf1101::findNyukin( $db, array( "data_kbn" => $data_kbn, "seko_no" => $seko_no, "seko_no_sub" => $seko_no_sub, "seikyu_no" => $uri_den_no ) );
        $count = count($rec);
        if ($count > $meisai_row_count) { $count = $meisai_row_count - 1; }
        for ($index = 0; $index < $count; $index++) {
            $row = $rec[$index];
            $row_arr[] = array($row['nyukin_ymd'], $row['kamoku_nm'], $row['nyukin_prc']);
            $total += $row['nyukin_prc'];
        }

        // その他
        if (count($rec) > $meisai_row_count) {
            $etc = 0;
            for ($index = $count; $index < count($rec); $index++) {
                $row = $rec[$index];
                $total += $row['nyukin_prc'];
                $etc += $row['nyukin_prc'];
            }
            $row_arr[] = array(null, 'その他', $etc);
        }
        
        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
        $pdfObj->write_num(array('x' => 305 + $add_x, 'y' => $meisai_top + 42, 'width' => 65, 'height' => 15), $total);  // 入金合計
    }
    /**
     * 入金情報を出力
     * 
     * @param type $pdfObj
     * @param type $db
     * @param type $kaisyalogo
     * @param type $seko_no
     * @param type $meisai_top
     * @param type $add_x
     * @param type $row_height
     * @return なし
     */
    private function outNyukin02($pdfObj, $db, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $meisai_top, $add_x = 0, $row_height = 14.1)
    {
        static $meisai_row_count = 3;

        $set_arr[] = array('x' =>  32 + $add_x, 'y' => $meisai_top-28, 'width' =>  60, 'height' => 15, 'type' => 'ymd', 'format' => 'Y/m/d', 'align' => 'C'); // 入金日
        $set_arr[] = array('x' =>  97 + $add_x, 'y' => $meisai_top-28, 'width' => 195, 'height' => 15); // 品目
        $set_arr[] = array('x' => 297 + $add_x, 'y' => $meisai_top-28, 'width' =>  65, 'height' => 15, 'type' => 'num'); // 入金金額

        $row_arr = array();
        $total = 0;

        $rec = DataMapper_Pdf1101::findNyukin( $db, array( "data_kbn" => $data_kbn, "seko_no" => $seko_no, "seko_no_sub" => $seko_no_sub, "seikyu_no" => $uri_den_no ) );
        $count = count($rec);
        if ($count > $meisai_row_count) { $count = $meisai_row_count - 1; }
        for ($index = 0; $index < $count; $index++) {
            $row = $rec[$index];
            $row_arr[] = array($row['nyukin_ymd'], $row['kamoku_nm'], $row['nyukin_prc']);
            $total += $row['nyukin_prc'];
        }
        // その他
        if (count($rec) > $meisai_row_count) {
            $etc = 0;
            for ($index = $count; $index < count($rec); $index++) {
                $row = $rec[$index];
                $total += $row['nyukin_prc'];
                $etc += $row['nyukin_prc'];
            }
            $row_arr[] = array(null, 'その他', $etc);
        }
        
        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
        $pdfObj->write_num(array('x' => 297 + $add_x, 'y' => $meisai_top + 14, 'width' => 65, 'height' => 15), $total);  // 入金合計
    }
    static private function getChuBunruiName($db, $chu_bunrui_cd) {
         $chu_bun_ary = DataMapper_MsterGetLib::GetChuBunruiMst($db);
         foreach ($chu_bun_ary as $row) {
             if ($row['chu_bunrui_cd'] == $chu_bunrui_cd) {
                 return $row['chu_bunrui_nm'];
             }
         }
         return null;
    }
    /**
     * 入金情報を出力（法事用）
     * 
     * @param type $pdfObj
     * @param type $db
     * @param type $kaisyalogo
     * @param type $seko_no
     * @param type $meisai_top
     * @param type $add_x
     * @param type $row_height
     * @return なし
     */
    private function outNyukin03($pdfObj, $db, $uri_den_no, $data_kbn, $seko_no, $seko_no_sub, $meisai_top, $add_x = 0, $row_height = 14.1)
    {
        static $meisai_row_count = 5;

        $set_arr[] = array('x' => 40  + $add_x, 'y' => $meisai_top-28, 'width' =>  60, 'height' => 15, 'type' => 'ymd', 'format' => 'Y/m/d', 'align' => 'C'); // 入金日
        $set_arr[] = array('x' => 105 + $add_x, 'y' => $meisai_top-28, 'width' => 185, 'height' => 15); // 品目
        $set_arr[] = array('x' => 300 + $add_x, 'y' => $meisai_top-28, 'width' =>  65, 'height' => 15, 'type' => 'num'); // 入金金額

        $row_arr = array();
        $total = 0;

        $rec = DataMapper_Pdf1101::findNyukin( $db, array( "data_kbn" => $data_kbn, "seko_no" => $seko_no, "seko_no_sub" => $seko_no_sub, "seikyu_no" => $uri_den_no ) );
        $count = count($rec);
        if ($count > $meisai_row_count) { $count = $meisai_row_count - 1; }
        for ($index = 0; $index < $count; $index++) {
            $row = $rec[$index];
            $row_arr[] = array($row['nyukin_ymd'], $row['kamoku_nm'], $row['nyukin_prc']);
            $total += $row['nyukin_prc'];
        }

        // その他
        if (count($rec) > $meisai_row_count) {
            $etc = 0;
            for ($index = $count; $index < count($rec); $index++) {
                $row = $rec[$index];
                $total += $row['nyukin_prc'];
                $etc += $row['nyukin_prc'];
            }
            $row_arr[] = array(null, 'その他', $etc);
        }
        
        $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
        $pdfObj->write_num(array('x' => 300 + $add_x, 'y' => $meisai_top + 41, 'width' => 65, 'height' => 15), $total);  // 入金合計
    }
    /**
     *
     * ソースファイルのインデックスを取得する
     *
     * <AUTHOR> Sai
     * @since 2014/8/19
     * @return int インデックス
     */
    private function getSourceFileIndex($data_kbn) {
        if ($this->_moushiKbn == 6) {
            return $this->_moushiKbn;
        } else {
            return $data_kbn;
        }
     }

    /**
     *
     * 表紙を追加する
     *
     * <AUTHOR> Sai
     * @since 2014/10/7
     * @return 
     */
    private function addHyoshi($pdfObj, $db, $seko_no) {
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileHyosi);
        
        $kaisya = DataMapper_PdfCommon::getKaisya($db, array('kaisya_snm', 'syamei_logo_img '));
        if (isset($kaisya['syamei_logo_img'])) {
            $logo = $db->readBlobCont($kaisya['syamei_logo_img']);
            $pdfObj->write_image(array('x' => 160, 'y' => 700, 'width' => 0, 'height' => 60), $logo);
        }
       // 請求先
        $sekyu = App_Utils::getSekoSekyuInfo($seko_no);
        if (isset( $sekyu['sekyu_nm'])) {
            $pdfObj->write_string(array('x' =>50, 'y' => 96, 'width' =>180, 'height' =>  15, 'align' => 'C', 'font_size' => 15), $sekyu['sekyu_nm']);
        }
        
        $rec = DataMapper_SekoKihonInfo::findOne( $db, array('seko_no'=>$seko_no) );
        if (count($rec) > 0) {
            // 葬家名
            $pdfObj->write_string(array('x' => 125, 'y' => 207, 'width' =>130, 'height' => 45, 'font_size' => 36, 'align' => 'C'), $rec['souke_nm']);
        }
        // 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);
    }
    
    /**
     *
     * 振込み案内ページを追加する
     *
     * <AUTHOR> Matsuyama
     * @since 2016/09/20
     * @param type $pdfObj
     * @param type $db
     * @param type $souke_nm    葬家名
     * @param type $sekyu_gaku  御請求金額
     * @param type $sekyu_no    請求No
     * @return 
     */
    private function addFurikomiGuide($pdfObj, $db, $souke_nm, $sekyu_gaku, $sekyu_no) {
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileGuide);
        // 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);
        
        // 葬家名
        if (isset($souke_nm)) {
            $pdfObj->write_string(array('x' =>60, 'y' => 80, 'width' => 60, 'height' => 18, 'font_size' => 14, 'align' => 'C'), $souke_nm);
        }
        
        $bank_data = DataMapper_BankMst::find($db, array('seikyu_print_kbn' => 1,'bumon_cd' => $this->_bumonCd));
        $count = 0;
        $y1 = 345;
        $y2 = 370;
        foreach ($bank_data as $value) {
            if (!empty($bank_data)) {
                // 口座名義人
                $pdfObj->write_string(array('x' =>138, 'y' => $y1, 'width' => 400, 'height' => 18, 'font_size' => 14), $value['koza_meigi_nm']);
                // 振込先情報
                $pdfObj->write_string(array('x' =>138, 'y' => $y2, 'width' => 350, 'height' => 18, 'font_size' => 14)
                                            ,  $value['bank_nm'].'　'
                                              .$value['shiten_nm'].'　　'
                                              .($value['yokin_sbt'] == 1 ? '当座' : '普通').'　'.$value['kouza_no']);
            }
            $y1 += 61;
            $y2 += 61;
            $count++;
            if($count == 3){
                break;
            }
        }
        // 御請求金額
        if (isset($sekyu_gaku)) {
            if(strlen($sekyu_gaku) > 0){
                $pdfObj->write_string(array('x' =>305, 'y' => 543, 'width' => 30, 'height' => 18, 'font_size' => 14), "円");
            }
            $pdfObj->write_num(array('x' =>157, 'y' => 543, 'width' => 150, 'height' => 18, 'font_size' => 14), $sekyu_gaku);
        }
        // お客様番号
        if (isset($sekyu_no)) {
            $pdfObj->write_string(array('x' =>215, 'y' => 591, 'width' => 150, 'height' => 18, 'font_size' => 14), $sekyu_no);
        }
    }

    /**
     *
     * 振込み案内ページを追加する（法事）
     *
     * <AUTHOR> Sugiyama
     * @since 2019/12/03
     * @param type $pdfObj
     * @param type $db
     * @param type $souke_nm    葬家名
     * @param type $sekyu_gaku  御請求金額
     * @param type $sekyu_no    請求No
     * @return 
     */
    private function addFurikomiGuideHouji($pdfObj, $db, $souke_nm, $sekyu_gaku, $sekyu_no, $bumon_cd) {
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileGuide);
        // 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);
        
        // 葬家名
        if (isset($souke_nm)) {
            $pdfObj->write_string(array('x' =>60, 'y' => 80, 'width' => 60, 'height' => 18, 'font_size' => 14, 'align' => 'C'), $souke_nm);
        }
        
        $bank_data = DataMapper_BankMst::find($db, array('seikyu_print_kbn' => 1,'bumon_cd' => $bumon_cd));
        $count = 0;
        $y1 = 345;
        $y2 = 370;
        foreach ($bank_data as $value) {
            if (!empty($bank_data)) {
                // 口座名義人
                $pdfObj->write_string(array('x' =>138, 'y' => $y1, 'width' => 400, 'height' => 18, 'font_size' => 14), $value['koza_meigi_nm']);
                // 振込先情報
                $pdfObj->write_string(array('x' =>138, 'y' => $y2, 'width' => 350, 'height' => 18, 'font_size' => 14)
                                            ,  $value['bank_nm'].'　'
                                              .$value['shiten_nm'].'　　'
                                              .($value['yokin_sbt'] == 1 ? '当座' : '普通').'　'.$value['kouza_no']);
            }
            $y1 += 61;
            $y2 += 61;
            $count++;
            if($count == 3){
                break;
            }
        }
        // 御請求金額
        if($this->_moushiKbnHouji == 2){
            if (isset($sekyu_gaku)) {
                if(strlen($sekyu_gaku) > 0){
                    $pdfObj->write_string(array('x' =>305, 'y' => 543, 'width' => 30, 'height' => 18, 'font_size' => 14), "円");
                }
                $pdfObj->write_num(array('x' =>157, 'y' => 543, 'width' => 150, 'height' => 18, 'font_size' => 14), $sekyu_gaku);
            }
        }
        // お客様番号
        if (isset($sekyu_no)) {
            $pdfObj->write_string(array('x' =>215, 'y' => 591, 'width' => 150, 'height' => 18, 'font_size' => 14), $sekyu_no);
        }
    }
    
    /**
     *
     * 見積表紙に会員情報を出力する
     *
     * <AUTHOR> Sai
     * @since 2015/12/17
     * @param $db
     * @param $pdfObj
     * @param $seko_no
     * @return 
     */
    private function outKaiinInfo($db, $pdfObj, $seko_no) {
        $y = 202.5;
        // 会員情報出力処理
        $kainNo = $this->getKainNo($db, $seko_no, 1);
        foreach ($kainNo as $key => $rec) {
            if ($key >6) {
                break;
            }
            $pdfObj->write_string(array('x' => 440, 'y' => $y, 'width' => 135, 'height' => 16,), trim($rec['kain_no']) . " " . $rec['apply_no'] . " " . $rec['course_snm_cd'] . " " . $rec['kanyu_nm']);
            $y += 14;
        }
    }
    
    /**
     * PDFのファイル名を施行No + 葬家名 + タイトル 形式で取得する
     * 
     * <AUTHOR> Matsuyama
     * @since 2016/09/06
     * @return string   PDFのファイル名
     */
    public function getFileName($db, $seko_no, $title) {
        $file_name = $title;
        $rec = DataMapper_SekoKihonInfo::findOne( $db, array('seko_no'=>$seko_no) );
        if (Msi_Sys_Utils::myCount($rec) > 0) {
            $file_name = $seko_no.$rec['souke_nm'].'家'.$title;
        }
        return $file_name;
    }

    /**
     *
     * 担当者印印字処理
     * 承認日が2017/04/25以前の場合は印鑑が黒くなるため、対応前のバージョンで出力を行う
     *
     * <AUTHOR> Sai
     * @since 2017/04/28
     * @param type $pdfObj 
     * @param type $shonin_dt 
     * @param type $x 
     * @param type $y 
     * @param type $img 
     * @return 
     */
    private function seal_out($pdfObj, $shonin_dt, $x, $y, $img) {
        if ($shonin_dt > '2017/04/25') {
            $pdfObj->seal_out($x, $y, $img);
        } else {
            $pdfObj->seal_out_old($x, $y, $img);
        }
    }
    
    /**
     * コード名称取得
     * 
     * <AUTHOR> Sugiyama
     * @since 2017/09/13
     * @param type $db
     * @param type $code_kbn
     * @return type
     */
    private function getCodeNm($db,$code_kbn){
        $sql = "SELECT 
                    kbn_value_cd,
                    kbn_value_cd_num,
                    kbn_value_lnm,
                    kbn_value_snm
                FROM 
                    code_nm_mst
                WHERE code_kbn = :code_kbn
                ";
        $select = $db->easySelect($sql,array('code_kbn' => $code_kbn));
        return $select;                
        
    }
    
    /**
     * 消費税金額の内訳を出力する
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @version 2019/06/xx keigen nise02よりコピー
     * @param App_Pdf  $pdfObj
     * @param integer  $type     1:葬儀, 2:法事
     * @param string  $circle_num
     * @return void
     */
    protected function outZeiUchiwake($pdfObj, $type=1, $circle_num = '')
    {
//        $tgt_type    = $this->_p_tgt_type;
        $seko_no     = $this->_p_seko_no;
        $seko_no_sub = $this->_p_seko_no_sub;
        $data_kbn    = $this->_p_data_kbn;
        // $history_no  = $this->_p_history_no;

        $db = Msi_Sys_DbManager::getMyDb();

        $shohizei = App_KeigenUtils::getSeikyuShohizeiEasy($db, $seko_no, $seko_no_sub, $data_kbn);
        
        $aUchiwake = App_KeigenUtils::reduceZeiUchiwakeCasc($shohizei); // 行数の圧縮

        if ( $type == 2 ) { // 法事
            $optAttr = array( 'left'  => 440, // 447.5,   // X
                              'top'   => 363, // 310,     // Y
                              'width' => 130,
                              'height' => 20.2 );
            App_KeigenPdfUtils::outZeiUchiwake01($pdfObj, '合計金額の内訳', $shohizei, $optAttr);
            
        }else if ( $type == 8 ) { // 法事
            foreach ($shohizei as $key => $value) {
                $shohizei[$key][1] = NULL;
            }
            $optAttr = array( 'left'  => 440, // 447.5,   // X
                              'top'   => 363, // 310,     // Y
                              'width' => 130,
                              'height' => 20.2 );
            App_KeigenPdfUtils::outZeiUchiwake01($pdfObj, '合計金額の内訳', $shohizei, $optAttr);
        } else { // 葬儀
            $optAttr = array( 'left'  => 378,     // X
                              'top'   => 528, // 510, 550.5,   // Y
                              'width' => 174,
                              // 'fixRowNum' => 7,
                              'fontSize'  => 9,
                              'height' => 14); // 15.8 );
            App_KeigenPdfUtils::outZeiUchiwake012($pdfObj, '合計金額'.$circle_num.'の内訳　　　　', $aUchiwake, $optAttr);
        }
        // App_KeigenPdfUtils::outZeiUchiwake01($pdfObj, '合計金額'.$circle_num.'の内訳', $aUchiwake, $optAttr);
        // $pdfObj->test_line_out(600, 1000);  // mesh for DEV
    }

    /**
     * 消費税金額の内訳を出力する(単品、別注品)
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @version 2019/06/xx keigen nise02よりコピー
     * @param App_Pdf  $pdfObj
     * @param   string          $uri_den_no   売上伝票№
     * @return void
     */
    protected function outZeiUchiwake03($pdfObj, $uri_den_no)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        // $shohizei = App_KeigenUtils::getSeikyuShohizeiUriEasy($db, $uri_den_no); // 税種別毎
        $shohizei = App_KeigenUtils::getSeikyuShohizeiEasyRitsu($db, $uri_den_no); // 税率毎

        $aUchiwake = $shohizei;

        // 御請求金額行を追加
        $sum = 0;
        foreach ( $shohizei as $i => $line ) {
            if ( preg_match('/内消費税/u', $line[0]) ) {
                continue;
            }
            $sum += +$line[1];
        }
        $aUchiwake[] = array( '御請求金額', $sum );

        $optAttr = array( 'left'  => 400, // X
                          'top'   => 558, // Y
                          'width' => 170,
                          'height' => 14 );

        App_KeigenPdfUtils::outZeiUchiwake03($pdfObj, '', $aUchiwake, $optAttr);

        // $pdfObj->test_line_out(600, 1000);  // mesh for DEV
    }

    protected $_sougi_ymd = null; // 消費税基準日
    protected $_is_keigen_ctxt = false; // 軽減税率対応形式で出力

    /**
     * 軽減税率対象の可否を設定する
     * 葬儀や法事は、消費税基準日(sougi_ymd)を設定する
     *   juchu_*_history からの出力時でも現在の sougi_ymd を使うので注意
     * 単品、別注品は常に可で設定する
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @version 2019/06/xx keigen nise02よりコピー
     * @param $seko_no
     * @param $seko_no_sub
     * @param $data_kbn
     * @return void
     */
    protected function _prepKeigen($seko_no, $seko_no_sub, $data_kbn)
    {
        $db = Msi_Sys_DbManager::getMyDb();

        if ( $data_kbn == 3 || $data_kbn == 4 ) { // // 単品(3),別注品(4)は sougi_ymd を気にしない
            $this->_forceKeigenAppliedCtxt();
            return;
        }

        $sougi_ymd = $db->getOneVal( <<< END_OF_SQL
SELECT TO_CHAR(sougi_ymd, 'YYYY/MM/DD') AS sougi_ymd
  FROM seko_kihon_info
 WHERE seko_no=?
   AND delete_flg=0 
END_OF_SQL
                                     , array( $seko_no ) );

        $this->_sougi_ymd = $sougi_ymd; // null もあり得る
    }

    /**
     * 強制的に軽減税率対象とする
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @version 2019/06/xx keigen nise02よりコピー
     * @param $boolean
     * @return void
     */
    protected function _forceKeigenAppliedCtxt($boolean=true)
    {
        $this->_is_keigen_ctxt = $boolean;
    }

    /**
     * 消費税基準日(sougi_ymd)が軽減税率対象となる場合に真を返す
     *
     * <AUTHOR> mihara
     * @since 2019/05/xx
     * @version 2019/06/xx keigen nise02よりコピー
     * @return boolean
     */
    protected function _isKeigenAppliedCtxt()
    {
        if ( $this->_is_keigen_ctxt ) {
            return true;
        }

        $sougi_ymd = $this->_sougi_ymd;
        if ( $sougi_ymd == null ) {
            return false;
        }

        $keigenBgnYmd = App_KeigenUtils::startDayOfKeigen();
        if ( $keigenBgnYmd <= $sougi_ymd ) {
            return true;
        }
        return false;
    }
    /**
     * 施行基本申込区分 取得
     *
     * <AUTHOR> Sai
     * @since      2014/08/19
     * @param      string $seko_no
     * @return     string     $moushi_kbn
     */
    public static function getSekoMoushiKbn($seko_no)
    {
        $db = Msi_Sys_DbManager::getMyDb();
        $moushi_kbn = '1';
        $select = $db->easySelOne( <<< END_OF_SQL
SELECT
    free7_kbn AS moushi_kbn
  FROM seko_kihon_info
 WHERE seko_no = '$seko_no' AND delete_flg = 0
END_OF_SQL
        );
        if (count($select) > 0) {
            $moushi_kbn = $select['moushi_kbn'];
        }
        return $moushi_kbn;
    }
}
