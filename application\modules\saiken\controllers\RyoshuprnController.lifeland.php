<?php

/**
 * Saiken_RyosyuprnController
 *
 * 領収証・印紙確認一覧表印刷指示画面　コントローラクラス
 *
 * @category   App
 * @package    controller\Saiken
 * <AUTHOR> Sugiyama
 * @since      2020/12/xx
 * @filesource 
 */

/**
 * 領収証・印紙確認一覧表印刷指示画面 コントローラクラス
 *
 * @category   App
 * @package    controller\Saiken
 * <AUTHOR> Sugiyama
 * @since      2020/12/xx
 */
class Saiken_RyoshuprnController extends Msi_Zend_Controller_Action {
    
    private static $bindings = array(
    );
    
    /**
     * 領収証・印紙確認一覧表印刷指示画面 コントローラクラス
     *
     * @category   App
     * @package    controller\Saiken
     * <AUTHOR> Sugiyama
     * @since      2020/12/xx
     */
    public function printAction() {
        $params = Msi_Sys_Utils::webInputs();
        $db = Msi_Sys_DbManager::getMyDb();
        // 部門マスタを取得
        $_data = App_ClsBumonPrint::getBumon($db);
        $_dataApp = $this->_defaultDataApp();
        $data = array(
            'dataApp'      => $_dataApp,
            'bindings' => self::$bindings,
            'status' => 'OK',
            'msg' => ''
        );
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_json = $json;
        App_Smarty::pushCssFile(['app/saiken.print.siji.css']);
        App_Smarty::pushCssFile(['app/saiken.printryoshu.siji.css']);
        App_Smarty::pushJsFile(['app/saiken.printryoshu.siji.js']);
    }

    /**
     * 初期値を返す
     *
     * <AUTHOR> Sugiyama
     * @since  2020/12/xx
     */
    protected function _defaultDataApp() {
        $db = Msi_Sys_DbManager::getMyDb();
        $bumon_cd = App_Utils::getTantoBumonCd();
        $oya_bumon_cd = DataMapper_BumonMst::findOyaBumonCd($db, $bumon_cd);
        $bumon_info = DataMapper_BumonEx::find($db, array('bumon_cd' => $oya_bumon_cd));
        if(count($bumon_info) > 0){
            $s_bumon_cd = $oya_bumon_cd;
            $s_bumon_nm = $bumon_info[0]['bumon_lnm'];
        }        
        $dataApp = array(
            'report_cd'     => 'pdf1202',
            'print_ymd'     => Msi_Sys_Utils::getDate(), // 印刷年月日
            'taisho_st_ymd' => Msi_Sys_Utils::getDate(strtotime('first day of')), // 対象年月日（自）
            'taisho_ed_ymd' => Msi_Sys_Utils::getDate(), // 対象年月日（至）
            's_bumon_cd'    => $s_bumon_cd,
            's_bumon_nm'    => $s_bumon_nm,
        );
        return $dataApp;
    }
}
