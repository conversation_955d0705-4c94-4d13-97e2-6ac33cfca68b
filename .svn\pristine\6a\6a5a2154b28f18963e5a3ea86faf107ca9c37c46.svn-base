var appcst = appcst || {};
var appgjk = appgjk || {}; // 互助会ライブラリ
var appsk = appsk || {};// 請求先ライブラリ
$(function () {
    "use strict";

    // 画面クラスとモデルのプロパティのオブジェクト
    appcst.pro = {
        d_free1: '#uketuke_date',
        zip_no1: '#zip_temple',
        tel_no1: '#tel_temple',
        i_free1: '#sinzoku_ninzu',
        i_free2: '#doshi_ninzu',
        i_free3: '#tonari_ninzu',
        i_free4: '#microbus_daisu',
        ts_free1: '#gakki_date',
        ts_free2: '#hyakuki_date',
        ts_free3: '#higan_date',
        ts_free4: '#bon_date', //2年盆日付
        ts_free5: '#ki_houyo_date',
        // 基本タブ
        v_free13: '.kaiin_cd', // 会員コード
        kaiin_nm: '.kaiin_nm', // 会員名
        kaiin_kbnnm: '.kaiin_kbnnm', // 会員区分名
        kaiin_no: '#kaiin_no', // 会員番号
        moushi_cd: '#apply_type', // 申込区分
        moushi_cd2: '#apply_type2', // 申込区分
        sougi_cd: '#funeral_type', // 葬儀区分
        kaiin_cd: '#member', // 会員
        daicho_no_eria: '#code_1', // 台帳番号1
        daicho_no_mm: '#code_2', // 台帳番号2
        daicho_no_seq: '#code_3', // 台帳番号3
        kaiin_sonota: '#member_detail', // 会員その他
        uketuke_tanto_cd: '#staff_1', // 受付担当者コード
        k_nm: '#input-tab #name', // 故人名
        k_knm: '#input-tab #kana', // 故人カナ
        k_nenrei_man: '#input-tab #age_at_death', // 故人年齢
        k_death_ymd: '#input-tab #date_death',
        k_haigu_cd: '#input-tab #spouse',
        k_seinengappi_ymd: '#input-tab #birthday_date',
        kg_yubin_no: '#input-tab #zip_1',
        kg_addr1: '#input-tab #address_1_1',
        kg_tel: '#input-tab #tel_1',
        kg_addr2: '#input-tab #address_1_2',
        kg_setai_cd: '#input-tab #head_1',
        nb_kazari_date: '#nb_kazari_ymd, #nb_kazari_time',
        nb_kazari_ymd: '#nb_kazari_ymd', //お飾り希望日
        nb_kazari_time: '#nb_kazari_time', //お飾り希望日時
        nb_kataduke_date: '#nb_kataduke_ymd, #nb_kataduke_time',
        nb_kataduke_ymd: '#nb_kataduke_ymd', //お片付け希望日
        nb_kataduke_time: '#nb_kataduke_time', //お片付け希望日時
        free3_code_cd: '.kazari_basho_kbn',
        v_free7: '.kazari_basho_nm', //飾り備考
        v_free8: '.kataduke_basho_nm', //片付け備考
        kj_yubin_no: '#input-tab #zip_2',
        kj_addr1: '#input-tab #address_2_1',
        kj_tel: '#input-tab #tel_2',
        kj_addr2: '#input-tab #address_2_2',
        kj_setai_cd: '#input-tab #head_2',
        kh_yubin_no: '#input-tab #zip_3',
        kh_addr1: '#input-tab #address_3_1',
        kh_hito_cd: '#input-tab #head_3',
        kh_addr2: '#input-tab #address_3_2',
        dm_print_kbn: '.cls_dm_print .select-container',
        dm_print_mi_ryu: '#dm_riyu',
        ryoshu_nm: '#ryoshu_nm', // 領収名         施行基本情報 v_free14
        kaishu_ymd: '#kaishu_ymd', //ご精算予定日
        // 基本タブ日程
        nitei_ymd: '.nitei_date, .nitei_time',
        nitei_date: '.nitei_date',
        nitei_time: '.nitei_time',
        basho_nm: '.basho_nm',
        // 喪主タブ
        m_nm: '#infochief-tab #chief #name', // 喪主名
        m_knm: '#infochief-tab #chief #kana', // 喪主カナ
        m_gengo: '#infochief-tab #chief #birthday_era', // 生年月日年号
        m_seinengappi_ymd: '#infochief-tab #chief #birthday_date', // 生年月日和暦
        mg_yubin_no: '#infochief-tab #chief #zip_1', // 現住所郵便番号
        mg_addr1: '#infochief-tab #chief #address_1_1', // 現住所1
        mg_tel: '#infochief-tab #chief #tel_1', // 現住所電話番号
        mg_m_tel: '#infochief-tab #chief #mobile_tel_1',
        mg_addr2: '#infochief-tab #chief #address_1_2', // 現住所2
        mj_yubin_no: '#infochief-tab #chief #zip_2',
        mj_addr1: '#infochief-tab #chief #address_2_1',
        mj_tel: '#infochief-tab #chief #tel_2',
        mj_addr2: '#infochief-tab #chief #address_2_2',
        mh_yubin_no: '#infochief-tab #chief #zip_3', // 勤務先郵便番号
        mh_addr1: '#infochief-tab #chief #address_3_1', // 勤務先住所1
        mh_addr2: '#infochief-tab #chief #address_3_2', // 勤務先住所2
        mk_kinmusaki_kbn: '#infochief-tab #chief #employee', // 勤務先区分
        mk_kinmusaki_nm: '#infochief-tab #chief #company', // 勤務先名
        mk_tel: '#infochief-tab #chief #company_tel', // 勤務先電話番号
        mk_yakusyoku_nm: '#infochief-tab #chief #position', // 役職/職種
        mk_fax: '#infochief-tab #chief #company_fax', // 勤務先FAX
        // 喪主タブ 請求先情報
        sekyu_nm: '#infochief-tab #bill #name', // 請求先名
        sekyu_knm: '#infochief-tab #bill #kana', // 請求先カナ
        sekyu_moshu_kankei: '#bill_relationship_name', // 喪主との関係
        sekyu_yubin_no: '#zip_4', // 現住所郵便番号
        sekyu_addr1: '#address_4_1', // 現住所1
        sekyu_tel: '#tel_4', // 現住所電話番号
        mobile_tel: '#mobile_tel_2',
        sekyu_addr2: '#address_4_2', // 現住所2
        sekyu_biko1: '#memo', // 備考
        souke_nm: '#infochief-tab #family_name',
        souke_knm: '#infochief-tab #family_name_kana',
        // 互助会確認タブ
        kain_no: '.i_member_id', // 加入番号
        kanyu_nm: '.i_member_name', // 加入名
        yoto_kbn: '.i_usage .select-container', // 用途
        keiyaku_gaku: '.i_deposit', // 契約金額
        harai_gaku: '.i_pay', // 払込金額
        harai_no: '.i_times', // 回数
        wari_gaku: '.i_discount', // 前納割引
        cose_chg_gaku: '.i_balance', // コース変更差額
        early_use_cost: '.i_early', // 早期利用費
        kanyu_dt: '.i_entry', // 加入年月日
        zei_kijyn_ymd: '.i_tax', // 消費税基準日
        kanyu_dantai_ext: '#other_entry_name', // 加入団体その他
        riyu_memo: '#change_reason', // 理由
        plan_use_prc: '#plan_use_prc',
        plan_change_prc: '#plan_change_prc',

        bumon_cd: '#hall_cd', // 売上部門コード
        biko2: '#customer-tab #memo2', // 備考（お客様用）
        biko1: '#customer-tab #alt_careful_memo',
        nb_biko: '#memo3', // 法事項目タブ特記事項
    };

    // 赤字クラスの追加削除処理
    appcst.toggleAkajiClass = function (that, targets) {
        _.each(targets, function (val) {
            var target = that.model.get(val);
            if (!$.msiJqlib.isNullEx2(target) && target < 0) {
                that.$(appcst.pro[val]).addClass('com-akaji');
            } else {
                that.$(appcst.pro[val]).removeClass('com-akaji');
            }
        });
    };


    /**
     * 日付と時刻を結合してタイムスタンプに設定する処理
     * @param {string} niteiDate 日付 
     * @param {string} niteiTime 時刻
     * @param {string} pro モデルの属性
     * @param {model} model モデル
     */
    var _setNiteiymd = function (niteiDate, niteiTime, pro, model) {
        var niteiymd = null;
        if (!$.msiJqlib.isNullEx2(niteiDate)) {
            niteiymd = niteiDate;
        }
        if (!$.msiJqlib.isNullEx2(niteiTime)) {
            if (!$.msiJqlib.isNullEx2(niteiymd)) {
                niteiymd += " ";
            }
            niteiymd += niteiTime;
        }
        model.set(pro, niteiymd);
    };
    // 場所区分によるpickup入力フィールドの活性・非活性
    var _setSpotStatus = function (val, $elPickNm, $elPickDlg, disable) {
        $elPickNm.attr("readonly", "readonly");
        // 未設定または自宅
        if ($.msiJqlib.isNullEx2(val) || val === "0") { // 0:自宅
            $elPickNm.attr("disabled", "disabled");
            $elPickDlg.addClass("disabled");
        } else if (val === "5" || val === "9" || val === "99") {
            $elPickNm.removeAttr("readonly");
            $elPickNm.removeAttr("disabled");
            $elPickDlg.addClass("disabled");
        } else {
            $elPickNm.removeAttr("readonly");
            $elPickNm.removeAttr("disabled");
            $elPickDlg.removeClass("disabled");
        }
    };

    var _setKazariSpotStatus = function (val, $elPickNm, disable) {
        $elPickNm.attr("readonly", "readonly");
        // 未設定または自宅
        if ($.msiJqlib.isNullEx2(val) || val === "0") { // 0:自宅
            $elPickNm.attr("disabled", "disabled");
        } else {
            $elPickNm.removeAttr("readonly");
            $elPickNm.removeAttr("disabled");
        }
    };

    // 有無select2内容
    var _umu_kbn = [{id: '0', text: 'なし'}, {id: '1', text: 'あり'}];
    // 勤務先select2内容
    var _employee = [{id: '1', text: '元'}, {id: '2', text: '現'}];

    // 画像ヘルパー処理 
    var _gazoHelper = function (t, hookData, gazo) {
        var m = t.model;
        t.$el.msiPickHelper({
            action: 'gazo',
            onSelect: function (data) {
                if (data.gazo_img) {
                    m.set(gazo.oid, data.gazo_img);
                } else {
                    m.set(gazo.oid, null);
                }
                m.set(gazo.code, data.code);
                m.set(gazo.name, data.gazo_nm);
            },
            onClear: function () {
                m.set(gazo.oid, null);
                m.set(gazo.code, null);
                m.set(gazo.name, null);
            },
            hookSetData: function () {
                return hookData;
            }
        });
    };

    /**
     * @description 画面全体処理
     */
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                // 基本タブ
                bumon_cd: null, // 売上部門コード
//                bumon_cd: '00010', // 売上部門コード
                v_free13: null, // 会員番号   
                kaiin_nm: null, // 会員名
                kaiin_kbnnm: null, // 会員区分名	
                seko_no: null, // 施行番号
                moushi_cd: "2", // 申込コード
                moushi_kbn: "2", // 申込区分
                moushi_cd2: "2", // 申込コード 法事と催事
                sougi_cd: "1", // 葬儀コード
                sougi_kbn: "1", // 葬儀区分
                daicho_no_eria: null, // 台帳番号1
                daicho_no_mm: null, // 台帳番号2
                daicho_no_seq: null, // 台帳番号3
                v_free2: null, // 整理No
                p_info_cd: null, // 個人情報保護コード
                p_info_kbn: null, // 個人情報保護区分
                kaiin_cd: "1", // 会員コード
                kaiin_kbn: "1", // 会員区分
                kaiin_sonota: null, // 会員区分その他
                kaiin_no: null, // 会員番号
                uketuke_tanto_cd: null, // 受付担当者コード
                uketuke_tanto_nm: null, // 受付担当者名
                seko_tanto_cd: null, // 施行担当者コード
                seko_tanto_nm: null, // 施行担当者名
                k_nm: null, // 故人名
                k_file_nm: null, // 添付ファイル名OID
                k_file: null, // 添付ファイルOID一時
                k_sex_cd: "1", // 性別コード
                k_sex_kbn: "1", // 性別区分
                k_haigu_cd: null, // 配偶者コード
                k_haigu_kbn: null, // 配偶者区分
                k_knm: null, // 故人カナ名
                k_gengo: "S", // 生年月日元号
                k_seinengappi_ymd: null, // 生年月日
                k_nenrei_man: null, // 故人年齢
                k_nenrei_kyounen: null, // 享年
                kaishu_ymd: null, // ご精算予定日
                ryoshu_nm: null, // 領収名
                kg_yubin_no: null, // 現住所郵便番号
                kg_addr1: null, // 現住所1
                kg_tel: null, // 現住所TEL
                kg_addr2: null, // 現住所2
                kg_setai_cd: "1", // 世帯主コード
                kg_setai_kbn: "1", // 世帯主区分
                nb_kazari_date: null,
                nb_kazari_ymd: null, //お飾り希望日
                nb_kazari_time: null, //お飾り希望日時
                nb_kataduke_date: null,
                nb_kataduke_ymd: null, //お片付け希望日
                nb_kataduke_time: null, //お片付け希望日時
                kj_kbn: "0", // 住民登録住所の現住所に同じチェックボックス
                kj_yubin_no: null, // 住民登録住所郵便番号
                kj_addr1: null, // 住民登録住所1
                kj_tel: null, // 住民登録住所TEL
                kj_addr2: null, // 住民登録住所2
                kj_setai_cd: "1", // 住民登録住所世帯主コード
                kj_setai_kbn: "1", // 住民登録住所世帯主区分
                kh_kbn: "0", // 本籍の現住所に同じチェックボックス
                kh_yubin_no: null, // 本籍郵便番号
                kh_addr1: null, // 本籍住所1
                kh_hito_cd: "1", // 筆頭者コード
                kh_hito_kbn: "1", // 筆頭者区分
                kh_addr2: null, // 本籍住所2
                sg_seko_kbn: null, // 葬儀施行区分
                hj_seko_kbn: null, // 法事施行区分
                sk_houyo_cd: "0", // 施行法要区分コード
                sk_houyo_kbn: "0", // 施行法要区分
                k_kaimyo: null, // 故人戒名
                k_death_ymd: null, // 故人亡日
                free2_cd: null, // 葬儀・法事施行番号
                syushi_cd: null, // 宗旨コード
                syushi_kbn: null, // 宗旨区分
                syuha_cd: null, // 宗派コード
                syuha_kbn: null, // 宗派区分
                syuha_nm: null, // 宗派名
                syuha_knm: null, // 宗派名カナ
                jyusho_cd: null, // 寺院コード
                jyusho_nm: null, // 寺院名
                jyusho_knm: null, // 寺院カナ名
                v_free10: null, // 催事の件名
                // 喪主タブ
                m_nm: null, // 喪主名
                m_knm: null, // 喪主名カナ
                m_file_nm: null, // 喪主添付ファイル
                m_file: null, // 喪主添付ファイルOID一時
                m_gengo: "S", // 喪主生年月日元号
                m_seinengappi_ymd: null, // 喪主生年月日
                m_nenrei_man: null, // 喪主年齢
                mg_kbn: "0", // 故人に同じチェックボックス
                m_zoku_cd: null, // 喪主続柄コード
                m_zoku_kbn: null, // 喪主続柄区分
                m_zoku_cd2: null, // 喪主続柄コード
                m_zoku_kbn2: null, // 喪主続柄区分
                mg_yubin_no: null, // 喪主現住所郵便番号
                mg_addr1: null, // 喪主現住所1
                mg_tel: null, // 喪主現住所TEL
                mg_m_tel: null, // 喪主携帯
                mg_addr2: null, // 喪主現住所2
                mj_kbn: "0", // 住民登録住所の故人に同じチェックボックス
                mj_yubin_no: null, // 喪主住民登録住所郵便番号
                mj_addr1: null, // 喪主住民登録住所1
                mj_tel: null, // 喪主住民登録住所TEL
                mj_addr2: null, // 喪主住民登録住所2
                mk_kinmusaki_kbn: null, // 喪主勤務先
                mk_kinmusaki_nm: null, // 喪主勤務先名
                mk_tel: null, // 喪主勤務先TEL
                mk_yakusyoku_nm: null, // 喪主役職／職種
                mk_fax: null, // 喪主勤務先FAX
                sekyu_kbn: "0", // 請求先の喪主に同じチェックボックス
                dm_print_kbn: null, // DM発行区分
                dm_soufu_kbn: null, // DM送付先区分
                dm_print_mi_ryu: null, // DM未発行理由
                souke_nm: null, // 葬家
                souke_knm: null, // 葬家カナ
                biko1: null,
                biko2: null,
                nb_biko: null,
            };
        },
        validation: {
//            dm_print_kbn: {
//                required: true
//            },
//            dm_print_mi_ryu: {
//                required: function () {
//                    if (this.get("dm_print_kbn") === "1") {
//                        return true;
//                    } else {
//                        return false;
//                    }
//                }
//            },
            v_free13: {// 会員番号   
                required: false
            },
            kaiin_nm: {// 会員名
                required: false
            },
            kaiin_kbnnm: {// 会員区分名	
                required: false
            },
            moushi_cd: {
                required: true
            },
            moushi_cd2: {
                required: true
            },
            sougi_cd: {
                required: function () {
                    return true;
                }
            },
            daicho_no_eria: {
                required: false,
                maxLength: 2
            },
            daicho_no_mm: {
                required: false,
                maxLength: 2
            },
            daicho_no_seq: {
                required: false,
                maxLength: 5
            },
            v_free2: {
                required: false,
                maxLength: 10,
            },
            kaiin_cd: {
                required: true
            },
            k_nm: {
                required: false,
                maxLength: 30
            },
            uketuke_tanto_cd: {
                required: true
            },
            kaiin_sonota: {
                required: false,
                maxLength: 20
            },
            k_knm: {
                required: false,
                maxLength: 30
            },
            k_nenrei_kyounen: {
                required: false,
                pattern: 'digits'
            },
            k_nenrei_man: {
                required: false,
                pattern: 'digits'
            },
            k_seinengappi_ymd: "validateSeinengappi",
            kg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            kj_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            kh_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            kg_tel: {
                required: false,
                pattern: 'tel'
            },
            kj_tel: {
                required: false,
                pattern: 'tel'
            },
            kk_tel: {
                required: false,
                pattern: 'tel'
            },
            kg_addr1: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kg_addr2: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kj_addr1: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kj_addr2: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kh_addr1: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            kh_addr2: {
                required: false,
                //pattern: 'zenkaku',
                maxLength: 30
            },
            m_nm: {
                required: true,
                maxLength: 30
            },
            m_knm: {
                required: false,
                maxLength: 30
            },
            m_gengo: {
                required: true
            },
            m_seinengappi_ymd: "validateSeinengappiM",
            mg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            mg_tel: {
                required: false,
                pattern: 'tel'
            },
            mg_m_tel: {
                required: false,
                pattern: 'tel'
            },
            mg_addr1: {
                required: false,
                maxLength: 30
            },
            mg_addr2: {
                required: false,
                maxLength: 30
            },
            mj_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            mj_tel: {
                required: false,
                pattern: 'tel'
            },
            mj_addr1: {
                required: false,
                maxLength: 30
            },
            mj_addr2: {
                required: false,
                maxLength: 30
            },
            mk_kinmusaki_nm: {
                required: false,
                maxLength: 30
            },
            mk_tel: {
                required: false,
                pattern: 'tel'
            },
            mk_yakusyoku_nm: {
                required: false,
                maxLength: 30
            },
            mk_fax: {
                required: false,
                pattern: 'tel'
            },
            k_death_ymd: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
            kaishu_ymd: {
                required: function () {
                    if (!$.msiJqlib.isNullEx2(appcst.appModel.get("jichu_kakute_ymd"))) {
                        return true;
                    } else {
                        return false;
                    }
                },
                fn: Backbone.Validation.msi_v_fn.ymd
            },
            bumon_cd: {
                required: true,
            },
            ryoshu_nm: {// 領収名
                required: false,
                maxLength: 60
            },
            biko1: {
                required: false,
                maxLength: 250
            },
            //biko2: {             // 備考（お客様用）
            //    required: false,
            //    maxLength: 250
            //},
            nb_kazari_ymd: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
            nb_kazari_time: {
                required: false,
                pattern: 'time'
            },
            nb_kataduke_ymd: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
            nb_kataduke_time: {
                required: false,
                pattern: 'time'
            },
            basho_nm: {
                required: false,
                maxLength: 30
            }
        },
        labels: {
            dm_print_kbn: 'DM発行区分',
            dm_print_mi_ryu: 'DM未発行理由',
            v_free13: '会員番号',
            kaiin_nm: '会員名',
            kaiin_kbnnm: '会員区分名',
            moushi_cd: '申込区分',
            moushi_cd2: '申込区分',
            kaiin_cd: '会員区分',
            sougi_cd: '葬儀区分',
            daicho_no_eria: '台帳番号（エリア）',
            daicho_no_mm: '台帳番号（月）',
            daicho_no_seq: '台帳番号（連番）',
            v_free2:'整理No',
            kaiin_sonota: '会員区分（その他）',
            uketuke_tanto_cd: '受付担当者',
            k_nm: '故人名',
            k_knm: '故人カナ名',
            k_gengo: '生年月日元号',
            k_nenrei_man: '年齢',
            kg_addr1: '現住所1',
            kg_addr2: '現住所2',
            kg_tel: '現住所TEL',
            kj_addr1: '住民登録住所1',
            kj_addr2: '住民登録住所2',
            kh_addr1: '本籍住所1',
            kh_addr2: '本籍住所2',
            ryoshu_nm: '領収名',
            kk_kinmusaki_nm: '勤務先名',
            kk_yakusyoku_nm: '役職／職種',
            m_nm: '施主名',
            m_knm: '施主名カナ',
            m_gengo: '施主生年月日元号',
            mg_yubin_no: '施主現住所郵便番号',
            mg_addr1: '施主現住所1',
            mg_addr2: '施主現住所2',
            mg_tel: '施主現住所TEL',
            mg_m_tel: '施主携帯番号',
            mj_yubin_no: '施主住民登録住所郵便番号',
            mj_addr1: '施主住民登録住所1',
            mj_addr2: '施主住民登録住所2',
            mj_tel: '施主住民登録住所TEL',
            mk_kinmusaki_nm: '施主勤務先名',
            mk_tel: '施主勤務先TEL',
            mk_yakusyoku_nm: '施主役職／職種',
            mk_fax: '施主勤務先FAX',
            kaishu_ymd: 'ご精算予定日',
            bumon_cd: '部門コード',
            biko1: '備考（お客様用）',
            biko2: '注意事項(当社確認用)',
            nb_biko: '特記事項',
            nb_kazari_date: 'お飾り希望日',
            nb_kazari_ymd: 'お飾り希望日',
            nb_kazari_time: 'お飾り希望時間',
            nb_kataduke_date: 'お片付け希望日',
            nb_kataduke_ymd: 'お片付け希望日',
            nb_kataduke_time: 'お片付け希望時間',
        },
        validateSeinengappi: function (value, attr, computedState) {
            var gengo = computedState.k_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return $.msiJqlib.validateWareki(gengo, value);
            }
        },
        validateSeinengappiM: function (value, attr, computedState) {
            var gengo = computedState.m_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return $.msiJqlib.validateWareki(gengo, value);
            }
        }
    }); // AppModel

    var AppViewDef = {
        el: $("#wrapper"),
        events: {
            "click .tab li a": "changeTab",
            "click .seikyukaiin-ref": "seikyukaiinHelper",
            "click #kaiin_cd": "seikyukaiinHelper",
            "click #staff_1, .label.dlg_staff1": "uketukeHelper",
            "click #staff_2, .label.dlg_staff2": "sekoHelper",
            "click #btn_save": "doSave",
            "click #btn_print": "doPrint",
            "click #btn_delete": "doDelete",
            "click #btn_cancel": "doCancel",
            "click #detail .label.dlg_zip": "zipHelper",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
            "click .dlg_s_seko_no": "sekoInfoHelper",
            "click #seko_clear": function () {
                appcst.appModel.set("free2_cd", null);
            },
            "click #btn_copy": "copySekoInfo",
            "click #detail #temple, .label.dlg_temple": "nmjyushoHelper",
            "click #male": function () {
                // 基本タブ 性別（男）設定
                this.model.set("k_sex_cd", "1");
                this.model.set("k_sex_kbn", "1");
            },
            "click #female": function () {
                // 基本タブ 性別（女）設定
                this.model.set("k_sex_cd", "2");
                this.model.set("k_sex_kbn", "2");
            },
            "change #input-tab #as_address_2": function (e) {
                // 基本タブ 住民登録住所の現住所に同じチェックボックス値を設定
                this.setCheckBox(e, "kj_kbn", '#input-tab #as_address_2');
                if (this.model.get('kj_kbn') === "1") {
                    this.model.set('kj_yubin_no', null);
                    this.model.set('kj_addr1', null);
                    this.model.set('kj_addr2', null);
                    this.model.set('kj_tel', null);
                    this.$("#input-tab #zip_2").attr("disabled", "disabled");
                    this.$("#input-tab #address_2_1").attr("disabled", "disabled");
                    this.$("#input-tab #address_2_2").attr("disabled", "disabled");
                    this.$("#input-tab #tel_2").attr("disabled", "disabled");
//                    this.$("#zip_2").next(".dlg_zip").addClass("disabled");
                } else {
                    this.$("#input-tab #zip_2").removeAttr("disabled");
                    this.$("#input-tab #address_2_1").removeAttr("disabled");
                    this.$("#input-tab #address_2_2").removeAttr("disabled");
                    this.$("#input-tab #tel_2").removeAttr("disabled");
//                    this.$("#zip_2").next(".dlg_zip").removeClass("disabled");
                }
            },
            "change #input-tab #as_address_3": function (e) {
                // 基本タブ 本籍の現住所に同じチェックボックス値を設定
                this.setCheckBox(e, "kh_kbn", '#input-tab #as_address_3');
                if (this.model.get('kh_kbn') === "1") {
                    this.model.set('kh_yubin_no', null);
                    this.model.set('kh_addr1', null);
                    this.model.set('kh_addr2', null);
                    this.$("#input-tab #zip_3").attr("disabled", "disabled");
                    this.$("#input-tab #address_3_1").attr("disabled", "disabled");
                    this.$("#input-tab #address_3_2").attr("disabled", "disabled");
//                    this.$("#input-tab #zip_3").next(".dlg_zip").addClass("disabled");
                } else {
                    this.$("#input-tab #zip_3").removeAttr("disabled");
                    this.$("#input-tab #address_3_1").removeAttr("disabled");
                    this.$("#input-tab #address_3_2").removeAttr("disabled");
//                    this.$("#input-tab #zip_3").next(".dlg_zip").removeClass("disabled");
                }
            },
            "change #infochief-tab #as_address_4": function (e) {
                // 喪主タブ 故人に同じチェックボックス値を設定
                this.setCheckBox(e, "mg_kbn", '#infochief-tab #as_address_4');
                if (this.model.get('mg_kbn') === "1") {
                    this.model.set('mg_yubin_no', null);
                    this.model.set('mg_addr1', null);
                    this.model.set('mg_addr2', null);
                    this.model.set('mg_tel', null);
                    this.$("#infochief-tab #zip_1").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_1_1").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_1_2").attr("disabled", "disabled");
                    this.$("#infochief-tab #tel_1").attr("disabled", "disabled");
//                    this.$("#infochief-tab #zip_1").next(".dlg_zip").addClass("disabled");
                    // 葬儀情報(ヘッダー)の喪主バインディング処理 故人の現住所を設定
                    this.setHeadeMaddr(this.model.get('kg_addr1'), this.model.get('kg_addr2'));
                } else {
                    this.$("#infochief-tab #zip_1").removeAttr("disabled");
                    this.$("#infochief-tab #address_1_1").removeAttr("disabled");
                    this.$("#infochief-tab #address_1_2").removeAttr("disabled");
                    this.$("#infochief-tab #tel_1").removeAttr("disabled");
//                    this.$("#infochief-tab #zip_1").next(".dlg_zip").removeClass("disabled");
                    // // 葬儀情報(ヘッダー)の喪主バインディング処理 喪主の現住所を設定
                    this.setHeadeMaddr(this.model.get('mg_addr1'), this.model.get('mg_addr2'));
                }
            },
            "change #infochief-tab #as_address_5, #infochief-tab #as_address_5_2": function (e) {
                this.model.set('mj_kbn', "0");
                var $target = $(e.currentTarget);
                if ($target.is("#as_address_5")) {
                    var val = $('#infochief-tab #as_address_5:checked').val();
                    if (val === "1") {
                        $('#infochief-tab #as_address_5_2').attr("checked", false).button("refresh");
                        this.model.set('mj_kbn', "1");
                    }
                } else if ($target.is("#as_address_5_2")) {
                    var val = $('#infochief-tab #as_address_5_2:checked').val();
                    if (val === "1") {
                        $('#infochief-tab #as_address_5').attr("checked", false).button("refresh");
                        this.model.set('mj_kbn', "2");
                    }
                } else {
                    return;
                }
                var mj_kbn = this.model.get('mj_kbn');
                if (mj_kbn === "1" || mj_kbn === "2") {
                    this.model.set('mj_yubin_no', null);
                    this.model.set('mj_addr1', null);
                    this.model.set('mj_addr2', null);
                    this.model.set('mj_tel', null);
                    this.$("#infochief-tab #zip_2").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_2_1").attr("disabled", "disabled");
                    this.$("#infochief-tab #address_2_2").attr("disabled", "disabled");
                    this.$("#infochief-tab #tel_2").attr("disabled", "disabled");
//                    this.$("#infochief-tab #zip_2").next(".dlg_zip").addClass("disabled");
                } else {
                    this.$("#infochief-tab #zip_2").removeAttr("disabled");
                    this.$("#infochief-tab #address_2_1").removeAttr("disabled");
                    this.$("#infochief-tab #address_2_2").removeAttr("disabled");
                    this.$("#infochief-tab #tel_2").removeAttr("disabled");
//                    this.$("#infochief-tab #zip_2").next(".dlg_zip").removeClass("disabled");
                }
            },
            "change #infochief-tab #as_chief": function (e) {
                // 喪主タブ 請求先 喪主に同じチェックボックス値を設定
                this.setCheckBox(e, "sekyu_kbn", '#infochief-tab #as_chief');
                // 喪主タブ 請求先 喪主に同じチェックボックス値変更時処理
                appsk.setSeikyu(this);
                var ryoshu_nm = this.model.get("ryoshu_nm");
                if ($.msiJqlib.isNullEx2(ryoshu_nm)) {
                    this.model.set("ryoshu_nm", this.model.get("m_nm"));
                }
            },
            "change #input-tab #birthday_date": "calcNereiK",
            "change #input-tab #birthday_era": "calcNereiK",
            "change #input-tab #date_death": "calcNereiK",
            "change #infochief-tab #chief #birthday_date": "calcNereiM",
            "change #infochief-tab #chief #birthday_era": "calcNereiM",
            "select2-opening #syuha_cd": function () {
                // 宗派コードを開いたときに宗旨の区分で絞り込んで表示する
                var syushiCd = this.model.get("syushi_cd");
                var orgKbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
                var fileredKbns = [];
                _.each(orgKbns, function (item) {
//                    if (item.kbn_value_cd_num === syushiCd) {
                    fileredKbns.push(item);
//                    }
                });
                appcst.syuha_kbns = fileredKbns;
            },
            "change #syushi_cd": "setShuhaOther",
            "select2-selecting #syushi_cd": "clearSyuha",
            "select2-clearing #syushi_cd": "clearSyuha",
        },
        bindings: {
            '#hall_cd': {
                observe: 'bumon_cd',
                updateView: false
            },
            '#kaiin_cd': {// 会員番号   
                observe: 'v_free13',
            },
            '#kaiin_nm': {// 会員名
                observe: 'kaiin_nm',
            },
            '#kaiin_kbnnm': {// 会員区分名	
                observe: 'kaiin_kbnnm',
            },
            '#kaiin_no': 'kaiin_no',
            '#apply_type': {
                observe: 'moushi_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'moushi_kbn');
                    return $el.val();
                }
            },
            '#apply_type2': {
                observe: 'moushi_cd2',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                },
                getVal: function ($el, event, options) {
                    return $el.val();
                }
            },
            '#funeral_type': {
                observe: 'sougi_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'sougi_kbn');
                    return $el.val();
                }
            },
            '#personal_info': {
                observe: 'p_info_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'p_info_kbn');
                    return $el.val();
                }
            },
            '#member': {
                observe: 'kaiin_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                },
                // 葬儀情報(ヘッダー)の会員バインディング処理
                getVal: function ($el, event, options) {
                    var item = $el.select2("data");
                    this.$("#hd_kaiin").text(item.text);
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'kaiin_kbn');
                    return $el.val();
                }
            },
            '#code_1': {
                observe: 'daicho_no_eria',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo($el.val(), this.model.get("daicho_no_mm"), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_2': {
                observe: 'daicho_no_mm',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), $el.val(), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_3': {
                observe: 'daicho_no_seq',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), this.model.get("daicho_no_mm"), $el.val());
                    return $el.val();
                }
            },
            '#seiri_no': 'v_free2', // 整理No
            '#member_detail': {
                observe: 'kaiin_sonota',
                // 葬儀情報(ヘッダー)の会員その他バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_seko_kaiin_sonota").text($el.val());
                    return $el.val();
                }
            },
            '#staff_1': {
                observe: 'uketuke_tanto_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('uketuke_tanto_cd', null);
                    }
                    return val;
                }
            },
            '#staff_2': {
                observe: 'seko_tanto_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('seko_tanto_cd', null);
                    }
                    return val;
                },
                // select2摘要時に修正が必要 葬儀情報(ヘッダー)の施行担当者バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_seko_tanto").text($el.val());
                    return $el.val();
                }
            },
            '#input-tab #name': {
                observe: 'k_nm',
                // 葬儀情報(ヘッダー)の故人名バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_k_nm").text($el.val());
                    return $el.val();
                }
            },
            '#input-tab #kana': 'k_knm',
            '#input-tab #birthday_era': {
                observe: 'k_gengo',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
            '#input-tab #birthday_date': {
                observe: 'k_seinengappi_ymd'
            },
            '#input-tab #age': {
                observe: 'k_nenrei_man',
                onGet: function (val, options) {
                    if (!$.msiJqlib.isNullEx2(val)) {
                        return '満 ' + val + ' 歳';
                    }
                }
            },
            '#input-tab #age_at_death': {
                observe: 'k_nenrei_man'
            },
            '#infochief-tab #s_seko_no': 'free2_cd',
            '#sougi_seko': {
                observe: 'sg_seko_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
            '#houji_seko': {
                observe: 'hj_seko_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
            '#nengo': {
                observe: 'sk_houyo_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'sk_houyo_kbn');
                    return $el.val();
                }
            },
            '#input-tab #kaimyo': 'k_kaimyo',
            '#infochief-tab #houyo_kenmei': 'v_free10',
            '#input-tab #date_death': 'k_death_ymd',
            '#input-tab #zip_1': 'kg_yubin_no',
            '#input-tab #address_1_1': 'kg_addr1',
            '#input-tab #tel_1': 'kg_tel',
            '#input-tab #address_1_2': 'kg_addr2',
            '#input-tab #head_1': {
                observe: 'kg_setai_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'kg_setai_kbn');
                    return $el.val();
                }
            },
//            'as_address_2':'',
            '#input-tab #zip_2': 'kj_yubin_no',
            '#input-tab #address_2_1': 'kj_addr1',
            '#input-tab #tel_2': 'kj_tel',
            '#input-tab #address_2_2': 'kj_addr2',
            '#input-tab #head_2': {
                observe: 'kj_setai_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'kj_setai_kbn');
                    return $el.val();
                }
            },
            '#input-tab #zip_3': 'kh_yubin_no',
            '#input-tab #address_3_1': 'kh_addr1',
            '#input-tab #head_3': {
                observe: 'kh_hito_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'kh_hito_kbn');
                    return $el.val();
                }
            },
            '#input-tab #address_3_2': 'kh_addr2',
            '#infochief-tab #chief #name': {
                observe: 'm_nm',
                // 葬儀情報(ヘッダー)の喪主名バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_m_nm").text($el.val());
                    return $el.val();
                }
            },
            '#infochief-tab #chief #s_chief_relationship': {
                observe: 'm_zoku_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'm_zoku_kbn');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #s_chief_relationship2': {
                observe: 'm_zoku_cd2',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'm_zoku_kbn2');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #kana': 'm_knm',
            '#infochief-tab #chief #birthday_era': {
                observe: 'm_gengo',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
            '#infochief-tab #chief #birthday_date': {
                observe: 'm_seinengappi_ymd'
            },
            '#infochief-tab #chief #age': {
                observe: 'm_nenrei_man',
                onGet: function (val, options) {
                    if (!$.msiJqlib.isNullEx2(val)) {
                        return '満' + val + '歳';
                    }
                }
            },
            '#infochief-tab #chief #zip_1': 'mg_yubin_no',
            '#infochief-tab #chief #address_1_1': {
                observe: 'mg_addr1',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                    return $el.val();
                },
                afterUpdate: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                }
            },
            '#infochief-tab #chief #tel_1': 'mg_tel',
            '#infochief-tab #chief #mobile_tel_1': 'mg_m_tel',
            '#infochief-tab #chief #address_1_2': {
                observe: 'mg_addr2',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr1');
                    this.setHeadeMaddr(address, $el.val());
                    return $el.val();
                }
            },
            '#infochief-tab #chief #zip_2': 'mj_yubin_no',
            '#infochief-tab #chief #address_2_1': 'mj_addr1',
            '#infochief-tab #chief #tel_2': 'mj_tel',
            '#infochief-tab #chief #address_2_2': 'mj_addr2',
            '#infochief-tab #chief #employee': {
                observe: 'mk_kinmusaki_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el);
                }
            },
            '#infochief-tab #chief #company': 'mk_kinmusaki_nm',
            '#infochief-tab #chief #company_tel': 'mk_tel',
            '#infochief-tab #chief #position': 'mk_yakusyoku_nm',
            '#infochief-tab #chief #company_fax': 'mk_fax',
            '#infochief-tab #dm_print': {
                observe: 'dm_print_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #dm_sofu': {
                observe: 'dm_soufu_kbn',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #dm_riyu': 'dm_print_mi_ryu',
            '#infochief-tab #family_name': 'souke_nm',
            '#infochief-tab #family_name_kana': 'souke_knm',
            '#infochief-tab #kaishu_ymd': 'kaishu_ymd',
            '#input-tab #spouse': {
                observe: 'k_haigu_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'k_haigu_kbn');
                    return $el.val();
                }
            },
            '#input-tab #syushi_cd': {
                observe: 'syushi_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'syushi_kbn');
                    return $el.val();
                }
            },
            '#input-tab #syuha_cd': {
                observe: 'syuha_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'syuha_kbn');
                    // 宗派名設定処理
                    var item = $el.select2("data");
                    if ($.msiJqlib.isNullEx2(item)) {
                        this.model.set("syuha_nm", null);
                        this.model.set("syuha_knm", null);
                    } else {
                        this.model.set("syuha_nm", item.text);
                        this.model.set("syuha_knm", item.kbn_value_snm);
                    }
                    return $el.val();
                }
            },
            '#input-tab #syuha_nm_other': 'syuha_nm',
            '#input-tab #syuha_knm': 'syuha_knm',
            '#input-tab #syuha_knm2': 'syuha_knm',
            '#input-tab #temple_cd': 'jyusho_cd',
            '#input-tab #temple': {
                observe: 'jyusho_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("jyusho_cd", null);
                        this.model.set("jyusho_knm", null);
                    }
                    return val;
                }
            },
            '#input-tab #temple_knm': 'jyusho_knm',
            '#infochief-tab #memo2': 'biko1',
            '#customer-tab #alt_careful_memo': 'biko2',
            '#memo3': 'nb_biko',
            '#ryoshu_nm': 'ryoshu_nm', // 領収名
            '#nb_kazari_ymd': {
                observe: 'nb_kazari_ymd',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('nb_kazari_time'), 'nb_kazari_date', this.model);
                    var val = this.model;
                    return $el.val();
                }
            },
            '#nb_kazari_time': {
                observe: 'nb_kazari_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('nb_kazari_ymd'), $el.val(), 'nb_kazari_date', this.model);
                    return $el.val();
                }
            },
            '#nb_kataduke_ymd': {
                observe: 'nb_kataduke_ymd',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('nb_kataduke_time'), 'nb_kataduke_date', this.model);
                    return $el.val();
                }
            },
            '#nb_kataduke_time': {
                observe: 'nb_kataduke_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('nb_kataduke_ymd'), $el.val(), 'nb_kataduke_date', this.model);
                    return $el.val();
                }
            },
            '#nb_map_oid': 'nb_map_oid',
            '#nb_map_nm': 'nb_map_nm',
            '#nb_biko': 'nb_biko',
            '.basho_nm': {
                observe: 'basho_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("basho_cd", null);
//                        this.model.set("basho_kbn", null);
                    }
                    return val;
                }
            }
        },
        // 故人年齢計算処理
        calcNereiK: function () {
            var gengo = this.model.get("k_gengo"); // 故人生年月日元号
            var ymd = this.model.get("k_seinengappi_ymd"); // 故人生年月日和暦
            var seinengappi = $.msiJqlib.warekiToseireki(gengo, ymd); // 日付変換
            if (seinengappi) {
                var nakunariymd = this.model.get("k_death_ymd");
                if (!$.msiJqlib.chkDate(nakunariymd)) { // 日付チェック
                    nakunariymd = null;
                }
                var pram = {
                    nakunariymd: nakunariymd,
                    seinengappi: seinengappi,
                    setData: function (data) { // コールバック
                        this.model.set("k_nenrei_man", data.man);
                        this.model.set("k_nenrei_kyounen", data.kyonen);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("k_nenrei_man", null);
                this.model.set("k_nenrei_kyounen", null);
            }
        },
        // 喪主年齢計算処理
        calcNereiM: function () {
            var gengo = this.model.get("m_gengo"); // 喪主生年月日元号
            var ymd = this.model.get("m_seinengappi_ymd"); // 喪主生年月日和暦
            var seinengappi = $.msiJqlib.warekiToseireki(gengo, ymd); // 日付変換
            if (seinengappi) {
                var pram = {
                    nakunariymd: null,
                    seinengappi: seinengappi,
                    setData: function (data) {
                        this.model.set("m_nenrei_man", data.man);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("m_nenrei_man", null);
            }
        },
        // 年齢計算処理
        calcNerei: function (pram) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/calcnenrei',
                data: {
                    nakunariymd: pram.nakunariymd,
                    seinengappi: pram.seinengappi
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        if (_.has(pram, "setData")) {
                            pram.setData.apply(that, [mydata]);
                        }
                    }
                }
            });
        },
        // 葬儀情報(ヘッダー)の台帳番号バインディング処理
        setHeaderDaichoNo: function (d1, d2, d3) {
            this.$("#hd_daicho_no").text(d1 + '-' + d2 + '-' + d3);
        },
        // 葬儀情報(ヘッダー)の喪主バインディング処理
        setHeadeMaddr: function (addr1, addr2) {
            if ($.msiJqlib.isNullEx2(addr1)) {
                addr1 = '';
            }
            if ($.msiJqlib.isNullEx2(addr2)) {
                addr2 = '';
            }
            this.$("#hd_mg_addr").text(addr1 + ' ' + addr2);
        },
        // 宗派情報をクリアする
        clearSyuha: function (e) {
            if (e.val !== this.model.get("syushi_cd")) {
                this.model.set({'syuha_cd': null, 'syuha_kbn': null, 'syuha_nm': null, 'syuha_knm': null});
            }
        },
        // 名所住所ヘルパー処理
        nmjyushoHelper: function (e) {
            var t = this;
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var kind = $target.data("kind2");
            var code = $target.data("code");
            var name = $target.data("name");
            var kname = $target.data("kname");
            var m = this.model;
            var syuha_cd = null;
            if (kind === 1) {
                syuha_cd = this.model.get("syuha_cd");
            }
            this.$el.msiPickHelper({
                action: 'nmjyusho',
                kind2: kind,
                mydata: {s_syuha_cd: syuha_cd},
                onSelect: function (data) {
                    m.set(code, data.code);
                    m.set(name, data.name);
                    if (!$.msiJqlib.isNullEx2(kname)) {
                        m.set(kname, data.jyusho_lknm);
                        if (kind === 1) {
//                            appcst.kfModel.set('zip_no1', data.zip_no);
//                            appcst.kfModel.set('addr1_1', data.addr1_nm);
//                            appcst.kfModel.set('addr1_2', data.addr2_nm);
//                            appcst.kfModel.set('tel_no1', data.tel);
                            if (m.get("syuha_cd") !== data.syuha_cd) {
                                m.set('syushi_cd', data.syushi_cd);
                                m.set('syuha_cd', data.syuha_cd);
                                m.set('syuha_kbn', data.syushi_cd);
                                m.set('syuha_nm', data.syuha_nm);
                                m.set('syuha_knm', data.syuha_kana);
                                t.setShuhaOther();
                            }
                        }
                    }
                },
                onClear: function () {
                    m.set(code, null);
                    m.set(name, null);
                    if (!$.msiJqlib.isNullEx2(kname)) {
                        m.set(kname, null);
//                        appcst.kfModel.set('zip_no1', null);
//                        appcst.kfModel.set('addr1_1', null);
//                        appcst.kfModel.set('addr1_2', null);
//                        appcst.kfModel.set('tel_no1', null);
                    }
                }
            });
        },
        setShuhaOther: function () {
            if (this.model.get("syushi_cd") === '9') { // その他
                this.model.set("syuha_cd", '');
                this.$("#syuha_nm_other").show();
                this.$("#syuha_knm2").show();
                this.$("#syuha_knm").hide();
            } else {
                this.$("#syuha_nm_other").hide();
                this.$("#syuha_knm2").hide();
                this.$("#syuha_knm").show();
            }
        },
        initialize: function () {
            this.listenTo(appcst.niteiCol, 'reset', this.addAllNiteiCol);
            this.listenTo(appcst.appModel, 'change:moushi_cd2', this.setKenmei);
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.listenTo(appcst.appModel, 'change:kaiin_cd', this.setKaiinStatus);
            this.listenTo(appcst.appModel, 'change:dm_print_kbn', this.setDm);
            this.listenTo(appcst.appModel, 'change:free2_cd', function () {
                if ($.msiJqlib.isNullEx2(this.model.get("free2_cd"))) {
                    $("#seko_clear, #btn_copy").hide();
                } else {
                    $("#seko_clear, #btn_copy").show();
                }
            });
//            this.listenTo(this.model, 'change:gazo_oid', this.setGazo);

            // 会員切り替え処理
            this.setKaiinStatus();
            // 件名切り替え処理
            this.setKenmei();
            // DM発行切り替え処理
            this.setDm();
            // 日付ピッカー
            this.$("#date_death").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#nb_kazari_ymd").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#nb_kataduke_ymd").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".nitei_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.render();
        },
        render: function () {

            this.stickit();
            this.setSelect2();
            return this;
        },
        // select2設定処理
        setSelect2: function () {
            // 申込区分
            $.msiJqlib.setSelect2Com1(this.$("#apply_type"), {data: $.msiJqlib.objToArray3(data.dataKbns.moushi_kbn)});
            this.$(".cls_apply_type").hide();
            $.msiJqlib.setSelect2Com1(this.$("#apply_type2"), {data: $.msiJqlib.objToArray3(data.dataKbns.moushi_kbn2)});
            // 葬儀区分
            $.msiJqlib.setSelect2Com1(this.$("#funeral_type"), {data: $.msiJqlib.objToArray3(data.dataKbns.sougi_kbn)});
            // 個人情報保護
            $.msiJqlib.setSelect2Com1(this.$("#personal_info"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.p_info)}, $.msiJqlib.setSelect2Default1)));
            // 会員区分
            $.msiJqlib.setSelect2Com1(this.$("#member"), {data: $.msiJqlib.objToArray3(data.dataKbns.kaiin_kbn)});
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#birthday_era"), {data: $.msiJqlib.objToArray3(data.dataKbns.gengo)});
            // 葬儀・法事施工の有無
            $.msiJqlib.setSelect2Com1(this.$("#sougi_seko,#houji_seko"), ($.extend({data: _umu_kbn}, $.msiJqlib.setSelect2Default1)));
            // 勤務先
//            $.msiJqlib.setSelect2Com1(this.$("#employee"), {data: _employee});
            $.msiJqlib.setSelect2Com1(this.$("#employee"), ($.extend({data: _employee}, $.msiJqlib.setSelect2Default1)));
            // 施行法要
            $.msiJqlib.setSelect2Com1(this.$("#nengo"), $.extend({data: $.msiJqlib.objToArray3(data.dataKbns.houyo_kbn)}, $.msiJqlib.setSelect2Default1));
            // 続柄
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #s_chief_relationship"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 喪主様からみた続柄
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #s_chief_relationship2"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // DM発行区分
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #dm_print"), {data: $.msiJqlib.objToArray3(data.dataKbns.dm_print_kbn)});
            // DM送付先区分
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #dm_sofu"), {data: $.msiJqlib.objToArray3(data.dataKbns.dm_sofu_kbn)});
            // 場所区分
            //$.msiJqlib.setSelect2Com1(this.$("#spot_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kazari_basho_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 配偶者
            $.msiJqlib.setSelect2Com1(this.$("#spouse"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.haigu_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 現住所 世帯主
            $.msiJqlib.setSelect2Com1(this.$("#head_1, #u_head_1"), {data: $.msiJqlib.objToArray3(data.dataKbns.setai_kbn)});
            // 住民登録住所 世帯主
            $.msiJqlib.setSelect2Com1(this.$("#head_2"), {data: $.msiJqlib.objToArray3(data.dataKbns.setai_kbn)});
            // 筆頭者
            $.msiJqlib.setSelect2Com1(this.$("#head_3"), {data: $.msiJqlib.objToArray3(data.dataKbns.hito_kbn)});
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #birthday_era"), {data: $.msiJqlib.objToArray3(data.dataKbns.gengo)});
            // 宗旨区分
            $.msiJqlib.setSelect2Com1(this.$("#syushi_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.syushi_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 宗派区分
            appcst.syuha_kbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
            $.msiJqlib.setSelect2Com1(this.$("#syuha_cd"), ($.extend({data: function () {
                    return {results: appcst.syuha_kbns};
                }}, $.msiJqlib.setSelect2Default2)));
        },
        addNiteiOne: function (nitei) {
            var v = new NiteiView({model: nitei});
            this.$("#input-tab #infodate").append(v.render().el);
        },
        addAllNiteiCol: function (collection) {
            var $infodate = this.$("#input-tab #infodate");
            $infodate.find('fieldset').remove();
            collection.each(this.addNiteiOne, this);
        },
        // 会員切り替え処理
        setKaiinStatus: function () {
            var val = this.model.get('kaiin_cd');
            // その他
            if (val === "9") {
                this.$("#member_detail").removeAttr("disabled");
            } else {
                this.$("#member_detail").attr("disabled", "disabled");
            }
        },
        setDm: function () {
            var val = this.model.get('dm_print_kbn');
            if (val === '0') {
                this.$("#dm_sofu").removeAttr("disabled");
                if ($.msiJqlib.isNullEx2(this.model.get('dm_soufu_kbn'))) {
                    this.model.set('dm_soufu_kbn', '1');
                }
            } else {
                this.$("#dm_sofu").attr("disabled", "disabled");
                this.model.set('dm_soufu_kbn', null);
            }
        },
        setKenmei: function () {
            var val = this.model.get('moushi_cd2');
            if (val === '12') { // 催事
                this.$(".f_houyo_kenmei").show();
            } else {
                this.$(".f_houyo_kenmei").hide();
            }
        },
        // チェックボックス切り替え処理
        setCheckBox: function (e, pro, target) {
            var val = $(target + ':checked').val();
            this.model.set(pro, val === "1" ? "1" : "0");
        },
        // タブ切り替え処理
        changeTab: function (e) {
            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.tab li'), $(e.currentTarget));
            // 互助会確認の場合、加入状況をクリックする
            var tabIdx = $.msiJqlib.getTabIndex();
            if (tabIdx === 2) {
                this.$("#member_group_set #member_group_1").click();
            }
        },
        isInputOk: function () {
            var aMsg = [];
            // 施行基本フリーモデルチェック
            var result = appcst.kfModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    if ($.inArray(v, aMsg) < 0) {
                        aMsg.push(v);
                    }
                });
            }
            // 施行基本モデルチェック
            var result = appcst.appModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 施行日程コレクションチェック
            appcst.niteiCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                    });
                }
            });
            // 請求先情報モデルチェック
            var result = appcst.sekyuModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 施行互助会会員コレクションチェック
            appcst.gojokaiMemberCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                        ;
                    });
                }
            });
            // 施行互助会情報モデルチェック
            var result = appcst.gojokaiInfoModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // NG
            if (aMsg.length > 0) {
                var errClsNm = ".error1";
                var $li = this.$('.tab li');
                if (this.$("#infochief-tab").find(errClsNm).length) {
                    $li.eq(0).find("a").click();
                } else if (this.$("#input-tab").find(errClsNm).length) {
                    $li.eq(1).find("a").click();
                } else if (this.$("#infomember-tab").find(errClsNm).length) {
                    $li.eq(2).find("a").click();
                }
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }
            // OK
            $.msiJqlib.clearAlert();
            return true;
        },
        doSave: function () {
            if (!this.isInputOk()) {
                return;
            }
            var bumonCd = $('#hall_cd').val();
            var bumonChanged = bumonCd !== appcst.appModel.get("bumon_cd");
            // 見積確定
            if (!$.msiJqlib.isNullEx2(appcst.appModel.get("jichu_kakute_ymd")) && bumonChanged) {
                this.exeCheck();
            } else {
                this.exeSave();
            }
        },
        exeCheck: function () {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/CheckGetujiFixInput',
                data: {dataAppJson: JSON.stringify(appcst.appModel.toJSON())},
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        that.exeSave();
                    } else if (mydata.status === 'INFO') {
                        $.msiJqlib.setProgressing(false);
                        if (!confirm(mydata.msg)) {
                            return;
                        }
                        $.msiJqlib.setProgressing(true);
                        that.exeSave();
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        exeSave: function () {
            // 施行基本イコール
            var sekoKihonEq = $.msiJqlib.isEqual(appcst.appModel.toJSON(), orgDataApp);
            // 施行基本汎用フリー情報イコール
            var sekoKihonFreeEq = $.msiJqlib.isEqual(appcst.kfModel.toJSON(), orgDataKihonFree);
            // 施行日程イコール
            var sekoNiteiEq = $.msiJqlib.isEqual(appcst.niteiCol.toJSON(), orgDataNiteiCol);
            // 請求先情報イコール
            var sekyuInfoEq = $.msiJqlib.isEqual(appcst.sekyuModel.toJSON(), orgDataSekyuInfo);
            // 施行互助会情報イコール
            var sekoGojokaiInfoEq = $.msiJqlib.isEqual(appcst.gojokaiInfoModel.toJSON(), orgDataGojokaiInfo);
            // 施行互助会加入者イコール
            var sekoGojokaiMemberEq = $.msiJqlib.isEqual(appcst.gojokaiMemberCol.toJSON(), orgDataGojokaiMemberCol);
            if (sekoKihonEq && sekoNiteiEq && sekyuInfoEq && sekoGojokaiInfoEq && sekoGojokaiMemberEq && sekoKihonFreeEq) {
                $.msiJqlib.showInfo('データの変更がありません');
                return;
            }
            // チェンジフラグ設定
            var changeFlg = JSON.stringify({
                kihonChangeFlg: !sekoKihonEq,
                kihonFreeChangeFlg: !sekoKihonFreeEq,
                niteiChangeFlg: !sekoNiteiEq,
                sekyuInfoChangeFlg: !sekyuInfoEq,
                gojokaiInfoChangeFlg: !sekoGojokaiInfoEq,
                gojokaiMemberChangeFlg: !sekoGojokaiMemberEq
            });
            var bumonCd = $('#hall_cd').val();
            appcst.appModel.set('bumon_cd', bumonCd);
            // 施行基本情報
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            // 施行基本汎用フリー情報
            var dataSekoKihonFreeJson = JSON.stringify(appcst.kfModel.toJSON());
            // 施行日程情報
            var dataNiteiColJson = JSON.stringify(appcst.niteiCol.toJSON());
            // 請求先情報
            var dataSekyuInfoJson = JSON.stringify(appcst.sekyuModel.toJSON());
            // 施行互助会情報
            var dataGojokaiInfoJson = JSON.stringify(appcst.gojokaiInfoModel.toJSON());
            // 施行互助会加入者情報
            // 会員番号があるデータに絞込み
            var dataGojokaiMemberCol = this.gojokaiMemberfilter();
            var dataGojokaiMemberColJson = JSON.stringify(dataGojokaiMemberCol);

            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/houji/customerinfosave',
                data: {
                    dataSekoKihonJson: dataSekoKihonJson,
                    dataSekoKihonFreeJson: dataSekoKihonFreeJson,
                    dataNiteiColJson: dataNiteiColJson,
                    dataSekyuInfoJson: dataSekyuInfoJson,
                    dataGojokaiInfoJson: dataGojokaiInfoJson,
                    dataGojokaiMemberColJson: dataGojokaiMemberColJson,
                    controllerName: appcst.data.controllerName,
                    changeFlg: changeFlg
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        _resetData(mydata.dataSekoKihon,
                                mydata.dataNiteiCol,
                                mydata.dataSekyuInfo,
                                mydata.dataGojokaiInfo,
                                mydata.dataGojokaiMemberCol,
                                mydata.dataSekoKihonFree);
                        $("#header .id_number span.number").text(mydata.dataSekoKihon.seko_no);  // 施行番号設定
                        $.msiJqlib.showInfo(mydata.msg);
                        var matcheds = location.href.match(/(\/sn\/\d+)/);
                        if (!matcheds) {
                            var m = location.href.match(/\/(\D+)\/(\D+)\/(\D+)\/(\D+)/);
                            var herf;
                            if (m) {
                                herf = $.msiJqlib.baseUrl() + "/" + m[2] + "/" + m[3] + "/" + m[4].replace("/", '') + "/sn/" + mydata.dataSekoKihon.seko_no;
                            } else {
                                herf = location.href + "/sn/" + mydata.dataSekoKihon.seko_no;
                            }
                            location.href = herf;
                        }
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doPrint: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0101',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
        },
        doDelete: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('お客様情報を削除します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/customerinfodelete',
                data: {
                    seko_no: sekoNo
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        var matcheds = location.href.match(/(.+)(\/sn\/\d+)/);
                        if (matcheds) {
                            location.href = matcheds[1];
                        } else {
                            window.location.reload();
                        }
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        doCancel: function () {
            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }
            window.location.reload();
        },
        // 会員番号があるデータに絞込み
        gojokaiMemberfilter: function () {
            var dataGojokaiMemberCol = appcst.gojokaiMemberCol.filter(function (item) {
                return !$.msiJqlib.isNullEx2(item.get("kain_no"));
            });
            return dataGojokaiMemberCol;
        },
        // 郵便番号ヘルパー処理
        zipHelper: function (e) {
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            var val = $target.data("zip");
            var zip, addr1;
            var m = this.model;
            if (val === 'k1') { // 故人現住所
                zip = 'kg_yubin_no';
                addr1 = 'kg_addr1';
            } else if (val === 'k2') {// 故人住民登録住所
                zip = 'kj_yubin_no';
                addr1 = 'kj_addr1';
            } else if (val === 'k3') {// 故人本籍
                zip = 'kh_yubin_no';
                addr1 = 'kh_addr1';
            } else if (val === 'm1') {// 喪主現住所
                zip = 'mg_yubin_no';
                addr1 = 'mg_addr1';
            } else if (val === 'm2') {// 喪主住民登録住所
                zip = 'mj_yubin_no';
                addr1 = 'mj_addr1';
            } else if (val === 's1') {// 請求先現住所
                zip = 'sekyu_yubin_no';
                addr1 = 'sekyu_addr1';
                m = appcst.sekyuModel;
            } else if (val === 't1') {// 司式者
                zip = 'zip_no1';
                addr1 = 'addr1_1';
                m = appcst.kfModel;
            }
            this.$el.msiPickHelper({
                action: 'zipno',
                onSelect: function (data) {
                    m.set(zip, data.code);
                    m.set(addr1, data.name);
                },
                onClear: function () {
                    m.set(zip, null);
                    m.set(addr1, null);
                }
            });
        },
        //会員情報検索ダイアログ処理
        seikyukaiinHelper: function () {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'seikyukaiin',
                onSelect: function (data) {
                    bbm.set({
                        v_free13: data.code,
                        kaiin_nm: data.name,
                        kaiin_kbnnm: data.kaiin_kbn_nm,
                        kaiin_no: data.kaiin_no,
                    });
                },
                onClear: function () {
                    bbm.set({
                        v_free13: null,
                        kaiin_nm: null,
                        kaiin_kbnnm: null,
                        kaiin_no: null,
                    });
                },
            });
        },
        // 受付担当者ヘルパー処理 
        uketukeHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'uketuke_tanto_cd': data.code, 'uketuke_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'uketuke_tanto_cd': null, 'uketuke_tanto_nm': null});
                }
            });
        },
        // 施行担当者ヘルパー処理
        sekoHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'seko_tanto_cd': data.code, 'seko_tanto_nm': data.name});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text(data.name);
                },
                onClear: function () {
                    m.set({'seko_tanto_cd': null, 'seko_tanto_nm': null});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text('');
                }
            });
        },
        sekoInfoHelper: function () {
            var dataIn = {init_search: 1}; // 初回表示時検索する/しない=1/0
            var t = this;
            $.msiJqlib.celemonyDialogOnSelect = function (data) {
                if (data.length > 0) {
                    var sekoInfo = data[0];
                    appcst.appModel.set("free2_cd", sekoInfo.seko_no);
                    t.copySekoInfo();
                    $("#seko_clear, #btn_copy").show();
                }
            };
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/mref/sekosogidialog',
                type: 'GET',
                data: dataIn,
                dataType: 'html',
                success: function (html) {
                    // console.log( html );
                    $('#celemony_dialog').remove();
                    $(html).appendTo($('#wrapper')).fadeIn(400);
                }
                // error処理は共通設定を使う
            });
        },
        copySekoInfo: function () {
            var t = this;
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/houji/sekoinfocopy',
                data: {
                    dataSekoKihonJson: dataSekoKihonJson
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        appcst.appModel.set(mydata.sekodata);
                        appcst.sekyuModel.set(mydata.seikyudata);
                        appcst.kfModel.set('s_kamon_nm', mydata.sekodata.s_kamon_nm);
                        if(!$.msiJqlib.isNullEx2(mydata.sekodata.bumon_cd)){
                            $('#hall_cd').select2('val', mydata.sekodata.bumon_cd);
                        }
                        setTimeout(function () {
                            t.setBtnST();
                        }, 10);
//                        $.msiJqlib.showInfo(mydata.msg);
                    }
                }
            });
        },
        setBtnST: function () {

            // 喪主タブ 故人に同じを設定
            if (appcst.appModel.get("mg_kbn") === "1" && $('#as_address_4:checked').val() !== "1") {
                $('#as_address_4').click();
            }
            if (appcst.appModel.get("mg_kbn") === "0" && $('#as_address_4:checked').val() === "1") {
                $('#as_address_4').click();
            }
            // 基本タブ 住民登録住所の現住所に同じを設定
            if (appcst.appModel.get("kj_kbn") === "1" && $('#as_address_2:checked').val() !== "1") {
                $('#as_address_2').click();
            }
            if (appcst.appModel.get("kj_kbn") === "0" && $('#as_address_2:checked').val() === "1") {
                $('#as_address_2').click();
            }
            // 基本タブ 本籍の現住所に同じを設定
            if (appcst.appModel.get("kh_kbn") === "1" && $('#as_address_3:checked').val() !== "1") {
                $('#as_address_3').click();
            }
            if (appcst.appModel.get("kh_kbn") === "0" && $('#as_address_3:checked').val() === "1") {
                $('#as_address_3').click();
            }
            // 喪主タブ 住民登録住所の故人に同じを設定
            if (appcst.appModel.get("mj_kbn") === "1" && $('#as_address_5:checked').val() !== "1") {
                $('#as_address_5').click();
            }
            if (appcst.appModel.get("mj_kbn") === "0" && $('#as_address_5:checked').val() === "1") {
                $('#as_address_5').click();
            }
            if (appcst.appModel.get("sekyu_kbn") === "1" && $('#as_chief:checked').val() !== "1") {
                $('#as_chief').click();
            }
            if (appcst.appModel.get("sekyu_kbn") === "0" && $('#as_chief:checked').val() === "1") {
                $('#as_chief').click();
            }
            if (appcst.appModel.get("k_sex_kbn") === '1') {
                $("#male").click();
            } else if (appcst.appModel.get("k_sex_kbn") === '2') {
                $("#female").click();
            }
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        },
        setGazo: function (m, val) {
            var c = m.changed;
            var el;
            if (_.has(c, 'gazo_oid')) {
                el = "#crest_pic_cloth";
            } else {
                return;
            }
            this.addGazo(this.$(el), val);
        },
        addGazo: function ($el, val) {
            $el.find('img').remove();
            if (!$.msiJqlib.isNullEx2(val)) {
                $('<img>').appendTo($el).attr('src', $.msiJqlib.baseUrl() + '/mref/gazodlg/img/imgid/' + val);
            }
        },
    };
    /**
     * @description 日程タブ処理
     */
    // 日程タブ明細モデル
    var NiteiModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                nitei_kbn: null, // 日程区分
                nitei_ymd: null, // 日程タイムスタンプ
                nitei_date: null, // 日程日付のみ
                nitei_time: null // 日程時刻のみ
            };
        },
        validation: {
            nitei_ymd: function (val, attr, computed) {
                if (($.msiJqlib.isNullEx2(computed.nitei_date) && !$.msiJqlib.isNullEx2(computed.nitei_time))) {
                    return '日付と時刻の整合性がありません';
                }
            },
            nitei_date: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
            nitei_time: {
                required: false,
                pattern: 'time'
            },
            basho_nm: {
                required: false,
                maxLength: 40
            }
        },
        labels: {
            basho_nm: '場所名'
        }
    }); // NiteiModel

    // 日程コレクション
    var NiteiCollection = Backbone.Collection.extend({
        model: NiteiModel,
        comparator: 'nitei_kbn'
    });

    // 日程ビュー
    var NiteiView = Backbone.View.extend({
        tagName: 'fieldset',
        // 0:納品 1:法要 2:墓参 3:会食
        tmpl0: _.template($('#tmpl-nitei-0').html()),
        tmpl1: _.template($('#tmpl-nitei-1').html()),
        tmpl2: _.template($('#tmpl-nitei-2').html()),
        tmpl3: _.template($('#tmpl-nitei-3').html()),
        events: {
            "click .label.dlg_place": "nmjyushoHelper",
            "change .nitei_spot_cd": function () {
                this.model.set({'basho_cd': null, 'basho_nm': null});
            }
        },
        bindings: {
            '.nitei_kbn_nm': 'nitei_kbn_nm',
            '.nitei_date': {
                observe: 'nitei_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('nitei_time'), 'nitei_ymd', this.model);
                    // 施行基本の葬儀日に法要日を設定
                    if (this.model.get('nitei_kbn') === 1) {
//                        // 施行基本の葬儀日を設定
//                        appcst.appModel.set('sougi_ymd', $el.val());
                        // 葬儀情報(ヘッダー)の葬儀日バインディング処理
                        $("#hd_mg_sogibi").text($el.val());
                        appcst.appModel.set('houyo_ymd', $el.val());
                    }

                    if (this.model.get('nitei_kbn') === 0) {
                        // 施行基本の葬儀日を設定
                        appcst.appModel.set('sougi_ymd', $el.val());
                    }
                    return $el.val();
                }

            },
            '.nitei_time': {
                observe: 'nitei_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('nitei_date'), $el.val(), 'nitei_ymd', this.model);
                    return $el.val();
                }
            },
            '.nitei_spot_cd': {
                observe: 'spot_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'basho_kbn');
                    return $el.val();
                }
            },
            '.basho_nm': {
                observe: 'basho_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("basho_cd", null);
//                        this.model.set("basho_kbn", null);
                    }
                    return val;
                }
            }
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.listenTo(this.model, 'change:spot_cd', this.setPickupNmStatus);
            this.listenTo(appcst.appModel, 'change:nb_kazari_time', this.setNiteiSpot);
            this.listenTo(appcst.appModel, 'change:nb_kazari_ymd', this.setNiteiSpot);
        },
        render: function () {
            // 場所区分データ
            var niteiSpotKbns = {};
            // 日程区分
            var niteiKbn = this.model.get("nitei_kbn");
            if (niteiKbn === 0) {// 納品
                this.$el.html(this.tmpl0(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.kaishoku_kbn;
            } else if (niteiKbn === 1) {// 法要
                this.$el.html(this.tmpl1(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.kaishoku_kbn;
            } else if (niteiKbn === 2) { // 墓参
                this.$el.html(this.tmpl2(this.model.toJSON()));
            } else if (niteiKbn === 3) {// 会食
                this.$el.html(this.tmpl3(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.kaishoku_kbn;
            }

            this.$(".nitei_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".nitei_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.stickit();
            this.setPickupNmStatus();
            // 場所区分
            $.msiJqlib.setSelect2Com1(this.$(".nitei_spot_cd"), ($.extend({data: $.msiJqlib.objToArray3(niteiSpotKbns)}, $.msiJqlib.setSelect2Default1)));
//            $.msiJqlib.setSelect2Com1(this.$(".nitei_basho_1"), ($.extend({data: $.msiJqlib.objToArray3(niteiSpotKbns)}, $.msiJqlib.setSelect2Default1)));
//            $.msiJqlib.setSelect2Com1(this.$(".nitei_basho_2"), ($.extend({data: $.msiJqlib.objToArray3(niteiSpotKbns)}, $.msiJqlib.setSelect2Default1)));
            return this;
        },
        // 名所住所ヘルパー処理
        nmjyushoHelper: function (e) {
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var kind = $target.data("kind2");
            if ($.msiJqlib.isNullEx2(kind)) {
                return;
            }
            var actionNm = 'nmjyusho';
            var action = $target.data("action");
            if (!$.msiJqlib.isNullEx2(action)) {
                actionNm = action;
            }
            var m = this.model;

            var bumon_cd = $('#hall_cd').val();		//ごんきや 初期表示用の部門追加

            this.$el.msiPickHelper({
                action: actionNm,
                kind2: kind,
                mydata: {s_bumon_cd: bumon_cd}, //ごんきや 初期表示用の部門追加
                onSelect: function (data) {
                    m.set("basho_cd", data.code);
                    m.set("basho_nm", data.name);
                },
                onClear: function () {
                    m.set("basho_cd", null);
                    m.set("basho_nm", null);
                }
            });
        },
        // 場所区分切り替え処理
        setPickupNmStatus: function () {
            // 日程区分 0:納品 1:法要 2:墓参 3:会食
            var niteiKbn = this.model.get("nitei_kbn");
            if ($.inArray(niteiKbn, [0, 1, 3]) >= 0) {
                // 場所区分
                var bashoKbn = this.model.get('basho_kbn');

                if (bashoKbn === "2") {// 2:ホール
//                    this.$(".place").data("action", 'hallhouji');
                    this.$(".place").data("action", 'kaijyo');
                } else if (bashoKbn === "3") { // 3:ホテル
                    this.$(".place").data("action", 'hotel');
                } else if (bashoKbn === "4") { // 4:提携場
                    this.$(".place").data("action", 'teikeijo');
                } else if (bashoKbn === "5") { // 5:料亭
                    this.$(".place").data("action", null);
                } else {
                    this.$(".place").data("action", null);
                }
                // pickname入力可能区分
                var disable = true;
//                if ((niteiKbn === 1 || niteiKbn === 0) && (bashoKbn === "3" || bashoKbn === "4" || bashoKbn === "6")) { // 1:法要の場所区分 3:他会館 4:公民館 6:他寺院
//                    disable = true;
//                }
//                if (niteiKbn === 3 && (bashoKbn === "2" || bashoKbn === "3")) { // 3:会食の場所区分 2:他会館 3:公民館
//                    disable = true;
//                }
                _setSpotStatus(bashoKbn, this.$(".place"), this.$(".dlg_place"), disable);
                if (bashoKbn === '0' || bashoKbn === '5' || bashoKbn === '9' || bashoKbn === '99') { // 0:自宅と9:その他はpickup無し
                    bashoKbn = null;
                }
                this.$(".place").data("kind2", bashoKbn);
            }
        },
        // お飾り希望日入力時、納品日をセット
        setNiteiSpot: function () {
            var kazari_ymd = appcst.appModel.get("nb_kazari_ymd");
            var kazari_time = appcst.appModel.get("nb_kazari_time");
            var niteiKbn = this.model.get("nitei_kbn");

            if (this.model.get('nitei_kbn') === 0) {
                // お飾り希望日を入力したら納品日もセット
                this.model.set("nitei_date", kazari_ymd);
                this.model.set("nitei_time", kazari_time);
                _setNiteiymd(this.model.get('nitei_date'), this.model.get('nitei_time'), 'nitei_ymd', this.model);
                // 施行基本の葬儀日を設定
                appcst.appModel.set('sougi_ymd', this.model.get('nitei_date'));
            }
        }
    }); // NiteiView

    // 施行基本フリーモデル
    var KihonFreeModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                d_free1: null, // 受付日
                zip_no1: null, // 司式者郵便番号
                addr1_1: null, // 司式者現住所1
                addr1_2: null, // 司式者現住所2
                tel_no1: null, // 司式者電話番号
                i_free1: null, // 親族人数
                i_free2: null, // 導師人数
                i_free3: null, // 隣組人数
                i_free4: null, // マイクロバス台数
                free1_kbn: null, // マイクロバス有無
                free_kbn1: null, // 初月忌有無
                free_kbn2: null, // 百ヵ日忌有無
                free_kbn3: null, // お彼岸区分
                free_kbn4: null, // 新盆有無
                free_kbn5: null, // 忌法要有無
                free3_code_cd: null, //お飾り希望日区分
                free3_kbn: null, //お飾り希望日区分
                ts_free1: null, // 初月忌日付
                ts_free2: null, // 百ヵ日忌日付
                ts_free3: null, // お彼岸日付
                ts_free4: null, // 新盆日付
                ts_free5: null, // 忌法要日付
                v_free1: null, // 初月忌内容
                v_free2: null, // 百ヵ日忌内容
                v_free3: null, // お彼岸内容
                v_free4: null, // 新盆内容
                v_free5: null, // 忌法要フリー入力
                v_free6: null, // 忌法要内容
                v_free7: null, // お飾り備考名
                v_free8: null, // お片付け備考名
                gazo_oid: null, // 家紋OID
                gazo_nm: null, // 家紋コード
                s_kamon_nm: null, // 家紋名称
            };
        },
        validation: {
            d_free1: {
                required: false,
                customFun: function (value) {
                    return $.msiJqlib.chkYmd(value);
                }
            },
            i_free1: {
                required: false,
                pattern: 'digits'
            },
            i_free2: {
                required: false,
                pattern: 'digits'
            },
            i_free3: {
                required: false,
                pattern: 'digits'
            },
            i_free4: {
                required: false,
                pattern: 'digits'
            },
            zip_no1: {
                required: false,
                pattern: 'zip'
            },
            tel_no1: {
                required: false,
                pattern: 'tel'
            },
            ts_free1: {
                required: false,
                customFun: function (value) {
                    return $.msiJqlib.chkYmd(value);
                }
            },
            ts_free2: {
                required: false,
                customFun: function (value) {
                    return $.msiJqlib.chkYmd(value);
                }
            },
            ts_free3: {
                required: false,
                customFun: function (value) {
                    return $.msiJqlib.chkYmd(value);
                }
            },
            ts_free4: {
                required: false,
                customFun: function (value) {
                    return $.msiJqlib.chkYmd(value);
                }
            },
            ts_free5: {
                required: false,
                customFun: function (value) {
                    return $.msiJqlib.chkYmd(value);
                }
            },
            v_free7: {
                required: false,
                maxLength: 30
            },
            v_free8: {
                required: false,
                maxLength: 30
            }
        },
        labels: {
            d_free1: '受付日',
            zip_no1: '郵便番号',
            tel_no1: '電話番号',
            i_free1: '親族人数',
            i_free2: '導師人数',
            i_free3: '隣組人数',
            i_free4: 'マイクロバス台数',
            ts_free1: '初月忌日付',
            ts_free2: '百ヵ日忌日付',
            ts_free3: 'お彼岸日付',
            ts_free4: '2年盆日付',
            ts_free5: '忌法要日付',
            gazo_oid: '家紋', // 家紋OID
            gazo_nm: '画像名', // 家紋コード
            s_kamon_nm: '家紋名', // 家紋名称
            free3_code_cd: '会場区分',
            v_free7: 'お飾り備考',
            v_free8: 'お片付け備考',
        }
    });

    // 施行基本フリービュー
    var KihonFreeView = Backbone.View.extend({
        el: $("#detail"),
        events: {
            "click input[type='radio']": "doRdoClick",
            "click #lbl_dlg_crest": "kamonHelper",
            "change .kazari_basho_kbn": function () {
                this.model.set({'free3_cd_kbn': null, 'v_free7': null});
            }
        },
        bindings: {
            '#uketuke_date': 'd_free1',
            '#zip_temple': 'zip_no1',
            '#address_temple_1': 'addr1_1',
            '#address_temple_2': 'addr1_2',
            '#tel_temple': 'tel_no1',
            '#sinzoku_ninzu': 'i_free1',
            '#doshi_ninzu': 'i_free2',
            '#tonari_ninzu': 'i_free3',
            '#microbus_daisu': 'i_free4',
            '#gakki_date': 'ts_free1',
            '#hyakuki_date': 'ts_free2',
            '#higan_date': 'ts_free3',
            '#bon_date': 'ts_free4',
            '#ki_houyo_date': 'ts_free5',
            '#gakki_biko': 'v_free1',
            '#hyakuki_biko': 'v_free2',
            '#higan_biko': 'v_free3',
            '#bon_biko': 'v_free4', //2年盆備考
            '#ki_free': 'v_free5',
            '#ki_houyo_biko': 'v_free6',
            '#cloth_crest_name': 's_kamon_nm',
            "input[name='microbus_kbn']": $.msiJqlib.getRadioBinding('free1_kbn'),
            "input[name='gakki_kbn']": $.msiJqlib.getRadioBinding('free_kbn1'),
            "input[name='hyakuki_kbn']": $.msiJqlib.getRadioBinding('free_kbn2'),
            "input[name='higan_kbn']": $.msiJqlib.getRadioBinding('free_kbn3'),
            "input[name='bon_kbn']": $.msiJqlib.getRadioBinding('free_kbn4'), //2年盆区分
            "input[name='ki_houyo_kbn']": $.msiJqlib.getRadioBinding('free_kbn5'),
//            "input[name='kazari_basho_kbn']": $.msiJqlib.getRadioBinding('free_kbn6'),
            '.kazari_basho_kbn': {
                observe: 'free3_code_cd',
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    $.msiJqlib.setKbnCdVal($el, this.model, 'free3_kbn');
                    return $el.val();
                }
            },
            '.kazari_basho_nm': {
                observe: 'v_free7',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    return val;
                }
            },
            '.kataduke_basho_nm': {
                observe: 'v_free8',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    return val;
                }
            }
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.listenTo(this.model, 'change:gazo_oid', this.setGazo);
            // 場所区分
            this.listenTo(this.model, 'change:free3_code_cd', this.setKazariNmStatus);
            this.render();
        },
        render: function () {
            this.stickit();
            this.addGazo(this.$('#crest_pic_cloth'), this.model.get("gazo_oid")); // 家紋画像
            this.$("#uketuke_date, #gakki_date, #hyakuki_date, #higan_date, #bon_date, #ki_houyo_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            // 場所区分
            $.msiJqlib.setSelect2Com1(this.$(".kazari_basho_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kazari_basho_kbn)}, $.msiJqlib.setSelect2Default1)));
            return this;
        },
        // ラジオボタンクリック処理
        doRdoClick: function (e) {
            var $target = $(e.currentTarget);
            var attr = $target.parent("span").data("ref_attr");
            if (!$.msiJqlib.isNullEx2(attr)) {
                if ($target.hasClass("onCheck")) {
                    $target.attr("checked", false);
                    $target.button("refresh");
                    $target.parent().find("input").removeClass("onCheck");
                    this.model.set(attr, null);
                } else {
                    $target.parent().find("input").removeClass("onCheck");
                    $target.toggleClass("onCheck");
                }

            }
//            var attr = $target.parent("span").data("ref_attr");
//            if (!$.msiJqlib.isNullEx2(attr)) {
//                if ($target.val() == this.model.get(attr)) {
//                    $target.attr("checked", false);
//                    $target.button("refresh");
//                    this.model.set(attr, null);
//                } else {
//                    this.model.set(attr, $target.val());
//                }
//            }

        },
        // 家紋ヘルパー処理 
        kamonHelper: function () {
            _gazoHelper(this, {s_gazo_kbn: 1, limit: 20}, {oid: 'gazo_oid', code: 'gazo_nm', name: 's_kamon_nm'});
        },
        setGazo: function (m, val) {
            var c = m.changed;
            var el;
            if (_.has(c, 'gazo_oid')) {
                el = "#crest_pic_cloth";
            } else {
                return;
            }
            this.addGazo(this.$(el), val);
        },
        addGazo: function ($el, val) {
            $el.find('img').remove();
            if (!$.msiJqlib.isNullEx2(val)) {
                $('<img>').appendTo($el).attr('src', $.msiJqlib.baseUrl() + '/mref/gazodlg/img/imgid/' + val);
            }
        },
        // 場所区分切り替え処理
        setKazariNmStatus: function () {
            // お飾り区分　0：自宅、1：その他 
            var kazariKbn = this.model.get("free3_code_cd");
            if ($.inArray(kazariKbn, [0, 1]) <= 0) {
                // pickname入力可能区分
                var disable = true;
                _setKazariSpotStatus(kazariKbn, this.$(".kazari_basho_nm"), disable);
                if (kazariKbn === '0' || kazariKbn === '1') { // 0:自宅と1:その他はpickup無し
                    kazariKbn = null;
                }
                this.$(".place").data("kind2", kazariKbn);
            }
        }
    });

    // 全体ビュー
    var AppView = Backbone.View.extend($.extend(true, AppViewDef, $.customerFileup));
    // データ取得
    var data = msiLib2.getJsonFromHtml($('#data-json'));
    appcst.data = data;
    var showFlg = true;
    if ($.msiJqlib.isNullEx2(data.dataSekoKihon.seko_no)) {
        showFlg = false;
    }
    // サイドメニュー
    $.msiSideMenuLib.setSideMenu({showFooter: showFlg});
    // 日程タブ初期化
    appcst.niteiCol = new NiteiCollection();
    // 互助会タブ初期化
    appcst.gojokaiInfoModel = new appgjk.GojokaiInfoModel();
    appcst.gojokaiMemberCol = new appgjk.GojokaiMemberCollection();
    appcst.gojokaiInfoView = new appgjk.GojokaiInfoView({model: appcst.gojokaiInfoModel});
    // 喪主タブ請求先初期化処理
    appcst.sekyuModel = new appsk.SekyuModel();
    appcst.sekyuView = new appsk.SekyuView({model: appcst.sekyuModel});
    // APP初期化処理
    appcst.kfModel = new KihonFreeModel();
    appcst.kfView = new KihonFreeView({model: appcst.kfModel});
    appcst.appModel = new AppModel();
    appcst.appView = new AppView({model: appcst.appModel});
    //
    var orgDataApp, orgDataNiteiCol, orgDataSekyuInfo, orgDataGojokaiInfo, orgDataGojokaiMemberCol, orgDataKihonFree;
    var _resetData = function (dataSekoKihon, dataNiteiCol, dataSekyuInfo, dataGojokaiInfo, dataGojokaiMemberCol, dataKihonFree) {
        // モデルのデータを設定
        appcst.kfView.model.set(dataKihonFree);
        appcst.appView.model.set(dataSekoKihon);
        appcst.sekyuView.model.set(dataSekyuInfo);
        appcst.gojokaiInfoView.model.set(dataGojokaiInfo);
        appcst.gojokaiMemberCol.reset(dataGojokaiMemberCol);
        appcst.niteiCol.reset(dataNiteiCol);

        // 基本・喪主・その他タブ 
        if (dataSekoKihon) {
//            if (!$.msiJqlib.isNullEx2(dataSekoKihon.jichu_kakute_ymd)) {
//                $("#hall_cd").attr("disabled", "disabled");
//            }
//            // 受注変更の場合、部門コード変更可にする
//            if (appcst.data.controllerName === "juchuhenkoh") {
            $("#hall_cd").removeAttr("disabled");
//            }
            // 基本タブ 性別を設定
            if (dataSekoKihon.k_sex_kbn === "2") {
                $('#female').click();
            } else {
                $('#male').click();
            }
            // 喪主タブ 請求先の喪主に同じを設定
            if (dataSekoKihon.sekyu_kbn === "1" && $('#as_chief:checked').val() !== "1") {
                $('#as_chief').click();
            }
            // 喪主タブ 故人に同じを設定
            if (dataSekoKihon.mg_kbn === "1" && $('#as_address_4:checked').val() !== "1") {
                $('#as_address_4').click();
            }
            // 基本タブ 住民登録住所の現住所に同じを設定
            if (dataSekoKihon.kj_kbn === "1" && $('#as_address_2:checked').val() !== "1") {
                $('#as_address_2').click();
            }
            // 基本タブ 本籍の現住所に同じを設定
            if (dataSekoKihon.kh_kbn === "1" && $('#as_address_3:checked').val() !== "1") {
                $('#as_address_3').click();
            }
            // 喪主タブ 住民登録住所の故人に同じを設定
            if (dataSekoKihon.mj_kbn === "1" && $('#as_address_5:checked').val() !== "1") {
                $('#as_address_5').click();
            }
            // 故人名画像ファイルリンク設定
            appcst.appView.setImgLink("#input-tab", dataSekoKihon.k_file_nm, null);
            // 喪主名画像ファイルリンク設定
            appcst.appView.setImgLink("#infochief-tab #chief", dataSekoKihon.m_file_nm, null);
        }
        if (dataSekyuInfo) {
            // 請求先画像ファイルリンク設定
            appcst.appView.setImgLink("#infochief-tab #bill", dataSekyuInfo.sekyu_file_nm, null);
        }
        // 互助会タブ
        if (dataGojokaiInfo) {
            // 施行履歴を設定
            if (dataGojokaiInfo.rireki_kbn === "1") {
                $('#experienced').click();
            } else {
                $('#inexperienced').click();
            }
            // 事前相談有無を設定
            if (dataGojokaiInfo.sodan_kbn === "1") {
                $('#adviced').click();
            } else {
                $('#unadviced').click();
            }
            // 客様加入確認有無を設定
            if (dataGojokaiInfo.kanyu_kakunin_kbn === "1") {
                $('#registered').click();
            } else {
                $('#unregistered').click();
            }
            if (dataGojokaiInfo.plan_use_kbn === '1') {
                $('#plan_agree').click();
            } else {
                $('#plan_disagree').click();
            }
            appcst.setPlanUsePrc();
        }
        // データを退避する
        orgDataApp = appcst.appModel.toJSON();
        orgDataKihonFree = appcst.kfModel.toJSON();
        orgDataNiteiCol = appcst.niteiCol.toJSON();
        orgDataSekyuInfo = appcst.sekyuModel.toJSON();
        orgDataGojokaiInfo = appcst.gojokaiInfoModel.toJSON();
        orgDataGojokaiMemberCol = appcst.gojokaiMemberCol.toJSON();
        // 部門コードの初期値セット
        if ($.msiJqlib.isNullEx2(appcst.appModel.get('bumon_cd'))) {
            var bumonCd = $('#hall_cd').val();
            appcst.appModel.set('bumon_cd', bumonCd);
        }
        // 上記プラン選択、お客様加入確認、加入団体を非活性
        $("#plan_change_set input[name='plan_kbn']").button({disabled: true});
        $("#other_entry_set input[name='other_entry']").button({disabled: true});
        $("#join_use_set input[name='join_use']").button({disabled: true});
        $("#spec_agent").attr('disabled', 'disabled');
        $("#other_entry_group").attr('disabled', 'disabled');

// 2017.04.18 それぞれの部門に計上する
//葬儀・法事は計上部門は「00010：塩釜本社」のみなので、変更不可にする
//        $("#hall_cd").attr("disabled", "disabled");
//		$('#hall_cd').select2('val', '00010');
    };
    _resetData(data.dataSekoKihon, data.dataNiteiCol, data.dataSekyuInfo, data.dataGojokaiInfo, data.dataGojokaiMemberCol, data.dataSekoKihonFree);
    // かな自動入力ペア key:名称要素 value[カナ要素, カナモデルプロパティ, モデル]
    var _name_kana_pair = {
        '#input-tab #name': ['#input-tab #kana', 'k_knm', appcst.appModel]
        , '#infochief-tab #chief #name': ['#infochief-tab #chief #kana', 'm_knm', appcst.appModel]
        , '#infochief-tab #bill #name': ['#infochief-tab #bill #kana', 'sekyu_knm', appcst.sekyuModel]
        , '#infochief-tab #family_name': ['#infochief-tab #family_name_kana', 'souke_knm', appcst.appModel]
        , '#input-tab #syuha_nm_other': ['#input-tab #syuha_knm2', 'syuha_knm', appcst.appModel]
    };
    // カナ自動入力設定
    $.msiJqlib.setAutoKanaModel(_name_kana_pair);
    $("#btn_print").hide(); // 印刷ボタンを隠す
    $("#customer-div-wrapper").show();

    // ヘッダー共通処理
    // 新規作成ボタン押下
    $("#header #btn_new").click(function () {
        // mihara 20150514   if (!confirm('新規作成します。よろしいですか？')) {
        //    return;
        // }
        location.href = $.msiJqlib.baseUrl() + '/juchu/' + appcst.data.controllerName + '/new';
    });
});
