<?php /* Smarty version 2.6.28, created on 2025-06-05 11:48:27
         compiled from _fdn_header_base.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_fdn_header_base.tpl', 5, false),)), $this); ?>
<body>

<div id="wrapper">
  <div id="header">
<?php if (((is_array($_tmp=$this->_tpl_vars['my_header_logo'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<?php echo $this->_tpl_vars['my_header_logo']; ?>

<?php else: ?>
<a href="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
" id="logo"><?php echo ((is_array($_tmp=$this->_tpl_vars['app_title'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</a>
<?php endif; ?>
<?php if (((is_array($_tmp=$this->_tpl_vars['is_login'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_menu'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<div id="btn_menu" title="サイドメニューを開閉します">メニュー</div>
<?php endif; ?>
        <ul class="account <?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_celemony_dialog'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>cls-celemony_dialog_exists<?php endif; ?>">
<?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_nowl_header_bumon'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<?php echo $this->_tpl_vars['_nowl_header_bumon']; ?>

<?php endif; ?>
<?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_celemony_dialog'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
            <li class="id_number call_dialog_msi" data-dialog="celemony_dialog">
                <span class="number" id="seko_no_id"><?php echo ((is_array($_tmp=$this->_tpl_vars['seko_no'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</span>
            </li>
<?php endif; ?>
        </ul>
        <ul class="function">
                        <?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_new'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')) && ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_new_hide'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?><li id="h_btn_new" title="新規作成">新規作成</li><?php else: ?><li>&nbsp;</li><?php endif; ?>
            <?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_info'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?><li id="btn_info" title="インフォメーション">インフォメーション</li><?php else: ?><li>&nbsp;</li><?php endif; ?>
            <?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_help'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?><li id="btn_help" title="ヘルプ">ヘルプ</li><?php else: ?><li>&nbsp;</li><?php endif; ?>
            <?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_util'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?><li id="btn_util" title="個人設定">個人設定</li><?php else: ?><li>&nbsp;</li><?php endif; ?>
            <?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_account'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?><li id="btn_account" title="アカウント">アカウント</li><?php else: ?><li>&nbsp;</li><?php endif; ?>
                        <?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_logout'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?><li id="h_btn_logout" title="ログアウト">ログアウト</li><?php else: ?><li>&nbsp;</li><?php endif; ?>
        </ul>
        <div id="account_info">
            <div class="account_name"><?php echo htmlspecialchars(Msi_Sys_Utils::getAuthInfo('user_snm')) ; ?></div>
            <div class="last_login">前回ログイン日時<br />
            <?php echo htmlspecialchars(Msi_Sys_Utils::getLastLoginTsUi()); ?></div>
        </div>

        <div id="util_info" style="display:none">
        <ul>
          <?php $_from = $this->_tpl_vars['menuOnHeader']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }$this->_foreach['loop'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['loop']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['myrec']):
        $this->_foreach['loop']['iteration']++;
?>
          <!-- li class="subtitle"><?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['title'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</li -->
          <?php $_from = $this->_tpl_vars['myrec']['items']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }$this->_foreach['loop2'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['loop2']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['mysub']):
        $this->_foreach['loop2']['iteration']++;
?>
          <li><a href="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
<?php echo ((is_array($_tmp=$this->_tpl_vars['mysub']['url'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['mysub']['cap'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</a></li>
          <?php endforeach; endif; unset($_from); ?>
          <?php endforeach; endif; unset($_from); ?>
        </ul>
        </div>

        <?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_new'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
        <div id="h_new_info" style="display:none">
        <ul>
          <li><a id="btn_new" href="javascript:void(0)" title="新規作成画面へ移動します">新規作成</a></li>
        </ul>
        </div>
        <?php endif; ?>

        <?php if (! ! ((is_array($_tmp=$this->_tpl_vars['is_fdn_header_btn_logout'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
        <div id="h_logout" style="display:none">
        <ul>
          <li><a id="btn_logout" href="javascript:void(0)" title="ログアウト実行します">ログアウト</a></li>
        </ul>
        </div>
        <?php endif; ?>


<?php endif; ?>    </div><!-- /#header -->