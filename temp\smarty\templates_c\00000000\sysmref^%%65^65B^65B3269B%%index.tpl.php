<?php /* Smarty version 2.6.28, created on 2025-06-05 11:48:39
         compiled from sysmref/dlg/index.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'sysmref/dlg/index.tpl', 1, false),)), $this); ?>
<?php if (((is_array($_tmp=$this->_tpl_vars['is_iframe'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "fdn_head_std.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<div id="wrapper">
<div class="container" id="main-container">
<?php else: ?>
<?php echo $this->_tpl_vars['css_added']; ?>

<?php echo $this->_tpl_vars['js_added']; ?>

<?php endif; ?>
<div id="msi-dialog">

<div id="msi-dialog-header"><span class="title"><?php echo ((is_array($_tmp=$this->_tpl_vars['dlg_title'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</span>
</div><!-- /#msi-dialog-header -->

<div id="msi-dialog-main" >

<?php if (! ((is_array($_tmp=$this->_tpl_vars['is_no_cond'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<div class="search">
  <form id="msi-dialog-form">
    <div style="float:left;width:80%">
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "sysmref/dlg/".($this->_tpl_vars['dlg_tpl_search']), 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>

    <div class="act-buttons" style="float:right;width:150px">
      <input name="btn_search" id="btn_search" class="act-button-search" type="button" value="検索" />
      <input name="btn_search_prev" id="btn_search_prev" class="act-button-search-prev" type="button" title="前ページ" value="前" />
      <input name="btn_search_next" id="btn_search_next" class="act-button-search-next" type="button" title="次ページ" value="次" />
      <!-- input name="btn_clear" id="btn_clear" class="act-button-clear" type="button" value="クリア" / -->
    </div><!-- /.buttons -->
  </form>
</div><!-- /.search -->
<div style="clear:both"></div>
<?php endif; ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "sysmref/dlg/list.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div><!-- /#msi-dialog-main -->

<?php if (! ((is_array($_tmp=$this->_tpl_vars['is_no_footer'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<?php if (((is_array($_tmp=$this->_tpl_vars['dlg_tpl_footer'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "sysmref/dlg/".($this->_tpl_vars['dlg_tpl_footer']), 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php else: ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "sysmref/dlg/footer.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?><?php endif; ?>
</div><!-- /#msi-dialog -->

<script id="my-data-init-dlg-id" type="application/json">
<?php echo $this->_tpl_vars['mydata_init_dlg_json']; ?>

</script>

<?php if (((is_array($_tmp=$this->_tpl_vars['is_iframe'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
</div><!-- /#main-container -->
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "fdn_footer_std.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php else: ?>
<!-- /div --><!-- /#wrapper -->
<?php echo $this->_tpl_vars['css_added']; ?>

<?php echo $this->_tpl_vars['js_added']; ?>

<?php endif; ?>