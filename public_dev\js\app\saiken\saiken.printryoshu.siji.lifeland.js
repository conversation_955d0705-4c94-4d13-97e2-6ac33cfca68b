/** 
 * @fileoverview 印刷指示画面
 */
var apps = apps || {};
$(function () {
    "use strict";
    apps.pro = {
        s_bumon_nm : '#s_bumon_nm',
    };
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                taisho_st_ymd: null,
                taisho_ed_ymd: null,
                taisho_ymd: {
                    'display': '', // 非表示の場合、「'none'」
                    'pattern': 'ymd'    // 年月指定の場合、「'ym'」
                },
                s_bumon_cd   : null,
                s_bumon_nm   : null,
            };
        },
        validation: {
            s_bumon_nm: {
                required: true,
            },
            taisho_st_ymd: {
                required: true,
                fn      : Backbone.Validation.msi_v_fn.ymd,
            },
            taisho_ed_ymd: {
                required: true,
                fn      : Backbone.Validation.msi_v_fn.ymd,
            },
        },

        labels: {
            s_bumon_nm   : '会社',
            taisho_st_ymd: '対象年月日(自)',
            taisho_ed_ymd: '対象年月日(至)',
        }

    }); // AppModel

    var AppView = Backbone.View.extend({
        el: document, // '#my-form-id', // for #hall_cd
        events: {
            "click #btn_print"         : "doPrint",
            "click #btn_csvout"        : "doCsvOut",
            "click #chohyo_ari": function () {
                this.model.set("chouhyou_kbn", "1");
            },
            "click #chohyo_nashi": function () {
                this.model.set("chouhyou_kbn", "0");
            },
            "click .bumon-ref"         : "bumonHelper",
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(apps.pro, "my-error"));
            this.render();
        },
        render: function () {
            Backbone.Validation.bind(this, {
                valid: function (view, attr, selector) {
                    var $el    = view.$('#' + attr),
                        $group = $el.closest('.input-group');
                    $group = $group || $el;
                    $el.removeClass('my-error');
                    $el.attr('title', '');
                    $group.removeClass('has-error');
                },
                invalid: function (view, attr, error, selector) {
                    var $el    = view.$('#' + attr),
                        $group = $el.closest('.input-group');
                    $group = $group || $el;
                    $el.addClass('my-error');
                    $el.attr('title', error);
                    $group.addClass('has-error');
                }
            });
            this.stickit();
            // スクロール調整
            this.scrollAdj();
            this.btnEnabled('#btn_print, #btn_csvout');
            return this;
        },
        // 部門 pickup
        bumonHelper: function() {
            var bbm = this.model;
            this.$el.msiPickHelper({
                action: 'bumon',
                onSelect: function(data) {
                    bbm.set('s_bumon_cd', data.code);
                    bbm.set('s_bumon_nm', data.name.trim());
                },
                onClear: function() {
                    bbm.set('s_bumon_cd', null);
                    bbm.set('s_bumon_nm', null);
                },
                hookSetData: function() {
                    return {
                        s_bumon_kbn: 0,
                        s_except   : ['00001'] // 除外部門コード
                    }
                },
            });
        },
        // select2設定処理
        setSelect2: function (id, select2Data) {
            var isMultiple = ($(id).attr("data-picker-param") === "multiple:true");
            if (isMultiple) {
                $.msiJqlib.setSelect2Com1(this.$(id), {data: select2Data, multiple: isMultiple, placeholder: '(未設定)'});
            } else {
                $.msiJqlib.setSelect2Com1(this.$(id), {data: select2Data});
            }
        },
        // スクロールバー表示調整
        scrollAdj: function () {
            var $list = this.$('.items .list'),
                $header = this.$('.items .header'),
                sc_of,
                sc_w,
                hh;
            if ($list.length > 0) {
                if ($list[0].scrollHeight === $list[0].clientHeight) {
                    sc_of = 'auto';
                    sc_w = '44.4%';
                    $list.css("overflow-y", sc_of);
                    $header.css("overflow-y", sc_of);
                } else {
                    sc_of = 'scroll';
                    sc_w = '43.3%';
                    hh = $header.height();
                    $list.css("overflow-y", sc_of);
                    $header.css("overflow-y", sc_of);
                    $header.height(hh); // for Chrome. XXX
                }
            }
        },
        btnDisabled: function (elem) {
            $(elem).attr("disabled", "disabled");
        },
        btnEnabled: function (elem) {
            $(elem).removeAttr("disabled");
        },
        isInputOk: function () {
            this.clearErr();
            var aMsg = [], line;
            var result = this.model.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // NG
            if (aMsg.length > 0) {
                msiLib2.showErr(aMsg);
                return false;
            }
            // OK
            msiLib2.clearAlert();
            return true;
        },
        clearErr: function () {
            this.$el.msiErrClearAll();
        },
        // 印刷ボタン押下時
        doPrint: function (ev) {
            this.exePrint();
        },
        // CSVボタン押下時
        doCsvOut: function (ev) {
            this.exePrint(true);
        },
        exePrint: function (csv) {
            if (!this.isInputOk()) {
                return;
            }
            this.clearErr();
            var appObj = this.model.toJSON();
            appObj["csv"] = csv;
            var dataAppJson = JSON.stringify(appObj);
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/saiken/' + this.model.get("report_cd"),
                data: {
                    dataAppJson: dataAppJson,
                }
            });
        },
        bindings: {
            '#taisho_st_ymd': 'taisho_st_ymd',
            '#taisho_ed_ymd': 'taisho_ed_ymd',
            '#s_bumon_cd'   : 's_bumon_cd',
            '#s_bumon_nm'   : 's_bumon_nm',
        },
    }); // AppView

    var _getSelect2Data = function (dataAry) {
        var select2Data = new Array();
        _.each(dataAry, function (m) {
            select2Data.push({id: m.id, text: m.text});
        });
        return select2Data;
    };
    // 値を指定してAppViewのbindingsのキーを取得
    function bindingsKeyOf(value) {
        for (var key in app.bindings) {
            if (app.bindings[key] === value) {
                return key;
            }
        }
        return null;
    }
    var app = new AppView({model: new AppModel});
    var _resetData = function (myApp, myValidations, myLabels, myBindings, mySel2DtAry) {
        app.model.set(myApp);
        // 対象期間の非表示チェック
        if (app.model.get("taisho_ymd").display === 'none') {
            app.model.validation["taisho_st_ymd"].required = false;
            app.model.validation["taisho_ed_ymd"].required = false;
            $('.taisho_ymd').css("display", "none");
        } else if (app.model.get("taisho_ymd").pattern === 'ym') {
            // 年月指定
            app.model.validation["taisho_st_ymd"].required = false;
            app.model.validation["taisho_ed_ymd"].required = false;
            $('.taisho_ymd').css("display", "none");
        } else {
            // 年月日指定
            //app.model.validation["taisho_st_ym"].required = false;
            //app.model.validation["taisho_ed_ym"].required = false;
        }
        // バリデーション設定
        if (myValidations !== undefined) {
            for (var key in myValidations) {
                app.model.validation[key] = myValidations[key];
            }
        }
        // ラベル設定
        if (myLabels !== undefined) {
            for (var key in myLabels) {
                app.model.labels[key] = myLabels[key];
            }
        }
        // バインディング設定
        if (myBindings !== undefined) {
            for (var key in myBindings) {
                app.bindings[key] = myBindings[key];
            }
        }
        // select2設定
        if (mySel2DtAry !== undefined) {
            for (var i = 0, len = mySel2DtAry.length; i < len; i++) {
                app.setSelect2(mySel2DtAry[i].id, _getSelect2Data(mySel2DtAry[i].data));
            }
        }
        $(document).msiErrClearAll();
        app.model.trigger('change'); // patch
    };
    var _setInitData = function () {
        var mydata = msiLib2.getJsonFromHtml($('#my-data-init-id'));
        _resetData(mydata.dataApp, mydata.validations, mydata.labels, mydata.bindings, mydata.select2DataAry);
    };
    // リサイズ処理
    $(window).on('resize', function () {
        app.render();
    });
    $.msiJqlib.initDone(function () {
        _setInitData();
    }); // 処理完了
    $('#order').fadeIn('fast'); // ちらつきのごまかし
});
