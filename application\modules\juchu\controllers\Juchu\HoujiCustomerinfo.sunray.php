<?php

/**
 * Juchu_HoujiCustomerinfo
 *
 * 法事お客様情報クラス
 *
 * @category   App
 * @package    controllers\Juchu\HoujiCustomerinfo
 * <AUTHOR> Sai
 * @since      2014/06/26
 * @version    2019/05/07 mihara 軽減税率対応
 * @filesource 
 */

/**
 * 法事お客様情報クラス
 *
 * @category   App
 * @package    controllers\Juchu\HoujiCustomerinfo
 * <AUTHOR>
 * @since      2014/06/26
 */
class Juchu_HoujiCustomerinfo extends Juchu_JuchuCustomerinfoCom {

    /** 申込区分=>2:法事 */
    const MOUSHI_KBN_HOUJI = '2';
    /** コード区分: 0950=>施行法要 */
    const CODE_KBN_HOUYO = '0950';
    /** コード区分: 0960=>法要場所 */
    const CODE_HOUYO_BASHO_KBN = "0670";
    /** コード区分: 2530=>会食会場 */
    const CODE_KAISHOKU_KAIJO_KBN = "0670";
    /** コード区分: 1790=>元号 */
    const CODE_KBN_GENGO2 = '1790';
    /** コード区分: 7806=>用途(法事) */
    const CODE_KBN_YOTO_HOUJI = '7806';
    /** コード区分: 8140=>日程(法事) */
    const CODE_KBN_NITEI_HOUJI = '8140';
    /** コード区分: 7802=>会員情報区分 */
    const CODE_KBN_KAIIN_INFO = '7802';

    /**
     *
     * currentのcss名を取得する
     * <AUTHOR> Sai
     * @since 2014/06/26
     * @return customer
     */
    public function getCssClassName() {
        return 'customer';
    }

    /**
     * 初期情報取得処理
     *
     * <AUTHOR> Sai
     * @since 2014/06/26
     * @return array jsonData
     */
    public function getInitData($sidemenukey = '') {

        $this->_sekoNo = $this->getReqSekoNo();
        $db = Msi_Sys_DbManager::getMyDb();
        // 施行基本情報を設定する
        $this->setInitParam();
        // 施行基本情報を取得する
        $dataSekoKihon = $this->getSekoKihon();
        if (!isset($dataSekoKihon['uketuke_tanto_cd'])) {
            // お客様情報新規登録時の受付担当者にログイン者の情報を設定する
            $dataSekoKihon['uketuke_tanto_cd'] = App_Utils::getTantoCd();
            $dataSekoKihon['uketuke_tanto_nm'] = App_Utils::getTantoNm();
        }
        // 施行基本フリー情報を取得する
        $dataKihonFree = $this->getKihonFree();
        if (isset($dataKihonFree['tanto_cd1'])) {
            $dataSekoKihon['mitsu_tanto_cd'] = $dataKihonFree['tanto_cd1'];
            $dataSekoKihon['mitsu_tanto_nm'] = $dataKihonFree['tanto_nm1'];
        }
        if (Msi_Sys_Utils::myCount($dataKihonFree) == 0 && !isset($dataKihonFree['ts_free1'])) {
            // 新規登録時の受付日を設定する
            $dataKihonFree['ts_free1'] = Msi_Sys_Utils::getDate(null, 'Y/m/d H:i');
            $dataKihonFree['ts_free1_date'] = Msi_Sys_Utils::getDate();
            $dataKihonFree['ts_free1_time'] = Msi_Sys_Utils::getDate(null, 'H:i');
        }
        if (!isset($dataSekoKihon['est_shikijo_cd'])) {
            // ヘッダー部門の部門マスタの会計部門コードを部門区分が2:式場が条件
            $hall_cd = App_Utils::getCtxtHallWithKaisya();
            $bumonData = DataMapper_BumonMst::findOne($db, array('bumon_cd' => $hall_cd));
            if (Msi_Sys_Utils::myCount($bumonData) > 0) {
                $estBumon = DataMapper_BumonMst::find($db, array('kaikei_bumon_cd' => $bumonData['kaikei_bumon_cd'], 'bumon_kbn' => 2));
                if (Msi_Sys_Utils::myCount($estBumon) == 1) {
                    $dataSekoKihon['est_shikijo_cd'] = $estBumon[0]['bumon_cd'];
                }
            }
        }
        $dataSekoKihon['temple_cd2'] = $dataKihonFree['temple_cd2'];
        $dataSekoKihon['temple_nm2'] = $dataKihonFree['temple_nm2'];
        $dataSekoKihon['ofuse']      = $dataKihonFree['ofuse'];
        $dataSekoKihon['okuruma']    = $dataKihonFree['okuruma'];
        $dataSekoKihon['irai_biko']  = $dataKihonFree['irai_biko'];
        // 施行互助会加入者を取得する
        $dataGojokaiMemberCol = $this->getGojokaiMember(10);
        // 故人情報を取得する
        $dataKojinInfoCol = $this->getKojinInfo();
        // 施行日程を取得する
        $dataNiteiCol = $this->getNitei();
        // 施行請求先情報を取得する
        $dataSekyuInfo = $this->getSekyuInfo();
        // 施行互助会情報を取得する
        $dataGojokaiInfo = $this->getGojokaiInfo();
        // 施行契約先情報を取得する
        $dataSekoKeiyakusakiInfo = $this->getKeiyakusakiInfo();
        // 互助会コースマスタを取得する
        $gojokaiCouseMst = $this->getGojokaiCouseMst();
        // コード名称マスタデータを取得する
        $dataCodeNameMst = $this->getCodeNameMst();
        // 部門マスタを取得する(プルダウン用)
        $dataBumon = $this->getCdBumon();

        $controllerName = App_Utils::getParamsControllerName();
        $actionName = App_Utils::getParamsActionName();
        $gojokai_cose = $this->GetCodeNameMst2($db, self::CODE_GOJOKAI_COSE); // ソート順のため、個別取得する
        $this->_zeiKijunYmd = $this->getZeiKijunYmd();
        // 消費税取得処理
        $taxInfo = App_ClsTaxLib::GetTaxInfo($db, $this->_zeiKijunYmd);
        $taxInfoAll = App_ClsTaxLib::GetTaxInfoAll($db);
        // サイドメニューデータを取得する
        if (App_Utils::isHoujiInJuchu($controllerName) || App_Utils::isCustomerNewinJuchu($controllerName)) {
            Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
            $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName, $this->_moushiKbn);
        } else {
            $sideMenuData = $this->getSideMenuData();
        }
        list($hall_cd, $area_cd) = App_Utils::getDfltHallArea();
        // 契約種別用プルダウンを生成する
        $dataKeiyakuCif = $this->getKeiyakuCif();
        // 部門マスタを取得する(取扱店)
        $dataBumonT = $this->getCdBumonT();
        // 部門マスタを取得する(入電店)
        $dataBumonN = $this->getCdBumonN();
        // 口座情報取得
        $br_koza_info = $this->getBrKozaKanriMst($db);
        $dataSekoKihon['login_bumon_cd'] = App_Utils::getTantoBumonCd(App_Utils::getTantoCd());
        $dataKbns = array(
            // ヘッダー部
            'moushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_MOUSHI_KBN_H), // 申込区分
            'sougi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SOUGI_KBN), // 葬儀区分
            'p_info' => $this->filter($dataCodeNameMst, self::CODE_P_INFO), // 個人情報保護
            'kaiin_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_KBN), // 会員区分
            'est_shikijo' => $dataBumon, // 部門マスタ
            'cif_status_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_CIF_STATUS), // 検索結果区分
            'est_shikijo' => $dataBumon, // 部門マスタ
            'toriatsukai_kbn' => $dataBumonT, // 取扱店
            'nyuden_kbn'      => $dataBumonN, // 入電店
            'kaiin_sbt_cd'    => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_SBT),
            // 喪主タブ
            'zoku_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_ZOKU_KBN), // 続柄区分
            'gengo' => DataMapper_EraMst::getCodeNmEra(), // 元号
            'month' => DataMapper_EraMst::getCodeNmMonth(), // 元号(月)
            'day' => DataMapper_EraMst::getCodeNmDay(), // 元号(日)
            'kinmu_kbn_k' => $this->filter($dataCodeNameMst, self::CODE_KBN_KINMU_K), // 勤務先区分(故人)
            'kinmu_kbn_m' => $this->filter($dataCodeNameMst, self::CODE_KBN_KINMU_M), // 勤務先区分(喪主)
            'umu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_UMU), // 有無区分
            // 法事情報タブ
            'syuha_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUHA_KBN), // 宗派区分
            'syushi_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_SYUSHI_KBN), // 宗旨区分
            'hoyo_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_HOUYO), // 法要区分
            // 施行情報タブ
            'sogi_basho_kbn' => $this->filter($dataCodeNameMst, self::CODE_SOGI_BASHO_KBN), // 葬儀場所
            // 互助会タブ
            'kaiin_info_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KAIIN_INFO), // 会員情報区分
            'yoto_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_YOTO_HOUJI), // 用途
            'hunsitu_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_9717), // 紛失区分
            'wariken_mst' => $this->filter($dataCodeNameMst, self::CODE_KBN_9851), // 割引券
            'tanto_mst' =>  $this->getCdTanto(), // 部門マスタ
            'gojokai_cose' => $gojokai_cose, // 互助会コース
            'zei_cd' => $this->getZei_cd(), // 消費税コード
            'user_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_USER), // 利用者区分
            'kyosai_kbn' => $this->filter($dataCodeNameMst, self::CODE_KBN_KYOSAI), // 共済組合区分
            'genkyo_cd' => $this->filter($dataCodeNameMst, self::CODE_KBN_GENKYO), // 現況コード
        );
        $codeKbns = array(
            // ヘッダー部
            'moushi_code_kbn' => self::CODE_KBN_MOUSHI_KBN_H, // 申込区分
            'sougi_code_kbn' => self::CODE_KBN_SOUGI_KBN, // 葬儀区分
            'p_info_code_kbn' => self::CODE_P_INFO, // 個人情報保護
            'kaiin_code_kbn' => self::CODE_KBN_KAIIN_KBN, // 会員区分
            // 法事情報タブ
            'umu_code_kbn' => self::CODE_KBN_UMU, // 有無区分
            'zoku_code_kbn' => self::CODE_KBN_ZOKU_KBN, // 続柄区分
            'syuha_code_kbn' => self::CODE_KBN_SYUHA_KBN, // 宗派区分
            'syushi_code_kbn' => self::CODE_KBN_SYUSHI_KBN, // 宗旨区分
        );
        // 画面データを設定する
        $data = array(
            'dataSekoKihon' => $dataSekoKihon,
            'dataSekoKihonFree' => $dataKihonFree,
            'dataNiteiCol' => $dataNiteiCol,
            'dataKojinInfoCol' => $dataKojinInfoCol,
            'dataSekyuInfo' => $dataSekyuInfo,
            'dataGojokaiInfo' => $dataGojokaiInfo,
            'dataSekoKeiyakusakiInfo' => $dataSekoKeiyakusakiInfo,
            'dataGojokaiMemberCol' => $dataGojokaiMemberCol,
            'gojokaiCouseMst' => $gojokaiCouseMst,
            'dataKbns' => $dataKbns,
            'codeKbns' => $codeKbns,
            'controllerName' => $controllerName,
            'actionName' => $actionName,
            'taxInfo' => $taxInfo,
            'taxInfoAll' => $taxInfoAll,
            'sideMenuDataCol' => $sideMenuData['dataCol'],
            'area_cd' => $area_cd,
            'sidemenukey' => $sidemenukey,
            'keiyakuCif' => $dataKeiyakuCif,
            'brKozaInfo' => $br_koza_info,
        );
        $jsonData = Msi_Sys_Utils::json_encode($data);
        return $jsonData;
    }

    /**
     *
     * 施行基本情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/2/12
     * @version 2014/05/30 お知らせ状で使用するため、Juchu_JuchuCustomerinfoから移動
     * @return array 施行基本情報
     */
    protected function getSekoKihon() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            k.seko_no           -- 施行番号
            ,k.daicho_no_eria       
            ,TRIM(k.daicho_no_mm) AS daicho_no_mm
            ,k.daicho_no_seq      
            ,k.bumon_cd         -- 部門コード
            ,k.bumon_cd1 AS nyudentenpo_cd
            ,k.bumon_cd AS tenpo_cd
            ,k.moushi_cd        -- 申込コード
            ,k.moushi_kbn       -- 申込区分
            ,k.sougi_cd         -- 葬儀コード
            ,k.sougi_kbn        -- 葬儀区分
            ,k.p_info_cd        -- 個人情報保護コード
            ,k.p_info_kbn       -- 個人情報保護区分
            ,k.kaiin_cd         -- 会員コード
            ,k.kaiin_kbn        -- 会員区分
            ,k.kaiin_sonota     -- 会員区分その他
            ,k.uketuke_tanto_cd -- 受付担当者コード
            ,t1.tanto_nm AS uketuke_tanto_nm    -- 受付担当者名
            ,k.seko_tanto_cd    -- 施行担当者コード
            ,t2.tanto_nm AS seko_tanto_nm    -- 施行担当者名
            ,k.k_nm             -- 故人名
            ,k.k_file_nm        -- 故人名添付ファイル
            ,k.k_sex_cd         -- 性別コード
            ,k.k_sex_kbn        -- 性別区分
            ,k.k_haigu_cd       -- 配偶者コード
            ,k.k_haigu_kbn      -- 配偶者区分
            ,k.k_knm            -- 故人カナ名
            ,k.k_gengo          -- 生年月日元号
            ,TRIM(k.k_seinengappi_ymd) AS k_seinengappi_ymd  -- 生年月日
            ,k.k_nenrei_man     -- 故人年齢
            ,k.k_nenrei_kyounen -- 享年
            ,k.kg_yubin_no      -- 現住所郵便番号
            ,k.kg_addr1         -- 現住所1
            ,k.kg_tel           -- 現住所TEL
            ,k.kg_addr2         -- 現住所2
            ,k.kk_kinmusaki_kbn -- 勤務先
            ,k.kk_kinmusaki_nm  -- 勤務先名
            ,k.kk_tel           -- 勤務先TEL
            ,k.kk_yakusyoku_nm  -- 役職／職種
            ,k.kk_fax           -- 勤務先FAX
            ,k.keishiki_cd      -- 葬儀形式コード
            ,k.keishiki_kbn     -- 葬儀形式区分
            ,k.m_cif_no             
            ,k.m_cif_status         
            ,k.s_cif_no             
            ,k.s_cif_status         
            ,k.m_last_nm             -- 喪主苗字
            ,k.m_first_nm             -- 喪主名前
            ,k.m_nm             -- 喪主名
            ,k.m_last_knm            -- 喪主苗字カナ
            ,k.m_first_knm            -- 喪主名前カナ
            ,k.m_knm            -- 喪主名カナ
            ,k.m_file_nm        -- 喪主添付ファイル
            ,k.m_zoku_cd        -- 喪主続柄コード
            ,k.m_zoku_kbn       -- 喪主続柄区分
            ,k.m_zoku_cd2       -- 喪主からみた続柄コード
            ,k.m_zoku_kbn2      -- 喪主からみた続柄区分
            ,k.m_gengo          -- 喪主生年月日元号
            ,k.m_birth_year          -- 喪主誕生年
            ,k.m_birth_month          -- 喪主誕生月
            ,k.m_birth_day          -- 喪主誕生日
            ,k.m_seinengappi_ymd    -- 喪主生年月日
            ,k.m_nenrei_man     -- 喪主年齢
            ,k.mg_kbn           -- 喪主 故人に同じ
            ,k.mg_yubin_no      -- 喪主現住所郵便番号
            ,k.mg_addr1         -- 喪主現住所1
            ,k.mg_tel           -- 喪主現住所TEL
            ,k.mg_addr2         -- 喪主現住所2
            ,k.mk_kinmusaki_kbn -- 喪主勤務先
            ,k.mk_kinmusaki_nm  -- 喪主勤務先名
            ,k.mk_tel           -- 喪主勤務先TEL
            ,k.mk_yakusyoku_nm  -- 喪主役職／職種
            ,k.mk_fax           -- 喪主勤務先FAX
            ,k.sekyu_kbn        -- 請求先の喪主に同じ
            ,k.sekyu_cd         -- 請求先コード
            ,k.jichu_kakute_ymd -- 受注確定日
            ,k.sg_seko_kbn -- 葬儀施行区分
            ,k.hj_seko_kbn -- 法事施行区分
            ,k.sk_houyo_cd -- 施行法要区分コード
            ,k.sk_houyo_kbn -- 施行法要区分
            ,k.k_kaimyo -- 故人戒名
            ,TO_CHAR(k.k_death_ymd ,'YYYY/MM/DD') AS k_death_ymd-- 故人亡日
            ,TO_CHAR(k.sougi_ymd ,'YYYY/MM/DD') AS sougi_ymd
            ,k.k_nenrei_man     -- 年齢満
            ,k.free2_cd         -- 過去葬儀・法事施行番号
            ,k.souke_nm         -- 葬家名
            ,k.souke_knm        -- 葬家名カナ
            ,k.mg_m_tel         -- 喪主携帯
            ,k.biko1            -- 備考
            ,k.biko2            -- 備考
            ,k.m_zoku_cd        -- 喪主続柄コード
            ,k.m_zoku_kbn       -- 喪主続柄区分
            ,k.syushi_cd        -- 宗旨コード
            ,k.syushi_kbn       -- 宗旨区分
            ,k.syuha_cd         -- 宗派コード
            ,k.syuha_kbn        -- 宗派区分
            ,k.syuha_nm         -- 宗派名
            ,k.syuha_knm        -- 宗派カナ名
            ,k.jyusho_cd        -- 寺院コード
            ,k.jyusho_nm        -- 寺院名
            ,k.jyusho_knm       -- 寺院カナ名
            ,tera.tel AS temple_tel -- 寺院TEL
            ,tera.fax AS temple_fax -- 寺院FAX
            ,tera.zip_no AS temple_yubin_no -- 寺院郵便番号
            ,tera.addr1_nm AS temple_addr1 -- 寺院住所1
            ,tera.addr2_nm AS temple_addr2 -- 寺院住所2
            ,TO_CHAR(k.d_free1 ,'YYYY/MM/DD') AS kaishu_ymd-- 回収予定日
            ,k.est_shikijo_cd
            ,k.seko_shikijo_cd
            ,k.v_free7 AS seko_tanto_tel
            ,k.v_free9 AS m_mail_address
            ,k.v_free13
            ,k.v_free14
            ,k.v_free15
            ,k.v_free16
            ,k.v_free17
            ,k.n_free4 AS shinzoku_cnt        -- 親族人数
            ,k.n_free5 AS kaiso_cnt        -- 予想参列者数
            ,k.n_free7
            ,k.free6_code_cd AS m_sex_code_kbn -- 喪主性別コード区分
            ,k.free6_kbn AS m_sex_kbn   -- 喪主性別区分
            ,k.status_kbn
            ,bkm.oya_bumon_cd AS est_oya_bumon_cd
            ,skf.free8_code_cd  AS kaiin_sbt_code_cd
            ,skf.free8_kbn      AS kaiin_sbt_cd
            ,k.tanto_cd2 AS after_tanto_cd
            ,t3.tanto_nm AS after_tanto_nm
            ,skf.zip_no2 AS temple_yubin_no
            ,skf.addr2_1 AS temple_addr1
            ,skf.addr2_2 AS temple_addr2
            ,skf.tel_no2 AS temple_tel
            ,temple2.zip_no AS temple2_yubin_no
            ,temple2.addr1_nm AS temple2_addr1
            ,temple2.addr2_nm AS temple2_addr2
            ,temple2.tel AS temple2_tel
        FROM seko_kihon_info k
        LEFT JOIN seko_kihon_all_free skf
            ON  skf.seko_no    = k.seko_no
            AND skf.delete_flg = 0
        LEFT OUTER JOIN tanto_mst t1
            ON k.uketuke_tanto_cd = t1.tanto_cd
            AND t1.delete_flg = 0
        LEFT OUTER JOIN tanto_mst t2
            ON k.seko_tanto_cd = t2.tanto_cd
            AND t2.delete_flg = 0
        LEFT OUTER JOIN tanto_mst t3
            ON k.tanto_cd2 = t3.tanto_cd
            AND t3.delete_flg = 0
        LEFT JOIN nm_jyusho_mst tera
            ON tera.jyusho_kbn = 1
            AND tera.jyusho_cd = k.jyusho_cd
            AND tera.delete_flg = 0
        LEFT JOIN bumon_kaso_mst bkm
            ON bkm.ko_bumon_cd = k.est_shikijo_cd
            AND bkm.delete_flg = 0
        LEFT JOIN nm_jyusho_mst temple2
        ON temple2.jyusho_kbn = 1
        AND temple2.jyusho_cd = skf.free11_code_cd
        AND temple2.delete_flg = 0
        WHERE k.seko_no = :seko_no
            AND k.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        if (isset($select['k_birth_year']) && isset($select['k_birth_month']) && isset($select['k_birth_day'])) {
            $select['k_seinengappi_ymd_y'] = $select['k_birth_year'] . '-' . $select['k_birth_month'] . '-' . $select['k_birth_day'];
        }
        if (isset($select['m_birth_year']) && isset($select['m_birth_month']) && isset($select['m_birth_day'])) {
            $select['m_seinengappi_ymd_y'] = $select['m_birth_year'] . '-' . $select['m_birth_month'] . '-' . $select['m_birth_day'];
        }
        // 関連施行なしチェック取得処理
        if ($this->isMitsuKakutei()) {
            $denpyoData = DataMapper_UriageDenpyo::find($db, array('uri_den_no' => $this->getUriagedenpyoNo()));
        } else {
            $denpyoData = DataMapper_JuchuDenpyo::findDenpyo($db, array('denpyo_no' => $this->getJuchudenpyoNo()));
        }
        if (Msi_Sys_Utils::myCount($denpyoData) > 0) {
            $select['seko_check_kbn'] = $denpyoData[0]['k_free5'];
        }
        return $select;
    }

    /**
     * コード名称マスタ取得処理
     * 
     * <AUTHOR> Sai
     * @since      2014/06/30
     * @return  array コード名称マスタ
     */
    protected function getCodeNameMst() {
        $db = Msi_Sys_DbManager::getMyDb();
        // コード名称マスタを取得する

        $sql = "
        SELECT
            code_kbn            -- コード区分
            ,code_kbn_nm        -- コード区分名
            ,kbn_value_cd       -- 区分値コード
            ,kbn_value_cd_num   -- 区分値コード数値
            ,kbn_value_lnm      -- 区分値正式名
            ,kbn_value_snm      -- 区分値簡略名
        FROM code_nm_mst
        WHERE delete_flg = 0
        AND code_kbn IN (
            ?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?,?,?,?
            ,?,?
            )
        ORDER BY code_kbn
            ,disp_nox				
            ,kbn_value_cd_num
                ";
        $select = $db->easySelect($sql, array(
            self::CODE_KBN_MOUSHI_KBN, self::CODE_KBN_SOUGI_KBN, self::CODE_KBN_KAIIN_KBN, self::CODE_KBN_GENGO, self::CODE_KBN_KEISHIKI_KBN
            , self::CODE_KBN_YOTO_KBN, self::CODE_KBN_ZOKU_KBN, self::CODE_KBN_HOUYO, self::CODE_HOUYO_BASHO_KBN, self::CODE_KAISHOKU_KAIJO_KBN
            , self::CODE_P_INFO, self::CODE_KBN_YOTO_HOUJI, self::CODE_KBN_GENGO2, self::CODE_KBN_MOUSHI_KBN_H, self::CODE_KBN_SYUSHI_KBN
            , self::CODE_KBN_HAIGU_KBN, self::CODE_KBN_KINMU_K, self::CODE_KBN_KINMU_M, self::CODE_KBN_SYUHA_KBN, self::CODE_KBN_KYOSAI
            , self::CODE_KBN_GENKYO, self::CODE_KBN_YOTO_HOUJI, self::CODE_KBN_KAIIN_INFO, self::CODE_KBN_CIF_STATUS,self::CODE_KBN_KAIIN_SBT
            , self::CODE_KBN_9717, self::CODE_KBN_9851
        ));
        return $select;
    }

    /**
     *
     * 施行日程情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2014/06/30
     * @return array 施行日程情報
     */
    private function getNitei() {

        $dataNitei = array();
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
            SELECT
                sn.seko_no          -- 施行番号
                ,sn.nitei_kbn       -- 日程区分
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD HH24:MI') AS nitei_ymd-- 日程タイムスタンプ
                ,TO_CHAR(sn.nitei_ymd ,'YYYY/MM/DD') AS nitei_date-- 日程日付のみ
                ,TO_CHAR(sn.nitei_ymd ,'HH24:MI') AS nitei_time -- 日程時刻のみ
                ,TO_CHAR(sn.nitei_ed_ymd ,'YYYY/MM/DD HH24:MI') AS nitei_ed_ymd-- 日程終了タイムスタンプ
                ,TO_CHAR(sn.nitei_ed_ymd ,'HH24:MI') AS nitei_ed_time -- 日程終了時刻のみ
                ,sn.spot_cd       -- 場所区分コード
                ,sn.basho_kbn       -- 場所区分
                ,sn.basho_cd        -- 場所コード
                ,sn.basho_nm        -- 場所名 
                ,sn.v_free1
                ,sn.v_free2 -- 当社式場未使用チェック
                ,sn.v_free3 -- 式場予約番号
                ,CASE WHEN sn.basho_kbn = 2 THEN km.bumon_cd
                ELSE NULL END AS kaijo_bumon_cd
            FROM seko_nitei_houji sn
            LEFT JOIN kaijyo_mst km
                ON km.kaijyo_cd = sn.basho_cd
                AND km.delete_flg = 0
            WHERE sn.seko_no = :seko_no
                AND sn.delete_flg = 0
            ORDER BY sn.disp_no
                ";
        $select = $db->easySelect($sql, array('seko_no' => $this->_sekoNo));
        // 施行日程に存在するとき
        if (count($select) > 0) {
            for ($i = 0; $i < count($select); $i++) {
                $niteiOneRowData = array();
                $niteiOneRowData['seko_no'] = $select[$i]['seko_no'];
                $niteiOneRowData['nitei_kbn'] = (int) $select[$i]['nitei_kbn'];
                $niteiOneRowData['nitei_ymd'] = $select[$i]['nitei_ymd'];
                $niteiOneRowData['nitei_date'] = $select[$i]['nitei_date'];
                $niteiOneRowData['nitei_time'] = $select[$i]['nitei_time'];
                $niteiOneRowData['nitei_ed_ymd'] = $select[$i]['nitei_ed_ymd'];
                $niteiOneRowData['nitei_ed_time'] = $select[$i]['nitei_ed_time'];
                $niteiOneRowData['spot_cd'] = $select[$i]['spot_cd'];
                $niteiOneRowData['basho_kbn'] = $select[$i]['basho_kbn'];
                $niteiOneRowData['basho_cd'] = $select[$i]['basho_cd'];
                $niteiOneRowData['basho_nm'] = $select[$i]['basho_nm'];
                $niteiOneRowData['sikijo_check'] = $select[$i]['v_free2'];
                $niteiOneRowData['sikijo_yoyaku_no'] = $select[$i]['v_free3'];
                $niteiOneRowData['kaijo_bumon_cd'] = $select[$i]['kaijo_bumon_cd'];
                $dataNitei[$i] = $niteiOneRowData;
            }
        } else {
            $dataNitei = array(
                array('nitei_kbn' => 0), // 施行日
                array('nitei_kbn' => 4), // 控室
                array('nitei_kbn' => 5), // 納骨
                array('nitei_kbn' => 1), // 法要
                array('nitei_kbn' => 2), // 墓参
                array('nitei_kbn' => 3), // 法宴
            );
        }
        return $dataNitei;
    }

    /**
     * 保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/06/26
     * @param array $req リクエスト
     */
    public function save($req) {
        $cnt = 0;
        $db = Msi_Sys_DbManager::getMyDb();
        $controllerName = $req->getPost('controllerName');
        $oldSekoKihon = $req->getPost('oldDataApp');
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        $dataNiteiCol = Msi_Sys_Utils::json_decode($req->getPost('dataNiteiColJson'));
        $dataSekyuInfo = Msi_Sys_Utils::json_decode($req->getPost('dataSekyuInfoJson'));
        $dataGojokaiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiInfoJson'));
        $dataSekoKeiyakusakiInfo = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKeiyakusakiInfoJson'));
        $dataGojokaiMemberCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberColJson'));
        $dataGojokaiMemberDeleteCol = Msi_Sys_Utils::json_decode($req->getPost('dataGojokaiMemberDeleteColJson'));
        $dataKojinInfoCol = Msi_Sys_Utils::json_decode($req->getPost('dataKojinInfoColJson'));
        $changeFlg = Msi_Sys_Utils::json_decode($req->getPost('changeFlg'));
        $sidemenukey = Msi_Sys_Utils::json_decode($req->getPost('sidemenukey'));

        // 加入員番号重複チェック
        $msg = $this->checkGojokaiMember($dataGojokaiMemberCol);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        $this->_sekoNo = $dataSekoKihon['seko_no'];
        if (empty($this->_sekoNo)) {
            // 廃止部門チェック
            if (App_HakuzenUtils::isBumonHaishiFlgOn($dataSekoKihon['bumon_cd'], $db)) { // 廃止フラグ=1
                $data = array(
                    'status' => 'NG',
                    'msg' => "廃止された部門が設定されています。部門を変更してください。",
                );
                Msi_Sys_Utils::outJson($data);
                return;
            }
            $this->_sekoNo = $this->getAutoSekoNo($db);
        } else { // 2019/05/07 mihara keigen
            App_KeigenUtils::sougiYmdChgPre($this->_sekoNo); // sougi_ymd 変更の準備
        }

        // 施行基本情報を設定する
        $this->setInitParam();
        if (empty($this->_moushiKbn)) {
            $this->_moushiKbn = $dataSekoKihon['moushi_kbn'];
        }
        if (empty($this->_mitsuTantoCd)) {
            $this->_mitsuTantoCd = $dataSekoKihon['mitsu_tanto_cd'];
        }
        if (empty($this->_sekoTantoCd) && isset($dataSekoKihon['seko_tanto_cd'])) {
            $this->_sekoTantoCd = $dataSekoKihon['seko_tanto_cd'];
        }
        // 画面とステータス区分が異なる場合はエラー
        if (!$this->checkStatusKbn($dataSekoKihon)) {
            return;
        }
        // 見積確定後部門が売上部門かどうかチェックする
//        if ($this->isMitsuKakutei() && !$this->checkUriBumon($dataSekoKihon['bumon_cd'])) {
//            $data['status'] = 'NG';
//            $data['msg'] = "売上計上部門が売上未定部門のため、更新することができません。";
//            Msi_Sys_Utils::outJson($data);
//            return;
//        }
        // 会員区分チェック
        $msg = $this->checkKaiinKbn($dataSekoKihon, $dataGojokaiMemberCol);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 契約先番号チェック
        $msg = $this->checkKeiyakusaki($db, $dataSekoKihon, $dataSekoKeiyakusakiInfo);
        if (isset($msg)) {
            $data['status'] = 'NG';
            $data['msg'] = $msg;
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 施行プランコードが存在し見積式場が変わっていたら警告メッセージを出す
//        if (!empty($this->_sekoPlanCd)) {
//            if ($oldSekoKihon['est_shikijo_cd'] != $dataSekoKihon['est_shikijo_cd']) {
//                $data['status'] = 'NG';
//                $data['msg'] = "基本パターンが変更されるため、見積式場を変更することができません。処理を続行するには基本プランを一度削除する必要があります。";
//                Msi_Sys_Utils::outJson($data);
//                return;
//            }
//        }
        // 施行プランコードが存在し会員区分が変わっていたら警告メッセージを出す(見積確定前のみ)
//        if (!$this->isMitsuKakutei() && !empty($this->_sekoPlanCd)) {
//            if ($oldSekoKihon['kaiin_kbn'] != $dataSekoKihon['kaiin_kbn']) {
//                $data['status'] = 'NG';
//                $data['msg'] = "基本パターンが変更されるため、会員区分を変更することができません。処理を続行するには基本プランを一度削除する必要があります。";
//                Msi_Sys_Utils::outJson($data);
//                return;
//            }
//        }
        $gojokai_kbn_change = false; // 互助会区分変更フラグ
        if ($this->_gojokaiKbn !== "" && $this->_gojokaiKbn !== $dataSekoKihon['gojokai_kbn']) {
            $gojokai_kbn_change = true;
        }
        $denpyoNo = $this->getJuchudenpyoNo();
        // 施行日程保存処理 
        // 施行日程保存処理 
        if (empty($dataSekoKihon['seko_no']) || $changeFlg['niteiChangeFlg'] || $changeFlg['kihonChangeFlg']) {
            $msg = $this->saveSekoNitei($db, $dataNiteiCol);
            if (isset($msg)) {
                $data['status'] = 'NG';
                $data['msg'] = $msg;
                Msi_Sys_Utils::outJson($data);
                return;
            }
        }
        // 請求先情報保存処理 
        $cnt += $this->saveSekyuInfo($db,$dataSekoKihon,$dataSekyuInfo);
        // 基本情報の請求コード更新のため
        $changeFlg['kihonChangeFlg'] = true;
        $dataSekoKihon['sekyu_cd'] = $this->_sekyuCd;
        // 施行互助会情報保存処理 
        if ($changeFlg['gojokaiInfoChangeFlg'] || $changeFlg['gojokaiMemberChangeFlg']) {
            $cnt = $this->saveGojokaiInfo($db, $dataGojokaiInfo, $dataGojokaiMemberCol);
        }
        // 施行互助会加入者保存処理 
        if ($changeFlg['gojokaiMemberChangeFlg'] || $changeFlg['niteiChangeFlg']) {
            $cnt = $this->saveGojokaiMember($db, $dataGojokaiMemberCol);
        }
        // 施行契約先情報保存処理 
        if ($changeFlg['sekoKeiyakusakiInfoChangeFlg']) {
            $cnt = $this->saveSekoKeiyakusakiInfo($db, $dataSekoKeiyakusakiInfo);
        }
        // 施行故人情報保存処理 
        if ($changeFlg['kojinInfoChangeFlg']) {
            $cnt += $this->saveKojinInfo($db, $dataKojinInfoCol);
        }
        // 施行基本保存処理 
//        if ($changeFlg['kihonChangeFlg']) {
            //更新時のみチェック
            if (isset($dataSekoKihon['seko_no'])) {
                //施行基本情報取得
                $sql = "
                        SELECT
                             seko_no
                            ,moushi_kbn
                            ,jichu_kakute_ymd
                        FROM seko_kihon_info
                        WHERE seko_no = :seko_no
                        AND delete_flg = 0
                        ";
                $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
                if (count($select) == 0) {
                    $data['status'] = 'NG';
                    $data['msg'] = "施行情報が削除されています。確認してください。";
                    Msi_Sys_Utils::outJson($data);
                    return;
                }
            }
            // 部門コード変更フラグ
            $bumonCdChangeFlg = false;
            $parBumon = App_Utils::parBumonOfHall($dataSekoKihon['bumon_cd']);
            $dataSekoKihon['oya_bumon_cd'] = $parBumon['bumon_cd'];
//            $dataNiteiCol = array();
            $cnt = $this->saveSekoKihon($db, $dataSekoKihon, $bumonCdChangeFlg, $dataNiteiCol, $dataSekoKeiyakusakiInfo);
            if ($bumonCdChangeFlg) {
                // 伝票の部門コードを更新する。
                $cnt += $this->updateDenpyoBumoncd($db, $denpyoNo, $dataSekoKihon['bumon_cd']);
            }
            // 伝票の担当コードを更新する
            $cnt += $this->updateDenpyoDantocd($db, $dataSekoKihon['seko_tanto_cd']);
//        }
        if (!$this->isMitsuKakutei()) { // 見積未確定
            // 受注明細の付帯値引きの再設定を行う
//            if ($changeFlg['gojokaiInfoChangeFlg'] || $changeFlg['gojokaiMemberChangeFlg']) {
            $dataApp = array('k_free5' => $dataSekoKihon['seko_check_kbn']);
            $msiData = $this->getJuchuMsiData();
            App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
            $cnt += $this->saveJuchu($db, $dataApp, $msiData);
//            }
        } else {
            // 施行金額確定されていたらスルー
            $status_kbn = $this->getSekoStatusKbn();
            if (isset($status_kbn) && $status_kbn < static::STATUS_KBN_SEKOKAKUTEI) {
                $dataApp = array();
                $delCol = array();
                $msiData = $this->getUriageMsiData();
                App_Utils2::setGojokaiNebiki($msiData, $this->_sekoNo);
                $cnt += $this->saveUriage($db, $dataApp, $msiData, $delCol);
            } else {
                if ($changeFlg['kihonChangeFlg']) {
                    $dataApp = array('k_free5' => $dataSekoKihon['seko_check_kbn']);
                    $delCol = array();
                    $msiData = array();
                    $cnt += $this->saveUriage($db, $dataApp, $msiData, $delCol);
                }
            }
        }

        // 各伝票請求先情報更新処理
        $cnt += $this->saveSekyuSakiInfo($db, $dataSekoKihon,$dataSekyuInfo);
        // 施行基本フリー保存処理 
        $dataSekoKihonFree = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonFreeJson'));
        $cnt += $this->saveKihonFree($db, $dataSekoKihonFree, $dataSekoKihon, $dataSekoKeiyakusakiInfo);

        $sbt = 4;
        if (!App_Utils::isHoujiInJuchu($controllerName)) {
            $sbt = 5;
        } else {
            // 担当者別施行情報を更新する
            App_Utils::saveTantoSekoInfo($this->_sekoNo, $this->_moushiKbn);
        }
        // 施行管理情報を更新する
        App_ClsSekoKanri::saveSekoKanriInfo($db, $this->_sekoNo, $this->_sekoNoSub, $sbt, 1);
        $denpyoNo = $this->getJuchudenpyoNo();

        // 2019/05/07 mihara keigen
        try {
            App_KeigenUtils::sougiYmdChgEnd($db, $this->_sekoNo, $denpyoNo); // sougi_ymd 変更時の調整
        } catch (Exception $e) {
            $data = array('status' => 'NG', 'msg' => $e->getMessage());
            Msi_Sys_Utils::outJson($data);
            return;
        }
        // 会員値引商品作成処理
        $juchuKaiinWari = new Juchu_JuchuKaiinwari();
        $cnt += $juchuKaiinWari->saveWaribikiData($db, $this->_sekoNo);
        // 商品部門チェック(見積式場が設定されているときのみ)
//        if (!$this->checkShohinBumon($db, $dataSekoKihon)) {
//            return;
//        }
        // 関連施行なしチェックを伝票ヘッダーに保存
        if ($changeFlg['kihonChangeFlg']) {
            $where = array();
            $upd = array('k_free5' => $dataSekoKihon['seko_check_kbn']);
            if ($this->isMitsuKakutei()) {
                $table = 'uriage_denpyo';
                $where['uri_den_no'] = $this->getUriagedenpyoNo();
            } else {
                $table = 'juchu_denpyo';
                $where['denpyo_no'] = $this->getJuchudenpyoNo();
            }
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL($table, $upd, $where);
            $db->easyExecute($sql, $param);
        }
        // 施行金額確定されていたらスルー
        if ($dataSekoKihon['status_kbn'] < static::STATUS_KBN_SEKOKAKUTEI) {
            $kojinInfo = DataMapper_SekoKojinInfoHouji::find($db, array('seko_no' => $this->_sekoNo));
            if (isset($kojinInfo[0]['hk_last_nm']) && isset($kojinInfo[0]['hk_first_nm'])) {
                $dataSekoKihon['k_nm'] = $kojinInfo[0]['hk_last_nm'].'　'.$kojinInfo[0]['hk_last_nm'];
            } else if (isset($kojinInfo[0]['hk_last_nm'])) {
                $dataSekoKihon['k_nm'] = $kojinInfo[0]['hk_last_nm'];
            } else if (isset($kojinInfo[0]['hk_first_nm'])) {
                $dataSekoKihon['k_nm'] = $kojinInfo[0]['hk_first_nm'];
            }
//            $reqData = $this->makeMocData($db, $dataSekoKihon, $dataGojokaiMemberCol,$dataGojokaiMemberDeleteCol);
//            if (count($reqData['ContractInfos']) > 0) {
//                $rtnData = Logic_Exkaiin_Moc2KaiinUpdSeko2::doExec($db, $reqData);
//                $msg = null;
//                $gojokaiCouseMst = array_values($this->getGojokaiCouseMst());
//                $taxInfoAll = App_ClsTaxLib::GetTaxInfoAll($db);
//                foreach ($rtnData['ContractInfos'] as $onerow) {
//                    if (isset($onerow['UpdateStatus']) && $onerow['UpdateStatus'] == '1') {
//                        $msg = '加入者番号(' . $onerow['ContractNo'] . ')' . $onerow['UpdateMessage'];
//                        continue;
//                    }
//                    $keyIndex = array_search($onerow['ContractNo'], array_column($dataGojokaiMemberCol, 'v_free17'));
//                    if (!is_int($keyIndex)) {
//                        continue;
//                    }
//                    // 試算結果に更新する
//                    $gojoKeyIndex = array_search($onerow['CourseName'], array_column($gojokaiCouseMst, 'gojokai_cose_iw'));
//                    $taxKeyIndex = array_search($onerow['ContractTaxRate'], array_column($taxInfoAll, 'zei_rtu'));
//                    $dataGojokaiMemberCol[$keyIndex]['course_snm_cd'] = $gojokaiCouseMst[$gojoKeyIndex]['gojokai_cose_iw'];
////                    $dataGojokaiMemberCol[$keyIndex]['zei_cd'] = $taxInfoAll[$taxKeyIndex]['zei_cd'];
//                    $dataGojokaiMemberCol[$keyIndex]['kanyu_nm'] = $onerow['CustomerName'];
//                    $dataGojokaiMemberCol[$keyIndex]['kanyu_dt'] = $onerow['ContractDate'];
//                    $dataGojokaiMemberCol[$keyIndex]['keiyaku_gaku'] = $onerow['TerminationValue'];
////                    $dataGojokaiMemberCol[$keyIndex]['harai_no'] = $onerow['TotalPayNum'];
////                $dataGojokaiMemberCol[$keyIndex]['harai_gaku'] = $onerow['TotalPayValue'] - $onerow['BonusAmount'] - $onerow['BalanceDiscountValue'] - $onerow['PrepaymentDiscountValue'];
////                    $dataGojokaiMemberCol[$keyIndex]['wari_gaku'] = $onerow['PrepaymentDiscountValue'];
////                    $dataGojokaiMemberCol[$keyIndex]['waribiki_gaku'] = $onerow['BalanceDiscountValue'];
////                    $dataGojokaiMemberCol[$keyIndex]['early_use_cost_disp'] = $onerow['EarlyUseCost'];
////                    $dataGojokaiMemberCol[$keyIndex]['meigi_chg_cost_disp'] = $onerow['RenameCommission'];
//                    $dataGojokaiMemberCol[$keyIndex]['cur_cd'] = $onerow['ContractCode'];
//                    $dataGojokaiMemberCol[$keyIndex]['v_free10'] = $onerow['ContractStatus'];
////                    $dataGojokaiMemberCol[$keyIndex]['kanyu_tax'] = $onerow['ContractTax'];
////                    $dataGojokaiMemberCol[$keyIndex]['n_free3'] = $onerow['BonusAmount'];
////                    $dataGojokaiMemberCol[$keyIndex]['n_free4'] = $onerow['PremiumMonths'];
//                }
//                if (isset($msg)) {
//                    $data = array('status' => 'NG', 'msg' => '異常終了しました: ' . $msg);
//                    Msi_Sys_Utils::outJson($data);
//                    return;
//                }
//                $Status = null;
//                if (array_key_exists('Result', $rtnData) && array_key_exists('Status', $rtnData['Result'])) {
//                    $Status = +$rtnData['Result']['Status'];
//                }
//                if ($Status == 1) { // 1:異常終了
//                    $msg = '異常終了しました: ' . $rtnData['Result']['Message'];
//                    $data = array('status' => 'NG', 'msg' => $msg);
//                    Msi_Sys_Utils::outJson($data);
//                    return;
//                }
//                // 互助会情報の再度保存処理を行う
//                $cnt += $this->saveGojokaiMember($db, $dataGojokaiMemberCol);
//            }
            // 各種集計テーブル作成、更新処理
            $cnt += Logic_SyukeiTblUpdate::SyukeiMain($db, $denpyoNo, '2', null, $this->_sekoNo);
        }
        $db->commit();

        // 施行基本情報を取得する
        $dataSekoKihonNew = $this->getSekoKihon();
        // 施行基本フリー情報を取得する
        $dataKihonFreeNew = $this->getKihonFree();
        // 故人情報を取得する
        $dataKojinInfoColNew = $this->getKojinInfo();
        // 施行日程情報を取得する
        $dataNiteiColNew = $this->getNitei();
        // 施行請求先情報を取得する
        $dataSekyuInfoNew = $this->getSekyuInfo();
        // 施行互助会情報を取得する
        $dataGojokaiInfoNew = $this->getGojokaiInfo();
        // 施行契約先情報を取得する
        $dataSekoKeiyakusakiInfoNew = $this->getKeiyakusakiInfo();
        // 施行互助会加入者を取得する
        $dataGojokaiMemberColNew = $this->getGojokaiMember(10);
        // 施行故人情報を取得する
        $dataKojinInfoColNew = $this->getKojinInfo();


        // サイドメニューデータを取得する
        if (isset($sidemenukey)) {
            Juchu_Utils::setSekoParam($this->_sekoNo, $this->_sekoNoSub);
            $sideMenuData = Juchu_Utils::getSideMenuData('customer', null, $controllerName, '2', $sidemenukey);
        } else if (App_Utils::isHoujiInJuchu($controllerName)) {
            $sideMenuData = Juchu_Utils::getSideMenuData($this->getCssClassName(), $this->_sekoNo, null, '2');
        } else {
            $sideMenuData = $this->getSideMenuData();
        }
        $data = array(
            'dataSekoKihon' => $dataSekoKihonNew,
            'dataSekoKihonFree' => $dataKihonFreeNew,
            'dataKojinInfoCol' => $dataKojinInfoColNew,
            'dataNiteiCol' => $dataNiteiColNew,
            'dataSekyuInfo' => $dataSekyuInfoNew,
            'dataGojokaiInfo' => $dataGojokaiInfoNew,
            'dataSekoKeiyakusakiInfo' => $dataSekoKeiyakusakiInfoNew,
            'dataGojokaiMemberCol' => $dataGojokaiMemberColNew,
            'dataKojinInfoCol' => $dataKojinInfoColNew,
            'dataSideMenu' => $sideMenuData,
            'status' => 'OK',
            'msg' => ('更新しました'),
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     * 施行日程保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/06/30
     * @param Msi_Sys_Db $db db
     * @param array $dataNiteiCol 画面日程タブデータ
     * @return int 更新件数
     */
    private function saveSekoNitei($db, $dataNiteiCol) {
        $cnt = 0;
        $msg = null;
        // 更新対象外項目設定
        $except = array();
        array_push($except, 'nitei_date');
        array_push($except, 'nitei_time');
        array_push($except, 'ts_based_nm');
        array_push($except, 'ts_based_nm2');
        array_push($except, 'nitei_ed_time');
        array_push($except, 'sikijo_check');
        array_push($except, 'sikijo_yoyaku_no');
        array_push($except, 'kaijo_bumon_cd');

        // 施行日程データがない場合、登録する
        // 施行日程がある場合、更新する
        $disp_no = 0;
        foreach ($dataNiteiCol as $niteiRow) {
            $start_ymd = null;
            $ed_ymd = null;
            $codeMst = DataMapper_CodeNmMst::find($db, array('code_kbn' => self::CODE_KBN_NITEI_HOUJI, 'kbn_value_cd' => $niteiRow['nitei_kbn']));
            // 式場予約があるものは入力時間が予約の範囲内かどうかチェックする
            if (isset($niteiRow['sikijo_yoyaku_no'])) {
                if (isset($niteiRow['nitei_time']) && strlen($niteiRow['nitei_time'])) {
                    $start_ymd = $niteiRow['nitei_ymd'];
                } else {
                    $msg .= $codeMst[0]['kbn_value_lnm']."の開始日時が未入力です。";
                }
                if ($niteiRow['nitei_kbn'] == self::NITEI_KBN_HOYO || $niteiRow['nitei_kbn'] == self::NITEI_KBN_HOEN) {
                    if (isset($niteiRow['nitei_ed_time']) && strlen($niteiRow['nitei_ed_time'])) {
                        $ed_ymd = $niteiRow['nitei_ed_ymd'];
                    } else {
                        $msg .= $codeMst[0]['kbn_value_lnm']."の終了日時が未入力です。";
                    }
                } else {
                    if (isset($niteiRow['nitei_time']) && strlen($niteiRow['nitei_time'])) {
                        $ed_ymd = $niteiRow['nitei_ymd'];
                    }
                }
                $yoyakuData = DataMapper_SisetsuYoyaku::find2($db, array('yoyaku_seko_no' => $this->_sekoNo, 'yoyaku_id' => $niteiRow['sikijo_yoyaku_no']));
                if (count($yoyakuData) == 0) {
                    $msg .= $codeMst[0]['kbn_value_lnm'] . "の式場予約のデータが存在しません。画面を更新してください。";
                } else {
                    // 時間入力がない場合はエラー
                    if (strlen($start_ymd) == 0 || strlen($ed_ymd) == 0) {
                        $msg .= $codeMst[0]['kbn_value_lnm']."の日時が施設予約の範囲外です(予約時間：".$yoyakuData[0]['start_ymdhm']."～".$yoyakuData[0]['end_ymdhm'].")。";
                    } else {
                        if ($start_ymd < $yoyakuData[0]['start_ymdhm'] || $yoyakuData[0]['end_ymdhm'] < $ed_ymd) {
                            $msg .= $codeMst[0]['kbn_value_lnm']."の日時が施設予約の範囲外です(予約時間：".$yoyakuData[0]['start_ymdhm']."～".$yoyakuData[0]['end_ymdhm'].")。";
                        }   
                    }
                }
            }
            if (empty($niteiRow['nitei_time'])) {
                $niteiRow['free3_kbn'] = 1;
            } else {
                $niteiRow['free3_kbn'] = null;
            }
            if (empty($niteiRow['nitei_ed_time'])) {
                $niteiRow['nitei_ed_ymd'] = null;
            }
            // 1:法要 2:墓参 3:会食
            $nitei_kbn = $niteiRow["nitei_kbn"];
            $niteiRow['v_free2'] = Msi_Sys_Utils::emptyToNull($niteiRow['sikijo_check']);
            // 受注伝票明細存在チェック
            $sqlSelectNitei = "
            SELECT
                sn.seko_no          -- 施行番号
                ,sn.nitei_kbn       -- 日程区分 
            FROM seko_nitei_houji sn
            WHERE sn.seko_no = :seko_no
                AND sn.nitei_kbn = :nitei_kbn
                AND sn.delete_flg = 0
            ORDER BY sn.nitei_kbn
                ";
            $selectNitei = $db->easySelect($sqlSelectNitei, array('seko_no' => $this->_sekoNo, 'nitei_kbn' => $nitei_kbn));
            if (count($selectNitei) === 0) {
                $disp_no++;
                $niteiRow['spot_code_kbn'] = self::CODE_HOUYO_BASHO_KBN; // 法要 
                $niteiRow['seko_no'] = $this->_sekoNo;
                $niteiRow['disp_no'] = $disp_no;
                // 施行日程登録SQL
                list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_nitei_houji", $niteiRow, $except);
            } else {
                array_push($except, 'seko_no');
                // 条件部
                $where['seko_no'] = $this->_sekoNo;  // 施行番号
                $where['nitei_kbn'] = $niteiRow['nitei_kbn'];  // 日程区分
                $where['delete_flg'] = 0;  // 削除フラグ
                // 施行基本更新SQL
                list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_nitei_houji", $niteiRow, $where, $except);
            }
            $cnt += $db->easyExecute($sql, $param);
        }
        return $msg;
    }

    /**
     * 施行基本保存処理 
     *
     * <AUTHOR> Sai
     * @since 2014/06/26
     * @param Msi_Sys_Db $db db
     * @param array $dataSekoKihonOrg 画面施行基本データ
     * @param boolean &$bumonCdChangeFlg 部門コード変更フラグ
     * @param array $dataNiteiCol 画面施行日程データ
     * @return int 更新件数
     */
    private function saveSekoKihon($db, $dataSekoKihon, &$bumonCdChangeFlg, $dataNiteiCol, $dataSekoKeiyakusakiInfo=array()) {
        // 施行基本存在チェック
        $selectSekoKihon = $this->selectSekoKihon();
        $tableSekokihon = "seko_kihon_info";
        // 更新対象外項目設定
        $except = array('status_kbn','consult_seko_no','hanso_seko_no','k_last_nm_readonly','k_first_nm_readonly','k_file'
            ,'mitsu_tanto_cd','mitsu_tanto_nm','uketuke_tanto_nm','seko_tanto_nm','seko_tanto_tel','k_wa_year','pacemaker_code_kbn','pacemaker_kbn'
            ,'cause_death','infection_code_kbn','infection_umu_kbn','infection_txt','m_sex_code_kbn','m_sex_kbn','careful_memo','m_file','m_wa_year'
            ,'kaishu_ymd','kaimyo_kbn','kaimyo_code_kbn','tera_shokai_kbn','tera_shokai_code_kbn','temple_person','temple_tel','temple_fax'
            ,'temple_yubin_no','temple_addr1','temple_addr2','kaiso_cnt','shinzoku_cnt','tuya_check','kaso_check','shinzoku_cnt','sd_yotei_date'
            ,'sd_yotei_time','copy_moto_seko_no','copy_saki_seko_no','seika_contact','form_tel','form_fax','login_bumon_cd','shikijo_bumon_cd'
            ,'est_oya_bumon_cd','m_mail_address','uchiawase_tanto_nm','after_tanto_nm','seko_check_kbn','tenpo_cd','nyudentenpo_cd','kaiin_sbt_code_cd'
            ,'kaiin_sbt_cd','after_tanto_nm','after_tanto_cd','temple_cd2','temple_nm2','temple2_yubin_no','temple2_addr1','temple2_addr2','temple2_tel'
            ,'ofuse','okuruma','irai_biko'
            );
        
        // emptyToNull
        $dataSekoKihon = Msi_Sys_Utils::emptyToNullArr($dataSekoKihon);
        // 保存先に合うよう代入
        App_Utils2::issetColumn($dataSekoKihon, 'kaiin_kbn','gojokai_kbn');
        App_Utils2::issetColumn($dataSekoKihon, 'k_nenrei_man','k_nenrei_kyounen');
        App_Utils2::issetColumn($dataSekoKihon, 'm_mail_address','v_free9');
        App_Utils2::issetColumn($dataSekoKihon, 'seko_tanto_tel','v_free7');
        App_Utils2::issetColumn($dataSekoKihon, 'm_sex_code_kbn','free6_code_cd');
        App_Utils2::issetColumn($dataSekoKihon, 'm_sex_kbn','free6_kbn');
        App_Utils2::issetColumn($dataSekoKihon, 'kaiso_cnt','n_free5');
        App_Utils2::issetColumn($dataSekoKihon, 'shinzoku_cnt','n_free4');
        // 故人名添付ファイル存在するときに登録する
        App_Utils2::issetColumn($dataSekoKihon, 'k_file','k_file_nm');
        // 喪主名添付ファイル存在するときに登録する
        App_Utils2::issetColumn($dataSekoKihon, 'm_file','m_file_nm');
        if (isset($dataSekoKihon['m_last_nm'], $dataSekoKihon['m_first_nm']) && strlen($dataSekoKihon['m_last_nm']) > 0 && strlen($dataSekoKihon['m_first_nm']) > 0) {
            $dataSekoKihon['m_nm'] = $dataSekoKihon['m_last_nm'] . '　' . $dataSekoKihon['m_first_nm'];
        } else if (isset($dataSekoKihon['m_last_nm']) && strlen($dataSekoKihon['m_last_nm']) > 0) {
            $dataSekoKihon['m_nm'] = $dataSekoKihon['m_last_nm'];
        } else if (isset($dataSekoKihon['m_first_nm']) && strlen($dataSekoKihon['m_first_nm']) > 0) {
            $dataSekoKihon['m_nm'] = $dataSekoKihon['m_first_nm'];
        } else {
            $dataSekoKihon['m_nm'] = null;
        }
        if (isset($dataSekoKihon['m_last_knm'], $dataSekoKihon['m_first_knm']) && strlen($dataSekoKihon['m_last_knm']) > 0 && strlen($dataSekoKihon['m_first_knm']) > 0) {
            $dataSekoKihon['m_knm'] = $dataSekoKihon['m_last_knm'] . '　' . $dataSekoKihon['m_first_knm'];
        } else if (isset($dataSekoKihon['m_last_knm']) && strlen($dataSekoKihon['m_last_knm']) > 0) {
            $dataSekoKihon['m_knm'] = $dataSekoKihon['m_last_knm'];
        } else if (isset($dataSekoKihon['m_first_knm']) && strlen($dataSekoKihon['m_first_knm']) > 0) {
            $dataSekoKihon['m_knm'] = $dataSekoKihon['m_first_knm'];
        } else {
            $dataSekoKihon['m_knm'] = null;
        }
        App_Utils2::issetColumn($dataSekoKihon, 'after_tanto_cd','tanto_cd2');
//        if (!isset($dataSekoKihon['est_shikijo_cd'])) {
//            $dataSekoKihon['est_shikijo_cd'] = static::DEF_EST_SHIKIJO_CD;
//        }
        // 契約団体
        $dataSekoKihon['bumon_cd1'] = $dataSekoKihon['nyudentenpo_cd']; // 入電店
        // 常に施行式場を設定して自営式場以外の場合は見積式場を設定する
        $keyIndex = array_search(self::NITEI_KBN_HOYO, array_column($dataNiteiCol, 'nitei_kbn'));
        if (isset($dataNiteiCol[$keyIndex])) {
            $hoyo = $dataNiteiCol[$keyIndex];
        }
        $est_shikijo_cd = null;
        $seko_shikijo_cd = null;
        if ($hoyo['sikijo_check'] == '1') {
            $bumonData = DataMapper_Bumon::findOne($db, array('bumon_cd' => $dataSekoKihon['bumon_cd']));
            if (Msi_Sys_Utils::myCount($bumonData) > 0) {
                $kaikeiBumon = DataMapper_Bumon::findOne($db, array('kaikei_bumon_cd' => $bumonData['kaikei_bumon_cd'], 'bumon_kbn' => 3));
                if (Msi_Sys_Utils::myCount($kaikeiBumon) > 0) {
                    $est_shikijo_cd = $kaikeiBumon['bumon_cd'];
                    $seko_shikijo_cd = $kaikeiBumon['bumon_cd'];
                }
            }
        } else {
            if (isset($hoyo['basho_kbn']) && $hoyo['basho_kbn'] == '2') {
                if (isset($hoyo['basho_cd'])) {
                    $kaijyoData = DataMapper_Kaijyo::find($db, array('kaijyo_cd' => $hoyo['basho_cd']));
                    $bumonData = DataMapper_Bumon::findOne($db, array('bumon_cd' => $dataSekoKihon['bumon_cd']));
                    if (Msi_Sys_Utils::myCount($bumonData) > 0) {
                        $kaikeiBumon = DataMapper_Bumon::find($db, array('kaikei_bumon_cd' => $bumonData['kaikei_bumon_cd'], 'bumon_kbn' => 2));
                        if (Msi_Sys_Utils::myCount($kaikeiBumon) > 0) {
                            if (Msi_Sys_Utils::myCount($kaikeiBumon) == 1) {
                                $est_shikijo_cd = $kaikeiBumon[0]['bumon_cd'];
                                $seko_shikijo_cd = $kaikeiBumon[0]['bumon_cd'];
                            } else {
                                $est_shikijo_cd = $kaijyoData[0]['bumon_cd'];
                                // 式場の親部門と請負部門の会計部門が同一ならば見積式場と施行式場は同一
                                $est_bumon = DataMapper_Bumon::find($db, array('bumon_cd' => $kaijyoData[0]['bumon_cd']));
                                $seko_bumon = DataMapper_Bumon::find($db, array('bumon_cd' => $dataSekoKihon['bumon_cd']));
                                if ($est_bumon[0]['kaikei_bumon_cd'] == $seko_bumon[0]['kaikei_bumon_cd']) {
                                    $seko_shikijo_cd = $kaijyoData[0]['bumon_cd'];
                                } else {
                                    // 請負部門の会計部門配下の共通部門を施行式場に設定する
                                    $bumonData = DataMapper_Bumon::find($db, array('kaikei_bumon_cd' => $seko_bumon[0]['kaikei_bumon_cd'], 'bumon_kbn' => 3));
                                    $seko_shikijo_cd = $bumonData[0]['bumon_cd'];
                                }
                            }
                        }
                    }
                }
            }
        }
        $dataSekoKihon['est_shikijo_cd'] = $est_shikijo_cd;
        $dataSekoKihon['seko_shikijo_cd'] = $seko_shikijo_cd;
        // 施行プランコードがなければNULL
        if (!isset($selectSekoKihon['seko_plan_cd'])) {
            $dataSekoKihon['plan_shikijo_cd'] = null;
        }
        if (Msi_Sys_Utils::myCount($selectSekoKihon) === 0) {
            $dataSekoKihon['seko_no'] = $this->_sekoNo;
            // 施行基本登録SQL
            list($sqlSekoKihon, $param) = DataMapper_Utils::makeInsertSQL($tableSekokihon, $dataSekoKihon, $except);
        } else {
            if ($selectSekoKihon['bumon_cd'] !== $dataSekoKihon['bumon_cd']) {
                $bumonCdChangeFlg = true;
            }
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $dataSekoKihon['seko_no'];  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 施行基本更新SQL
            list($sqlSekoKihon, $param) = DataMapper_Utils::makeUpdateSQL($tableSekokihon, $dataSekoKihon, $where, $except);
        }
        $cnt = $db->easyExecute($sqlSekoKihon, $param);
        return $cnt;
    }

    /**
     * データ種別を取得する
     * 1 => '法事'
     *
     * <AUTHOR> Sai
     * @since      2014/09/22
     * @return string 1:法事
     */
    public function getDataSbt($record) {
        $data_sbt = "1";
        if (isset($record) && isset($record['service_kbn'])) {
            if ($record['service_kbn'] == '4') {
                $data_sbt = '2';
            } else if ($record['service_kbn'] == '5') {
                $data_sbt = '4';
            }
        }
        return $data_sbt;
    }

    /**
     * 施行情報取得処理 
     *
     * <AUTHOR> Sai
     * @since 2016/07/22
     * @param array $req リクエスト
     */
    public function getSekoInfoData($req) {
        $dataSekoKihon = Msi_Sys_Utils::json_decode($req->getPost('dataSekoKihonJson'));
        list($sekodata, $seikyudata, $dataKojinInfoCol) = $this->getSekoCopyInfoData($dataSekoKihon['free2_cd']);
        $data = array(
            'sekodata' => $sekodata,
            'seikyudata' => $seikyudata,
            'dataKojinInfoCol' => $dataKojinInfoCol,
            'status' => 'OK',
            'msg' => ('情報をコピーしました'),
        );
        Msi_Sys_Utils::outJson($data);
    }

    /**
     *
     * 施行情報を取得する
     *
     * <AUTHOR> Sai
     * @since 2016/7/22
     * @return array 施行基本情報
     */
    private function getSekoCopyInfoData($sekoNo) {
        $db = Msi_Sys_DbManager::getMyDb();
        $gengo = DataMapper_EraMst::getCodeNmEra();
        $sql = "
        SELECT
            k.*
            ,TO_CHAR(sn.nitei_ymd, 'YYYY/MM/DD') AS k_death_ymd2
            ,TO_CHAR(k.k_death_ymd, 'YYYY/MM/DD') AS k_death_ymd3
            ,skf.zip_no2    AS temple_yubin_no  -- 寺院郵便番号
            ,skf.addr2_1    AS temple_addr1     -- 寺院住所1
            ,skf.addr2_2    AS temple_addr2     -- 寺院住所2
            ,skf.tel_no2    AS temple_tel      -- 寺院TEL
        FROM seko_kihon_info k
        LEFT OUTER JOIN seko_nitei sn
            ON k.seko_no = sn.seko_no
            AND sn.nitei_kbn = 1
            AND sn.delete_flg = 0
        LEFT JOIN seko_kihon_all_free skf
            ON skf.seko_no = k.seko_no
            AND skf.delete_flg = 0
        WHERE k.seko_no = :seko_no
            AND k.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $sekoNo));
        $sekokihon = Msi_Sys_Utils::remapArrayFlat($select, 'souke_nm souke_knm m_nm m_last_nm m_first_nm m_knm m_last_knm m_first_knm 
                                                            m_birth_year m_birth_month m_birth_day m_gengo m_seinengappi_ymd m_seinengappi_ymd_y m_nenrei_man 
                                                            mg_yubin_no mg_addr1 mg_addr2 mg_tel mg_m_tel m_cif_no m_cif_status
                                                            mk_kinmusaki_kbn mk_kinmusaki_nm mk_yakusyoku_nm mk_tel mk_fax m_zoku_cd m_zoku_kbn
                                                            syushi_cd syushi_kbn syuha_cd syuha_kbn syuha_nm syuha_knm jyusho_cd jyusho_nm jyusho_knm
                                                            sekyu_kbn m_zoku_cd2 m_zoku_kbn2 s_cif_no s_cif_status v_free9 v_free8
                                                            temple_yubin_no temple_addr1 temple_addr2 temple_tel'
                        , array('v_free9' => 'm_mail_address','v_free8' => 'biko1'));
        $sqlkojin = "
        SELECT
            *
            ,TO_CHAR(hk_death_ymd, 'YYYY/MM/DD') AS hk_death_ymd
        FROM
            seko_kojin_info_houji
        WHERE seko_no = :seko_no
            AND delete_flg = 0
                ";
        $selectkojin = $db->easySelect($sqlkojin, array('seko_no' => $sekoNo));

        // 故人情報設定(#1に設定)
        $dataKojinInfoCol = $this->getKojinInfo();
        if ($select['moushi_kbn'] == static::MOUSHI_KBN_HOUJI) {
            // 施行データから引き継ぐもの
            $dataKojinInfoCol[0]['seko_no'] = $this->_sekoNo;
            if (count($selectkojin) > 0 && strlen(($selectkojin[0]['hk_nm'])) > 0) {
                $dataKojinInfoCol[0]['hk_cif_no'] = $selectkojin[0]['hk_cif_no'];
                $dataKojinInfoCol[0]['hk_file_oid'] = $selectkojin[0]['hk_file_oid'];
                $dataKojinInfoCol[0]['hk_file_nm'] = $selectkojin[0]['hk_file_nm'];
                $dataKojinInfoCol[0]['sekohoyo_kbn'] = $selectkojin[0]['sekohoyo_kbn'];
                $dataKojinInfoCol[0]['hk_kaimyo'] = $selectkojin[0]['hk_kaimyo'];
                    if (isset($dataKojinInfoCol[0]['hk_birth_year'])) {
                        $keyIndex = array_search($selectkojin[0]['hk_birth_year'], array_column($gengo, 'kbn_value_cd_num'));
                        $result = $gengo[$keyIndex];
                        $dataKojinInfoCol[0]['hk_wa_year'] = $result['kbn_value_snm'];
                    }
                    if (isset($selectkojin[0]['hk_birth_year']) && isset($selectkojin[0]['hk_birth_month']) && isset($selectkojin[0]['hk_birth_day'])) {
                        $dataKojinInfoCol[0]['hk_seinengappi_ymd_y'] = $selectkojin[0]['hk_birth_year'] . '-' . $selectkojin[0]['hk_birth_month'] . '-' . $selectkojin[0]['hk_birth_day'];
                    }
                    $dataKojinInfoCol[0]['hk_last_nm'] = $selectkojin[0]['hk_last_nm'];
                    $dataKojinInfoCol[0]['hk_first_nm'] = $selectkojin[0]['hk_first_nm'];
                    $dataKojinInfoCol[0]['hk_last_knm'] = $selectkojin[0]['hk_last_knm'];
                    $dataKojinInfoCol[0]['hk_first_knm'] = $selectkojin[0]['hk_first_knm'];
                    $dataKojinInfoCol[0]['hk_death_ymd'] = $selectkojin[0]['hk_death_ymd'];
                    $dataKojinInfoCol[0]['hk_sex_kbn'] = $selectkojin[0]['hk_sex_kbn'];
                    $dataKojinInfoCol[0]['hk_gengo'] = $selectkojin[0]['hk_gengo'];
                    $dataKojinInfoCol[0]['hk_birth_year'] = $selectkojin[0]['hk_birth_year'];
                    $dataKojinInfoCol[0]['hk_birth_month'] = $selectkojin[0]['hk_birth_month'];
                    $dataKojinInfoCol[0]['hk_birth_day'] = $selectkojin[0]['hk_birth_day'];
                    $dataKojinInfoCol[0]['hk_seinengappi_ymd'] = $selectkojin[0]['hk_seinengappi_ymd'];
                    $dataKojinInfoCol[0]['hk_nenrei_man'] = $selectkojin[0]['hk_nenrei_man'];
                    $dataKojinInfoCol[0]['hk_file_oid'] = $selectkojin[0]['hk_file_oid'];
                    $dataKojinInfoCol[0]['hkg_yubin_no'] = $selectkojin[0]['hkg_yubin_no'];
                    $dataKojinInfoCol[0]['hkg_addr1'] = $selectkojin[0]['hkg_addr1'];
                    $dataKojinInfoCol[0]['hkg_addr2'] = $selectkojin[0]['hkg_addr2'];
                    $dataKojinInfoCol[0]['hkg_tel'] = $selectkojin[0]['hkg_tel'];
            }
            // 顧客Noがあれば顧客マスタから設定する
            if (isset($select['k_cif_no'])) {
                // 顧客マスタより設定を行う
                $kokyakuData = DataMapper_KaiinKokyaku_KokyakuMst::findOne($db, array('kokyaku_no' => $select['k_cif_no']));
                if (Msi_Sys_Utils::myCount($kokyakuData) > 0) {
                    $dataKojinInfoCol[0]['hk_last_nm'] = $kokyakuData['kokyaku_nm1'];
                    $dataKojinInfoCol[0]['hk_first_nm'] = $kokyakuData['kokyaku_nm2'];
                    $dataKojinInfoCol[0]['hk_last_knm'] = $kokyakuData['kokyaku_kana1'];
                    $dataKojinInfoCol[0]['hk_first_knm'] = $kokyakuData['kokyaku_kana2'];
                    $dataKojinInfoCol[0]['hk_sex_kbn'] = $kokyakuData['sex_kbn'];
                    if (isset($kokyakuData['seinengappi_ymd'])) {
                        $dataKojinInfoCol[0]['hk_seinengappi_ymd_y'] = $kokyakuData['seinengappi_ymd'];
                        list($year, $month, $day) = explode('/', $kokyakuData['seinengappi_ymd']);
                        $dataKojinInfoCol[0]['hk_birth_year'] = $year;
                        $dataKojinInfoCol[0]['hk_birth_month'] = $month;
                        $dataKojinInfoCol[0]['hk_birth_day'] = $day;
                        $keyIndex = array_search($year, array_column($gengo, 'kbn_value_cd_num'));
                        $result = $gengo[$keyIndex];
                        $dataKojinInfoCol[0]['hk_wa_year'] = $result['kbn_value_snm'];
                        $dataKojinInfoCol[0]['hk_gengo'] = $result['gengo_en'];
                        $dataKojinInfoCol[0]['hk_seinengappi_ymd'] = $result['wareki_year'].'/'.$month.'/'.$day;
                        if (isset($kokyakuData['shibo_ymd'])) {
                            $dataKojinInfoCol[0]['hk_death_ymd'] = $kokyakuData['shibo_ymd'];
                            $nenrei = App_DateCalc::GetNenreiCalc2($kokyakuData['shibo_ymd'], $kokyakuData['seinengappi_ymd']);
                            $dataKojinInfoCol[0]['hk_nenrei_man'] = $nenrei['man'];
                        }
                    }
                    $dataKojinInfoCol[0]['hkg_yubin_no'] = $kokyakuData['yubin_no'];
                    $dataKojinInfoCol[0]['hkg_addr1'] = $kokyakuData['addr1'];
                    $dataKojinInfoCol[0]['hkg_addr2'] = $kokyakuData['addr2'];
                    $dataKojinInfoCol[0]['hkg_tel'] = $kokyakuData['tel1'];  
                }
            }
        } else {
            // 施行データから引き継ぐもの
            $dataKojinInfoCol[0]['hk_cif_no'] = $select['k_cif_no'];
            $dataKojinInfoCol[0]['seko_no'] = $this->_sekoNo;
            $dataKojinInfoCol[0]['hk_file_oid'] = $select['k_file_nm'];
            if (isset($select['k_file_nm'])) {
                $dataKojinInfoCol[0]['hk_file_nm'] = '画像ファイル';
            }
            $dataKojinInfoCol[0]['hk_last_nm'] = $select['k_last_nm'];
            $dataKojinInfoCol[0]['hk_first_nm'] = $select['k_first_nm'];
            $dataKojinInfoCol[0]['hk_last_knm'] = $select['k_last_knm'];
            $dataKojinInfoCol[0]['hk_first_knm'] = $select['k_first_knm'];
            $dataKojinInfoCol[0]['hk_sex_kbn'] = $select['k_sex_kbn'];
            $dataKojinInfoCol[0]['hk_birth_year'] = $select['k_birth_year'];
            $dataKojinInfoCol[0]['hk_birth_month'] = $select['k_birth_month'];
            $dataKojinInfoCol[0]['hk_birth_day'] = $select['k_birth_day'];
            if (isset($select['k_birth_year'])) {
                $keyIndex = array_search($select['k_birth_year'], array_column($gengo, 'kbn_value_cd_num'));
                $result = $gengo[$keyIndex];
                $dataKojinInfoCol[0]['hk_wa_year'] = $result['kbn_value_snm'];
            }
            $dataKojinInfoCol[0]['hk_gengo'] = $select['k_gengo'];
            $dataKojinInfoCol[0]['hk_seinengappi_ymd'] = $select['k_seinengappi_ymd'];
            $dataKojinInfoCol[0]['hk_seinengappi_ymd_y'] = $select['k_seinengappi_ymd_y'];
            $dataKojinInfoCol[0]['hk_nenrei_man'] = $select['k_nenrei_man'];
            $dataKojinInfoCol[0]['hk_death_ymd'] = $select['k_death_ymd2'];
            $dataKojinInfoCol[0]['hkg_yubin_no'] = $select['kg_yubin_no'];
            $dataKojinInfoCol[0]['hkg_addr1'] = $select['kg_addr1'];
            $dataKojinInfoCol[0]['hkg_addr2'] = $select['kg_addr2'];
            $dataKojinInfoCol[0]['hkg_tel'] = $select['kg_tel'];
            // 顧客Noがあれば顧客マスタから設定する
            if (isset($select['k_cif_no'])) {
                // 顧客マスタより設定を行う
                $kokyakuData = DataMapper_KaiinKokyaku_KokyakuMst::findOne($db, array('kokyaku_no' => $select['k_cif_no']));
                if (Msi_Sys_Utils::myCount($kokyakuData) > 0) {
                    $dataKojinInfoCol[0]['hk_last_nm'] = $kokyakuData['kokyaku_nm1'];
                    $dataKojinInfoCol[0]['hk_first_nm'] = $kokyakuData['kokyaku_nm2'];
                    $dataKojinInfoCol[0]['hk_last_knm'] = $kokyakuData['kokyaku_kana1'];
                    $dataKojinInfoCol[0]['hk_first_knm'] = $kokyakuData['kokyaku_kana2'];
                    $dataKojinInfoCol[0]['hk_sex_kbn'] = $kokyakuData['sex_kbn'];
                    if (isset($kokyakuData['seinengappi_ymd'])) {
                        $dataKojinInfoCol[0]['hk_seinengappi_ymd_y'] = $kokyakuData['seinengappi_ymd'];
                        list($year, $month, $day) = explode('/', $kokyakuData['seinengappi_ymd']);
                        $dataKojinInfoCol[0]['hk_birth_year'] = $year;
                        $dataKojinInfoCol[0]['hk_birth_month'] = $month;
                        $dataKojinInfoCol[0]['hk_birth_day'] = $day;
                        $keyIndex = array_search($year, array_column($gengo, 'kbn_value_cd_num'));
                        $result = $gengo[$keyIndex];
                        $dataKojinInfoCol[0]['hk_wa_year'] = $result['kbn_value_snm'];
                        $dataKojinInfoCol[0]['hk_gengo'] = $result['gengo_en'];
                        $dataKojinInfoCol[0]['hk_seinengappi_ymd'] = $result['wareki_year'].'/'.$month.'/'.$day;
                        if (isset($kokyakuData['shibo_ymd'])) {
                            $dataKojinInfoCol[0]['hk_death_ymd'] = $kokyakuData['shibo_ymd'];
                            $nenrei = App_DateCalc::GetNenreiCalc2($kokyakuData['shibo_ymd'], $kokyakuData['seinengappi_ymd']);
                            $dataKojinInfoCol[0]['hk_nenrei_man'] = $nenrei['man'];
                        }
                    }
                    $dataKojinInfoCol[0]['hkg_yubin_no'] = $kokyakuData['yubin_no'];
                    $dataKojinInfoCol[0]['hkg_addr1'] = $kokyakuData['addr1'];
                    $dataKojinInfoCol[0]['hkg_addr2'] = $kokyakuData['addr2'];
                    $dataKojinInfoCol[0]['hkg_tel'] = $kokyakuData['tel1'];  
                }
            }
        }
        // 喪主情報で顧客Noがあれば顧客マスタから設定する
        if (isset($sekokihon['m_cif_no'])) {
            $kokyakuData = DataMapper_KaiinKokyaku_KokyakuMst::findOne($db, array('kokyaku_no' => $sekokihon['m_cif_no']));
            if (Msi_Sys_Utils::myCount($kokyakuData) > 0) {
                $sekokihon['m_last_nm'] = $kokyakuData['kokyaku_nm1'];
                $sekokihon['m_first_nm'] = $kokyakuData['kokyaku_nm2'];
                $sekokihon['m_last_knm'] = $kokyakuData['kokyaku_kana1'];
                $sekokihon['m_first_knm'] = $kokyakuData['kokyaku_kana2'];
                $sekokihon['m_sex_kbn'] = $kokyakuData['sex_kbn'];
                if (isset($kokyakuData['seinengappi_ymd'])) {
                    $sekokihon['m_seinengappi_ymd_y'] = $kokyakuData['seinengappi_ymd'];
                    list($year, $month, $day) = explode('/', $kokyakuData['seinengappi_ymd']);
                    $sekokihon['m_birth_year'] = $year;
                    $sekokihon['m_birth_month'] = $month;
                    $sekokihon['m_birth_day'] = $day;
                    $keyIndex = array_search($year, array_column($gengo, 'kbn_value_cd_num'));
                    $result = $gengo[$keyIndex];
                    $sekokihon['m_wa_year'] = $result['kbn_value_snm'];
                    $sekokihon['m_gengo'] = $result['gengo_en'];
                    $sekokihon['m_seinengappi_ymd'] = $result['wareki_year'] . '/' . $month . '/' . $day;
                    $nenrei = App_DateCalc::GetNenreiCalc2(Msi_Sys_Utils::getDate(null, 'Y/m/d'), $kokyakuData['seinengappi_ymd']);
                    $sekokihon['m_nenrei_man'] = $nenrei['man'];
                }
                $sekokihon['mg_yubin_no'] = $kokyakuData['yubin_no'];
                $sekokihon['mg_addr1'] = $kokyakuData['addr1'];
                $sekokihon['mg_addr2'] = $kokyakuData['addr2'];
                $sekokihon['mg_tel'] = $kokyakuData['tel1'];
                $sekokihon['mg_m_tel'] = $kokyakuData['tel2'];
                $sekokihon['m_mail_address'] = $kokyakuData['e_mail'];
            }
        }

        $sql2 = "
        SELECT s.*
        ,koza.bank_nm || ' ' || koza.shiten_nm || ' ' ||
                            (CASE WHEN koza.yokin_sbt=0 THEN '普' WHEN koza.yokin_sbt=1 THEN '当座' ELSE null END) || koza.st_br_koza_no AS sekyu_bank_nm
        FROM sekyu_saki_info s
        LEFT JOIN br_koza_kanri_mst koza
        ON koza.transfer_bank_cd = s.v_free3
        WHERE s.seko_no = :seko_no
            AND s.sekyu_cd = :sekyu_cd
            AND s.delete_flg = 0
                ";
        $select2 = $db->easySelOne($sql2, array('seko_no' => $sekoNo, 'sekyu_cd' => $select['sekyu_cd']));
        $seikyu = Msi_Sys_Utils::remapArrayFlat($select2, 'sekyu_nm sekyu_last_nm sekyu_first_nm sekyu_knm sekyu_last_knm sekyu_first_knm
                                                            moshu_kankei_kbn moshu_kankei yubin_no addr1 addr2 tel mobile_tel biko1
                                                            sekyu_soufu_nm soufu_last_nm soufu_first_nm sekyu_soufu_knm
                                                            soufu_last_knm soufu_first_knm soufu_yubin_no soufu_addr1 soufu_addr2
                                                            soufu_tel soufu_file_nm soufu_file
                                                            ryosyu_meigi ryosyu_soufu_nm ryosyu_soufu_last_nm ryosyu_soufu_first_nm
                                                            ryosyu_soufu_knm ryosyu_soufu_last_knm ryosyu_soufu_first_knm
                                                            ryosyu_soufu_yubin_no ryosyu_soufu_addr1 ryosyu_soufu_addr2
                                                            ryosyu_soufu_tel ryosyu_soufu_file_nm ryosyu_soufu_file v_free3'
                        , array('moshu_kankei_kbn' => 'sekyu_moshu_kankei_kbn', 'moshu_kankei' => 'sekyu_moshu_kankei', 'yubin_no' => 'sekyu_yubin_no'
                    , 'addr1' => 'sekyu_addr1', 'addr2' => 'sekyu_addr2', 'tel' => 'sekyu_tel', 'biko1' => 'sekyu_biko1','v_free3' => 'sekyu_bank_no'));
        // 請求先情報で顧客Noがあれば顧客マスタから設定する
        if (isset($sekokihon['s_cif_no'])) {
            $kokyakuData = DataMapper_KaiinKokyaku_KokyakuMst::findOne($db, array('kokyaku_no' => $sekokihon['s_cif_no']));
            if (Msi_Sys_Utils::myCount($kokyakuData) > 0) {
                $seikyu['sekyu_last_nm'] = $kokyakuData['kokyaku_nm1'];
                $seikyu['sekyu_first_nm'] = $kokyakuData['kokyaku_nm2'];
                $seikyu['sekyu_last_knm'] = $kokyakuData['kokyaku_kana1'];
                $seikyu['sekyu_first_knm'] = $kokyakuData['kokyaku_kana2'];
                $seikyu['sekyu_yubin_no'] = $kokyakuData['yubin_no'];
                $seikyu['sekyu_addr1'] = $kokyakuData['addr1'];
                $seikyu['sekyu_addr2'] = $kokyakuData['addr2'];
                $seikyu['sekyu_tel'] = $kokyakuData['tel1'];
                $seikyu['mobile_tel'] = $kokyakuData['tel2'];
                if (isset($kokyakuData['transfer_bank_cd'])) {
                    $seikyu['sekyu_bank_no'] = $kokyakuData['transfer_bank_cd'];
                    $sql = "
                    SELECT 
                         koza.transfer_bank_cd
                        ,koza.bank_nm || ' ' || koza.shiten_nm || ' ' ||
                            (CASE WHEN koza.yokin_sbt=0 THEN '普' WHEN koza.yokin_sbt=1 THEN '当座' ELSE null END) || koza.st_br_koza_no AS transfer_bank_info
                    FROM br_koza_kanri_mst koza
                    WHERE koza.transfer_bank_cd = :transfer_bank_cd
                    ";
                    $select = $db->easySelOne($sql,array('transfer_bank_cd' => $kokyakuData['transfer_bank_cd']));
                    if (Msi_Sys_Utils::myCount($select) > 0) {
                        $seikyu['sekyu_bank_nm'] = $select['transfer_bank_info'];
                    }
                }
            }
        }
        return array($sekokihon, $seikyu, $dataKojinInfoCol);
    }

    /**
     *
     * 施行基本フリーを取得する
     *
     * <AUTHOR> Sai
     * @since 2015/08/03
     * @return array 施行基本フリーデータ
     */
    private function getKihonFree() {
        $db = Msi_Sys_DbManager::getMyDb();
        $sql = "
        SELECT
            skf.seko_no                     -- 施行番号
            ,TO_CHAR(skf.ts_free1 ,'YYYY/MM/DD HH24:MI') AS ts_free1     -- 受付日
            ,TO_CHAR(skf.ts_free1 ,'YYYY/MM/DD') AS ts_free1_date        -- 受付日日付のみ
            ,TO_CHAR(skf.ts_free1 ,'HH24:MI') AS ts_free1_time           -- 受付日時刻のみ
            ,skf.tanto_cd1                  -- 見積担当者コード
            ,skf.tanto_nm1                  -- 見積担当者名
            ,skf.free11_code_cd AS temple_cd2   -- 紹介寺院CD
            ,skf.v_free24       AS temple_nm2   -- 紹介寺院名
            ,skf.n_free1        AS ofuse        -- お布施
            ,skf.n_free2        AS okuruma      -- お車代
            ,skf.biko1          AS irai_biko    -- 依頼書備考
        FROM seko_kihon_all_free skf
        WHERE skf.seko_no = :seko_no
            AND skf.delete_flg = 0
                ";
        $select = $db->easySelOne($sql, array('seko_no' => $this->_sekoNo));
        return $select;
    }

    /**
     * 施行基本フリー保存処理
     *
     * <AUTHOR> Sai
     * @since 2016/07/22
     * @param Msi_Sys_Db $db db
     * @param array $dataKihonFree 施行基本フリーデータ
     * @return int 更新件数
     */
    private function saveKihonFree($db, $dataKihonFree, $dataSekoKihon, $dataSekoKeiyakusakiInfo = array()) {
        $dataKihonFree = Msi_Sys_Utils::emptyToNullArr($dataKihonFree);
        $dataKihonFree['tanto_cd1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['mitsu_tanto_cd']);
        $dataKihonFree['tanto_nm1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['mitsu_tanto_nm']);
        // 会員種別
        $dataKihonFree['free8_code_cd'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['kaiin_sbt_code_cd']);
        $dataKihonFree['free8_kbn'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['kaiin_sbt_cd']);
        $dataKihonFree['zip_no2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_yubin_no']);
        $dataKihonFree['addr2_1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_addr1']);
        $dataKihonFree['addr2_2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_addr2']);
        $dataKihonFree['tel_no2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_tel']);
        $dataKihonFree['free11_code_cd'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_cd2']);
        $dataKihonFree['v_free24'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['temple_nm2']);
        $dataKihonFree['n_free1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['ofuse']);
        $dataKihonFree['n_free2'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['okuruma']);
        $dataKihonFree['biko1'] = Msi_Sys_Utils::emptyToNull($dataSekoKihon['irai_biko']);
        $except = array('ts_free1_date','ts_free1_time', 'temple_cd2','temple_nm2','temple2_yubin_no','temple2_addr1','temple2_addr2','temple2_tel'
            ,'ofuse','okuruma','irai_biko'
            );
        // 存在チェック
        $select = $this->getKihonFree();
        if (Msi_Sys_Utils::myCount($select) === 0) {
            $dataKihonFree['seko_no'] = $this->_sekoNo;
            // 登録SQL
            list($sql, $param) = DataMapper_Utils::makeInsertSQL("seko_kihon_all_free", $dataKihonFree, $except);
        } else {
            array_push($except, 'seko_no');
            // 条件部
            $where['seko_no'] = $this->_sekoNo;  // 施行番号
            $where['delete_flg'] = 0;  // 削除フラグ
            // 更新SQL
            list($sql, $param) = DataMapper_Utils::makeUpdateSQL("seko_kihon_all_free", $dataKihonFree, $where, $except);
        }
        $cnt = $db->easyExecute($sql, $param);
        return $cnt;
    }

    /**
     * 
     * 施行の現在情報取得処理 
     *
     * <AUTHOR> Tosaka
     * @since  2020/06/24
     * @param array $dataApp 画面データ
     */
    public function getCurData($dataApp) {
        // 施行基本情報を設定する
        $this->_sekoNo = $dataApp['seko_no'];
        $this->setInitParam();
        $dataSekoKihon = $this->getSekoKihon();
        $niteiCol = $this->getNitei($dataSekoKihon);
        $data['dataSekoKihon'] = $dataSekoKihon;
        $data['dataNiteiCol'] = $niteiCol;
        return $data;
    }

}
