// BR入金処理
var appunmatch = appunmatch || {};
$(function () {
    "use strict";
    var utils = window.msiBbUtils;

    // 画面クラスとモデルのプロパティのオブジェクト 
    appunmatch.pro = {
    };

    // select2のvalを設定する
    var _setSelect2Val = function ($el, val) {
        $el.select2("val", val);
    };
    
    // 全体ビュー
    var AppView = Backbone.View.extend({
        el: $("#mst-form-id"),
        events: {
            "click #btn_search": "doSearch",
            "click #btn_search_prev": "doPrevSearch",
            "click #btn_search_next": "doNextSearch",
            "click #btn_save": "doSave",
            "click #btn_match_cancel": "doCancel",
            "click #btn_seikyu_multi": 'seikyuHelperMulti',
        },
        bindings: {
            '#s_kaisya_cd': {// 会社
                observe: 's_kaisya_cd',
                events: ['change'],
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
            '#s_torikomi_ymd': 's_torikomi_ymd', // 取込日
            '#s_keshikomi_kbn': {// 消込区分
                observe: 's_keshikomi_kbn',
                events: ['change'],
                getVal: utils.getValSel2,
                update: utils.updateSel2
            },
        },
        initialize: function () {
            this.listenTo(appunmatch.paymentCol, 'reset', this.addAllColPayment);
            this.listenTo(appunmatch.seikyuCol, 'reset', this.addAllColSeikyu);
            this.render();
            this.setSelect2();
        },
        addOnePayment: function (m) {
            var v = new PaymentView({model: m});
            this.$("#t-dtl-payment").append(v.render().el);
        },
        addOneSeikyu: function (m) {
            var jiyaV = new SeikyuView({model: m});
            this.$("#t-dtl-seikyu").append(jiyaV.render().el);
        },
        addAllColPayment: function (collection) {
            var $memberDtl = this.$("#t-dtl-payment");
            $memberDtl.find('tbody').remove();
            collection.each(this.addOnePayment, this);
        },
        addAllColSeikyu: function (collection) {
            var $memberDtl = this.$("#t-dtl-seikyu");
            $memberDtl.find('tbody').remove();
            collection.each(this.addOneSeikyu, this);
        },
        isInputOk: function () {
            $.msiJqlib.clearAlert();
            this.clearErr();
            var aMsg = [], line;
            // 明細コレクションチェック            
            appunmatch.paymentCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    line = i + 1;
                    _.each(resLine, function (v, k) {
                        aMsg.push(line + '行目 ' + v);
                    });
                }
            });
            // 明細コレクションチェック            
            appunmatch.seikyuCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    line = i + 1;
                    _.each(resLine, function (v, k) {
                        aMsg.push(line + '行目 ' + v);
                    });
                }
            });
            // NG
            if (aMsg.length > 0) {
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }
            return true;
        },
        //エラークラスクリア
        clearErr: function () {
            this.$el.msiErrClearAll({errCls: 'error1'});
        },
        // 保存
        doSave: function () {
            //エラーチェック
            if (!this.isInputOk()) {
                return;
            }
            
            var that = this;
            
            // 振込入金ファイル取込明細
            var paymentColJson = '';
            var paymentDataCol = [];
            var diff_prc = 0;
            var sekyu_select_cnt = 0;
            var keshi_cnt = 0;
            var delete_cnt = 0;
            var keshikomi_kbn = this.model.get('s_keshikomi_kbn');
            appunmatch.paymentCol.some(function (m) {
                if (m.get('selected') == 1) {
                    diff_prc = m.get('diff_prc');
                    paymentDataCol.push(m);
                    // 消込にチェックあればカウント
                    if (m.get('keshi_check') === '1') {
                        keshi_cnt++;
                    }
                    // 削除にチェックあればカウント
                    if (m.get('delete_chk')) {
                        delete_cnt++;
                    }
                    return true;
                }
            });
            appunmatch.seikyuCol.some(function (m) {
                if (m.get('selected') == 1) {
                    sekyu_select_cnt++;
                }
            });
            // 消込にチェックがありかつ請求データも選択されている場合はエラーを返す
            if (keshi_cnt > 0 && sekyu_select_cnt > 0) {
                $.msiJqlib.showErr('消込にチェックがあり、請求データが選択されている状態です。');
                return;
            }
            // 未選択はエラー(消込区分が削除の場合は対象外)
            if (keshikomi_kbn != '9' && keshi_cnt == 0 && delete_cnt == 0 && sekyu_select_cnt== 0) {
                $.msiJqlib.showErr('消込対象が選択されていません。');
                return;
            }
            if(diff_prc > 0){
                if (!confirm('入金データに差額があります.このまま保存してよろしいですか？')) {
                    return;
                }
            } else if (diff_prc < 0) {
                $.msiJqlib.showErr('差額がマイナスのため保存できません。');
                return;
            }
            
            paymentColJson = JSON.stringify(paymentDataCol);
            
            // 請求伝票
            var seikyuColJson = '';
            var seikyuDataCol = [];
            appunmatch.seikyuCol.each(function (m) {
                if (m.get('selected') == 1) {
                    seikyuDataCol.push(m);
                }
            });
            seikyuColJson = JSON.stringify(seikyuDataCol);
            
            //データ保存
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/unmatchkeshikomi/save',
                data: {
                    paymentColJson: paymentColJson,
                    seikyuColJson: seikyuColJson,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        appunmatch.reset2(mydata);
                        $.msiJqlib.showInfo(mydata.msg);
                        that.doSearch();
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        // マッチ解除
        doCancel: function () {
            //エラーチェック
            if (!this.isInputOk()) {
                return;
            }
            
            var that = this;
            
            // 振込入金ファイル取込明細
            var paymentColJson = '';
            var paymentDataCol = [];
            appunmatch.paymentCol.each(function (m) {
                if (m.get('selected') == 1) {
                    paymentDataCol.push(m);
                }
            });
            paymentColJson = JSON.stringify(paymentDataCol);
            
            //データ保存
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/unmatchkeshikomi/cancel',
                data: {
                    paymentColJson: paymentColJson,
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        appunmatch.reset2(mydata);
                        $.msiJqlib.showInfo(mydata.msg);
                        that.doSearch();
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        // 新規合算押下処理
        seikyuHelperMulti: function () {
            
            // 既に選択済の請求書は再選択させない
            var selectedSeikyuNo = [];
            var selectCnt = 0;
            $.each( appunmatch.seikyuCol.models, function(i){
                var sn = this.get('seikyu_den_no');
                var selected = this.get('selected');
                if( selectedSeikyuNo.indexOf(sn) < 0 ){ // 重複を排除
                    selectedSeikyuNo.push(sn);
                }
                if(selected == '1'){
                    selectCnt++;
                }
            });
            if (selectCnt != 0) {
                $.msiJqlib.showErr('選択されている行があります。解除してから検索をしてください。');
                return false;
            }
            
            $.msiJqlib.clearAlert();
            var t = this;
            this.$el.msiPickHelper({
                action: 'saiken.seikyuinfo.multi',
                onSelect: function (data) {
                    t.seiMsiData(data);
                },
                onClear: function () {
                },
                hookSetData: function () {
                    return {init_search: 1
                          , no_cond: 0
                          , s_bumon_cd: t.model.get("search_bumon_cd")
                          , s_seikyu_kbn: '4'
                          , not_data_kbns:"'21'"
                          , bun_gas_kbns:"'0','2','20'"
                          , s_multi_sel_kbn: '1'
                          , s_exclude_sk: selectedSeikyuNo}
                }
            });
        },
        seiMsiData: function (data) {
            var dataJson = JSON.stringify(data);
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/unmatchkeshikomi/sekyumsimulti',
                data: {
                    dataJson: dataJson
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        zanNyukinFlg = 0;
                        zanNyukinPrc = 0;
                        
                        $.each( mydata.dataSeikyu, function( i, m ){
                            var model = new SeikyuModel();
                            model.set( {	
                                seikyu_kbn: m.seikyu_kbn,          // 請区
                                seko_no: m.seko_no,                // 施行No
                                seko_no_sub: m.seko_no_sub,        // 施行No（枝番）
                                seikyu_den_no: m.seikyu_den_no,    // 請求番号
                                kaisya_cd: m.kaisya_cd,            // 会社コード
                                bumon_cd: m.bumon_cd,              // 部門コード
                                seikyu_ymd: m.seikyu_ymd,          // 請求日
                                br_koza_no: m.br_koza_no,          // 口座番号
                                sekyu_knm: m.sekyu_knm,            // 請求先名
                                sekyu_tel: m.sekyu_tel,            // 電話番号
                                seikyu_zei_prc: m.seikyu_zei_prc,        // 請求金額
                                nyukin_prc: m.nyukin_prc,          // 入金済金額
                                this_nyukin_prc: null,             // 今回入金額
                                seikyu_zan: m.seikyu_zan,          // 請求残高
                                seikyu_zan_org: m.seikyu_zan,          // 請求残高
                                history_no: m.history_no,          // 取込履歴番号
                                data_kbn: m.data_kbn,              // データ区分
                             } );
                             that.addRow(model);
                         });
                        
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
            });
        },
        // 請求行追加処理
        addRow: function( m ) {        
            appunmatch.seikyuCol.push(m);
            var v = new SeikyuView({model: m, collection: appunmatch.seikyuCol});
            this.$("#t-dtl-seikyu").append(v.render().el); // DOM追加
            this.resetLineNo();
        },
        // 行番号採番処理
        resetLineNo: function() {
            var nowModel = null, col = appunmatch.seikyuCol;
            if( !( 'models' in col ) ){
                return ;
            }
            for(var i=0, aLen=col.models.length; i < aLen ; i++){
                nowModel = col.models[i];
                nowModel.set('row', i+1 );
            }
        },
        // 検索実行(前頁)
        doPrevSearch: function(e) {
            appunmatch.offset -= 100;
            this.searchFunc(e, appunmatch.offset);
        },
        // 検索実行(次頁)
        doNextSearch: function(e) {
            appunmatch.offset += 100;
            this.searchFunc(e, appunmatch.offset);
        },
        // 検索実行
        doSearch: function (e) {
            appunmatch.offset = 0;
            this.searchFunc(e, appunmatch.offset);
        },
        // 検索処理
        searchFunc: function (e, offset) {
            
            $.msiJqlib.clearAlert();
            this.clearErr();
            if ($.msiJqlib.isNullEx2(this.model.get('s_kaisya_cd'))) {
                $.msiJqlib.showErr('部門を選択してください。');
                return;
            }
            this.model.set('search_bumon_cd',this.model.get('s_kaisya_cd'));
            if (offset === undefined)
                offset = 0;
            // 次ボタン等クリア
            $('#btn_search_next').removeAttr('data-offset').css('visibility', 'hidden');
            $('#btn_search_prev').removeAttr('data-offset').css('visibility', 'hidden');

            var bbm = this.model;
            $.ajax( {
                url: $.msiJqlib.baseUrl() + '/saiken/unmatchkeshikomi/search',
                type: 'GET',
                data: {
                    s_kaisya_cd    : bbm.get('search_bumon_cd'),
                    s_torikomi_ymd : bbm.get('s_torikomi_ymd'),
                    s_keshikomi_kbn: bbm.get('s_keshikomi_kbn'),
                    offset: offset,
                },
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        appunmatch.reset(mydata);
                        zanNyukinFlg = 0;
                        
                        var cleardata  = {'dataSeikyu':null};
                        appunmatch.reset2(cleardata);
                        
                        if ( appunmatch.offset > 0){
                            $('#btn_search_prev').css('visibility','visible'); // show();
                        }
                        if (mydata.nextcount > 0){
                            $('#btn_search_next').css('visibility','visible'); // show();
                        }
                        
                        paymentChgFlg = 0;// 入金データ変更フラグ 0:変更無
                        $('#btn_seikyu_multi').removeAttr('data-offset').css('visibility', 'hidden');
                        $('#order .items .list').scrollTop(0);
                        
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
                // error処理は共通設定を使う
            } );
        },
        // 他データ参照
        _showNew: function(path) {
            var url = $.msiJqlib.baseUrl() + path;
            // location.href = url;
            // window.open( url, '_blank' );
            var that = this;
            var refreshFunc = function() { console.log('refresh searching...');
                                           that.doSearch();
                                         };
            msiLib2.openWinSub( refreshFunc, url );
        },
        render: function () {
            $.msiJqlib.setSelect2Com1($("#s_kaisya_cd"), ($.extend({data: data.bumon_cd}, {allowClear: true})));
            $.msiJqlib.setSelect2Com1($("#s_keshikomi_kbn"), ($.extend({data: data.keshikomi_kbn}, {allowClear: true})));
            $('#s_keshikomi_kbn').val(0).trigger('change');
            this.stickit();
        },
        // select2設定処理
        setSelect2: function () {
        },
    });
    // 全体モデル    
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                s_kaisya_cd    : null, // 会社
                search_bumon_cd    : null, // 検索ボタン押下時に保持する部門コード
                s_torikomi_ymd : null, // 取込日
                fee_kbn : null, // 手数料差異(コード名称)
                s_keshikomi_kbn: 0,    // 消込区分
            };
        },
        validation: {
            s_kaisya_cd: {// 会社
                required: true
            },
            s_torikomi_ymd: {// 取込日
                required: true
            },
            s_keshikomi_kbn: {// 消込区分
                required: true
            },
        },
        labels: {
            s_kaisya_cd    : '会社',
            s_torikomi_ymd : '取込日',
            s_keshikomi_kbn: '消込区分',
        }
    });

    // 明細モデル (Payment)
    var PaymentModel = Backbone.Model.extend({
        defaults: function () {
            return {
                row:null,
                kaisya_cd:null,
                nyukin_houhou: null,     // 入金方法
                kaisya_nm: null,         // 会社名
                kanjyo_date: null,       // 入金日
                kouza_no: null,          // 口座番号
                furikomi_nm: null,       // 振込依頼人名
                nyukin_prc: null,        // 入金額
                furikomi_chg_cost: null, // 振込手数料
                kariuke_prc: null,       // 仮受金
                diff_prc: 0,             // 差額
                diff_prc_org: 0,         // 差額(DB取得値)
                totalPage: 0,            // 総頁数
                offset: 0,               // ページ切替用
                pattern:null,            // チェックボックスのパターン
                keshi_check:'0',       // 消込チェック時の値
                mod_cnt:null
            };
        },
        validation: {
        },
        labels: {
        }
    });
    // コレクション  (Payment)   
    var paymentCollection = Backbone.Collection.extend({
        model: PaymentModel
    });
    // ビュー     (Payment)
    var PaymentView = Backbone.View.extend({
        tagName: 'tbody',
        tmpl: _.template($('#payment_tmpl').html()),
        events: {
            "click tr": "setSelected",
            "click .keshikomi_chk": "doKeshikomiChk",
            "click .delete_chk": "doDeleteChk",
        },
        bindings: {
                '.row': 'row',
                '.nyukin_houhou': 'nyukin_houhou',         // 入金方法
                '.kaisya_nm': 'kaisya_nm',                  // 会社名
                '.kanjyo_date': 'kanjyo_date',             // 入金日
                '.kouza_no': 'kouza_no',                   // 口座番号
                '.furikomi_nm': 'furikomi_nm',             // 振込依頼人名
                '.nyukin_prc': {                          // 入金額
                    observe: 'nyukin_prc',
                    events: ['change'],
                    onSet: utils.commaOmit,
                    onGet: utils.commaAdd
                },
                '.keshikomi_chk': 'keshikomi_chk',         // 消込
                '.furikomi_chg_cost': {                  // 振込手数料
                    observe: 'furikomi_chg_cost',
                    events: ['change'],
                    onSet: utils.commaOmit,
                    onGet: utils.commaAdd
                },
                '.kariuke_prc': {                         // 仮受金
                    observe: 'kariuke_prc',
                    events: ['change'],
                    onSet: utils.commaOmit,
                    onGet: utils.commaAdd
                },
                '.diff_prc': {                            // 差額
                    observe: 'diff_prc',
                    events: ['change'],
                    onSet: utils.commaOmit,
                    onGet: utils.commaAdd
                },
                '.delete_chk': 'delete_chk',               // 削除
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback({row: '.row'}, "error1"));
            this.listenTo(this.model, 'change', function () {
                $.msiJqlib.setBtnEnable($("#btn_save"));
                $.msiJqlib.setBtnEnable($("#btn_koshin")); //更新ボタン有効にする
            });
            this.listenTo(this.model, 'destroy', this.remove);
            this.listenTo(this.model, 'change:pattern', this.patternChkBox);
        },
        render: function () {
            //テンプレートから明細部分のHTML作成
            this.$el.html(this.tmpl({idx: this.model.cid}));
            this.stickit();
            // 赤字クラス切り替え処理
            this.toggleClass();
            // チェックボックス切り替え処理
            this.patternChkBox();
            return this;
        },
        // マイナスのときに赤字クラスを追加する処理
        toggleClass: function (e) {
            appunmatch.toggleAkajiClass(this, ['nyukin_prc']);
            appunmatch.toggleAkajiClass(this, ['sagaku_jiysa']);
        },
        setSelected: function (e) {
            
            // 該当項目のカーソル時のクリックは処理しない
            var className = e.target.className;
            var target = className.match('keshikomi_chk|delete_chk');
            var keshikomi_kbn = this.model.get('payment_kbn');
            if (!$.msiJqlib.isNullEx2(target)) {
                return;
            }
            
            // 入金データが編集されている場合、
            // 別行や同じ行を再クリックする場合はアラートを出す(同じ行でも再検索しているため)
            if(keshikomi_kbn != '9' && paymentChgFlg == 1){
                if (!confirm('データが変更されています。 \nこのデータを保存せず、行を選択しなおしてよろしいですか？')) {
                        return;
                }
                paymentChgFlg = 0;// 入金データ変更フラグ 0:変更無
            }
            
            // 選択行解除
            $('tr').removeClass('row-selected');
            zanNyukinPrc = 0;
            var cleardata  = {'dataSeikyu':null};
            appunmatch.reset2(cleardata);
            appunmatch.paymentCol.each(function (m, i) {
                m.set('selected', '0');
                m.set('keshi_check', '0');
                // 消込区分が削除の場合は対象外
                if(keshikomi_kbn != '9'){
                    m.set('delete_chk', false);
                }
                m.set('diff_prc', m.get('diff_prc_org'));
                var payment_kbn = m.get('payment_kbn');
                if(payment_kbn == 0){
                    $('.delete_chk').prop('checked',false);
                    $('.keshikomi_chk').prop('checked',false);
                    m.set('kariuke_prc', 0);
                    m.set('furikomi_chg_cost', 0);
                }
            });
            
            var sel = this.model.get('selected');
            if(sel == '1'){
                return;
            }
            
            // 今回選択した行を変更
            this.$('tr').addClass('row-selected');
            this.model.set('selected', '1');
            var DataColJson = JSON.stringify(this.model);
            
            // 消込区分が9:削除の場合、請求を検索しにいかない
            if(keshikomi_kbn == 9 || keshikomi_kbn == "9" ){
                return;
            }
            // 消込区分が0:未消込の場合
            if(keshikomi_kbn == 0 || keshikomi_kbn == "0" ){
                // 請求書検索ボタンを表示
                $('#btn_seikyu_multi').removeAttr('data-offset').css('visibility', 'visible');
                // 選択した行によりマッチ解除ボタンの表示切替
                var diff_prc_org = this.model.get('diff_prc_org');
                if(diff_prc_org > 0){ // DBから取得した差額が有りの場合表示
                    $('#btn_match_cancel').css('visibility','visible');
                }else{                // DBから取得した差額が無しの場合非表示
                    $('#btn_match_cancel').css('visibility','hidden');
                }
            }
            
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/saiken/unmatchkeshikomi/searchseikyu',
                data: {
                    DataColJson: DataColJson
                    ,bumon_cd: appunmatch.appModel.get('search_bumon_cd')
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        zanNyukinFlg = 0;
                        zanNyukinPrc = 0;
                        appunmatch.reset2(mydata);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                        return;
                    }
                }
            });
        },
        // 消込チェック処理
        doKeshikomiChk: function () {
            var dif_prc = this.model.get('diff_prc');
            var furikomi_chg_cost = this.model.get('kariuke_prc');
            
            if(this.$(".keshikomi_chk").prop("checked") == true){
                if(dif_prc !== 0){
                   this.model.set('kariuke_prc', dif_prc);
                   this.model.set('diff_prc', 0);
                   paymentChgFlg = 1;// 入金データ変更フラグ 1:変更有
                } 
                this.model.set('keshi_check', '1');
            }else{
                this.model.set('kariuke_prc', 0);
                this.model.set('diff_prc', furikomi_chg_cost);
                this.model.set('keshi_check', '0');
                paymentChgFlg = 0;// 入金データ変更フラグ 0:変更無
            }
        },
        // 削除チェック処理
        doDeleteChk: function () {
            
            // 請求データが選択されている場合、チェックは押せない
            var selected = 0;
            appunmatch.seikyuCol.some(function (m, i) {
                selected = m.get('selected');
                if(selected == 1){
                    // 1レコードでもあったらアウト
                    return true;
                }
            });
            if(selected == 1){
                $.msiJqlib.showErr('請求を選択している場合、削除チェックできません.請求の選択を解除後、再操作してください.');
                this.$('.delete_chk').prop('checked',false);
                this.model.set('delete_chk', false);
                return;
            }
            
            var nyukin_prc = this.model.get('nyukin_prc');
            
            if(this.$(".delete_chk").prop("checked") == true) {
                this.model.set('kariuke_prc', 0);
                this.model.set('delete_chk', true);
                paymentChgFlg = 1;// 入金データ変更フラグ 1:変更有
            }else{
                this.model.set('kariuke_prc', 0);
                this.model.set('delete_chk', false);
                paymentChgFlg = 0;// 入金データ変更フラグ 0:変更無
            }
            
        },
        // チェックボックスの切り替え処理
        patternChkBox: function () {
            var pattern = this.model.get('pattern');
            if(pattern == 0){ // 未消込(差額無)
                this.$('.keshikomi_chk').prop('disabled', true);
                this.$('.keshikomi_chk').prop('checked',false);
                this.$('.delete_chk').prop('disabled', false);
                this.$('.delete_chk').prop('checked',false);
                $('#btn_match_cancel').css('visibility', 'hidden');
                $('#btn_save').css('visibility', 'visible');
            }else if(pattern == 1){ // 消込済
                this.$('.keshikomi_chk').prop('disabled', true);
                this.$('.keshikomi_chk').prop('checked',true);
                this.$('.delete_chk').prop('disabled', true);
                this.$('.delete_chk').prop('checked',false);
                $('#btn_match_cancel').css('visibility','visible');
                $('#btn_save').css('visibility', 'hidden');
            }else if(pattern == 8){ // 未消込(差額有)
                this.$('.keshikomi_chk').prop('disabled', false);
                this.$('.keshikomi_chk').prop('checked',false);
                this.$('.delete_chk').prop('disabled', true);
                this.$('.delete_chk').prop('checked',false);
                $('#btn_save').css('visibility', 'visible');
            }else if(pattern == 9){ // 削除
                this.$('.keshikomi_chk').prop('disabled', true);
                this.$('.keshikomi_chk').prop('checked',false);
                this.$('.delete_chk').prop('disabled', false);
                this.$('.delete_chk').prop('checked',true);
                $('#btn_match_cancel').css('visibility', 'hidden');
                $('#btn_save').css('visibility', 'visible');
            }
        },
    });
    
    // 明細モデル  (Seikyu)   
    var SeikyuModel = Backbone.Model.extend({
        defaults: function () {
            return {
                row: null,
                seikyu_kbn: null,       // 請区
                seko_no: null,          // 施行No
                seko_no_sub:null,       // 施行No（枝番）
                seikyu_den_no: null,    // 請求番号
                kaisya_cd: null,        // 会社コード
                bumon_cd: null,         // 部門コード
                seikyu_ymd: null,       // 請求日
                br_koza_no: null,       // 口座番号
                sekyu_knm: null,        // 請求先名
                sekyu_tel: null,        // 電話番号
                seikyu_zei_prc: null,      // 請求金額
                nyukin_prc: null,       // 入金済金額
                this_nyukin_prc: null,  // 今回入金額
                seikyu_zan: null,       // 請求残高
                seikyu_zan_org: null,       // 請求残高
                history_no: null,       // 取込履歴番号
                data_kbn: null,         // データ区分
                furikomi_chg_cost_flg: false,   // 振込手数料が発生した行はtrueにする
            };
        },
        validation: {
        },
        labels: {
        }
    });
    // コレクション (Seikyu) 
    var seikyuCollection = Backbone.Collection.extend({
        model: SeikyuModel
    });
    // ビュー (Seikyu) 
    var SeikyuView = Backbone.View.extend({
        tagName: 'tbody',
        className: '',
        tmpl: _.template($('#seikyu_tmpl').html()),
        events: {
            "click tr": "setSelected",
        },
        bindings: {
             '.row': 'row',
            '.seikyu_kbn': 'seikyu_kbn',            // 請区
            '.seko_no': 'seko_no',                  // 施行No
            '.seikyu_den_no': 'seikyu_den_no',      // 請求番号
            '.seikyu_ymd': 'seikyu_ymd',            // 請求日
            '.br_koza_no': 'br_koza_no',            // 口座番号
            '.sekyu_knm': 'sekyu_knm',              // 請求先名
            '.sekyu_tel': 'sekyu_tel',              // 電話番号
            '.seikyu_zei_prc': {                  // 請求金額
                observe: 'seikyu_zei_prc',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '.nyukin_prc': {                       // 入金済金額
                observe: 'nyukin_prc',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '.this_nyukin_prc': {                  // 今回入金額
                observe: 'this_nyukin_prc',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
            '.seikyu_zan': {                        // 請求残高
                observe: 'seikyu_zan',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd
            },
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback({row: '.row'}, "error1"));
            this.listenTo(this.model, 'change', function () {
                $.msiJqlib.setBtnEnable($("#btn_save"));
            });
            this.listenTo(this.model, 'destroy', this.remove);
        },
        render: function () {
            //テンプレートから明細部分のHTML作成
            this.$el.html(this.tmpl({idx: this.model.cid}));
            this.stickit();
            var $tr = this.$('tr');
            if (this.model.get('_selected')) {
                $tr.addClass('row-selected');
            } else {
                $tr.removeClass('row-selected');
            }
            // 赤字クラス切り替え処理
            this.toggleClass();
            return this;
        },
        // マイナスのときに赤字クラスを追加する処理
        toggleClass: function (e) {
            appunmatch.toggleAkajiClass(this, ['nyukin_prc']);
            appunmatch.toggleAkajiClass(this, ['sagaku_jiysa']);
        },
        setSelected: function (e) {
            var delete_chk_flg = 0;
            var keshikomi_kbn = null;
            var keshi_check = null;
            var diff_prc = null;
            var kaisya_cd = null;
            var furikomi_chg_cost = null;
            var fee_kbn = appunmatch.appModel.get('fee_kbn');
            var seikyu_den_no = this.model.get('seikyu_den_no');
            var furikomi_chg_cost_flg = false;
            var furikomi_chg_cost_row_no = null;
            var furikomi_chg_cost_row_no = null;
            $.each( appunmatch.seikyuCol.models, function(i){
                var sn = this.get('seikyu_den_no');
                var furikomi_flg = this.get('furikomi_chg_cost_flg');
                if(seikyu_den_no != sn && furikomi_flg){ 
                    furikomi_chg_cost_flg = true;
                    furikomi_chg_cost_row_no = this.get('row');
                }
            });
            appunmatch.paymentCol.some(function (m, i) {
                var delete_chk = m.get('delete_chk');
                var furiSel = m.get('selected');
                if(furiSel == 1){
                    keshikomi_kbn = m.get('payment_kbn');
                    keshi_check = m.get('keshi_check');
                    diff_prc = m.get('diff_prc');
                    if(delete_chk == true){
                        delete_chk_flg = 1;
                    }
                    // 入金データは1レコードのみ選択可能なためbreak
                    return true;
                }
            });
            
            // 消込区分が0:未消込以外は請求データを選択できない
            if(keshikomi_kbn !== 0 && keshikomi_kbn !== "0" ){
                return; // メッセージは出さずそのままreturn
            }
            
            // 削除チェックしている場合はエラー
            if(delete_chk_flg == 1){
                $.msiJqlib.showErr('削除チェックしている場合、請求を選択できません.削除チェック解除後、再操作してください.');
                return;
            }
            // 消込にチェックされて差額が0の場合はエラー
            if(keshi_check == 1 && diff_prc == 0){
                $.msiJqlib.showErr('入金額は全て割り振っています.');
                return;
            }
            
            // 既にマッチし、請求残高の無い請求データを選択できない
            var history_no = this.model.get('history_no');
            var seikyu_zan = this.model.get('seikyu_zan');
            var selected = this.model.get('selected');
            if(selected !== '1' && (seikyu_zan == "0" || seikyu_zan == '0' || seikyu_zan == null)){
                $.msiJqlib.showErr('既に消込済の請求データのため、選択できません.');
                return;
            }
            // 選択解除時に選択行で振込手数料が発生している場合は選択解除できない
            if(selected === '1' && furikomi_chg_cost_flg){
                $.msiJqlib.showErr('振込手数料が発生しているため選択解除できません。('+furikomi_chg_cost_row_no+'行目)');
                return;
            }
            
            paymentChgFlg = 1;// 入金データ変更フラグ 1:変更有
            
            var this_nyukin_prc = 0;
            if (selected === '1') {
                this.$('tr').removeClass('row-selected');
                this.model.set('selected', '0');
                this.model.set('furikomi_chg_cost_flg', false);
                var this_nyukin_prc = this.model.get('this_nyukin_prc');
                // 今回入金額
               this.model.set('this_nyukin_prc', 0);
                // 請求残高
               this.model.set('seikyu_zan', this.model.get('seikyu_zan_org'));
                // 入金額(未割振分)
                zanNyukinPrc = Number(zanNyukinPrc) + Number(this_nyukin_prc);
                zanNyukinFlg = 0;
                // 差額 = 入金額(未割振分)の場合、変更なし時と同様にする
                appunmatch.paymentCol.some(function (m, i) {
                    var furiSel = m.get('selected');
                    if(furiSel == 1){
                        var nyukin_prc = m.get('nyukin_prc');
                        var diff_prc_org = m.get('diff_prc_org');
                        if(zanNyukinPrc == diff_prc_org 
                                || (diff_prc_org == null && zanNyukinPrc == nyukin_prc)){
                            zanNyukinPrc = diff_prc_org;
                            paymentChgFlg = 0;// 入金データ変更フラグ 0:変更無
                        }
                        // 入金データは1レコードのみ選択可能なためbreak
                        return true;
                    }
                });
            } else {
                // 未割振分の入金額が無い場合はエラー
                if(zanNyukinFlg == 1){
                    $.msiJqlib.showErr("入金額は全て割り振っています.");
                    return;
                }
                this.$('tr').addClass('row-selected');
                this.model.set('selected', '1');
                seikyu_zan = this.model.get('seikyu_zan');
                
                appunmatch.paymentCol.some(function (m, i) {
                    var furiSel = m.get('selected');
                    if(furiSel == 1){
                        var nyukin_prc = m.get('nyukin_prc');
                        var diff_prc_org = m.get('diff_prc_org');
                        kaisya_cd = m.get('kaisya_cd').trim();
                        furikomi_chg_cost = m.get('furikomi_chg_cost');
                        if (!$.msiJqlib.isNullEx2(furikomi_chg_cost)) {
                            furikomi_chg_cost = Number(m.get('furikomi_chg_cost'));
                        } else {
                            furikomi_chg_cost = 0;
                        }
                        if(zanNyukinPrc == 0 || zanNyukinPrc == null){
                            if(diff_prc_org > 0){
                                zanNyukinPrc = Number(diff_prc_org);
                            }else{
                                zanNyukinPrc = Number(nyukin_prc);
                            }
                        }
                        // 入金データは1レコードのみ選択可能なためbreak
                        return true;
                    }
                });
                if(Number(seikyu_zan) > Number(zanNyukinPrc)){
                    this_nyukin_prc = zanNyukinPrc;
                    // 入金が不足している場合は振込手数料差異をチェックして金額以下なら手数料を設定、以上なら請求残に設定
                    var flg = false;
                    _.each(fee_kbn, function (item) {
                        if ((item.kbn_value_cd == kaisya_cd) && (item.kbn_value_snm >= Number(seikyu_zan) - Number(zanNyukinPrc))) {
                            seikyuZan = 0;
                            furikomi_chg_cost += Number(seikyu_zan) - Number(zanNyukinPrc);
                            flg = true;
                        }
                    });
                    if (!flg) {
                        seikyuZan = Number(seikyu_zan) - Number(zanNyukinPrc);
                    } else {
                        this.model.set('furikomi_chg_cost_flg', true);
                    }
                }else{
                    this_nyukin_prc = seikyu_zan;
                    seikyuZan = 0;
                }
                // 今回入金額
                this.model.set('this_nyukin_prc', this_nyukin_prc);
                // 請求残高
                this.model.set('seikyu_zan', seikyuZan);
                // 入金額(未割振分)
                zanNyukinPrc = Number(zanNyukinPrc) - Number(this_nyukin_prc);
                if(zanNyukinPrc <= 0){
                    zanNyukinFlg = 1;
                }
            }
            // 差額・仮受金
            appunmatch.paymentCol.some(function (m, i) {
                var furiSel = m.get('selected');
                var kariuke_prc = m.get('kariuke_prc');
                if(furiSel == 1){
                    if(kariuke_prc > 0){
                        m.set('kariuke_prc', zanNyukinPrc);
                    }else{
                        m.set('diff_prc', zanNyukinPrc);
                    }
                    m.set('furikomi_chg_cost', furikomi_chg_cost);
                    // 入金データは1レコードのみ選択可能なためbreak
                    return true;
                }
            });
        },
    });
    
    // 赤字クラスの追加削除処理  
    appunmatch.toggleAkajiClass = function (that, targets) {
        _.each(targets, function (val) {
            var target = that.model.get(val);
            var target = that.model.get(val);
            if (!$.msiJqlib.isNullEx2(target) && target < 0) {
                that.$("#sagaku_jiysa").addClass('com-akaji');
            } else {
                that.$("#sagaku_jiysa").removeClass('com-akaji');
            }
        });
    };

    try {
        //データ取得
        var data = msiLib2.getJsonFromHtml($('#data-json'));
    } catch (e) {
        console.log('JSON error. ' + e);
        $.msiJqlib.showErr('JSON error. ' + e);
    }
    
    var zanNyukinPrc = 0;   // 入金額(未割振分)
    var zanNyukinFlg = 0;   // 入金額(未割振分)判定用
    var seikyu_zan = 0;     // 請求残高(計算前)←eachの中で計算する用
    var seikyuZan = 0;      // 請求残高(計算後)←eachの中で計算する用
    var paymentChgFlg = 0;  // 入金データ変更フラグ
    
    // APP初期化処理
    appunmatch.appModel = new AppModel();
    appunmatch.paymentModel = new PaymentModel();
    appunmatch.paymentCol = new paymentCollection();
    appunmatch.seikyuCol = new seikyuCollection();
    appunmatch.appView = new AppView({model: appunmatch.appModel, collection: appunmatch.paymentCol});
    appunmatch.PaymentView = new PaymentView({model: new PaymentModel, collection: appunmatch.paymentCol});
    appunmatch.SeikyuView = new SeikyuView({model: new SeikyuModel, collection: appunmatch.seikyuCol});
    appunmatch.offset = 0;
    
    $('#btn_match_cancel').css('visibility', 'hidden');
    $('#btn_search_next').removeAttr('data-offset').css('visibility', 'hidden');
    $('#btn_search_prev').removeAttr('data-offset').css('visibility', 'hidden');
    $('#btn_seikyu_multi').removeAttr('data-offset').css('visibility', 'hidden');
            
    appunmatch.reset = function (data) {
        //データ保持
        appunmatch.data = data;
        // モデルのデータを設定
        appunmatch.appView.model.set(data);
        //データセット(Payment)
        appunmatch.paymentCol.reset(data.dataPayment);
        // 退避データ
        appunmatch.orgdownloadColJson = appunmatch.paymentCol.toJSON();
        $(document).msiErrClearAll();
        
        if(appunmatch.offset === 0){
            $('.act-button-search-prev').removeAttr('data-offset').css('visibility','hidden');
        }
    };
    appunmatch.reset2 = function (data) {
        //データ保持
        appunmatch.data2 = data;
        //データセット(Seikyu)
        appunmatch.seikyuCol.reset(data.dataSeikyu);
    };
    
    // データ設定    
    appunmatch.reset(data);
    // msiパーツの有効化 
    msiLib2.msiPrepareParts('#order');
    // グリッドリサイズ対応
    var dwh = $("#order").height();
    $("#wideBasic").css("height", (dwh - 400) + 'px');
    $(window).resize(function() {
        var dwh = $("#order").height();
        $("#wideBasic").css("height", (dwh - 400) + 'px');
    });
});
