<?php

/**
 * Saiken_SeikyuinfomultidlgController
 * ※Saiken_SeikyuinfobechudlgControllerをコピーし、テンプレートを変更
 *
 * 請求情報検索ダイアログ (マルチ選択可)
 *
 * @category   App
 * @package    controllers\saiken
 * <AUTHOR> Kino
 * @since      2021/02/XX
 * @filesource 
 */

/**
 * 請求情報検索ダイアログ (マルチ選択可)
 *
 * @category   App
 * @package    controllers\saiken
 * <AUTHOR> Kino
 * @since      2021/02/XX
 */
class Saiken_SeikyuinfomultidlgController extends Mref_AbstractdlgController {
    
    /** 分割合算区分：0=>通常 */
    static protected $_bungas_normal = '0';
    /** 分割合算区分：2=>分割先 */
    static protected $_bungas_bunsaki = '2';
    /** 分割合算区分：20=>合算先 */
    static protected $_bungas_gassaki = '20';

    /**
     * init ファンクション
     *
     * @override
     * <AUTHOR> Kino
     * @since  2021/02/XX
     */
    public function init() {
        // Msi_Zend_Controller_Action を override. dlg => denpyodlg
        // controller path を dlg に設定
        $this->_helper->viewRenderer->setViewScriptPathSpec(':module/seikyuinfomultidlg/:action.:suffix');
    }

    /**
     * 検索処理
     *
     * @return array($data, $hash)
     * <AUTHOR> Kino
     * @since  2021/02/XX
     */
    protected function _doSearch() {
        $params = $this->_params;
        $data = array();
        $hash = array();
        $isMoreData = false;

        $db = Msi_Sys_DbManager::getMyDb();
        $this->_params['limit'] = $limit = Msi_Sys_Utils::easyGetVar($params, 'limit', 'DIGIT', 300);
        $this->_params['offset'] = $offset = Msi_Sys_Utils::easyGetVar($params, 'offset', 'DIGIT', 0);

        $cond = array(
            '__etc_limit' => $limit + 1,
            '__etc_offset' => $offset,
        );
        
        $seikyu_kbn = Msi_Sys_Utils::easyGetVar($params, 's_seikyu_kbn');                                  // 請求区分
        $nyukin_kbn = Msi_Sys_Utils::easyGetVar($params, 's_nyukin_kbn');                                  // 入金区分
        $denpyo_no = Msi_Sys_Utils::easyGetVar($params, 's_denpyo_no');                                    // 請求書№
        $seikyu_nm = Msi_Sys_Utils::easyGetVar($params, 's_seikyu_nm');                                    // 請求先名
        $seikyu_knm = Msi_Sys_Utils::easyGetVar($params, 's_seikyu_knm');                                  // 請求先名カナ
        $seikyu_ymd_from = Msi_Sys_Utils::checkVarOrDefault($params['s_seikyu_ymd_from'], 'DATE2', null);  // 開始請求日
        $seikyu_ymd_to = Msi_Sys_Utils::checkVarOrDefault($params['s_seikyu_ymd_to'], 'DATE2', null);      // 終了請求日
        $urikakezan_from = str_replace(",", "", $params['s_urikakezan_from']);                             // 売掛残高(開始)
        $urikakezan_to = str_replace(",", "", $params['s_urikakezan_to']);                                 // 売掛残高(終了)
        $bumon_cd = Msi_Sys_Utils::easyGetVar($params, 's_bumon_cd');                                      // 部門コード
        $bumon_nm = Msi_Sys_Utils::easyGetVar($params, 's_bumon_nm');                                      // 部門名
        $tanto_cd = Msi_Sys_Utils::easyGetVar($params, 's_tanto_cd');                                      // 担当者コード
        $tanto_nm = Msi_Sys_Utils::easyGetVar($params, 's_tanto_nm');                                      // 担当者名
        $nafuda_nm = Msi_Sys_Utils::easyGetVar($params, 's_nafuda_nm');                                    // 名札
        $bun_gas_kbns = Msi_Sys_Utils::easyGetVar($params, 'bun_gas_kbns');                                // 分割合算区分(複数)
        $not_data_kbns = Msi_Sys_Utils::easyGetVar($params, 'not_data_kbns');                              // 非対象データ区分(複数)
        $disp_kbn = Msi_Sys_Utils::easyGetVar($params, 'disp_kbn');                                        // 表示区分(表示項目の設定)
        $exclude_seikyu_no = Msi_Sys_Utils::easyGetVar($params, 's_exclude_sk');                           // 表示対象外とする請求書番号配列
        $oya_bumon_cd = Msi_Sys_Utils::easyGetVar($params, 's_oya_bumon');                           // 親部門コード
        $br_koza_no = Msi_Sys_Utils::easyGetVar($params, 's_kouza_no');                           // 口座番号
        $seko_no = Msi_Sys_Utils::easyGetVar($params, 's_seko_no');                           // 施行番号

        $this->view->s_multi_sel_kbn = Msi_Sys_Utils::easyGetVar($params, 's_multi_sel_kbn');  // 複数選択区分
        $this->view->s_title_disp = Msi_Sys_Utils::easyGetVar($params, 's_title_disp');  // タイトル区分
        
        // 請求区分
        if ( strlen($seikyu_kbn) > 0 ) {
            $cond['data_kbn'] = array( '=', $seikyu_kbn );
            $this->view->s_seikyu_kbn = $seikyu_kbn;
        }
        // 入金区分
        if ( strlen($nyukin_kbn) > 0 ) {
            if ($nyukin_kbn == 1)	{	// 未入金
                $cond['seikyu_zan'] = array( '<>', '0' );
            } else {
                $cond['seikyu_zan'] = array( '=', '0' );
            }
            $this->view->s_nyukin_kbn = $nyukin_kbn;
        }
        // 請求書№
        if ( strlen($denpyo_no) > 0 ) {
            $cond['seikyu_den_no'] = array( '~', $denpyo_no );
            $this->view->s_denpyo_no = $denpyo_no;
        }
        // 請求先名
        if ( strlen($seikyu_nm) > 0 ) {
            $cond['__x1'] = array( 'x', "(sekyusaki_sekyu_nm ~ :x1_1)", array('x1_1'=>$seikyu_nm) );
            $this->view->s_seikyu_nm = $seikyu_nm;
        }
        // 請求先名カナ
        if ( strlen($seikyu_knm) > 0 ) {
            $name_kana_han = mb_convert_kana($seikyu_knm, 'k', 'UTF-8'); // 半角カナに変換
            $name_kana_zen = mb_convert_kana($seikyu_knm, 'KV', 'UTF-8'); // 全角カナに変換
            $cond['__x2'] = array( 'x', "(sekyusaki_sekyu_knm ~ :x2_1 OR sekyusaki_sekyu_knm ~ :x2_2)", array('x2_1'=> $name_kana_zen, 'x2_2'=> $name_kana_han) );            
        }        
        // 請求日(開始)
        if ( strlen($seikyu_ymd_from) > 0 ) {
            $cond['seikyu_ymdx__no__1'] = array( '>=', $seikyu_ymd_from );
            $this->view->s_seikyu_ymd_from = $seikyu_ymd_from;
        }
        // 請求日(終了)
        if ( strlen($seikyu_ymd_to) > 0 ) { 
            $cond['seikyu_ymdx__no__2'] = array( '<=', $seikyu_ymd_to );
            $this->view->s_seikyu_ymd_to = $seikyu_ymd_to;
        }
        // 売掛残高(開始)
        if ( strlen($urikakezan_from) > 0 ) {
            $cond['seikyu_zan__no__1'] = array( '>=', $urikakezan_from );
            $this->view->s_urikakezan_from = $urikakezan_from;
        }
        // 売掛残高(終了)
        if ( strlen($urikakezan_to) > 0 ) { 
            $cond['seikyu_zan__no__2'] = array( '<=', $urikakezan_to );
            $this->view->s_urikakezan_to = $urikakezan_to;
        }
        // 名札
        if ( strlen($nafuda_nm) > 0 ) {
            $cond['nafuda_nm'] = array( '~', $nafuda_nm );
            $this->view->s_nafuda_nm = $nafuda_nm;
        }
        // 分割合算区分(複数)
        if ( strlen($bun_gas_kbns) > 0 ) {
            $cond['__raw_bun_gas_kbns'] = "T.bun_gas_kbn_num IN (".$bun_gas_kbns.")";
        }
        // 非対象データ区分(複数)
        if ( strlen($not_data_kbns) > 0 ) {
            $cond['__raw_not_data_kbns'] = "T.data_kbn NOT IN (".$not_data_kbns.")";
        }
        
        // 画面表示区分(3:分割合算画面)
        if ( strlen($disp_kbn) > 0 ) {
            $this->disp_kbn = $disp_kbn;
        }
        // 親部門コード
        if ( strlen($oya_bumon_cd) > 0 ) {
            $cond['oya_bumon_cd'] = array( '=', $oya_bumon_cd );
            $this->view->s_oya_bumon = $oya_bumon_cd;
        }
        // 部門コード
        if ( strlen($bumon_cd) > 0 ) {
            $cond['bumon_cd'] = array( '~', $bumon_cd );
        }
        // 口座番号
        if ( strlen($br_koza_no) > 0 ) {
            $cond['br_koza_no'] = array( '~', $br_koza_no );
            $this->view->s_kouza_no = $br_koza_no;
        }
        // 施行番号
        if ( strlen($seko_no) > 0 ) {
            $cond['seko_no'] = array( '~', $seko_no );
            $this->view->s_seko_no = $seko_no;
        }
        
        // 複数会社対応
        $kaisyacd = App_Utils::getCtxtKaisyaEasy();
        $cond['kaisya_cd'] = $kaisyacd;

        // サブクラスでの調整用
        $this->_pre_search_hook($cond);

        foreach (DataMapper_SeikyuDenpyo::findForDlg($db, $cond) as $rec) {
            
            // 表示対象外請求書番号が指定されている場合
            if( in_array( $rec['seikyu_den_no'], $exclude_seikyu_no ) ){
                continue;
            }
            
            $myid = $rec['myid']    = $rec['seikyu_den_no'];
            $rec['data_kbn']        = $rec['data_kbn'];
            $rec['seikyu_kbn_nm']   = $rec['seikyu_kbn_nm'];
            $rec['juchu_denpyo_no'] = $rec['denpyo_no'];
            $rec['seko_no']         = $rec['seko_no'];
            $rec['seko_no_sub']     = $rec['seko_no_sub'];
            $rec['code']            = $rec['seikyu_den_no'];
            $rec['seikyu_ymd']      = $rec['seikyu_ymdx'];
            $rec['seikyu_cd']       = $rec['sekyusaki_sekyu_cd'];
            $rec['seikyu_nm']       = $rec['sekyusaki_sekyu_nm'];
            $rec['seikyu_addr']     = $rec['sekyusaki_sekyu_addr'];
            $rec['tanto_cd']        = $rec['tanto_cd'];
            $rec['tanto_nm']        = $rec['tanto_nm'];
            $rec['br_koza_no']      = $rec['br_koza_no'];
            $rec['bumon_cd']        = $rec['bumon_cd'];
            $rec['bumon_nm']        = $rec['bumon_snm'];
            $rec['bumon_lnm']       = $rec['bumon_lnm'];
            $rec['nafuda_nm']       = $rec['nafuda_nm'];
            $rec['denpyo_biko2']    = $rec['denpyo_biko2'];
            $rec['seikyu_prc']      = Msi_Sys_Utils::filterComma($rec['seikyu_prc']);
            $rec['zei_prc']         = Msi_Sys_Utils::filterComma($rec['zei_prc']);
            $rec['seikyu_zei_prc']  = Msi_Sys_Utils::filterComma($rec['seikyu_zei_prc']);
            $rec['nyukin_prc']      = Msi_Sys_Utils::filterComma($rec['nyukin_prc']);
            $rec['seikyu_zan']      = Msi_Sys_Utils::filterComma($rec['seikyu_zan']);
            $sekyuSelect = DataMapper_SekyuSakiInfo::findSeikyusekyu($db, array('seikyu_den_no' => $rec['seikyu_den_no']));
            // 請求伝票から売上伝票データを取得する
            if (isset($rec['uri_den_no'])) {
                if ($rec['bun_gas_kbn_num'] === static::$_bungas_normal) {
                    $uriData = DataMapper_UriageDenpyo::find($db, array('uri_den_no' => $rec['uri_den_no']));
                    $rec['juchu_denpyo_no'] = $uriData[0]['denpyo_no'];
                } else {    // 通常以外は元の請求伝票からデータを取得する
                    $sekyuMotoData = DataMapper_SeikyuDenpyo::find($db, array('seikyu_den_no' => $rec['bun_gas_seikyu_den_no']));
                    if (isset($sekyuMotoData[0]['uri_den_no'])) {
                        $uriData = DataMapper_UriageDenpyo::find($db, array('uri_den_no' => $sekyuMotoData[0]['uri_den_no']));
                    }
                    $rec['juchu_denpyo_no'] = $uriData[0]['denpyo_no'];
                }
            }
            $data[] = $rec;
            $hash[$myid] = $rec;
        }

        // Msi_Sys_Utils::debug( Msi_Sys_Utils::dump($data) );
        return array($data, $hash);
    }

    /**
     * 検索前調整
     *
     * <AUTHOR> Kino
     * @since  2021/02/XX
     * @param      array      $cond  条件
     */
    protected function _pre_search_hook(&$cond) {
        
    }

    /**
     * テンプレート変数設定
     *
     * <AUTHOR> Kino
     * @since  2021/02/XX
     * @return void
     */
    protected function _setScriptVar() {
        $this->view->dlg_tpl_search = 'search-denpyo.tpl';
        $this->view->dlg_tpl_head = 'head-denpyo-multi.tpl';
        $this->view->dlg_tpl_list = 'list-denpyo-multi.tpl';
        $this->view->dlg_title = '請求書検索';
        $db = Msi_Sys_DbManager::getMyDb();

        foreach (Msi_Sys_Utils::strArrayify_qw('s_seikyu_kbn s_nyukin_kbn s_denpyo_no s_seikyu_nm s_seikyu_ymd_from' .
                's_seikyu_ymd_to s_urikakezan_from s_urikakezan_to s_bumon_cd s_oya_bumon s_kouza_no' .
                's_bumon_nm s_tanto_cd s_tanto_nm s_nafuda_nm s_seko_no') as $k) {
            @ $this->view->$k = $this->_params[$k];
        }
        $this->view->s_nyukin_kbn = '1';

        $this->view->disp_kbn = 0;
        if (isset($this->_params['disp_kbn'])) {
            if ($this->_params['disp_kbn'] == 0) {
                $this->view->s_seikyu_kbn = 1;
                $this->view->disp_kbn = 0;
            } else {
                $this->view->disp_kbn = 9;
            }
        }
        if (isset($this->_params['readonly'])) {
            @ $readonly = $this->_params['readonly'];
            if ($readonly == 1) {
                $this->view->readonly = 'readonly';
            } else {
                $this->view->readonly = null;
            }
        } else {
            $this->view->readonly = null;
        }

        if (isset($this->_params['s_data_kbn']) && $this->_params['s_data_kbn'] === '2') {
            $this->view->s_seikyu_kbn = 2;
        }
        $cond = array('bumon_cd' => array('<>', '00001') // 全社は表示しない
            , 'bumon_kbn' => 0                   // 部門区分：親会社
        );
        $bumonSel = DataMapper_Bumon::find($db, $cond);
        $bumon_cd = Msi_Sys_Utils::remapArray($bumonSel, 'bumon_cd  bumon_lnm', array('bumon_cd' => 'id', 'bumon_lnm' => 'text'));
        $dataKbns = array('bumon_cd' => $bumon_cd);
        $data = array(
            'dataKbns' => $dataKbns,
        );
        $json = Msi_Sys_Utils::json_encode($data);
        $this->view->mydata_form_json = $json;

//        App_Smarty::pushCssFile(['app/mref.dialog2.css', 'app/saiken.seikyudlg.css']);
        App_Smarty::pushCssFile( ['app/mref.dialog2.css','app/saiken.seikyuinfomultidlg.css']);
        App_Smarty::pushJsFile( ['app/saiken.seikyuinfomultidlg.js'] ); 
    }

}
