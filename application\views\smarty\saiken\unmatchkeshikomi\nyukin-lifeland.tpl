{include file="fdn_head_std.tpl"}
{include file="fdn_header_4.tpl"}  
<form id="mst-form-id" method="post">
    <div id="main">
        <meta charset="utf-8">
        <div id="order" style="overflow: hidden;">
            
            <div class="page-title"><span>入金アンマッチ消込</span></div>
            
            <div id="searchbtnarea">
                <fieldset class="base1">
                    <input name="btn_search" id="btn_search" type="button" value="検索" />
                </fieldset>
                <fieldset class="base2">
                    <input name="btn_search_next" id="btn_search_next" class="act-button-search-next" type="button" title="次ページ" value="次" />
                    <input name="btn_search_prev" id="btn_search_prev" class="act-button-search-prev" type="button" title="前ページ" value="前" />
                
                </fieldset>
            </div>
            
            <div class="search">
            <!-- 検索条件 -->
                <fieldset class="base1">
                    <label for="kaisya_cd" id="kaisya_cd my-require" class="lbl_kaisya_cd lbl_normal_cmn">部門</label>
                    <input  type="hidden" id="s_kaisya_cd" name="kaisya_cd" class="cls_kaisya_cd"/>
                    <label for="torikomi_ymd" id="torikomi_ymd my-require" class="lbl_torikomi_ymd lbl_normal_cmn">取込日</label>
                    <input  type="text" id="s_torikomi_ymd" name="torikomi_ymd" class="cls_torikomi_ymd txt my-type-date date_auto_slash to_alpha_num"/>
                    <div class="label dlg_date no_left_border"></div>
                    <label for="keshikomi_kbn" id="keshikomi_kbn my-require" class="lbl_keshikomi_kbn lbl_normal_cmn">消込区分</label>
                    <input  type="hidden" id="s_keshikomi_kbn" name="keshikomi_kbn" class="cls_keshikomi_kbn"/>
                </fieldset>
            </div><!-- /.search -->
            
            <div class="title_nyukin">【入金データ(アンマッチ)】</div>
            <div class="items">
                <div class="header">
                    <table>
                        <tr>
                            <td class="w3 row">選択</td>
                            <td class="w5 nyukin_houhou">入金方法</td>
{*                            <td class="kaisya_nm">会社</td>*}
                            <td class="kanjyo_date">入金日</td>
                            <td class="w10 kouza_no">口座番号</td>
                            <td class="w20 furikomi_nm">振込依頼人名</td>
                            <td class="w10 nyukin_prc">入金額</td>
                            <td class="w3 keshikomi_chk">消込</td>
                            <td class="w10 furikomi_chg_cost">振込手数料</td>
                            <td class="w10 kariuke_prc">仮受金</td>
                            <td class="w10 diff_prc">差額</td>
                            <td class="w3 delete_chk">削除</td>
                        </tr>
                    </table>
                </div><!-- /.header -->
                <div class="list">
                    <table id="t-dtl-payment"></table>
                </div><!-- /.list -->
            </div><!-- /.items -->
            
            <div class="title_seikyu">【請求】</div>
            <div id="wideBasic">
                <div class="item">
                    <div class="header">
                        <table id="t_head">
                            <tr>
                                <td class="w3 row">選択</td>
                                <td class="w5 seikyu_kbn">請区</td>
                                <td class="w8 seko_no">施行No</td>
                                <td class="w8 seikyu_den_no">請求番号</td>
                                <td class="w10 seikyu_ymd">請求日</td>
                                <td class="w8 br_koza_no">口座番号</td>
                                <td class="w20 sekyu_knm">請求先名</td>
                                <td class="w10 sekyu_tel">電話番号</td>
                                <td class="w7 seikyu_zei_prc">請求金額</td>
                                <td class="w7 nyukin_prc">入金済金額</td>
                                <td class="w7 this_nyukin_prc">今回入金額</td>
                                <td class="w7 seikyu_zan">請求残高</td>
                            </tr>
                        </table>
                    </div><!-- /.header -->
                    <div class="list">
                        <table id="t-dtl-seikyu"></table>
                    </div><!-- /.list -->
                </div><!-- /.item -->

                <div id="buttonsArea">
                    <input type="button" name="btn_seikyu_multi"  id="btn_seikyu_multi" value="請求書検索" />
                    <input type="button" name="btn_save"    id="btn_save" value="保存" />
                    <input type="button" name="btn_match_cancel"    id="btn_match_cancel" value="マッチ解除"/>
                </div>
            </div>
        </div>
    </div>
</form>
<!-- payment テンプレート -->
<script type="text/template" id="payment_tmpl">
    <tr id="row_<%=idx%>" >
        <td class="w3 row"></td>
        <td class="w5 nyukin_houhou"></td>
{*        <td class="kaisya_nm"></td>*}
        <td class="kanjyo_date"></td>
        <td class="w10 kouza_no"></td>
        <td class="w20 furikomi_nm"></td>
        <td class="w10 nyukin_prc"></td>
        <td class="w3 keshikomi"><input type="checkbox" class="keshikomi_chk"></td>
        <td class="w10 furikomi_chg_cost"></td>
        <td class="w10 kariuke_prc"></td>
        <td class="w10 diff_prc"></td>
        <td class="w3 delete"><input type="checkbox" class="delete_chk"/></td>
    </tr>
</script>
<!-- seikyu テンプレート -->
<script type="text/template" id="seikyu_tmpl">
    <tr id="row_<%=idx%>" >
        <td class="w3 row"></td>
        <td class="w5 seikyu_kbn"></td>
        <td class="w8 seko_no"></td>
        <td class="w8 seikyu_den_no"></td>
        <td class="w10 seikyu_ymd"></td>
        <td class="w8 br_koza_no"></td>
        <td class="w20 sekyu_knm"></td>
        <td class="w10 sekyu_tel"></td>
        <td class="w7 seikyu_zei_prc"></td>
        <td class="w7 nyukin_prc"></td>
        <td class="w7 this_nyukin_prc"></td>
        <td class="w7 seikyu_zan"></td>                       
    </tr>
</script>


<script id="data-json" type="application/json">
    {$data_json|smarty:nodefaults}
</script>
{include file="fdn_footer_std.tpl"}
