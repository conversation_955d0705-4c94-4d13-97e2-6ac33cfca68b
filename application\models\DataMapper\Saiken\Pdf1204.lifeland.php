<?php

/**
 * DataMapper_Saiken_Pdf1204
 *
 * PDF出力 入金消込リスト データマッパークラス
 *
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mogi
 * @since      2021/01/xx
 * @version    2022/09/05 Kobayashi セレモア用にコピー
 * @filesource 
 */

/**
 * PDF出力 入金消込リスト データマッパークラス
 * 
 * @category   App
 * @package    models\DataMapper
 * <AUTHOR> Mogi
 * @since      2021/01/xx
 */
class DataMapper_Saiken_Pdf1204 extends DataMapper_Abstract {

    /**
     * データ 取得
     *
     * <AUTHOR> Mogi
     * @since      2021/01/xx
     * @param   Msi_Sys_Db $db
     * @param   array      $keyHash  条件
     * @return  array      該当データがない場合はarray()を返す
     */
    public static function find($db, $keyHash = array()) {
        $param = array();
        list( $whereStr, $param ) = static::setWhere($keyHash, $param, 'T');
        list( $orderBy, $tailClause ) = static::setEtc($keyHash, 'T');
        if (strlen($tailClause) <= 0) {
            $tailClause = '';
        }
        if (strlen($orderBy) <= 0) {//施行番号・入金日・口座番号の順//furikomi_payment_info
            $orderBy = ' ORDER BY T.seko_no, T.kanjyo_date, T.kouza_no ';
        }
        /**
         * マッチングリスト表示項目
         * 施行番号・請求番号・入金日・請求日・口座番号・入金先・請求先名・電話番号・入金額・請求額・入金方法・消込区分・差額
         * 使用テーブル：振込入金消込情報、その明細、請求伝票
         * 
         * 使用テーブル：メインは振込入金消込情報、JOINするのは振込入金消込明細、請求伝票
         */
        $select = $db->easySelect(<<< END_OF_SQL
SELECT 
    *
    ,nyukin_prc - seikyu_prc AS sagaku -- 差額（入金額-請求額）
FROM (
    SELECT
        seikyu.seko_no -- 施行番号
        ,seikyu.bumon_cd -- 部門コード
        ,bm.bumon_lnm AS bumon_lnm -- 部門名
        ,substring(cast(ufnm.barcode_info as varchar),20,10) AS seikyu_no -- 請求番号
        ,TO_CHAR(f.kanjyo_date, 'YYYY/MM/DD') AS kanjyo_date -- 入金日
        ,TO_CHAR(seikyu.seikyu_ymd, 'YYYY/MM/DD') AS seikyu_ymd -- 請求日
        ,TO_CHAR(seikyu.keijo_ymd, 'YYYY/MM/DD') AS keijo_ymd -- 売上計上日
        ,f.kouza_no -- 口座番号
        ,trim(f.furikomi_nm) AS furikomi_nm -- 入金先
        ,COALESCE(CONCAT(ssi.sekyu_nm1, ssi.sekyu_nm2), seikyu.sekyu_nm) AS sekyu_nm -- 請求先名
        ,COALESCE(CONCAT(ssi.sekyu_knm1, ssi.sekyu_knm2), seikyu.sekyu_knm) AS sekyu_knm -- 請求先カナ名
        ,COALESCE(ssi.sekyu_tel, seikyu.sekyu_tel) AS sekyu_tel -- 電話番号
        ,f.nyukin_prc -- 入金額
        ,CASE WHEN payment_kbn != 9 AND msi.seikyu_no IS NULL THEN f.kariuke_prc
        ELSE seikyu.seikyu_zan + seikyu.nyukin_prc END AS seikyu_prc -- 請求額
        ,CASE f.file_type
            WHEN '1' THEN '振込'
            WHEN '2' THEN 'コンビニ'
            ELSE '' 
        END AS nyukin_kbn -- 入金方法
        ,f.payment_kbn -- 消込区分(0:未消込　1:消込済み　9:削除)
        ,CASE f.payment_kbn
            WHEN 0 THEN '未消込'
            WHEN 1 THEN-- '消込済み'
                CASE
                    WHEN auto_payment_flg = 1 THEN '自動消込'
                    ELSE '手動消込' END
            WHEN 9 THEN '削除'
            ELSE '' END AS keshikomi_kbn
        ,TO_CHAR(f.upload_date, 'YYYY/MM/DD') AS upload_date -- 取込日（絞り込み用）
        ,f.kaisya_cd -- 会社コード（絞り込み用）
        ,f.history_no -- 取込履歴番号
        ,CASE f.payment_kbn
            WHEN 1 THEN f.kouza_no -- 消込済みの場合のみ表示
            ELSE '' END AS kouza_no2
        ,CASE f.payment_kbn
            WHEN 1 THEN -- 消込済みの場合のみ表示
                CASE f.file_type
                    WHEN '1' THEN '振込'
                    WHEN '2' THEN 'コンビニ'
                    ELSE '' 
                END
            ELSE '' END AS nyukin_kbn2
    FROM furikomi_payment_info f
    LEFT JOIN furikomi_payment_info_msi msi
        ON msi.history_no = f.history_no
        AND msi.msi_no = f.msi_no
        AND msi.delete_flg = 0
    LEFT JOIN seikyu_denpyo seikyu
        ON  seikyu.seikyu_den_no = msi.seikyu_no
        AND seikyu.delete_flg = 0
    LEFT JOIN seikyu_sekyu_saki_info ssi
        ON ssi.seikyu_den_no = msi.seikyu_no
        AND ssi.delete_flg = 0
    LEFT JOIN bumon_mst bm
        ON bm.bumon_cd = seikyu.bumon_cd
        AND bm.delete_flg = 0
    LEFT JOIN upload_furikomi_nyukin_msi ufnm
        ON ufnm.history_no = f.history_no
        AND ufnm.msi_no = f.msi_no
        AND ufnm.delete_flg = 0
    WHERE f.delete_flg = 0
) T
 WHERE $whereStr
 $orderBy
 $tailClause
END_OF_SQL
                , $param);
        return $select;
    }

}
