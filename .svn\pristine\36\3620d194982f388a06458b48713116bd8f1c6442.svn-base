<?php

/**
 * PDF
 *
 * @category   App
 * @package    controller
 * <AUTHOR>
 * @since      2014/03/25
 * @filesource 
 */

/**
 * PDF
 *
 * @category   App
 * @package    controller
 * <AUTHOR> Sato
 * @since      2014/03/25
 */
class Hachu_PdfController extends Zend_Controller_Action {

	const ERR_STATUS_OK = 0;
	const ERR_STATUS_UNJUST_PARAMETER = 1;
	const ERR_STATUS_NODATA = 2;

	// 帳票コード別の下敷きファイル名
	private static $sourceFileName = array(
		'00002' => 'pdf_tmpl/02001.pdf', // おくやみ記事FAX申込書
		'00003' => 'pdf_tmpl/02001.pdf', // おくやみ記事HP掲載依頼  2014/12/22 Kayo ADD
		'00004' => 'pdf_tmpl/02002.pdf', // 加工依頼書
		'00005' => 'pdf_tmpl/02003.pdf', // ビューアショット依頼書
		'00006' => 'pdf_tmpl/02005.pdf', // お礼状（定型文）        
		'00007' => 'pdf_tmpl/02004.pdf', // オリジナル礼状専用申込書
//		'00009' => 'pdf_tmpl/0203_sub.pdf', // 商品発注書
		'00009' => 'pdf_tmpl/0203.pdf', // 商品発注書
		'00010' => 'pdf_tmpl/0203_sub.pdf', // 商品発注書
//        '00011' => 'pdf_tmpl/0202.pdf',  // 料理発注書（出前）
		'00011' => 'pdf_tmpl/0202_sub.pdf', // 料理発注書（出前）    
		'00012' => 'pdf_tmpl/0208.pdf', // 式場・会場発注書
//        '00013' => 'pdf_tmpl/0202.pdf',  // 料理発注書（出前）
		'00013' => 'pdf_tmpl/0202_sub.pdf', // 料理発注書（出前）
		'00014' => 'pdf_tmpl/0206.pdf', // 生花祭壇・アレンジ花　発注書
		'00015' => 'pdf_tmpl/0201.pdf', // 湯灌業務発注書
		'00016' => 'pdf_tmpl/0204.pdf', // 車輌発注書
//		'00017' => 'pdf_tmpl/0207.pdf', // 花運搬・花輪発注書
		'00017' => 'pdf_tmpl/0203A.pdf', // 供花・供物発注書　未使用
        '00033' => 'pdf_tmpl/0214.pdf', // 供花・供物発注書
		'00018' => 'pdf_tmpl/0207.pdf', // 花運搬・花輪発注書
		'00019' => 'pdf_tmpl/0205.pdf', // 喪服発注書
		'00020' => 'pdf_tmpl/0203.pdf', // 商品発注書
		//'00021' => 'pdf_tmpl/0203.pdf',   // 商品発注書
		'00021' => 'pdf_tmpl/0206_sub.pdf', // 商品発注書(遺品装飾)
		'00022' => 'pdf_tmpl/0205.pdf', // 喪服発注書（着付）
		'00031' => 'pdf_tmpl/0204.pdf', // 車輌発注書
		'00032' => 'pdf_tmpl/0204.pdf', // 車輌発注書
		'00034' => 'pdf_tmpl/0209.pdf', // 寺院依頼書
		'00035' => 'pdf_tmpl/0210.pdf', // 葬儀・喪家情報確認書
		'00036' => 'pdf_tmpl/0211.pdf', // 駐車場警備依頼書
		'00037' => 'pdf_tmpl/0212.pdf'   // 業務依頼書
	);
	// 帳票コード別の用紙方向
	private static $pageOrientation = array(
		'00002' => 'P', // おくやみ記事FAX申込書
		'00003' => 'P', // おくやみ記事HP掲載依頼  2014/12/22 Kayo ADD
		'00004' => 'P', // 加工依頼書
		'00005' => 'P', // ビューアショット依頼書
		'00006' => 'P', // お礼状（定型文）
		'00007' => 'L', // オリジナル礼状専用申込書
		'00009' => 'P', // 商品発注書
		'00010' => 'P', // 商品発注書
		'00011' => 'P', // 料理発注書（出前）
		'00012' => 'P', // 式場・会場発注書
		'00013' => 'P', // 料理発注書（出前）
		'00014' => 'P', // 生花祭壇・アレンジ花　発注書
		'00015' => 'P', // 湯灌業務発注書
		'00016' => 'P', // 車輌発注書
		'00017' => 'P', // 花運搬・花輪発注書
		'00018' => 'P', // 花運搬・花輪発注書
		'00019' => 'P', // 喪服発注書
		'00020' => 'P', // 商品発注書
		'00021' => 'P', // 商品発注書(遺品装飾)
		'00022' => 'P', // 喪服発注書（着付）
		'00031' => 'P', // 車輌発注書
		'00032' => 'P', // 車輌発注書
        '00033' => 'P', // 供花・供物発注書
		'00034' => 'P', // 寺院依頼書
		'00035' => 'P', // 葬儀・喪家情報確認書
		'00036' => 'P', // 駐車場警備依頼書
		'00037' => 'P'   // 業務依頼書
	);
	// 帳票コード別の下敷きファイル名
	private static $sourceFileNameHouji = array(
		'00002' => 'pdf_tmpl/02001.pdf', // おくやみ記事FAX申込書
		'00003' => 'pdf_tmpl/02001.pdf', // おくやみ記事HP掲載依頼  2014/12/22 Kayo ADD
		'00004' => 'pdf_tmpl/02002.pdf', // 加工依頼書
		'00005' => 'pdf_tmpl/02003.pdf', // ビューアショット依頼書
		'00006' => 'pdf_tmpl/02005.pdf', // お礼状（定型文）        
		'00007' => 'pdf_tmpl/02004.pdf', // オリジナル礼状専用申込書
		'00009' => 'pdf_tmpl/0203_houji.pdf', // 商品発注書
		'00010' => 'pdf_tmpl/0203_sub.pdf', // 商品発注書
		'00011' => 'pdf_tmpl/0202_sub_houji.pdf', // 料理発注書（出前）    
		'00012' => 'pdf_tmpl/0208.pdf', // 式場・会場発注書
		'00013' => 'pdf_tmpl/0202_sub_houji.pdf', // 料理発注書（出前）
		'00014' => 'pdf_tmpl/0206_houji.pdf', // 生花祭壇・アレンジ花　発注書
		'00015' => 'pdf_tmpl/0201_houji.pdf', // 湯灌業務発注書
		'00016' => 'pdf_tmpl/0204_houji.pdf', // 車輌発注書
		'00017' => 'pdf_tmpl/0203A.pdf', // 供花・供物発注書
                '00033' => 'pdf_tmpl/0214_houji.pdf', // 供花・供物発注書
		'00018' => 'pdf_tmpl/0207.pdf', // 花運搬・花輪発注書
		'00019' => 'pdf_tmpl/0205.pdf', // 喪服発注書
		'00020' => 'pdf_tmpl/0203_houji.pdf', // 商品発注書
		'00021' => 'pdf_tmpl/0206_sub.pdf', // 商品発注書(遺品装飾)
		'00022' => 'pdf_tmpl/0205.pdf', // 喪服発注書（着付）
		'00031' => 'pdf_tmpl/0204_houji.pdf', // 車輌発注書
		'00032' => 'pdf_tmpl/0204_houji.pdf', // 車輌発注書
		'00034' => 'pdf_tmpl/0209.pdf', // 寺院依頼書
		'00035' => 'pdf_tmpl/0210.pdf', // 葬儀・喪家情報確認書
		'00036' => 'pdf_tmpl/0211.pdf', // 駐車場警備依頼書
		'00037' => 'pdf_tmpl/0212.pdf'   // 業務依頼書
	);
	/**
	 * アクション
	 *
	 * <AUTHOR> Sato
	 * @since 2014/03/25
	 */
	public function indexAction() {
		$params = Msi_Sys_Utils::webInputs();

		// プレビュー有無
		if (isset($params['preview'])) {
			$preview = htmlspecialchars($params['preview']) == 'on';
		} else {
			$preview = false;
		}

		try {
			// 施行番号
			if (isset($params['seko_no'])) {
				$seko_no = htmlspecialchars($params['seko_no']);
			} else {
				throw new Exception($this->getErrMsg(self::ERR_STATUS_UNJUST_PARAMETER));
			}

			// 発注管理番号
			if (isset($params['hachu_no'])) {
				$hachu_no_ary = null;

				// 発注管理番号を取得
				if (is_string($params['hachu_no'])) {
					$hachu_no_ary[] = htmlspecialchars($params['hachu_no']);
				} else if (is_array($params['hachu_no'])) {
					foreach ($params['hachu_no'] as $value) {
						$hachu_no_ary[] = htmlspecialchars($value);
					}
				}

				if (!isset($hachu_no_ary)) {
					throw new Exception($this->getErrMsg(self::ERR_STATUS_UNJUST_PARAMETER));
				}
			} else {
				throw new Exception($this->getErrMsg(self::ERR_STATUS_UNJUST_PARAMETER));
			}

			// プレビュー時、発注ボタン表示
			if (isset($params['hachu'])) {
				$hachu_dsp = htmlspecialchars($params['hachu']);
			} else {
				$hachu_dsp = 'off';
			}

			$hachu_time = date('Y/m/d H:i:s');  // 発注日時

			$db = Msi_Sys_DbManager::getMyDb();

                        $this->_moushi_kbn = $this->getMoushiKbn($db, $seko_no);

			// 施行発注管理情報を取得
			$hachu_rec = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, null, null);
			if (count($hachu_rec) == 0) {
				throw new Exception($this->getErrMsg(self::ERR_STATUS_NODATA));
			}
			if ($hachu_rec[0]['ha_rp_kbn'] == 11) {
				$hachu_ary = $this->dividePerHachusho_ryori($hachu_rec);
			} else if (($hachu_rec[0]['ha_rp_kbn'] == 16) || ($hachu_rec[0]['ha_rp_kbn'] == 31) || ($hachu_rec[0]['ha_rp_kbn'] == 32)) {
				$hachu_ary = $this->dividePerHachusho_sharyo($hachu_rec);
			} else {
				$hachu_ary = $this->dividePerHachusho($hachu_rec);
			}
			$kitsukeFlg = false;
            if ($hachu_rec[0]["report_cd"] == '00016'){
                $hachu_ary = $this->dividePerHachusho_syaryo($hachu_rec);
            }
			foreach ($hachu_ary as $hachu) {
				$siire_cd = $hachu['siire_cd'];		// 仕入先コード
				$report_cd = $hachu['report_cd'];	  // 帳票コード
				$hachu_no_ary_wk = $hachu['hachu_no_ary'];
				$category_kbn = "";
				if (isset($hachu['category_kbn'])) {
					$category_kbn = $hachu['category_kbn'];
				}
				if ($report_cd == '00019') {
					$kitsukeFlg = true;
				}
				//if ($report_cd != '00022') {
				if (($report_cd == '00022') && ($kitsukeFlg == true)) {
					for ($loop = 0; $loop < count($hachu_no_ary_wk); $loop++) {
						array_push($info_ary[0]['hachu_no_ary'], $hachu_no_ary_wk[$loop]);
					}
				} else {
					$pdfObj = new App_Pdf(App_HachuLib::getReportName($db, $report_cd), 'A4', self::$pageOrientation[$report_cd]);

					$status = $this->outData($pdfObj, $db, $seko_no, $hachu_no_ary_wk, $siire_cd, $report_cd, $hachu_time, $category_kbn);
					if (isset($status) && $status != self::ERR_STATUS_OK) {
						throw new Exception($this->getErrMsg($status));
					}
					if ($preview) {
						$buf = $pdfObj->fileOutBuf();
						$file_name = $pdfObj->getFileName();
						$key = Msi_Sys_Utils::prepareMyTempFileEasy($buf, $file_name);

						$info = array();
						$info['hachu_time'] = $hachu_time;
						$info['seko_no'] = $seko_no;
						$info['hachu_no_ary'] = $hachu_no_ary_wk;
						$info['file'] = $file_name;
						$info['key'] = $key;
						$info_ary[] = $info;
					} else {
						$pdfObj->downloadAjaxPush();
					}
				}
			}
		} catch (Exception $ex) {
			if ($preview) {
				Msi_Sys_Utils::pushMsg($ex->getMessage(), 'err');
				$this->_helper->viewRenderer->setViewScriptPathSpec(':module/:action.:suffix');
				$this->_helper->viewRenderer->setScriptAction('pdf');
			} else {
				Msi_Sys_Utils::outJson(array('status' => 'NG', 'msg' => $ex->getMessage()));
			}
			return;
		}

		if ($preview) {
			App_Smarty::pushCssFile(['app/hachu.pdf.css']);
			App_Smarty::pushJsFile(['app/hachu.pdf.js']);

			// 発注ボタン表示設定
			if ($hachu_dsp == 'auto') {
				// ・発注書が１枚かつ喪服：発注可
				// ・発注書が１枚かつ未発注：発注可
				if (count($hachu_ary) == 1) {
					$report_cd = $hachu_ary[array_keys($hachu_ary)[0]]['report_cd'];
					if ($report_cd == '00019' || $report_cd == '00022') {
						$on = true;
					} else {
						$on = true;
						foreach ($hachu_no_ary as $hachu_no) {
							$row = DataMapper_HachuCommon::isOrderEnd($db, $seko_no, $hachu_no);
							if ($row['is_order_end']) {
								$on = false;
								break;
							}
						}
					}
					// 全て未発注なら、発注ボタン表示
					if ($on) {
						$this->view->hachu_dsp = true;
					}
				} else {
					$on = false;
					$report_cd = $hachu_ary[array_keys($hachu_ary)[0]]['report_cd'];
					//喪服・着付けの場合のみ
					if ($report_cd == '00019' || $report_cd == '00022') {
						$on = true;
					}
					// 全て未発注なら、発注ボタン表示
					if ($on) {
						$this->view->hachu_dsp = true;
					}
				}
			}
			$this->view->info_json = Msi_Sys_Utils::json_encode(array('data' => $info_ary));
			$this->view->info_ary = $info_ary;
			$this->_helper->viewRenderer->setViewScriptPathSpec(':module/:action.:suffix');
			$this->_helper->viewRenderer->setScriptAction('pdf');
		} else {
			$pdfObj->downloadAjaxFlush();
		}
	}
    
    /**
     * 施行発注管理情報 配列を発注書単位に分ける（車両用）
     * 
     * <AUTHOR> Yanagiso
     * @since      2015/04/27
     * @param array $hachu_ary 施行発注管理情報 配列
     * @return array $ret 施行発注管理情報
     */
    private function dividePerHachusho_syaryo($hachu_ary)
    {
        $ret = array();
        foreach ($hachu_ary as $hachu) {
            //$id = $hachu['siire_cd'].'-'.$hachu['report_cd'].'-'.$hachu['ha_syori_kbn'];
            $id = $hachu['siire_cd'].'-'.$hachu['report_cd'].'-'.$hachu['ha_syori_kbn'];
            if (!isset($ret[$id])) {
                $ret[$id] = array(
                    'siire_cd'			=> $hachu['siire_cd'],
                    'report_cd'			=> $hachu['report_cd'],
                    'ha_syori_kbn'		=> $hachu['ha_syori_kbn'],
                    'shiire_zei_kbn'	=> $hachu['shiire_zei_kbn'],
                    'hachu_no_ary'		=> array());
            }
            $ret[$id]['hachu_no_ary'][] = $hachu['hachu_no'];
        }
        return $ret;
    }

	/**
	 * 発注書印刷処理　メイン
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @version 2014/12/22 おくやみ記事HP掲載依頼でPHPエラーになる件を修正 Kayo
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return string $status		ステータス
	 */
	private function outData($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time, $category_kbn) {
		switch ($report_cd) {
			case '00002':
				// おくやみ記事FAX申込書
				$status = $this->outData02001($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00003':
				// おくやみ記事HP掲載依頼   2014/12/22 ADD Kayo
				$status = $this->outData02001($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00004':
				// 加工依頼書
				$status = $this->outData02002($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00005':
				// ビューアショット依頼書
				$status = $this->outData02003($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00006':
				// お礼状（定型文）
				$status = $this->outData02005($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00007':
				// オリジナル礼状専用申込書
				$status = $this->outData02004($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00009':
			case '00010':
				// 商品発注書（返礼品）
//				$status = $this->outData0203_sub($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				$status = $this->outData0203($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time, '1');
				break;
			case '00020':
				//case '00021':
				// 商品発注書（その他）
				$status = $this->outData0203($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time, '10');
				break;
			case '00011':
			case '00013':
				// 料理発注書（出前）
				$status = $this->outData0202($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time, $category_kbn, '2');
				break;
			case '00012':
				// 式場・会場発注書
				$status = $this->outData0208($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00014':
				// 生花祭壇・アレンジ花　発注書
				$status = $this->outData0206($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00015':
				// 湯灌業務発注書
				$status = $this->outData0201($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00016':
			case '00031':
			case '00032':
				// 車輌発注書
				$status = $this->outData0204($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00017':
				// 供花・供物発注書
				$status = $this->outData0203A($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00018':
				// 花運搬・花輪発注書
				$status = $this->outData0207($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00019':
			case '00022':
				// 喪服発注書
				$status = $this->outData0205($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00021':
				// 発注書(遺品装飾)
				$status = $this->outData0206_sub($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
            case '00033':
                // 供花・供物発注書
                $status = $this->outData0214($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
                break;
			case '00034':
				// 寺院依頼書
				$status = $this->outData0209($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00035':
				// 葬儀・喪家情報確認書
				$status = $this->outData0210($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00036':
				// 駐車場警備依頼書
				$status = $this->outData0211($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			case '00037':
				// 業務依頼書
				$status = $this->outData0212($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time);
				break;
			default:
				$status = self::ERR_STATUS_UNJUST_PARAMETER;
		}
		return $status;
	}

	/**
	 * 湯灌業務発注書
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0201($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		$rec = DataMapper_SekoKihon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$recKihon = $rec[0];

		$rec = DataMapper_SekoYukanInfo::find2($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$recYukan = $rec[0];

		$pdfObj->write_string(array('x' => 100, 'y' => 288, 'width' => 195, 'height' => 15), $recYukan['yukan_nm']);  // 湯灌区分
		$pdfObj->write_string(array('x' => 100, 'y' => 303, 'width' => 195, 'height' => 15), $recYukan['yu_te_nm']);  // 手当て
		// $pdfObj->write_string(array('x' => 375, 'y' => 295, 'width' => 195, 'height' => 15), $recYukan['tanto_nm']);  // 湯灌受注者
		$pdfObj->write_string(array('x' => 100, 'y' => 318, 'width' => 280, 'height' => 15, 'font_size' => 10), $recKihon['k_knm']); // 故人名カナ  
		$pdfObj->write_string(array('x' => 100, 'y' => 334, 'width' => 280, 'height' => 15), $recKihon['k_nm']); // 故人名
		$pdfObj->write_string(array('x' => 100, 'y' => 350, 'width' => 280, 'height' => 15, 'font_size' => 10), $recKihon['m_knm']); // 喪主名カナ  
		$pdfObj->write_string(array('x' => 100, 'y' => 367, 'width' => 280, 'height' => 15), $recKihon['m_nm']); // 喪主名

		$pdfObj->write_string(array('x' => 375, 'y' => 328, 'width' => 60, 'height' => 15), $recKihon['k_sex_nm']);  // 性別
		$pdfObj->write_string(array('x' => 520, 'y' => 328, 'width' => 40, 'height' => 15, 'align' => 'C'), $recKihon['k_nenrei_man']);  // 満
		$pdfObj->write_string(array('x' => 375, 'y' => 361, 'width' => 90, 'height' => 15), $recKihon['m_zoku_nm']);  // 続柄

		if ($recYukan['yu_basyo_cd'] == 0) {
			$pdfObj->write_string(array('x' => 100, 'y' => 393, 'width' => 280, 'height' => 15), '自宅');  // 湯灌場所
		} else {
			$pdfObj->write_string(array('x' => 100, 'y' => 393, 'width' => 280, 'height' => 15), $recYukan['yu_spot_nm']);  // 湯灌場所
		}

		$pdfObj->write_string(array('x' => 100, 'y' => 426, 'width' => 470, 'height' => 15), $recYukan['yukan_yubin_no'] . '　' . $recYukan['yukan_addr1'] . '　' . $recYukan['yukan_addr2']);  // 湯灌住所
		$pdfObj->write_string(array('x' => 450, 'y' => 437, 'width' => 280, 'height' => 15), $recYukan['yukan_tel']);  // 湯灌先電話番号 

		$pdfObj->write_date(array('x' => 100, 'y' => 459, 'width' => 280, 'height' => 15, 'type' => 'time'), $recYukan['yukan_ymd'], "Y年　n月　j日　G時　i分");  // 湯灌日時

		$pdfObj->write_string(array('x' => 100, 'y' => 491, 'width' => 280, 'height' => 15), $recYukan['syushi_nm']);  // 宗旨 
		if (isset($recYukan['syuha_nm2'])) {
			$pdfObj->write_string(array('x' => 375, 'y' => 491, 'width' => 280, 'height' => 15), $recYukan['syuha_nm'] . '（' . $recYukan['syuha_nm2'] . '）');  // 宗派
		} else {
			$pdfObj->write_string(array('x' => 375, 'y' => 491, 'width' => 280, 'height' => 15), $recYukan['syuha_nm']);  // 宗派
		}

		if ($recYukan['spot_cd'] == '') {
			$basho_nm = '';
		} else if ($recYukan['spot_cd'] == '00') {
			$basho_nm = '自宅';
		} else {
			$basho_nm = $recYukan['basho_nm'];
		}

		if (isset($recYukan['hitsugi_nm'])) {
			$pdfObj->write_string(array('x' => 375, 'y' => 654, 'width' => 280, 'height' => 15), $recYukan['hitsugi_nm']);  // 棺の種類
		}

		if ($recYukan['item1_use_kbn'] == 1) {
			$pdfObj->write_circle(array('x' => 141, 'y' => 521, 'width' => 30, 'height' => 20)); //旅支度   
		} else {
			$pdfObj->write_circle(array('x' => 215, 'y' => 521, 'width' => 30, 'height' => 20)); //旅支度  
		}
		if ($recYukan['item2_use_kbn'] == 1) {
			$pdfObj->write_circle(array('x' => 417, 'y' => 521, 'width' => 30, 'height' => 20)); //エンバー 
		} else {
			$pdfObj->write_circle(array('x' => 491, 'y' => 521, 'width' => 30, 'height' => 20)); //エンバー
		}
		if ($recYukan['item3_use_kbn'] == 1) {
			$pdfObj->write_circle(array('x' => 141, 'y' => 554, 'width' => 30, 'height' => 20)); //布団供養  
		} else {
			$pdfObj->write_circle(array('x' => 215, 'y' => 554, 'width' => 30, 'height' => 20)); //布団供養 
		}
		if ($recYukan['item4_use_kbn'] == 1) {
			$pdfObj->write_circle(array('x' => 417, 'y' => 554, 'width' => 30, 'height' => 20)); //湯灌時ドライ 
		} else {
			$pdfObj->write_circle(array('x' => 491, 'y' => 554, 'width' => 30, 'height' => 20)); //湯灌時ドライ
		}
		if ($recYukan['item5_use_kbn'] == 1) {
			$pdfObj->write_circle(array('x' => 141, 'y' => 586, 'width' => 30, 'height' => 20)); //守り刀
		} else {
			$pdfObj->write_circle(array('x' => 215, 'y' => 586, 'width' => 30, 'height' => 20)); //守り刀
		}
		if ($recYukan['item6_use_kbn'] == 1) {
			$pdfObj->write_circle(array('x' => 417, 'y' => 586, 'width' => 30, 'height' => 20)); //おくりたたみ
		} else {
			$pdfObj->write_circle(array('x' => 491, 'y' => 586, 'width' => 30, 'height' => 20)); //おくりたたみ
		}
		if ($recYukan['item7_use_kbn'] == 1) {
			$pdfObj->write_circle(array('x' => 141, 'y' => 619, 'width' => 30, 'height' => 20)); //ハウスクリーニング
		} else {
			$pdfObj->write_circle(array('x' => 215, 'y' => 619, 'width' => 30, 'height' => 20)); //ハウスクリーニング
		}
		if ($recYukan['item8_use_kbn'] == 1) {
			$pdfObj->write_circle(array('x' => 403, 'y' => 619, 'width' => 60, 'height' => 20)); //ケアサービスより連絡
		} else {
			$pdfObj->write_circle(array('x' => 477, 'y' => 619, 'width' => 60, 'height' => 20)); //ケアサービスより連絡
		}

		$pdfObj->write_string(array('x' => 100, 'y' => 654, 'width' => 200, 'height' => 15), $recYukan['biko1']);  // 棺場所
		$pdfObj->write_strings(array('x' => 100, 'y' => 680, 'width' => 470, 'height' => 60), $recYukan['biko2']);  // 特記事項
	}

	/**
	 * 料理発注書（出前）
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
         * @param string $item_no               項目（メニュー）番号
	 * @return void
	 */
	private function outData0202($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time, $category_kbn, $item_no) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		if($this->_moushi_kbn == '2'){
			$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileNameHouji[$report_cd]);
			// 発注共通項目
			$pdfObj->hachu_cmn_out_houji($seko_no, $siire_cd, $report_cd, $hachu_time, $category_kbn);
		}else{
			$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);
			// 発注共通項目
			$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time, $category_kbn);
		}
		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
        $hachu_nm = App_Utils::getTantoNm();
		//フッダー項目を出力
		$pdfObj->hachu_footer_out2(470, 770, $seko_no, $hachu_nm);

		/*        $rec = DataMapper_SekoKihon::find( $db, array( "seko_no" => $seko_no ) );
		  if (count($rec) == 0) {
		  return self::ERR_STATUS_NODATA;
		  }
		  $recKihon = $rec[0]; */

//        $y = 273;
		$y = 298;
		//$row_height = 29.2;
		$row_height = 45.5;
		$row_count = 7;

		// カラムの設定
		$set_arr[] = array('x' => 30, 'y' => $y, 'width' => 115, 'height' => 15); //品名 
		$set_arr[] = array('x' => 110, 'y' => $y, 'width' => 70, 'height' => 15, 'type' => 'num'); //数量
		$set_arr[] = array('x' => 176, 'y' => $y, 'width' => 70, 'height' => 15, 'type' => 'num'); //単価
		$set_arr[] = array('x' => 248, 'y' => $y, 'width' => 70, 'height' => 15, 'type' => 'num'); //金額
		$set_arr[] = array('x' => 328, 'y' => $y, 'width' => 125, 'height' => 15, 'align' => 'C'); //届日時
		$set_arr[] = array('x' => 455, 'y' => $y, 'width' => 120, 'height' => 15); // 届先
		$set_arr[] = array('x' => 76, 'y' => $y + 21.8, 'width' => 115, 'height' => 15, 'font_size' => 10); // 住所１
		$set_arr[] = array('x' => 190, 'y' => $y + 21.8, 'width' => 135, 'height' => 15, 'font_size' => 10); // 住所２   
		$set_arr[] = array('x' => 362, 'y' => $y + 21.8, 'width' => 215, 'height' => 15, 'font_size' => 10); // 備考
		// 施行発注管理情報を取得
		$rec_ary = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd);

		// 値の設定
		$row_arr = array();
		$biko = '';
		$hashi = 0; //箸
		$torizara = 0; //とり皿
		$gurasu = 0; // グラス
		foreach ($rec_ary as $rec) {
			if ($rec['nonyu_ymd_inp'] === '1' && $rec['nonyu_ymd2_inp'] === '1') {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd'])) . '～' . date('G:i', strtotime($rec['nonyu_ymd2']));
			} else if ($rec['nonyu_ymd_inp'] === '1') {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd']));
			} else if ($rec['nonyu_ymd2_inp'] === '1') {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd2']));
			} else if (isset($rec['nonyu_ymd'])){
                $nonyu_dt = date('Y/n/j', strtotime($rec['nonyu_ymd']));
			} else {
				$nonyu_dt = null;
			}
			$row = array();
			$row[] = $rec['shohin_nm'];

			$row[] = $rec['hachu_suryo'];
			$row[] = $rec['tnk'];
			$row[] = $rec['hachu_suryo'] * $rec['tnk'];
			$row[] = $nonyu_dt;
			//$row[] = $rec['nonyu_ymd'];
			// 届先の優先順は納入場所→住所	2014/08/11 add
			// （ただし、納入場所は必須なので、住所になることはないと思う）
//			if (isset($rec['nonyu_nm'])) {
//	            $row[] = $rec['nonyu_nm'];
//			} else {
//				$row[] = NULL;
//	            $row[] = $rec['nonyu_addr1'];
//	            $row[] = $rec['nonyu_addr2'];
//			}

			$row[] = $rec['nonyu_nm'];

			$row[] = $rec['nonyu_addr1'];
			$row[] = $rec['nonyu_addr2'];
			$row[] = $rec['hd_biko1'];
//			if (isset($rec['nonyu_addr1'])) {
//				$row[] = NULL;
//	            $row[] = $rec['nonyu_addr1'];
//	            $row[] = $rec['nonyu_addr2'];
//			} else {
//	            $row[] = $rec['nonyu_nm'];
//			}
			$row_arr[] = $row;
//			$hashi = $hashi + $rec['n_free1']; //箸
//			$torizara = $torizara + $rec['n_free2']; //とり皿
//			$gurasu = $gurasu + $rec['n_free3']; // グラス
			if (count($row_arr) >= $row_count)	{
				break;
			}	
		}
        // 備考データを取得
		$row = DataMapper_PdfCommon::findSekoKanri($db, array('seko_no' => $seko_no, 'seko_no_sub' => '00', 'data_sbt' => '3', 'item_no' => $item_no));
		if(count($row)){
			$pdfObj->write_strings(array('x' => 35, 'y' => 625, 'width' => 540, 'height' => 110, 'font_size' => 12.3), $row[0]['v_free1']);
		}
		if (count($row_arr) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr, $row_height, $row_arr);

			// 備考
//			if ($hashi == 0 && $torizara == 0 && $gurasu == 0) {
//				$biko = null;
//			} else {
//				$biko = '箸 ' . $hashi . ' 膳　' . 'とり皿 ' . $torizara . ' 枚　' . 'グラス ' . $gurasu . ' コ　';
//			}
//            $pdfObj->write_strings(array('x' => 30, 'y' => 650, 'width' => 540, 'height' => 37, 'font_size' => 12), $biko);
			$pdfObj->write_strings(array('x' => 30, 'y' => 625, 'width' => 540, 'height' => 37, 'font_size' => 12), $biko);
		}
	}

	/**
	 * 商品発注書
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @version 2014/12/05 フォントサイズを変更 Kayo
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
         * @param string $item_no               項目（メニュー）番号
	 * @return void
	 */
	private function outData0203($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time, $item_no) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
                if($this->_moushi_kbn == '2'){
                    $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileNameHouji[$report_cd]);
                    // 発注共通項目
                    $pdfObj->hachu_cmn_out_houji($seko_no, $siire_cd, $report_cd, $hachu_time);
                }else{
                    $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);
                    // 発注共通項目
                    $pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
                }
		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
                $hachu_nm = App_Utils::getTantoNm();
		//フッダー項目を出力
		$pdfObj->hachu_footer_out2(470, 770, $seko_no, $hachu_nm);

		/* $rec = DataMapper_SekoKihon::find( $db, array( "seko_no" => $seko_no ) );
		  if (count($rec) == 0) {
		  return self::ERR_STATUS_NODATA;
		  }
		  $recKihon = $rec[0]; */

		$meisai_top = 324;
		$row_height = 40.5;
//        $row_count = 12;
		$row_count = 7;

		// カラムの設定
		$set_arr[] = array('x' => 45, 'y' => $meisai_top, 'width' => 70, 'height' => 15, 'font_size' => 11);
		$set_arr[] = array('x' => 125, 'y' => $meisai_top, 'width' => 115, 'height' => 15, 'font_size' => 11);
//        $set_arr[] = array('x' => 250, 'y' => $meisai_top, 'width' =>  50, 'height' => 15, 'font_size' => 11, 'type' => 'num', 'precision' => 2);
		$set_arr[] = array('x' => 250, 'y' => $meisai_top, 'width' => 50, 'height' => 15, 'font_size' => 11, 'type' => 'num');
		$set_arr[] = array('x' => 305, 'y' => $meisai_top, 'width' => 34, 'height' => 15, 'font_size' => 11, 'type' => 'num');
		$set_arr[] = array('x' => 342, 'y' => $meisai_top, 'width' => 110, 'height' => 15, 'font_size' => 11, 'align' => 'C');
		$set_arr[] = array('x' => 455, 'y' => $meisai_top, 'width' => 115, 'height' => 15, 'font_size' => 11);
		$set_arr[] = array('x' => 100, 'y' => $meisai_top + 13, 'width' => 470, 'height' => 15, 'font_size' => 11);
		$set_arr[] = array('x' => 100, 'y' => $meisai_top + 27, 'width' => 470, 'height' => 15, 'font_size' => 11);

		// 施行発注管理情報を取得
		$rec_ary = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd);

		// 値の設定
		$row_arr = array();
		foreach ($rec_ary as $rec) {
			if ($rec['nonyu_ymd_inp'] === '1' && $rec['nonyu_ymd2_inp'] === '1') {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd'])) . '～' . date('G:i', strtotime($rec['nonyu_ymd2']));
			} else if ($rec['nonyu_ymd_inp'] === '1') {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd']));
			} else if ($rec['nonyu_ymd2_inp'] === '1') {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd2']));
			} else if (isset($rec['nonyu_ymd'])){
                                $nonyu_dt = date('Y/n/j', strtotime($rec['nonyu_ymd']));
			} else {
				$nonyu_dt = null;
			}
			$row = array();
			$row[] = $rec['shohin_kbn_nm'];
			$row[] = $rec['shohin_nm'];
			//$row[] = $rec['tnk'];
			if ($rec['ha_rp_kbn'] == 22) {
				$row[] = $rec['hachu_tnk'];
			} else {
				$row[] = $rec['tnk'];
			}
			$row[] = $rec['hachu_suryo'];
			$row[] = $nonyu_dt;
			$row[] = $rec['nonyu_nm'];
			$row[] = $rec['nonyu_yubin_no'] . '　' . $rec['nonyu_addr1'] . '　' . $rec['nonyu_addr2'];
			$row[] = $rec['hd_biko1'] . '　' . $rec['hd_biko2'];
			$row_arr[] = $row;
			if (count($row_arr) >= $row_count)
				break;
		}
                // 備考データを取得
		$row = DataMapper_PdfCommon::findSekoKanri($db, array('seko_no' => $seko_no, 'seko_no_sub' => '00', 'data_sbt' => '3', 'item_no' => $item_no));
                if(count($row)){
                    $pdfObj->write_strings(array('x' => 35, 'y' => 625, 'width' => 540, 'height' => 110, 'font_size' => 12.3), $row[0]['v_free1']);
                }
		if (count($row_arr) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
		}
	}
	
	/**
	 * 商品発注書
	 * 
	 * <AUTHOR> Otake
	 * @since      2017/04/13
	 * @version 2014/12/05 フォントサイズを変更 Kayo
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd	帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0203A($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);
		
		// 通夜日時・葬儀場所日時 2017/04/13 ADD otake 
		$recOrderHead = DataMapper_PdfCommon::getOrderHead($db, $seko_no, $siire_cd);
		$tyRec = DataMapper_SekoNitei::find($db, array('seko_no' => $recOrderHead[0]['free2_cd'], 'nitei_kbn' => 4));
		$sgRec = DataMapper_SekoNitei::find($db, array('seko_no' => $recOrderHead[0]['free2_cd'], 'nitei_kbn' => 11));
		if (count($tyRec) > 0) {//通夜
			$tydt = date('Y/n/j G:i', strtotime($tyRec[0]['nitei_ymd']));
			$pdfObj->write_string(array('x' => 333, 'y' => 200, 'width' => 242, 'height' => 15), $tydt);
		}
		if (count($sgRec) > 0) {//葬儀
			$sgdt = date('Y/n/j G:i', strtotime($sgRec[0]['nitei_ymd']));
			$pdfObj->write_string(array('x' => 333, 'y' => 217, 'width' => 242, 'height' => 15), $sgdt);
			$pdfObj->write_string(array('x' => 333, 'y' => 234, 'width' => 242, 'height' => 15), $sgRec[0]['basho_nm']);
		}

		/* $rec = DataMapper_SekoKihon::find( $db, array( "seko_no" => $seko_no ) );
		  if (count($rec) == 0) {
		  return self::ERR_STATUS_NODATA;
		  }
		  $recKihon = $rec[0]; */

		$meisai_top = 293;
		$row_height = 51.5;//45.5;
		$row_count = 7;//8;

		// カラムの設定
//		$set_arr[] = array('x' => 45, 'y' => $meisai_top, 'width' => 70, 'height' => 15, 'font_size' => 11);					//区分名	shohin_kbn_nm 2017/04/13 Otake 無効化
		$set_arr[] = array('x' => 30, 'y' => $meisai_top, 'width' => 115, 'height' => 15, 'font_size' => 14);					//商品名	shohin_nm
//		$set_arr[] = array('x' => 250, 'y' => $meisai_top, 'width' =>  50, 'height' => 15, 'font_size' => 11, 'type' => 'num', 'precision' => 2);
		$set_arr[] = array('x' => 195, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'font_size' => 14, 'type' => 'num');	//単価		hachu_tnk or tnk
		$set_arr[] = array('x' => 155, 'y' => $meisai_top, 'width' => 34, 'height' => 15, 'font_size' => 14, 'type' => 'num');	//数量		hachu_suryo
		$set_arr[] = array('x' => 265, 'y' => $meisai_top, 'width' => 60, 'height' => 15, 'font_size' => 14, 'type' => 'num');	//金額		$tanka * $rec['hachu_suryo']
		$set_arr[] = array('x' => 333, 'y' => $meisai_top, 'width' => 114, 'height' => 15, 'font_size' => 14, 'align' => 'C');	//届日時	$nonyu_dt
		$set_arr[] = array('x' => 455, 'y' => $meisai_top, 'width' => 115, 'height' => 15, 'font_size' => 14);					//届先		nonyu_nm
		$set_arr[] = array('x' => 77, 'y' => $meisai_top + 19, 'width' => 498, 'height' => 15, 'font_size' => 10);				//届先住所	nonyu_yubin_no .nonyu_addr1 .nonyu_addr2
		$set_arr[] = array('x' => 77, 'y' => $meisai_top + 34, 'width' => 498, 'height' => 15, 'font_size' => 10);				//名札		nafuda_nm
//		$set_arr[] = array('x' => 100, 'y' => $meisai_top + 40, 'width' => 470, 'height' => 15, 'font_size' => 11);				//備考欄	hd_biko1 .hd_biko2 2017/04/13 otake 無効化

		// 施行発注管理情報を取得
		$rec_ary = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd);

		// 値の設定
		$row_arr = array();
		$testring = '';
		foreach ($rec_ary as $rec) {
			if (isset($rec['nonyu_ymd']) && isset($rec['nonyu_ymd2'])) {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd'])) . '～' . date('G:i', strtotime($rec['nonyu_ymd2']));
			} else if (isset($rec['nonyu_ymd'])) {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd']));
			} else {
				$nonyu_dt = null;
			}
			$row = array();
//			$row[] = $rec['shohin_kbn_nm'];　//無効化 2017/04/13 Otake
			$row[] = $rec['shohin_nm'];
			//$row[] = $rec['tnk'];
			if ($rec['ha_rp_kbn'] == 22) {
				$row[] = $tanka = $rec['hachu_tnk'];
			} else {
				$row[] = $tanka = $rec['tnk'];
			}
			$row[] = $rec['hachu_suryo'];
			$row[] = $tanka * $rec['hachu_suryo'];
			$row[] = $nonyu_dt;
			$row[] = $rec['nonyu_nm'];
			$row[] = '〒' .$rec['nonyu_yubin_no'] . '　' . $rec['nonyu_addr1'] . '　' . $rec['nonyu_addr2'];
			$row[] = $rec['nafuda_nm'];
//			$row[] = $rec['hd_biko1'] . '　' . $rec['hd_biko2']; //備考欄を利用する場合はこれと$set_arrを有効化する。2017/04/13 otake
			$row_arr[] = $row;
			
			if (count($row_arr) >= $row_count) break;
		}

		if (count($row_arr) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
		}
	}

	/**
	 * 商品発注書（返礼品）
	 * 
	 * <AUTHOR> Yanagiso
	 * @since      2015/03/18
	 * @version 2014/12/05 フォントサイズを変更 Kayo
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0203_sub($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		/* $rec = DataMapper_SekoKihon::find( $db, array( "seko_no" => $seko_no ) );
		  if (count($rec) == 0) {
		  return self::ERR_STATUS_NODATA;
		  }
		  $recKihon = $rec[0]; */

//        $meisai_top = 298;
//        $row_height = 40.6;
//        $row_count = 12;    
		$meisai_top = 328;
		$row_height = 23.4;
		$row_count = 15;
		//       //test
		//       $meisai_top = 324.5;
		//       $row_height = 31.2;
		//       $row_count = 11;
		// カラムの設定
//        $set_arr[] = array('x' =>  45, 'y' => $meisai_top, 'width' =>  70, 'height' => 15, 'font_size' => 11);
//        $set_arr[] = array('x' => 125, 'y' => $meisai_top, 'width' => 115, 'height' => 15, 'font_size' => 11);
//        $set_arr[] = array('x' => 250, 'y' => $meisai_top, 'width' =>  50, 'height' => 15, 'font_size' => 11, 'type' => 'num', 'precision' => 2);
//        $set_arr[] = array('x' => 305, 'y' => $meisai_top, 'width' =>  34, 'height' => 15, 'font_size' => 11, 'type' => 'num');
//        $set_arr[] = array('x' => 342, 'y' => $meisai_top, 'width' => 110, 'height' => 15, 'font_size' => 11, 'align' => 'C');
//        $set_arr[] = array('x' => 455, 'y' => $meisai_top, 'width' => 115, 'height' => 15, 'font_size' => 11);
//        $set_arr[] = array('x' => 100, 'y' => $meisai_top + 13, 'width' => 470, 'height' => 15, 'font_size' => 11);
//        $set_arr[] = array('x' => 100, 'y' => $meisai_top + 27, 'width' => 470, 'height' => 15, 'font_size' => 11);
		$set_arr[] = array('x' => 65, 'y' => $meisai_top, 'width' => 70, 'height' => 15, 'font_size' => 11); //発注日
		$set_arr[] = array('x' => 149, 'y' => $meisai_top, 'width' => 70, 'height' => 15, 'font_size' => 11); //届け日
		$set_arr[] = array('x' => 183, 'y' => $meisai_top, 'width' => 110, 'height' => 15, 'font_size' => 11, 'align' => 'C'); //返礼品区分
		$set_arr[] = array('x' => 295, 'y' => $meisai_top, 'width' => 145, 'height' => 15, 'font_size' => 11); //商品名
		$set_arr[] = array('x' => 400, 'y' => $meisai_top, 'width' => 100, 'height' => 15, 'font_size' => 11, 'type' => 'num'); //単価
		$set_arr[] = array('x' => 490, 'y' => $meisai_top, 'width' => 70, 'height' => 15, 'font_size' => 11, 'type' => 'num'); //数量
//        $set_arr[] = array('x' =>  75, 'y' => $meisai_top + 15.4, 'width' =>  570, 'height' => 15, 'font_size' => 11);//備考
		// 施行発注管理情報を取得
		$rec_ary = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd);

		// 値の設定
		$row_arr = array();
		foreach ($rec_ary as $rec) {
//            if (isset($rec['nonyu_ymd']) && isset($rec['nonyu_ymd2'])) {
//                    $nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd'])).'～'.date('G:i', strtotime($rec['nonyu_ymd2']));
//            } else if (isset($rec['nonyu_ymd'])) {
//                    $nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd']));
			if (isset($rec['nonyu_ymd'])) {
				$nonyu_dt = date('n/j', strtotime($rec['nonyu_ymd']));
			} else {
				$nonyu_dt = null;
			}
			$row = array();
//            $row[] = $rec['shohin_kbn_nm'];
//            $row[] = $rec['shohin_nm'];
//            $row[] = $rec['tnk'];
//            $row[] = $rec['hachu_suryo'];
//            $row[] = $nonyu_dt;
//            $row[] = $rec['nonyu_nm'];
//            $row[] = $rec['nonyu_yubin_no'].'　'.$rec['nonyu_addr1'].'　'.$rec['nonyu_addr2'];
//            $row[] = $rec['hd_biko1'].'　'.$rec['hd_biko2'];
			$row[] = date('n/j', strtotime($hachu_time));
			$row[] = $nonyu_dt;
			$row[] = $rec['shohin_kbn_nm'];
			$row[] = $rec['shohin_nm'];
			$row[] = $rec['tnk'];
			$row[] = $rec['hachu_suryo'];
//            $row[] = $rec['hd_biko1'];
			//$row[] = $rec['nonyu_nm'];
			$row_arr[] = $row;
			if (count($row_arr) >= $row_count)
				break;
		}

		if (count($row_arr) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
		}

		$pdfObj->write_string(array('x' => 23, 'y' => 687, 'width' => 58, 'height' => 15, 'font_size' => 8), $rec_ary[0]['bumon_lnm']);
//        $center_nm = '川崎ギフトセンター';
//        $center_nm = '高津ギフトセンター';
//        $pdfObj->write_string(array('x' => 203, 'y' => 687, 'width' => 54, 'height' => 15, 'font_size' => 8), $center_nm);  
		$pdfObj->write_string(array('x' => 382, 'y' => 687, 'width' => 58, 'height' => 15, 'font_size' => 8), $rec_ary[0]['bumon_lnm']);
	}

    /**
     * 車輌発注書
     * 
     * <AUTHOR> Sato
     * @since      2014/03/25
     * @param object $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db DB
     * @param string $seko_no		施工番号
     * @param array $hachu_no_ary	施行発注管理情報 配列
     * @param string $siire_cd		仕入先コード
     * @param string $report_cd		帳票コード
     * @param string $hachu_time	発注日時
     * @return void
     */
    private function outData0204($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time)
    {
        $pdfObj->set_default_font_size(12);
        $pdfObj->set_default_font_family_h('kozgopromedium');
        if($this->_moushi_kbn == '2'){
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileNameHouji[$report_cd]);
            // 発注共通項目
            $pdfObj->hachu_cmn_out_syaryo_houji($seko_no, $siire_cd, $report_cd, $hachu_time);
        }else{
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);
            // 発注共通項目
            $pdfObj->hachu_cmn_out_syaryo($seko_no, $siire_cd, $report_cd, $hachu_time);
        }

        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        //フッダー項目を出力
        $pdfObj->hachu_footer_out(470, 790, $seko_no, null);

        /*$rec = DataMapper_SekoKihon::find( $db, array( "seko_no" => $seko_no ) );
        if (count($rec) == 0) {
            return self::ERR_STATUS_NODATA;
        }
		$recKihon = $rec[0];*/

        $row_height = 29;

        // カラムの設定
        $set_arr[] = array('x' =>  30, 'y' => 310, 'width' => 115, 'height' => 15);                     // 0
        $set_arr[] = array('x' => 150, 'y' => 304, 'width' =>  40, 'height' => 15, 'type' => 'num');    // 1
        $set_arr[] = array('x' => 150, 'y' => 315, 'width' =>  40, 'height' => 15, 'type' => 'num');    // 乗車人数 2
        $set_arr[] = array('x' => 195, 'y' => 310, 'width' => 100, 'height' => 15, 'type' => 'time', 'format' => "n月　j日　G時　i分"); // 3
        $set_arr[] = array('x' => 305, 'y' => 310, 'width' => 145, 'height' => 15, 'align' => 'L'); // 行程中央 4
        $set_arr[] = array('x' => 305, 'y' => 302, 'width' => 145, 'height' => 15, 'align' => 'L'); // 行程上   5
        $set_arr[] = array('x' => 305, 'y' => 315, 'width' => 145, 'height' => 15, 'align' => 'L'); // 行程下   6
        // 施行発注管理情報を取得
        $rec_ary = $this->getSekoSyaryoMsi($db, $seko_no, $hachu_no_ary, $siire_cd, 1);

        // 値の設定　寝台・移送車
        $row_arr1 = array();
        foreach ($rec_ary as $rec) {
            $row = array();
            $row[] = $rec['rq_car_nm'];//$rec['shohin_nm'];             // 車種 2015/03/17 UPD Kayo
            $row[] = $rec['car_suryo'];             // 台数
            $row[] = $rec['person_suryo'];          // 乗車人数　2016/03/21 ADD Kayo
            $row[] = $rec['departure_dt'];          // 出発時間　
            // 行程
            $koutei1 =   null;
            $koutei2 =   null;
            if ((strlen($rec['route_dep_nm']) == 0) && (strlen($rec['route_via_nm']) == 0) && (strlen($rec['route_arr_nm']) == 0)){
                $koutei  =   null;
                $koutei1 =   null;
                $koutei2 =   null;
            }
            else if (strlen($rec['route_via_nm'])>0) {
                //$koutei =  $rec['route_dep_nm'].'→'.$rec['route_via_nm'].'→'.$rec['route_arr_nm'];
                if (strlen($rec['route_dep_nm']) == 0) {
                    $koutei =  $rec['route_via_nm'].'→'.$rec['route_arr_nm'];    
                }else{
                    $koutei =  $rec['route_dep_nm'].'→'.$rec['route_via_nm'].'→'.$rec['route_arr_nm'];    
                }
                
                if (mb_strlen($koutei, 'UTF-8')>=15) {
                   $koutei1 =   mb_substr($koutei,0,15, 'UTF-8');
                   $koutei2 =   mb_substr($koutei,15,mb_strlen($koutei) - 15, 'UTF-8');
                } else {
                   $koutei1 =   $koutei;
                   $koutei2 =   null;
                }
            } else {
                $koutei = $rec['route_dep_nm'].'→'.$rec['route_arr_nm'];
                if (mb_strlen($koutei, 'UTF-8')>=15) {
                   $koutei1 =   mb_substr($koutei, 0, 15, 'UTF-8');
                   $koutei2 =   mb_substr($koutei,15,strlen($koutei) - 15, 'UTF-8');
                } else {
                   $koutei1 =   $koutei;
                   $koutei2 =   null;
                }
			}
            if (strlen($koutei1)>0) {
                if (strlen($koutei2)>0) {
                    $row[] = null;              // 行程中央　
                    $row[] = $koutei1;          // 行程上　
                    $row[] = $koutei2;          // 行程下　
                } else {
                    $row[] = $koutei1;          // 行程中央　
                    $row[] = null;              // 行程上　
                    $row[] = null;              // 行程下　
                }
            } else {
                if (strlen($koutei2)>0) {
                    $row[] = $koutei2;          // 行程中央　
                    $row[] = null;              // 行程上　
                    $row[] = null;              // 行程下
                } else {
                    $row[] = null;              // 行程中央　
                    $row[] = null;              // 行程上　
                    $row[] = null;              // 行程下
                }    
            }    
            $row_arr1[] = $row;
            if (count($row_arr1) >= 2) break;
        }

        if (count($row_arr1) > 0) {
            // 表形式で書き込む
            $pdfObj->write_table_simple($set_arr, $row_height, $row_arr1);
        }

        // カラムの設定
        for ($i = 0; $i < count($set_arr); $i++) {
             if ($i <= 0) {
                $set_arr[$i]['y'] = 420;
            } else if ($i == 1) {
                $set_arr[$i]['y'] = 412;
            } else if ($i == 2) {
                $set_arr[$i]['y'] = 424;
            } else if ($i <= 4) {
                $set_arr[$i]['y'] = 420;
            } else if ($i <= 5) {
                $set_arr[$i]['y'] = 412;
            } else {
                $set_arr[$i]['y'] = 424;
            }    
       }

        // 施行発注管理情報を取得
        $rec_ary = $this->getSekoSyaryoMsi($db, $seko_no, $hachu_no_ary, $siire_cd, 2);

        // 値の設定　霊柩車
        $row_arr2 = array();
        foreach ($rec_ary as $rec) {
            $row = array();
            $row[] = $rec['rq_car_nm'];//$rec['shohin_nm'];
            $row[] = $rec['car_suryo'];
            $row[] = $rec['person_suryo'];          // 乗車人数　2016/03/21 ADD Kayo
            $row[] = $rec['departure_dt'];
            // 行程
            $koutei1 =   null;
            $koutei2 =   null;
            if ((strlen($rec['route_dep_nm']) == 0) && (strlen($rec['route_via_nm']) == 0) && (strlen($rec['route_arr_nm']) == 0)){
                $koutei  =   null;
                $koutei1 =   null;
                $koutei2 =   null;
            }
            else if (strlen($rec['route_via_nm'])>0) {
                //$koutei =  $rec['route_dep_nm'].'→'.$rec['route_via_nm'].'→'.$rec['route_arr_nm'];
                if (strlen($rec['route_dep_nm']) == 0) {
                    $koutei =  $rec['route_via_nm'].'→'.$rec['route_arr_nm'];    
                }else{
                    $koutei =  $rec['route_dep_nm'].'→'.$rec['route_via_nm'].'→'.$rec['route_arr_nm'];    
                }
                
                $leng   =  mb_strlen($koutei, 'UTF-8');
                if ($leng >=15) {
                   $koutei1 =    mb_substr($koutei,0,15, 'UTF-8');
                   $koutei2 =    mb_substr($koutei,15,mb_strlen($koutei) - 15, 'UTF-8');
                } else {
                   $koutei1 =   $koutei;
                   $koutei2 =   null;
                }
            } else {
                $koutei = $rec['route_dep_nm'].'→'.$rec['route_arr_nm'];
                //if (strlen(mb_strlen, 'UTF-8')>=15) {
                if (mb_strlen($koutei, 'UTF-8')>=15) {
                   $koutei1 =    mb_substr($koutei,0,15, 'UTF-8');
                   $koutei2 =    mb_substr($koutei,15,mb_strlen($koutei) - 15, 'UTF-8');
                } else {
                   $koutei1 =   $koutei;
                   $koutei2 =   null;
                }
			}
            if (strlen($koutei1)>0) {
                if (strlen($koutei2)>0) {
                    $row[] = null;              // 行程中央　
                    $row[] = $koutei1;          // 行程上　
                    $row[] = $koutei2;          // 行程下　
                } else {
                    $row[] = $koutei1;          // 行程中央　
                    $row[] = null;              // 行程上　
                    $row[] = null;              // 行程下　
                }
            } else {
                if (strlen($koutei2)>0) {
                    $row[] = $koutei2;          // 行程中央　
                    $row[] = null;              // 行程上　
                    $row[] = null;              // 行程下
                } else {
                    $row[] = null;              // 行程中央　
                    $row[] = null;              // 行程上　
                    $row[] = null;              // 行程下
                }    
            }    
            $row_arr2[] = $row;
            if (count($row_arr2) >= 2) break;
        }

        if (count($row_arr2) > 0) {
            // 表形式で書き込む
            $pdfObj->write_table_simple($set_arr, $row_height, $row_arr2);
        }

        // カラムの設定
        for ($i = 0; $i < count($set_arr); $i++) {
            if ($i <= 0) {
                $set_arr[$i]['y'] = 530;
            } else if ($i == 1) {
                $set_arr[$i]['y'] = 522;
            } else if ($i == 2) {
                $set_arr[$i]['y'] = 535;
            } else if ($i <= 4) {
                $set_arr[$i]['y'] = 530;
            } else if ($i <= 5) {
                $set_arr[$i]['y'] = 522;
            } else {
                $set_arr[$i]['y'] = 536;
            }    
        }
 
        // 施行発注管理情報を取得
        $rec_ary = $this->getSekoSyaryoMsi($db, $seko_no, $hachu_no_ary, $siire_cd, 3);

        // 値の設定　送迎車輌
        $row_arr3 = array();
        foreach ($rec_ary as $rec) {
            $row = array();
            $row[] = $rec['rq_car_nm'];//$rec['shohin_nm'];
            $row[] = $rec['car_suryo'];
            $row[] = $rec['person_suryo'];          // 乗車人数　2016/03/21 ADD Kayo
            $row[] = $rec['departure_dt'];
           // 行程
            $koutei1 =   null;
            $koutei2 =   null;
            if ((strlen($rec['route_dep_nm']) == 0) && (strlen($rec['route_via_nm']) == 0) && (strlen($rec['route_arr_nm']) == 0)){
                $koutei  =   null;
                $koutei1 =   null;
                $koutei2 =   null;
            }
            else if (strlen($rec['route_via_nm'])>0) {
                //$koutei =  $rec['route_dep_nm'].'→'.$rec['route_via_nm'].'→'.$rec['route_arr_nm'];
                if (strlen($rec['route_dep_nm']) == 0) {
                    $koutei =  $rec['route_via_nm'].'→'.$rec['route_arr_nm'];    
                }else{
                    $koutei =  $rec['route_dep_nm'].'→'.$rec['route_via_nm'].'→'.$rec['route_arr_nm'];    
                }
                
                if (mb_strlen($koutei, 'UTF-8')>=15) {
                   $koutei1 =    mb_substr($koutei,0,15, 'UTF-8');
                   $koutei2 =    mb_substr($koutei,15,mb_strlen($koutei) - 15, 'UTF-8');
                } else {
                   $koutei1 =   $koutei;
                   $koutei2 =   null;
                }
            } else {
                $koutei = $rec['route_dep_nm'].'→'.$rec['route_arr_nm'];
                
                if (mb_strlen($koutei, 'UTF-8')>=15) {
                   $koutei1 =    mb_substr($koutei,0,15, 'UTF-8');
                   $koutei2 =    mb_substr($koutei,15,mb_strlen($koutei) - 15, 'UTF-8');
                } else {
                   $koutei1 =   $koutei;
                   $koutei2 =   null;
                }
			}
            if (strlen($koutei1)>0) {
                if (strlen($koutei2)>0) {
                    $row[] = null;              // 行程中央　
                    $row[] = $koutei1;          // 行程上　
                    $row[] = $koutei2;          // 行程下　
                } else {
                    $row[] = $koutei1;          // 行程中央　
                    $row[] = null;              // 行程上　
                    $row[] = null;              // 行程下　
                }
            } else {
                if (strlen($koutei2)>0) {
                    $row[] = $koutei2;          // 行程中央　
                    $row[] = null;              // 行程上　
                    $row[] = null;              // 行程下
                } else {
                    $row[] = null;              // 行程中央　
                    $row[] = null;              // 行程上　
                    $row[] = null;              // 行程下
                }    
            }    
            $row_arr3[] = $row;
            if (count($row_arr3) >= 4) break;
        }

        if (count($row_arr3) > 0) {
            // 表形式で書き込む
            $pdfObj->write_table_simple($set_arr, $row_height, $row_arr3);
        }
        
        // 備考
        $row = DataMapper_Pdf0204::getHead($db, $seko_no);
        if (isset($row)) {
                $pdfObj->write_strings(array('x' => 30, 'y' => 665, 'width' => 540, 'height' => 70), $row['biko']);
        }
    }

	/**
	 * 喪服発注書
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0205($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		$row_height = 25;

		// カラムの設定
//        $set_arr[] = array('x' =>  38, 'y' => 262, 'width' => 168, 'height' => 15, 'font_size' => 11);  // 商品名（上）
//        $set_arr[] = array('x' =>  38, 'y' => 270, 'width' => 168, 'height' => 15, 'font_size' => 11);  // 商品名（中）
//        $set_arr[] = array('x' =>  38, 'y' => 273, 'width' => 168, 'height' => 15, 'font_size' => 10);  // 商品名（下）
//        $set_arr[] = array('x' => 205, 'y' => 270, 'width' =>  40, 'height' => 15, 'type' => 'num');
//        $set_arr[] = array('x' => 250, 'y' => 270, 'width' => 130, 'height' => 15);	// 準備場所
//        $set_arr[] = array('x' => 250, 'y' => 263, 'width' => 130, 'height' => 15, 'font_size' => 8);	// 住所１
//        $set_arr[] = array('x' => 250, 'y' => 275, 'width' => 130, 'height' => 15, 'font_size' => 8);	// 住所２
//        $set_arr[] = array('x' => 390, 'y' => 270, 'width' => 100, 'height' => 15, 'type' => 'time', 'format' => "n 月 j 日  G 時 i 分", 'align' => 'C');
//        $set_arr[] = array('x' => 495, 'y' => 270, 'width' =>  25, 'height' => 15, 'align' => 'C');	// 発注
		$set_arr[] = array('x' => 38, 'y' => 288, 'width' => 168, 'height' => 15, 'font_size' => 11);  // 商品名（上）
		$set_arr[] = array('x' => 38, 'y' => 296, 'width' => 168, 'height' => 15, 'font_size' => 11);  // 商品名（中）
		$set_arr[] = array('x' => 38, 'y' => 299, 'width' => 168, 'height' => 15, 'font_size' => 10);  // 商品名（下）
		$set_arr[] = array('x' => 205, 'y' => 295, 'width' => 40, 'height' => 15, 'type' => 'num');
		$set_arr[] = array('x' => 250, 'y' => 295, 'width' => 130, 'height' => 15); // 準備場所
		$set_arr[] = array('x' => 250, 'y' => 288, 'width' => 130, 'height' => 15, 'font_size' => 8); // 住所１
		$set_arr[] = array('x' => 250, 'y' => 300, 'width' => 130, 'height' => 15, 'font_size' => 8); // 住所２
		$set_arr[] = array('x' => 390, 'y' => 295, 'width' => 100, 'height' => 15, 'type' => 'time', 'format' => "n 月 j 日  G 時 i 分", 'align' => 'C');
		$set_arr[] = array('x' => 495, 'y' => 295, 'width' => 25, 'height' => 15, 'align' => 'C'); // 発注
		$sex = DataMapper_CodeNmMst::find($db, array('code_kbn' => '0080'));
		$kimono = array('袷', '絽');

		// 施行発注喪服情報明細を取得
		$rec_ary = $this->getSekoMofukuMsi($db, $seko_no, $hachu_no_ary, $siire_cd, 0);

		// 値の設定　貸衣裳
		$sum1 = 0;  // 着付時貸出品
		$row_arr1 = array();
		foreach ($rec_ary as $rec) {
			$row = array();
			switch ($rec['kasi_kbn']) {
				case 1:
					$shohin_nm1 = $rec['shohin_nm'] . ' (' . $sex[0]['kbn_value_lnm'] . ')';
					//$shohin_nm2 = '身長'.$rec['ma_height'].' 体重'.$rec['ma_weight'].' W'.$rec['ma_waist'].' 股下'.$rec['ma_inseam'];
					$ma_height = $rec['ma_height'];
					$ma_weight = $rec['ma_weight'];
					$ma_waist = $rec['ma_waist'];
					$ma_inseam = $rec['ma_inseam'];
					if ($ma_height == 0.0) {
						$ma_height = '       ';
					}
					if ($ma_weight == 0.0) {
						$ma_weight = '      ';
					}
					if ($ma_waist == 0.0) {
						$ma_waist = '      ';
					}
					if ($ma_inseam == 0.0) {
						$ma_inseam = '      ';
					}
					$shohin_nm2 = '身長' . $ma_height . ' 体重' . $ma_weight . ' W' . $ma_waist . ' 股下' . $ma_inseam;
					break;
				case 2:
					$shohin_nm1 = $rec['shohin_nm'] . ' (' . $sex[1]['kbn_value_lnm'] . ')';
					//$shohin_nm2 = $rec['fm_size_nm'].' 年齢'.$rec['fm_age'].'才';
					$fm_age = $rec['fm_age'];
					if ($fm_age == 0.0) {
						$fm_age = '    ';
					}
					$shohin_nm2 = $rec['fm_size_nm'] . ' 年齢' . $fm_age . '才';
					break;
				case 3:
					$shohin_nm1 = $rec['shohin_nm'];
					//$shohin_nm2 = '着物'.$rec['size_nm'].' '.$kimono[$rec['kimono_kbn']];
					$size_nm = $rec['size_nm'];
					$kimono_kbn = $rec['kimono_kbn'];
					if ($size_nm == '') {
						$size_nm = '      ';
					}
					if (!isset($kimono_kbn)) {
						$kimono_kbn = 0;
					}
					$shohin_nm2 = '着物' . $size_nm . ' ' . $kimono[$kimono_kbn];
					break;
				default:
					continue;
			}
			$row[] = $shohin_nm1;
			$row[] = null;
			$row[] = $shohin_nm2;
			$row[] = $rec['hachu_suryo'];
			// 届先の優先順は納入場所→住所	2014/08/11 add
			// （ただし、納入場所は必須なので、住所になることはないと思う）
			if (isset($rec['nonyu2_nm'])) {
				$row[] = $rec['nonyu2_nm'];
				$row[] = NULL;
				$row[] = NULL;
			} else {
				$row[] = NULL;
				$row[] = $rec['nonyu_addr1'];
				$row[] = $rec['nonyu_addr2'];
			}
			$row[] = $rec['nonyu_ymd'];
			$row[] = $rec['order_flg'];
			$row_arr1[] = $row;
			$sum1 += $rec['prc'];  // 着付時貸出品
			if (count($row_arr1) >= 8)
				break;
		}

		if (count($row_arr1) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr, $row_height, $row_arr1);
		}

//        $pdfObj->write_num(array('x' =>390, 'y' =>558, 'width' => 160, 'height' => 15), $sum1); // 着付時貸出品
		$pdfObj->write_num(array('x' => 113, 'y' => 582, 'width' => 160, 'height' => 15), $sum1); // 着付時貸出品
		// カラムの設定
		for ($i = 0; $i < count($set_arr); $i++) {
			$set_arr[$i]['y'] += 214;
		}

		// 施行発注喪服情報明細を取得
		$rec_ary = $this->getSekoMofukuMsi($db, $seko_no, $hachu_no_ary, $siire_cd, 1);

		// 値の設定　販売品
		$sum2 = 0;  // 着付時販売品
		$row_arr2 = array();
		foreach ($rec_ary as $rec) {
			$row = array();
			if (isset($rec['size_nm'])) {
				$row[] = $rec['shohin_nm'] . ' (' . $rec['size_nm'] . ')';
			} else {
				$row[] = $rec['shohin_nm'];
			}
			$row[] = null;
			if (isset($rec['tabi_length'])) {
				//$row[] = '足袋'.$rec['tabi_length'].'cm';
				$tabi_length = $rec['tabi_length'];
				if ($tabi_length == 0.0) {
					$tabi_length = '      ';
				}
				$row[] = '足袋' . $tabi_length . 'cm';
			} else {
				$row[] = null;
			}
			$row[] = $rec['hachu_suryo'];
			// 届先の優先順は納入場所→住所	2014/08/11 add
			// （ただし、納入場所は必須なので、住所になることはないと思う）
			if (isset($rec['nonyu2_nm'])) {
				$row[] = $rec['nonyu2_nm'];
				$row[] = NULL;
				$row[] = NULL;
			} else {
				$row[] = NULL;
				$row[] = $rec['nonyu_addr1'];
				$row[] = $rec['nonyu_addr2'];
			}
			$row[] = $rec['nonyu_ymd'];
			$row[] = $rec['order_flg'];
			$row_arr2[] = $row;
			$sum2 += $rec['prc'];  // 着付時販売品
			if (count($row_arr2) >= 3) {
				break;
			}
		}

		if (count($row_arr2) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr, $row_height, $row_arr2);
		}

//        $pdfObj->write_num(array('x' =>390, 'y' =>583, 'width' => 160, 'height' => 15), $sum2); // 着付時販売品
		$pdfObj->write_num(array('x' => 390, 'y' => 582, 'width' => 160, 'height' => 15), $sum2); // 着付時販売品
		//
        // カラムの設定
//        $set_arr3[] = array('x' =>  40, 'y' => 625, 'width' =>  40, 'height' => 15, 'type' => 'num');
//        $set_arr3[] = array('x' =>  80, 'y' => 625, 'width' => 190, 'height' => 15);	// 準備場所
//        $set_arr3[] = array('x' =>  80, 'y' => 618, 'width' => 190, 'height' => 15, 'font_size' => 12);	// 住所１
//        $set_arr3[] = array('x' =>  80, 'y' => 630, 'width' => 190, 'height' => 15, 'font_size' => 12);	// 住所２
//        $set_arr3[] = array('x' => 275, 'y' => 625, 'width' => 100, 'height' => 15, 'type' => 'time', 'format' => "n 月 j 日  G 時 i 分", 'align' => 'C');
//        $set_arr3[] = array('x' => 390, 'y' => 625, 'width' => 100, 'height' => 15, 'type' => 'time', 'format' => "n 月 j 日  G 時 i 分", 'align' => 'C');
//        $set_arr3[] = array('x' => 495, 'y' => 270, 'width' =>  25, 'height' => 15, 'align' => 'C');	// 発注
		$set_arr3[] = array('x' => 40, 'y' => 627, 'width' => 40, 'height' => 15, 'type' => 'num');
		$set_arr3[] = array('x' => 80, 'y' => 627, 'width' => 190, 'height' => 15); // 準備場所
		$set_arr3[] = array('x' => 80, 'y' => 620, 'width' => 190, 'height' => 15, 'font_size' => 12); // 住所１
		$set_arr3[] = array('x' => 80, 'y' => 632, 'width' => 190, 'height' => 15, 'font_size' => 12); // 住所２
		$set_arr3[] = array('x' => 275, 'y' => 627, 'width' => 100, 'height' => 15, 'type' => 'time', 'format' => "n 月 j 日  G 時 i 分", 'align' => 'C');
		$set_arr3[] = array('x' => 390, 'y' => 627, 'width' => 100, 'height' => 15, 'type' => 'time', 'format' => "n 月 j 日  G 時 i 分", 'align' => 'C');
		$set_arr3[] = array('x' => 495, 'y' => 627, 'width' => 25, 'height' => 15, 'align' => 'C'); // 発注
		// 施行発注喪服情報明細を取得
		$rec_ary = $this->getSekoMofukuMsi($db, $seko_no, NULL, $siire_cd, 2);

		// 値の設定　着付け
		$row_arr3 = array();
		foreach ($rec_ary as $rec) {
			$row = array();
			$row[] = $rec['hachu_suryo'];
//			if (isset($rec['nonyu_addr1']) && ($rec['nonyu_addr1'] != "")) {
//				$row[] = NULL;
//	            $row[] = $rec['nonyu_addr1'];
//	            $row[] = $rec['nonyu_addr2'];
//			} else {
			$row[] = $rec['nonyu_nm'];
			$row[] = NULL;
			$row[] = NULL;
//			}
			$row[] = $rec['k_start_dt'];
			$row[] = $rec['k_end_dt'];
			$row[] = $rec['order_flg'];
			$row_arr3[] = $row;
			$suryo3[] = $rec['hachu_suryo'];  // 人数
			$sum3[] = $rec['prc'];  // 着付請求額
			if (count($row_arr3) >= 2) {
				break;
			}
		}

		if (count($row_arr3) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr3, $row_height, $row_arr3);
		}

		if (isset($suryo3[0])) {
//            $pdfObj->write_num(array('x' =>390, 'y' =>676, 'width' =>  30, 'height' => 15), $suryo3[0]); // 人数
//            $pdfObj->write_num(array('x' =>440, 'y' =>676, 'width' => 110, 'height' => 15), $sum3[0]); // 着付請求額
			$pdfObj->write_num(array('x' => 115, 'y' => 676, 'width' => 30, 'height' => 15), $suryo3[0]); // 人数
			$pdfObj->write_num(array('x' => 165, 'y' => 676, 'width' => 110, 'height' => 15), $sum3[0]); // 着付請求額
		}

		if (isset($suryo3[1])) {
//            $pdfObj->write_num(array('x' =>390, 'y' =>701, 'width' =>  30, 'height' => 15), $suryo3[1]); // 人数
//            $pdfObj->write_num(array('x' =>440, 'y' =>701, 'width' => 110, 'height' => 15), $sum3[1]); // 着付請求額
			$pdfObj->write_num(array('x' => 390, 'y' => 676, 'width' => 30, 'height' => 15), $suryo3[1]); // 人数
			$pdfObj->write_num(array('x' => 440, 'y' => 676, 'width' => 110, 'height' => 15), $sum3[1]); // 着付請求額
		}

		// 備考
		$row = DataMapper_Pdf0205::getHead($db, $seko_no);
		if (isset($row)) {
//			$pdfObj->write_strings(array('x' => 30, 'y' => 740, 'width' => 540, 'height' => 45), $row['biko']);
			$pdfObj->write_strings(array('x' => 30, 'y' => 712, 'width' => 540, 'height' => 45), $row['biko']);
		}
	}

	/**
	 * 生花祭壇・アレンジ花　発注書
	 *
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @version 2014/12/05 備考欄を改行するように変更。 
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0206($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
                if($this->_moushi_kbn == '2'){
                    $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileNameHouji[$report_cd]);
                    // 発注共通項目
                    $pdfObj->hachu_cmn_out_houji($seko_no, $siire_cd, $report_cd, $hachu_time);
                }else{
                    $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);
                    // 発注共通項目
                    $pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
                }
		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		$seika = DataMapper_SekoSeikaInfo::find($db, array("seko_no" => $seko_no));
		if (count($seika) > 0) {
			// 故人の好きな花
			$pdfObj->write_string(array('x' => 112, 'y' => 287, 'width' => 50, 'height' => 15), $seika[0]['fl_color1_nm']); // 第一希望
			$pdfObj->write_string(array('x' => 167, 'y' => 287, 'width' => 50, 'height' => 15), $seika[0]['fl_color2_nm']); // 第二希望
			$pdfObj->write_string(array('x' => 220, 'y' => 287, 'width' => 50, 'height' => 15), $seika[0]['fl_color3_nm']); // 第三希望
			$pdfObj->write_string(array('x' => 275, 'y' => 287, 'width' => 295, 'height' => 15), $seika[0]['fl_etc_memo']); // 備考
		}
		$row_height = 27.1;

		// カラムの設定
		$set_arr1[] = array('x' => 30, 'y' =>328, 'width' => 60, 'height' => 15);
		$set_arr1[] = array('x' => 95, 'y' =>323, 'width' =>120, 'height' => 15);
		$set_arr1[] = array('x' => 95, 'y' =>336, 'width' =>120, 'height' => 15);
		$set_arr1[] = array('x' =>210, 'y' =>328, 'width' => 70, 'height' => 15, 'type' => 'num', 'align' => 'R');
		$set_arr1[] = array('x' =>290, 'y' =>328, 'width' => 65, 'height' => 15, 'type' => 'num', 'align' => 'R');
		$set_arr1[] = array('x' =>360, 'y' =>328, 'width' =>210, 'height' => 15);

		$rec_ary = DataMapper_Pdf0206::find2($db, array('seko_no' => $seko_no), 1);
		$row_arr1 = array();
		foreach ($rec_ary as $rec) {
			if (!in_array($rec['hachu_no'], $hachu_no_ary)) {
				continue;
			}
			$row = array();
			$row[] = $rec['v_free1'];
			$row[] = $rec['shohin_nm'];
			// 納品日
			$nonyu_ymd = null;
			if (strlen($rec['nonyu_ymd']) > 0)	{
				$nonyu_ymd = $rec['nonyu_ymd'];
			}
			if (strlen($rec['nonyu_ymd2']) > 0)	{
				if (strlen($rec['nonyu_ymd']) > 0)	{
					$nonyu_ymd .= '～' . $rec['nonyu_ymd2hhmi'];
				} else {
					$nonyu_ymd .= '～' . $rec['nonyu_ymd2'];
				}	
			} else {
				if (strlen($rec['nonyu_ymd']) > 0)	{
					$nonyu_ymd .= '～';
				}	
			} 
			$row[] = $nonyu_ymd;
			$row[] = $rec['hachu_tnk'];
			$row[] = $rec['hachu_suryo'];
			$row[] = $rec['hd_biko1'];
			$row_arr1[] = $row;
			if (count($row_arr1) >= 8) {
				break;
			}
		}
		if (count($row_arr1) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr1, $row_height, $row_arr1);
		}

		// 神道関係
		// カラムの設定
		$set_arr2[] = array('x' => 30, 'y' => 574, 'width' => 60, 'height' => 15);
		$set_arr2[] = array('x' => 95, 'y' => 574, 'width' => 120, 'height' => 15);
		$set_arr2[] = array('x' => 210, 'y' => 574, 'width' => 70, 'height' => 15, 'type' => 'num', 'align' => 'R');
		$set_arr2[] = array('x' => 290, 'y' => 574, 'width' => 65, 'height' => 15, 'type' => 'num', 'align' => 'R');
		$set_arr2[] = array('x' => 360, 'y' => 574, 'width' => 210, 'height' => 15);

		$rec_ary = DataMapper_Pdf0206::find2($db, array('seko_no' => $seko_no), 2);
		$row_arr2 = array();
		foreach ($rec_ary as $rec) {
			if (!in_array($rec['hachu_no'], $hachu_no_ary)) {
				continue;
			}
			$row = array();
			$row[] = $rec['v_free1'];
			$row[] = $rec['shohin_nm'];
			//$row[] = number_format($rec['hachu_prc'], 2);
			$row[] = $rec['hachu_tnk'];
			$row[] = $rec['hachu_suryo'];
			$row[] = $rec['hd_biko1'];
			$row_arr2[] = $row;
			if (count($row_arr2) >= 4) {
				break;
			}
		}
		if (count($row_arr2) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr2, $row_height, $row_arr2);
		}

		$seika = DataMapper_SekoSeikaInfo::find($db, array("seko_no" => $seko_no));
		if (count($seika) > 0) {
			// 備考
			$pdfObj->write_strings(array('x' => 35, 'y' => 695, 'width' => 540, 'height' => 60), $seika[0]['biko1']);
		}
	}

	/**
	 * 花運搬・花輪発注書
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0207($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		/* $rec = DataMapper_SekoKihon::find( $db, array( "seko_no" => $seko_no ) );
		  if (count($rec) == 0) {
		  return self::ERR_STATUS_NODATA;
		  }
		  $recKihon = $rec[0]; */

		$rec_ary = DataMapper_CodeNmMst::find($db, array('code_kbn' => '0280'));
		foreach ($rec_ary as $rec) {
			$umu[] = $rec['kbn_value_lnm'];
		}

		// 施行発注管理情報を取得
		$rec_ary = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, 18);
		if (count($rec_ary) > 0) {
			$hanaunpan = DataMapper_SekoHanaunpanInfo::find($db, array("seko_no" => $seko_no));
			if (count($hanaunpan) > 0) {
				// 運搬予定　行き
				$pdfObj->write_num(array('x' => 205, 'y' => 280, 'width' => 40, 'height' => 15), $hanaunpan[0]['start_1seika_cnt']);
				$pdfObj->write_num(array('x' => 315, 'y' => 280, 'width' => 40, 'height' => 15), $hanaunpan[0]['start_2seika_cnt']);
				$pdfObj->write_num(array('x' => 425, 'y' => 280, 'width' => 40, 'height' => 15), $hanaunpan[0]['start_kago_cnt']);
				$pdfObj->write_num(array('x' => 535, 'y' => 280, 'width' => 40, 'height' => 15), $hanaunpan[0]['start_butsu_cnt']);

				// 運搬予定　帰り
				$pdfObj->write_num(array('x' => 205, 'y' => 296, 'width' => 40, 'height' => 15), $hanaunpan[0]['return_1seika_cnt']);
				$pdfObj->write_num(array('x' => 315, 'y' => 296, 'width' => 40, 'height' => 15), $hanaunpan[0]['return_2seika_cnt']);
				$pdfObj->write_num(array('x' => 425, 'y' => 296, 'width' => 40, 'height' => 15), $hanaunpan[0]['return_kago_cnt']);
				$pdfObj->write_num(array('x' => 535, 'y' => 296, 'width' => 40, 'height' => 15), $hanaunpan[0]['return_butsu_cnt']);

				// 行程
				$pdfObj->write_string(array('x' => 95, 'y' => 314, 'width' => 125, 'height' => 15, 'align' => 'C'), $hanaunpan[0]['start_spot_nm']);
				$pdfObj->write_string(array('x' => 95, 'y' => 329, 'width' => 125, 'height' => 15, 'align' => 'C'), $hanaunpan[0]['return_spot_nm']);
				$pdfObj->write_string(array('x' => 230, 'y' => 320, 'width' => 125, 'height' => 15, 'align' => 'C'), $hanaunpan[0]['nouhin_spot_nm']);

				// 出棺時間
				$pdfObj->write_date(array('x' => 425, 'y' => 320, 'width' => 150, 'height' => 15, 'align' => 'C', 'type' => 'time'), $hanaunpan[0]['syukan_dt'], "n 月 j 日  G 時 i 分");

				// 行き指定時間
				$pdfObj->write_date(array('x' => 95, 'y' => 345, 'width' => 155, 'height' => 15, 'align' => 'C', 'type' => 'time'), $hanaunpan[0]['start_from_dt'], "n 月 j 日  G 時 i 分");
				$pdfObj->write_date(array('x' => 270, 'y' => 345, 'width' => 155, 'height' => 15, 'align' => 'C', 'type' => 'time'), $hanaunpan[0]['start_to_dt'], "n 月 j 日  G 時 i 分");
				$pdfObj->write_string(array('x' => 480, 'y' => 345, 'width' => 95, 'height' => 15, 'align' => 'C'), $umu[$hanaunpan[0]['start_ban_kbn']]);

				// 帰り指定時間
				$pdfObj->write_date(array('x' => 95, 'y' => 360, 'width' => 155, 'height' => 15, 'align' => 'C', 'type' => 'time'), $hanaunpan[0]['return_from_dt'], "n 月 j 日  G 時 i 分");
				$pdfObj->write_date(array('x' => 270, 'y' => 360, 'width' => 155, 'height' => 15, 'align' => 'C', 'type' => 'time'), $hanaunpan[0]['return_to_dt'], "n 月 j 日  G 時 i 分");
				$pdfObj->write_string(array('x' => 480, 'y' => 360, 'width' => 95, 'height' => 15, 'align' => 'C'), $umu[$hanaunpan[0]['return_ban_kbn']]);

				// 備考
				$pdfObj->write_string(array('x' => 25, 'y' => 390, 'width' => 400, 'height' => 15), $hanaunpan[0]['biko1'] . ' ' . $hanaunpan[0]['biko2']);
			}
		}

		$row_height = 30.5;

		// カラムの設定
		$set_arr[] = array('x' => 25, 'y' => 475, 'width' => 110, 'height' => 15);
		$set_arr[] = array('x' => 135, 'y' => 475, 'width' => 55, 'height' => 15, 'type' => 'num');
		$set_arr[] = array('x' => 190, 'y' => 475, 'width' => 40, 'height' => 15, 'type' => 'num');
		$set_arr[] = array('x' => 230, 'y' => 475, 'width' => 110, 'height' => 15, 'type' => 'time', 'format' => "n 月 j 日  G 時 i 分", 'align' => 'C');
		$set_arr[] = array('x' => 340, 'y' => 475, 'width' => 85, 'height' => 15);
		$set_arr[] = array('x' => 425, 'y' => 475, 'width' => 110, 'height' => 15);

		// 施行発注管理情報を取得
		$rec_ary = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, 17);

		// 値の設定
		$row_arr = array();
		foreach ($rec_ary as $rec) {
			$row = array();
			$row[] = $rec['shohin_nm'];
			$row[] = $rec['tnk'];
			$row[] = $rec['hachu_suryo'];
			$row[] = $rec['nonyu_ymd'];
			$row[] = $rec['nonyu_nm'];
			$row[] = $rec['v_free1'];
			$row_arr[] = $row;
			if (count($row_arr) >= 7)
				break;
		}

		if (count($row_arr) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
		}

		$seika = DataMapper_SekoSeikaInfo::find($db, array("seko_no" => $seko_no));
		if (count($seika) > 0) {
			// 故人の好きな花
			$txt1 = $seika[0]['fl_color1_nm'];
			$txt2 = $seika[0]['fl_color2_nm'];
			$txt3 = $seika[0]['fl_color3_nm'];

			$txt = "故人の好きな花（色）";
			if ($txt1 != "") {
				$txt = $txt . "　第一希望　" . $txt1;
			}
			if ($txt2 != "") {
				$txt = $txt . "　第二希望　" . $txt2;
			}
			if ($txt3 != "") {
				$txt = $txt . "　第三希望　" . $txt3;
			}
			$txtbiko = $seika[0]['fl_etc_memo'];

			if (($txt1 == "") && ($txt2 == "") && ($txt3 == "")) {
				$pdfObj->write_string(array('x' => 35, 'y' => 692, 'width' => 530, 'height' => 15), $txtbiko); // 備考 
			} else {
				$pdfObj->write_string(array('x' => 35, 'y' => 692, 'width' => 530, 'height' => 15), $txt);
				$pdfObj->write_string(array('x' => 35, 'y' => 712, 'width' => 530, 'height' => 15), $txtbiko); // 備考 
			}
		}
	}

	/**
	 * 式場・会場発注書
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0208($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
		// 発注書タイトル
		$pdfObj->hachu_title_out($db, $report_cd);

		// 会社ロゴ
		$pdfObj->kaisyalogo_out($db, $seko_no, 470, 90, false);

		// 発注ヘッダ項目
		$rec = DataMapper_PdfCommon::getOrderHead($db, $seko_no);
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$head = $rec[0];

		$head2['daicho_no_eria'] = $head['daicho_no_eria'];
		$head2['daicho_no_mm'] = $head['daicho_no_mm'];
		$head2['daicho_no_seq'] = $head['daicho_no_seq'];
		$head2['souke_knm'] = $head['souke_knm'];
		$head2['souke_nm'] = $head['souke_nm'];
		$head2['s_tanto_nm'] = $head['s_tanto_nm'];
		$pdfObj->hachu_head_out($head2, 25, 97);

		$pdfObj->write_date(array('x' => 275, 'y' => 105, 'width' => 125, 'height' => 10, 'align' => 'R', 'type' => 'time', 'font_size' => 15), $head['sougi_ymd'], "n 月 j 日"); // 葬儀日
		$pdfObj->write_string(array('x' => 330, 'y' => 140, 'width' => 135, 'height' => 10, 'align' => 'C'), $head['basho_nm']);		   // 葬儀場所
		$pdfObj->write_date(array('x' => 470, 'y' => 140, 'width' => 105, 'height' => 10, 'align' => 'C', 'type' => 'time'), $head['sougi_ymd'], "G 時 i 分"); // 葬儀時
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 8));
		if (count($rec) > 0) {
			$pdfObj->write_string(array('x' => 330, 'y' => 173, 'width' => 135, 'height' => 10, 'align' => 'C'), $rec[0]['basho_nm']);		   // 壇払場所
			$pdfObj->write_date(array('x' => 470, 'y' => 173, 'width' => 105, 'height' => 10, 'align' => 'C', 'type' => 'time'), $rec[0]['nitei_ymd'], "G 時 i 分"); // 壇払時
		}

		$rec = DataMapper_Pdf0208::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$kaijo = $rec[0];

		//
		// 式場
		//

        $pdfObj->write_string(array('x' => 100, 'y' => 219, 'width' => 170, 'height' => 10, 'align' => 'C'), $kaijo['sk_kaijyo_nm']);
		$pdfObj->write_num(array('x' => 280, 'y' => 219, 'width' => 30, 'height' => 10), $kaijo['sk_seat_cnt']);
		$pdfObj->write_string(array('x' => 430, 'y' => 219, 'width' => 130, 'height' => 10, 'align' => 'C'), $kaijo['shishikisha_cnt']);
		$pdfObj->write_string(array('x' => 100, 'y' => 264, 'width' => 85, 'height' => 10, 'align' => 'C'), $kaijo['uke_nm']);
		$pdfObj->write_string(array('x' => 195, 'y' => 264, 'width' => 365, 'height' => 10), $kaijo['uke_memo']);
		$pdfObj->write_string(array('x' => 100, 'y' => 281, 'width' => 85, 'height' => 10, 'align' => 'C'), $kaijo['cyo_nm']);
		$pdfObj->write_string(array('x' => 195, 'y' => 281, 'width' => 365, 'height' => 10), $kaijo['cyo_memo']);
		$pdfObj->write_date(array('x' => 100, 'y' => 325, 'width' => 85, 'height' => 10, 'align' => 'C', 'type' => 'time'), $kaijo['tk_in_ymd'], "G:i");
		$pdfObj->write_string(array('x' => 195, 'y' => 325, 'width' => 125, 'height' => 10, 'align' => 'C'), $kaijo['tk_in_nm']);
		$pdfObj->write_date(array('x' => 335, 'y' => 325, 'width' => 85, 'height' => 10, 'align' => 'C', 'type' => 'time'), $kaijo['tk_out_ymd'], "G:i");
		$pdfObj->write_string(array('x' => 430, 'y' => 325, 'width' => 125, 'height' => 10, 'align' => 'C'), $kaijo['tk_out_nm']);
		$pdfObj->write_date(array('x' => 100, 'y' => 344, 'width' => 85, 'height' => 10, 'align' => 'C', 'type' => 'time'), $kaijo['sk_in_ymd'], "G:i");
		$pdfObj->write_string(array('x' => 195, 'y' => 344, 'width' => 125, 'height' => 10, 'align' => 'C'), $kaijo['sk_in_nm']);
		$pdfObj->write_date(array('x' => 335, 'y' => 344, 'width' => 85, 'height' => 10, 'align' => 'C', 'type' => 'time'), $kaijo['sk_out_ymd'], "G:i");
		$pdfObj->write_string(array('x' => 430, 'y' => 344, 'width' => 125, 'height' => 10, 'align' => 'C'), $kaijo['sk_out_nm']);

		// 準備品・手配品
		$x = 38;
		$rec_ary = DataMapper_Pdf0208::findTehai($db, array("seko_no" => $seko_no));
		$size = count($rec_ary);
		if ($size > 6)
			$size = 6;
		for ($index = 0; $index < $size; $index++) {
			$tehai = $rec_ary[$index];
			$pdfObj->write_string(array('x' => $x, 'y' => 393, 'width' => 68, 'height' => 10, 'align' => 'C'), $tehai['tehai_nm']);
			$pdfObj->write_strings(array('x' => $x, 'y' => 409, 'width' => 68, 'height' => 65, 'align' => 'C', 'valign' => 'M'), $tehai['msi_biko1']);
			$x += 82.7;
		}

		//
		// 会場
		//

        $pdfObj->write_string(array('x' => 280, 'y' => 502, 'width' => 100, 'height' => 10, 'align' => 'C'), $kaijo['db_kaisyoku_nm']);
		$pdfObj->write_string(array('x' => 460, 'y' => 502, 'width' => 100, 'height' => 10, 'align' => 'C'), $kaijo['db_shinko_nm']);

		// 料理
		$rec = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd);
		$count = count($rec);
		for ($index = 0; $index < $count; $index++) {
			$row = $rec[$index];
			switch ($index) {
				case 0:
					$pdfObj->write_string(array('x' => 50, 'y' => 548, 'width' => 90, 'height' => 10, 'align' => 'C'), $row['shohin_nm']);
					$pdfObj->write_num(array('x' => 150, 'y' => 548, 'width' => 20, 'height' => 10), $row['hachu_suryo']);
					break;
				case 1:
					$pdfObj->write_string(array('x' => 50, 'y' => 570, 'width' => 90, 'height' => 10, 'align' => 'C'), $row['shohin_nm']);
					$pdfObj->write_num(array('x' => 150, 'y' => 570, 'width' => 20, 'height' => 10), $row['hachu_suryo']);
					break;
				case 2:
					$pdfObj->write_string(array('x' => 50, 'y' => 684, 'width' => 90, 'height' => 10, 'align' => 'C'), $row['shohin_nm']);
					$pdfObj->write_num(array('x' => 150, 'y' => 684, 'width' => 20, 'height' => 10), $row['hachu_suryo']);
					break;
				case 3:
					$pdfObj->write_string(array('x' => 50, 'y' => 703, 'width' => 90, 'height' => 10, 'align' => 'C'), $row['shohin_nm']);
					$pdfObj->write_num(array('x' => 150, 'y' => 703, 'width' => 20, 'height' => 10), $row['hachu_suryo']);
					break;
			}
		}

		// 盛付
		$rec = DataMapper_Pdf0208::getTableCount($db, $seko_no);
		$str = '';
		foreach ($rec as $row) {
			$str .= $row['cooking_cnt'] . '名×' . $row['record_count'] . PHP_EOL;
		}
		$pdfObj->write_strings(array('x' => 55, 'y' => 585, 'width' => 135, 'height' => 95, 'align' => 'C', 'valign' => 'M'), $str);

		// 連絡事項
		$pdfObj->write_strings(array('x' => 40, 'y' => 733, 'width' => 150, 'height' => 42, 'align' => 'L', 'font_size' => 8), $kaijo['allergy_memo2']);

		// 親族代表者挨拶
		$pdfObj->write_string(array('x' => 280, 'y' => 527, 'width' => 100, 'height' => 10, 'align' => 'C'), $kaijo['sz_daihyo_nm2']);
		$pdfObj->write_string(array('x' => 210, 'y' => 546, 'width' => 170, 'height' => 10, 'align' => 'C'), $kaijo['sz_daihyo_nm']);

		// 献杯
		$pdfObj->write_string(array('x' => 460, 'y' => 527, 'width' => 100, 'height' => 10, 'align' => 'C'), $kaijo['kenpai_nm2']);
		$pdfObj->write_string(array('x' => 400, 'y' => 546, 'width' => 160, 'height' => 10, 'align' => 'C'), $kaijo['kenpai_nm']);

		// 会場セッティング
		if (isset($kaijo['gazo_img'])) {
			$img = $db->readBlobCont($kaijo['gazo_img']);
			$pdfObj->write_image(array('x' => 205, 'y' => 587, 'width' => 355, 'height' => 149), $img);
		}

		$pdfObj->write_string(array('x' => 205, 'y' => 740, 'width' => 50, 'height' => 10, 'align' => 'C'), 'ビール');
		$pdfObj->write_string(array('x' => 265, 'y' => 740, 'width' => 115, 'height' => 10, 'align' => 'C'), $kaijo['beer_nm']);
		$pdfObj->write_string(array('x' => 205, 'y' => 760, 'width' => 50, 'height' => 10, 'align' => 'C'), '日本酒');
		$pdfObj->write_string(array('x' => 265, 'y' => 760, 'width' => 115, 'height' => 10, 'align' => 'C'), $kaijo['sake_nm']);
		$pdfObj->write_string(array('x' => 383, 'y' => 740, 'width' => 50, 'height' => 10, 'align' => 'C'), '他飲物');
		$pdfObj->write_string(array('x' => 445, 'y' => 740, 'width' => 115, 'height' => 10, 'align' => 'C'), $kaijo['etc_drink_nm']);
		$pdfObj->write_string(array('x' => 383, 'y' => 760, 'width' => 50, 'height' => 10, 'align' => 'C'), '陰膳');
		$pdfObj->write_string(array('x' => 445, 'y' => 760, 'width' => 115, 'height' => 10, 'align' => 'C'), $kaijo['inzen_nm']);
	}

	/**
	 * おくやみ記事FAX申込書
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @version 2014/12/22 おくやみ記事HP掲載依頼でPHPエラーになる件を修正 Kayo
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData02001($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(16);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
		// 発注書タイトル
		$pdfObj->hachu_title_out($db, $report_cd);

		// 発注日
		$pdfObj->write_date(array('x' => 470, 'y' => 50, 'width' => 105, 'height' => 10, 'type' => 'time', 'align' => 'R'), $hachu_time, "Y 年 n 月 j 日");

		// 会社ロゴ
		// 2014/09/09 DEL Kayo $pdfObj->kaisyalogo_out($db, $seko_no, 400, 750);
		$common_w = DataMapper_PdfCommon::find2($db, array("seko_no" => $seko_no));
		if (count($common_w) > 0) {
			$logo = null;
			if (isset($common_w[0]['logo_img'])) {
				$logo = $db->readBlobCont($common_w[0]['logo_img']);
			}
			$pdfObj->kaisyalogo_ary_out(
					360		//$x
					, 750		//$y
					, array(
				'logo' => $logo,
				'nm' => $common_w[0]['kaisya_lnm'],
				'bumon_nm' => $common_w[0]['bumon_lnm'],
				'bumon_tel' => $common_w[0]['tel']
					)
					, 230	//$width=110
					, 30	 //$logo_size=15
					, 16	 //$k_font_size=null
					, 16	 //$b_font_size=null
			);
		}

		$rec = DataMapper_PdfCommon::getOrderHead($db, $seko_no, $siire_cd);
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$order = $rec[0];

		// 発注先
		if ($report_cd == '00003') {
			$pdfObj->write_string(array('x' => 40, 'y' => 88, 'width' => 270, 'height' => 15, 'font_size' => 16), 'ホームページ');
		} else {
			$pdfObj->write_string(array('x' => 40, 'y' => 88, 'width' => 270, 'height' => 15, 'font_size' => 16), $order['siire_lnm']);
		}
		$rec = DataMapper_Pdf02001::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$keisai = $rec[0];

		$rec = DataMapper_SekoKihon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$kihon = $rec[0];

		$pdfObj->write_string(array('x' => 145, 'y' => 135, 'width' => 400, 'height' => 15), $kihon['k_knm']);  // フリガナ
		$pdfObj->write_string(array('x' => 145, 'y' => 168, 'width' => 400, 'height' => 15), $kihon['k_nm']);  // 故人お名前
		$pdfObj->write_string(array('x' => 525, 'y' => 150, 'width' => 45, 'height' => 15, 'align' => 'C'), $kihon['k_sex_nm']);  // 性別
		$pdfObj->write_string(array('x' => 145, 'y' => 203, 'width' => 440, 'height' => 15), $keisai['sk_k_katagaki']);  // 故人肩書
		//$pdfObj->write_string(array('x' => 185, 'y' => 240, 'width' => 140, 'height' => 15), $order['m_nm']);            // 遺族肩書　名前 喪主名 2014/09/09 UPD Kayo
		$pdfObj->write_string(array('x' => 145, 'y' => 240, 'width' => 440, 'height' => 15), $keisai['sk_i_katagaki']);  // 遺族肩書

		$jusho = $kihon['kg_yubin_no'] . ' ' . $kihon['kg_addr1'] . $kihon['kg_addr2'];
		if ($kihon['souke_addr_kbn'] == 2) {
			$mBasho = DataMapper_SekoNohinBasho::find($db, $seko_no, array('basyo_kbn_cd' => 2));
			if (count($mBasho) > 0) {
				$jusho = $mBasho[0]['yubin_no'] . ' ' . $mBasho[0]['addr1'] . $mBasho[0]['addr2'];
			}
		}
		$sk_kakunin = null;	 // 2015/04/19 ADD Kayo
		switch ($keisai['sk_kakunin_kbn']) {
			case 0:
				$sk_kakunin = '故人自宅';
				break;
			case 1:
				$sk_kakunin = '通夜室';
				break;
			case 2:
				$sk_kakunin = '喪主自宅';
				break;
			case 9:
				if (isset($keisai['sk_kakunin_nm'])) {
					$sk_kakunin = $keisai['sk_kakunin_nm'];
				}
				break;
		}
		$pdfObj->write_string(array('x' => 145, 'y' => 278, 'width' => 440, 'height' => 15), $jusho);  // 住所
		$pdfObj->write_string(array('x' => 195, 'y' => 303, 'width' => 180, 'height' => 15), $sk_kakunin);  // 確認
		$pdfObj->write_string(array('x' => 430, 'y' => 303, 'width' => 150, 'height' => 15), $keisai['sk_kakunin_tel']);  // TEL

		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 1));
		if (count($rec) > 0) {
			$pdfObj->write_date(array('x' => 145, 'y' => 338, 'width' => 440, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "n 月 j 日 A g 時 i 分", null, null, array('午前', '午後')); // 死亡日時
		}

		$pdfObj->write_date(array('x' => 145, 'y' => 375, 'width' => 380, 'height' => 15, 'type' => 'ymd'), $kihon['k_seinengappi_ymd'], "y 年 n 月 j 日", 'gG', $kihon['k_gengo']);  // 生年月日
		$pdfObj->write_string(array('x' => 525, 'y' => 375, 'width' => 20, 'height' => 15, 'align' => 'C'), $kihon['k_nenrei_man']);  // 年齢

		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 7));
		if (count($rec) > 0) {
			$pdfObj->write_date(array('x' => 145, 'y' => 412, 'width' => 440, 'height' => 10, 'type' => 'time'), $rec[0]['nitei_ymd'], "n 月 j 日 A g 時 i 分", null, null, array('午前', '午後')); // 葬儀日時
		}

		// 葬儀基本情報
		$sougi = App_Utils::getSougiInfo($seko_no);
		$addr = $basho = $tel = '';
		if (count($sougi) > 0) {
			$addr = $sougi['zip_no'] . ' ' . $sougi['addr1_nm'] . $sougi['addr2_nm'];
			$basho = $sougi['sougi_shikijo'];
			$tel = $sougi['sougi_tel'];
		}
		$pdfObj->write_string(array('x' => 185, 'y' => 448, 'width' => 405, 'height' => 10), $addr); // 葬儀場所　住所
		$pdfObj->write_string(array('x' => 185, 'y' => 484, 'width' => 200, 'height' => 10), $basho);		   // 葬儀場所　場所
		$pdfObj->write_string(array('x' => 430, 'y' => 484, 'width' => 150, 'height' => 10), $tel);		   // 葬儀場所　TEL

		$pdfObj->write_string(array('x' => 145, 'y' => 514, 'width' => 345, 'height' => 15), $kihon['m_knm']);  // フリガナ
		$pdfObj->write_string(array('x' => 145, 'y' => 544, 'width' => 345, 'height' => 15), $kihon['m_nm']);  // 喪主お名前
		$pdfObj->write_string(array('x' => 525, 'y' => 533, 'width' => 45, 'height' => 15, 'align' => 'C'), $kihon['m_zoku_nm']);  // 続柄
		if ($report_cd == '00003') {
			$free = '■ホームページ';
			$pdfObj->write_string(array('x' => 135, 'y' => 582, 'width' => 445, 'height' => 15), $free);  // 掲載希望
		} else {
			$rec = DataMapper_Siire::find($db, array("siire_cd" => $siire_cd));
			if (count($rec) == 0) {
				return self::ERR_STATUS_NODATA;
			}
			$siire = $rec[0];

			// 全国紙
			if (($siire['newspaper_kbn'] == 0) || ($siire['newspaper_kbn'] == 99)) {
				$free = '';
				$rec = DataMapper_Pdf02001::findDetail($db, array("seko_no" => $seko_no), $siire_cd);
				foreach ($rec as $row) {
					$str = $row['keisai_nm'];
					if (isset($str))
						$free .= $str . ' ';
				}
				$pdfObj->write_string(array('x' => 135, 'y' => 582, 'width' => 445, 'height' => 15), $free);  // 掲載希望
			}
		}

		$pdfObj->write_string(array('x' => 145, 'y' => 615, 'width' => 440, 'height' => 15), $keisai['sk_comment1']);  // 一言
		$pdfObj->write_string(array('x' => 145, 'y' => 630, 'width' => 440, 'height' => 15), $keisai['sk_comment2']);  //
		$pdfObj->write_string(array('x' => 145, 'y' => 670, 'width' => 440, 'height' => 15), $keisai['sk_biko1']);  // 備考
		$pdfObj->write_string(array('x' => 145, 'y' => 685, 'width' => 440, 'height' => 15), $keisai['sk_biko2']);  //
	}

	/**
	 * 加工依頼書
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
//    private function outData02002($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time)
//    {
//        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);
//        $pdfObj->set_default_font_size(12);
//        $pdfObj->set_default_font_family_h('kozgopromedium');
//		//フッダー項目を出力
//		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);
//
//        // 確認用のグリッドを出力
//        //$pdfObj->test_line_out(600, 1000);
//        
//        $rec = DataMapper_Pdf02002::find( $db, array( "seko_no" => $seko_no, "hachu_no" =>$hachu_no_ary[0] ) );
//        if (count($rec) == 0) {
//            return self::ERR_STATUS_NODATA;
//        }
//        $row = $rec[0];
//
//        $pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
//        
//        // 新規・焼増
//        if ($row['yakimashi_kbn'] == 0) {
//            $pdfObj->write_circle(array('x' => 35,  'y' => 126, 'width' => 50, 'height' => 13));
//        } else {
//            $pdfObj->write_circle(array('x' => 117, 'y' => 126, 'width' => 50, 'height' => 13));
//        }
//
//        $pdfObj->write_string(array('x' => 115, 'y' => 200, 'width' => 460, 'height' => 10, 'font_size' => 12), $row['s_input_bmn_nm']);
//        $pdfObj->write_string(array('x' => 385, 'y' => 200, 'width' => 460, 'height' => 10, 'font_size' => 12), $row['s_output_bmn_nm']);
//        
//        // 故人のお名前
//        $pdfObj->write_string(array('x' => 180, 'y' => 325, 'width' => 220, 'height' => 10, 'font_size' => 14), $row['k_knm']);
//        $pdfObj->write_string(array('x' => 180, 'y' => 343, 'width' => 220, 'height' => 10, 'font_size' => 18), $row['k_nm']);
//        
//        switch ($row['k_sex_kbn']) {
//            case 1:
//                $pdfObj->write_circle(array('x' => 487, 'y' => 328, 'width' => 25, 'height' => 10));
//                break;
//            case 2:
//                $pdfObj->write_circle(array('x' => 517, 'y' => 328, 'width' => 25, 'height' => 10));
//                break;
//        }
//
//        $pdfObj->write_string(array('x' => 520, 'y' => 343, 'width' => 30, 'height' => 10, 'align' => 'C', 'font_size' => 16), $row['k_nenrei_man']);
//
//        // 出力希望時間
//        if (isset($row['s_output_dt'])) {
//            $pdfObj->write_date(array('x' => 103, 'y' => 216, 'width' => 140, 'height' => 10, 'type' => 'time', 'align' => 'C','font_size' => 12), $row['s_output_dt'], "n月 j日 G 時 i 分");
//        }
//
//        // 加工方法
//        switch ($row['s_kako_kbn']) {
//            case 0:
//                $pdfObj->write_circle(array('x' => 126, 'y' => 378, 'width' => 45, 'height' => 12));
//                break;
//            case 1:
//                $pdfObj->write_circle(array('x' => 215, 'y' => 378, 'width' => 35, 'height' => 12)); 
//                break;
//        }
//
//        // 切り抜きサイズ
//        switch ($row['size_kbn']) {
//            case 0:
//                $pdfObj->write_circle(array('x' => 383, 'y' => 378, 'width' => 15, 'height' => 12));
//                break;
//            case 1:
//                $pdfObj->write_circle(array('x' => 452, 'y' => 378, 'width' => 15, 'height' => 12));
//                break;
//            case 2:
//                $pdfObj->write_circle(array('x' => 528, 'y' => 378, 'width' => 15, 'height' => 12));
//                break;
//        }
//
//        // 着せ替え
//        switch ($row['s_kisekae_kbn']) {
//            case 1:
//                $pdfObj->write_circle(array('x' => 132, 'y' => 394, 'width' => 32, 'height' => 12));
//                break;
//            case 0:
//                $pdfObj->write_circle(array('x' => 211, 'y' => 394, 'width' => 42, 'height' => 12));
//                break;
//        }
//        // 着せ替え:する
//        if ($row['s_kisekae_kbn'] == 1) {
//            // 服
//            switch ($row['s_huku_kbn']) {
//                case 0:
//                    $pdfObj->write_circle(array('x' => 132, 'y' => 411, 'width' => 32, 'height' => 12));
//                    $pdfObj->write_string(array('x' => 160, 'y' => 425, 'width' => 70, 'height' => 15, 'font_size' => 12), $row['huku_nm']);
//                    break;
//                case 1:
//                    $pdfObj->write_circle(array('x' => 215, 'y' => 411, 'width' => 32, 'height' => 12));
//                    $pdfObj->write_string(array('x' => 160, 'y' => 425, 'width' => 70, 'height' => 15, 'font_size' => 12), $row['huku_nm']);
//                    break;
//            }
//        }    
//
//        // 着せ替え:しない
////        if ($row['s_kisekae_kbn'] == 0) {
//            // 背景
//            switch ($row['s_haikei_kbn']) {
//                case 0:
//                    $pdfObj->write_circle(array('x' => 394, 'y' => 394, 'width' => 32, 'height' => 12));
//                    break;
//                case 1:
//                    $pdfObj->write_circle(array('x' => 491, 'y' => 394, 'width' => 62, 'height' => 12));
//                    break;
//            }
////        }    
//        $pdfObj->write_string(array('x' => 455, 'y' => 409, 'width' => 80, 'height' => 15, 'font_size' => 12), $row['haik_nm']);
//
////        if ($row['s_kisekae_kbn'] == 1) {
////            // 家紋
////            switch ($row['s_kamon_kbn']) {
////                case 0:
//////                    $pdfObj->write_circle(array('x' => 137, 'y' => 385, 'width' => 15, 'height' => 15));
////                    $pdfObj->write_circle(array('x' => 176, 'y' => 453, 'width' => 15, 'height' => 12));
////                    break;
////                case 1:
//////                    $pdfObj->write_circle(array('x' => 180, 'y' => 385, 'width' => 45, 'height' => 15));
////                    $pdfObj->write_circle(array('x' => 313, 'y' => 453, 'width' => 45, 'height' => 12));
////                    break;
////                case 2:
//////                    $pdfObj->write_circle(array('x' => 248, 'y' => 385, 'width' => 30, 'height' => 15));
////                    $pdfObj->write_circle(array('x' => 479, 'y' => 453, 'width' => 30, 'height' => 12));
////                    break;
////            }
////            // 名称
/////            $pdfObj->write_string(array('x' => 135, 'y' => 408, 'width' => 150, 'height' => 10, 'align' => 'C', 'font_size' => 16), $row['s_kamon_nm']);
/////            $pdfObj->write_string(array('x' => 135, 'y' => 468, 'width' => 150, 'height' => 10, 'align' => 'C', 'font_size' => 12), $row['s_kamon_nm']);
////        }    
//        // 写真サイズ
//        $pdfObj->write_num(array('x' => 195, 'y' => 476, 'width' => 70, 'height' => 10, 'font_size' => 16), $row['s_han_cnt']);
//        $pdfObj->write_num(array('x' => 470, 'y' => 476, 'width' => 70, 'height' => 10, 'font_size' => 16), $row['s_cab_cnt']);
//        $pdfObj->write_string(array('x' =>  60, 'y' => 503, 'width' => 35, 'height' => 10, 'align' => 'C', 'font_size' => 16), $row['s_sitei_yoko1']);
//        $pdfObj->write_string(array('x' => 133, 'y' => 503, 'width' => 35, 'height' => 10, 'align' => 'C', 'font_size' => 16), $row['s_sitei_tate1']);
//        $pdfObj->write_num(array('x' => 195, 'y' => 503, 'width' => 70, 'height' => 10, 'font_size' => 16), $row['s_sitei_cnt1']);
//        $pdfObj->write_string(array('x' => 340, 'y' => 503, 'width' => 35, 'height' => 10, 'align' => 'C', 'font_size' => 16), $row['s_sitei_yoko2']);
//        $pdfObj->write_string(array('x' => 407, 'y' => 503, 'width' => 35, 'height' => 10, 'align' => 'C', 'font_size' => 16), $row['s_sitei_tate2']);
//        $pdfObj->write_num(array('x' => 470, 'y' => 503, 'width' => 70, 'height' => 10, 'font_size' => 16), $row['s_sitei_cnt2']);
//
//        // パネル写真
//        switch ($row['p_size_kbn']) {
//            case 0:
//                $pdfObj->write_circle(array('x' => 141, 'y' => 550, 'width' => 30, 'height' => 13));
//                break;
//            case 1:
//                $pdfObj->write_circle(array('x' => 251, 'y' => 550, 'width' => 30, 'height' => 13));
//                break;
//            case 2:
//                $pdfObj->write_circle(array('x' => 375, 'y' => 550, 'width' => 30, 'height' => 13));
//                break;
//            case 3:
//                $pdfObj->write_circle(array('x' => 500, 'y' => 550, 'width' => 30, 'height' => 13));
//                break;
//        }
//        $pdfObj->write_date(array('x' => 103, 'y' => 565, 'width' => 140, 'height' => 10, 'type' => 'time', 'align' => 'C', 'font_size' => 12), $row['p_limit_dt'], "n月 j日 G時 i分");
//
//        // 注意事項
//        $pdfObj->write_strings(array('x' => 25, 'y' => 651, 'width' => 320, 'height' => 60, 'align' => 'L', 'valign' => 'T', 'font_size' => 12), $row['biko1'].PHP_EOL.$row['biko2']);
//
//		// 電話連絡
//        switch ($row['s_jotai_kbn']) {
//            case 0:
//                $pdfObj->write_check(array('x' => 47, 'y' => 609, 'size' => 10), true);
//                break;
//            case 1:
//                $pdfObj->write_check(array('x' => 197, 'y' => 609, 'size' => 10), true);
//                break;
//        }
//    } サンメンバージョンコメントアウト otake 17/04/04

    /**
     * 加工依頼書
     * 
     * <AUTHOR> Sato
     * @since      2014/03/25
     * @version 2015/06/22 彩苑カスタマイズ対応
     * @param object $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db DB
     * @param string $seko_no		施工番号
     * @param array $hachu_no_ary	施行発注管理情報 配列
     * @param string $siire_cd		仕入先コード
     * @param string $report_cd		帳票コード
     * @param string $hachu_time	発注日時
     * @return void
     */
    private function outData02002($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
        $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);
        $pdfObj->set_default_font_size(12);
        $pdfObj->set_default_font_family_h('kozgopromedium');
//		//フッダー項目を出力
//		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);
        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);

        $rec = DataMapper_Pdf02002::find2($db, array("seko_no" => $seko_no, "hachu_no" => $hachu_no_ary[0]));
        if (count($rec) == 0) {
            return self::ERR_STATUS_NODATA;
        }
        $row = $rec[0];
        $rec2 = DataMapper_Pdf02002::findSiire($db, array("siire_cd" => $siire_cd));
        if (count($rec2) > 0) {
            // 発注先
            $pdfObj->write_string(array('x' => 390, 'y' => 32, 'width' => 150, 'height' => 15, 'font_size' => 18,), 'FAX ' . $rec2[0]['siire_fax']);
            $pdfObj->write_string(array('x' => 430, 'y' => 54, 'width' => 150, 'height' => 15,), '電話 ' . $rec2[0]['siire_tel']);
            // 業者コード
            $pdfObj->write_string(array('x' => 490, 'y' => 655, 'width' => 60, 'height' => 15, 'font_size' => 18,), $rec2[0]['siire_kanri_cd']);
//            $pdfObj->write_string(array('x' => 390, 'y' => 50, 'width' => 150, 'height' => 15, 'font_size' => 18,), 'FAX '. '0947-88-8909');
//            $pdfObj->write_string(array('x' => 430, 'y' => 70, 'width' => 150, 'height' => 15,), '電話 '. '************');
        }

        // 発注元
        $pdfObj->write_string(array('x' => 44, 'y' => 117, 'width' => 265, 'height' => 15, 'font_size' => 18,), $row['kaisya_snm'] . ' ' . $row['bumon_nm']);
        // 担当者
        $pdfObj->write_string(array('x' => 314, 'y' => 133, 'width' => 65, 'height' => 15, 'align' => 'C',), $row['tanto_nm']);
        $pdfObj->write_string(array('x' => 385, 'y' => 105, 'width' => 30, 'height' => 15, 'align' => 'C',), '会社');
        $pdfObj->write_string(array('x' => 385, 'y' => 134, 'width' => 30, 'height' => 15, 'align' => 'C',), '携帯');
        $pdfObj->write_check(array('x' => 410, 'y' => 104, 'size' => 13), $row['free_kbn12'] == '0');
        $pdfObj->write_check(array('x' => 410, 'y' => 134, 'size' => 13), $row['free_kbn12'] == '1');
        if ($row['free_kbn12'] == '0') {
            $pdfObj->write_string(array('x' => 430, 'y' => 100, 'width' => 150, 'height' => 15, 'font_size' => 17,), $row['bumon_tel']);
        } else {
            $pdfObj->write_string(array('x' => 430, 'y' => 130, 'width' => 150, 'height' => 15, 'font_size' => 17,), $row['mobile_tel']);
        }
        // 葬家名
        $pdfObj->write_string(array('x' => 140, 'y' => 172, 'width' => 200, 'height' => 15, 'font_size' => 15,), $row['k_knm']);
        $pdfObj->write_string(array('x' => 140, 'y' => 212, 'width' => 200, 'height' => 15, 'font_size' => 18,), $row['k_nm']);
        $pdfObj->write_date(array('x' => 400, 'y' => 187, 'width' => 140, 'height' => 10, 'type' => 'time', 'align' => 'C',), $row['tuya_time'], "n月 j日 G 時 i 分");
        $pdfObj->write_date(array('x' => 400, 'y' => 227, 'width' => 140, 'height' => 10, 'type' => 'time', 'align' => 'C',), $row['s_output_dt'], "n月 j日 G 時 i 分");
        // 取り込み先
        $pdfObj->write_string(array('x' => 75, 'y' => 282, 'width' => 200, 'height' => 15, 'font_size' => 15, 'align' => 'C',), $row['s_input_bmn_cd'] . ' ' . $row['s_input_bmn_nm']);
        // 請求先
        $pdfObj->write_string(array('x' => 322, 'y' => 282, 'width' => 200, 'height' => 15, 'font_size' => 15, 'align' => 'C',), $row['s_output_bmn_cd'] . ' ' . $row['s_output_bmn_nm']);
        // データの取込先
        $pdfObj->write_check(array('x' => 63, 'y' => 342, 'size' => 13), $row['free_kbn1'] == '0');
        $pdfObj->write_string(array('x' => 80, 'y' => 343, 'width' => 60, 'height' => 15,), 'スキャナ');
        $pdfObj->write_check(array('x' => 150, 'y' => 342, 'size' => 13), $row['free_kbn1'] == '1');
        $pdfObj->write_string(array('x' => 167, 'y' => 343, 'width' => 60, 'height' => 15,), 'USB');
        $pdfObj->write_check(array('x' => 210, 'y' => 342, 'size' => 13), $row['free_kbn1'] == '2');
        $pdfObj->write_string(array('x' => 227, 'y' => 343, 'width' => 80, 'height' => 15,), 'デスクトップ');
        $pdfObj->write_string(array('x' => 325, 'y' => 343, 'width' => 210, 'height' => 15,), $row['v_free1']);
        // 着せ替えの選択
        $pdfObj->write_check(array('x' => 63, 'y' => 392, 'size' => 13), $row['s_kisekae_kbn'] == '0');
        $pdfObj->write_string(array('x' => 80, 'y' => 393, 'width' => 160, 'height' => 15,), '着せ替えしない(そのまま)');
        $pdfObj->write_check(array('x' => 255, 'y' => 392, 'size' => 13), $row['s_kisekae_kbn'] == '1');
        $pdfObj->write_string(array('x' => 272, 'y' => 393, 'width' => 140, 'height' => 15,), '着せ替えする');
        $pdfObj->write_string(array('x' => 405, 'y' => 388, 'width' => 150, 'height' => 15,), $row['huku_nm']);
        $pdfObj->write_string(array('x' => 348, 'y' => 402, 'width' => 198, 'height' => 15, 'size' => 10), $row['huku_nm2']);
        // 背景の選択
        $pdfObj->write_check(array('x' => 63, 'y' => 442, 'size' => 13), $row['s_haikei_kbn'] == '0');
        $pdfObj->write_string(array('x' => 80, 'y' => 443, 'width' => 160, 'height' => 15,), 'バック消し');
        $pdfObj->write_check(array('x' => 175, 'y' => 442, 'size' => 13), $row['s_haikei_kbn'] == '1');
        $pdfObj->write_string(array('x' => 192, 'y' => 443, 'width' => 200, 'height' => 15,), '背景そのまま(変更しない)');
        $pdfObj->write_string(array('x' => 405, 'y' => 435, 'width' => 220, 'height' => 15,), $row['haik_nm']);
        $pdfObj->write_string(array('x' => 348, 'y' => 450, 'width' => 198, 'height' => 15,'size' => 10), $row['haik_nm2']);
        // 家紋の選択
//        $pdfObj->write_string(array('x' => 150, 'y' => 496, 'width' => 150, 'height' => 15,), $row['s_kamon_cd']);
        $pdfObj->write_string(array('x' => 360, 'y' => 496, 'width' => 180, 'height' => 15,), $row['s_kamon_nm']);
        // 修正する箇所の指定
        $pdfObj->write_check(array('x' => 63, 'y' => 544, 'size' => 13), $row['free_kbn2'] == '1');
        $pdfObj->write_string(array('x' => 80, 'y' => 545, 'width' => 160, 'height' => 15,), '首の傾き');
        $pdfObj->write_check(array('x' => 153, 'y' => 544, 'size' => 13), $row['free_kbn3'] == '1');
        $pdfObj->write_string(array('x' => 170, 'y' => 545, 'width' => 160, 'height' => 15,), '体の傾き');
        $pdfObj->write_check(array('x' => 243, 'y' => 544, 'size' => 13), $row['free_kbn4'] == '1');
        $pdfObj->write_string(array('x' => 260, 'y' => 545, 'width' => 160, 'height' => 15,), '髪型');
        $pdfObj->write_check(array('x' => 313, 'y' => 544, 'size' => 13), $row['free_kbn5'] == '1');
        $pdfObj->write_string(array('x' => 330, 'y' => 545, 'width' => 160, 'height' => 15,), '衣服');
        $pdfObj->write_check(array('x' => 383, 'y' => 544, 'size' => 13), $row['free_kbn6'] == '1');
        $pdfObj->write_string(array('x' => 400, 'y' => 545, 'width' => 160, 'height' => 15,), '襟');
        $pdfObj->write_check(array('x' => 438, 'y' => 544, 'size' => 13), $row['free_kbn7'] == '1');
        $pdfObj->write_string(array('x' => 455, 'y' => 545, 'width' => 160, 'height' => 15,), '腕');
        $pdfObj->write_string(array('x' => 110, 'y' => 563, 'width' => 380, 'height' => 15,), $row['v_free2']);
        // 写真から削除する箇所の指定
        $pdfObj->write_check(array('x' => 63, 'y' => 612, 'size' => 13), $row['free_kbn8'] == '1');
        $pdfObj->write_string(array('x' => 80, 'y' => 613, 'width' => 160, 'height' => 15,), '装飾品すべて');
        $pdfObj->write_check(array('x' => 173, 'y' => 612, 'size' => 13), $row['free_kbn9'] == '1');
        $pdfObj->write_string(array('x' => 190, 'y' => 613, 'width' => 160, 'height' => 15,), 'ポケットの中身');
        $pdfObj->write_check(array('x' => 293, 'y' => 612, 'size' => 13), $row['free_kbn10'] == '1');
        $pdfObj->write_string(array('x' => 310, 'y' => 613, 'width' => 160, 'height' => 15,), 'イヤホン');
        $pdfObj->write_check(array('x' => 373, 'y' => 612, 'size' => 13), $row['free_kbn11'] == '1');
        $pdfObj->write_string(array('x' => 390, 'y' => 613, 'width' => 160, 'height' => 15,), '医療機器');
        $pdfObj->write_string(array('x' => 110, 'y' => 631, 'width' => 380, 'height' => 15,), $row['v_free3']);
        // 備考
        $pdfObj->write_strings(array('x' => 80, 'y' => 672, 'width' => 450, 'height' => 110,), $row['biko1']);

//        $pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
    }

	/**
	 * ビューアショット依頼書
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData02003($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);

		$rec = DataMapper_Pdf02003::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$row = $rec[0];

		$pdfObj->write_string(array('x' => 490, 'y' => 111, 'width' => 80, 'height' => 10, 'valign' => 'M', 'font_size' => 10), $row['kaisya_lnm']);

		// ご依頼日
		$pdfObj->write_date(array('x' => 90, 'y' => 113, 'width' => 210, 'height' => 10, 'type' => 'time', 'align' => 'C', 'font_size' => 16), $hachu_time, "Y年 n月 j日");

		$pdfObj->write_string(array('x' => 110, 'y' => 135, 'width' => 460, 'height' => 10, 'valign' => 'M'), $row['m_input_bmn_nm']);
		$pdfObj->write_string(array('x' => 110, 'y' => 150, 'width' => 460, 'height' => 10, 'valign' => 'M'), $row['m_output_bmn_nm']);
		$pdfObj->write_string(array('x' => 110, 'y' => 168, 'width' => 460, 'height' => 10, 'valign' => 'M', 'font_size' => 16), $row['m_seikyu_bmn_nm']);
		// ビューアーショットのみの依頼
		if ($row['m_view_shot_kbn'] == 1) {
			$pdfObj->write_string(array('x' => 230, 'y' => 195, 'width' => 460, 'height' => 10, 'font_size' => 16), '×');
		}

		// 故人の姓名
		$pdfObj->write_string(array('x' => 150, 'y' => 230, 'width' => 180, 'height' => 10, 'align' => 'C', 'font_size' => 16), $row['k_nm']);
		$pdfObj->write_string(array('x' => 150, 'y' => 245, 'width' => 180, 'height' => 10, 'align' => 'C', 'font_size' => 13), '（ ' . $row['souke_nm'] . '家 ）');
		$pdfObj->write_string(array('x' => 370, 'y' => 243, 'width' => 65, 'height' => 10, 'align' => 'C', 'font_size' => 16), $row['k_nenrei_man']);
		switch ($row['k_sex_kbn']) {
			case 1:
				$pdfObj->write_circle(array('x' => 486, 'y' => 248, 'width' => 14, 'height' => 14));
				break;
			case 2:
				$pdfObj->write_circle(array('x' => 515, 'y' => 247, 'width' => 14, 'height' => 14));
				break;
		}

		// 葬儀日
		$pdfObj->write_date(array('x' => 150, 'y' => 270, 'width' => 160, 'height' => 10, 'type' => 'time', 'align' => 'C', 'font_size' => 16), $row['sou_time'], "Y年 n月 j日 G時 i分");

		// 御担当
		$pdfObj->write_string(array('x' => 460, 'y' => 274, 'width' => 80, 'height' => 10, 'align' => 'C', 'font_size' => 14), $row['s_tanto_nm']);

		if ($row['m_view_shot_kbn'] == 0) {
			if (isset($row['m_muki_kbn'])) {
				// 頭の向き
				switch ($row['m_muki_kbn']) {
					case 0:
						$pdfObj->write_circle(array('x' => 186, 'y' => 301, 'width' => 24, 'height' => 24));
						break;
					case 1:
						$pdfObj->write_circle(array('x' => 253, 'y' => 301, 'width' => 24, 'height' => 24));
						break;
				}
			}

			switch ($row['m_haikei_kbn']) {
				case 0:
					$pdfObj->write_circle(array('x' => 50, 'y' => 333, 'width' => 105, 'height' => 15));
					break;
				case 1:
					$pdfObj->write_circle(array('x' => 340, 'y' => 333, 'width' => 150, 'height' => 15));
					break;
				case 2:
					$pdfObj->write_circle(array('x' => 50, 'y' => 504.5, 'width' => 150, 'height' => 15));
					$pdfObj->write_vstring(array('x' => 50, 'y' => 546, 'width' => 12, 'height' => 67, 'valign' => 'J', 'font_size' => 11), $row['kaisya_snm']);
					$k_nenrei_kyounen = App_ExcelUtil::num2kan($row['k_nenrei_man']);
					$pdfObj->write_vstring(array('x' => 181.5, 'y' => 575, 'width' => 12, 'height' => 30, 'valign' => 'J', 'font_size' => 11), $k_nenrei_kyounen);
					$k_nm = str_replace('　', '', $row['k_nm']);
					$k_nm = str_replace(' ', '', $k_nm);
					$pdfObj->write_vstring(array('x' => 215, 'y' => 552, 'width' => 12, 'height' => 63, 'valign' => 'J', 'font_size' => 12), $k_nm);
					break;
				case 3:
					$pdfObj->write_circle(array('x' => 340, 'y' => 504.5, 'width' => 150, 'height' => 15));
					$pdfObj->write_string(array('x' => 385, 'y' => 623, 'width' => 125, 'height' => 15, 'font_size' => 16), $row['gazo_cd2']);
					break;
			}
		}
		// 注意事項
		$pdfObj->write_strings(array('x' => 37, 'y' => 694, 'width' => 318, 'height' => 113, 'align' => 'L', 'valign' => 'T', 'font_size' => 13), $row['biko']);
	}

	/**
	 * オリジナル礼状専用申込書
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData02004($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(1000, 600);

		$rec = DataMapper_PdfCommon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$common = $rec[0];

		// 会社名
		$pdfObj->write_string(array('x' => 300, 'y' => 85, 'width' => 230, 'height' => 15, 'align' => 'C', 'font_size' => 16), $common['kaisya_lnm']);

		// 縦書きの出力
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_font_direction('v');

		$rec = DataMapper_PdfCommon::getOrderHead($db, $seko_no);
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$order = $rec[0];

		$rec = DataMapper_Pdf02004::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$orei = $rec[0];

		$rec = DataMapper_SekoKihon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$kihon = $rec[0];

		// 確認先
		$pdfObj->write_vstring(array('x' => 780, 'y' => 30, 'width' => 10, 'height' => 80, 'valign' => 'B'), $orei['bumon_snm']);

		$addr1 = '';
		$addr2 = '';
		if ($kihon['mg_kbn'] == 1) { // 故人宅
			$addr1 = $kihon['kg_addr1'];
			$addr2 = $kihon['kg_addr2'];
		} else  { // 喪主宅
            $addr1 = $kihon['mg_addr1'];
            $addr2 = $kihon['mg_addr2'];
		}
		// 故人
		$pdfObj->write_vstring(array('x' => 794, 'y' => 175, 'width' => 12, 'height' => 180, 'font_size' => 13), $kihon['k_knm']);
		$pdfObj->write_vstring(array('x' => 730, 'y' => 140, 'width' => 65, 'height' => 190, 'font_size' => 16), $kihon['k_nm']);
		$pdfObj->write_vstring(array('x' => 730, 'y' => 350, 'width' => 65, 'height' => 30, 'valign' => 'M', 'font_size' => 14), $orei['m_zoku2_nm']);
		//$pdfObj->write_vstring(array('x' => 695, 'y' => 140, 'width' => 15, 'height' => 300, 'font_size' => 16), App_ExcelUtil::num2kan($addr1));
		//$pdfObj->write_vstring(array('x' => 675, 'y' => 140, 'width' => 15, 'height' => 300, 'font_size' => 16), App_ExcelUtil::num2kan($addr2));
		$addr1 = App_ExcelUtil::num2kanEx($addr1);
		$addr2 = App_ExcelUtil::num2kanEx($addr2);
		$addr1_len = mb_strlen($addr1, 'UTF-8');
		$font1 = 16;
		if ($addr1_len > 36) {
			$font1 = 7;
		} else if ($addr1_len > 29) {
			$font1 = 8;
		} else if ($addr1_len > 24) {
			$font1 = 10;
		} else if ($addr1_len > 21) {
			$font1 = 12;
		} else if ($addr1_len > 18) {
			$font1 = 14;
		}
		$addr2_len = mb_strlen($addr2, 'UTF-8');
		$font2 = 16;
		if ($addr2_len > 36) {
			$font2 = 7;
		} else if ($addr2_len > 29) {
			$font2 = 8;
		} else if ($addr2_len > 24) {
			$font2 = 10;
		} else if ($addr2_len > 21) {
			$font2 = 12;
		} else if ($addr2_len > 18) {
			$font2 = 14;
		}
		$pdfObj->write_vstring(array('x' => 695, 'y' => 140, 'width' => 15, 'height' => 300, 'font_size' => $font1), App_ExcelUtil::num2kan($addr1));
		$pdfObj->write_vstring(array('x' => 675, 'y' => 140, 'width' => 15, 'height' => 300, 'font_size' => $font2), App_ExcelUtil::num2kan($addr2));


		// 喪主
		switch ($orei['m_kbn']) {
			case 0:
				$pdfObj->write_vstring(array('x' => 590, 'y' => 140, 'width' => 65, 'height' => 60, 'valign' => 'M', 'font_size' => 16), '喪主');
				break;
			case 1:
				$pdfObj->write_vstring(array('x' => 590, 'y' => 140, 'width' => 65, 'height' => 60, 'valign' => 'M', 'font_size' => 16), '施主');
				break;
		}
		$pdfObj->write_vstring(array('x' => 642, 'y' => 255, 'width' => 12, 'height' => 190, 'font_size' => 13), $kihon['m_knm']);
		$pdfObj->write_vstring(array('x' => 585, 'y' => 220, 'width' => 55, 'height' => 220, 'font_size' => 16), $kihon['m_nm']);

		// 親族名
		$x = 532;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 140, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm1']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 220, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm1']);
		$x -= 42;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 140, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm2']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 220, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm2']);
		$x -= 42;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 140, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm3']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 220, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm3']);
		$x -= 42;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 140, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm4']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 220, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm4']);
		$x -= 42;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 140, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm5']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 220, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm5']);

		// 会社・団体名
		$pdfObj->write_vstring(array('x' => 340, 'y' => 200, 'width' => 25, 'height' => 250, 'font_size' => 16), $orei['k_etc_nm']);
		$pdfObj->write_vstring(array('x' => 285, 'y' => 140, 'width' => 55, 'height' => 300, 'font_size' => 16), $orei['k_knm_nm']);

		// 命日
		$rec = DataMapper_SekoNitei::find($db, array("seko_no" => $seko_no, "nitei_kbn" => 1));
		if (count($rec) > 0) {
			$nitei = $rec[0];
			if (isset($nitei['nitei_ymd'])) {
				$pdfObj->write_vdate(array('x' => 260, 'y' => 188, 'width' => 20, 'font_size' => 13), $nitei['nitei_ymd'], "Y年 n月 j日 A g時 i分", 11, array('午前', '午後'));
			}
		}

		$rec = DataMapper_SekoNitei::find($db, array("seko_no" => $seko_no, "nitei_kbn" => 11));
		if (count($rec) > 0) {
			$nitei = $rec[0];
			if (isset($nitei['nitei_ymd'])) {
				// 告別式
				$pdfObj->write_vdate(array('x' => 230, 'y' => 188, 'width' => 20, 'font_size' => 13), $nitei['nitei_ymd'], "Y年 n月 j日 A g時 i分", 11, array('午前', '午後'));
			}
		}

		// 宗派
		$syuha = $orei['syushi_nm'];
		if (isset($orei['syuha_nm'])) {
			$syuha .= '　（' . $orei['syuha_nm'] . '）';
		}
		$pdfObj->write_vstring(array('x' => 187, 'y' => 200, 'width' => 40, 'height' => 240, 'font_size' => 16), $syuha);

		// 家紋
//        $rec_shas = DataMapper_SekoShasinInfo::find( $db, array( "seko_no" => $seko_no, "yakimashi_kbn" => 0 ) );
//        if (count($rec_shas) > 0) {
//            $syushi = $rec_shas[0];
//            if (strlen($syushi['s_kamon_nm']) > 0) {
//               $kamon_kbn = 'あり';
//            } else {
//               $kamon_kbn = 'なし';
//            }
//            $pdfObj->write_vstring(array('x' => 145, 'y' => 200, 'width' => 40, 'height' => 60, 'font_size' => 16), $kamon_kbn);
//            $pdfObj->write_vstring(array('x' => 145, 'y' => 300, 'width' => 40, 'height' => 140,'font_size' => 16), $syushi['s_kamon_nm']);
//        } else {
//            $pdfObj->write_vstring(array('x' => 145, 'y' => 200, 'width' => 40, 'height' => 60, 'font_size' => 16), 'なし');
//        }   
		if (strlen($orei['kamon_nm']) > 0) {
			$pdfObj->write_vstring(array('x' => 145, 'y' => 200, 'width' => 40, 'height' => 60, 'font_size' => 16), 'あり');
			$pdfObj->write_vstring(array('x' => 145, 'y' => 300, 'width' => 40, 'height' => 140, 'font_size' => 16), $orei['kamon_nm']);
		} else {
			$pdfObj->write_vstring(array('x' => 145, 'y' => 200, 'width' => 40, 'height' => 60, 'font_size' => 16), 'なし');
		}

		// 取材
		$pdfObj->write_vstring(array('x' => 80, 'y' => 140, 'width' => 50, 'height' => 150, 'font_size' => 16), $orei['sz_nm']);
		$rec = DataMapper_CodeNmMst::find($db, array('code_kbn' => '0190', 'kbn_value_cd' => $orei['sz_zoku_kbn']));
		if (count($rec) > 0) {
			$zoku = $rec[0];
			$pdfObj->write_vstring(array('x' => 80, 'y' => 310, 'width' => 55, 'height' => 40, 'valign' => 'M', 'font_size' => 16), $zoku['kbn_value_lnm']);
		}
		if (isset($orei['sz_st_kbn1'])) {
			$date = $orei['sz_ymd'] . ' ' . $orei['sz_st_kbn1'] . ':';
			switch ($orei['sz_st_kbn2']) {
				case 0:
					$date .= '00:00';
					break;
				case 1:
					$date .= '15:00';
					break;
				case 2:
					$date .= '30:00';
					break;
				case 3:
					$date .= '45:00';
					break;
			}
			$pdfObj->write_vdate(array('x' => 25, 'y' => 140, 'width' => 30, 'font_size' => 16), $date, "n月 j日 G時 i分");
		}

		// 横書きの出力
		$pdfObj->set_font_direction('h');
		$pdfObj->write_string(array('x' => 730, 'y' => 405, 'width' => 65, 'height' => 30, 'align' => 'C', 'font_size' => 16), $kihon['k_nenrei_man']);
		$pdfObj->write_string(array('x' => 30, 'y' => 375, 'width' => 110, 'height' => 15, 'align' => 'C', 'font_size' => 16), $orei['sz_tel']);
		$pdfObj->write_string(array('x' => 30, 'y' => 415, 'width' => 110, 'height' => 15, 'align' => 'C', 'font_size' => 16), $orei['sz_fax']);
		$pdfObj->write_num(array('x' => 30, 'y' => 480, 'width' => 80, 'height' => 15, 'font_size' => 16), $orei['ori_cnt']);
		$pdfObj->write_strings(array('x' => 425, 'y' => 473, 'width' => 190, 'height' => 68, 'font_size' => 11), $orei['hd_biko1'] . PHP_EOL . $orei['hd_biko2']);

		// 施行担当者
		$pdfObj->write_string(array('x' => 150, 'y' => 475, 'width' => 130, 'height' => 10, 'align' => 'C', 'font_size' => 16), $order['s_tanto_nm']);
		// 請求先
		$pdfObj->write_string(array('x' => 635, 'y' => 480, 'width' => 170, 'height' => 10, 'font_size' => 16), $order['bumon_lnm']);
	}

	/**
	 * お礼状（定型文）
	 * 
	 * <AUTHOR> Yanagiso
	 * @since      2015/02/17
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData02005($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);     

		$rec = DataMapper_PdfCommon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$common = $rec[0];

		// 縦書きの出力
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_font_direction('v');

		$rec = DataMapper_PdfCommon::getOrderHead($db, $seko_no);
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$order = $rec[0];

		$rec = DataMapper_Pdf02004::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$orei = $rec[0];

		$rec = DataMapper_SekoKihon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$kihon = $rec[0];

		$addr1 = '';
		$addr2 = '';
		if ($kihon['souke_addr_cd'] == 1) { // 故人宅
			$addr1 = $kihon['kg_addr1'];
			$addr2 = $kihon['kg_addr2'];
		} else if ($kihon['souke_addr_cd'] == 2) { // 喪主宅
			$basho = DataMapper_SekoNohinBasho::find($db, $seko_no, array('basyo_kbn_cd' => 2));
			if (count($basho) > 0) {
				$addr1 = $basho[0]['addr1'];
				$addr2 = $basho[0]['addr2'];
			}
		}
		// 故人
		$pdfObj->write_vstring(array('x' => 560, 'y' => 500, 'width' => 12, 'height' => 180, 'font_size' => 12), $kihon['k_knm']);
		$pdfObj->write_vstring(array('x' => 488, 'y' => 460, 'width' => 65, 'height' => 190, 'font_size' => 16), $kihon['k_nm']);
		$pdfObj->write_vstring(array('x' => 488, 'y' => 660, 'width' => 65, 'height' => 30, 'valign' => 'M', 'font_size' => 14), $orei['m_zoku2_nm']);
		//$pdfObj->write_vstring(array('x' => 445, 'y' => 460, 'width' => 15, 'height' => 300, 'font_size' => 16), App_ExcelUtil::num2kan($addr1));
		//$pdfObj->write_vstring(array('x' => 425, 'y' => 460, 'width' => 15, 'height' => 300, 'font_size' => 16), App_ExcelUtil::num2kan($addr2));
		$addr1 = App_ExcelUtil::num2kan($addr1);
		$addr2 = App_ExcelUtil::num2kan($addr2);
		$addr1_len = mb_strlen($addr1, 'UTF-8');
		$font1 = 16;
		if ($addr1_len > 36) {
			$font1 = 7;
		} else if ($addr1_len > 29) {
			$font1 = 8;
		} else if ($addr1_len > 24) {
			$font1 = 10;
		} else if ($addr1_len > 21) {
			$font1 = 12;
		} else if ($addr1_len > 18) {
			$font1 = 14;
		}
		$addr2_len = mb_strlen($addr2, 'UTF-8');
		$font2 = 16;
		if ($addr2_len > 36) {
			$font2 = 7;
		} else if ($addr2_len > 29) {
			$font2 = 8;
		} else if ($addr2_len > 24) {
			$font2 = 10;
		} else if ($addr2_len > 21) {
			$font2 = 12;
		} else if ($addr2_len > 18) {
			$font2 = 14;
		}
		$pdfObj->write_vstring(array('x' => 445, 'y' => 460, 'width' => 15, 'height' => 300, 'font_size' => $font1), App_ExcelUtil::num2kan($addr1));
		$pdfObj->write_vstring(array('x' => 425, 'y' => 460, 'width' => 15, 'height' => 300, 'font_size' => $font2), App_ExcelUtil::num2kan($addr2));

		// 喪主
		switch ($orei['m_kbn']) {
			case 0:
				$pdfObj->write_vstring(array('x' => 340, 'y' => 457, 'width' => 65, 'height' => 60, 'valign' => 'M', 'font_size' => 16), '喪主');
				break;
			case 1:
				$pdfObj->write_vstring(array('x' => 340, 'y' => 457, 'width' => 65, 'height' => 60, 'valign' => 'M', 'font_size' => 16), '施主');
				break;
		}
		$pdfObj->write_vstring(array('x' => 395, 'y' => 570, 'width' => 12, 'height' => 190, 'font_size' => 13), $kihon['m_knm']);
		$pdfObj->write_vstring(array('x' => 340, 'y' => 540, 'width' => 55, 'height' => 220, 'font_size' => 16), $kihon['m_nm']);

		// 親族名
		$x = 285;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 457, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm1']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 540, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm1']);
		$x -= 41;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 457, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm2']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 540, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm2']);
		$x -= 41;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 457, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm3']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 540, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm3']);
		$x -= 41;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 457, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm4']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 540, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm4']);
		$x -= 41;
		$pdfObj->write_vstring(array('x' => $x, 'y' => 457, 'width' => 40, 'height' => 60, 'valign' => 'M', 'font_size' => 16), $orei['zoku_nm5']);
		$pdfObj->write_vstring(array('x' => $x, 'y' => 540, 'width' => 40, 'height' => 220, 'font_size' => 16), $orei['kankei_nm5']);

		// 会社・団体名
		$pdfObj->write_vstring(array('x' => 92, 'y' => 515, 'width' => 25, 'height' => 250, 'font_size' => 16), $orei['k_etc_nm']);
		$pdfObj->write_vstring(array('x' => 30, 'y' => 470, 'width' => 55, 'height' => 300, 'font_size' => 16), $orei['k_knm_nm']);

		//============
		// 横書き
		//============
		$pdfObj->set_font_direction('h');
		//故人の年齢
		$pdfObj->write_string(array('x' => 492, 'y' => 712, 'width' => 65, 'height' => 30, 'align' => 'C', 'font_size' => 16), $kihon['k_nenrei_man']);
		//納品枚数
		$pdfObj->write_string(array('x' => 270, 'y' => 347, 'width' => 50, 'height' => 30, 'font_size' => 12), $orei['teikei_cnt']);
		//定型文パターン
		$pdfObj->write_string(array('x' => 455, 'y' => 317, 'width' => 100, 'height' => 30, 'font_size' => 12, 'align' => 'C'), $orei['title_nm']);
		// 宗派
		$syuha = $orei['syushi_nm'];
		$pdfObj->write_string(array('x' => 490, 'y' => 347, 'width' => 100, 'height' => 30, 'font_size' => 12), $syuha);
		// 家紋
//        $rec_shas = DataMapper_SekoShasinInfo::find( $db, array( "seko_no" => $seko_no, "yakimashi_kbn" => 0 ) );
//        if (count($rec_shas) > 0) {
//            $syushi = $rec_shas[0];         
//            $pdfObj->write_string(array('x' => 240, 'y' => 407, 'width' => 100, 'height' => 30,'font_size' => 12), $syushi['gazo_nm']);            
//            $pdfObj->write_string(array('x' => 380, 'y' => 407, 'width' => 100, 'height' => 30,'font_size' => 12), $syushi['s_kamon_nm']);
//        }  
		$pdfObj->write_string(array('x' => 255, 'y' => 407, 'width' => 100, 'height' => 30, 'font_size' => 12), $orei['kamon_code']);
		$pdfObj->write_string(array('x' => 380, 'y' => 407, 'width' => 100, 'height' => 30, 'font_size' => 12), $orei['kamon_nm']);

		if ($orei['hagaki_size'] == 0) {
			$pdfObj->write_circle(array('x' => 134, 'y' => 292, 'width' => 70, 'height' => 20));
		} else if ($orei['hagaki_size'] == 1) {
			$pdfObj->write_circle(array('x' => 208, 'y' => 292, 'width' => 80, 'height' => 20));
		} else if ($orei['hagaki_size'] == 2) {
			$pdfObj->write_circle(array('x' => 293, 'y' => 292, 'width' => 75, 'height' => 20));
		}

		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
	}

	/**
	 * 施行発注管理情報を取得
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $ha_rp_kbn = null) {
		$rec_ary = array();
		foreach ($hachu_no_ary as $hachu_no) {
			$param = array("seko_no" => $seko_no, "hachu_no" => $hachu_no);
			if (isset($siire_cd)) {
				$param['siire_cd'] = $siire_cd;
			}
			if (isset($report_cd)) {
				$param['report_cd'] = $report_cd;
			}
			if (isset($ha_rp_kbn)) {
				$param['ha_rp_kbn'] = $ha_rp_kbn;
			}
			$ary = DataMapper_SekoHachuInfo::find($db, $param);
			if (count($ary) > 0) {
				$rec_ary[] = $ary[0];
			}
		}
		return $rec_ary;
	}

	/**
	 * 施行喪服情報明細を取得
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $tr_kbn	0:貸衣裳 1:販売品
	 * @return array　$rec_ary　施行喪服情報明細
	 */
	private function getSekoMofukuMsi($db, $seko_no, $hachu_no_ary, $siire_cd, $tr_kbn) {
		$rec_ary = array();
		if (isset($hachu_no_ary)) {
			foreach ($hachu_no_ary as $hachu_no) {
				$param = array("seko_no" => $seko_no, "hachu_no" => $hachu_no, "siire_cd" => $siire_cd, "tr_kbn" => $tr_kbn);
				$ary = DataMapper_Pdf0205::find($db, $param);
				foreach ($ary as $rec) {
					$rec_ary[] = $rec;
				}
			}
		} else {
			$param = array("seko_no" => $seko_no, "siire_cd" => $siire_cd, "tr_kbn" => $tr_kbn);
			$rec_ary = DataMapper_Pdf0205::find($db, $param);
		}
		return $rec_ary;
	}

    /**
     * 施行車輌情報明細を取得
     * 
     * <AUTHOR> Sato
     * @since      2014/03/25
     * @param Msi_Sys_Db $db DB
     * @param string $seko_no		施工番号
     * @param array $hachu_no_ary	施行発注管理情報 配列
     * @param string $siire_cd		仕入先コード
     * @param string $sy_used_kbn	1:寝台・移送車 2:霊柩車 3:送迎車輌
     * @return array $rec_ary 施行車輌情報明細
     */
    private function getSekoSyaryoMsi($db, $seko_no, $hachu_no_ary, $siire_cd, $sy_used_kbn) {
        $rec_ary = array();
        if (isset($hachu_no_ary)) {
            foreach ($hachu_no_ary as $hachu_no) {
                $param = array( "seko_no" => $seko_no, "hachu_no" => $hachu_no, "siire_cd" => $siire_cd, "sy_used_kbn" => $sy_used_kbn );
                $ary = DataMapper_SekoSyaryoMsi::find( $db, $param );
                foreach ($ary as $rec) {
                    $rec_ary[] = $rec;
                }
            }
        } else {
            $param = array( "seko_no" => $seko_no, "siire_cd" => $siire_cd, "sy_used_kbn" => $sy_used_kbn );
            $rec_ary = DataMapper_SekoSyaryoMsi::find( $db, $param );
        }
        return $rec_ary;
    }

	/**
	 * 出棺場所・火葬場を取得
	 * 
	 * <AUTHOR> Yanagiso
	 * @since      2015/02/16
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @return array $rec_ary 施行車輌情報明細
	 */
	private function getSekoSyaryoMsi_place($db, $seko_no) {
		$rec_ary = array();
		$param = array("seko_no" => $seko_no);
		$rec_ary = DataMapper_SekoSyaryoMsi::find_place($db, $param);

		return $rec_ary;
	}

	/**
	 * 施行発注管理情報 配列を発注書単位に分ける
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param array $hachu_ary 施行発注管理情報 配列
	 * @return array $ret 施行発注管理情報
	 */
	private function dividePerHachusho($hachu_ary) {
		$ret = array();
		foreach ($hachu_ary as $hachu) {
			//$id = $hachu['siire_cd'].'-'.$hachu['report_cd'].'-'.$hachu['ha_syori_kbn'];
			$id = $hachu['siire_cd'] . '-' . $hachu['report_cd'] . '-' . $hachu['ha_syori_kbn'] . '-' . $hachu['shiire_zei_kbn'];
			if (!isset($ret[$id])) {
				$ret[$id] = array(
					'siire_cd' => $hachu['siire_cd'],
					'report_cd' => $hachu['report_cd'],
					'ha_syori_kbn' => $hachu['ha_syori_kbn'],
					'shiire_zei_kbn' => $hachu['shiire_zei_kbn'],
					'hachu_no_ary' => array());
			}
			$ret[$id]['hachu_no_ary'][] = $hachu['hachu_no'];
		}
		return $ret;
	}

	/**
	 * 施行発注管理情報 配列を発注書単位に分ける（料理用）
	 * 
	 * <AUTHOR> Yanagiso
	 * @since      2015/03/18
	 * @param array $hachu_ary 施行発注管理情報 配列
	 * @return array $ret 施行発注管理情報
	 */
	private function dividePerHachusho_ryori($hachu_ary) {
		$ret = array();
		foreach ($hachu_ary as $hachu) {
			$id = $hachu['siire_cd'].'-'.$hachu['report_cd'].'-'.$hachu['ha_syori_kbn'];
			//$id = $hachu['siire_cd'] . '-' . $hachu['report_cd'] . '-' . $hachu['ha_syori_kbn'] . '-' . $hachu['shiire_zei_kbn'] . '-' . $hachu['category_kbn'];
			if (!isset($ret[$id])) {
				$ret[$id] = array(
					'siire_cd' => $hachu['siire_cd'],
					'report_cd' => $hachu['report_cd'],
					'ha_syori_kbn' => $hachu['ha_syori_kbn'],
					'shiire_zei_kbn' => $hachu['shiire_zei_kbn'],
					'hachu_no_ary' => array(),
					'category_kbn' => $hachu['category_kbn']
				);
			}
			$ret[$id]['hachu_no_ary'][] = $hachu['hachu_no'];
		}
		return $ret;
	}

	/**
	 * 施行発注管理情報 配列を発注書単位に分ける（車輛用）
	 * 
	 * <AUTHOR> Yanagiso
	 * @since      2015/03/18
	 * @param array $hachu_ary 施行発注管理情報 配列
	 * @return array $ret 施行発注管理情報
	 */
	private function dividePerHachusho_sharyo($hachu_ary) {
		$ret = array();
		$id = 0;
		foreach ($hachu_ary as $hachu) {
			//$id = $hachu['siire_cd'].'-'.$hachu['report_cd'].'-'.$hachu['ha_syori_kbn'];
			if (!isset($ret[$id])) {
				$ret[$id] = array(
					'siire_cd' => $hachu['siire_cd'],
					'report_cd' => $hachu['report_cd'],
					'ha_syori_kbn' => $hachu['ha_syori_kbn'],
					'shiire_zei_kbn' => $hachu['shiire_zei_kbn'],
					'hachu_no_ary' => array()
				);
			}
			$ret[$id]['hachu_no_ary'][] = $hachu['hachu_no'];
			$id = $id + 1;
		}
		return $ret;
	}

	/**
	 * エラー処理
	 * 
	 * <AUTHOR> Sato
	 * @since      2014/03/25
	 * @param string $status
	 * @return string $msg エラーメッセージ
	 */
	private function getErrMsg($status) {
		switch ($status) {
			case self::ERR_STATUS_UNJUST_PARAMETER:
				$msg = 'パラメーターが不正です。';
				break;
			case self::ERR_STATUS_NODATA:
				$msg = 'データがありません。';
				break;
			default:
				$msg = 'エラー[' . $status . ']';
		}
		return $msg;
	}

	/**
	 * 遺品装飾　発注書
	 *
	 * <AUTHOR> YAnagiso 
	 * @since      2015/04/16 
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0206_sub($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
		//$pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		//$pdfObj->hachu_cmn_out_seika($seko_no, $siire_cd, $report_cd, $hachu_time);
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		$rec = DataMapper_SekoKihon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$recKihon = $rec[0];

		$seika = DataMapper_SekoSeikaInfo::find($db, array("seko_no" => $seko_no));
		if (count($seika) > 0) {
			// 故人の好きな花
			$pdfObj->write_string(array('x' => 110, 'y' => 278, 'width' => 50, 'height' => 15), $seika[0]['fl_color1_nm']); // 第一希望
			$pdfObj->write_string(array('x' => 165, 'y' => 278, 'width' => 50, 'height' => 15), $seika[0]['fl_color2_nm']); // 第二希望
			$pdfObj->write_string(array('x' => 220, 'y' => 278, 'width' => 50, 'height' => 15), $seika[0]['fl_color3_nm']); // 第三希望
			$pdfObj->write_string(array('x' => 275, 'y' => 278, 'width' => 295, 'height' => 15), $seika[0]['fl_etc_memo']); // 備考
			// 備考
			$pdfObj->write_strings(array('type' => 'strings', 'x' => 401, 'y' => 585, 'width' => 190, 'height' => 210, 'font_size' => 10), $seika[0]['biko1']);
		}

		$row_height = 16.3;
		$sum = 0;   // 合計
		// カラムの設定
		$set_arr1[] = array('x' => 25, 'y' => 311, 'width' => 70, 'height' => 15);
		$set_arr1[] = array('x' => 95, 'y' => 311, 'width' => 70, 'height' => 15, 'type' => 'num');
		$set_arr1[] = array('x' => 175, 'y' => 311, 'width' => 44, 'height' => 15, 'type' => 'num');
		$set_arr1[] = array('x' => 273, 'y' => 311, 'width' => 110, 'height' => 15, 'align' => 'C');
		$set_arr1[] = array('x' => 385, 'y' => 311, 'width' => 190, 'height' => 15);

		// 施行発注管理情報を取得
		$rec_ary = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd);
		// 値の設定　遺品装飾
		$row_arr1 = array();
		foreach ($rec_ary as $rec) {
			if (!in_array($rec['hachu_no'], $hachu_no_ary)) {
				continue;
			}

			if (isset($rec['nonyu_ymd']) && isset($rec['nonyu_ymd2'])) {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd'])) . '～' . date('G:i', strtotime($rec['nonyu_ymd2']));
			} else if (isset($rec['nonyu_ymd'])) {
				$nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd']));
			} else {
				$nonyu_dt = null;
			}
			$row = array();
			$row[] = $rec['v_free1'];
			$row[] = $rec['hachu_tnk'];
			$row[] = $rec['hachu_suryo'];
			$row[] = $nonyu_dt;
			//$row[] = $rec['nonyu_ymd'];
			$row[] = $rec['nonyu_nm'];
			$row_arr1[] = $row;
			$sum += $rec['hachu_prc'];
			if (count($row_arr1) >= 7) {
				break;
			}
		}

		if (count($row_arr1) > 0) {
			// 表形式で書き込む
			$pdfObj->write_table_simple($set_arr1, $row_height, $row_arr1);
		}

		/*
		  // カラムの設定（生花・お花（その他）
		  $set_arr2[] = array('x' =>  25, 'y' => 441, 'width' =>  70, 'height' => 15);
		  $set_arr2[] = array('x' =>  95, 'y' => 441, 'width' =>  65, 'height' => 15, 'type' => 'num');
		  $set_arr2[] = array('x' => 175, 'y' => 441, 'width' =>  40, 'height' => 15, 'type' => 'num');
		  $set_arr2[] = array('x' => 273, 'y' => 441, 'width' => 108, 'height' => 15, 'align' => 'C');
		  $set_arr2[] = array('x' => 385, 'y' => 441, 'width' => 108, 'height' => 15);
		  $set_arr2[] = array('x' => 495, 'y' => 441, 'width' =>  80, 'height' => 15);

		  // 施行発注管理情報を取得
		  $rec_ary = DataMapper_Pdf0206::find($db, array('seko_no' => $seko_no, 'seika_kbn' => 2));

		  $jusyo_kbn = false;
		  // 値の設定　生花・お花（その他）
		  $row_arr2 = array();
		  foreach ($rec_ary as $rec) {
		  if (!in_array($rec['hachu_no'], $hachu_no_ary)) { continue; }

		  if (isset($rec['nonyu_ymd']) && isset($rec['nonyu_ymd2'])) {
		  $nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd'])).'～'.date('G:i', strtotime($rec['nonyu_ymd2']));
		  } else if (isset($rec['nonyu_ymd'])) {
		  $nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd']));
		  } else {
		  $nonyu_dt = null;
		  }
		  if (($rec['delivery_kbn'] == 1) or ($rec['delivery_kbn'] == 2)){
		  $jusyo_kbn = true;
		  }
		  $row = array();
		  $row[] = $rec['v_free1'];
		  $row[] = $rec['hachu_tnk'];
		  $row[] = $rec['hachu_suryo'];
		  $row[] = $nonyu_dt;
		  //$row[] = $rec['nonyu_ymd'];
		  $row[] = $rec['v_free2'];
		  $row[] = $rec['nonyu_nm'];
		  $row_arr2[] = $row;
		  $sum += $rec['hachu_prc'];
		  if (count($row_arr2) >= 7) { break; }
		  }

		  if (count($row_arr2) > 0) {
		  // 表形式で書き込む
		  $pdfObj->write_table_simple($set_arr2, $row_height, $row_arr2);
		  }
		 */
		// 合計
		$pdfObj->write_num(array('x' => 95, 'y' => 554, 'width' => 65, 'height' => 15), $sum);

		/*
		  if ($jusyo_kbn == true){
		  $addr1 = '';
		  $addr2 = '';
		  if ($recKihon['souke_addr_cd'] == 1) { // 故人宅
		  $addr1 =  $recKihon['kg_addr1'];
		  $addr2 =  $recKihon['kg_addr2'];
		  } else if ($recKihon['souke_addr_cd'] == 2) { // 喪主宅
		  $basho = DataMapper_SekoNohinBasho::find($db, $seko_no, array('basyo_kbn_cd' => 2));
		  if (count($basho) > 0) {
		  $addr1 =  $basho[0]['addr1'];
		  $addr2 =  $basho[0]['addr2'];
		  }
		  }
		  $pdfObj->write_string(array('x' =>75, 'y' =>407, 'width' => 430, 'height' => 15, 'font_size' => 10), $addr1.' '.$addr2);	// 故人住所
		  }
		 */
	}
    
    /**
     * 供花・供物発注書
     * 
     * <AUTHOR> Sai
     * @since      2015/07/02
     * @param object $pdfObj PDFオブジェクト
     * @param Msi_Sys_Db $db DB
     * @param string $seko_no		施工番号
     * @param array $hachu_no_ary	施行発注管理情報 配列
     * @param string $siire_cd		仕入先コード
     * @param string $report_cd		帳票コード
     * @param string $hachu_time	発注日時
     * @return void
     */
    private function outData0214($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
        $pdfObj->set_default_font_size(12);
        $pdfObj->set_default_font_family_h('kozgopromedium');
        if($this->_moushi_kbn == '2'){
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileNameHouji[$report_cd]);
            // 発注共通項目
            $pdfObj->hachu_cmn_out_houji($seko_no, $siire_cd, $report_cd, $hachu_time);
        }else{
            $pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);
            // 発注共通項目
            $this->hachu_cmn_out($pdfObj, $seko_no, $siire_cd, $report_cd, $hachu_time);
        }

        // 確認用のグリッドを出力
        //$pdfObj->test_line_out(600, 1000);
        //フッダー項目を出力
        $pdfObj->hachu_footer_out(470, 790, $seko_no, null);

        $meisai_top = 300;
        $row_height = 45.5;
        $row_count = 8;

        // カラムの設定
        $set_arr[] = array('x' =>  28, 'y' => $meisai_top,      'width' => 120, 'height' => 15, 'font_size' => 11); //商品名
        $set_arr[] = array('x' => 137, 'y' => $meisai_top,      'width' =>  50, 'height' => 15, 'font_size' => 11, 'type' => 'num'); //数量
        $set_arr[] = array('x' => 185, 'y' => $meisai_top,      'width' =>  70, 'height' => 15, 'font_size' => 11, 'type' => 'num'); //単価
        $set_arr[] = array('x' => 246, 'y' => $meisai_top,      'width' =>  80, 'height' => 15, 'font_size' => 11, 'type' => 'num'); //金額
        $set_arr[] = array('x' => 330, 'y' => $meisai_top,      'width' => 120, 'height' => 15, 'font_size' => 11, 'align' => 'C'); //届け日
        $set_arr[] = array('x' => 455, 'y' => $meisai_top,      'width' => 120, 'height' => 15, 'font_size' => 11); //届け先
        $set_arr[] = array('x' =>  80, 'y' => $meisai_top + 21, 'width' => 250, 'height' => 15, 'font_size' => 11); //届け先住所
        $set_arr[] = array('x' => 363, 'y' => $meisai_top + 21, 'width' => 215, 'height' => 15, 'font_size' => 11); //備考
        // 施行発注管理情報を取得
        $rec_ary = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd);

        // 値の設定
        $row_arr = array();
        foreach ($rec_ary as $rec) {
            $nonyu_dt = null;
            if (isset($rec['nonyu_ymd']) && isset($rec['nonyu_ymd2'])) {
                if ($rec['nonyu_ymd_inp'] == 1 && $rec['nonyu_ymd2_inp'] == 1)  {
                    $nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd'])) . '～' . date('G:i', strtotime($rec['nonyu_ymd2']));
                } else {
                    if ($rec['nonyu_ymd_inp'] == 1) {
                        $nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd'])) . '～';
                    } else {
                        if ($rec['nonyu_ymd2_inp'] == 1) {
                            $nonyu_dt = date('Y/n/j', strtotime($rec['nonyu_ymd'])) . '～' . date('G:i', strtotime($rec['nonyu_ymd2']));
                        } else {
                            $nonyu_dt = date('Y/n/j', strtotime($rec['nonyu_ymd']));
                        }
                    }
                }
            } else if (isset($rec['nonyu_ymd'])) {
                if ($rec['nonyu_ymd_inp'] == 1) {
                    $nonyu_dt = date('Y/n/j G:i', strtotime($rec['nonyu_ymd']));
                } else {
                    $nonyu_dt = date('Y/n/j', strtotime($rec['nonyu_ymd']));
                }    
            }
            $row = array();
            $row[] = $rec['shohin_nm'];
            $row[] = $rec['hachu_suryo'];
            $row[] = $rec['tnk'];
            $kingaku = null;
            if (isset($rec['hachu_suryo']) && isset($rec['tnk'])) {
                $kingaku = $rec['hachu_suryo'] * $rec['tnk'];
            }
            $row[] = $kingaku;
            $row[] = $nonyu_dt;
            $row[] = $rec['nonyu_nm'];
            $row[] = $rec['nonyu_addr1'] . $rec['nonyu_addr2'];
            $row[] = $rec['hd_biko1'];

            $row_arr[] = $row;
            if (count($row_arr) >= $row_count)
                break;
        }

        if (count($row_arr) > 0) {
            // 表形式で書き込む
            $pdfObj->write_table_simple($set_arr, $row_height, $row_arr);
        }
        // 発信情報を設定
        $this->setHassinInfo($db, $pdfObj, $seko_no, $hachu_no_ary);
        // 備考データを取得
        $row = DataMapper_PdfCommon::findSekoKanri($db, array('seko_no' => $seko_no, 'seko_no_sub' => '00', 'data_sbt' => '3', 'item_no' => '12',));
        if (count($row) > 0) {
            $pdfObj->write_strings(array('x' => 28, 'y' => 672, 'width' => 600, 'height' => 60, 'font_size' => 11), $row[0]["v_free1"]);
        }
    }
    
    /**
     * 発注共通項目を出力
     * 
     * <AUTHOR> Sato
     * @since 2014/03/13
     * @version 2014/06/29 MSI Sai pdf.phpより引越し
     * @param object $pdfObj PDFオブジェクト
     * @param string $seko_no 施行番号
     * @param string $seko_no 仕入先コード
     * @param string $report_cd 帳票コード
     * @param string $hachu_time 発注日時
     * @param string $category_kbn カテゴリ区分
     * @param string $title 発注書タイトル
     * @param boolean $flg 1:精進揚げ 2:出棺
     */
    private function hachu_cmn_out($pdfObj, $seko_no, $siire_cd, $report_cd, $hachu_time, $category_kbn = null, $title = null, $flg = 0) {

        $db = Msi_Sys_DbManager::getMyDb();

        // 発注書タイトル
        if (is_null($category_kbn)) {
            $pdfObj->hachu_title_out($db, $report_cd, 15, 35);
        } else {
            // MYTODO とりあえず持ち帰りは精進揚げに変える
            if ($category_kbn == '33') {
                $title = '精進揚げ';
            }
            $pdfObj->write_string(array('x' => 15, 'y' => 35, 'width' => 550, 'height' => 36, 'font_size' => 22, 'align' => 'C'), $title . '発注書');
        }

        // 会社ロゴ
        $this->kaisyalogo_out_hachu($pdfObj, $db, $seko_no, 430, 60, false, $report_cd);

        $x = 0;
        $y = 0;
        // 発注ヘッダ項目
        $recOrderHead = DataMapper_PdfCommon::getOrderHead($db, $seko_no, $siire_cd);
        if (count($recOrderHead) > 0) {
            $recOrderHead[0]['hachu_time'] = $hachu_time;
            $pdfObj->hachu_head_out($recOrderHead[0], $x, $y, $flg);
        }
    }
    
    /**
     * 会社ロゴを出力
     * 
     * <AUTHOR> Yanagiso
     * @since 2014/03/28
     * @version 2014/06/29 MSI Sai pdf.phpより引越し
     * @param object $pdfObj PDFオブジェクト
     * @param type $db
     * @param type $x
     * @param type $y
     */
    private function kaisyalogo_out_hachu($pdfObj, $db, $seko_no, $x, $y, $nomal = true, $report_cd = null) {
        $common = DataMapper_PdfCommon::find($db, array("seko_no" => $seko_no));
        if (count($common) > 0) {
            // 貸布団の場合正式部門名（依頼元）をnullにする
            if('00042' == $report_cd){
                $common[0]['bumon_lnm'] = null;
                $common[0]['tel']		= null;
                $common[0]['fax']		= null;
            }
            $logo = null;
            if (isset($common[0]['logo_img'])) {
                $logo = $db->readBlobCont($common[0]['logo_img']);
            }
            if ($nomal) {
                $pdfObj->kaisyalogo_ary_out_hachu_kouseisya(
                        $x, $y, array(
                    'logo'		=> $logo,
                    'nm'		=> $common[0]['kaisya_lnm'],
                    'bumon_nm'	=> $common[0]['bumon_lnm'],
                    'bumon_tel' => $common[0]['tel'],
                    'bumon_fax' => $common[0]['fax']	// 2017/09/18 ADD Kayo
                        )
                );
            } else {
				if($report_cd == '00014'){
					$pdfObj->kaisyalogo_ary_out_hachu_kouseisya(
							$x, $y, array(
						'logo'		=> $logo,
						'nm'		=> $common[0]['kaisya_lnm'],
						'bumon_nm'	=> $common[0]['bumon_lnm'],
						'bumon_tel' => $common[0]['tel'],
						'bumon_fax' => $common[0]['fax']	// 2017/09/18 ADD Kayo
							)
							, 230    //$width=110
							, 33     //$logo_size=15
							, 16     //$k_font_size=null
							, 14     //$b_font_size=null
							, 15	 //TEL pos
							, 28	 //FAX pos 
					);
				} else {	
					$pdfObj->kaisyalogo_ary_out_hachu_kouseisya(
							$x, $y, array(
						'logo'		=> null,
						'nm'		=> $common[0]['kaisya_lnm'],
						'bumon_nm'	=> $common[0]['bumon_lnm'],
						'bumon_tel' => $common[0]['tel'],
						'bumon_fax' => $common[0]['fax']	// 2017/09/18 ADD Kayo
							)
							, 600    //$width=110
							, 100    //$logo_size=15
							, 16     //$k_font_size=null
							, 14     //$b_font_size=null
					);
				}	
            }
        }
    }
    
    /**
     * 発信情報設定処理
     * 
     * <AUTHOR> Sai
     * @since      2015/06/29
     * @param Msi_Sys_Db $db DB
     * @param object $pdfObj PDFオブジェクト
     * @param string $seko_no 施行番号
     * @param array $hachu_no_ary 発注番号 配列
     * @param array $keyMapper	発信情報座標 配列
     * @param string $report_cd 帳票コード
     * @return void
     */
    private function setHassinInfo($db, $pdfObj, $seko_no, $hachu_no_ary, $keyMapper = null,$report_cd = null) {
        if (count($hachu_no_ary) > 0) {
            $hassinInfo = array("h1" => array('x' => 95, 'y' => 752), "h2" => array('x' => 290, 'y' => 752), "h3" => array('x' => 455, 'y' => 752));
            if (!is_null($keyMapper)) {
                $hassinInfo = array_merge($hassinInfo, $keyMapper);
            }
            $rec = DataMapper_Pdf0203::getHassinInfo($db, array('seko_no' => $seko_no, 'hachu_no' => $hachu_no_ary[0]));
            if (count($rec) > 0) {
                // 貸布団以外の場合は部門名（発信部署）表示させる
                if('00042' != $report_cd){
                    $pdfObj->write_string(array('x' => $hassinInfo['h1']['x'], 'y' => $hassinInfo['h1']['y'], 'width' => 150, 'height' => 10), $rec['bumon_nm']);
                }
                $pdfObj->write_string(array('x' => $hassinInfo['h2']['x'], 'y' => $hassinInfo['h2']['y'], 'width' => 120, 'height' => 10), $rec['order_tanto_nm']);
                $pdfObj->write_string(array('x' => $hassinInfo['h3']['x'], 'y' => $hassinInfo['h3']['y'], 'width' => 120, 'height' => 10), $rec['chk_order_tanto_nm']);
            }
        }
    }

	/**
	 * 寺院依頼書
	 * 
	 * <AUTHOR> Yanagiso
	 * @since      2015/03/18
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0209($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		$rec = DataMapper_SekoKihon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$recKihon = $rec[0];

		//ヘッダー
		$pdfObj->write_string(array('x' => 80, 'y' => 214, 'width' => 112, 'height' => 15, 'font_size' => 10, 'font_size' => 14, 'align' => 'C'), $recKihon['k_nm'] . '様');
		$pdfObj->write_string(array('x' => 316, 'y' => 214, 'width' => 120, 'height' => 15, 'font_size' => 10, 'font_size' => 14, 'align' => 'L'), $recKihon['bumon_lnm']);
		$pdfObj->write_string(array('x' => 40, 'y' => 241, 'width' => 120, 'height' => 15, 'font_size' => 10, 'font_size' => 14, 'align' => 'C'), $recKihon['tanto_nm']);
		//故人名
		$pdfObj->write_string(array('x' => 100, 'y' => 289, 'width' => 195, 'height' => 15, 'font_size' => 10), $recKihon['k_knm']);
		$pdfObj->write_string(array('x' => 100, 'y' => 305, 'width' => 195, 'height' => 15), $recKihon['k_nm']);
		$pdfObj->write_string(array('x' => 380, 'y' => 299, 'width' => 60, 'height' => 15), $recKihon['k_sex_nm']);
		$pdfObj->write_string(array('x' => 520, 'y' => 299, 'width' => 40, 'height' => 15, 'align' => 'C'), $recKihon['k_nenrei_man']);  // 満
		//没日
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 1));
		if (count($rec) > 0) {
			$pdfObj->write_date(array('x' => 380, 'y' => 330, 'width' => 280, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "Y年　n月　j日");  // 日時
		}
		//生年月日
		$pdfObj->write_date(array('x' => 100, 'y' => 330, 'width' => 105, 'height' => 10, 'type' => 'time', 'align' => 'L'), $recKihon['k_seinengappi_ymd'], "Y 年 n 月 j 日");
		//喪主名
		$pdfObj->write_string(array('x' => 100, 'y' => 351, 'width' => 195, 'height' => 15, 'font_size' => 10), $recKihon['m_knm']);
		$pdfObj->write_string(array('x' => 100, 'y' => 367, 'width' => 195, 'height' => 15), $recKihon['m_nm']);
		$pdfObj->write_string(array('x' => 380, 'y' => 362, 'width' => 195, 'height' => 15), $recKihon['m_zoku_nm']);
		//住所
		if ($recKihon['mg_kbn'] == 1) {
			$pdfObj->write_string(array('x' => 100, 'y' => 393, 'width' => 600, 'height' => 15), $recKihon['kg_yubin_no'] . ' ' . $recKihon['kg_addr1'] . ' ' . $recKihon['kg_addr2']);
			$pdfObj->write_string(array('x' => 100, 'y' => 423, 'width' => 600, 'height' => 15), $recKihon['kg_tel']);
		} else {
			$pdfObj->write_string(array('x' => 100, 'y' => 393, 'width' => 600, 'height' => 15), $recKihon['mg_yubin_no'] . ' ' . $recKihon['mg_addr1'] . ' ' . $recKihon['mg_addr2']);
			$pdfObj->write_string(array('x' => 100, 'y' => 423, 'width' => 600, 'height' => 15), $recKihon['mg_tel']);
		}

		//通夜
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 4));
		if (count($rec) > 0) {
			$pdfObj->write_string(array('x' => 100, 'y' => 453, 'width' => 150, 'height' => 10), $rec[0]['basho_nm']);
			$pdfObj->write_date(array('x' => 260, 'y' => 453, 'width' => 280, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "Y年　n月　j日　G時　i分～");  // 日時
		}
		//告別式
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 7));
		if (count($rec) > 0) {
			$pdfObj->write_string(array('x' => 100, 'y' => 483, 'width' => 150, 'height' => 10), $rec[0]['basho_nm']);
			$pdfObj->write_date(array('x' => 260, 'y' => 483, 'width' => 280, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "Y年　n月　j日　G時　i分～");  // 日時
		}
		//初七日法要
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 8));
		if (count($rec) > 0) {
			$pdfObj->write_string(array('x' => 100, 'y' => 512, 'width' => 150, 'height' => 10), $rec[0]['basho_nm']);
			$pdfObj->write_date(array('x' => 260, 'y' => 512, 'width' => 280, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "Y年　n月　j日　G時　i分～");  // 日時
		}
		//式場
		$pdfObj->write_string(array('x' => 100, 'y' => 542, 'width' => 150, 'height' => 10), $recKihon['bumon_lnm']);

		//火葬場所名
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 6));
		if (count($rec) > 0) {
			$pdfObj->write_string(array('x' => 100, 'y' => 572, 'width' => 150, 'height' => 10), $rec[0]['basho_nm']);
		}
	}

	/**
	 * 葬儀・喪家情報確認書
	 * 
	 * <AUTHOR> Yanagiso
	 * @since      2015/03/18
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0210($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		$rec = DataMapper_SekoKihon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$recKihon = $rec[0];

		//ヘッダー
		$pdfObj->write_string(array('x' => 80, 'y' => 214, 'width' => 112, 'height' => 15, 'font_size' => 10, 'font_size' => 14, 'align' => 'C'), $recKihon['k_nm'] . '様');
		$pdfObj->write_string(array('x' => 316, 'y' => 214, 'width' => 120, 'height' => 15, 'font_size' => 10, 'font_size' => 14, 'align' => 'L'), $recKihon['bumon_lnm']);
		$pdfObj->write_string(array('x' => 40, 'y' => 241, 'width' => 120, 'height' => 15, 'font_size' => 10, 'font_size' => 14, 'align' => 'C'), $recKihon['tanto_nm']);
		//故人名
		$pdfObj->write_string(array('x' => 100, 'y' => 289, 'width' => 195, 'height' => 15, 'font_size' => 10), $recKihon['k_knm']);
		$pdfObj->write_string(array('x' => 100, 'y' => 305, 'width' => 195, 'height' => 15), $recKihon['k_nm']);
		$pdfObj->write_string(array('x' => 380, 'y' => 299, 'width' => 60, 'height' => 15), $recKihon['k_sex_nm']);
		$pdfObj->write_string(array('x' => 520, 'y' => 299, 'width' => 40, 'height' => 15, 'align' => 'C'), $recKihon['k_nenrei_man']);  // 満
		//没日
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 1));
		if (count($rec) > 0) {
			$pdfObj->write_date(array('x' => 380, 'y' => 332, 'width' => 280, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "Y年　n月　j日");  // 日時
		}
		//生年月日
		$pdfObj->write_date(array('x' => 100, 'y' => 332, 'width' => 105, 'height' => 10, 'type' => 'time', 'align' => 'L'), $recKihon['k_seinengappi_ymd'], "Y 年 n 月 j 日");
		//喪主名
		$pdfObj->write_string(array('x' => 100, 'y' => 354, 'width' => 195, 'height' => 15, 'font_size' => 10), $recKihon['m_knm']);
		$pdfObj->write_string(array('x' => 100, 'y' => 370, 'width' => 195, 'height' => 15), $recKihon['m_nm']);
		$pdfObj->write_string(array('x' => 380, 'y' => 365, 'width' => 195, 'height' => 15), $recKihon['m_zoku_nm']);
		//住所
		if ($recKihon['mg_kbn'] == 1) {
			$pdfObj->write_string(array('x' => 100, 'y' => 397, 'width' => 600, 'height' => 15), $recKihon['kg_yubin_no'] . ' ' . $recKihon['kg_addr1'] . ' ' . $recKihon['kg_addr2']);
			$pdfObj->write_string(array('x' => 100, 'y' => 430, 'width' => 600, 'height' => 15), $recKihon['kg_tel']);
		} else {
			$pdfObj->write_string(array('x' => 100, 'y' => 397, 'width' => 600, 'height' => 15), $recKihon['mg_yubin_no'] . ' ' . $recKihon['mg_addr1'] . ' ' . $recKihon['mg_addr2']);
			$pdfObj->write_string(array('x' => 100, 'y' => 430, 'width' => 600, 'height' => 15), $recKihon['mg_tel']);
		}

		//通夜
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 4));
		if (count($rec) > 0) {
			$pdfObj->write_string(array('x' => 100, 'y' => 463, 'width' => 150, 'height' => 10), $rec[0]['basho_nm']);
			$pdfObj->write_date(array('x' => 260, 'y' => 463, 'width' => 280, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "Y年　n月　j日　G時　i分～");  // 日時
		}
		//告別式
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 7));
		if (count($rec) > 0) {
			$pdfObj->write_string(array('x' => 100, 'y' => 494, 'width' => 150, 'height' => 10), $rec[0]['basho_nm']);
			$pdfObj->write_date(array('x' => 260, 'y' => 494, 'width' => 280, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "Y年　n月　j日　G時　i分～");  // 日時
		}
		//式場
		$pdfObj->write_string(array('x' => 100, 'y' => 528, 'width' => 150, 'height' => 10), $recKihon['bumon_lnm']);

		//火葬場所名
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 6));
		if (count($rec) > 0) {
			$pdfObj->write_string(array('x' => 100, 'y' => 560, 'width' => 150, 'height' => 10), $rec[0]['basho_nm']);
		}
	}

	/**
	 * 駐車場警備依頼書
	 * 
	 * <AUTHOR> Yanagiso
	 * @since      2015/03/18
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0211($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		$rec = DataMapper_SekoKihon::find($db, array("seko_no" => $seko_no));
		if (count($rec) == 0) {
			return self::ERR_STATUS_NODATA;
		}
		$recKihon = $rec[0];

		//通夜
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 4));
		if (count($rec) > 0) {
			$pdfObj->write_date(array('x' => 115, 'y' => 331, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "n");  // 月
			$pdfObj->write_date(array('x' => 163, 'y' => 331, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "j");  // 日
			$pdfObj->write_date(array('x' => 217, 'y' => 331, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "G");  // 時
		}
		//告別式
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 7));
		if (count($rec) > 0) {
			$pdfObj->write_date(array('x' => 115, 'y' => 397, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "n");  // 月
			$pdfObj->write_date(array('x' => 163, 'y' => 397, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "j");  // 日
			$pdfObj->write_date(array('x' => 217, 'y' => 397, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "G");  // 時
		}

		//湯灌
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 2));
		if (count($rec) > 0) {
			$pdfObj->write_date(array('x' => 115, 'y' => 462, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "n");  // 月
			$pdfObj->write_date(array('x' => 163, 'y' => 462, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "j");  // 日
			$pdfObj->write_date(array('x' => 217, 'y' => 462, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "G");  // 時
		}

		//住所
		if ($recKihon['mg_kbn'] == 1) {
			$pdfObj->write_string(array('x' => 100, 'y' => 495, 'width' => 600, 'height' => 15), $recKihon['kg_yubin_no'] . ' ' . $recKihon['kg_addr1'] . ' ' . $recKihon['kg_addr2']);
		} else {
			$pdfObj->write_string(array('x' => 100, 'y' => 495, 'width' => 600, 'height' => 15), $recKihon['mg_yubin_no'] . ' ' . $recKihon['mg_addr1'] . ' ' . $recKihon['mg_addr2']);
		}

		//火葬場所名
		$rec = DataMapper_SekoNitei::find($db, array('seko_no' => $seko_no, 'nitei_kbn' => 6));
		if (count($rec) > 0) {
			$pdfObj->write_string(array('x' => 100, 'y' => 593, 'width' => 150, 'height' => 10), $rec[0]['basho_nm']);
			$pdfObj->write_date(array('x' => 463, 'y' => 593, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "G");  // 時
			$pdfObj->write_date(array('x' => 515, 'y' => 593, 'width' => 50, 'height' => 15, 'type' => 'time'), $rec[0]['nitei_ymd'], "i");  // 分
		}
	}

	/**
	 * 業務依頼書
	 * 
	 * <AUTHOR> Yanagiso
	 * @since      2015/03/19
	 * @param object $pdfObj PDFオブジェクト
	 * @param Msi_Sys_Db $db DB
	 * @param string $seko_no		施工番号
	 * @param array $hachu_no_ary	施行発注管理情報 配列
	 * @param string $siire_cd		仕入先コード
	 * @param string $report_cd		帳票コード
	 * @param string $hachu_time	発注日時
	 * @return void
	 */
	private function outData0212($pdfObj, $db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd, $hachu_time) {
		$pdfObj->set_default_font_size(12);
		$pdfObj->set_default_font_family_h('kozgopromedium');
		$pdfObj->addSourcePage(__DIR__ . '/' . self::$sourceFileName[$report_cd]);

		// 確認用のグリッドを出力
//        $pdfObj->test_line_out(600, 1000);
		// 発注共通項目
		$pdfObj->hachu_cmn_out($seko_no, $siire_cd, $report_cd, $hachu_time);
		//フッダー項目を出力
		$pdfObj->hachu_footer_out(470, 790, $seko_no, null);

		// 施行発注管理情報を取得
//        $rec_hachu = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, $report_cd);
		$rec_hachu = $this->getSekoHachuInfo($db, $seko_no, $hachu_no_ary, $siire_cd, null);
		$shohin_nm = $rec_hachu[0]['shohin_nm'];

		$pdfObj->write_string(array('x' => 30, 'y' => 339, 'width' => 150, 'height' => 10), $shohin_nm);
		$pdfObj->write_string(array('x' => 300, 'y' => 339, 'width' => 150, 'height' => 10), $rec_hachu[0]['hachu_suryo']);

		$pdfObj->write_string(array('x' => 30, 'y' => 447, 'width' => 150, 'height' => 10), $shohin_nm);
		/*
		  //振り分け
		  $pdfObj->write_string(array('x' =>  30, 'y' => 556, 'width' => 180, 'height' => 10), '表示１＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊');
		  $pdfObj->write_string(array('x' => 306, 'y' => 556, 'width' => 180, 'height' => 10), '表示２＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊');
		  $pdfObj->write_string(array('x' =>  30, 'y' => 583, 'width' => 180, 'height' => 10), '表示３＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊');
		  $pdfObj->write_string(array('x' => 306, 'y' => 583, 'width' => 180, 'height' => 10), '表示４＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊');
		  $pdfObj->write_string(array('x' =>  30, 'y' => 610, 'width' => 180, 'height' => 10), '表示５＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊');
		  $pdfObj->write_string(array('x' => 306, 'y' => 610, 'width' => 180, 'height' => 10), '表示６＊＊＊＊＊＊＊＊＊＊＊＊＊＊＊');
		  $pdfObj->write_string(array('x' =>  30, 'y' => 637, 'width' => 180, 'height' => 10), '表示７＊＊＊＊＊＊＊＊＊＊＊＊＊＊');
		  $pdfObj->write_string(array('x' => 306, 'y' => 637, 'width' => 180, 'height' => 10), '表示８＊＊＊＊＊＊＊＊＊＊＊＊＊＊');
		 */
	}
    /**
     * 
     * 申込区分を取得
     * 
     * @param DBObject  $db
     * @param Sgring    $sekoNo
     * @return String    $moushi_kbn
     */
    private function getMoushiKbn($db, $sekoNo){
        $moushi_kbn = null;
        $sql = "SELECT moushi_kbn FROM seko_kihon_info WHERE delete_flg = 0 AND seko_no = :seko_no";
        $select_moushi = $db->easySelect($sql, array('seko_no' => $sekoNo));
        if(count($select_moushi) > 0){
            $moushi_kbn = $select_moushi[0]['moushi_kbn'];
        }else{
            $moushi_kbn = 1;
        }
        return $moushi_kbn;
    }
}
