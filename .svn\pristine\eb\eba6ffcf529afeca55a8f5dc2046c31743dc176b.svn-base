<?php
/**
 * Logic_FileTrans_FileTransGenFile
 *
 * ファイル転送生成ファイル(file_trans_gen_file) 登録
 *
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/03/xx
 * @filesource 
 */

/**
 * ファイル転送生成ファイル(file_trans_gen_file) 登録
 * 
 * @category   App
 * @package    models\Logic
 * <AUTHOR> Mihara
 * @since      2025/03/xx
 */
class Logic_FileTrans_FileTransGenFile
{
    /**
     * ファイル転送生成ファイル 登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      array  $aFiles     登録ファイル  array('genFileName'=><(一時)ファイル名>, 'ft_type'=>, 'file_type'=> は必須. 複数ファイル可.
     * @param      array  $opt        オプション
     * @return     integer|array      生成ファイル番号
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    public static function genFile( $db, $aFiles, $opt=array() )
    {
        $myObj = new static( $db, $opt );
        $rtn = $myObj->_genFile( $aFiles );
        return $rtn;
    }

    /**
     * コンストラクタ
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db db
     * @param      array  $opt        対象オプション
     */
    protected function __construct( $db, $opt )
    {
        $this->_db  = $db;
        $this->_opt = $opt;
    }

    /**
     * ファイル転送生成ファイル 登録処理
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      array  $aFiles     登録ファイル  array('genFileName'=><(一時)ファイル名>, 'ft_type'=>, 'file_type'=> は必須. 複数ファイル可.
     * @return     integer|array      生成ファイル番号
     * @throws     Msi_Sys_Exception_InputException|Msi_Sys_Exception_LogicException
     */
    protected function _genFile( $aFiles )
    {
        $db = $this->_db;
        $isScalar = null; // ファイル１つの場合、真

        if ( !is_array($aFiles) ) {
            throw new Msi_Sys_Exception_LogicException("(62fae2dd)ファイル未指定");
        }
        if ( !array_key_exists(0, $aFiles) ) { // aFiles
            $aaFiles = array( $aFiles );
            $isScalar = true;
        } else { // aaFiles
            $isScalar = count($aFiles) === 1;
            $aaFiles = $aFiles;
        }

        $aRegFileIds = array(); // 登録した生成ファイル番号

        foreach ( $aaFiles as $_aFile ) {
            $fileId = $this->_regOne( $db, $_aFile );
            $aRegFileIds[] = $fileId;
        }

        $rtn = $isScalar ? $aRegFileIds[0] : $aRegFileIds;
        return $rtn;
    }

    /**
     * １ファイル登録
     *
     * <AUTHOR> Mihara
     * @since      2025/03/xx
     * @param      Msi_Sys_Db $db
     * @param      array      $aFile
     * @return     integer    生成ファイル番号
     * @throws     Msi_Sys_Exception_DbException
     */
    protected function _regOne( $db, $aFile )
    {
        $genFileName = Msi_Sys_Utils::arrDefinedOrDefault($aFile, 'genFileName', null);
        if ( $genFileName === null ) {
            throw new Msi_Sys_Exception_InputException("genFileName が指定されていません");
        }

        $ft_type     = Msi_Sys_Utils::arrDefinedOrDefault($aFile, 'ft_type', null);
        if ( $ft_type === null ) {
            throw new Msi_Sys_Exception_InputException("ft_type が指定されていません");
        }

        $file_type   = Msi_Sys_Utils::arrDefinedOrDefault($aFile, 'file_type', null);
        if ( $ft_type === null ) {
            throw new Msi_Sys_Exception_InputException("file_type が指定されていません");
        }

        $exe_mode   = Msi_Sys_Utils::arrDefinedOrDefault($aFile, 'exe_mode', 'batch');
        $result     = Msi_Sys_Utils::arrDefinedOrDefault($aFile, 'result', 'ok');
        list($size, $mime, $output_cnt) = Logic_FileTrans_Utils::getFileAttr($genFileName);
        list($gen_ts, $gen_user) = Logic_FileTrans_Utils::getTsAndUser($db);

        $is_header_flg = Msi_Sys_Utils::arrDefinedOrDefault($aFile, 'is_header_flg', 0);

        $dbData = array( 'ft_type'    => $ft_type,
                         'file_type'  => $file_type,
                         'exe_mode'   => $exe_mode,
                         'filename'   => $file_type,
                         'size'       => $size,
                         'mime'       => $mime,
                         'is_header_flg' => $is_header_flg,
                         'result'     => $result,
                         'output_cnt' => $output_cnt,
                         'gen_ts'     => $gen_ts,
                         'gen_user'   => $gen_user );
        $dbData = array_merge( $dbData, Msi_Sys_Utils::remapArrayFlat( $aFile, <<< END_OF_TXT
ext_cond
errcode  err_detail  addinfo
END_OF_TXT
        ) );

        $oid = $db->writeBlob($genFileName);
        $dbData['oid'] = $oid;

        $file_id = $db->getSequenceNextVal( 'file_trans_gen_file_file_id_seq' );
        $dbData['file_id'] = $file_id;

        $dbData = Msi_Sys_Utils::emptyToNullArr($dbData);

        $isNew = DataMapper_Utils::upsertEasy( $db, 'file_trans_gen_file', $dbData );

        return $file_id;
    }

}
