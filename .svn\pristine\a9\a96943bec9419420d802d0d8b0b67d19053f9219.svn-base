/** 
 * @fileoverview 領収書発行
 */
msiGlobalObj.doReadonlyByMyself = true; // 参照専用は独自処理
$(function () {
    "use strict";

    var msg01 = "データが変更されています. \nこのデータを保存せず、ページ移動してよろしいですか？";

    var utils = window.msiBbUtils;
    
    var MeisaiModel = Backbone.Model.extend({
        defaults: function () {
            return {
                ryoshusho_no: null,
                hako_kbn: null,
                hako_kbn_nm:null,
                disp_kbn: null,
                syukin_tanto_cd: null,
                syukin_tanto_nm: null,
                jimu_tanto_cd: null,
                jimu_tanto_nm: null,
                atena: null,
                kin_1: null,
                kin_2: null,
                kin_3: null,
                kin_4: null,
                way_1: null,
                way_2: null,
                way_3: null,
                way_4: null,
                zei_1: null,
                zei_2: null,
                zei_3: null,
                zei_4: null,
                gokei: null,
                tadashikaki_cd: null, // 但し書きコード     2016/03/16 ADD <PERSON>o
                tadashikaki: null,
                biko: null,
                hako_date: null,
                haki_date: null,
                haki_user: null,
                is_edit: false,
                seq_no: meisaiList.nextSeqNo(),
                line_no: -1,
                ryoshu_check : 0,
                reissue_flg: true,
                inshi_prc : null,
            };
        },
        // 金額の集計を返す
        sumZan: function () {
            var sum = 0;
            if(this.get('hako_kbn') != 1){
                sum = +this.get('kin_1') + +this.get('kin_2') + +this.get('kin_3') + +this.get('kin_4');
            }
            return sum;
        },

        // ある税CD の領収額を返す
        getKinAllWithZeiCd: function (zei_cd) {
            var that = this;
            var kin = _.reduce([1, 2, 3, 4], function (accum, cnt) {
                if (that.get('zei_' + cnt) == zei_cd) {
                    accum += +that.get('kin_' + cnt);
                }
                return accum;
            }, 0);
            return kin;
        },

        validation: {
            hako_kbn: {
                required: true,
            },
            disp_kbn: {
                required: true,
            },
            syukin_tanto_cd: {
                required: false,
                pattern: 'number',
            },
            syukin_tanto_nm: {
                required: false,
            },
            jimu_tanto_cd: {
                required: false,
                pattern: 'number',
            },
            jimu_tanto_nm: {
                required: false,
            },
            atena: {
                required: false,
                maxLength: 40,
            },
            kin_1: [
                {
                    required: function (value, attr, computed) {
                        var way_1 = this.get('way_1');
                        if (way_1) {
                            return true;
                        }
                        return false;
                    },
                    msg: '領収方法指定時は金額は必須です',
                },
                {
                    pattern: 'number',
                },
            ],
            kin_2: [
                {
                    required: function (value, attr, computed) {
                        var way_2 = this.get('way_2');
                        if (way_2) {
                            return true;
                        }
                        return false;
                    },
                    msg: '領収方法指定時は金額は必須です',
                },
                {
                    pattern: 'number',
                },
            ],
            kin_3: [
                {
                    required: function (value, attr, computed) {
                        var way_3 = this.get('way_3');
                        if (way_3) {
                            return true;
                        }
                        return false;
                    },
                    msg: '領収方法指定時は金額は必須です',
                },
                {
                    pattern: 'number',
                },
            ],
            kin_4: [
                {
                    required: function (value, attr, computed) {
                        var way_4 = this.get('way_4');
                        if (way_4) {
                            return true;
                        }
                        return false;
                    },
                    msg: '領収方法指定時は金額は必須です',
                },
                {
                    pattern: 'number',
                },
            ],
            way_1: [
                {
                    required: function (value, attr, computed) {
                        var kin_1 = this.get('kin_1');
                        if (kin_1) {
                            return true;
                        }
                        return false;
                    },
                    msg: '金額指定時は領収方法指定は必須です',
                },
            ],
            way_2: [
                {
                    required: function (value, attr, computed) {
                        var kin_2 = this.get('kin_2');
                        if (kin_2) {
                            return true;
                        }
                        return false;
                    },
                    msg: '金額指定時は領収方法指定は必須です',
                },
            ],
            way_3: [
                {
                    required: function (value, attr, computed) {
                        var kin_3 = this.get('kin_3');
                        if (kin_3) {
                            return true;
                        }
                        return false;
                    },
                    msg: '金額指定時は領収方法指定は必須です',
                },
            ],
            way_4: [
                {
                    required: function (value, attr, computed) {
                        var kin_4 = this.get('kin_4');
                        if (kin_4) {
                            return true;
                        }
                        return false;
                    },
                    msg: '金額指定時は領収方法指定は必須です',
                },
            ],
            zei_1: [
                {
                    required: function (value, attr, computed) {
                        if(!$.msiJqlib.isNullEx2(app.model.get('juchu_den_no'))){
                            return false;
                        }
                        if (!this.get('is_edit'))
                            return false;
                        var curZan = app.model.getSeikyuZan() - this.sumZan(); // 全額入金なら０になる
                        var way_1 = this.get('way_1');
                        if (curZan != 0 && way_1) {
                            return true;
                        }
                        return false;
                    },
                    msg: '全額入金以外は対象税率の指定が必要です',
                },
                {
                    fn: function (value, attr, computedState) {
                        if(!$.msiJqlib.isNullEx2(app.model.get('juchu_den_no'))){
                            return false;
                        }
                        var zei_cd = value;
                        if (!zei_cd)
                            return;
                        var msg;
                        var taisho_gaku = app.model.getSeikyuZanWithZeiCd(zei_cd);
                        var kin_all = this.getKinAllWithZeiCd(zei_cd);
                        if (taisho_gaku < kin_all) {
                            msg = '税種別毎の残高を超えています';
                            return msg;
                        }
                        return;
                    },
                },
            ],
            zei_2: [
                {
                    required: function (value, attr, computed) {
                        if (!this.get('is_edit'))
                            return false;
                        var curZan = app.model.getSeikyuZan() - this.sumZan(); // 全額入金なら０になる
                        var way_2 = this.get('way_2');
                        if (curZan != 0 && way_2) {
                            return true;
                        }
                        return false;
                    },
                    msg: '全額入金以外は対象税率の指定が必要です',
                },
                {
                    fn: function (value, attr, computedState) {
                        var zei_cd = value;
                        if (!zei_cd)
                            return;
                        var msg;
                        var taisho_gaku = app.model.getSeikyuZanWithZeiCd(zei_cd);
                        var kin_all = this.getKinAllWithZeiCd(zei_cd);
                        if (taisho_gaku < kin_all) {
                            msg = '税種別毎の残高を超えています';
                            return msg;
                        }
                        return;
                    },
                },
            ],
            zei_3: [
                {
                    required: function (value, attr, computed) {
                        if (!this.get('is_edit'))
                            return false;
                        var curZan = app.model.getSeikyuZan() - this.sumZan(); // 全額入金なら０になる
                        var way_3 = this.get('way_3');
                        if (curZan != 0 && way_3) {
                            return true;
                        }
                        return false;
                    },
                    msg: '全額入金以外は対象税率の指定が必要です',
                },
                {
                    fn: function (value, attr, computedState) {
                        var zei_cd = value;
                        if (!zei_cd)
                            return;
                        var msg;
                        var taisho_gaku = app.model.getSeikyuZanWithZeiCd(zei_cd);
                        var kin_all = this.getKinAllWithZeiCd(zei_cd);
                        if (taisho_gaku < kin_all) {
                            msg = '税種別毎の残高を超えています';
                            return msg;
                        }
                        return;
                    },
                },
            ],
            zei_4: [
                {
                    required: function (value, attr, computed) {
                        if (!this.get('is_edit'))
                            return false;
                        var curZan = app.model.getSeikyuZan() - this.sumZan(); // 全額入金なら０になる
                        var way_4 = this.get('way_4');
                        if (curZan != 0 && way_4) {
                            return true;
                        }
                        return false;
                    },
                    msg: '全額入金以外は対象税率の指定が必要です',
                },
                {
                    fn: function (value, attr, computedState) {
                        var zei_cd = value;
                        if (!zei_cd)
                            return;
                        var msg;
                        var taisho_gaku = app.model.getSeikyuZanWithZeiCd(zei_cd);
                        var kin_all = this.getKinAllWithZeiCd(zei_cd);
                        if (taisho_gaku < kin_all) {
                            msg = '税種別毎の残高を超えています';
                            return msg;
                        }
                        return;
                    },
                },
            ],
            gokei: {
                required: false, // チェックは処理内部で行う
                pattern: 'number',
            },
            tadashikaki_cd: {
                //required: true,
                required: function (value, attr, computed) {
                    // 編集可の場合に必須チェック
                    return computed.is_edit;
                },
            },
            tadashikaki: {
                required: false,
                maxLength: 40,
            },
            biko: {
                required: false,
                maxLength: 40,
            },
            _all: function (val, attr, computed) {
                if(!$.msiJqlib.isNullEx2(app.model.get('juchu_den_no'))){
                    return;
                }
                var sum = this.sumZan();
                var zan = app.model.getSeikyuZan();
                if (sum > zan) {
                    return '金額が請求残高を超えています';
                }
                return;
            },
        },

        labels: {
            hako_kbn: '発行区分',
            disp: '再発行文字印刷',
            syukin_tanto_cd: '集金担当者',
            syukin_tanto_nm: '集金担当者名',
            jimu_tanto_cd: '事務担当者',
            jimu_tanto_nm: '事務担当者名',
            atena: '宛名',
            kin_1: '金額1',
            kin_2: '金額2',
            kin_3: '金額3',
            kin_4: '金額4',
            gokei: '合計',
            tadashikaki_cd: '但し書きコード', // 2016/03/16 ADD Kayo
            tadashikaki: '但し書き',
            biko: '備考',
            hako_date: '発行日時',
            haki_date: '破棄日時',
        },

    }); // MeisaiModel

    var MeisaiCollection = Backbone.Collection.extend({
            model: MeisaiModel,
            nextSeqNo: function () {
                ++MeisaiCollection.seq_cnt;
                return MeisaiCollection.seq_cnt;
            },
            resetLineNo: function () {
                var i, max, m;
                for (i = 0, max = this.length; i < max; i++) {
                    m = this.at(i);
                    m.set('line_no', i + 1);
                }
            },
        },
        {// classProperties
            seq_cnt: 0
        }
    ); // MeisaiCollection

    var meisaiList = new MeisaiCollection;

    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seikyu_nm: null,
                seikyu_tel: null,
                seikyu_addr1: null,
                seikyu_addr2: null,
                seikyu_gaku: null,
                nyukin_gaku: null,
                seikyu_zan: null,
                ryoshu_hako_gaku: null,
                // 入金情報
                nyukin_den_no: null,
                nyukin_prc: null,
                chosei_prc: null,
                nyukin_ymd: null,
                pay_method_nm: null,
                pay_method_cd: null,
            };
        },
        // 請求残高を返す
        getSeikyuZan: function () {
            var zan = _.reduce(this.get('_uchiwakeData'), function (accum, rec) {
                var taisho_zan = +rec['taisho_zan'];
                accum += taisho_zan;
                return accum;
            }, 0);
            return zan;
        },
        // ある税CD の請求残高を返す
        getSeikyuZanWithZeiCd: function (zei_cd) {
            var zanRec = _.find(this.get('_uchiwakeData'), function (rec) {
                return rec.zei_cd == zei_cd;
            });
            var zan = zanRec ? zanRec['taisho_gaku'] : 0;
            return zan;
        },
        validation: {
            denpyo_no: {
                required: false,
            },
        },
        labels: {
        }
    }); // AppModel
    
    var MeisaiView = Backbone.View.extend({
        tagName: "tbody",
        template: _.template($('#item-template').html()),
        events: {
            "click a.destroy": "clear",
            "click a.add": "add",
            "click .syukin_tanto_cd,.syukin_tanto-ref": 'tantoHelper',
        },
        tantoHelper: function () {
            if (utils.isReadOnlyCtxt())
                return; // 参照専用の場合は何もしない
            var bbm = this.model;
            if (!bbm.get('is_edit'))
                return; // 参照要素
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    bbm.set('syukin_tanto_cd', data.code);
                    bbm.set('syukin_tanto_nm', data.name);
                },
                onClear: function () {
                    bbm.set('syukin_tanto_cd', null);
                    bbm.set('syukin_tanto_nm', null);
                },
                hookSetData: function () {
                    var bumon_cd = $('#hall_cd option:selected').val();
                    return {s_bumon: bumon_cd};
                },
            });
        },
        // 但し書きコードを変更
        changeTadashikakiCd: function () {
            //データ取得
            var m = this.model;
            var tadashikaki_cd = $.msiJqlib.getSelect2Val(this.$(".tadashikaki_cd"));
            if (tadashikaki_cd != 999) {
                var tadashikaki = $.msiJqlib.getSelect2Data(this.$(".tadashikaki_cd"));
                if (typeof tadashikaki != "undefined") {
                    if (!$.msiJqlib.isNullEx2(tadashikaki)) {
                        var tadashikaki_nm = tadashikaki['kbn_value_snm'];
                        tadashikaki_nm = tadashikaki['kbn_value_snm'];
                        if(!$.msiJqlib.isNullEx2(tadashikaki_nm)){
                            var seko_info = app.model.get('seko_info');
                            var k_nm = "";
                            if(!$.msiJqlib.isNullEx2(seko_info)){
                                k_nm = seko_info['k_nm'];
                            }
                            tadashikaki_nm = tadashikaki_nm.replace("@K@",k_nm);
                        }
                        m.set('tadashikaki', tadashikaki_nm);
                    }
                }
            } else {
                m.set('tadashikaki', null);
            }
        },
        initialize: function () {
            Backbone.Validation.bind(this);
            this.listenTo(this.model, 'change:kin_1 change:kin_2 change:kin_3 change:kin_4', this.recalc);
            this.listenTo(this.model, 'change:way_1 change:way_2 change:way_3 change:way_4　change:zei_1 change:zei_2 change:zei_3 change:zei_4', this.setInshiPrc);
            this.listenTo(this.model, 'change:tadashikaki_cd', this.changeTadashikakiCd);
            this.listenTo(this.model, 'change:ryoshu_check', this.changeCheck);
            this.listenTo(this.model, 'destroy', this.remove);
        },
        changeCheck: function() {
            var that = this;
            _.each(app.collection.models, function(v, k){
                var model = v;
                if(model.get('idx') !== that.model.get('idx')){
                    if(model.get('ryoshu_check') == '1'){
                        var idx = model.get('idx');
                        $('#ryoshu_check_'+idx).click();
                    }
                }
            });
        },
        resetCheck: function() {
            
        },
        recalcAndRender: function () {
            this.recalc();
            this.render();
        },
        recalc: function () {
            var m = this.model,
                    hako_kbn = m.get('hako_kbn'),
                    is_set = false,
                    gokei = 0;
            _.map('kin_1 kin_2 kin_3 kin_4'.split(/\s+/),
                    function (k) {
                        if (!$.msiJqlib.isNullEx2(m.get(k))) {
                            is_set = true;
                            gokei = gokei + parseInt(m.get(k), 10);
                        }
                    });
            if (!is_set) { //  &&  gokei == 0 ) {
                m.set('gokei', null);
                this.setInshiPrc();
            } else {
                m.set('gokei', +gokei);
                this.setInshiPrc();
            }
        },
        setInshiPrc: function (m) {
            var data_kbn = app.model.get('data_kbn');
            var zei_cd   = app.model.get('zei_cd');
            var inshi_prc = 0;
            var gokei = this.model.get('gokei');
            var way_1 = this.model.get('way_1');
            var way_2 = this.model.get('way_2');
            var way_3 = this.model.get('way_3');
            var way_4 = this.model.get('way_4');
            var kin_1 = this.model.get('kin_1');
            var kin_2 = this.model.get('kin_2');
            var kin_3 = this.model.get('kin_3');
            var kin_4 = this.model.get('kin_4');
            var zei_1 = this.model.get('zei_1');
            var zei_2 = this.model.get('zei_2');
            var zei_3 = this.model.get('zei_3');
            var zei_4 = this.model.get('zei_4');
            var zei_rtu_0 = 0;
            if(data_kbn != 4){
                zei_1 = zei_cd;
                way_2 = "0";
                way_3 = "0";
                way_4 = "0";
            }
            if(!$.msiJqlib.isNullEx2(kin_1) && !$.msiJqlib.isNullEx2(zei_1)) {
                var zei_rtu_0 = _getZeiRitu(zei_1);
                var zei_hasu_0 = _getZeiHasu(zei_1);
                var zei_rtu_1 = zei_rtu_0 + 100; 
                var zei_prc_1 = $.msiJqlib.round((kin_1 * zei_rtu_0) /  zei_rtu_1, zei_hasu_0);
                gokei = gokei - zei_prc_1;
            }
            if(!$.msiJqlib.isNullEx2(kin_2) && !$.msiJqlib.isNullEx2(zei_2)) {
                var zei_rtu_0 = _getZeiRitu(zei_2);
                var zei_hasu_0 = _getZeiHasu(zei_2);
                var zei_rtu_2 = zei_rtu_0 + 100; 
                var zei_prc_2 = $.msiJqlib.round((kin_2 * zei_rtu_0) /  zei_rtu_2, zei_hasu_0);
                gokei = gokei - zei_prc_2;
            }
            if(!$.msiJqlib.isNullEx2(kin_3) && !$.msiJqlib.isNullEx2(zei_3)) {
                var zei_rtu_0 = _getZeiRitu(zei_3);
                var zei_hasu_0 = _getZeiHasu(zei_3);
                var zei_rtu_3 = zei_rtu_0 + 100; 
                var zei_prc_3 = $.msiJqlib.round((kin_3 * zei_rtu_0) /  zei_rtu_3, zei_hasu_0);
                gokei = gokei - zei_prc_3;
            }
            if(!$.msiJqlib.isNullEx2(kin_4) && !$.msiJqlib.isNullEx2(zei_4)) {
                var zei_rtu_0 = _getZeiRitu(zei_4);
                var zei_hasu_0 = _getZeiHasu(zei_4);
                var zei_rtu_4 = zei_rtu_0 + 100; 
                var zei_prc_4 = $.msiJqlib.round((kin_4 * zei_rtu_0) /  zei_rtu_4, zei_hasu_0);
                gokei = gokei - zei_prc_4;
            }
            if(way_1 !== "4" && way_2 !== "4" && way_3 !== "4" && way_4 !== "4" ){
                _.each(app.model.get('inshi_info'), function(m, i) {
                    if(m.kingaku_from <= gokei && m.kingaku_to >= gokei){
                        inshi_prc = m.inshi_zei_kingaku;
                    }
                });                
            }
            this.model.set('inshi_prc', inshi_prc);
        },
        render: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_err_setting_std_2());
            if (this.template === null) {
                this.template = _.template($('#item-template').html());
            }
            this.model.set('idx', this.model.cid);
            this.$el.html(this.template(this.model.toJSON()));
            this.$('.radio_set').buttonset();
            // 参照専用要素
            if (!this.model.get('is_edit') || utils.isReadOnlyCtxt()) {
                this.$el.msiInputReadonly();
                this.$el.find('td').addClass('my-readonly-dy');
                if(this.model.get('hako_kbn') == 0 || this.model.get('hako_kbn') == 1){
                    this.$el.find('.row').msiInputReadonlyUnset();  // 新規作成発行済みだけ選択を活性
                }else if(this.model.get('hako_kbn') == 9){
                    this.$('.radio_set').buttonset("disable");
                    this.$('.radio_set .lbl_ryoshu_check').addClass('change_disabled');                    
                }
                this.$el.find('.dlg_tanto').remove();
                this.$el.find('.syukin_tanto_nm').removeClass('my-txt75').addClass('my-txt90');
                this.$el.find('.kin_1,.kin_2,.kin_3,.kin_4,.gokei').removeClass('my-bold');
                this.$el.find('.hako_kbn').addClass("hako_kbn_disabled");
                if (!this.model.get('kin_2')) {
                    this.$el.find('.div-kin2').hide();
                }
                if (!this.model.get('kin_3')) {
                    this.$el.find('.div-kin3').hide();
                }
                if (!this.model.get('kin_4')) {
                    this.$el.find('.div-kin4').hide();
                }
            }else{
                this.$el.find('.row').msiInputReadonly();  // 選択だけ活性
            }
            // 再発行の場合
            if(this.model.get('hako_kbn') == 1){
                this.$el.find('.t-kingaku-all').addClass('my-readonly-dy');
                this.$el.find('.t-kingaku-all').msiInputReadonly();
                this.$el.find('.t-syukin_tanto').addClass('my-readonly-dy');
                this.$el.find('.t-syukin_tanto').msiInputReadonly();
                this.$el.find('.syukin_tanto_nm').removeClass('my-txt75').addClass('my-txt90');
                this.$el.find('.dlg_tanto').remove();
                this.$el.find('.kin_1,.kin_2,.kin_3,.kin_4,.gokei').removeClass('my-bold');
                this.$el.find('.t-biko').msiInputReadonly();
                this.$el.find('.t-biko').addClass('my-readonly-dy');
                this.model.set('is_edit', false);
            }            
            // 軽減税率対応 keigen
            if (app) {
                var optData = app.model.get('_optData');
                if (optData) {
                    var optWay = optData.opt_way;
                    var optZei = optData.opt_zei;
                    var that = this;
                    _.each([1, 2, 3, 4], function (cnt) {
                        var zei_k = "zei_" + cnt;
                        var way_k = "way_" + cnt;
                        var zei_kc = '.' + zei_k;
                        var way_kc = '.' + way_k;
                        $.msiJqlib.setSelect2Com1(that.$(zei_kc),{data: optZei, dropdownAutoWidth: true, allowClear: true, placeholder: ' '});
                        $.msiJqlib.setSelect2Com1(that.$(way_kc),{data: optWay, dropdownAutoWidth: true, allowClear: true, placeholder: ' '});
                        var zei = that.model.get(zei_k);
                        var way = that.model.get(way_k);
                        if (!$.msiJqlib.isNullEx2(zei)) {
                            that.$(zei_kc).select2('val', zei);
                        }
                        if (!$.msiJqlib.isNullEx2(way)) {
                            that.$(way_kc).select2('val', way);
                        }
                    });
                }
            }
            this.stickit();
            $('.msi-picker', this.$el).each(msiLib2.msiPickerBinder);
            if (utils.isReadOnlyCtxt()) {
                this.$el.find(':input').attr('disabled', 'disabled')
                        .end().find('.my-readonly-hidden').hide();
            }
            return this;
        },
        clear: function () {
            minDataApp = app.model.toJSON(); // not JSON, but Object
            minDataCol = app.collection.toJSON(); // not JSON, but Object
            this.model.destroy();
            min2DataApp = app.model.toJSON(); // not JSON, but Object
            min2DataCol = app.collection.toJSON(); // not JSON, but Object
        },
        add: function (e) {
            // shift を押した場合は上に追加
            var isUpside = e.shiftKey ? true : false;
            // Ctrl を押した場合はコピー
            var isCopy = (e.ctrlKey || e.metaKey) ? true : false;
            var off = this.model.get('line_no');
            var newModel, orgModel;
            if (isCopy) {
                orgModel = meisaiList.get(this.model);
                newModel = orgModel.clone(); // shallow copy
                newModel.set('msi_no', null);
            } else {
                newModel = new MeisaiModel;
            }
            // newModel.set( 'msi_biko2',  ""+jQuery.format.date(new Date(), 'MM/dd hh:mm:ss') ); // XXX
            if (isUpside)
                off = off - 1;
            meisaiList.add(newModel, {at: off, silent: true}); // add event を挙げない
            meisaiList.resetLineNo(); // line_no を再設定
            meisaiList.trigger('add', newModel, meisaiList, {at: off}); // , options) // 改めて add event を挙げる
        },

        bindings: {
            '.line_no': 'line_no',
            '.ryoshusho_no': {
                observe: 'ryoshusho_no',
                events: ['change'],
            },
            '.hako_kbn': 'hako_kbn_nm',
            '.disp_kbn': {
                observe: 'disp_kbn',
                events: ['change'],
                getVal: utils.getValSel2,
                update: utils.updateSel2,
            },
            '.syukin_tanto_cd': {
                observe: 'syukin_tanto_cd',
                events: ['change'],
            },
            '.syukin_tanto_nm': {
                observe: 'syukin_tanto_nm',
                events: ['change'],
            },
            '.jimu_tanto_cd': {
                observe: 'jimu_tanto_cd',
                events: ['change'],
            },
            '.jimu_tanto_nm': {
                observe: 'jimu_tanto_nm',
                events: ['change'],
            },
            '.atena': {
                observe: 'atena',
                events: ['change'],
            },
            '.gokei': {
                observe: 'gokei',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.inshi_prc': {
                observe: 'inshi_prc',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.kin_1': {
                observe: 'kin_1',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.kin_2': {
                observe: 'kin_2',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.kin_3': {
                observe: 'kin_3',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.kin_4': {
                observe: 'kin_4',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '.way_1': {
                observe: 'way_1',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.way_2': {
                observe: 'way_2',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.way_3': {
                observe: 'way_3',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.way_4': {
                observe: 'way_4',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.zei_1': {
                observe: 'zei_1',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.zei_2': {
                observe: 'zei_2',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.zei_3': {
                observe: 'zei_3',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.zei_4': {
                observe: 'zei_4',
                events: ['change'],
                getVal: utils.getValSel2,
                afterUpdate: function ($el, event, options) {
                    $.msiJqlib.setSelect2Val($el, $el.val());
                },
            },
            '.tadashikaki_cd': $.msiJqlib.getSelect2Binding('tadashikaki_cd'),
            '.tadashikaki': {
                observe: 'tadashikaki',
                events: ['change'],
            },
            '.biko': {
                observe: 'biko',
                events: ['change'],
            },
            '.hako_date': {
                observe: 'hako_date',
                events: ['change'],
            },
            '.haki_date': {
                observe: 'haki_date',
                events: ['change'],
            },
            '.haki_user': {
                observe: 'haki_user',
                events: ['change'],
                update: function ($el, val, options) {
                    var haki_date = $el.closest('td').find('input.haki_date');
                    if (haki_date.val()) {
                        haki_date.attr('title', '破棄者: ' + val);
                    }
                    $el.val(val);
                },
            },
            '.ryoshu_check' : $.msiJqlib.getCheckBinding('ryoshu_check'),
        },
    }); // MeisaiView

    var AppView = Backbone.View.extend({
        el: document, // '#my-form-id', // for #hall_cd
        events: {
            "click #add-meisai"    : "createMeisai",
            "click #btn_save"      : "doSave",
            "click #btn_cancel"    : "doCancel",
            "click #btn_reissue"   : "doReissue",
            "click #kin-imp-btn"   : "setKingaku",
        },
        denpyoHelper: function () {
            // this は Backbone.View
            // 参照専用モードでも伝票検索は抑制しない 
            // if ( utils.isReadOnlyCtxt() ) return; // 参照専用の場合は何もしない
            var bbm = this.model,
                    bbv = this;
            this.$el.msiPickHelper({
                action: 'zaiko.ido.denpyo',
                onSelect: function (data) {
                    if (bbv.isChanged()) {
                        if (!confirm(msg01)) {
                            return;
                        }
                    }
                    bbm.set('denpyo_no', data.code);
                    var denpyo_no = data.code;
                    $.ajax({// url: location.href,
                        data: {
                            denpyo_no: denpyo_no,
                            action: '表示',
                        },
                        type: 'POST',
                        success: function (mydata) {
                            if (mydata.status == 'OK') {
                                _resetData(mydata.dataApp, mydata.dataCol);
                                if (mydata.dlKey) { // 領収書PDFファイルダウンロード
                                    var data = $.extend({}, mydata.dlKey, {action: 'PDF', preview: 'off', out_type: 1});
                                    msiLib2.fileDlAjax({
                                        url: $.msiJqlib.baseUrl() + '/saiken/ryoshu/order',
                                        data: data
                                    });
                                }
                            } else {
                                msiLib2.showErr(mydata.msg);
                            }
                        }
                    });
                },
                onClear: function () {
                    if (bbv.isChanged()) {
                        if (!confirm(msg01)) {
                            return;
                        }
                    }
                    bbm.set('denpyo_no', null);
                    $.ajax({// url: location.href,
                        data: {
                            action: '初期化',
                        },
                        type: 'POST',
                        success: function (mydata) {
                            if (mydata.status == 'OK') {
                                _resetData(mydata.dataApp, mydata.dataCol);
                            } else {
                                msiLib2.showErr(mydata.msg);
                            }
                        }
                    });
                },
                hookSetData: function () {
                    var bumon_cd = $('#hall_cd option:selected').val();
                    return {
                        init_search: 0,
                        no_cond: 0,
                    };
                }
            });
        },

        initialize: function () {
            Backbone.Validation.bind(this);
            this.listenTo(this.model, 'change', this.recalcAndRender); // for zei calc and cancel button
            this.listenTo(this.collection, 'add', this.addOne);
            this.listenTo(this.collection, 'change remove add', this.recalc);
            this.listenTo(this.collection, 'change remove add', this.render);
            this.listenTo(this.collection, 'reset', this.resetCol);
            this.recalc();
            this.render();
        },
        disableSave: function () {
            var mydata = msiLib2.getJsonFromHtml($("#my-data-init-id"));
            var total_amount = mydata.dataApp.seikyu_gaku;
            var gokei = 0;
            _.each(mydata.dataCol, function (m) {
                var _mgokei = parseInt(m.gokei);
                gokei += (_mgokei) ? _mgokei : 0;
            });
            if (gokei == total_amount) {
                $("#btn_save").hide();
            }
        },
        resetCol: function (collection, options) {
            var $table = this.$("#dtl-table-id");
            $table.find('tbody').remove();
            var gokei = 0;
            var total_amount = app.model.get("seikyu_gaku");
            _.each(collection.models, function (m) {
                if (m.attributes.hako_kbn !== "9") {
                    var _mgokei = parseInt(m.attributes.gokei);
                    gokei += (_mgokei) ? _mgokei : 0;
                }
                var v = new MeisaiView({model: m});
                $table.append(v.render().el);
            });

            this.recalc();
            this.render();
            this.showInitialAmount(total_amount, gokei);
        },

        createMeisai: function (e) {
            this.collection.add(new MeisaiModel);
        },
        addOne: function (meisai, list, options) {
            var v = new MeisaiView({model: meisai});
            var line_no = meisai.get('line_no'), off;
            if (_.has(options, 'at')) {
                off = options['at'];
            }
            if (off === undefined) {
                this.$("#dtl-table-id").append(v.render().el);
            } else if (off > 0) {
                this.$("#dtl-table-id").find('tbody').eq(off - 1).after(v.render().el);
            } else { // off === 0
                this.$("#dtl-table-id").prepend(v.render().el);
            }
        },
        recalcAndRender: function () {
            this.recalc();
            this.render();
        },
        recalc: function () {
            this.collection.each(function (m, i) {
                m.set('line_no', i + 1);
            });
        }, // recalc
        showInitialAmount: function (total_amount, current_amount) {
            return; // XXX
            var length_gk = $(".t-genkin").length;
            var last_gk = $(".t-genkin")[length_gk - 1];
            var before_last_gk = $(".t-genkin")[length_gk - 2];
            if ($(before_last_gk.children).val()) {
                $(last_gk.children).val((total_amount > current_amount) ? (total_amount - current_amount) : '');
                $(last_gk.children).trigger({type: 'change'}).trigger({type: 'keyup'}).trigger({type: 'blur'});
            } else {
                var length_kgt = $(".t-kogite").length;
                var last_kgt = $(".t-kogite")[length_kgt - 1];
                var before_last_kgt = $(".t-kogite")[length_kgt - 2];
                if ($(before_last_kgt.children).val()) {
                    $(last_kgt.children).val((total_amount > current_amount) ? (total_amount - current_amount) : '');
                    $(last_kgt.children).trigger({type: 'change'}).trigger({type: 'keyup'}).trigger({type: 'blur'});
                } else {
                    var length_fkm = $(".t-genkin").length;
                    var last_fkm = $(".t-genkin")[length_fkm - 1];
                    var before_last_fkm = $(".t-genkin")[length_fkm - 2];
                    $(last_fkm.children).val((total_amount > current_amount) ? (total_amount - current_amount) : '');
                    $(last_fkm.children).trigger({type: 'change'}).trigger({type: 'keyup'}).trigger({type: 'blur'});
                }
            }
        },
        render: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_err_setting_std());
            if (this.collection.length) {
                // this.dtlFooter.show();
            } else {
                this.collection.add(new MeisaiModel); // １行は表示する
            }
            this.renderKeigen(); //軽減税率対応 keigen
            this.stickit();
            // スクロール調整
            this.scrollAdj();
            // select2 調整。XXX 
            //this.select2Adj(); // patch
            // 参照専用モード
            if (utils.isReadOnlyCtxt()) {
                if (this.isSelectedCtxt()) {
                    this.btnEnabled('#btn_print');
                } else {
                    this.btnDisabled('#btn_print');
                }
                return this;
            }
            // 領収証発行ボタン活性制御
            if(!$.msiJqlib.isNullEx2(app)){
                if(!$.msiJqlib.isNullEx2(app.model.get('juchu_den_no'))){
                    if(!$.msiJqlib.isNullEx2(app.model.get('status_kbn')) && app.model.get('status_kbn') >= 4){
                        $('#btn_save').addClass('disable_btn');
                    }
                }
            }
            return this;
        },
        // 軽減税率関連 render  keigen
        renderKeigen: function () {
            if (app) {
                var uchiwakeData = app.model.get('_uchiwakeData');
                var html = [];
                var aHdr = [];
                var aVal = [];
                var text;
                var htmlStr;
                var width = 0;
                html.push('<table id="seikyu-tbl-2" class="seikyu-tbl-2-cls">');
                _.each(uchiwakeData, function (rec) {
                    text = rec['taisho_cap'];
                    htmlStr = '<th class="seikyu-tbl-2-cap-cls">' + _.escape(text) + '</th>';
                    aHdr.push(htmlStr);

                    htmlStr = '<th class="seikyu-tbl-2-cap-cls">' + _.escape(text + '残高') + '</th>';
                    aHdr.push(htmlStr);

                    text = $.msiJqlib.commaAdd(rec['taisho_gaku']);
                    htmlStr = '<td class="seikyu-tbl-2-val-cls text-right">' + _.escape(text) + '</td>';
                    aVal.push(htmlStr);

                    text = $.msiJqlib.commaAdd(rec['taisho_zan']);
                    htmlStr = '<td class="seikyu-tbl-2-val-cls text-right">' + _.escape(text) + '</td>';
                    aVal.push(htmlStr);
                });
                if (aHdr.length) {
                    html.push('<tr>' + aHdr.join('') + '</tr>');
                    html.push('<tr>' + aVal.join('') + '</tr>');
                    width = 15 * aHdr.length;
                }
                html.push('</table>');
                var myHtml = html.join('');
                var $myRoot = this.$('#seikyu-info-2');
                $myRoot.empty();
                //$myRoot.css('width', width + '%');
                $myRoot.append(myHtml);
            }
        },

        // スクロールバー表示調整
        scrollAdj: function () {
            var $list = this.$('.items .list'),
                    $header = this.$('.items .header'),
                    sc_of,
                    sc_w,
                    hh;
            if ($list[0].scrollHeight === $list[0].clientHeight) {
                sc_of = 'auto'; // not 'hidden'. hide for 'auto' in Chrome.
                sc_w = '44.4%';
                $list.css("overflow-y", sc_of);
                $header.css("overflow-y", sc_of);
                $('#denpyo_biko1').css('width', sc_w);
                $('#denpyo_biko2').css('width', sc_w);
                $('#note_txt_x').css('width', sc_w);
            } else {
                sc_of = 'scroll';
                sc_w = '43.3%';
                hh = $header.height();
                $list.css("overflow-y", sc_of);
                $header.css("overflow-y", sc_of);
                $header.height(hh); // for Chrome. XXX
                var h0 = this.$('.items').height();
                $list.height(h0 - hh - 5);
                $('#denpyo_biko1').css('width', sc_w);
                $('#denpyo_biko2').css('width', sc_w);
                $('#note_txt_x').css('width', sc_w);
            }
        },
        btnDisabled: function (elem) {
            // $(elem).hide();
            $(elem).attr("disabled", "disabled");
        },
        btnEnabled: function (elem) {
            // $(elem).show();
            $(elem).removeAttr("disabled");
        },
        isInputOk: function () {
            msiLib2.clearAlert();
            var aMsg = [], line;
            var result = this.model.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            var maxlen = app.collection.length - 1;
            this.collection.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    line = i + 1;
                    if (maxlen > i)
                        return; // 最終行以外は読み飛ばす
                    _.each(resLine, function (v, k) {
                        
                        aMsg.push(v);
                    });
                }
            });
            var lastModel = app.collection.at(app.collection.length - 1),
                    gokei = lastModel.get('gokei'),
                    atena = lastModel.get('atena');
            if ($.msiJqlib.isNullEx2(gokei)) {
                aMsg.push('金額が入力されていません');
            } else if (gokei == 0) {
                aMsg.push('金額がゼロになっています');
            } else if (gokei < 0) {
                aMsg.push('金額がマイナス値になっています');
            }
            if ($.msiJqlib.isNullEx2(atena) || lastModel.get('atena').length <= 0) {
                aMsg.push('宛名が入力されていません');
            }
            // NG
            if (aMsg.length > 0) {
                msiLib2.showErr(aMsg.join(', '));
                return false;
            }
            // OK
            msiLib2.clearAlert();
            return true;
        },
        // 残高を金額欄に設定します。受注伝票の場合
        setKingakuAzukari: function () {
            var ryoshu_hako_gaku = this.model.get('ryoshu_hako_gaku');
            var seikyu_gaku = this.model.get('seikyu_gaku');
            var seikyu_den_no = this.model.get('seikyu_den_no');
            if(!$.msiJqlib.isNullEx2(seikyu_den_no)){
                var ryoshu_prc = seikyu_gaku - ryoshu_hako_gaku;
                var pay_method_cd = this.model.get('pay_method_cd');
                _.each(app.collection.models, function (model , index2) {
                    if(model.get('is_edit')){
                        model.set('way_1', pay_method_cd);
                        model.set('kin_1', ryoshu_prc);
                        return false;
                    }
                });                
            }else{
                _.each(app.collection.models, function (model , index2) {
                    if(model.get('is_edit')){
                        model.set('way_1', '2');
                        model.set('kin_1', 0);
                        return false;
                    }
                });
            }
        },
        // 残高を金額欄に設定します
        setKingaku: function () {
            var data_kbns1 = ['1','2','3']; // 税率未入力モード
            var data_kbn = this.model.get('data_kbn');
            if(data_kbns1.includes(data_kbn)){
                this.setKingakuAzukari();
                return;
            }
            var uhiwakeData = this.model.get('_uchiwakeData');
            var zei_ary = [];
            var cnt = 1;
            if(!$.msiJqlib.isNullEx2(uhiwakeData)){
                var ryoshu_hako_gaku = this.model.get('ryoshu_hako_gaku');
                _.each(uhiwakeData, function (obj , index) {
                    var set_kingaku = 0;
                    if(obj.taisho_zan != 0){
                        set_kingaku = obj.taisho_zan;
                        _.each(app.collection.models, function (model , index2) {
                            if(model.get('is_edit')){
                                if(set_kingaku != 0){
                                    if($.msiJqlib.isNullEx2(model.get('way_'+cnt))){
                                        model.set('way_'+cnt, '2');
                                    }
                                    model.set('zei_'+cnt, obj.zei_cd);
                                    model.set('kin_'+cnt, set_kingaku);
                                    cnt++;
                                }
                            }
                        });
                    }
                });
            }
        },
        // 残高を項目に取り込みます
        _setZan: function (fldName) {
            var zan = app.model.get('seikyu_gaku') - app.model.get('ryoshu_hako_gaku'),
                    curOff = app.collection.length - 1,
                    curModel = app.collection.at(curOff),
                    prevModel;
            if (prevModel.get('hako_kbn') != 9) { // 破棄以外の場合
                zan = zan + Number(prevModel.get('gokei'));
            }
            if (zan > 0) {
                curModel = app.collection.at(app.collection.length - 1);
                curModel.set(fldName, zan);
            }
        },
        setGenkin: function () {
            this._setZan('genkin');
        },
        setKogite: function () {
            this._setZan('kogite');
        },
        setFurikomi: function () {
            this._setZan('furikomi');
        },
        // 領収証発行
        doSave: function (mode) {
            var lastModel = app.collection.at(app.collection.length - 1),
                    hako_kbn = lastModel.get('hako_kbn');
            if (hako_kbn != 9) {
                var nyukin_prc = this.model.get('nyukin_prc');
                // 明細の金額を集計
                var sum_prc = _.reduce(app.collection.models, function (accum, model) {
                        if((model.get('hako_kbn') == 0 || model.get('hako_kbn') == 1) && model.get('reissue_flg')){
                            accum += model.sumZan();
                        }
                        return accum;
                }, 0);
                if($.msiJqlib.isNullEx2(this.model.get('juchu_den_no'))){
                    // 入力された入金金額の判定
                    // 請求金額チェック
                    var seikyu_gaku = this.model.get('seikyu_gaku');
                    if(Number(seikyu_gaku) < Number(sum_prc)){
                        msiLib2.showErr("金額が請求金額を超えています");
                        return;
                    }
                }
                // 入金金額チェック
                //if(Number(nyukin_prc) < Number(sum_prc)){
                //    msiLib2.showErr("金額が入金金額を超えています");
                //    return;
                //}
                if (!this.isInputOk()) {
                    return;
                }
            }
            if (hako_kbn == 9) { // 破棄
                if (!confirm("直前の領収書を破棄します.\nなお、金額は考慮されません.\n直前の領収書を破棄しますか？")) {
                    return;
                }
            } else {
                if (!confirm("領収書を発行します.\n領収書は一度発行すると取消できません.\n領収書を発行しますか？")) {
                    return;
                }
            }
            msiLib2.clearAlert();
            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataColJson = JSON.stringify(this.collection.toJSON());
            $.ajax({// url: location.href,
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                    action: '保存',
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        _resetData(mydata.dataApp, mydata.dataCol);
                        if (mydata.dlKey) { // 領収書PDFファイルダウンロード
                            var data = $.extend({}, mydata.dlKey, {action: 'PDF', preview: 'off', out_type: 1});
                            msiLib2.fileDlAjax({
                                url: location.href, data: data
                            });
                        }
                        msiLib2.showInfo2(mydata.msg);
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
            });
        },
        // 領収証破棄
        doCancel: function () {
            msiLib2.clearAlert();
            var selected = [];
            _.each(app.collection.models, function(v, k){
                var model = v;
                if(!$.msiJqlib.isNullEx2(model.get('ryoshu_check')) && model.get('ryoshu_check') == "1"){
                    selected.push(model);
                }
            });
            if(selected.length === 0){
                msiLib2.showErr('選択されていません。');
                return;
            }else if(selected.length > 1){
                msiLib2.showErr('行が複数選択されています。');
                return;
            }
            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataColJson = JSON.stringify(this.collection.toJSON());
            $.ajax({
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                    action: '破棄',
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        _resetData(mydata.dataApp, mydata.dataCol);
                        msiLib2.showInfo2(mydata.msg);
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
            });
        },
        // 領収証再発行
        doReissue: function() {
            msiLib2.clearAlert();
            var selected = [];
            _.each(app.collection.models, function(v, k){
                var model = v;
                if(!$.msiJqlib.isNullEx2(model.get('ryoshu_check')) && model.get('ryoshu_check') == "1"){
                    selected.push(model);
                }
            });
            if(selected.length === 0){
                msiLib2.showErr('選択されていません。');
                return;
            }else if(selected.length > 1){
                msiLib2.showErr('行が複数選択されています。');
                return;
            }
            var dataAppJson = JSON.stringify(this.model.toJSON());
            var dataColJson = JSON.stringify(this.collection.toJSON());
            $.ajax({
                data: {
                    dataAppJson: dataAppJson,
                    dataColJson: dataColJson,
                    action: '再発行',
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        _resetData(mydata.dataApp, mydata.dataCol);
                        if(!$.msiJqlib.isNullEx2(mydata.hako_flg) && mydata.hako_flg){
                            $('#btn_save').removeClass('disable_btn');
                        }
                        msiLib2.showInfo2(mydata.msg);
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
            });

        },
        // 選択モード(更新モード)か否か
        isSelectedCtxt: function () { // aka. isUpdCtxt
            var denpyo_no = this.model.get('denpyo_no');
            if (denpyo_no) {
                return true;
            }
            return false;
        },
        // 初期状態から変更されているか
        isChanged: function () {
            if (!orgDataApp || !orgDataCol ||
                    ($.msiJqlib.isEqual(orgDataApp, this.model.toJSON()) &&
                            $.msiJqlib.isEqual(orgDataCol, this.collection.toJSON()))) {
                return false;
            }
            return true;
        },
        bindings: {
            '#seikyu_nm': 'seikyu_nm',
            '#seikyu_tel': 'seikyu_tel',
            '#seikyu_addr1': 'seikyu_addr1',
            '#seikyu_addr2': 'seikyu_addr2',
            '#seikyu_gaku': {
                observe: 'seikyu_gaku',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '#nyukin_gaku': {
                observe: 'nyukin_gaku',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '#seikyu_zan': {
                observe: 'seikyu_zan',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '#ryoshu_hako_gaku': {
                observe: 'ryoshu_hako_gaku',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            // 入金情報
            '#nyukin_den_no': 'nyukin_den_no',
            '#nyukin_prc': {
                observe: 'nyukin_prc',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },
            '#chosei_prc': {
                observe: 'chosei_prc',
                events: ['change'],
                onSet: utils.commaOmit,
                onGet: utils.commaAdd,
            },            
            '#nyukin_ymd': 'nyukin_ymd',
            '#pay_method_nm': 'pay_method_nm',
        },
    }); // AppView

    var app;
    var orgDataApp, orgDataCol;
    var _resetData, _getZeiRitu, _getZeiHasu;
    var minDataApp, minDataCol, min2DataApp, min2DataCol;
    var _setInitData;
    var disableSave;
    var _zeiMst = [];
    //var my-data-init-id = msiLib2.getJsonFromHtml($("#my-data-init-id"));   
    
    app = new AppView({model: new AppModel, collection: meisaiList});

    disableSave = function () {
        var mydata = msiLib2.getJsonFromHtml($("#my-data-init-id"));
        var total_amount = mydata.dataApp.seikyu_gaku;
        var gokei = 0;
        _.each(mydata.dataCol, function (m) {
            var _mgokei = parseInt(m.gokei);
            gokei += (_mgokei) ? _mgokei : 0;
        });
        if (gokei == total_amount) {
            $("#btn_save").prop("disabled", true);
        }
    };
    _getZeiRitu = function(zei_cd) {
        var ritu = 0,
            d = _.findWhere( _zeiMst, { id: ""+zei_cd } );
        if ( d ) ritu = parseInt(d.text, 10);
        return ritu;
    };
    _getZeiHasu = function(zei_cd) {
        var hasu = 0,
            d = _.findWhere( _zeiMst, { id: ""+zei_cd } );
        if ( d ) hasu = parseInt(d.zei_hasu_kbn, 0);
        return hasu;
    };
    _resetData = function (myApp, myCol) {
        app.model.set(myApp);
        app.collection.reset(myCol);
        orgDataApp = app.model.toJSON(); // not JSON, but Object
        orgDataCol = app.collection.toJSON(); // not JSON, but Object
        $(document).msiErrClearAll();
        app.model.trigger('change'); // patch
        var data_kbns1 = ['1','2','3']; // 税率未入力モード
        var data_kbns2 = ['4'];         // 税率入力モード（標準）
        if(data_kbns1.includes(myApp.data_kbn)){
            // 税率未入力モード
            $('.way_2').attr("disabled", "disabled");
            $('.way_3').attr("disabled", "disabled");
            $('.way_4').attr("disabled", "disabled");
            $('.zei_1').attr("disabled", "disabled");
            $('.zei_2').attr("disabled", "disabled");
            $('.zei_3').attr("disabled", "disabled");
            $('.zei_4').attr("disabled", "disabled");
            $('.kin_2').attr("disabled", "disabled");
            $('.kin_3').attr("disabled", "disabled");
            $('.kin_4').attr("disabled", "disabled");
            $('#ryoshu-footer').hide();
        }
        if(!$.msiJqlib.isNullEx2(app) && !$.msiJqlib.isNullEx2(app.model)){
            var tadashikaki = app.model.get('tadashikaki');
            $.msiJqlib.setSelect2Com1($(".tadashikaki_cd"), ($.extend({data: $.msiJqlib.objToArray3(tadashikaki)}, $.msiJqlib.setSelect2Default2,{'dropdownAutoWidth':true,placeholder: ''})));
        }
    };

    _setInitData = function () {
        var mydata = msiLib2.getJsonFromHtml($('#my-data-init-id'));
        _zeiMst = mydata.dataApp.zei;
        if (_.has(mydata.dataApp, '_reload_denpyo_no')) { // cf. denpyoHelper()
            // 更新や参照で denpyo_no が指定されている場合
            $.ajax({
                data: {
                    denpyo_no: mydata.dataApp['_reload_denpyo_no'],
                    action: '表示',
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status == 'OK') {
                        _resetData(mydata.dataApp, mydata.dataCol);
                    } else {
                        msiLib2.showErr(mydata.msg);
                    }
                }
            });
            return;
        } else {
            _resetData(mydata.dataApp, mydata.dataCol);
        }

        setTimeout(function () {
            app.model.trigger('change');
        }, 0); // scrollbar patch
    };

    // ページ遷移前の確認
    $(window).on('beforeunload', function () {
        if (app.isChanged()) {
            return "保存されていないデータがあります.";
        }
    });

    // リサイズ処理
    // $(window).on( 'resize', function() { app.render(); } );

    // 参照専用
    if ($('#my-form-id').hasClass('my-ctxt-readonly')) {
        var $form = $('#my-form-id');
        $form.msiInputReadonly()
                .msiCalReadonly()
                .find('.my-readonly-hidden').hide();

        $form.find('.my-readonly-visible').show();

        $('#btn_save, #btn_copy, #btn_delete, #btn_new, #btn_cancel')
                .hide();
        // .attr("disabled", "disabled");
        utils.setReadOnlyCtxt();
    }

    // 付加的な disabled 付与
    $('#order').find('.label[data-ref-rel]').each(function () {
        // console.log( '.label[data-ref-rel]->' + $(this).attr('class') );
        var $this = $(this),
            ref = $this.attr('data-ref-rel'),
            $el = $(ref, $this.parent());
        if ($el.attr('disabled')) { // || $el.attr('readonly') ) {
            $(this).addClass("my-disabled").attr("disabled", "disabled");
        } else {
            $(this).removeClass("my-disabled").removeAttr("disabled");
        }
    });

    $.msiJqlib.initDone(function () {
        _setInitData();
    }); // 処理完了

    $('#order').fadeIn('fast'); // ちらつきのごまかし
    
});
