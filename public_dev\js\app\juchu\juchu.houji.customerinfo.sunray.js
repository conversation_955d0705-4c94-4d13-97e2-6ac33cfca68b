var appcst = appcst || {};
var appgjk = appgjk || {}; // 互助会ライブラリ
var appsk = appsk || {};// 請求先ライブラリ
$(function () {
    "use strict";
    /** 申込区分: 2=>法事 */
    var MOUSHI_KBN_HOUJI = '2';
    /** 有無区分: 0=>なし */
    var UMU_KBN_NO = '0';

    /** 日程区分=>施行日 */
    var NITEI_SEKO = '0';
    /** 日程区分=>墓参 */
    var NITEI_BOSAN = '2';
    /** 日程区分=>法宴 */
    var NITEI_HOEN = '3';
    /** 日程区分=>控室 */
    var NITEI_HIKAE = '4';
    /** 日程: 5=>納骨 */
    var NITEI_NOKOTU = '5';
    /** 場所区分=>ホール */
    var BASHO_KIND_HALL = '2';
    /** 場所区分=>火葬 */
    var BASHO_KIND_KASO = '3';
    /** 場所区分=>他式場 */
    var BASHO_KIND_OTHER = '15';
    /** 会場区分=>自営 */
    var KAIJYO_KBN_JISYA = '1';
    /** 会場区分=>他営 */
    var KAIJYO_KBN_TAEI = '2';
    /** 会場タイプ=>控室 */
    var KAIJYO_TYPE_HIKAE = '2';
    /** 会場タイプ=>エンバー */
    var KAIJYO_TYPE_EMBALM = '9';
    /** 会員区分=>互助会 */
    var KAIIN_KBN_GOJO = '100';
    /** 会員区分=>こすもす */
    var KAIIN_KBN_K = '200';
    /** 会員区分=>アスカラメイト */
    var KAIIN_KBN_A = '400';
    /** 会員区分=>ファーストステップ */
    var KAIIN_KBN_F = '500';
    /** 会員区分=>企業 */
    var KAIIN_KBN_COM = '700';

    var YOTO_COURSE = '1';
    var YOTO_PLAN = '2';
    var YOTO_NO_USE = '4';
    /** 契約種別 */
    var ContractType_GOJO = '1'; // 互助会
    /** ステータス: 3=>施行金額確定済 */
    var STATUS_SEKO_KAKUTEI = '3';


    // 画面クラスとモデルのプロパティのオブジェクト
    appcst.pro = {
        // ヘッダー部
        bumon_cd: '#hall_cd', // 売上部門コード
        moushi_cd: '#apply_type', // 申込区分
        daicho_no_eria: '#code_1', // 台帳番号1
        daicho_no_mm: '#code_2', // 台帳番号2
        daicho_no_seq: '#code_3', // 台帳番号3
        sougi_cd: '#funeral_type', // 葬儀区分
        p_info_cd: '#personal_info', // 個人情報保護区分
        kaiin_cd: '#member', // 会員区分
        v_free13: '#member_sbt', // 契約種別
        mitsu_tanto_cd: '#mitsu_tanto', // 見積担当者コード
        mitsu_tanto_nm: '#mitsu_tanto', // 見積担当者名
        seko_tanto_nm: '#staff_2', // 施行担当者コード
        seko_tanto_tel: '#seko_tanto_tel', // 施行担当者携帯番号
        souke_nm: '#family_name',
        souke_knm: '#family_name_kana',
        // 喪主請求情報タブ
        m_first_nm: '#m_first_nm',
        m_last_nm: '#m_last_nm',
        m_first_knm: '#m_first_knm',
        m_last_knm: '#m_last_knm',
        mg_yubin_no: '#zip_1',
        mg_addr1: '#address_1_1',
        mg_tel: '#tel_1',
        mg_m_tel: '#mobile_tel_1',
        mg_addr2: '#address_1_2',
        m_mail_address: '#infochief-tab #m_mail_address',
        mk_kinmusaki_kbn: '#infochief-tab #employee',
        mk_kinmusaki_nm: '#company',
        mk_tel: '#company_tel',
        mk_yakusyoku_nm: '#position',
        mk_fax: '#company_fax',
        sekyu_first_nm: '#sekyu_first_nm',
        sekyu_last_nm: '#sekyu_last_nm',
        sekyu_first_knm: '#sekyu_first_knm',
        sekyu_last_knm: '#sekyu_last_knm',
        sekyu_moshu_kankei: '#bill_relationship_name',
        sekyu_yubin_no: '#zip_4',
        sekyu_addr1: '#address_4_1',
        sekyu_tel: '#tel_4',
        mobile_tel: '#mobile_tel_2',
        sekyu_addr2: '#address_4_2',
        sekyu_biko1: '#memo',
        fc_tel: '#fc_tel',
        fc_mobile_tel: '#fc_mobile_tel',
        fc_office_tel: '#fc_office_tel',
        fc_office_fax: '#fc_office_fax',
        // 法事項目タブ
        hk_last_nm: '.hk_last_nm',
        hk_first_nm: '.hk_first_nm',
        hk_last_knm: '.hk_last_knm',
        hk_first_knm: '.hk_first_knm',
        hk_death_ymd: '.hk_death_ymd',
        hkg_yubin_no: '.hkg_yubin_no',
        hkg_tel: '.hkg_tel',
        temple_cd2: '#temple_nm2',
        sekohoyo_kbn: '.sekohoyo_kbn',
        kaiso_cnt: '#kaiso_cnt',
        shinzoku_cnt: '#shinzoku_cnt',
        ofuse: '#ofuse',
        okuruma: '#okuruma',
        irai_biko: '#irai_biko',
        // 基本タブ日程
        nitei_ymd: '.nitei_date, .nitei_time',
        nitei_date: '.nitei_date',
        nitei_time: '.nitei_time',
        nitei_ed_ymd: '.nitei_date, .nitei_ed_time',
        nitei_ed_time: '.nitei_ed_time',
        basho_nm: '.basho_nm',
        // 互助会確認タブ
        kain_no: '.i_member_id',
        apply_no: '.i_apply_no',
        course_snm_cd: '.i_cose .select-container',
        kanyu_nm: '.i_member_name',
        yoto_kbn: '.i_usage .select-container',
        keiyaku_gaku: '.i_deposit',
        plan_convert_gaku: '.i_plan_convert',
        harai_gaku: '.i_pay',
        harai_no: '.i_times',
        zankin: '.zankin',
        wari_gaku: '.i_discount',
        cose_chg_gaku: '.i_balance',
        early_use_cost: '.i_early',
        meigi_chg_cost: '.i_mg_chg_cost',
        kanyu_dt_gen: '.i_entry_era .select-container',
        kanyu_dt: '.i_entry',
        zei_kijyn_ymd: '.i_tax',
        kanyu_dantai_ext: '#other_entry_name',
        riyu_memo: '#change_reason',
        plan_use_prc: '#plan_use_prc',
        plan_change_prc: '#plan_change_prc',
        meigi_chg_cost_disp: '.i_mg_chg_cost',
        n_free5: '.i_ekimu2',
        n_free6: '.i_wari1',
        n_free7: '.i_wari2',
        n_free8: '.i_wari3',
    };

    // 赤字クラスの追加削除処理
    appcst.toggleAkajiClass = function (that, targets) {
        _.each(targets, function (val) {
            var target = that.model.get(val);
            if (!$.msiJqlib.isNullEx2(target) && target < 0) {
                that.$(appcst.pro[val]).addClass('com-akaji');
            } else {
                that.$(appcst.pro[val]).removeClass('com-akaji');
            }
        });
    };
    /**
     * validation valid時処理
     * @param {View} view
     * @param {string} attr
     */
    var _valid = function (view, attr) {
        var $el = view.$(appcst.pro[attr]);
        if ($el.length) {
            $el.removeClass('error1');
            $el.attr('title', '');
        }
    };

    /**
     * validation invalid時処理
     * @param {View} view
     * @param {string} attr
     * @param {string} error
     */
    var _invalid = function (view, attr, error) {
        var $el = view.$(appcst.pro[attr]);
        if ($el.length) {
            $el.addClass('error1');
            $el.attr('title', error);
        }
    };
    /**
     * 日付と時刻を結合してタイムスタンプに設定する処理
     * @param {string} niteiDate 日付
     * @param {string} niteiTime 時刻
     * @param {string} pro モデルの属性
     * @param {model} model モデル
     */
    var _setNiteiymd = function (niteiDate, niteiTime, pro, model) {
        var niteiymd = null;
        if (!$.msiJqlib.isNullEx2(niteiDate)) {
            niteiymd = niteiDate;
        }
        if (!$.msiJqlib.isNullEx2(niteiTime)) {
            if (!$.msiJqlib.isNullEx2(niteiymd)) {
                niteiymd += " ";
            }
            niteiymd += niteiTime;
        }
        model.set(pro, niteiymd);
    };
    /**
     * 日付チェック処理
     * @param {string} value 日付
     */
    var _chkYmd = function (value) {
        if (!$.msiJqlib.isNullEx2(value) && !$.msiJqlib.chkDate(value)) {
            return Backbone.Validation.messages.ymd;
        }
    };

    // select2のvalを設定する
    var _setSelect2Val = function ($el, val) {
        $el.select2("val", val);
    };

    // 区分値コード数値設定処理
    var _setKbnCdVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_cd_num);
        }
    };
    // 区分値コード正式名設定処理
    var _setKbnLnmVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_lnm);
        }
    };
    // 区分値コード略称名設定処理
    var _setKbnSnmVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_snm);
        }
    };
    // 区分値コード区分設定処理
    var _setCodeKbn = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.code_kbn);
        }
    };
    // 契約内種別設定処理(契約内種別用)
    var _setKeiyakuText = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.text);
        }
    };
    // 契約顧客番号設定処理(契約内種別用)
    var _setKeiyakuNo = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.CustomerNo);
        }
    };
    // 契約顧客番号設定処理(契約内種別用)
    var _setKeiyakuMstText = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.mst_text);
        }
    };
    // 契約種別設定処理(契約内種別用)
    var _setKeiyakuSbt = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.ContractType);
        }
    };
    // 契約内種別設定処理(契約内種別用)
    var _setKeiyakunaiSbt = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.keiyaku_nai_sbt);
        }
    };
    // 和暦の生年月日チェック処理
    var _validateSeinengappi = function (gengo, value) {
        if ($.msiJqlib.isNullEx2(value) || $.msiJqlib.isNullEx2(gengo)) {
            return '生年月日は必須項目です';
        }
        var seinengappi = $.msiJqlib.warekiToseireki(gengo, value);
        if (!seinengappi) {
            return '生年月日の形式エラーです';
        }
    };
    // 場所区分によるpickup入力フィールドの活性・非活性
    var _setSpotStatus = function (niteiKbn, val, $elPickNm, $elPickDlg, disable) {
        if (!$.msiJqlib.isNullEx2(niteiKbn) && niteiKbn == NITEI_SEKO) {
            $elPickDlg.addClass("disabled");
        } else if (!$.msiJqlib.isNullEx2(niteiKbn) && niteiKbn == NITEI_NOKOTU) {
            $elPickDlg.addClass("disabled");
        } else {
            $elPickNm.attr("readonly", "readonly");
            // 未設定または自宅
            if ($.msiJqlib.isNullEx2(val) || val === "0") { // 0:自宅
                $elPickNm.attr("disabled", "disabled");
                $elPickDlg.addClass("disabled");
            } else if (val === "9" || disable) {
                $elPickNm.removeAttr("readonly");
                $elPickNm.removeAttr("disabled");
                $elPickDlg.addClass("disabled");
            } else {
                $elPickNm.removeAttr("disabled");
                $elPickDlg.removeClass("disabled");
            }
        }
    };
    var changeToNum = function (val) {
        var num = parseInt(val, 10);
        if (isFinite(num)) {
            return num;
        } else {
            num = 0;
            return num;
        }
    };

    /**
     * @description 画面全体処理
     */
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                // ヘッダー部
                bumon_cd: null, // 売上部門コード
                status_kbn: '1', // ステータス
                login_bumon_cd: null, // ログイン者の所属部門コード
                shikijo_bumon_cd: null, // 当社以外で設定するときの部門コード
                seko_no: null, // 施行番号
                gojokai_kbn: null, // 互助会区分
                moushi_code_kbn: codeKbns.moushi_code_kbn, // 申込区分コード区分
                moushi_cd: "2", // 申込コード
                moushi_kbn: "2", // 申込区分
                sougi_code_kbn: codeKbns.sougi_code_kbn, // 葬儀区分コード区分
                sougi_cd: "1", // 葬儀コード
                sougi_kbn: "1", // 葬儀区分
                daicho_no_eria: null, // 台帳番号1
                daicho_no_mm: null, // 台帳番号2
                daicho_no_seq: null, // 台帳番号3
                p_info_code_kbn: codeKbns.p_info_code_kbn, // 個人情報保護区分コード区分
                p_info_cd: null, // 個人情報保護コード
                p_info_kbn: null, // 個人情報保護区分
                kaiin_code_kbn: codeKbns.kaiin_code_kbn, // 会員区分コード区分
                kaiin_cd: "600", // 会員コード
                kaiin_kbn: "600", // 会員区分
                v_free13: null, // 利用契約番号
                v_free14: null, // 契約内種別
                v_free15: null, // 利用顧客番号
                v_free16: null, // 利用契約内種別コード
                v_free17: null, // 利用契約内種別名
                n_free7: null, // 契約種別
                mitsu_tanto_cd: null, // 見積担当者コード
                mitsu_tanto_nm: null, // 見積担当者名
                uketuke_tanto_cd: null, // 受付担当者コード
                uketuke_tanto_nm: null, // 受付担当者名
                seko_tanto_cd: null, // 施行担当者コード
                seko_tanto_nm: null, // 施行担当者名
                seko_tanto_tel: null, // 施行担当者携帯番号
                est_shikijo_cd: null, // 見積式場
                est_oya_bumon_cd: null,  // 見積式場の親部門コード
                seko_shikijo_cd: null, // 施行式場
                kaiin_sbt_code_cd: '9600', // 会員種別コード free8_code_cd SEKO_KIHON_ALL_FREE
                kaiin_sbt_cd: null, // 会員種別 free8_kbn SEKO_KIHON_ALL_FREE
                sougi_ymd: null,
                // 喪主請求情報タブ
                free2_cd: null, // 葬儀・法事施行番号
                seko_check_kbn: null,
                m_nm: null, // 喪主名
                m_last_nm: null, // 喪主苗字
                m_first_nm: null, // 喪主名前
                m_knm: null, // 喪主名カナ
                m_last_knm: null, // 喪主苗字カナ
                m_first_knm: null, // 喪主名前カナ
                m_file_nm: null, // 喪主添付ファイル
                m_file: null, // 喪主添付ファイルOID一時
                m_zoku_cd: null, // 喪主続柄コード
                m_zoku_kbn: null, // 喪主続柄区分
                m_zoku_nm: null, // 喪主続柄名
                m_zoku_code_kbn: codeKbns.zoku_code_kbn, // 喪主続柄区分コード区分
                m_zoku_cd2: null, // 喪主続柄コード
                m_zoku_kbn2: null, // 喪主続柄区分
                m_zoku_nm2: null, // 喪主続柄区分
                m_zoku_code_kbn2: codeKbns.zoku_code_kbn, // 喪主続柄区分コード区分
                m_birth_year: null, // 生年月日(年)
                m_wa_year: null, // 生年月日(元号年)
                m_gengo: null, // 喪主元号
                m_birth_month: null, // 生年月日(月)
                m_birth_day: null, // 生年月日(日)
                m_seinengappi_ymd: null, // 喪主生年月日
                m_seinengappi_ymd_y: null, // 喪主生年月日(西暦)
                m_nenrei_man: null, // 喪主年齢
                m_cif_no: null, // 喪主CIFNo
                m_cif_status: "0", // 喪主CIFNoステータス
                mg_yubin_no: null, // 喪主現住所郵便番号
                mg_addr1: null, // 喪主現住所1
                m_mail_address: null, // 喪主mailアドレス
                mg_tel: null, // 喪主現住所TEL
                mg_m_tel: null, // 喪主携帯
                mg_addr2: null, // 喪主現住所2
                mk_kinmusaki_kbn: null, // 喪主勤務先
                mk_kinmusaki_nm: null, // 喪主勤務先名
                mk_tel: null, // 喪主勤務先TEL
                mk_yakusyoku_nm: null, // 喪主役職／職種
                mk_fax: null, // 喪主勤務先FAX
                m_sex_code_kbn: codeKbns.sex_code_kbn, // 性別コード区分
                m_sex_kbn: "1", // 性別区分
                s_cif_no: null, // 請求先CIFNo
                s_cif_status: "0", // 請求先CIFNoステータス
                uchiawase_tanto_nm: null, // 打合せ担当者(人員配置情報)
                after_tanto_nm: null, // アフター担当者
                after_tanto_cd: null, // アフター担当者
                jyusho_cd: null, // 寺院コード
                jyusho_nm: null, // 寺院名
                jyusho_knm: null, // 寺院カナ名
                temple_tel: null, // 寺院tel
                temple_fax: null, // 寺院fax
                temple_yubin_no: null, // 寺院郵便番号
                temple_addr1: null, // 寺院住所1
                temple_addr2: null, // 寺院住所2
                temple_cd2: null, // 紹介寺院CD   free11_code_cd SEKO_KIHON_ALL_FREE
                temple_nm2: null, // 紹介寺院名   v_free24       SEKO_KIHON_ALL_FREE
                temple2_yubin_no: null, // 紹介寺院郵便番号 表示のみ
                temple2_addr1: null, // 紹介寺院住所1   表示のみ
                temple2_addr2: null, // 紹介寺院住所2   表示のみ
                temple2_tel: null, // 紹介寺院tel   表示のみ
                kaiso_cnt: null, //予想参列者者
                shinzoku_cnt: null, //遺族・親族人数
                ofuse       : null, // お布施 n_free1   SEKO_KIHON_ALL_FREE
                okuruma     : null, // お車代 n_free2   SEKO_KIHON_ALL_FREE
                irai_biko   : null, // 依頼書備考 biko1 SEKO_KIHON_ALL_FREE
            };
        },
        validation: {
            m_last_knm: {
                required: false,
                maxLength: 20
            },
            m_first_knm: {
                required: false,
                maxLength: 20
            },
            seko_tanto_tel: {
                required: false,
                pattern: 'tel'
            },
            m_seinengappi_ymd: "validateSeinengappiM",
            mg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            m_mail_address: {
                required: false,
                pattern: 'email'
            },
//            mg_tel: {
//                required: false,
//                pattern: 'tel'
//            },
            bumon_cd: {
                required: true,
            },
            v_free13: {
                required: function () {
                    // 会員区分がアスカラメイト・ファーストステップ・こすもすの場合は必須
                    if (appcst.appModel.get("kaiin_kbn") === KAIIN_KBN_A
                            || appcst.appModel.get("kaiin_kbn") === KAIIN_KBN_F
                            || appcst.appModel.get("kaiin_kbn") === KAIIN_KBN_K) {
                        return true;
                    } else {
                        return false;
                    }
                },
            },
            kaiso_cnt: {
                required: false,
                pattern: 'number'
            },
            shinzoku_cnt: {
                required: false,
                pattern: 'number'
            },
            ofuse: {
                required: false,
                pattern: 'number',
                maxLength: 12
            },
            okuruma: {
                required: false,
                pattern: 'number',
                maxLength: 12
            },
            irai_biko: {
                required: false,
                maxLength: 256
            },
            seko_check_kbn: function (val, attr, computed) {
                if ($.msiJqlib.isNullEx2(computed.seko_check_kbn) && $.msiJqlib.isNullEx2(computed.free2_cd)) {
                    return '関連施行が設定されていません。';
                }
            },
//            souke_nm: {
//                // 申込区分が法事の時は必須
//                required: function () {
//                    if (appcst.appModel.get("moushi_kbn") == MOUSHI_KBN_HOUJI) {
//                        return true;
//                    } else {
//                        return false;
//                    }
//                },
//            },
            souke_knm: {
                required: false,
                maxLength: 50
            },
            temple_yubin_no: {
                required: false,
                pattern: 'zip'
            },
        },
        labels: {
            seko_tanto_tel: '施行担当者携帯番号',
            m_last_knm: '施主姓カナ',
            m_first_knm: '施主名前カナ',
            m_gengo: '施主生年月日元号',
            mg_yubin_no: '施主現住所郵便番号',
            m_mail_address: '施主Mailアドレス',
            mg_tel: '施主現住所TEL',
            mg_m_tel: '施主携帯番号',
            mj_yubin_no: '施主住民登録住所郵便番号',
            mk_tel: '施主勤務先TEL',
            mk_fax: '施主勤務先FAX',
            bumon_cd: '部門コード',
            v_free13: '契約種別',
            kaiso_cnt: '予想参列者数',
            shinzoku_cnt: '遺族・親族人数',
            ofuse       : 'お布施',
            okuruma     : 'お車代',
            irai_biko   : '依頼書備考',
            souke_nm: '喪家',
            souke_knm: '喪家名カナ',
            temple_yubin_no: '菩提寺郵便番号',
        },
        validateSeinengappi: function (value, attr, computedState) {
            var gengo = computedState.k_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return _validateSeinengappi(gengo, value);
            }
        },
        validateSeinengappiM: function (value, attr, computedState) {
            var gengo = computedState.m_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return _validateSeinengappi(gengo, value);
            }
        }
    }); // AppModel

    var AppViewDef = {
        el: $("#wrapper"),
        events: {
            "click .tab li a": "changeTab",
            "click #btn_kokyaku": "kokyakuHelper",
            "click #btn_gojokai_search": "gojokaiHelper",
            "click #mitsu_tanto, .label.dlg_mitsu_tanto": "mitsuTanHelper",
            "click #uketuke_tanto, .label.dlg_uketuke_tanto": "uketukeHelper",
            "click #staff_2, .label.dlg_staff2": "sekoHelper",
            "click #btn_shikijo_yoyaku": "showShikijoYoyaku",
            "change #input-tab #birthday_era,#input-tab #birthday_month,#input-tab #birthday_day": "calcNereiK",
            "select2-open #input-tab #birthday_era": function () {
                var era = this.model.get('k_birth_year');
                if (!$.msiJqlib.isNullEx2(era)) {
                    return;
                }
                var choices = $('.select2-results').children('li');
                var index = choices.length - 70;
                var choice = $(choices[index]);
                choices.filter(".select2-highlighted").removeClass("select2-highlighted");
                choice = $(choices[index]);
                choice.addClass("select2-highlighted");
                // 自動スクロールさせる
                $('.select2-results').scrollTop(0 + (index * 25));

            },
            "change #infochief-tab #birthday_era,#infochief-tab #birthday_month,#infochief-tab #birthday_day": "calcNereiM",
            "select2-open #infochief-tab #birthday_era": function () {
                var era = this.model.get('m_birth_year');
                if (!$.msiJqlib.isNullEx2(era)) {
                    return;
                }
                var choices = $('.select2-results').children('li');
                var index = choices.length - 70;
                var choice = $(choices[index]);
                choices.filter(".select2-highlighted").removeClass("select2-highlighted");
                choice = $(choices[index]);
                choice.addClass("select2-highlighted");
                // 自動スクロールさせる
                $('.select2-results').scrollTop(0 + (index * 25));

            },
            'change #kaiin_sbt_cd': function(e) {
                    this.$("#hd_seko_kaiin_sonota").text(e.added.text);
            },
            "click #m_male": function () {
                // 基本タブ 性別（男）設定
                this.model.set("m_sex_kbn", "1");
            },
            "click #m_female": function () {
                // 基本タブ 性別（女）設定
                this.model.set("m_sex_kbn", "2");
            },
            "click #detail .label.dlg_zip": "zipHelper",
            "click #btn_save": "doSave",
            "click #btn_print": "doPrint",
            "click #btn_delete": "doDelete",
            "click #btn_cancel": "doCancel",
            "click #btn_irai" : "doIrai",
            "click #btn_km_copy": "fromKtoMCopy",
            "click #btn_ms_copy": "fromMtoSCopy",
            "click #btn_ss_copy": "fromStoSCopy",
            "click #btn_sr_copy": "fromStoRCopy",
            "click .dlg_s_seko_no": "sekoInfoHelper",
            "click #seko_clear": function () {
                appcst.appModel.set("free2_cd", null);
                $('#seko_check_kbn').removeAttr('disabled');
            },
            "click #btn_copy": "copySekoInfo",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
            "click .label.dlg_temple": "nmjyushoHelper",
            "click .label.dlg_temple2": "nmjyushoHelper2",
            "change #syushi_cd": "setShuhaOther",
            "select2-selecting #syushi_cd": "clearSyuha",
            "select2-clearing #syushi_cd": "clearSyuha",
            "select2-opening #syuha_cd": function () {
                // 宗派コードを開いたときに宗旨の区分で絞り込んで表示する
                var syushiCd = this.model.get("syushi_cd");
                var orgKbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
                var fileredKbns = [];
                _.each(orgKbns, function (item) {
                    if (item.kbn_value_cd_num === syushiCd) {
                        fileredKbns.push(item);
                    }
                });
                appcst.syuha_kbns = fileredKbns;
            },
            "select2-opening #member_sbt": function () {
                // 開いた時に会員区分と契約種別が同一のもののみ展開する
                var kaiinKbn = this.model.get("kaiin_kbn");
                var fileredKbns = [];
                _.each(orgKeiyakuSbt, function (item) {
                    if (item.kaiin_kbn === kaiinKbn) {
                        fileredKbns.push(item);
                    }
                });
                appcst.keiyaku_sbt = fileredKbns;
            },
            "click #after_tanto_nm, .dlg_af_tanto": "afterTantoHelper",

        },
        bindings: {
            '#hall_cd': {
                observe: 'bumon_cd',
                updateView: false
            },
            '#apply_type': {
                observe: 'moushi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'moushi_kbn');
                    _setCodeKbn($el, this.model, 'moushi_code_kbn');
                    return $el.val();
                }
            },
            '#funeral_type': {
                observe: 'sougi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'sougi_kbn');
                    _setCodeKbn($el, this.model, 'sougi_code_kbn');
                    return $el.val();
                }
            },
            '#code_1': {
                observe: 'daicho_no_eria',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo($el.val(), this.model.get("daicho_no_mm"), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_2': {
                observe: 'daicho_no_mm',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), $el.val(), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_3': {
                observe: 'daicho_no_seq',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), this.model.get("daicho_no_mm"), $el.val());
                    return $el.val();
                }
            },
            '#personal_info': {
                observe: 'p_info_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'p_info_kbn');
                    _setCodeKbn($el, this.model, 'p_info_code_kbn');
                    return $el.val();
                }
            },
            '#member': {
                observe: 'kaiin_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'kaiin_kbn');
                    _setCodeKbn($el, this.model, 'kaiin_code_kbn');
                    return $el.val();
                }
            },
            '#member_sbt': {
                observe: 'v_free13',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKeiyakuText($el, this.model, 'v_free14');
                    _setKeiyakuNo($el, this.model, 'v_free15');
                    _setKeiyakunaiSbt($el, this.model, 'v_free16');
                    _setKeiyakuMstText($el, this.model, 'v_free17');
                    _setKeiyakuSbt($el, this.model, 'n_free7');
                    return $el.val();
                }
            },
            '#mitsu_tanto': 'mitsu_tanto_nm',
            '#uketuke_tanto': {
                observe: 'uketuke_tanto_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('uketuke_tanto_cd', null);
                    }
                    return val;
                }
            },
            '#staff_2': {
                observe: 'seko_tanto_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('seko_tanto_cd', null);
                    }
                    return val;
                },
                // select2摘要時に修正が必要 葬儀情報(ヘッダー)の施行担当者バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_seko_tanto").text($el.val());
                    return $el.val();
                }
            },
            '#seko_tanto_tel': 'seko_tanto_tel',
            '#est_shikijo_cd': {
                observe: 'est_shikijo_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#seko_shikijo_cd': {
                observe: 'seko_shikijo_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#kaiin_sbt_cd': $.msiJqlib.getSelect2Binding('kaiin_sbt_cd'),
            '#infochief-tab #s_seko_no': 'free2_cd',
            '#seko_check_kbn': $.msiJqlib.getCheckBinding('seko_check_kbn'),
            'm_nm': {
                observe: 'm_nm',
                // 葬儀情報(ヘッダー)の喪主名バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_m_nm").text($el.val());
                    return $el.val();
                }
            },
            '#m_last_nm': 'm_last_nm',
            '#m_first_nm': 'm_first_nm',
            '#s_chief_relationship': {
                observe: 'm_zoku_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'm_zoku_kbn');
                    _setKbnLnmVal($el, this.model, 'm_zoku_nm');
                    return $el.val();
                }
            },
            '#s_chief_relationship2': {
                observe: 'm_zoku_cd2',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'm_zoku_kbn2');
                    _setKbnLnmVal($el, this.model, 'm_zoku_nm2');
                    return $el.val();
                }
            },
            '#m_last_knm': 'm_last_knm',
            '#m_first_knm': 'm_first_knm',
            '#birthday_era': {
                observe: 'm_birth_year',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnSnmVal($el, this.model, 'm_wa_year');
                    return $el.val();
                }
            },
            '#birthday_month': {
                observe: 'm_birth_month',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#birthday_day': {
                observe: 'm_birth_day',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#age': {
                observe: 'm_nenrei_man',
                onGet: function (val, options) {
                    if (!$.msiJqlib.isNullEx2(val)) {
                        return '満' + val + '歳';
                    }
                }
            },
            '#m_cif_no': 'm_cif_no',
            '#m_cif_status': {
                observe: 'm_cif_status',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #zip_1': 'mg_yubin_no',
            '#infochief-tab #chief #address_1_1': {
                observe: 'mg_addr1',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                    return $el.val();
                },
                afterUpdate: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                }
            },
            '#infochief-tab #chief #address_1_2': {
                observe: 'mg_addr2',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr1');
                    this.setHeadeMaddr(address, $el.val());
                    return $el.val();
                }
            },
            '#infochief-tab #m_mail_address': 'm_mail_address',
            '#infochief-tab #chief #tel_1': 'mg_tel',
            '#infochief-tab #chief #mobile_tel_1': 'mg_m_tel',
            '#infochief-tab #chief #employee': {
                observe: 'mk_kinmusaki_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #company': 'mk_kinmusaki_nm',
            '#infochief-tab #chief #company_tel': 'mk_tel',
            '#infochief-tab #chief #position': 'mk_yakusyoku_nm',
            '#infochief-tab #chief #company_fax': 'mk_fax',
            '#s_cif_no': 's_cif_no',
            '#s_cif_status': {
                observe: 's_cif_status',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#family_name': 'souke_nm',
            '#family_name_kana': 'souke_knm',
            '#funeral_style': {
                observe: 'keishiki_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'keishiki_kbn');
                    _setCodeKbn($el, this.model, 'keishiki_code_kbn');
                    return $el.val();
                }
            },
            '#memo2': 'biko1',
            '#uchiawase_tanto_nm': 'uchiawase_tanto_nm',
            '#after_tanto_nm': 'after_tanto_nm',
            '#syushi_cd': {
                observe: 'syushi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syushi_kbn');
                    _setCodeKbn($el, this.model, 'syushi_code_kbn');
                    return $el.val();
                }
            },
            '#syuha_cd': {
                observe: 'syuha_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syuha_kbn');
                    _setCodeKbn($el, this.model, 'syuha_code_kbn');
                    // 宗派名設定処理
                    var item = $el.select2("data");
                    if ($.msiJqlib.isNullEx2(item)) {
                        this.model.set("syuha_nm", null);
                        this.model.set("syuha_knm", null);
                    } else {
                        this.model.set("syuha_nm", item.text);
                        this.model.set("syuha_knm", item.kbn_value_snm);
                    }
                    return $el.val();
                }
            },
            '#syuha_nm_other': 'syuha_nm',
            '#syuha_knm': 'syuha_knm',
            '#temple_cd': 'jyusho_cd',
            '#temple': {
                observe: 'jyusho_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("jyusho_cd", null);
                        this.model.set("jyusho_knm", null);
                    }
                    return val;
                }
            },
            '#temple_knm': 'jyusho_knm',
            '#temple_tel': 'temple_tel',
            '#temple_fax': 'temple_fax',
            '#temple_yubin_no': 'temple_yubin_no',
            '#temple_addr1': 'temple_addr1',
            '#temple_addr2': 'temple_addr2',
            '#temple_cd2': 'temple_cd2',
            '#temple_nm2': 'temple_nm2',
            '#temple2_yubin_no': 'temple2_yubin_no',
            '#temple2_addr1': 'temple2_addr1',
            '#temple2_addr2': 'temple2_addr2',
            '#temple2_tel': 'temple2_tel',
            '#kaiso_cnt': {
                observe: 'kaiso_cnt',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#shinzoku_cnt': {
                observe: 'shinzoku_cnt',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#ofuse': {
                observe: 'ofuse',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#okuruma': {
                observe: 'okuruma',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#irai_biko': 'irai_biko',
        },
        // アフター担当者ヘルパー処理 
        afterTantoHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'after_tanto_cd': data.code, 'after_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'after_tanto_cd': null, 'after_tanto_nm': null});
                }
            });
        },
        // 名所住所ヘルパー処理
        nmjyushoHelper: function (e) {
            var t = this;
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var kind = $target.data("kind2");
            var code = $target.data("code");
            var name = $target.data("name");
            var kname = $target.data("kname");
            var tel = $target.data("tel");
            var fax = $target.data("fax");
            var zip_no = $target.data("zip_no");
            var addr1 = $target.data("addr1");
            var addr2 = $target.data("addr2");
            var m = this.model;
            var syuha_cd = null;
            if (kind === 1) {
                syuha_cd = this.model.get("syuha_cd");
            }
            this.$el.msiPickHelper({
                action: 'nmjyusho',
                kind2: kind,
                mydata: {is_easyreg_jiin:0},
                onSelect: function (data) {
                    m.set(code, data.code);
                    m.set(name, data.name);
                    if (!$.msiJqlib.isNullEx2(kname)) {
                        m.set(kname, data.jyusho_lknm);
                        m.set(tel, data.tel);
                        m.set(fax, data.fax);
                        m.set(zip_no, data.zip_no);
                        m.set(addr1, data.addr1_nm);
                        m.set(addr2, data.addr2_nm);
                        if (kind === 1) {
                            if (!$.msiJqlib.isNullEx2(data.syuha_cd) && m.get("syuha_cd") !== data.syuha_cd) {
                                m.set('syushi_cd', data.syushi_cd);
                                m.set('syuha_cd', data.syuha_cd);
                                m.set('syuha_kbn', data.syushi_kbn);
                                m.set('syuha_nm', data.syuha_nm);
                                m.set('syuha_knm', data.syuha_kana);
                                t.setShuhaOther();
                            }
                        }
                    }
                },
                onClear: function () {
                    m.set(code, null);
                    m.set(name, null);
                    m.set(kname, null);
                    m.set(tel, null);
                    m.set(fax, null);
                    m.set(zip_no, null);
                    m.set(addr1, null);
                    m.set(addr2, null);
                }
            });
        },
        nmjyushoHelper2: function (e) {
            var m = this.model;
            var syuha_cd = this.model.get("syuha_cd");
            this.$el.msiPickHelper({
                action: 'nmjyusho',
                kind2: 1,
                mydata: {is_easyreg_jiin:0},
                onSelect: function (data) {
                    m.set('temple_cd2', data.code);
                    m.set('temple_nm2', data.name);
                    m.set('temple2_yubin_no', data.zip_no);
                    m.set('temple2_addr1', data.addr1_nm);
                    m.set('temple2_addr2', data.addr2_nm);
                    m.set('temple2_tel', data.tel);
                },
                onClear: function () {
                    m.set('temple_cd2', null);
                    m.set('temple_nm2', null);
                    m.set('temple2_yubin_no', null);
                    m.set('temple2_addr1', null);
                    m.set('temple2_addr2', null);
                    m.set('temple2_tel', null);
                }
            });
        },
        setShuhaOther: function () {
            if (this.model.get("syushi_cd") === '9') { // その他
                this.model.set("syuha_cd", '');
                this.$("#syuha_nm_other").show();
                this.$("#syuha_knm2").show();
                this.$("#syuha_knm").hide();
            } else {
                this.$("#syuha_nm_other").hide();
                this.$("#syuha_knm2").hide();
                this.$("#syuha_knm").show();
            }
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            // disabledの場合はリターン（報告書タブの式場移動用処理）
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            $target.datepicker("show");
        },
        // 顧客ピッカー
        kokyakuHelper: function(e) {
            // console.log('KOKYAKU 検索');
            var that = this;
            msiGlobalObj.kaikokyakudlg2Open( this, 
                                             ['故人#1', '故人#2', '故人#3', '故人#4', '施主', '請求先'], 
                                             { kokyaku_no_p1: appcst.kojinInfoCol.models[0].get('hk_cif_no'),
                                               kokyaku_no_p2: appcst.kojinInfoCol.models[1].get('hk_cif_no'),
                                               kokyaku_no_p3: appcst.kojinInfoCol.models[2].get('hk_cif_no'),
                                               kokyaku_no_p4: appcst.kojinInfoCol.models[3].get('hk_cif_no'),
                                               kokyaku_no_p5: this.model.get('m_cif_no'),
                                               kokyaku_no_p6: this.model.get('s_cif_no'),
                                               kokyaku_result_p5: this.model.get('m_cif_status'),
                                               kokyaku_result_p6: this.model.get('s_cif_status'),
                                               result_set_3: true, // true:未検索/該当なし/顧客No(3択)  false:該当なし/顧客No(2択)
                                               //
                                               // s_cond_etc01: '0', // 数値型は不可. 文字列型のみ
                                               // s_kokyaku_kbn: 0, // 9617(顧客区分): 0:個人,1:得意先
                                               // s_dantai_cd: '00400110', // 0010000, 00400110:ＪＡ東京みどりＧＬＣ, 00400001: ファミリーライフクラブ　ゴールド
                                               // is_easyreg: 0, // 顧客登録画面リンク表示
                                             },
                                             this._kokyakuHelperOnClose,
                                             this._kokyakuHelperOnSet );
        },

        // ダイアログ close 時ハンドラ ([キャンセル]ボタン押下時には呼ばれない)
        // kokyakudlgData: kokyakudlg のモデルデータ
        _kokyakuHelperOnClose: function( kokyakudlgData ) {
            console.log( '_kokyakuHelperOnClose kokyakudlgData=>', kokyakudlgData );
//            var dumpStr = JSON.stringify(kokyakudlgData, undefined, 2);
//            this.model.set( { dump_kokyaku: dumpStr } );

            if (kokyakudlgData.kokyaku_upd_p1) { // 設定されていれば
                if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_no_p1)) {
                    appcst.kojinInfoCol.models[0].set('hk_cif_no', kokyakudlgData.kokyaku_no_p1);
                    if (kokyakudlgData.kokyaku_data_p1) {
                        appcst.kojinInfoCol.models[0].set('hk_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_hyoji_nm);
                        appcst.kojinInfoCol.models[0].set('hk_last_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_nm1);
                        appcst.kojinInfoCol.models[0].set('hk_first_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_nm2);
                        appcst.kojinInfoCol.models[0].set('hk_knm', kokyakudlgData.kokyaku_data_p1.kokyaku_hyoji_kana);
                        appcst.kojinInfoCol.models[0].set('hk_last_knm', kokyakudlgData.kokyaku_data_p1.kokyaku_kana1);
                        appcst.kojinInfoCol.models[0].set('hk_first_knm', kokyakudlgData.kokyaku_data_p1.kokyaku_kana2);
                        appcst.kojinInfoCol.models[0].set('hk_sex_kbn', kokyakudlgData.kokyaku_data_p1.sex_kbn);
                        if ( kokyakudlgData.kokyaku_data_p1.sex_kbn == 2 ) { 
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[0].cid).attr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[0].cid).prev().addClass("ui-state-active");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[0].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[0].cid).prev().removeClass("ui-state-active");
                        } else if ( kokyakudlgData.kokyaku_data_p1.sex_kbn == 1 ) { 
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[0].cid).attr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[0].cid).prev().addClass("ui-state-active");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[0].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[0].cid).prev().removeClass("ui-state-active");
                        } else {
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[0].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[0].cid).prev().removeClass("ui-state-active");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[0].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[0].cid).prev().removeClass("ui-state-active");
                        }
                        appcst.kojinInfoCol.models[0].set('hkg_yubin_no', kokyakudlgData.kokyaku_data_p1.yubin_no);
                        appcst.kojinInfoCol.models[0].set('hkg_addr1', kokyakudlgData.kokyaku_data_p1.addr1);
                        appcst.kojinInfoCol.models[0].set('hkg_addr2', kokyakudlgData.kokyaku_data_p1.addr2);
                        appcst.kojinInfoCol.models[0].set('hkg_tel', kokyakudlgData.kokyaku_data_p1.tel1);
                        appcst.kojinInfoCol.models[0].set('hk_death_ymd', kokyakudlgData.kokyaku_data_p1.shibo_ymd);

                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p1.seinengappi_ymd)) {
                            var wareki = $.msiJqlib.arrSeirekiToWareki(kokyakudlgData.kokyaku_data_p1.seinengappi_ymd);
                            if ( wareki[0] ) {
                                var gengo = wareki[0];
                                appcst.kojinInfoCol.models[0].set('hk_gengo', gengo);
                                appcst.kojinInfoCol.models[0].set('hk_wa_year', gengo+$.msiJqlib.fillPre(wareki[1]));
                            }
                            var spSe = kokyakudlgData.kokyaku_data_p1.seinengappi_ymd.split('/');
                            appcst.kojinInfoCol.models[0].set('hk_birth_year', spSe[0]);
                            appcst.kojinInfoCol.models[0].set('hk_birth_month', spSe[1]);
                            appcst.kojinInfoCol.models[0].set('hk_birth_day', spSe[2]);
                        }
                    }
                } else {
                    appcst.kojinInfoCol.models[0].set('hk_cif_no', null);
                    
                    appcst.kojinInfoCol.models[0].set('hk_nm', null);
                    appcst.kojinInfoCol.models[0].set('hk_last_nm', null);
                    appcst.kojinInfoCol.models[0].set('hk_first_nm', null);
                    appcst.kojinInfoCol.models[0].set('hk_knm', null);
                    appcst.kojinInfoCol.models[0].set('hk_last_knm', null);
                    appcst.kojinInfoCol.models[0].set('hk_first_knm', null);
                    appcst.kojinInfoCol.models[0].set('hk_sex_kbn', null);
                    $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[0].cid).removeAttr("checked", "checked");
                    $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[0].cid).prev().removeClass("ui-state-active");
                    $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[0].cid).removeAttr("checked", "checked");
                    $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[0].cid).prev().removeClass("ui-state-active");
                    appcst.kojinInfoCol.models[0].set('hkg_yubin_no', null);
                    appcst.kojinInfoCol.models[0].set('hkg_addr1', null);
                    appcst.kojinInfoCol.models[0].set('hkg_addr2', null);
                    appcst.kojinInfoCol.models[0].set('hkg_tel', null);
                    appcst.kojinInfoCol.models[0].set('hk_gengo', null);
                    appcst.kojinInfoCol.models[0].set('hk_wa_year', null);
                    appcst.kojinInfoCol.models[0].set('hk_birth_year', null);
                    appcst.kojinInfoCol.models[0].set('hk_birth_month', null);
                    appcst.kojinInfoCol.models[0].set('hk_birth_day', null);
                    appcst.kojinInfoCol.models[0].set('hk_seinengappi_ymd', null);
                    appcst.kojinInfoCol.models[0].set('hk_seinengappi_ymd_y', null);
                    appcst.kojinInfoCol.models[0].set('hk_nenrei_man', null);
                    appcst.kojinInfoCol.models[0].set('hk_death_ymd', null);
                }
            }
            if (kokyakudlgData.kokyaku_upd_p2) { // 設定されていれば
                if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_no_p2)) {
                    appcst.kojinInfoCol.models[1].set('hk_cif_no', kokyakudlgData.kokyaku_no_p2);
                    if (kokyakudlgData.kokyaku_data_p2) {
                        appcst.kojinInfoCol.models[1].set('hk_nm', kokyakudlgData.kokyaku_data_p2.kokyaku_hyoji_nm);
                        appcst.kojinInfoCol.models[1].set('hk_last_nm', kokyakudlgData.kokyaku_data_p2.kokyaku_nm1);
                        appcst.kojinInfoCol.models[1].set('hk_first_nm', kokyakudlgData.kokyaku_data_p2.kokyaku_nm2);
                        appcst.kojinInfoCol.models[1].set('hk_knm', kokyakudlgData.kokyaku_data_p2.kokyaku_hyoji_kana);
                        appcst.kojinInfoCol.models[1].set('hk_last_knm', kokyakudlgData.kokyaku_data_p2.kokyaku_kana1);
                        appcst.kojinInfoCol.models[1].set('hk_first_knm', kokyakudlgData.kokyaku_data_p2.kokyaku_kana2);
                        appcst.kojinInfoCol.models[1].set('hk_sex_kbn', kokyakudlgData.kokyaku_data_p1.sex_kbn);
                        if ( kokyakudlgData.kokyaku_data_p2.sex_kbn == 2 ) { 
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[1].cid).attr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[1].cid).prev().addClass("ui-state-active");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[1].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[1].cid).prev().removeClass("ui-state-active");
                        } else if ( kokyakudlgData.kokyaku_data_p2.sex_kbn == 1 ) { 
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[1].cid).attr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[1].cid).prev().addClass("ui-state-active");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[1].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[1].cid).prev().removeClass("ui-state-active");
                        } else { 
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[1].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[1].cid).prev().removeClass("ui-state-active");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[1].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[1].cid).prev().removeClass("ui-state-active");
                        }
                        appcst.kojinInfoCol.models[1].set('hkg_yubin_no', kokyakudlgData.kokyaku_data_p2.yubin_no);
                        appcst.kojinInfoCol.models[1].set('hkg_addr1', kokyakudlgData.kokyaku_data_p2.addr1);
                        appcst.kojinInfoCol.models[1].set('hkg_addr2', kokyakudlgData.kokyaku_data_p2.addr2);
                        appcst.kojinInfoCol.models[1].set('hkg_tel', kokyakudlgData.kokyaku_data_p2.tel1);
                        appcst.kojinInfoCol.models[1].set('hk_death_ymd', kokyakudlgData.kokyaku_data_p2.shibo_ymd);

                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p2.seinengappi_ymd)) {
                            var wareki = $.msiJqlib.arrSeirekiToWareki(kokyakudlgData.kokyaku_data_p2.seinengappi_ymd);
                            if ( wareki[0] ) {
                                var gengo = wareki[0];
                                appcst.kojinInfoCol.models[1].set('hk_gengo', gengo);
                                appcst.kojinInfoCol.models[1].set('hk_wa_year', gengo+$.msiJqlib.fillPre(wareki[1]));
                            }
                            var spSe = kokyakudlgData.kokyaku_data_p2.seinengappi_ymd.split('/');
                            appcst.kojinInfoCol.models[1].set('hk_birth_year', spSe[0]);
                            appcst.kojinInfoCol.models[1].set('hk_birth_month', spSe[1]);
                            appcst.kojinInfoCol.models[1].set('hk_birth_day', spSe[2]);
                        }
                    }
                } else {
                    appcst.kojinInfoCol.models[1].set('hk_cif_no', null);
                    
                    appcst.kojinInfoCol.models[1].set('hk_nm', null);
                    appcst.kojinInfoCol.models[1].set('hk_last_nm', null);
                    appcst.kojinInfoCol.models[1].set('hk_first_nm', null);
                    appcst.kojinInfoCol.models[1].set('hk_knm', null);
                    appcst.kojinInfoCol.models[1].set('hk_last_knm', null);
                    appcst.kojinInfoCol.models[1].set('hk_first_knm', null);
                    appcst.kojinInfoCol.models[1].set('hk_sex_kbn', null);
                    $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[1].cid).removeAttr("checked", "checked");
                    $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[1].cid).prev().removeClass("ui-state-active");
                    $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[1].cid).removeAttr("checked", "checked");
                    $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[1].cid).prev().removeClass("ui-state-active");
                    appcst.kojinInfoCol.models[1].set('hkg_yubin_no', null);
                    appcst.kojinInfoCol.models[1].set('hkg_addr1', null);
                    appcst.kojinInfoCol.models[1].set('hkg_addr2', null);
                    appcst.kojinInfoCol.models[1].set('hkg_tel', null);
                    appcst.kojinInfoCol.models[1].set('hk_gengo', null);
                    appcst.kojinInfoCol.models[1].set('hk_wa_year', null);
                    appcst.kojinInfoCol.models[1].set('hk_birth_year', null);
                    appcst.kojinInfoCol.models[1].set('hk_birth_month', null);
                    appcst.kojinInfoCol.models[1].set('hk_birth_day', null);
                    appcst.kojinInfoCol.models[1].set('hk_seinengappi_ymd', null);
                    appcst.kojinInfoCol.models[1].set('hk_seinengappi_ymd_y', null);
                    appcst.kojinInfoCol.models[1].set('hk_nenrei_man', null);
                    appcst.kojinInfoCol.models[1].set('hk_death_ymd', null);
                }
            }
            if (kokyakudlgData.kokyaku_upd_p3) { // 設定されていれば
                if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_no_p3)) {
                    appcst.kojinInfoCol.models[2].set('hk_cif_no', kokyakudlgData.kokyaku_no_p3);
                    if (kokyakudlgData.kokyaku_data_p3) {
                        appcst.kojinInfoCol.models[2].set('hk_nm', kokyakudlgData.kokyaku_data_p3.kokyaku_hyoji_nm);
                        appcst.kojinInfoCol.models[2].set('hk_last_nm', kokyakudlgData.kokyaku_data_p3.kokyaku_nm1);
                        appcst.kojinInfoCol.models[2].set('hk_first_nm', kokyakudlgData.kokyaku_data_p3.kokyaku_nm2);
                        appcst.kojinInfoCol.models[2].set('hk_knm', kokyakudlgData.kokyaku_data_p3.kokyaku_hyoji_kana);
                        appcst.kojinInfoCol.models[2].set('hk_last_knm', kokyakudlgData.kokyaku_data_p3.kokyaku_kana1);
                        appcst.kojinInfoCol.models[2].set('hk_first_knm', kokyakudlgData.kokyaku_data_p3.kokyaku_kana2);
                        appcst.kojinInfoCol.models[2].set('hk_sex_kbn', kokyakudlgData.kokyaku_data_p1.sex_kbn);
                        if ( kokyakudlgData.kokyaku_data_p3.sex_kbn == 2 ) { 
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[2].cid).attr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[2].cid).prev().addClass("ui-state-active");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[2].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[2].cid).prev().removeClass("ui-state-active");
                        } else if ( kokyakudlgData.kokyaku_data_p3.sex_kbn == 1 ) { 
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[2].cid).attr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[2].cid).prev().addClass("ui-state-active");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[2].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[2].cid).prev().removeClass("ui-state-active");
                        } else { 
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[2].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[2].cid).prev().removeClass("ui-state-active");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[2].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[2].cid).prev().removeClass("ui-state-active");
                        }
                        appcst.kojinInfoCol.models[2].set('hkg_yubin_no', kokyakudlgData.kokyaku_data_p3.yubin_no);
                        appcst.kojinInfoCol.models[2].set('hkg_addr1', kokyakudlgData.kokyaku_data_p3.addr1);
                        appcst.kojinInfoCol.models[2].set('hkg_addr2', kokyakudlgData.kokyaku_data_p3.addr2);
                        appcst.kojinInfoCol.models[2].set('hkg_tel', kokyakudlgData.kokyaku_data_p3.tel1);
                        appcst.kojinInfoCol.models[2].set('hk_death_ymd', kokyakudlgData.kokyaku_data_p3.shibo_ymd);

                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p3.seinengappi_ymd)) {
                            var wareki = $.msiJqlib.arrSeirekiToWareki(kokyakudlgData.kokyaku_data_p3.seinengappi_ymd);
                            if ( wareki[0] ) {
                                var gengo = wareki[0];
                                appcst.kojinInfoCol.models[2].set('hk_gengo', gengo);
                                appcst.kojinInfoCol.models[2].set('hk_wa_year', gengo+$.msiJqlib.fillPre(wareki[1]));
                            }
                            var spSe = kokyakudlgData.kokyaku_data_p3.seinengappi_ymd.split('/');
                            appcst.kojinInfoCol.models[2].set('hk_birth_year', spSe[0]);
                            appcst.kojinInfoCol.models[2].set('hk_birth_month', spSe[1]);
                            appcst.kojinInfoCol.models[2].set('hk_birth_day', spSe[2]);
                        }
                    }
                } else {
                    appcst.kojinInfoCol.models[2].set('hk_cif_no', null);
                    
                    appcst.kojinInfoCol.models[2].set('hk_nm', null);
                    appcst.kojinInfoCol.models[2].set('hk_last_nm', null);
                    appcst.kojinInfoCol.models[2].set('hk_first_nm', null);
                    appcst.kojinInfoCol.models[2].set('hk_knm', null);
                    appcst.kojinInfoCol.models[2].set('hk_last_knm', null);
                    appcst.kojinInfoCol.models[2].set('hk_first_knm', null);
                    appcst.kojinInfoCol.models[2].set('hk_sex_kbn', null);
                    $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[2].cid).removeAttr("checked", "checked");
                    $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[2].cid).prev().removeClass("ui-state-active");
                    $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[2].cid).removeAttr("checked", "checked");
                    $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[2].cid).prev().removeClass("ui-state-active");
                    appcst.kojinInfoCol.models[2].set('hkg_yubin_no', null);
                    appcst.kojinInfoCol.models[2].set('hkg_addr1', null);
                    appcst.kojinInfoCol.models[2].set('hkg_addr2', null);
                    appcst.kojinInfoCol.models[2].set('hkg_tel', null);
                    appcst.kojinInfoCol.models[2].set('hk_gengo', null);
                    appcst.kojinInfoCol.models[2].set('hk_wa_year', null);
                    appcst.kojinInfoCol.models[2].set('hk_birth_year', null);
                    appcst.kojinInfoCol.models[2].set('hk_birth_month', null);
                    appcst.kojinInfoCol.models[2].set('hk_birth_day', null);
                    appcst.kojinInfoCol.models[2].set('hk_seinengappi_ymd', null);
                    appcst.kojinInfoCol.models[2].set('hk_seinengappi_ymd_y', null);
                    appcst.kojinInfoCol.models[2].set('hk_nenrei_man', null);
                    appcst.kojinInfoCol.models[2].set('hk_death_ymd', null);
                }
            }
            if (kokyakudlgData.kokyaku_upd_p4) { // 設定されていれば
                if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_no_p4)) {
                    appcst.kojinInfoCol.models[3].set('hk_cif_no', kokyakudlgData.kokyaku_no_p4);
                    if (kokyakudlgData.kokyaku_data_p4) {
                        appcst.kojinInfoCol.models[3].set('hk_nm', kokyakudlgData.kokyaku_data_p4.kokyaku_hyoji_nm);
                        appcst.kojinInfoCol.models[3].set('hk_last_nm', kokyakudlgData.kokyaku_data_p4.kokyaku_nm1);
                        appcst.kojinInfoCol.models[3].set('hk_first_nm', kokyakudlgData.kokyaku_data_p4.kokyaku_nm2);
                        appcst.kojinInfoCol.models[3].set('hk_knm', kokyakudlgData.kokyaku_data_p4.kokyaku_hyoji_kana);
                        appcst.kojinInfoCol.models[3].set('hk_last_knm', kokyakudlgData.kokyaku_data_p4.kokyaku_kana1);
                        appcst.kojinInfoCol.models[3].set('hk_first_knm', kokyakudlgData.kokyaku_data_p4.kokyaku_kana2);
                        appcst.kojinInfoCol.models[3].set('hk_sex_kbn', kokyakudlgData.kokyaku_data_p1.sex_kbn);
                        if ( kokyakudlgData.kokyaku_data_p4.sex_kbn == 2 ) { 
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[3].cid).attr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[3].cid).prev().addClass("ui-state-active");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[3].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[3].cid).prev().removeClass("ui-state-active");
                        } else if ( kokyakudlgData.kokyaku_data_p4.sex_kbn == 1 ) { 
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[3].cid).attr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[3].cid).prev().addClass("ui-state-active");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[3].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[3].cid).prev().removeClass("ui-state-active");
                        } else { 
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[3].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[3].cid).prev().removeClass("ui-state-active");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[3].cid).removeAttr("checked", "checked");
                            $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[3].cid).prev().removeClass("ui-state-active");
                        }
                        appcst.kojinInfoCol.models[3].set('hkg_yubin_no', kokyakudlgData.kokyaku_data_p4.yubin_no);
                        appcst.kojinInfoCol.models[3].set('hkg_addr1', kokyakudlgData.kokyaku_data_p4.addr1);
                        appcst.kojinInfoCol.models[3].set('hkg_addr2', kokyakudlgData.kokyaku_data_p4.addr2);
                        appcst.kojinInfoCol.models[3].set('hkg_tel', kokyakudlgData.kokyaku_data_p4.tel1);
                        appcst.kojinInfoCol.models[3].set('hk_death_ymd', kokyakudlgData.kokyaku_data_p4.shibo_ymd);

                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p4.seinengappi_ymd)) {
                            var wareki = $.msiJqlib.arrSeirekiToWareki(kokyakudlgData.kokyaku_data_p4.seinengappi_ymd);
                            if ( wareki[0] ) {
                                var gengo = wareki[0];
                                appcst.kojinInfoCol.models[3].set('hk_gengo', gengo);
                                appcst.kojinInfoCol.models[3].set('hk_wa_year', gengo+$.msiJqlib.fillPre(wareki[1]));
                            }
                            var spSe = kokyakudlgData.kokyaku_data_p4.seinengappi_ymd.split('/');
                            appcst.kojinInfoCol.models[3].set('hk_birth_year', spSe[0]);
                            appcst.kojinInfoCol.models[3].set('hk_birth_month', spSe[1]);
                            appcst.kojinInfoCol.models[3].set('hk_birth_day', spSe[2]);
                        }
                    }
                } else {
                    appcst.kojinInfoCol.models[3].set('hk_cif_no', null);
                    
                    appcst.kojinInfoCol.models[3].set('hk_nm', null);
                    appcst.kojinInfoCol.models[3].set('hk_last_nm', null);
                    appcst.kojinInfoCol.models[3].set('hk_first_nm', null);
                    appcst.kojinInfoCol.models[3].set('hk_knm', null);
                    appcst.kojinInfoCol.models[3].set('hk_last_knm', null);
                    appcst.kojinInfoCol.models[3].set('hk_first_knm', null);
                    appcst.kojinInfoCol.models[3].set('hk_sex_kbn', null);
                    $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[3].cid).removeAttr("checked", "checked");
                    $('#kinfo_msi #male_'+appcst.kojinInfoCol.models[3].cid).prev().removeClass("ui-state-active");
                    $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[3].cid).removeAttr("checked", "checked");
                    $('#kinfo_msi #female_'+appcst.kojinInfoCol.models[3].cid).prev().removeClass("ui-state-active");
                    appcst.kojinInfoCol.models[3].set('hkg_yubin_no', null);
                    appcst.kojinInfoCol.models[3].set('hkg_addr1', null);
                    appcst.kojinInfoCol.models[3].set('hkg_addr2', null);
                    appcst.kojinInfoCol.models[3].set('hkg_tel', null);
                    appcst.kojinInfoCol.models[3].set('hk_gengo', null);
                    appcst.kojinInfoCol.models[3].set('hk_wa_year', null);
                    appcst.kojinInfoCol.models[3].set('hk_birth_year', null);
                    appcst.kojinInfoCol.models[3].set('hk_birth_month', null);
                    appcst.kojinInfoCol.models[3].set('hk_birth_day', null);
                    appcst.kojinInfoCol.models[3].set('hk_seinengappi_ymd', null);
                    appcst.kojinInfoCol.models[3].set('hk_seinengappi_ymd_y', null);
                    appcst.kojinInfoCol.models[3].set('hk_nenrei_man', null);
                    appcst.kojinInfoCol.models[3].set('hk_death_ymd', null);
                }
            }
            if ( kokyakudlgData.kokyaku_upd_p5 ) { // 設定されていれば
                if ( !$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_no_p5) ) {
                    this.model.set('m_cif_no', kokyakudlgData.kokyaku_no_p5);
                     this.model.set('m_cif_status', 2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
//                    this.model.set('m_cif_status', 1);
                    if ( kokyakudlgData.kokyaku_data_p5) {
                        this.model.set('m_nm', kokyakudlgData.kokyaku_data_p5.kokyaku_hyoji_nm);
                        this.model.set('m_last_nm', kokyakudlgData.kokyaku_data_p5.kokyaku_nm1);
                        this.model.set('m_first_nm', kokyakudlgData.kokyaku_data_p5.kokyaku_nm2);
                        this.model.set('m_knm', kokyakudlgData.kokyaku_data_p5.kokyaku_hyoji_kana);
                        this.model.set('m_last_knm', kokyakudlgData.kokyaku_data_p5.kokyaku_kana1);
                        this.model.set('m_first_knm', kokyakudlgData.kokyaku_data_p5.kokyaku_kana2);
                        this.model.set('m_sex_kbn', kokyakudlgData.kokyaku_data_p5.sex_kbn);
                        if ( kokyakudlgData.kokyaku_data_p5.sex_kbn == 2 ) { 
                            $('#m_female').attr("checked", "checked");
                            $('#m_female').prev().addClass("ui-state-active");
                            $('#m_male').removeAttr("checked", "checked");
                            $('#m_male').prev().removeClass("ui-state-active");
                        } else if ( kokyakudlgData.kokyaku_data_p5.sex_kbn == 1 ) { 
                            $('#m_male').attr("checked", "checked");
                            $('#m_male').prev().addClass("ui-state-active");
                            $('#m_female').removeAttr("checked", "checked");
                            $('#m_female').prev().removeClass("ui-state-active");
                        } else {
                            $('#m_male').removeAttr("checked", "checked");
                            $('#m_male').prev().removeClass("ui-state-active");
                            $('#m_female').removeAttr("checked", "checked");
                            $('#m_female').prev().removeClass("ui-state-active");
                        }
                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p5.seinengappi_ymd)) {
                            var spSe = kokyakudlgData.kokyaku_data_p5.seinengappi_ymd.split('/');
                            this.model.set('m_birth_year', spSe[0]);
                            this.model.set('m_birth_month', spSe[1]);
                            this.model.set('m_birth_day', spSe[2]);
                            var wareki = $.msiJqlib.arrSeirekiToWareki(kokyakudlgData.kokyaku_data_p5.seinengappi_ymd);
                            if ( wareki[0] ) {
                                var gengo = wareki[0];
                                this.model.set('m_gengo', gengo);
                                this.model.set('m_wa_year', gengo+$.msiJqlib.fillPre(wareki[1]));
                            }
                        }

                        this.model.set('mg_yubin_no', kokyakudlgData.kokyaku_data_p5.yubin_no);
                        this.model.set('mg_addr1', kokyakudlgData.kokyaku_data_p5.addr1);
                        this.model.set('mg_addr2', kokyakudlgData.kokyaku_data_p5.addr2);
                        this.model.set('mg_tel', kokyakudlgData.kokyaku_data_p5.tel1);
                        this.model.set('mg_m_tel', kokyakudlgData.kokyaku_data_p5.tel2);
                        this.model.set('m_mail_address', kokyakudlgData.kokyaku_data_p5.e_mail);
                        this.model.set('kaiin_sbt_cd', kokyakudlgData.kokyaku_data_p5.kaiin_sbt);
                        this.model.set('mk_kinmusaki_nm', kokyakudlgData.kokyaku_data_p5.kimsaki_nm);
                        this.model.set('mk_tel', kokyakudlgData.kokyaku_data_p5.kimsaki_tel);
                        this.calcNereiM();
                    }
                } else {
                    this.model.set('m_cif_no', null);
                     this.model.set('m_cif_status', kokyakudlgData.kokyaku_result_p5); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    
                    this.model.set('m_nm', null);
                    this.model.set('m_last_nm', null);
                    this.model.set('m_first_nm', null);
                    this.model.set('m_knm', null);
                    this.model.set('m_last_knm', null);
                    this.model.set('m_first_knm', null);
                    this.model.set('m_sex_kbn', null);
                    $('#m_male').removeAttr("checked", "checked");
                    $('#m_male').prev().removeClass("ui-state-active");
                    $('#m_female').removeAttr("checked", "checked");
                    $('#m_female').prev().removeClass("ui-state-active");
                    this.model.set('mg_yubin_no', null);
                    this.model.set('mg_addr1', null);
                    this.model.set('mg_addr2', null);
                    this.model.set('mg_tel', null);
                    this.model.set('mg_m_tel', null);
                    this.model.set('m_mail_address', null);
                    
                    this.model.set('m_birth_year', null);
                    this.model.set('m_birth_month', null);
                    this.model.set('m_birth_day', null);
                    this.model.set('m_seinengappi_ymd', null);
                    this.model.set('m_seinengappi_ymd_y', null);
                    this.model.set('m_nenrei_man', null);
                    this.model.set('kaiin_sbt_cd', null);
                    this.model.set('mk_kinmusaki_nm', null);
                    this.model.set('mk_tel', null);
                }
            }
            if ( kokyakudlgData.kokyaku_upd_p6) { // 設定されていれば
                if ( !$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_no_p6) ) {
                    this.model.set('s_cif_no', kokyakudlgData.kokyaku_no_p6);
                     this.model.set('s_cif_status', 2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
//                    this.model.set('s_cif_status', 1);
                    if ( kokyakudlgData.kokyaku_data_p6) {
                        appcst.sekyuModel.set('sekyu_nm', kokyakudlgData.kokyaku_data_p6.kokyaku_hyoji_nm);
                        appcst.sekyuModel.set('sekyu_last_nm', kokyakudlgData.kokyaku_data_p6.kokyaku_nm1);
                        appcst.sekyuModel.set('sekyu_first_nm', kokyakudlgData.kokyaku_data_p6.kokyaku_nm2);
                        appcst.sekyuModel.set('sekyu_knm', kokyakudlgData.kokyaku_data_p6.kokyaku_hyoji_kana);
                        appcst.sekyuModel.set('sekyu_last_knm', kokyakudlgData.kokyaku_data_p6.kokyaku_kana1);
                        appcst.sekyuModel.set('sekyu_first_knm', kokyakudlgData.kokyaku_data_p6.kokyaku_kana2);
                        appcst.sekyuModel.set('sekyu_yubin_no', kokyakudlgData.kokyaku_data_p6.yubin_no);
                        appcst.sekyuModel.set('sekyu_addr1', kokyakudlgData.kokyaku_data_p6.addr1);
                        appcst.sekyuModel.set('sekyu_addr2', kokyakudlgData.kokyaku_data_p6.addr2);
                        appcst.sekyuModel.set('sekyu_tel', kokyakudlgData.kokyaku_data_p6.tel1);
                        appcst.sekyuModel.set('mobile_tel', kokyakudlgData.kokyaku_data_p6.tel2);
                        if(!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p6.transfer_bank_cd)){
                            appcst.sekyuModel.set('sekyu_bank_no', kokyakudlgData.kokyaku_data_p6.transfer_bank_cd);
                            var transfer_bank_cd = kokyakudlgData.kokyaku_data_p6.transfer_bank_cd;
                            var bankData = data.brKozaInfo.filter(function (item, index) {
                                    return item.transfer_bank_cd == transfer_bank_cd;
                            });
                            if(!$.msiJqlib.isNullEx2(bankData) && bankData.length > 0){
                                appcst.sekyuModel.set('sekyu_bank_nm', bankData[0].transfer_bank_info);
                            }   
                        }
                        appcst.sekyuModel.set('soufu_last_nm', kokyakudlgData.kokyaku_data_p6.sekyu_atena1);
                        appcst.sekyuModel.set('soufu_first_nm', kokyakudlgData.kokyaku_data_p6.sekyu_atena2);
                        appcst.sekyuModel.set('soufu_yubin_no', kokyakudlgData.kokyaku_data_p6.sekyu_yubin_no);
                        appcst.sekyuModel.set('soufu_addr1', kokyakudlgData.kokyaku_data_p6.sekyu_addr1);
                        appcst.sekyuModel.set('soufu_addr2', kokyakudlgData.kokyaku_data_p6.sekyu_addr2);
                    }
                } else {
                    this.model.set('s_cif_no', null);
                    this.model.set('s_cif_status', kokyakudlgData.kokyaku_result_p6); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    
                    appcst.sekyuModel.set('sekyu_nm', null);
                    appcst.sekyuModel.set('sekyu_last_nm', null);
                    appcst.sekyuModel.set('sekyu_first_nm', null);
                    appcst.sekyuModel.set('sekyu_knm', null);
                    appcst.sekyuModel.set('sekyu_last_knm', null);
                    appcst.sekyuModel.set('sekyu_first_knm', null);
                    appcst.sekyuModel.set('soufu_last_nm', null);
                    appcst.sekyuModel.set('soufu_first_nm', null);
                    appcst.sekyuModel.set('soufu_yubin_no', null);
                    appcst.sekyuModel.set('soufu_addr1', null);
                    appcst.sekyuModel.set('soufu_addr2', null);
                    appcst.sekyuModel.set('sekyu_yubin_no', null);
                    appcst.sekyuModel.set('sekyu_addr1', null);
                    appcst.sekyuModel.set('sekyu_addr2', null);
                    appcst.sekyuModel.set('sekyu_tel', null);
                    appcst.sekyuModel.set('mobile_tel', null);
                    appcst.sekyuModel.set('sekyu_bank_no', null);
                    appcst.sekyuModel.set('sekyu_bank_nm', null);
                }
            }

        },

        // データ選択時ハンドラ
        // offset: 順序番号, selectedData, kokyakudlgData: モデルデータ
        _kokyakuHelperOnSet: function( offset, selectedData, kokyakudlgData ) {
            console.log( '_kokyakuHelperOnSet [offset, selectedData, kokyakudlgData]=>', [offset, selectedData, kokyakudlgData] );
        },
        // コース情報 pickup
        gojokaiHelper: function (e) {
            if (msiLib2.isChildWindow()) {
                return;
                msiLib2.showWarn('子画面からはNoの変更はできません');
            }
            var kijunYmd = this.model.get('sougi_ymd');
            if (!$.msiJqlib.isNullEx2(kijunYmd)) {
                if (!kijunYmd.match(/^(\d{4})[\/](\d{2})[\/](\d{2})$/) ) {
                    kijunYmd = $.msiJqlib.getStdDate();
                } 
            } else {
                kijunYmd = $.msiJqlib.getStdDate();
            }
            msiGlobalObj.mockaiindlgOpen( this, { 
                    // s_cust_nm: this.model.get('user_name'), // 氏名. 検索条件の初期値
                    // s_member_no: '00-0-00000-000', // 加入者番号
                    // または個別設定、s_member_no2: '22', s_member_no3: '3', s_member_no4: '44444', s_member_no5: '555',
                    // s_tel_ex: '044-4444-4444', // TEL
                    // s_yubin_no: '123-4567', // 郵便番号
                    // s_addr_ex: '住所',
                    // s2_is_add_sisan_data: 0, // 「契約情報検索」応答形式で返す場合は 0. 通常不要.
                    s2_application_date: kijunYmd, // 利用予定日(契約情報参照で使う)
                    s2_user_name: appcst.kojinInfoCol.models[0].get('hk_last_nm')+'　'+appcst.kojinInfoCol.models[0].get('hk_first_nm')
                }, // 利用者(契約情報参照で使う)
                this.selectMocCustp,   // [決定]時のハンドラ
//                this.clearMocCustp     // [取消]時のハンドラ. 現状、ダイアログに[取消]ボタンは非表示なので使いません
            );
        },
        selectMocCustp: function (data) {
            console.log('@@@ selectMocCustp =>', data);
            if (!data) {
                return;
            }

            // 空いている行を見つける. 
            var emptyOffset = _.reduce( appcst.gojokaiMemberCol.models,
                                        function(ac, m, i) { if ( ac === null && !m.get('kain_no') ) ac = i; return ac; }, null );
            // 施行互助会会員の会員番号を取得する
            var kaiin_no_array = [];
            appcst.gojokaiMemberCol.each(function (m, i) {
                kaiin_no_array.push(m.get('kain_no'));
            });
            var err_flg = false;
            var err_msg = '';
            _.each(data, function (v, k) {
                var course_snm_cd = null;
                _.each(appcst.data.gojokaiCouseMst, function (item, iw) {
                    if (!$.msiJqlib.isNullEx2(course_snm_cd)) {
                        return false;
                    }
                    if (!$.msiJqlib.isNullEx2(iw) && v['CourseName'] === iw) {
                        course_snm_cd = iw;
                    } else if (!$.msiJqlib.isNullEx2(iw) && v['CourseName'].indexOf(iw) === 0) {
                        course_snm_cd = iw;
                    } else if (!$.msiJqlib.isNullEx2(iw) && iw.indexOf(v['CourseName']) === 0) {
                        course_snm_cd = iw;
                    }
                });
                var taxInfo = appcst.data.taxInfoAll;
                var zei_cd = null;
                _.each(taxInfo, function (item) {
                    if (item.zei_rtu == changeToNum(String(v['ContractTaxRate']))) {
                        zei_cd = item.zei_cd;
                    }
                });
                var kain_no = null;
                if (!$.msiJqlib.isNullEx2(v['member_no']) && !$.msiJqlib.isNullEx2(v['member_no_id'])) {
                    var kainNoAry = v['member_no_id'].split('-');
                    kain_no = kainNoAry[0]+'-'+v['member_no'];
                }
                var data = {cif_no: v['CustomerNo'],
                    course_snm_cd: course_snm_cd,
                    kain_no: kain_no,
                    kanyu_nm: v['CustomerName'],
                    kanyu_dt: v['ContractDate'],
                    keiyaku_gaku: changeToNum(String(v['TerminationValue'])),
                    harai_no: changeToNum(String(v['TotalPayNum'])),
                    harai_gaku: changeToNum(String(v['TotalPayValue'])) - changeToNum(String(v['BonusAmount'])) - changeToNum(String(v['BalanceDiscountValue'])) - changeToNum(String(v['PrepaymentDiscountValue'])),
                    wari_gaku: changeToNum(String(v['PrepaymentDiscountValue'])),
                    waribiki_gaku: changeToNum(String(v['BalanceDiscountValue'])),
                    early_use_cost_disp: changeToNum(String(v['EarlyUseCost'])),
                    meigi_chg_cost_disp: changeToNum(String(v['RenameCommission'])),
                    zei_cd: zei_cd,
                    cur_cd: v['ContractCode'],
                    v_free10: v['ContractStatus'],
                    v_free11: v['EarlyUseOriginDate'],
                    v_free12: v['EarlyUseLimitedDate'],
                    v_free13: v['RenameCommissionUsage'],
                    v_free14: v['TargetUser'],
                    v_free15: v['TargetDate'],
                    v_free16: kain_no,
                    v_free17: v['ContractNo'],
                    kanyu_tax: changeToNum(String(v['ContractTax'])),
//                    kaiin_info_kbn: '3',  // テスト
                    kaiin_info_kbn: '1',
                    n_free3: changeToNum(String(v['BonusAmount'])),
                    n_free4: changeToNum(String(v['PremiumMonths'])),
                    cur_cd: changeToNum(String(v['ContractStatus'])),
                    zan_gaku: (changeToNum(v['TerminationValue'])-changeToNum(v['TotalPayValue'])-changeToNum(v['PremiumMonths'])),
                };
                if (kaiin_no_array.indexOf(v['ContractNo']) !== -1) {
                    return;
                }
                var m = appcst.gojokaiMemberCol.at(emptyOffset++); // 確保分を超えるとエラー
                if (emptyOffset > 10) {
//                    err_flg = true;
//                    err_msg = '10件を超えての登録はできません。';
                    return;
                }
                m.set(data);
            });
//            if (err_flg) {
//                $.msiJqlib.showErr(err_msg);
//                return;
//            }
        },
        clearMocCustp: function (data) {
            console.log('@@@ clearMocCustp =>', data);
            this._clearCustAll();
            this.$('#mocRes').val(null);
        },
        _clearCustOne: function (offset) {
            this.model.set('ContractNo' + offset, null);
            this.model.set('CustomerName' + offset, null);
            this.model.set('CustomerEtc' + offset, null);
        },
        _clearCustAll: function () {
            var that = this;
            _.each([1, 2, 3, 4], function (cnt) {
                that._clearCustOne(cnt);
            });
        },
        // 見積担当者ヘルパー処理 
        mitsuTanHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'mitsu_tanto_cd': data.code, 'mitsu_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'mitsu_tanto_cd': null, 'mitsu_tanto_nm': null});
                }
            });
        },
        // プルダウン生成処理
        setKeiyakuCif: function () {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/setkeiyakucif',
                data: {
                    seko_no: this.model.get('seko_no'),
                    m_cif_no: this.model.get('m_cif_no'),
                    s_cif_no: this.model.get('s_cif_no'),
                    dataKojinInfoColJson: JSON.stringify(appcst.kojinInfoCol.toJSON()),
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        orgKeiyakuSbt = mydata.keiyakuCif;
                    }
                }
            });
        },
        // 喪主年齢計算処理
        calcNereiM: function () {
            var era = this.model.get('m_wa_year');
            var seireki = this.model.get('m_birth_year');
            var month = this.model.get('m_birth_month');
            var day = this.model.get('m_birth_day');
            if ($.msiJqlib.isNullEx2(era) || $.msiJqlib.isNullEx2(month) || $.msiJqlib.isNullEx2(day)) {
                return;
            }
            var gengo = era.slice(0, 1);
            var wareki_y = era.slice(1);
            var seinengappi = seireki + '/' + month + '/' + day;
            if (!$.msiJqlib.chkDate(seinengappi)) { // 日付チェック
                seinengappi = null;
            }
            this.model.set('m_gengo', gengo);
            this.model.set('m_seinengappi_ymd', wareki_y + '/' + month + '/' + day);
            this.model.set('m_seinengappi_ymd_y', seinengappi);
            if (seinengappi) {
                var pram = {
                    nakunariymd: null,
                    seinengappi: seinengappi,
                    setData: function (data) {
                        this.model.set("m_nenrei_man", data.man);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("m_nenrei_man", null);
            }
        },
        // 年齢計算処理
        calcNerei: function (pram) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/calcnenrei',
                data: {
                    nakunariymd: pram.nakunariymd,
                    seinengappi: pram.seinengappi
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        if (_.has(pram, "setData")) {
                            pram.setData.apply(that, [mydata]);
                        }
                    }
                }
            });
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        // 宗派情報をクリアする
        clearSyuha: function (e) {
            if (e.val !== this.model.get("syushi_cd")) {
                this.model.set({'syuha_cd': null
                    , 'syuha_kbn': null
                    , 'syuha_nm': null
                    , 'syuha_knm': null
//                    , 'jyusho_cd': null
//                    , 'jyusho_nm': null
//                    , 'jyusho_knm': null
//                    , 'temple_tel': null
//                    , 'temple_fax': null
//                    , 'temple_addr1': null
//                    , 'temple_addr2': null
                });
            }
        },
        // マイナスのときに赤字クラスを追加する処理
        toggleClass: function (e) {
            appcst.toggleAkajiClass(this, ['shikijo_shiyou_prc', 'tuya_paku_su', 'tuya_paku_su2', 'tuya_shikijo_tanka', 'tuya_shikijo_tanka2', 'tuya_shikijo_prc1', 'tuya_shikijo_prc2', 'n_free1']);
        },
        // 葬儀情報(ヘッダー)の台帳番号バインディング処理
        setHeaderDaichoNo: function (d1, d2, d3) {
            this.$("#hd_daicho_no").text(d1 + '-' + d2 + '-' + d3);
        },
        // 葬儀情報(ヘッダー)の喪主バインディング処理
        setHeadeMaddr: function (addr1, addr2) {
            if ($.msiJqlib.isNullEx2(addr1)) {
                addr1 = '';
            }
            if ($.msiJqlib.isNullEx2(addr2)) {
                addr2 = '';
            }
            this.$("#hd_mg_addr").text(addr1 + ' ' + addr2);
        },
        doIrai: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0184',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
            return;
        },
        initialize: function () {
            this.listenTo(appcst.niteiCol, 'reset', this.addAllNiteiCol);
            this.listenTo(appcst.kojinInfoCol, 'reset', this.addAllKojinInfoCol);
            this.listenTo(appcst.appModel, 'change:kaiin_kbn', this.setKaiinStatus);
            this.listenTo(appcst.appModel, 'change:free2_cd', function () {
                if ($.msiJqlib.isNullEx2(this.model.get("free2_cd"))) {
                    $("#seko_clear, #btn_copy").hide();
                } else {
                    $("#seko_clear, #btn_copy").show();
                }
            });
            // 見積式場切り替え処理
            this.setSideMenuStatus();
            // 赤字クラス切り替え処理
            this.toggleClass();
            this.setKaiinStatus();
            // 日付ピッカー
            this.$("#date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.render();
        },
        render: function () {
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.stickit();
            this.setSelect2();
            $('.exp_consultation').hide();
            return this;
        },
        // select2設定処理
        setSelect2: function () {
            // ヘッダー部
            // 申込区分
            $.msiJqlib.setSelect2Com1(this.$("#apply_type"), {data: $.msiJqlib.objToArray3(data.dataKbns.moushi_kbn)});
            // 葬儀区分
            $.msiJqlib.setSelect2Com1(this.$("#funeral_type"), {data: $.msiJqlib.objToArray3(data.dataKbns.sougi_kbn)});
            // 個人情報保護
            $.msiJqlib.setSelect2Com1(this.$("#personal_info"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.p_info)}, $.msiJqlib.setSelect2Default1)));
            // 会員区分
            $.msiJqlib.setSelect2Com1(this.$("#member"), {data: $.msiJqlib.objToArray3(data.dataKbns.kaiin_kbn)});
            // 契約種別CIF
            appcst.keiyaku_sbt = orgKeiyakuSbt;
            $.msiJqlib.setSelect2Com1(this.$("#member_sbt"), ($.extend({data: function () {
                    return {results: appcst.keiyaku_sbt};
                }}, $.msiJqlib.setSelect2Default1)));
            // 見積式場
            $.msiJqlib.setSelect2Com1(this.$("#est_shikijo_cd"), ($.extend({data: data.dataKbns.est_shikijo}, $.msiJqlib.setSelect2Default2)));
            $.msiJqlib.setSelect2Com1(this.$("#seko_shikijo_cd"), ($.extend({data: data.dataKbns.est_shikijo}, $.msiJqlib.setSelect2Default1)));
            // 会員種別
            $.msiJqlib.setSelect2Com1(this.$("#kaiin_sbt_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kaiin_sbt_cd)}, $.msiJqlib.setSelect2Default2)));
            // 喪主請求情報タブ
            // 続柄
            $.msiJqlib.setSelect2Com1(this.$("#s_chief_relationship"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 喪主様からみた続柄
            $.msiJqlib.setSelect2Com1(this.$("#s_chief_relationship2"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#birthday_era"), {data: $.msiJqlib.objToArray3(data.dataKbns.gengo)});
            $.msiJqlib.setSelect2Com1(this.$("#birthday_month"), {data: $.msiJqlib.objToArray3(data.dataKbns.month)});
            $.msiJqlib.setSelect2Com1(this.$("#birthday_day"), {data: $.msiJqlib.objToArray3(data.dataKbns.day)});
            // 喪主CIFNoステータス
            $.msiJqlib.setSelect2Com1(this.$("#m_cif_status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.cif_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 請求先CIFNoステータス
            $.msiJqlib.setSelect2Com1(this.$("#s_cif_status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.cif_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 勤務先
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #employee"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kinmu_kbn_m)}, $.msiJqlib.setSelect2Default1)));
            // 宗旨区分
            $.msiJqlib.setSelect2Com1(this.$("#syushi_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.syushi_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 宗派区分
            appcst.syuha_kbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
            $.msiJqlib.setSelect2Com1(this.$("#syuha_cd"), ($.extend({data: function () {
                    return {results: appcst.syuha_kbns};
                }}, $.msiJqlib.setSelect2Default2)));
        },
        addNiteiOne: function (nitei) {
            var v = new NiteiView({model: nitei});
            this.$("#infodate-tab #infodate").append(v.render().el);
        },
        addAllNiteiCol: function (collection) {
            var $infodate = this.$("#infodate-tab #infodate");
            $infodate.find('fieldset').remove();
            collection.each(this.addNiteiOne, this);
        },
        addKojinInfoOne: function (memo) {
            var v = new KojinInfoView({model: memo});
            this.$("#kinfo_msi").append(v.render().el);
        },
        addAllKojinInfoCol: function (collection) {
            var $memoMsi = this.$("#kinfo_msi");
            $memoMsi.find('tbody').remove();
            collection.each(this.addKojinInfoOne, this);
        },
        setKaiinStatus: function () {
            var kaiinKbn = this.model.get('kaiin_kbn');
            // 会員区分がアスカラメイト・ファーストステップ・こすもすの場合は活性、それ以外は非活性
            if (kaiinKbn == KAIIN_KBN_A || kaiinKbn == KAIIN_KBN_F || kaiinKbn == KAIIN_KBN_K) {
                $(".cls_member_sbt").removeAttr('disabled');
            } else {
                $(".cls_member_sbt").attr('disabled', 'disabled');
                this.model.set('v_free13', null);
                this.model.set('v_free14', null);
                this.model.set('n_free7', null);
            }
        },
        // 見積式場変更によるサイドメニュー切り替え処理
        setSideMenuStatus: function () {
            var sideMenu = data.sideMenuDataCol;
            var moushi_kbn = this.model.get('moushi_kbn');
            if ($.msiJqlib.isNullEx2(this.model.get('est_shikijo_cd'))) {
                // お客様情報以外非表示
                _.each(sideMenu, function (item) {
                    var css = item.css_class;
                    if (css != 'customer') {
                        $("#side ." + css).hide();
                    }
                });
            } else {
                _.each(sideMenu, function (item) {
                    var css = item.css_class;
                    $("#side ." + css).show();
                });
            }
        },
        // チェックボックス切り替え処理
        setCheckBox: function (e, pro, target) {
            var val = $(target + ':checked').val();
            this.model.set(pro, val === "1" ? "1" : "0");
        },
        // タブ切り替え処理
        changeTab: function (e) {
            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.tab li'), $(e.currentTarget));
            // 互助会確認の場合、加入状況をクリックする
            var tabIdx = $.msiJqlib.getTabIndex();
            if (tabIdx === 3) {
                this.$("#member_group_set #member_group_1").click();
            }
        },
        isInputOk: function () {
            var aMsg = [];
            // 施行基本フリーモデルチェック
            var result = appcst.kfModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    if ($.inArray(v, aMsg) < 0) {
                        aMsg.push(v);
                    }
                });
            }
            // 施行基本モデルチェック
            var result = appcst.appModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 施行日程コレクションチェック
            appcst.niteiCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                    });
                }
            });
            // 請求先情報モデルチェック
            var result = appcst.sekyuModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            var len = 0;
            if (!$.msiJqlib.isNullEx2(appcst.sekyuModel.get("sekyu_first_knm")) && !$.msiJqlib.isNullEx2(appcst.sekyuModel.get("sekyu_last_knm"))) {
                len = changeToNum(appcst.sekyuModel.get('sekyu_first_knm').length) + changeToNum(appcst.sekyuModel.get('sekyu_last_knm').length);
            } else if (!$.msiJqlib.isNullEx2(appcst.sekyuModel.get("sekyu_first_knm"))) {
                len = changeToNum(appcst.sekyuModel.get('sekyu_first_knm').length);
            } else if (!$.msiJqlib.isNullEx2(appcst.sekyuModel.get("sekyu_last_knm"))) {
                len = changeToNum(appcst.sekyuModel.get('sekyu_last_knm').length);
            }
            if (len >= 60) {
                aMsg.push('請求先名のフリガナは合計で60文字未満となるように入力してください。');
            }
            // 施行互助会会員コレクションチェック
            appcst.gojokaiMemberCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                        ;
                    });
                }
            });
            // 施行互助会情報モデルチェック
            var result = appcst.gojokaiInfoModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 契約先情報モデルチェック
            var result = appcst.sekoKeiyakusakiInfoModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 施行故人情報コレクションチェック
            appcst.kojinInfoCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                        ;
                    });
                }
            });
            // NG
            if (aMsg.length > 0) {
                var errClsNm = ".error1";
                var $li = this.$('.tab li');
                if (this.$("#infochief-tab").find(errClsNm).length) {
                    $li.eq(0).find("a").click();
                } else if (this.$("#input-tab").find(errClsNm).length) {
                    $li.eq(1).find("a").click();
                } else if (this.$("#infodate-tab").find(errClsNm).length) {
                    $li.eq(2).find("a").click();
                } else if (this.$("#infomember-tab").find(errClsNm).length) {
                    $li.eq(3).find("a").click();
                }
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }
            // OK
            $.msiJqlib.clearAlert();
            return true;
        },
        doSave: function (mode) {
            if (!this.isInputOk()) {
                return;
            }
            if (!this.gojoYotoCheck()) {
                return;
            }
            var bumonCd = $('#hall_cd').val();
            var bumonChanged = bumonCd !== appcst.appModel.get("bumon_cd");
            // 見積確定
            if (!$.msiJqlib.isNullEx2(appcst.appModel.get("jichu_kakute_ymd")) && bumonChanged) {
                this.exeCheck(mode);
            } else {
                this.exeSave(mode);
            }
        },
        usePrcCheck: function () {
            var flg = true;
            var kaiin_kbn = appcst.appModel.get('kaiin_kbn');
            // 会員区分が互助会以外はスルーする
            if (kaiin_kbn != KAIIN_KBN_GOJO) {
                return flg;
            }
            var use_keiyaku_gaku = appcst.gojokaiInfoModel.get('use_keiyaku_gaku');
            if ($.msiJqlib.isNullEx2(use_keiyaku_gaku)) {
                return flg;
            }
            var use_prc = 0;
            var yoto_cnt = 0;
            if (appcst.gojokaiMemberCol.length != 0) {
                _.each(appcst.gojokaiMemberCol.models, function (item) {
                    if (!$.msiJqlib.isNullEx2(item.get('yoto_kbn')) && (item.get('yoto_kbn') == YOTO_COURSE || item.get('yoto_kbn') == YOTO_PLAN) && item.get('delete_check') == '0') {
                        use_prc += changeToNum(item.get('keiyaku_gaku'));
                        yoto_cnt++;
                    }
                });
            }
            // ご利用コースの契約金額と一致しなければfalse
            if (yoto_cnt != 0 && use_keiyaku_gaku != use_prc) {
                flg = false;
            }

            return flg;
        },
        gojoYotoCheck: function () {
            var flg = true;
            var kaiin_kbn = appcst.appModel.get('kaiin_kbn');
            // 会員区分が互助会以外はスルーする
//            if (kaiin_kbn != KAIIN_KBN_GOJO) {
//                return flg;
//            }
            var yoto_cnt = 0;
            if (appcst.gojokaiMemberCol.length != 0) {
                _.each(appcst.gojokaiMemberCol.models, function (item) {
                    if (!$.msiJqlib.isNullEx2(item.get('yoto_kbn')) && (item.get('yoto_kbn') == YOTO_COURSE) && item.get('delete_check') == '0') {
                        yoto_cnt++;
                    }
                });
            }
            // コース施行が２件以上あればfalse
            if (yoto_cnt > 1) {
                $.msiJqlib.showErr('用途にコース施行が複数件選択されています。');
                flg = false;
            }

            return flg;
        },
        exeCheck: function (mode) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/CheckGetujiFixInput',
                data: {dataAppJson: JSON.stringify(appcst.appModel.toJSON())},
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        that.exeSave(mode);
                    } else if (mydata.status === 'INFO') {
                        $.msiJqlib.setProgressing(false);
                        if (!confirm(mydata.msg)) {
                            return;
                        }
                        $.msiJqlib.setProgressing(true);
                        that.exeSave(mode);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        exeSave: function (mode) {
            var t = this;
            // 施行基本イコール
            var sekoKihonEq = $.msiJqlib.isEqual(appcst.appModel.toJSON(), orgDataApp);
            // 施行基本汎用フリー情報イコール
            var sekoKihonFreeEq = $.msiJqlib.isEqual(appcst.kfModel.toJSON(), orgDataKihonFree);
            // 施行日程イコール
            var sekoNiteiEq = $.msiJqlib.isEqual(appcst.niteiCol.toJSON(), orgDataNiteiCol);
            // 請求先情報イコール
            var sekyuInfoEq = $.msiJqlib.isEqual(appcst.sekyuModel.toJSON(), orgDataSekyuInfo);
            // 施行互助会情報イコール
            var sekoGojokaiInfoEq = $.msiJqlib.isEqual(appcst.gojokaiInfoModel.toJSON(), orgDataGojokaiInfo);
            // 施行契約先情報イコール
            var sekoKeiyakusakiInfoEq = $.msiJqlib.isEqual(appcst.sekoKeiyakusakiInfoModel.toJSON(), orgDataSekoKeiyakusakiInfo);
            // 施行互助会加入者イコール
            var sekoGojokaiMemberEq = $.msiJqlib.isEqual(appcst.gojokaiMemberCol.toJSON(), orgDataGojokaiMemberCol);
            // 施行故人情報イコール
            var sekoKojinInfoEq = $.msiJqlib.isEqual(appcst.kojinInfoCol.toJSON(), orgDataKojinInfoCol);
            var kaiinKbn = appcst.appModel.get("kaiin_kbn");
            var $tabId = $.msiJqlib.getTabId();
            if ($.msiJqlib.isNullEx2(mode)) {
                if (sekoKihonEq && sekoNiteiEq && sekyuInfoEq && sekoGojokaiInfoEq && sekoGojokaiMemberEq && sekoKeiyakusakiInfoEq && sekoKihonFreeEq && sekoKojinInfoEq) {
                    // 保存時のタブが会員情報タブかつその他加入確認タブの場合は変更がなくても印刷可能にする
//                    if (kaiinKbn == KAIIN_KBN_COM) {
//                        if ($tabId.is('#tab-kaiin-info')) {
//                            if (this.$("#member_group_set .lbl_member_group_2").hasClass('ui-state-active')) {
//                                t.doKeiyakuPrint();
//                                return;
//                            }
//                        }
//                    }
                    $.msiJqlib.showInfo('データの変更がありません');
                    return;
                }
            } else {
                if (mode != 'shikijo') {
                    if (sekoKihonEq && sekoNiteiEq && sekyuInfoEq && sekoGojokaiInfoEq && sekoGojokaiMemberEq && sekoKeiyakusakiInfoEq && sekoKihonFreeEq && sekoKojinInfoEq) {
                        // 保存時のタブが会員情報タブかつその他加入確認タブの場合は変更がなくても印刷可能にする
//                        if (kaiinKbn == KAIIN_KBN_COM) {
//                            if ($tabId.is('#tab-kaiin-info')) {
//                                if (this.$("#member_group_set .lbl_member_group_2").hasClass('ui-state-active')) {
//                                    t.doKeiyakuPrint();
//                                    return;
//                                }
//                            }
//                        }
                        $.msiJqlib.showInfo('データの変更がありません');
                        return;
                    }
                }
            }
            if ($.msiJqlib.isNullEx2(mode)) {
                // 会員区分が互助会の時に金額が一致しなければ警告をだす
                if (!this.usePrcCheck()) {
                    if (!confirm('ご利用コースの契約金額とご利用金額が一致しませんがよろしいでしょうか。')) {
                        return;
                    }
                }
            } else {
                if (mode != 'shikijo') {
                    // 会員区分が互助会の時に金額が一致しなければ警告をだす
                    if (!this.usePrcCheck()) {
                        if (!confirm('ご利用コースの契約金額とご利用金額が一致しませんがよろしいでしょうか。')) {
                            return;
                        }
                    }
                }
            }
            // チェンジフラグ設定
            var changeFlg = JSON.stringify({
                kihonChangeFlg: !sekoKihonEq,
                kihonFreeChangeFlg: !sekoKihonFreeEq,
                niteiChangeFlg: !sekoNiteiEq,
                sekyuInfoChangeFlg: !sekyuInfoEq,
                gojokaiInfoChangeFlg: !sekoGojokaiInfoEq,
                sekoKeiyakusakiInfoChangeFlg: !sekoKeiyakusakiInfoEq,
                gojokaiMemberChangeFlg: !sekoGojokaiMemberEq,
                kojinInfoChangeFlg: !sekoKojinInfoEq
            });
//            var bumonCd = $('#hall_cd').val();
//            appcst.appModel.set('bumon_cd', bumonCd);
            // 施行基本情報
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            // 施行基本汎用フリー情報
            var dataSekoKihonFreeJson = JSON.stringify(appcst.kfModel.toJSON());
            // 施行日程情報
            var dataNiteiColJson = JSON.stringify(appcst.niteiCol.toJSON());
            // 請求先情報
            var dataSekyuInfoJson = JSON.stringify(appcst.sekyuModel.toJSON());
            // 施行互助会情報
            var dataGojokaiInfoJson = JSON.stringify(appcst.gojokaiInfoModel.toJSON());
            // 施行契約先情報
            var dataSekoKeiyakusakiInfoJson = JSON.stringify(appcst.sekoKeiyakusakiInfoModel.toJSON());
            // 施行故人情報
            var dataKojinInfoColJson = JSON.stringify(appcst.kojinInfoCol.toJSON());
            // 施行互助会加入者情報
            // 会員番号があるデータに絞込み
            var dataGojokaiMemberCol = this.gojokaiMemberfilter();
            var dataGojokaiMemberDeleteCol = this.gojokaiMemberDeletefilter();
            var dataGojokaiMemberColJson = JSON.stringify(dataGojokaiMemberCol);
            var dataGojokaiMemberDeleteColJson = JSON.stringify(dataGojokaiMemberDeleteCol);

            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/houji/customerinfosave',
                data: {
                    oldDataApp: orgDataApp,
                    dataSekoKihonJson: dataSekoKihonJson,
                    dataSekoKihonFreeJson: dataSekoKihonFreeJson,
                    dataNiteiColJson: dataNiteiColJson,
                    dataSekyuInfoJson: dataSekyuInfoJson,
                    dataGojokaiInfoJson: dataGojokaiInfoJson,
                    dataSekoKeiyakusakiInfoJson: dataSekoKeiyakusakiInfoJson,
                    dataGojokaiMemberColJson: dataGojokaiMemberColJson,
                    dataGojokaiMemberDeleteColJson: dataGojokaiMemberDeleteColJson,
                    dataKojinInfoColJson: dataKojinInfoColJson,
                    controllerName: appcst.data.controllerName,
                    changeFlg: changeFlg,
                    sidemenukey: JSON.stringify(sidemenukey)
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        _resetData(mydata.dataSekoKihon,
                                mydata.dataNiteiCol,
                                mydata.dataSekyuInfo,
                                mydata.dataGojokaiInfo,
                                mydata.dataGojokaiMemberCol,
                                mydata.dataSekoKihonFree,
                                mydata.dataKojinInfoCol,
                                mydata.dataSekoKeiyakusakiInfo);
                        $("#header .id_number span.number").text(mydata.dataSekoKihon.seko_no);  // 施行番号設定
                        $.msiJqlib.showInfo(mydata.msg);
                        var matcheds = location.href.match(/(\/sn\/\d+)/);
                        if (!matcheds) {
                            var m = location.href.match(/\/(\D+)\/(\D+)\/(\D+)\/(\D+)/);
                            var herf;
                            if (m) {
                                herf = $.msiJqlib.baseUrl() + "/" + m[2] + "/" + m[3] + "/" + m[4].replace("/", '') + "/sn/" + mydata.dataSekoKihon.seko_no;
                            } else {
                                herf = location.href + "/sn/" + mydata.dataSekoKihon.seko_no;
                            }
                            location.href = herf;
                        }
                        t.setSideMenuStatus();
//                        t.doKeiyakuPrint();
                        if (!$.msiJqlib.isNullEx2(mode)) {
                            if (mode == 'shikijo') {
                                t.exeShowShikijoYoyaku();
                            }
                        }
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                }
            });
        },
        doPrint: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/pdf0101',
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
        },
        doKeiyakuPrint: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            var kaiinKbn = appcst.appModel.get("kaiin_kbn");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            // 会員区分が企業のみ
            if (kaiinKbn != KAIIN_KBN_COM) {
                return;
            }
            var pdfUrl = null;
            var $tabId = $.msiJqlib.getTabId();
            // 契約先状況報告書のみ現在は使用
            // 保存時のタブが会員情報タブかつその他加入確認タブの場合に印刷処理
            if ($tabId.is('#tab-kaiin-info')) {
                if (this.$("#member_group_set .lbl_member_group_2").hasClass('ui-state-active')) {
                    var pdfUrl = 'pdf0102';
                }
            }
            if ($.msiJqlib.isNullEx2(pdfUrl)) {
                return;
            }
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/' + pdfUrl,
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
            return;
        },
        doDelete: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('お客様情報を削除します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/customerinfodelete',
                data: {
                    seko_no: sekoNo
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        var matcheds = location.href.match(/(.+)(\/sn\/\d+)/);
                        if (matcheds) {
                            location.href = matcheds[1];
                        } else {
                            window.location.reload();
                        }
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        doCancel: function () {
            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }
            window.location.reload();
        },
        // 会員番号があるデータに絞込み
        gojokaiMemberfilter: function () {
            var dataGojokaiMemberCol = appcst.gojokaiMemberCol.filter(function (item) {
                return !$.msiJqlib.isNullEx2(item.get("kain_no")) && item.get("delete_check") == '0';
            });
            return dataGojokaiMemberCol;
        },
        // 会員番号があるデータに絞込み
        gojokaiMemberDeletefilter: function () {
            var dataGojokaiMemberCol = appcst.gojokaiMemberCol.filter(function (item) {
                return item.get("delete_check") == '1';
            });
            return dataGojokaiMemberCol;
        },
        // 郵便番号ヘルパー処理
        zipHelper: function (e) {
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            var val = $target.data("zip");
            var zip, addr1;
            var m = this.model;
            if (val === 'k1') { // 故人現住所
                zip = 'kg_yubin_no';
                addr1 = 'kg_addr1';
            } else if (val === 'm1') {// 喪主現住所
                zip = 'mg_yubin_no';
                addr1 = 'mg_addr1';
            } else if (val === 's1') {// 請求先現住所
                zip = 'sekyu_yubin_no';
                addr1 = 'sekyu_addr1';
                m = appcst.sekyuModel;
            } else if (val === 's2') {// 請求送付先現住所
                zip = 'soufu_yubin_no';
                addr1 = 'soufu_addr1';
                m = appcst.sekyuModel;
            } else if (val === 'r1') {// 領収証送付先現住所
                zip = 'ryosyu_soufu_yubin_no';
                addr1 = 'ryosyu_soufu_addr1';
                m = appcst.sekyuModel;
            } else if (val === 't1') {// 司式者
                zip = 'temple_yubin_no';
                addr1 = 'temple_addr1';
            }
            this.$el.msiPickHelper({
                action: 'zipno',
                onSelect: function (data) {
                    m.set(zip, data.code);
                    m.set(addr1, data.name);
                },
                onClear: function () {
                    m.set(zip, null);
                    m.set(addr1, null);
                }
            });
        },
        // 受付担当者ヘルパー処理 
        uketukeHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'uketuke_tanto_cd': data.code, 'uketuke_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'uketuke_tanto_cd': null, 'uketuke_tanto_nm': null});
                }
            });
        },
        // 施行担当者ヘルパー処理
        sekoHelper: function () {
            var m = this.model;
            // ステータスが施行金額確定以上は変更不可
            if (m.get('status_kbn') >= STATUS_SEKO_KAKUTEI) {
                return;
            }
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'seko_tanto_cd': data.code, 'seko_tanto_nm': data.name, 'seko_tanto_tel': data.mobile_tel});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text(data.name);
                },
                onClear: function () {
                    m.set({'seko_tanto_cd': null, 'seko_tanto_nm': null, 'seko_tanto_tel': null});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text('');
                }
            });
        },
        sekoInfoHelper: function () {
            var dataIn = {init_search: 1}; // 初回表示時検索する/しない=1/0
            var t = this;
            var seko_check = this.model.get('seko_check_kbn');
            var status_kbn = this.model.get('status_kbn');
            if (seko_check == '1') {
                return; // 関連施行なしにチェックがあれば何もしない
            }
            // 請求承認されたら変更不可
            if (status_kbn > STATUS_SEKO_KAKUTEI) {
                return;
            }
            $.msiJqlib.celemonyDialogOnSelect = function (data) {
                if (data.length > 0) {
                    var sekoInfo = data[0];
                    appcst.appModel.set("free2_cd", sekoInfo.seko_no);
                    t.copySekoInfo();
                    $("#seko_clear, #btn_copy").show();
                    $('#seko_check_kbn').attr('disabled', 'disabled');
                }
            };
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/mref/sekosogidialog',
                type: 'GET',
                data: dataIn,
                dataType: 'html',
                success: function (html) {
                    // console.log( html );
                    $('#celemony_dialog').remove();
                    $(html).appendTo($('#wrapper')).fadeIn(400);
                }
                // error処理は共通設定を使う
            });
        },
        copySekoInfo: function () {
            var t = this;
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/houji/sekoinfocopy',
                data: {
                    dataSekoKihonJson: dataSekoKihonJson
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        appcst.appModel.set(mydata.sekodata);
                        appcst.sekyuModel.set(mydata.seikyudata);
                        appcst.kojinInfoCol.reset(mydata.dataKojinInfoCol);
                        // 画像リンク設定
                        _.each(appcst.kojinInfoCol.models, function (m) {
                            appcst.hkFile = fileUpLib.upload({m: m, attr_oid: 'hk_file_oid', attr_fnm: 'hk_file_nm', el: '#hk_file_clip_' + m.cid, imgprv: false});
                            if (m.get('hk_sex_kbn') === "2") {
                                $('#female_' + m.cid).attr("checked", "checked");
                                $('#female_' + m.cid).prev().addClass("ui-state-active");
                                $('#male_' + m.cid).removeAttr("checked", "checked");
                                $('#male_' + m.cid).prev().removeClass("ui-state-active");
                            } else if (m.get('hk_sex_kbn') === "1") {
                                $('#male_' + m.cid).attr("checked", "checked");
                                $('#male_' + m.cid).prev().addClass("ui-state-active");
                                $('#female_' + m.cid).removeAttr("checked", "checked");
                                $('#female_' + m.cid).prev().removeClass("ui-state-active");
                            } else {
                                $('#male_' + m.cid).removeAttr("checked", "checked");
                                $('#male_' + m.cid).prev().removeClass("ui-state-active");
                                $('#female_' + m.cid).removeAttr("checked", "checked");
                                $('#female_' + m.cid).prev().removeClass("ui-state-active");
                            }
                        });
                        setTimeout(function () {
                        }, 10);
//                        $.msiJqlib.showInfo(mydata.msg);
                    }
                }
            });
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            $target.datepicker("show");
        },
        // 故人住所を喪主住所にコピー
        fromKtoMCopy: function () {
            // とりあえず#1のデータを設定
            var k_yubin = null;
            var k_addr1 = null;
            var k_addr2 = null;
            var k_tel = null;
            _.each(appcst.kojinInfoCol.models, function (m) {
                if (m.get('seq_no') == "1") {
                    k_yubin = m.get('hkg_yubin_no');
                    k_addr1 = m.get('hkg_addr1');
                    k_addr2 = m.get('hkg_addr2');
                    k_tel = m.get('hkg_tel');
                }
            });
            this.model.set('mg_yubin_no', k_yubin);
            this.model.set('mg_addr1', k_addr1);
            this.model.set('mg_addr2', k_addr2);
            this.model.set('mg_tel', k_tel);
        },
        // 喪主情報を請求情報にコピー
        fromMtoSCopy: function () {
            this.model.set('s_cif_no', this.model.get('m_cif_no'));
            this.model.set('s_cif_status', this.model.get('m_cif_status'));
            appcst.sekyuModel.set('sekyu_last_nm', this.model.get('m_last_nm'));
            appcst.sekyuModel.set('sekyu_first_nm', this.model.get('m_first_nm'));
            appcst.sekyuModel.set('sekyu_nm', this.model.get('m_nm'));
            appcst.sekyuModel.set('sekyu_last_knm', this.model.get('m_last_knm'));
            appcst.sekyuModel.set('sekyu_first_knm', this.model.get('m_first_knm'));
            appcst.sekyuModel.set('sekyu_knm', this.model.get('m_knm'));
            appcst.sekyuModel.set('sekyu_yubin_no', this.model.get('mg_yubin_no'));
            appcst.sekyuModel.set('sekyu_addr1', this.model.get('mg_addr1'));
            appcst.sekyuModel.set('sekyu_addr2', this.model.get('mg_addr2'));
            appcst.sekyuModel.set('sekyu_tel', this.model.get('mg_tel'));
            appcst.sekyuModel.set('mobile_tel', this.model.get('mg_m_tel'));
            appcst.sekyuModel.set('sekyu_file_nm', this.model.get('m_file_nm'));
            appcst.sekyuModel.set('sekyu_file', this.model.get('m_file'));
            appcst.sekyuModel.set('sekyu_moshu_kankei_kbn', '1');
        },
        // 請求先情報を請求送付先情報にコピー
        fromStoSCopy: function () {
            appcst.sekyuModel.set('soufu_last_nm', appcst.sekyuModel.get('sekyu_last_nm'));
            appcst.sekyuModel.set('soufu_first_nm', appcst.sekyuModel.get('sekyu_first_nm'));
            appcst.sekyuModel.set('sekyu_soufu_nm', appcst.sekyuModel.get('sekyu_nm'));
//            appcst.sekyuModel.set('soufu_last_knm', appcst.sekyuModel.get('sekyu_last_knm'));
//            appcst.sekyuModel.set('soufu_first_knm', appcst.sekyuModel.get('sekyu_first_knm'));
//            appcst.sekyuModel.set('sekyu_soufu_knm', appcst.sekyuModel.get('sekyu_knm'));
            appcst.sekyuModel.set('soufu_yubin_no', appcst.sekyuModel.get('sekyu_yubin_no'));
            appcst.sekyuModel.set('soufu_addr1', appcst.sekyuModel.get('sekyu_addr1'));
            appcst.sekyuModel.set('soufu_addr2', appcst.sekyuModel.get('sekyu_addr2'));
            appcst.sekyuModel.set('soufu_tel', appcst.sekyuModel.get('sekyu_tel'));
            appcst.sekyuModel.set('soufu_file_nm', appcst.sekyuModel.get('sekyu_file_nm'));
            appcst.sekyuModel.set('soufu_file', appcst.sekyuModel.get('sekyu_file'));
        },
        // 請求送付先情報を領収書送付先情報にコピー
        fromStoRCopy: function () {
            appcst.sekyuModel.set('ryosyu_soufu_last_nm', appcst.sekyuModel.get('soufu_last_nm'));
            appcst.sekyuModel.set('ryosyu_soufu_first_nm', appcst.sekyuModel.get('soufu_first_nm'));
            appcst.sekyuModel.set('ryosyu_soufu_nm', appcst.sekyuModel.get('sekyu_soufu_nm'));
            if (!$.msiJqlib.isNullEx2(appcst.sekyuModel.get('sekyu_last_nm')) && !$.msiJqlib.isNullEx2(appcst.sekyuModel.get('sekyu_first_nm'))) {
                appcst.sekyuModel.set('ryosyu_meigi', appcst.sekyuModel.get('sekyu_last_nm') + appcst.sekyuModel.get('sekyu_first_nm'));
            } else if (!$.msiJqlib.isNullEx2(appcst.sekyuModel.get('sekyu_last_nm'))) {
                appcst.sekyuModel.set('ryosyu_meigi', appcst.sekyuModel.get('sekyu_last_nm'));
            } else if (!$.msiJqlib.isNullEx2(appcst.sekyuModel.get('sekyu_first_nm'))) {
                appcst.sekyuModel.set('ryosyu_meigi', appcst.sekyuModel.get('sekyu_first_nm'));
            }
//            appcst.sekyuModel.set('ryosyu_soufu_last_knm', appcst.sekyuModel.get('soufu_last_knm'));
//            appcst.sekyuModel.set('ryosyu_soufu_first_knm', appcst.sekyuModel.get('soufu_first_knm'));
//            appcst.sekyuModel.set('ryosyu_soufu_knm', appcst.sekyuModel.get('sekyu_soufu_knm'));
            appcst.sekyuModel.set('ryosyu_soufu_yubin_no', appcst.sekyuModel.get('soufu_yubin_no'));
            appcst.sekyuModel.set('ryosyu_soufu_addr1', appcst.sekyuModel.get('soufu_addr1'));
            appcst.sekyuModel.set('ryosyu_soufu_addr2', appcst.sekyuModel.get('soufu_addr2'));
            appcst.sekyuModel.set('ryosyu_soufu_tel', appcst.sekyuModel.get('soufu_tel'));
            appcst.sekyuModel.set('ryosyu_soufu_file_nm', appcst.sekyuModel.get('soufu_file_nm'));
            appcst.sekyuModel.set('ryosyu_soufu_file', appcst.sekyuModel.get('soufu_file'));
        },
        showShikijoYoyaku: function () {
            var mode = 'shikijo';
            // 施行番号がなければエラー
            var sekoNo = this.model.get('seko_no');
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                $.msiJqlib.showErr('式場予約の前に施行登録を行ってください。');
                return;
            }
            this.doSave(mode);
        },
        // 式場予約画面呼び出し処理
        exeShowShikijoYoyaku: function () {
            // 呼び出し元設定例 parms *は必須
            // p_yoyaku_bunrui:0:搬送 1:葬儀 2:法事
            //* p_seko_no: 施行番号（搬送時の受付番号）
            //* p_seko_no_sub: 搬送時の受付番号枝番
            // p_tgt_day: YYYY-MM-DD
            var t = this;
            var sekoNo = appcst.appModel.get("seko_no");
            var url = $.msiJqlib.baseUrl() + '/cale/yoyaku/cale/p_yoyaku_bunrui/2/p_seko_no/' + sekoNo;
            var refreshFunc = function () {
                // 葬儀・法事の場合
                // 予約情報を取得し再設定する
                // 予約保存時に施行日程（日程開始日、予約場所等）を更新(updateのみ)している）
                console.log("openerCalled");
                t.getCurData();
            };
            msiLib2.openWinSub(refreshFunc, url);
        },
        getCurData: function () {
            var am = appcst.appModel;
            var t = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/houji/getcurdata',
                data: {
                    seko_no: appcst.appModel.get("seko_no")
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        am.set('est_shikijo_cd', mydata.dataSekoKihon.est_shikijo_cd);
                        am.set('seko_shikijo_cd', mydata.dataSekoKihon.seko_shikijo_cd);
                        am.set('sougi_ymd', mydata.dataSekoKihon.sougi_ymd);
                        appcst.niteiCol.reset(mydata.dataNiteiCol);
                    }
                }
            });
        },
    };

    /**
     * @description 日程タブ処理
     */
    // 日程タブ明細モデル
    var NiteiModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                nitei_kbn: null, // 日程区分
                ts_based_nm: null, // 日程基本名
                ts_based_nm2: null, // 日程基本名2 
                nitei_ymd: null, // 日程タイムスタンプ
                nitei_date: null, // 日程日付のみ
                nitei_time: null, // 日程時刻のみ
                nitei_ed_ymd: null, // 日程終了
                nitei_ed_time: null, // 日程終了時刻
                spot_cd: null, // 場所区分コード
                basho_kbn: null, // 場所区分
                basho_cd: null, // 場所コード
                basho_nm: null, // 場所名
                sikijo_check: null, // 当社式場未使用チェック
                sikijo_yoyaku_no: null, // 式場予約番号
            };
        },
        validation: {
            nitei_ymd: function (val, attr, computed) {
                if (($.msiJqlib.isNullEx2(computed.nitei_date) && !$.msiJqlib.isNullEx2(computed.nitei_time))) {
                    return '日付と時刻の整合性がありません';
                }
            },
            nitei_date: function (value) {
                return $.msiJqlib.chkYmd(value);
            },
            nitei_time: {
                required: false,
                pattern: 'time'
            },
            nitei_ed_ymd: function (val, attr, computed) {
                if ($.msiJqlib.isNullEx2(computed.nitei_date) && !$.msiJqlib.isNullEx2(computed.nitei_ed_time)) {
                    return '日付と時刻の整合性がありません';
                }
            },
            nitei_ed_time: {
                required: false,
                pattern: 'time'
            },
            basho_nm: {
                required: false,
                maxLength: 40
            }
        },
        labels: {
            basho_nm: '場所名'
        }
    }); // NiteiModel

    // 日程コレクション
    var NiteiCollection = Backbone.Collection.extend({
        model: NiteiModel,
    });

    // 日程ビュー
    var NiteiView = Backbone.View.extend({
        tagName: 'fieldset',
        // 0:施行日 1:法要 2:墓参 3:会食 4:控室 5:納骨
        tmpl0: _.template($('#tmpl-nitei-0').html()),
        tmpl1: _.template($('#tmpl-nitei-1').html()),
        tmpl2: _.template($('#tmpl-nitei-2').html()),
        tmpl3: _.template($('#tmpl-nitei-3').html()),
        tmpl4: _.template($('#tmpl-nitei-4').html()),
        tmpl5: _.template($('#tmpl-nitei-5').html()),
        events: {
            "click .label.dlg_place": "nmjyushoHelper",
            "change .nitei_spot_cd": function () {
                this.model.set({'basho_cd': null, 'basho_nm': null});
            },
            "select2-opening .nitei_spot_cd": function () {
                var niteiSpotKbns = {};
                // 日程区分
                var niteiKbn = this.model.get("nitei_kbn");
                if (niteiKbn === 1) {   // 法要
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 2) {    // 墓参
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 3) {    // 法宴
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                } else if (niteiKbn === 4) { // 控室
                    niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                }
                var orgKbns = $.msiJqlib.objToArray3(niteiSpotKbns);
                var fileredKbns = [];
                var sikijo_check = this.model.get('sikijo_check');
                _.each(orgKbns, function (item) {
                    if (sikijo_check == '1') {
                        if (item.kbn_value_cd_num !== BASHO_KIND_HALL) {
                            fileredKbns.push(item);
                        }
                    } else {
                        fileredKbns.push(item);
                    }
                });
                appcst.niteiSpotKbns = fileredKbns;
            },
            "change .sikijo_check": function () {
                this.model.set({'spot_cd': null, 'basho_kbn': null, 'basho_cd': null, 'basho_nm': null, 'nitei_date':null, 'nitei_time':null, 'nitei_ed_time':null});
            },
        },
        bindings: {
            '.nitei_kbn_nm': 'nitei_kbn_nm',
            '.nitei_date': {
                observe: 'nitei_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('nitei_time'), 'nitei_ymd', this.model);
                    _setNiteiymd($el.val(), this.model.get('nitei_ed_time'), 'nitei_ed_ymd', this.model);
                    // 施行基本の葬儀日を設定
                    if (this.model.get('nitei_kbn') === 0) {
                        appcst.appModel.set('sougi_ymd', $el.val());
                        // 葬儀情報(ヘッダー)の葬儀日バインディング処理
                        $("#hd_mg_sogibi").text($el.val());
                    }
                    return $el.val();
                }

            },
            '.nitei_time': {
                observe: 'nitei_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('nitei_date'), $el.val(), 'nitei_ymd', this.model);
                    return $el.val();
                }
            },
            '.nitei_ed_time': {
                observe: 'nitei_ed_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('nitei_date'), $el.val(), 'nitei_ed_ymd', this.model);
                    return $el.val();
                }
            },
            '.nitei_spot_cd': {
                observe: 'spot_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'basho_kbn');
                    return $el.val();
                }
            },
            '.basho_nm': {
                observe: 'basho_nm',
                getVal: function ($el, event, options) {
                    var val = $el.val();
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set("basho_cd", null);
                    }
                    return val;
                }
            },
            ".sikijo_check": $.msiJqlib.getCheckBinding('sikijo_check'),
            'sikijo_yoyaku_no': 'sikijo_yoyaku_no'
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.listenTo(this.model, 'change:spot_cd', this.setPickupNmStatus);
            this.listenTo(this.model, 'change:basho_nm change:spot_cd', this.setBashoNm);
            this.listenTo(this.model, 'change:sikijo_check', this.setSpotStatus);
        },
        render: function () {
            // 場所区分データ
            var niteiSpotKbns = {};
            // 日程区分
            var niteiKbn = this.model.get("nitei_kbn");
            var sikijo_yoyaku_no = this.model.get("sikijo_yoyaku_no");
            var buttonDisabled = null;
            if (!($.msiJqlib.isNullEx2(sikijo_yoyaku_no))) {
                buttonDisabled = 'disabled';
            }
            if (niteiKbn === 0) {   // 施行日
                this.$el.html(this.tmpl0(this.model.toJSON()));
            } else if (niteiKbn === 1) {    // 法要
                this.$el.html(this.tmpl1(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                this.$('.radio_set').buttonset();
            } else if (niteiKbn === 2) {    // 墓参
                this.$el.html(this.tmpl2(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
            } else if (niteiKbn === 3) {    // 法宴
                this.$el.html(this.tmpl3(this.model.toJSON()));
                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
                this.$('.radio_set').buttonset();
            } else if (niteiKbn === 4) {    // 控室
//                this.$el.html(this.tmpl4(this.model.toJSON()));
//                niteiSpotKbns = data.dataKbns.sogi_basho_kbn;
//                this.$('.radio_set').buttonset();
            } else if (niteiKbn === 5) {    // 納骨
                this.$el.html(this.tmpl5(this.model.toJSON()));
            }

            this.$(".nitei_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".nitei_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.$(".nitei_ed_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            this.stickit();
            this.setRadioButtonStatus();
            this.setPickupNmStatus();
            this.setSpotStatus();
            // 場所区分
            appcst.niteiSpotKbns = $.msiJqlib.objToArray3(niteiSpotKbns);
            $.msiJqlib.setSelect2Com1(this.$(".nitei_spot_cd"), ($.extend({data: function () {
                    return {results: appcst.niteiSpotKbns};
                }}, $.msiJqlib.setSelect2Default1)));
            return this;
        },
        setRadioButtonStatus: function () {
            var niteiKbn = this.model.get("nitei_kbn");
            var sikijo_yoyaku_no = this.model.get("sikijo_yoyaku_no");
            var buttonDisabled = 'enable';
            if (!($.msiJqlib.isNullEx2(sikijo_yoyaku_no))) {
                buttonDisabled = 'disable';
            }
            if (niteiKbn === 1) {   // 法要
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 3) { // 法宴
                this.$('.radio_set').buttonset(buttonDisabled);
            } else if (niteiKbn === 4) { // 控室
                this.$('.radio_set').buttonset(buttonDisabled);
            }
        },
        setSpotStatus: function () {
            // 施設予約番号があればスルーする
            var niteiKbn = this.model.get("nitei_kbn");
            if (!$.msiJqlib.isNullEx2(this.model.get("sikijo_yoyaku_no"))) {
                this.$(".nitei_spot_cd").attr("disabled", "disabled");
                this.$(".nitei_date").attr("disabled", "disabled");
                this.$(".nitei_time").attr("disabled", "disabled");
                this.$(".nitei_ed_time").attr("disabled", "disabled");
                this.$(".dlg_date_common").removeClass("dlg_date");
                this.$(".dlg_time_common").removeClass("dlg_time");
                return;
            }
            if (this.model.get('sikijo_check') == '1') {
                this.$(".nitei_spot_cd").removeAttr("disabled");
                this.$(".nitei_date").removeAttr("disabled");
                this.$(".nitei_time").removeAttr("disabled");
                this.$(".nitei_ed_time").removeAttr("disabled");
                this.$(".dlg_date_common").addClass("dlg_date");
                this.$(".dlg_time_common").addClass("dlg_time");
            } else {
                if (!$.msiJqlib.isNullEx2(niteiKbn) && (niteiKbn == NITEI_BOSAN || niteiKbn == NITEI_SEKO || niteiKbn == NITEI_NOKOTU)) {
                    this.$(".nitei_spot_cd").removeAttr("disabled");
                    this.$(".nitei_date").removeAttr("disabled");
                    this.$(".nitei_time").removeAttr("disabled");
                    this.$(".nitei_ed_time").removeAttr("disabled");
                    this.$(".dlg_date_common").addClass("dlg_date");
                    this.$(".dlg_time_common").addClass("dlg_time");
                } else {
                    this.$(".nitei_spot_cd").attr("disabled", "disabled");
                    this.$(".nitei_date").attr("disabled", "disabled");
                    this.$(".nitei_time").attr("disabled", "disabled");
                    this.$(".nitei_ed_time").attr("disabled", "disabled");
                    this.$(".dlg_date_common").removeClass("dlg_date");
                    this.$(".dlg_time_common").removeClass("dlg_time");
                }
            }
        },
        // 名所住所ヘルパー処理
        nmjyushoHelper: function (e) {
            var niteiKbn = this.model.get('nitei_kbn');
            var bashoKbn = this.model.get('basho_kbn');
            if (!$.msiJqlib.isNullEx2(this.model.get("sikijo_yoyaku_no")) && niteiKbn != NITEI_SEKO) {
                this.$(".nitei_spot_cd").attr("disabled", "disabled");
                return;
            }
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var kind = $target.data("kind2");
            if ($.msiJqlib.isNullEx2(kind)) {
                return;
            }
            var actionNm = 'nmjyusho';
            var action = $target.data("action");
            if (!$.msiJqlib.isNullEx2(action)) {
                actionNm = action;
            }
            var hanso_kbn = null;
            var s_hanso_kbn = $target.data("s_hanso_kbn");
            if (!$.msiJqlib.isNullEx2(s_hanso_kbn)) {
                hanso_kbn = s_hanso_kbn;
            }
            var kaijyo_kbn = null;
            if (bashoKbn == BASHO_KIND_HALL) {
                kaijyo_kbn = KAIJYO_KBN_JISYA;
            } else if (bashoKbn == BASHO_KIND_OTHER) {
                kaijyo_kbn = KAIJYO_KBN_TAEI;
            }
            var kaijyo_type = null;
            if (niteiKbn == NITEI_HIKAE) {
                kaijyo_type = KAIJYO_TYPE_HIKAE;
            }
            var m = this.model;

            var bumon_cd = $('#hall_cd').val();

            this.$el.msiPickHelper({
                action: actionNm,
                kind2: kind,
                mydata: {s_hanso_kbn: hanso_kbn, s_kaijyo_kbn: kaijyo_kbn, s_type_kbn: kaijyo_type},
                onSelect: function (data) {
                    m.set("basho_cd", data.code);
                    m.set("basho_nm", data.name);
                },
                onClear: function () {
                    m.set("basho_cd", null);
                    m.set("basho_nm", null);
                }
            });
        },
        // 場所区分切り替え処理
        setPickupNmStatus: function () {
            // 日程区分 0:施行日 1:法要 2:墓参 3:会食 4:控室 5:納骨
            var niteiKbn = this.model.get("nitei_kbn");
            if ($.inArray(niteiKbn, [1, 2, 3, 4, 5]) >= 0) {
                // 場所区分
                var bashoKbn = this.model.get('basho_kbn');

                if (((niteiKbn === 1 || niteiKbn === 2 || niteiKbn === 3 || niteiKbn === 4) && (bashoKbn == BASHO_KIND_HALL || bashoKbn == BASHO_KIND_OTHER))) {
                    this.$(".place").data("action", 'kaijyo');
                } else {
                    this.$(".place").data("action", null);
                }
                // pickname入力可能区分
                var disable = false;
                _setSpotStatus(niteiKbn, bashoKbn, this.$(".place"), this.$(".dlg_place"), disable);
                if (bashoKbn === '0' || bashoKbn === '9' || disable) { // 0:自宅と9:その他はpickup無し
                    bashoKbn = null;
                }
                this.$(".place").data("kind2", bashoKbn);
            }
        },
        setBashoNm: function () {
            var t = this;
            var cm = this.model;
            var cur_nitei_kbn = cm.get("nitei_kbn");
            if ($.inArray(cur_nitei_kbn, [1, 2, 3, 4, 5]) >= 0) { // 1:法要 2:墓参 3:法宴 4:控室 5:納骨
                _.each(appcst.niteiCol.models, function (m) {
                    var niteiKbn = m.get("nitei_kbn");
                    if ($.inArray(niteiKbn, [1, 2, 3, 4, 5]) >= 0 && cur_nitei_kbn !== niteiKbn) { // 1:法要 2:墓参 3:法宴 4:控室 5:納骨
                        var orgNitei = t.getOrgNitei(niteiKbn);
                    }
                });
            }
        },
        getOrgNitei: function (kbn) {
            var ret = null;
            _.each(orgDataNiteiCol, function (o) {
                if (o.nitei_kbn === kbn) {
                    ret = o;
                }
            });
            return ret;
        }
    }); // NiteiView

    // 受付情報タブ 故人情報モデル
    var KojinInfoModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, //　施行番号
                seq_no: null, //　連番
                sekohoyo_kbn: null, // 施行法要区分
                hk_last_nm: null, // 故人苗字
                hk_first_nm: null, // 故人名前
                hk_last_knm: null, // 故人苗字カナ
                hk_first_knm: null, // 故人名前カナ
                hk_kaimyo: null, // 戒名
                hk_death_ymd: null, // 死亡日
                hk_cif_no: null, // CIFNo
                hk_sex_kbn: "1", // 性別区分
                hk_gengo: null, // 元号
                hk_wa_year: null, // 生年月日(元号年)
                hk_birth_year: null, // 誕生年
                hk_birth_month: null, // 誕生月
                hk_birth_day: null, // 誕生日
                hk_seinengappi_ymd: null, // 生年月日
                hk_seinengappi_ymd_y: null, // 生年月日(西暦)
                hk_nenrei_man: null, // 満年齢
                hk_file_oid: null, // ファイル
                hk_file_nm: null, // ファイル名
                hkg_yubin_no: null, // 郵便番号
                hkg_addr1: null, // 住所1
                hkg_addr2: null, // 住所2
                hkg_tel: null, // TEL
            };
        },
        validation: {
            hk_nm: {
                required: false,
                maxLength: 60
            },
            hk_first_nm: {
                required: function (val, attr, computed) {
                    // 1行名必須
//                    if (computed.seq_no == '1') {
//                        return true;
//                    } else {
                        return false;
//                    }
                },
                maxLength: 20
            },
            hk_last_nm: {
                required: function (val, attr, computed) {
                    // 1行名必須
//                    if (computed.seq_no == '1') {
//                        return true;
//                    } else {
                        return false;
//                    }
                },
                maxLength: 20
            },
            hk_knm: {
                required: false,
                maxLength: 60
            },
            hk_first_knm: {
                required: function (val, attr, computed) {
                    // 1行名必須
//                    if (computed.seq_no == '1') {
//                        return true;
//                    } else {
                        return false;
//                    }
                },
                maxLength: 20
            },
            hk_last_knm: {
                required: function (val, attr, computed) {
                    // 1行名必須
//                    if (computed.seq_no == '1') {
//                        return true;
//                    } else {
                        return false;
//                    }
                },
                maxLength: 20
            },
            hk_seinengappi_ymd: "validateSeinengappi",
            hk_nenrei_man: {
                required: false,
                pattern: 'number'
            },
            hk_death_ymd: function (value) {
                return _chkYmd(value);
            },
            hkg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
//            hkg_tel: {
//                required: false,
//                pattern: 'tel'
//            },
            sekohoyo_kbn: {
                required: function (val, attr, computed) {
                    // 1行名必須
//                    if (computed.seq_no == '1') {
//                        return true;
//                    } else {
                        return false;
//                    }
                },
            },
        },
        labels: {
            hk_nm: '故人名',
            hk_last_nm: '故人苗字',
            hk_first_nm: '故人名前',
            hk_knm: '故人名カナ',
            hk_last_knm: '故人苗字カナ',
            hk_first_knm: '故人名前カナ',
            hk_seinengappi_ymd: '故人生年月日',
            hk_nenrei_man: '故人年齢',
            hk_death_ymd: '亡日',
            hkg_yubin_no: '故人郵便番号',
            hkg_tel: '故人TEL',
            sekohoyo_kbn: '故人施行法要区分',
        },
        validateSeinengappi: function (value, attr, computedState) {
            var gengo = computedState.hk_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return _validateSeinengappi(gengo, value);
            }
        },
    }); // KojinInfoModel

    // 故人情報コレクション
    var KojinInfoCollection = Backbone.Collection.extend({
        model: KojinInfoModel
    });

    // 故人情報ビュー
    var KojinInfoView = Backbone.View.extend({
        tagName: 'tbody',
        tmpl: _.template($('#tmpl-kojin_info').html()),
        events: {
            "click .label.dlg_hk_zip": "zipHelper",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
            "click .male": function () {
                // 性別（男）設定
                this.model.set("hk_sex_kbn", "1");
            },
            "click .female": function () {
                // 性別（女）設定
                this.model.set("hk_sex_kbn", "2");
            },
            "select2-open .birthday_era": function () {
                var era = this.model.get('hk_birth_year');
                if (!$.msiJqlib.isNullEx2(era)) {
                    return;
                }
                var choices = $('.select2-results').children('li');
                var index = choices.length - 70;
                var choice = $(choices[index]);
                choices.filter(".select2-highlighted").removeClass("select2-highlighted");
                choice = $(choices[index]);
                choice.addClass("select2-highlighted");
                // 自動スクロールさせる
                $('.select2-results').scrollTop(0 + (index * 25));

            },
            "click .btn_hk_mk_copy": "fromMtoKCopy",
        },
        bindings: {
            '.seq_no': 'seq_no',
            '.sekohoyo_kbn': {
                observe: 'sekohoyo_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '.hk_death_ymd': {
                observe: 'hk_death_ymd',
            },
            '.hk_last_nm': 'hk_last_nm',
            '.hk_first_nm': 'hk_first_nm',
            '.hk_last_knm': 'hk_last_knm',
            '.hk_first_knm': 'hk_first_knm',
            '.hk_kaimyo': 'hk_kaimyo',
            '.hk_cif_no': 'hk_cif_no',
            '.birthday_era': {
                observe: 'hk_birth_year',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnSnmVal($el, this.model, 'hk_wa_year');
                    return $el.val();
                }
            },
            '.birthday_month': {
                observe: 'hk_birth_month',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '.birthday_day': {
                observe: 'hk_birth_day',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '.hk_age': 'hk_nenrei_man',
            '.hk_sex_kbn': $.msiJqlib.getCheckBinding('hk_sex_kbn'),
            '.hkg_yubin_no': 'hkg_yubin_no',
            '.hkg_addr1': 'hkg_addr1',
            '.hkg_addr2': 'hkg_addr2',
            '.hkg_tel': 'hkg_tel',
        },
        initialize: function () {
            Backbone.Validation.bind(this);
            this.listenTo(this.model, 'change:hk_death_ymd change:hk_birth_year change:hk_birth_month change:hk_birth_day', this.calcNereiK);
            this.render();
        },
        render: function () {
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.$el.html(this.tmpl({idx: this.model.cid}));
            this.$('.radio_set').buttonset();
            this.stickit();
            this.$(".hk_death_ymd").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            // select2
            // 施行法要
            $.msiJqlib.setSelect2Com1(this.$(".sekohoyo_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.hoyo_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$(".birthday_era"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.gengo)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$(".birthday_month"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.month)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$(".birthday_day"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.day)}, $.msiJqlib.setSelect2Default1)));
            // かな自動入力ペア key:名称要素 value[カナ要素, カナモデルプロパティ, モデル]
            var _name_kana_pair = {
                ".hk_last_nm": [".hk_last_knm", 'hk_last_knm', this.model],
                ".hk_first_nm": [".hk_first_knm", 'hk_first_knm', this.model]
            };
            // カナ自動入力設定
            $.msiJqlib.setAutoKanaModelCol(_name_kana_pair, this);
            // 郵便番号自動入力ペア key:名称要素 value[カナ要素, カナモデルプロパティ, モデル]
            var _zip_addr_pair = {
                '.hkg_yubin_no': ['hkg_yubin_no', 'hkg_addr1', this.model]
            };

            // 郵便番号による住所1自動入力設定
            $.msiJqlib.setAutoZipToAddrModel(_zip_addr_pair, this);
            return this;
        },
        // 喪主情報を故人情報にコピー
        fromMtoKCopy: function () {
            this.model.set('hkg_yubin_no', appcst.appModel.get('mg_yubin_no'));
            this.model.set('hkg_addr1', appcst.appModel.get('mg_addr1'));
            this.model.set('hkg_addr2', appcst.appModel.get('mg_addr2'));
            this.model.set('hkg_tel', appcst.appModel.get('mg_tel'));
        },
        zipHelper: function (e) {
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'zipno',
                onSelect: function (data) {
                    m.set('hkg_yubin_no', data.code);
                    m.set('hkg_addr1', data.name);
                },
                onClear: function () {
                    m.set('hkg_yubin_no', null);
                    m.set('hkg_addr1', null);
                }
            });
        },
        // 故人年齢計算処理
        calcNereiK: function () {
            var era = this.model.get('hk_wa_year');
            var seireki = this.model.get('hk_birth_year');
            var month = this.model.get('hk_birth_month');
            var day = this.model.get('hk_birth_day');
            if ($.msiJqlib.isNullEx2(era) || $.msiJqlib.isNullEx2(month) || $.msiJqlib.isNullEx2(day)) {
                this.model.set("hk_gengo", null);
                this.model.set("hk_nenrei_man", null);
                this.model.set("hk_seinengappi_ymd", null);
                this.model.set("hk_seinengappi_ymd_y", null);
                return;
            }
            var gengo = era.slice(0, 1);
            var wareki_y = era.slice(1);
            var seinengappi = seireki + '/' + month + '/' + day;
            if (!$.msiJqlib.chkDate(seinengappi)) { // 日付チェック
                seinengappi = null;
            }
            this.model.set('hk_gengo', gengo);
            this.model.set('hk_seinengappi_ymd', wareki_y + '/' + month + '/' + day);
            this.model.set('hk_seinengappi_ymd_y', seinengappi);

            if (seinengappi) {
                var nakunariymd = this.model.get('hk_death_ymd');
                if (!$.msiJqlib.chkDate(nakunariymd)) { // 日付チェック
                    nakunariymd = null;
                    this.model.set("hk_gengo", null);
                    this.model.set("hk_nenrei_man", null);
                    this.model.set("hk_seinengappi_ymd", null);
                    this.model.set("hk_seinengappi_ymd_y", null);
                    return;
                }
                var pram = {
                    nakunariymd: nakunariymd,
                    seinengappi: seinengappi,
                    setData: function (data) { // コールバック
                        this.model.set("hk_nenrei_man", data.man);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("hk_nenrei_man", null);
            }
        },
        // 年齢計算処理
        calcNerei: function (pram) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/calcnenrei',
                data: {
                    nakunariymd: pram.nakunariymd,
                    seinengappi: pram.seinengappi
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        if (_.has(pram, "setData")) {
                            pram.setData.apply(that, [mydata]);
                        }
                    }
                }
            });
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            $target.datepicker("show");
        },
    }); // KojinInfoView
    //

    // 施行基本フリーモデル
    var KihonFreeModel = Backbone.Model.extend({
        defaults: function () {
            return {
                seko_no: null, // 施行番号
                ts_free1: null, // 受付日
                ts_free1_date: null, // 受付日(日付のみ)
                ts_free1_time: null, // 受付日(時間のみ)
            };
        },
        validation: {
            ts_free1_date: {
                required: true,
                customFun: function (value) {
                    return _chkYmd(value);
                }
            },
            ts_free1_time: {
                required: false,
                pattern: 'time'
            },
        },
        labels: {
            ts_free1: '受付日',
            ts_free1_date: '受付日',
            ts_free1_time: '受付時間',
        }
    });

    // 施行基本フリービュー
    var KihonFreeView = Backbone.View.extend({
        el: $("#detail"),
        events: {
        },
        bindings: {
            '#uketuke_date': {
                observe: 'ts_free1_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('ts_free1_time'), 'ts_free1', this.model);
                    return $el.val();
                }
            },
            '#uketuke_time': {
                observe: 'ts_free1_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('ts_free1_date'), $el.val(), 'ts_free1', this.model);
                    return $el.val();
                }
            },
        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.render();
        },
        render: function () {
            this.stickit();
            this.$("#uketuke_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#uketuke_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            return this;
        },
    });

    // 全体ビュー
    var AppView = Backbone.View.extend($.extend(true, AppViewDef, $.customerFileup));
    // データ取得
    var data = msiLib2.getJsonFromHtml($('#data-json'));
    appcst.data = data;
    var codeKbns = data.codeKbns;
    var sidemenukey = data.sidemenukey;
    var orgKeiyakuSbt = data.keiyakuCif;
    var showFlg = true;
    if ($.msiJqlib.isNullEx2(data.dataSekoKihon.seko_no)) {
        showFlg = false;
    }
    // コントローラー名
    appcst.controllerName = data.controllerName;
    // 日程タブ初期化
    appcst.niteiCol = new NiteiCollection();
    appcst.kojinInfoCol = new KojinInfoCollection();
    // 互助会タブ初期化
    appcst.gojokaiInfoModel = new appgjk.GojokaiInfoModel();
    appcst.sekoKeiyakusakiInfoModel = new appgjk.SekoKeiyakusakiInfoModel();
    appcst.gojokaiMemberCol = new appgjk.GojokaiMemberCollection();
    appcst.gojokaiInfoView = new appgjk.GojokaiInfoView({model: appcst.gojokaiInfoModel});
    appcst.sekoKeiyakusakiInfoView = new appgjk.SekoKeiyakusakiInfoView({model: appcst.sekoKeiyakusakiInfoModel});
    // 喪主タブ請求先初期化処理
    appcst.sekyuModel = new appsk.SekyuModel();
    appcst.sekyuView = new appsk.SekyuView({model: appcst.sekyuModel});
    // APP初期化処理
    appcst.kfModel = new KihonFreeModel();
    appcst.kfView = new KihonFreeView({model: appcst.kfModel});
    appcst.appModel = new AppModel();
    appcst.appView = new AppView({model: appcst.appModel});
    //
    var orgDataApp, orgDataNiteiCol, orgDataSekyuInfo, orgDataGojokaiInfo, orgDataSekoKeiyakusakiInfo, orgDataGojokaiMemberCol, orgDataKihonFree, orgDataKojinInfoCol;
    var _resetData = function (dataSekoKihon, dataNiteiCol, dataSekyuInfo, dataGojokaiInfo, dataGojokaiMemberCol, dataKihonFree, dataKojinInfoCol, dataSekoKeiyakusakiInfo) {
        // モデルのデータを設定
        appcst.kfView.model.set(dataKihonFree);
        appcst.appView.model.set(dataSekoKihon);
        appcst.sekyuView.model.set(dataSekyuInfo);
        appcst.gojokaiInfoView.model.set(dataGojokaiInfo);
        appcst.sekoKeiyakusakiInfoView.model.set(dataSekoKeiyakusakiInfo);
        appcst.gojokaiMemberCol.reset(dataGojokaiMemberCol);
        appcst.niteiCol.reset(dataNiteiCol);
        appcst.kojinInfoCol.reset(dataKojinInfoCol);

        if (appcst.controllerName === "juchuhenkoh") {
            $("#h_btn_new").hide();
        }
        // 基本・喪主・その他タブ 
        if (dataSekoKihon) {
            $("#hall_cd").removeAttr("disabled");
            if (!$.msiJqlib.isNullEx2(dataSekoKihon.jichu_kakute_ymd)) {
                $("#hall_cd").attr("disabled", "disabled");
                $("#est_shikijo_cd").attr("disabled", "disabled");
            }
            if (dataSekoKihon.m_sex_kbn === "2") {
                $('#m_female').attr("checked", "checked");
                $('#m_female').prev().addClass("ui-state-active");
                $('#m_male').removeAttr("checked", "checked");
                $('#m_male').prev().removeClass("ui-state-active");
            } else if (dataSekoKihon.m_sex_kbn === "1") {
                $('#m_male').attr("checked", "checked");
                $('#m_male').prev().addClass("ui-state-active");
                $('#m_female').removeAttr("checked", "checked");
                $('#m_female').prev().removeClass("ui-state-active");
            } else {
                $('#m_male').removeAttr("checked", "checked");
                $('#m_male').prev().removeClass("ui-state-active");
                $('#m_female').removeAttr("checked", "checked");
                $('#m_female').prev().removeClass("ui-state-active");
            }
            // 故人名画像ファイルリンク設定
            appcst.appView.setImgLink("#input-tab", dataSekoKihon.k_file_nm, null);
            // 喪主名画像ファイルリンク設定
            appcst.appView.setImgLink("#infochief-tab #chief", dataSekoKihon.m_file_nm, null);
        }
        if (dataSekyuInfo) {
            // 請求先画像ファイルリンク設定
            appcst.appView.setImgLink("#infochief-tab #bill_sekyu", dataSekyuInfo.sekyu_file_nm, null);
            appcst.appView.setImgLink("#infochief-tab #bill_soufu", dataSekyuInfo.soufu_file_nm, null);
            appcst.appView.setImgLink("#infochief-tab #bill_ryosyu_soufu", dataSekyuInfo.ryosyu_soufu_file_nm, null);
        }
        // 互助会タブ
        if (dataGojokaiInfo) {
            // 施行履歴を設定
            if (dataGojokaiInfo.rireki_kbn === "1") {
                $('#experienced').click();
            } else {
                $('#inexperienced').click();
            }
            // 事前相談有無を設定
            if (dataGojokaiInfo.sodan_kbn === "1") {
                $('#adviced').click();
            } else {
                $('#unadviced').click();
            }
        }
        // 施行金額確定されていたら特定項目を編集不可にする
        if (dataSekoKihon.status_kbn >= STATUS_SEKO_KAKUTEI) {
            // 部門
            $('#hall_cd').attr('disabled', 'disabled');
            // 施行担当者
            $('.dlg_staff2').attr('disabled', 'disabled');
            // 施行情報タブ
            var $form = $('#infodate-tab');
            $form.msiInputReadonly()
                    .msiCalReadonly()
                    .find('.my-readonly-hidden').hide();
            // 会員情報タブ
            var $form = $('#member_1');
            $form.msiInputReadonly()
                    .msiCalReadonly()
                    .find('.my-readonly-hidden').hide();
            var $form = $('#member_2');
            $form.msiInputReadonly()
                    .msiCalReadonly()
                    .find('.my-readonly-hidden').hide();
            $('#btn_gojokai_search').attr('disabled', 'disabled')
            // 請求承認されていたら変更不可
            if (dataSekoKihon.status_kbn > STATUS_SEKO_KAKUTEI) {
                $('#seko_check_kbn').attr('disabled', 'disabled');
                $('#seko_clear').hide();
            }
        }
        // 部門コードの初期値セット
        if ($.msiJqlib.isNullEx2(appcst.appModel.get('bumon_cd'))) {
            var bumonCd = $('#hall_cd').val();
            appcst.appModel.set('bumon_cd', bumonCd);
            msiLib2.setHallCd(bumonCd);
        }
        // 画像リンク設定
        _.each(appcst.kojinInfoCol.models, function (m) {
            appcst.hkFile = fileUpLib.upload({m: m, attr_oid: 'hk_file_oid', attr_fnm: 'hk_file_nm', el: '#hk_file_clip_' + m.cid, imgprv: false});
            if (m.get('hk_sex_kbn') === "2") {
                $('#female_' + m.cid).attr("checked", "checked");
                $('#female_' + m.cid).prev().addClass("ui-state-active");
                $('#male_' + m.cid).removeAttr("checked", "checked");
                $('#male_' + m.cid).prev().removeClass("ui-state-active");
            } else if (m.get('hk_sex_kbn') === "1") {
                $('#male_' + m.cid).attr("checked", "checked");
                $('#male_' + m.cid).prev().addClass("ui-state-active");
                $('#female_' + m.cid).removeAttr("checked", "checked");
                $('#female_' + m.cid).prev().removeClass("ui-state-active");
            } else {
                $('#male_' + m.cid).removeAttr("checked", "checked");
                $('#male_' + m.cid).prev().removeClass("ui-state-active");
                $('#female_' + m.cid).removeAttr("checked", "checked");
                $('#female_' + m.cid).prev().removeClass("ui-state-active");
            }
        });
        // データを退避する
        orgDataApp = appcst.appModel.toJSON();
        orgDataKihonFree = appcst.kfModel.toJSON();
        orgDataNiteiCol = appcst.niteiCol.toJSON();
        orgDataSekyuInfo = appcst.sekyuModel.toJSON();
        orgDataGojokaiInfo = appcst.gojokaiInfoModel.toJSON();
        orgDataSekoKeiyakusakiInfo = appcst.sekoKeiyakusakiInfoModel.toJSON();
        orgDataGojokaiMemberCol = appcst.gojokaiMemberCol.toJSON();
        orgDataKojinInfoCol = appcst.kojinInfoCol.toJSON();

    };
    _resetData(data.dataSekoKihon, data.dataNiteiCol, data.dataSekyuInfo, data.dataGojokaiInfo, data.dataGojokaiMemberCol, data.dataSekoKihonFree, data.dataKojinInfoCol, data.dataSekoKeiyakusakiInfo);
    // かな自動入力ペア key:名称要素 value[カナ要素, カナモデルプロパティ, モデル]
    var _name_kana_pair = {
        '#m_last_nm': ['#m_last_knm', 'm_last_knm', appcst.appModel]
        , '#m_first_nm': ['#m_first_knm', 'm_first_knm', appcst.appModel]
        , '#sekyu_last_nm': ['#sekyu_last_knm', 'sekyu_last_knm', appcst.sekyuModel]
        , '#sekyu_first_nm': ['#sekyu_first_knm', 'sekyu_first_knm', appcst.sekyuModel]
        , '#soufu_last_nm': ['#soufu_last_knm', 'soufu_last_knm', appcst.sekyuModel]
        , '#soufu_first_nm': ['#soufu_first_knm', 'soufu_first_knm', appcst.sekyuModel]
        , '#ryosyu_soufu_last_nm': ['#ryosyu_soufu_last_knm', 'ryosyu_soufu_last_knm', appcst.sekyuModel]
        , '#ryosyu_soufu_first_nm': ['#ryosyu_soufu_first_knm', 'ryosyu_soufu_first_knm', appcst.sekyuModel]
        , '#fc_last_nm': ['#fc_last_knm', 'fc_last_knm', appcst.appModel]
        , '#fc_first_nm': ['#fc_first_knm', 'fc_first_knm', appcst.appModel]
        , '#family_name': ['#family_name_kana', 'souke_knm', appcst.appModel]
    };
    // カナ自動入力設定
    $.msiJqlib.setAutoKanaModel(_name_kana_pair);

    var _zip_addr_pair = {
        '#infochief-tab #zip_1': ['mg_yubin_no', 'mg_addr1', appcst.appModel]
        , '#infochief-tab #zip_4': ['sekyu_yubin_no', 'sekyu_addr1', appcst.sekyuModel]
        , '#infochief-tab #zip_5': ['soufu_yubin_no', 'soufu_addr1', appcst.sekyuModel]
        , '#infochief-tab #zip_6': ['ryosyu_soufu_yubin_no', 'ryosyu_soufu_addr1', appcst.sekyuModel]
        , '#input-tab #temple_yubin_no': ['temple_yubin_no', 'temple_addr1', appcst.appModel]
    };

    // 郵便番号による住所1自動入力設定
    $.msiJqlib.setAutoZipToAddrModel(_zip_addr_pair);
    $("#btn_print").hide(); // 印刷ボタンを隠す
    $("#customer-div-wrapper").show();

    // ヘッダー共通処理
    // 新規作成ボタン押下
    $("#header #btn_new").click(function () {
        location.href = $.msiJqlib.baseUrl() + '/juchu/' + appcst.data.controllerName + '/new';
    });
    // ページ遷移前の確認
    $(window).on('beforeunload', function () {
        if (_isChanged()) {
            return "保存されていないデータがあります.";
        }
    });
    var _isChanged = function () {
        var changed = true;
        // 施行基本イコール
        var sekoKihonEq = $.msiJqlib.isEqual(appcst.appModel.toJSON(), orgDataApp);
        // 施行基本汎用フリー情報イコール
        var sekoKihonFreeEq = $.msiJqlib.isEqual(appcst.kfModel.toJSON(), orgDataKihonFree);
        // 施行日程イコール
        var sekoNiteiEq = $.msiJqlib.isEqual(appcst.niteiCol.toJSON(), orgDataNiteiCol);
        // 請求先情報イコール
        var sekyuInfoEq = $.msiJqlib.isEqual(appcst.sekyuModel.toJSON(), orgDataSekyuInfo);
        // 施行互助会情報イコール
        var sekoGojokaiInfoEq = $.msiJqlib.isEqual(appcst.gojokaiInfoModel.toJSON(), orgDataGojokaiInfo);
        // 施行契約先情報イコール
        var sekoKeiyakusakiInfoEq = $.msiJqlib.isEqual(appcst.sekoKeiyakusakiInfoModel.toJSON(), orgDataSekoKeiyakusakiInfo);
        // 施行互助会加入者イコール
        var sekoGojokaiMemberEq = $.msiJqlib.isEqual(appcst.gojokaiMemberCol.toJSON(), orgDataGojokaiMemberCol);
        // 施行故人情報イコール
        var sekoKojinInfoEq = $.msiJqlib.isEqual(appcst.kojinInfoCol.toJSON(), orgDataKojinInfoCol);

        if (sekoKihonEq && sekoNiteiEq && sekyuInfoEq && sekoGojokaiInfoEq && sekoGojokaiMemberEq && sekoKeiyakusakiInfoEq && sekoKihonFreeEq && sekoKojinInfoEq) {
            changed = false;
        }
        return changed;
    };
});
