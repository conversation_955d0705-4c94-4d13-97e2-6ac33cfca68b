<?php /* Smarty version 2.6.28, created on 2025-06-05 11:48:27
         compiled from fdn_head_std.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'fdn_head_std.tpl', 7, false),array('function', 'msi_fake_import_css2', 'fdn_head_std.tpl', 16, false),array('function', 'msi_fake_import_js', 'fdn_head_std.tpl', 42, false),)), $this); ?>
<!DOCTYPE html>
<html lang="ja">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<?php echo $this->_tpl_vars['hd_pre_added']; ?>

<link href="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
css/jquery-ui-1.10.3.custom.css" rel="stylesheet" type="text/css" />
<link href="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
css/jquery-ui-patch.css" rel="stylesheet" type="text/css" />
<!-- link href="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
css/select2.css?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
" rel="stylesheet" type="text/css" / -->
<link href="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
css/lib_jq_new.css?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
" rel="stylesheet" type="text/css" />
<link href="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
css/colorbox.css?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
" rel="stylesheet" type="text/css" />
<?php echo smarty_function_msi_fake_import_css2(array('base' => ($this->_tpl_vars['app_base']),'file' => "main_pub.css",'trail' => "?".($this->_tpl_vars['app_version'])), $this);?>

<?php echo smarty_function_msi_fake_import_css2(array('base' => ($this->_tpl_vars['app_base']),'file' => "mystyles_new.css",'trail' => "?".($this->_tpl_vars['app_version'])), $this);?>

<?php echo $this->_tpl_vars['css_added']; ?>


<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/vendor/modernizr.js"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/msiGlobal.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<?php if (((is_array($_tmp=$this->_tpl_vars['js_require_js_ctxt'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/require.config.js"></script>
<?php else: ?>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/vendor/jquery-1.10.2.js"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/vendor/jquery-ui-1.10.3.js"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/vendor/lib_bb.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/vendor/lib_cmn.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/vendor/spin.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/vendor/lib_jq.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/vendor/css_browser_selector_dev.js"></script>       <!-- 2015/07/16 ADD Kayo -->
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/mylib/jqlib.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/mylib/msiLib2.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/mylib/jquery.autoKana.msi.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/mylib/bb.utils.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/mylib/bb.validation.js?<?php echo ((is_array($_tmp=$this->_tpl_vars['app_version'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"></script>
<?php echo smarty_function_msi_fake_import_js(array('base' => ($this->_tpl_vars['app_base']),'file' => "main.js",'trail' => "?".($this->_tpl_vars['app_version'])), $this);?>

<?php if (((is_array($_tmp=$this->_tpl_vars['is_lock_seko_enabled'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<?php echo smarty_function_msi_fake_import_js(array('base' => ($this->_tpl_vars['app_base']),'file' => "app/lib.lock_seko_deco.js",'trail' => "?".($this->_tpl_vars['app_version'])), $this);?>

<script type="text/javascript">
msiGlobalObj.lock_seko_no_expire_sec=<?php echo ((is_array($_tmp=$this->_tpl_vars['lock_seko_no_expire_sec'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
;
</script>
<?php endif; ?>
<?php endif; ?><?php if (((is_array($_tmp=$this->_tpl_vars['js_require_js_trans_ctxt'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
js/require.config.js"></script>
<?php endif; ?>
<?php echo $this->_tpl_vars['js_added']; ?>

<?php if (((is_array($_tmp=$this->_tpl_vars['isReadOnlyCtxt'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<script type="text/javascript">
msiGlobalObj.isReadonly=true;
</script>
<?php endif; ?>

<?php echo $this->_tpl_vars['gaijiFontSetting']; ?>

<?php echo $this->_tpl_vars['keigenZeiSetting']; ?>


<?php $this->assign('is_login_ot_user', App_OtUserUtils::isLoginOtUser()); ?><?php if (((is_array($_tmp=$this->_tpl_vars['is_login'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')) && ! ((is_array($_tmp=$this->_tpl_vars['is_login_ot_user'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html'))): ?>
<script src="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
default/etc/initmstrglobaldl/v/<?php echo Mref_Jsonlist_Gengo::getVer(); ?>"></script>
<?php endif; ?>

<title><?php echo ((is_array($_tmp=$this->_tpl_vars['app_title'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</title>
<?php echo $this->_tpl_vars['head_added']; ?>


</head>