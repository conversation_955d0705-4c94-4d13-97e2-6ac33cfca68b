<?php /* Smarty version 2.6.28, created on 2025-06-05 11:48:27
         compiled from default/index/indexsys.tpl */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'default/index/indexsys.tpl', 8, false),)), $this); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "fdn_head_std.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "fdn_header_0.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

	<div id="top">
		<div id="contents">

          <?php $_from = $this->_tpl_vars['menuTable']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }$this->_foreach['loop'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['loop']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['myrec']):
        $this->_foreach['loop']['iteration']++;
?>
          <div id="menu_pos_<?php echo ((is_array($_tmp=$this->_foreach['loop']['iteration'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
" class="menu <?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['attr'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
 <?php if (((is_array($_tmp=$this->_tpl_vars['myrec']['count'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')) > 0): ?>enable<?php endif; ?>">
            <div class="subtitle"><?php echo ((is_array($_tmp=$this->_tpl_vars['myrec']['title'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</div>
            <ul>
              <?php $_from = $this->_tpl_vars['myrec']['items']; if (!is_array($_from) && !is_object($_from)) { settype($_from, 'array'); }$this->_foreach['loop2'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['loop2']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['mysub']):
        $this->_foreach['loop2']['iteration']++;
?>
	          <li><a href="<?php echo ((is_array($_tmp=$this->_tpl_vars['app_base'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
<?php echo ((is_array($_tmp=$this->_tpl_vars['mysub']['url'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['mysub']['cap'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')); ?>
</a></li>
              <?php endforeach; endif; unset($_from); ?>
            </ul>
          </div>
          <?php endforeach; endif; unset($_from); ?>

		  <div id="calendar">
		  </div>

		  <div id="news">
				<a href="#" id="news_prev">前へ</a>
				<a href="#" id="news_next">次へ</a>
<?php echo $this->_tpl_vars['html_sys_info']; ?>

		  </div>
		</div>
	</div>

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "fdn_footer_std.tpl", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>