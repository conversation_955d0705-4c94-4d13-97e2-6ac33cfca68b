var appcst = appcst || {};
var appcif = appcif || {};// CIF検索ダイアログ
var appgjk = appgjk || {}; // 互助会ライブラリ
var appjh = appjh || {};// 受注内容変更ライブラリ
$(function () {
    "use strict";
    /** 申込区分: 5=>生前依頼 */
    var MOUSHI_KBN_SEIZEN = '5';
    /** 有無区分: 0=>なし */
    var UMU_KBN_NO = '0';
    /** 紹介項目区分: 99=>諸口 */
    var SHOKAI_KBN_OTHER = '99';
    var YOTO_COURSE = '1';
    var YOTO_PLAN = '2';
    /** 会員区分=>互助会 */
    var KAIIN_KBN_GOJO = '100';


    // 画面クラスとモデルのプロパティのオブジェクト
    appcst.pro = {
        // ヘッダー部
        bumon_cd: '#hall_cd', // 売上部門コード
        moushi_cd: '#apply_type', // 申込区分
        sougi_cd: '#funeral_type', // 葬儀区分
        daicho_no_eria: '#code_1', // 台帳番号1
        daicho_no_mm: '#code_2', // 台帳番号2
        daicho_no_seq: '#code_3', // 台帳番号3
        p_info_cd: '#personal_info', // 個人情報保護区分
        kaiin_cd: '#member', // 会員区分
        mitsu_tanto_cd: '#mitsu_tanto', // 見積担当者コード
        mitsu_tanto_nm: '#mitsu_tanto', // 見積担当者名
        seko_tanto_nm: '#staff_2', // 施行担当者コード
        contact_kbn: '#contact_kbn', // 施行担当者コード
        // 受付情報タブ
        ts_free1_date: '#uketuke_date', // 受付日
        ts_free1_time: '#uketuke_time', // 受付時間
        k_haigu_cd: '#input-tab #spouse', // 配偶者コード
        kg_yubin_no: '#input-tab #zip_1', // 故人郵便番号
        kg_addr1: '#input-tab #address_1_1', // 故人住所1
        kg_addr2: '#input-tab #address_1_2', // 故人住所2
        kg_tel: '#input-tab #tel_1', // 故人TEL
        kk_kinmusaki_kbn: '#input-tab #employee', // 故人勤務先区分
        kk_kinmusaki_nm: '#input-tab #company', // 故人勤務先名
        kk_tel: '#input-tab #company_tel', // 故人勤務先TEL
        kk_yakusyoku_nm: '#input-tab #position', // 故人役職名
        kk_fax: '#input-tab #company_fax', // 故人勤務先FAX
        k_nenrei_man: '#input-tab #age', // 故人年齢
        k_last_knm: '#k_last_knm', 
        k_first_knm: '#k_first_knm', 
        // 喪主請求情報タブ
        m_last_knm: '#infochief-tab #chief #m_last_knm',
        m_first_knm: '#infochief-tab #chief #m_first_knm',
        mg_yubin_no: '#infochief-tab #chief #zip_1',
        mg_addr1: '#infochief-tab #chief #address_1_1',
        m_mail_address: '#infochief-tab #chief #m_mail_address',
        mg_tel: '#infochief-tab #chief #tel_1',
        mg_m_tel: '#infochief-tab #chief #mobile_tel_1',
        mg_addr2: '#infochief-tab #chief #address_1_2',
        mk_kinmusaki_kbn: '#infochief-tab #chief #employee',
        mk_kinmusaki_nm: '#infochief-tab #chief #company',
        mk_tel: '#infochief-tab #chief #company_tel',
        mk_yakusyoku_nm: '#infochief-tab #chief #position',
        mk_fax: '#infochief-tab #chief #company_fax',
        m_nenrei_man: '#infochief-tab #chief #age', 
        // 打合せ事項①タブ
        keishiki_cd: '#input-tab #funeral_style',
        syushi_kbn: '#input-tab #syushi_kbn',
        syuha_cd: '#input-tab #syuha_cd',
        jyusho_cd: '#input-tab #temple_cd',
        jyusho_nm: '#input-tab #temple',
        temple_tel2: '#input-tab #temple_tel',
        temple_yubin_no: '#input-tab #temple_yubin_no',
        temple_person: '#input-tab #temple_person',
        // 相談履歴タブ
        reception_ymd: '.reception_date',
        reception_date: '.reception_date',
        reception_time: '.reception_time',
        // 互助会確認タブ
        kaiin_info_kbn: '.i_kaiin_info',
        kain_no: '.i_member_id',
        apply_no: '.i_apply_no',
        course_snm_cd: '.i_cose .select-container',
        kanyu_nm: '.i_member_name',
        yoto_kbn: '.i_usage .select-container',
        keiyaku_gaku: '.i_deposit',
        plan_convert_gaku: '.i_plan_convert',
        harai_gaku: '.i_pay',
        harai_no: '.i_times',
        zankin: '.zankin',
        wari_gaku: '.i_wari_gaku',
        cose_chg_gaku: '.i_balance',
        early_use_cost_disp: '.i_early',
        meigi_chg_cost_disp: '.i_mg_chg_cost',
        n_free3: '.i_premium_gaku',
        n_free4: '.i_kannnou_gaku',
        waribiki_gaku: '.i_waribiki_gaku',
        kanyu_tax: '.i_kanyu_tax',
        kanyu_dt_gen: '.i_entry_era .select-container',
        kanyu_dt: '.i_entry',
        zei_kijyn_ymd: '.i_tax',
        kanyu_dantai_ext: '#other_entry_name',
        riyu_memo: '#change_reason',
        plan_use_prc: '#plan_use_prc',
        plan_change_prc: '#plan_change_prc',
        n_free5: '.i_ekimu2',
        n_free6: '.i_wari1',
        n_free7: '.i_wari2',
        n_free8: '.i_wari3',

    };
    /**
     * validation valid時処理
     * @param {View} view
     * @param {string} attr
     */
    var _valid = function (view, attr) {
        var $el = view.$(appcst.pro[attr]);
        if ($el.length) {
            $el.removeClass('error1');
            $el.attr('title', '');
        }
    };

    /**
     * validation invalid時処理
     * @param {View} view
     * @param {string} attr
     * @param {string} error
     */
    var _invalid = function (view, attr, error) {
        var $el = view.$(appcst.pro[attr]);
        if ($el.length) {
            $el.addClass('error1');
            $el.attr('title', error);
        }
    };

    /**
     * 日付と時刻を結合してタイムスタンプに設定する処理
     * @param {string} niteiDate 日付
     * @param {string} niteiTime 時刻
     * @param {string} pro モデルの属性
     * @param {model} model モデル
     */
    var _setNiteiymd = function (niteiDate, niteiTime, pro, model) {
        var niteiymd = null;
        if (!$.msiJqlib.isNullEx2(niteiDate)) {
            niteiymd = niteiDate;
        }
        if (!$.msiJqlib.isNullEx2(niteiTime)) {
            if (!$.msiJqlib.isNullEx2(niteiymd)) {
                niteiymd += " ";
            }
            niteiymd += niteiTime;
        }
        model.set(pro, niteiymd);
    };
    /**
     * 日付チェック処理
     * @param {string} value 日付
     */
    var _chkYmd = function (value) {
        if (!$.msiJqlib.isNullEx2(value) && !$.msiJqlib.chkDate(value)) {
            return Backbone.Validation.messages.ymd;
        }
    };

    // select2のvalを設定する
    var _setSelect2Val = function ($el, val) {
        $el.select2("val", val);
    };

    // 区分値コード数値設定処理
    var _setKbnCdVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_cd_num);
        }
    };
    // 区分値コード正式名設定処理
    var _setKbnLnmVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.orgData.kbn_value_lnm);
        }
    };
    // 区分値コード略称名設定処理
    var _setKbnSnmVal = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.kbn_value_snm);
        }
    };
    // 区分値コード区分設定処理
    var _setCodeKbn = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.code_kbn);
        }
    };
    // 親部門設定処理(見積式場用)
    var _setOyaBumon = function ($el, m, target) {
        var item = $el.select2("data");
        if ($.msiJqlib.isNullEx2(item)) {
            m.set(target, null);
        } else {
            m.set(target, item.oya_bumon_cd);
        }
    };
    // 和暦の生年月日チェック処理
    var _validateSeinengappi = function (gengo, value) {
        if ($.msiJqlib.isNullEx2(value) || $.msiJqlib.isNullEx2(gengo)) {
            return '生年月日は必須項目です';
        }
        var seinengappi = $.msiJqlib.warekiToseireki(gengo, value);
        if (!seinengappi) {
            return '生年月日の形式エラーです';
        }
    };
    // 赤字クラスの追加削除処理
    appcst.toggleAkajiClass = function (that, targets) {
        _.each(targets, function (val) {
            var target = that.model.get(val);
            if (!$.msiJqlib.isNullEx2(target) && target < 0) {
                that.$(appcst.pro[val]).addClass('com-akaji');
            } else {
                that.$(appcst.pro[val]).removeClass('com-akaji');
            }
        });
    };
    var changeToNum = function (val) {
        var num = parseInt(val, 10);
        if (isFinite(num)) {
            return num;
        } else {
            num = 0;
            return num;
        }
    };

    /**
     * @description 画面全体処理
     */
    // 全体モデル
    var AppModel = Backbone.Model.extend({
        defaults: function () {
            return {
                // ヘッダー部
                bumon_cd: null, // 売上部門コード
                seko_no: null, // 施行番号
                status_kbn: "1", // ステータス
                moushi_code_kbn: codeKbns.moushi_code_kbn, // 申込区分コード区分
                moushi_cd: "5", // 申込コード
                moushi_kbn: "5", // 申込区分
                sougi_code_kbn: codeKbns.sougi_code_kbn, // 葬儀区分コード区分
                sougi_cd: "1", // 葬儀コード
                sougi_kbn: "1", // 葬儀区分
                daicho_no_eria: null, // 台帳番号1
                daicho_no_mm: null, // 台帳番号2
                daicho_no_seq: null, // 台帳番号3
                p_info_code_kbn: codeKbns.p_info_code_kbn, // 個人情報保護区分コード区分
                p_info_cd: null, // 個人情報保護コード
                p_info_kbn: null, // 個人情報保護区分
                kaiin_code_kbn: codeKbns.kaiin_code_kbn, // 会員区分コード区分
                kaiin_cd: "600", // 会員コード
                kaiin_kbn: "600", // 会員区分
                mitsu_tanto_cd: null, // 見積担当者コード
                mitsu_tanto_nm: null, // 見積担当者名
                uketuke_tanto_cd: null, // 受付担当者コード
                uketuke_tanto_nm: null, // 受付担当者名
                seko_tanto_cd: null, // 施行担当者コード
                seko_tanto_nm: null, // 施行担当者名
                k_last_nm_readonly: null, // 故人姓(表示用)
                k_first_nm_readonly: null, // 故人名(表示用)
                est_shikijo_cd: null, // 見積式場
                est_oya_bumon_cd: null,  // 見積式場の親部門コード
                // 受付情報タブ
                k_nm: null, // 故人名
                k_last_nm: null, // 故人苗字
                k_first_nm: null, // 故人名前
                k_file_nm: null, // 添付ファイル名OID
                k_file: null, // 添付ファイルOID一時
                k_sex_code_kbn: codeKbns.sex_code_kbn, // 性別区分コード区分
                k_sex_cd: "1", // 性別コード
                k_sex_kbn: "1", // 性別区分
                k_haigu_code_kbn: codeKbns.haigu_code_kbn, // 配偶者区分コード区分
                k_haigu_cd: null, // 配偶者コード
                k_haigu_kbn: null, // 配偶者区分
                k_knm: null, // 故人カナ名
                k_last_knm: null, // 故人カナ苗字
                k_first_knm: null, // 故人カナ名前
                k_birth_year: null, // 生年月日(年)
                k_wa_year: null, // 生年月日(元号年)
                k_gengo: null, // 故人元号
                k_birth_month: null, // 生年月日(月)
                k_birth_day: null, // 生年月日(日)
                k_seinengappi_ymd: null, // 生年月日(和暦)
                k_seinengappi_ymd_y: null, // 生年月日(西暦)
                k_nenrei_man: null, // 故人年齢
                k_nenrei_kyounen: null, // 行年・享年
                k_cif_no: null, // 故人CIFNo
                k_cif_status: "0", // 故人CIFNoステータス
                d_free2: null, // 故人生前予約契約日
                pacemaker_code_kbn: codeKbns.umu_code_kbn, // ペースメーカー区分コード区分
                pacemaker_kbn: null, // ペースメーカー区分
                kg_yubin_no: null, // 現住所郵便番号
                kg_addr1: null, // 現住所1
                kg_addr2: null, // 現住所2
                kg_tel: null, // 現住所TEL
                kk_kinmusaki_kbn: null, // 勤務先
                kk_kinmusaki_nm: null, // 勤務先名
                kk_tel: null, // 勤務先TEL
                kk_yakusyoku_nm: null, // 役職／職種
                kk_fax: null, // 勤務先FAX
                careful_memo: null, // 注意事項
                // 喪主請求情報タブ
                m_nm: null, // 喪主名
                m_last_nm: null, // 喪主苗字
                m_first_nm: null, // 喪主名前
                m_knm: null, // 喪主名カナ
                m_last_knm: null, // 喪主苗字カナ
                m_first_knm: null, // 喪主名前カナ
                m_file_nm: null, // 喪主添付ファイル
                m_file: null, // 喪主添付ファイルOID一時
                m_zoku_cd: null, // 喪主続柄コード
                m_zoku_kbn: null, // 喪主続柄区分
                m_zoku_nm: null, // 喪主続柄名
                m_zoku_code_kbn: codeKbns.zoku_code_kbn, // 喪主続柄区分コード区分
                m_zoku_cd2: null, // 喪主続柄コード
                m_zoku_kbn2: null, // 喪主続柄区分
                m_zoku_nm2: null, // 喪主続柄区分
                m_zoku_code_kbn2: codeKbns.zoku_code_kbn, // 喪主続柄区分コード区分
                m_birth_year: null, // 生年月日(年)
                m_wa_year: null, // 生年月日(元号年)
                m_gengo: null, // 喪主元号
                m_birth_month: null, // 生年月日(月)
                m_birth_day: null, // 生年月日(日)
                m_seinengappi_ymd: null, // 喪主生年月日
                m_seinengappi_ymd_y: null, // 喪主生年月日(西暦)
                m_nenrei_man: null, // 喪主年齢
                m_cif_no: null, // 喪主CIFNo
                m_cif_status: "0", // 喪主CIFNoステータス
                d_free3: null, // 相談者互助会加入日
                mg_yubin_no: null, // 喪主現住所郵便番号
                mg_addr1: null, // 喪主現住所1
                m_mail_address: null, // 喪主mailアドレス
                mg_tel: null, // 喪主現住所TEL
                mg_m_tel: null, // 喪主携帯
                mg_addr2: null, // 喪主現住所2
                mk_kinmusaki_kbn: null, // 喪主勤務先
                mk_kinmusaki_nm: null, // 喪主勤務先名
                mk_tel: null, // 喪主勤務先TEL
                mk_yakusyoku_nm: null, // 喪主役職／職種
                mk_fax: null, // 喪主勤務先FAX
                m_sex_code_kbn: codeKbns.sex_code_kbn, // 性別区分コード区分
                m_sex_kbn: "1", // 性別区分
                // 打合せ事項①タブ
                keishiki_code_kbn: codeKbns.keishiki_code_kbn, // 葬儀形式コード区分
                keishiki_cd: "1", // 葬儀形式コード
                keishiki_kbn: "1", // 葬儀形式区分
                syushi_code_kbn: codeKbns.syushi_code_kbn, // 宗旨コード区分
                syushi_cd: null, // 宗旨コード
                syushi_kbn: null, // 宗旨区分
                syuha_code_kbn: codeKbns.syuha_code_kbn, // 宗派コード区分
                syuha_cd: null, // 宗派コード
                syuha_kbn: null, // 宗派区分
                syuha_nm: null, // 宗派名
                syuha_knm: null, // 宗派名カナ
                kaimyo_kbn: null, // 戒名区分
                kaimyo_code_kbn: codeKbns.kaimyo_code_kbn, // 戒名区分コード区分
                jyusho_cd: null, // 寺院コード
                jyusho_nm: null, // 寺院名
                jyusho_knm: null, // 寺院カナ名
                temple_tel2: null, // 寺院tel
                temple_yubin_no: null, // 寺院郵便番号
                temple_addr1: null, // 寺院住所1
                temple_addr2: null, // 寺院住所2
                temple_fax: null, // 寺院fax
                tera_shokai_code_kbn: codeKbns.tera_shokai_code_kbn, // 寺紹介者コード
                tera_shokai_kbn: null, // 寺紹介者区分
                temple_person: null, //導師人数
                seko_biko1: null, // メモ
                sougi_ymd: null, 
            };
        },
        validation: {
            kaiin_kbn: {
                required: true,
            },
            k_seinengappi_ymd: "validateSeinengappi",
            k_nenrei_man: {
                required: false,
                pattern: 'number'
            },
            kg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            kg_tel: {
                required: false,
                pattern: 'tel'
            },
            kk_tel: {
                required: false,
                pattern: 'tel'
            },
            kk_fax: {
                required: false,
                pattern: 'tel'
            },
            k_first_knm: {
                required: false,
                maxLength: 20
            },
            k_last_knm: {
                required: false,
                maxLength: 20
            },
            m_first_knm: {
                required: false,
                maxLength: 20
            },
            m_last_knm: {
                required: false,
                maxLength: 20
            },
            m_seinengappi_ymd: "validateSeinengappiM",
            m_nenrei_man: {
                required: false,
                pattern: 'number'
            },
            mg_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            mg_tel: {
                required: false,
                pattern: 'tel'
            },
            m_mail_address: {
                required: false,
                pattern: 'email'
            },
            temple_person: {
                required: false,
                pattern: 'number'
            },
            bumon_cd: {
                required: true,
            },
            temple_yubin_no: {
                required: false,
                pattern: 'zip'
            },
            temple_tel: {
                required: false,
                pattern: 'tel'
            },
        },
        labels: {
            kaiin_kbn: '会員区分',
            k_seinengappi_ymd: '対象者生年月日',
            k_nenrei_man: '対象者年齢',
            kg_tel: '対象者電話番号',
            kk_tel: '対象者勤務先電話番号',
            kk_fax: '対象者勤務先FAX番号',
            k_last_knm: '対象者姓カナ',
            k_first_knm: '対象者名前カナ',
            m_last_knm: '相談者姓カナ',
            m_first_knm: '相談者名前カナ',
            m_gengo: '相談者生年月日元号',
            m_seinengappi_ymd: '相談者生年月日',
            m_nenrei_man: '相談者年齢',
            mg_yubin_no: '相談者現住所郵便番号',
            mg_tel: '相談者現住所TEL',
            mg_m_tel: '相談者携帯番号',
            m_mail_address: '相談者Mailアドレス',
            mk_tel: '相談者勤務先TEL',
            mk_fax: '相談者勤務先FAX',
            temple_person: '導師人数',
            bumon_cd: '部門コード',
            temple_tel: '菩提寺TEL',
            temple_yubin_no: '菩提寺郵便番号',
        },
        validateSeinengappi: function (value, attr, computedState) {
            var gengo = computedState.k_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return _validateSeinengappi(gengo, value);
            }
        },
        validateSeinengappiM: function (value, attr, computedState) {
            var gengo = computedState.m_gengo;
            if (!$.msiJqlib.isNullEx2(value)) {
                return _validateSeinengappi(gengo, value);
            }
        }
    }); // AppModel

    var AppViewDef = {
        el: $("#wrapper"),
        events: {
            "click .tab li a": "changeTab",
            "click #btn_kokyaku": "kokyakuHelper",
            "click #btn_gojokai_search": "gojokaiHelper",
            "click #mitsu_tanto, .label.dlg_mitsu_tanto": "mitsuTanHelper",
            "click #uketuke_tanto, .label.dlg_uketuke_tanto": "uketukeHelper",
            "click #staff_2, .label.dlg_staff2": "sekoHelper",
            "change #input-tab #k_last_nm": function (e) {
                this.model.set('k_last_nm_readonly', e.currentTarget.value);
            },
            "change #input-tab #k_first_nm": function (e) {
                this.model.set('k_first_nm_readonly', e.currentTarget.value);
            },
            "click #male": function (e) {
                // 基本タブ 性別（男）設定
                if ($(e.currentTarget).hasClass('k_m_sex_checked')) {
                    $('#male').removeAttr("checked", "checked");
                    $('#male').prev().removeClass("ui-state-active");
                    $('#male').removeClass("k_m_sex_checked");
                    $('#female').removeClass("k_fm_sex_checked");
                    this.model.set("k_sex_cd", "0");
                    this.model.set("k_sex_kbn", "0");
                } else {
                    $('#male').addClass("k_m_sex_checked");
                    $('#female').removeClass("k_fm_sex_checked");
                    this.model.set("k_sex_cd", "1");
                    this.model.set("k_sex_kbn", "1");
                }
            },
            "click #female": function (e) {
                // 基本タブ 性別（女）設定
                if ($(e.currentTarget).hasClass('k_fm_sex_checked')) {
                    $('#female').removeAttr("checked", "checked");
                    $('#female').prev().removeClass("ui-state-active");
                    this.model.set("k_sex_cd", "0");
                    this.model.set("k_sex_kbn", "0");
                    $('#female').removeClass("k_fm_sex_checked");
                    $('#male').removeClass("k_m_sex_checked");
                } else {
                    $('#female').addClass("k_fm_sex_checked");
                    $('#male').removeClass("k_m_sex_checked");
                    this.model.set("k_sex_cd", "2");
                    this.model.set("k_sex_kbn", "2");
                }
            },
            "click #m_male": function (e) {
                // 基本タブ 性別（男）設定
                if ($(e.currentTarget).hasClass('m_m_sex_checked')) {
                    $('#m_male').removeAttr("checked", "checked");
                    $('#m_male').prev().removeClass("ui-state-active");
                    $('#m_male').removeClass("m_m_sex_checked");
                    $('#m_female').removeClass("m_fm_sex_checked");
                    this.model.set("m_sex_kbn", "0");
                } else {
                    $('#m_male').addClass("m_m_sex_checked");
                    $('#m_female').removeClass("m_fm_sex_checked");
                    this.model.set("m_sex_kbn", "1");
                }
            },
            "click #m_female": function (e) {
                // 基本タブ 性別（女）設定
                if ($(e.currentTarget).hasClass('m_fm_sex_checked')) {
                    $('#m_female').removeAttr("checked", "checked");
                    $('#m_female').prev().removeClass("ui-state-active");
                    this.model.set("m_sex_kbn", "0");
                    $('#m_female').removeClass("m_fm_sex_checked");
                    $('#m_male').removeClass("m_m_sex_checked");
                } else {
                    $('#m_female').addClass("m_fm_sex_checked");
                    $('#m_male').removeClass("m_m_sex_checked");
                    this.model.set("m_sex_kbn", "2");
                }
            },
            "change #input-tab #birthday_era,#input-tab #birthday_month,#input-tab #birthday_day": "calcNereiK",
            "select2-open #input-tab #birthday_era": function () {
                var era = this.model.get('k_birth_year');
                if (!$.msiJqlib.isNullEx2(era)) {
                    return;
                }
                var choices = $('.select2-results').children('li');
                var index = choices.length - 70;
                var choice = $(choices[index]);
                choices.filter(".select2-highlighted").removeClass("select2-highlighted");
                choice = $(choices[index]);
                choice.addClass("select2-highlighted");
                // 自動スクロールさせる
                $('.select2-results').scrollTop(0 + (index * 25));

            },
            "change #infochief-tab #birthday_era,#infochief-tab #birthday_month,#infochief-tab #birthday_day": "calcNereiM",
            "select2-open #infochief-tab #birthday_era": function () {
                var era = this.model.get('m_birth_year');
                if (!$.msiJqlib.isNullEx2(era)) {
                    return;
                }
                var choices = $('.select2-results').children('li');
                var index = choices.length - 70;
                var choice = $(choices[index]);
                choices.filter(".select2-highlighted").removeClass("select2-highlighted");
                choice = $(choices[index]);
                choice.addClass("select2-highlighted");
                // 自動スクロールさせる
                $('.select2-results').scrollTop(0 + (index * 25));

            },
            "click #detail .label.dlg_zip": "zipHelper",
            "click .label.dlg_temple": "nmjyushoHelper",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
            "select2-opening #input-tab #syuha_cd": function () {
                // 宗派コードを開いたときに宗旨の区分で絞り込んで表示する
                var syushiCd = this.model.get("syushi_cd");
                var orgKbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
                var fileredKbns = [];
                _.each(orgKbns, function (item) {
                    if (item.kbn_value_cd_num === syushiCd) {
                        fileredKbns.push(item);
                    }
                });
                appcst.syuha_kbns = fileredKbns;
            },
            "select2-selecting #input-tab #syushi_cd": "clearSyuha",
            "select2-clearing #input-tab #syushi_cd": "clearSyuha",
            "change #input-tab #funeral_style": function (e) {
                _setSogiYmd();
            },
            "click .dlg_keiyaku_no, .cls_keiyaku_dantai_cd, .cls_keiyaku_dantai_nm": "keiyakuHelper",
            "click #btn_save": "doSave",
            "click #btn_print": "doPrint",
            "click #btn_uketamawari": "doPrintUketamawari",
            "click #btn_print_history": "doPrintHistory",
            "click #btn_delete": "doDelete",
            "click #btn_cancel": "doCancel",
            "click #btn_seko_copy": "doSekoCopy",
            "change #input-tab #syushi_cd": "setShuhaOther",
            "click #btn_mk_copy": "fromMtoKCopy",

        },
        bindings: {
            '#hall_cd': {
                observe: 'bumon_cd',
                updateView: false
            },
            '#apply_type': {
                observe: 'moushi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'moushi_kbn');
                    _setCodeKbn($el, this.model, 'moushi_code_kbn');
                    return $el.val();
                }
            },
            '#funeral_type': {
                observe: 'sougi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'sougi_kbn');
                    _setCodeKbn($el, this.model, 'sougi_code_kbn');
                    return $el.val();
                }
            },
            '#personal_info': {
                observe: 'p_info_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'p_info_kbn');
                    _setCodeKbn($el, this.model, 'p_info_code_kbn');
                    return $el.val();
                }
            },
            '#member': {
                observe: 'kaiin_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'kaiin_kbn');
                    _setCodeKbn($el, this.model, 'kaiin_code_kbn');
                    return $el.val();
                }
            },
            '#code_1': {
                observe: 'daicho_no_eria',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo($el.val(), this.model.get("daicho_no_mm"), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_2': {
                observe: 'daicho_no_mm',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), $el.val(), this.model.get("daicho_no_seq"));
                    return $el.val();
                }
            },
            '#code_3': {
                observe: 'daicho_no_seq',
                // 葬儀情報(ヘッダー)の台帳番号バインディング処理
                getVal: function ($el, event, options) {
                    this.setHeaderDaichoNo(this.model.get("daicho_no_eria"), this.model.get("daicho_no_mm"), $el.val());
                    return $el.val();
                }
            },
            '#mitsu_tanto': 'mitsu_tanto_nm',
            '#uketuke_tanto': {
                observe: 'uketuke_tanto_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('uketuke_tanto_cd', null);
                    }
                    return val;
                }
            },
            '#staff_2': {
                observe: 'seko_tanto_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('seko_tanto_cd', null);
                    }
                    return val;
                },
                // select2摘要時に修正が必要 葬儀情報(ヘッダー)の施行担当者バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_seko_tanto").text($el.val());
                    return $el.val();
                }
            },
            '#est_shikijo_cd': {
                observe: 'est_shikijo_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setOyaBumon($el, this.model, 'est_oya_bumon_cd');
                    return $el.val();
                }
            },
            'k_nm': {
                observe: 'k_nm',
                // 葬儀情報(ヘッダー)の故人名バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_k_nm").text($el.val());
                    return $el.val();
                }
            },
            '#k_last_nm': 'k_last_nm',
            '#k_first_nm': 'k_first_nm',
            '#k_last_nm_readonly': 'k_last_nm_readonly',
            '#k_first_nm_readonly': 'k_first_nm_readonly',
            '#input-tab #spouse': {
                observe: 'k_haigu_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'k_haigu_kbn');
                    _setCodeKbn($el, this.model, 'k_haigu_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #k_last_knm': 'k_last_knm',
            '#input-tab #k_first_knm': 'k_first_knm',
            '#input-tab #birthday_era': {
                observe: 'k_birth_year',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnSnmVal($el, this.model, 'k_wa_year');
                    return $el.val();
                }
            },
            '#input-tab #birthday_month': {
                observe: 'k_birth_month',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #birthday_day': {
                observe: 'k_birth_day',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #age': 'k_nenrei_man',
            '#input-tab #k_cif_no': 'k_cif_no',
            '#input-tab #k_cif_status': {
                observe: 'k_cif_status',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #seizen_yoyaku_ymd': 'd_free2',
            '#input-tab #age_at_death': 'k_nenrei_kyounen',
            '#input-tab #pacemaker': {
                observe: 'pacemaker_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setCodeKbn($el, this.model, 'pacemaker_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #zip_1': 'kg_yubin_no',
            '#input-tab #address_1_1': 'kg_addr1',
            '#input-tab #tel_1': 'kg_tel',
            '#input-tab #address_1_2': 'kg_addr2',
            '#input-tab #employee': {
                observe: 'kk_kinmusaki_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#input-tab #company': 'kk_kinmusaki_nm',
            '#input-tab #company_tel': 'kk_tel',
            '#input-tab #company_fax': 'kk_fax',
            '#input-tab #position': 'kk_yakusyoku_nm',
            '#input-tab #careful_memo': 'careful_memo',
            'm_nm': {
                observe: 'm_nm',
                // 葬儀情報(ヘッダー)の喪主名バインディング処理
                getVal: function ($el, event, options) {
                    this.$("#hd_m_nm").text($el.val());
                    return $el.val();
                }
            },
            '#infochief-tab #chief #m_last_nm': 'm_last_nm',
            '#infochief-tab #chief #m_first_nm': 'm_first_nm',
            '#infochief-tab #chief #s_chief_relationship': {
                observe: 'm_zoku_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'm_zoku_kbn');
                    _setKbnLnmVal($el, this.model, 'm_zoku_nm');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #s_chief_relationship2': {
                observe: 'm_zoku_cd2',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'm_zoku_kbn2');
                    _setKbnLnmVal($el, this.model, 'm_zoku_nm2');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #m_last_knm': 'm_last_knm',
            '#infochief-tab #chief #m_first_knm': 'm_first_knm',
            '#infochief-tab #chief #birthday_era': {
                observe: 'm_birth_year',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnSnmVal($el, this.model, 'm_wa_year');
                    return $el.val();
                }
            },
            '#infochief-tab #chief #birthday_month': {
                observe: 'm_birth_month',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #birthday_day': {
                observe: 'm_birth_day',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #age': {
                observe: 'm_nenrei_man',
                onGet: function (val, options) {
                    if (!$.msiJqlib.isNullEx2(val)) {
                        return '満' + val + '歳';
                    }
                }
            },
            '#infochief-tab #chief #age': 'm_nenrei_man',
            '#infochief-tab #m_cif_no': 'm_cif_no',
            '#infochief-tab #m_cif_status': {
                observe: 'm_cif_status',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #m_gojokai_ymd': 'd_free3',
            '#infochief-tab #chief #zip_1': 'mg_yubin_no',
            '#infochief-tab #chief #address_1_1': {
                observe: 'mg_addr1',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                    return $el.val();
                },
                afterUpdate: function ($el, event, options) {
                    var address = this.model.get('mg_addr2');
                    this.setHeadeMaddr($el.val(), address);
                }
            },
            '#infochief-tab #chief #address_1_2': {
                observe: 'mg_addr2',
                // 葬儀情報(ヘッダー)の喪主住所バインディング処理
                getVal: function ($el, event, options) {
                    var address = this.model.get('mg_addr1');
                    this.setHeadeMaddr(address, $el.val());
                    return $el.val();
                }
            },
            '#infochief-tab #chief #tel_1': 'mg_tel',
            '#infochief-tab #chief #mobile_tel_1': 'mg_m_tel',
            '#infochief-tab #chief #m_mail_address': 'm_mail_address',
            '#infochief-tab #chief #employee': {
                observe: 'mk_kinmusaki_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '#infochief-tab #chief #company': 'mk_kinmusaki_nm',
            '#infochief-tab #chief #company_tel': 'mk_tel',
            '#infochief-tab #chief #position': 'mk_yakusyoku_nm',
            '#infochief-tab #chief #company_fax': 'mk_fax',
            '#input-tab #funeral_style': {
                observe: 'keishiki_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'keishiki_kbn');
                    _setCodeKbn($el, this.model, 'keishiki_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #syushi_cd': {
                observe: 'syushi_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syushi_kbn');
                    _setCodeKbn($el, this.model, 'syushi_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #syuha_cd': {
                observe: 'syuha_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    // 区分値コード数値設定処理
                    _setKbnCdVal($el, this.model, 'syuha_kbn');
                    _setCodeKbn($el, this.model, 'syuha_code_kbn');
                    // 宗派名設定処理
                    var item = $el.select2("data");
                    if ($.msiJqlib.isNullEx2(item)) {
                        this.model.set("syuha_nm", null);
                        this.model.set("syuha_knm", null);
                    } else {
                        this.model.set("syuha_nm", item.text);
                        this.model.set("syuha_knm", item.kbn_value_snm);
                    }
                    return $el.val();
                }
            },
            '#input-tab #syuha_nm_other': 'syuha_nm',
            '#input-tab #syuha_knm': 'syuha_knm',
            '#input-tab #tera_shokai': {
                observe: 'tera_shokai_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCodeKbn($el, this.model, 'tera_shokai_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #temple_cd': 'jyusho_cd',
            '#input-tab #temple': {
                observe: 'jyusho_nm',
            },
            '#input-tab #temple_knm': 'jyusho_knm',
            '#input-tab #temple_tel': 'temple_tel2',
            '#input-tab #temple_yubin_no': 'temple_yubin_no',
            '#input-tab #temple_addr1': 'temple_addr1',
            '#input-tab #temple_addr2': 'temple_addr2',
            '#input-tab #temple_fax': 'temple_fax',
            '#kaimyo': {
                observe: 'kaimyo_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
                getVal: function ($el, event, options) {
                    _setCodeKbn($el, this.model, 'kaimyo_code_kbn');
                    return $el.val();
                }
            },
            '#input-tab #temple_person': {
                observe: 'temple_person',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#infochief-tab #memo': 'seko_biko1',
            '#sougi_ymd': 'sougi_ymd',
        },
        // 契約先情報 pickup
        keiyakuHelper: function(e) {
            var m = this.model; 
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var cd = $target.data("cd");
            var nm = $target.data("nm");
            this.$el.msiPickHelper({
                action: '/mref/keiyakusakidlg', 
                _myId: '#msi-dialog',
                width: '94%',
                height: '94%',
                onSelect: function(data) {
//                    console.log(data);
                    m.set(cd, data.code);
                    m.set(nm, data.name);
                },
                onClear: function() {
                    m.set(cd, null);
                    m.set(nm, null);
                },
                hookSetData: function() {
                    return {
                        init_search: 0,
                        s_seko_kbn: 1,    // 1:施行
                        //s_est_shikijo_cd: appcst.appModel.get('est_shikijo_cd'),    // 見積式場
                    }
                },
            });
        },
        // 顧客ピッカー
        kokyakuHelper: function(e) {
            // console.log('KOKYAKU 検索');
            var that = this;
            msiGlobalObj.kaikokyakudlg2Open( this, 
                                             ['相談者', '対象者'],
                                             {kokyaku_no_p1: this.model.get('m_cif_no'),
                                               kokyaku_no_p2: this.model.get('k_cif_no'),
                                               kokyaku_result_p1: this.model.get('m_cif_status'),
                                               kokyaku_result_p2: this.model.get('k_cif_status'),
                                               result_set_3: true, // true:未検索/該当なし/顧客No(3択)  false:該当なし/顧客No(2択)
                                               //
                                               // s_cond_etc01: '0', // 数値型は不可. 文字列型のみ
                                               // s_kokyaku_kbn: 0, // 9617(顧客区分): 0:個人,1:得意先
                                               // s_dantai_cd: '00400110', // 0010000, 00400110:ＪＡ東京みどりＧＬＣ, 00400001: ファミリーライフクラブ　ゴールド
                                               // is_easyreg: 0, // 顧客登録画面リンク表示
                                             },
                                             this._kokyakuHelperOnClose,
                                             this._kokyakuHelperOnSet );
        },

        // ダイアログ close 時ハンドラ ([キャンセル]ボタン押下時には呼ばれない)
        // kokyakudlgData: kokyakudlg のモデルデータ
        _kokyakuHelperOnClose: function( kokyakudlgData ) {
            console.log( '_kokyakuHelperOnClose kokyakudlgData=>', kokyakudlgData );
//            var dumpStr = JSON.stringify(kokyakudlgData, undefined, 2);
//            this.model.set( { dump_kokyaku: dumpStr } );

            if ( kokyakudlgData.kokyaku_upd_p1 ) { // 設定されていれば
                if ( !$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_no_p1) ) {
                    this.model.set('m_cif_no', kokyakudlgData.kokyaku_no_p1);
                     this.model.set('m_cif_status', 2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
//                    this.model.set('m_cif_status', 1);
                    if ( kokyakudlgData.kokyaku_data_p1) {
                        this.model.set('m_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_hyoji_nm);
                        this.model.set('m_last_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_nm1);
                        this.model.set('m_first_nm', kokyakudlgData.kokyaku_data_p1.kokyaku_nm2);
                        this.model.set('m_knm', kokyakudlgData.kokyaku_data_p1.kokyaku_hyoji_kana);
                        this.model.set('m_last_knm', kokyakudlgData.kokyaku_data_p1.kokyaku_kana1);
                        this.model.set('m_first_knm', kokyakudlgData.kokyaku_data_p1.kokyaku_kana2);
                        this.model.set('m_sex_kbn', kokyakudlgData.kokyaku_data_p1.sex_kbn);
                        if (kokyakudlgData.kokyaku_data_p1.sex_kbn == 2) {
                            $('#m_female').attr("checked", "checked");
                            $('#m_female').prev().addClass("ui-state-active");
                            $('#m_male').removeAttr("checked", "checked");
                            $('#m_male').prev().removeClass("ui-state-active");
                            $('#m_female').addClass("m_fm_sex_checked");
                            $('#m_male').removeClass("m_m_sex_checked");
                        } else if (kokyakudlgData.kokyaku_data_p1.sex_kbn == 1) {
                            $('#m_male').attr("checked", "checked");
                            $('#m_male').prev().addClass("ui-state-active");
                            $('#m_female').removeAttr("checked", "checked");
                            $('#m_female').prev().removeClass("ui-state-active");
                            $('#m_female').removeClass("m_fm_sex_checked");
                            $('#m_male').addClass("m_m_sex_checked");
                        } else {
                            $('#m_male').removeAttr("checked", "checked");
                            $('#m_male').prev().removeClass("ui-state-active");
                            $('#m_female').removeAttr("checked", "checked");
                            $('#m_female').prev().removeClass("ui-state-active");
                            $('#m_female').removeClass("m_fm_sex_checked");
                            $('#m_male').addClass("m_m_sex_checked");
                        }
                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p1.seinengappi_ymd)) {
                            var spSe = kokyakudlgData.kokyaku_data_p1.seinengappi_ymd.split('/');
                            this.model.set('m_birth_year', spSe[0]);
                            this.model.set('m_birth_month', spSe[1]);
                            this.model.set('m_birth_day', spSe[2]);
                            var wareki = $.msiJqlib.arrSeirekiToWareki(kokyakudlgData.kokyaku_data_p1.seinengappi_ymd);
                            if ( wareki[0] ) {
                                var gengo = wareki[0];
                                this.model.set('m_gengo', gengo);
                                this.model.set('m_wa_year', gengo+$.msiJqlib.fillPre(wareki[1]));
                            }
                        }

                        this.model.set('mg_yubin_no', kokyakudlgData.kokyaku_data_p1.yubin_no);
                        this.model.set('mg_addr1', kokyakudlgData.kokyaku_data_p1.addr1);
                        this.model.set('mg_addr2', kokyakudlgData.kokyaku_data_p1.addr2);
                        this.model.set('mg_tel', kokyakudlgData.kokyaku_data_p1.tel1);
                        this.model.set('mg_m_tel', kokyakudlgData.kokyaku_data_p1.tel2);
                        this.model.set('m_mail_address', kokyakudlgData.kokyaku_data_p1.e_mail);
                        this.model.set('mk_kinmusaki_nm', kokyakudlgData.kokyaku_data_p1.kimsaki_nm);
                        this.model.set('mk_tel', kokyakudlgData.kokyaku_data_p1.kimsaki_tel);
                        this.calcNereiM();
                    }
                } else {
                    this.model.set('m_cif_no', kokyakudlgData.kokyaku_no_p1);
                     this.model.set('m_cif_status', kokyakudlgData.kokyaku_result_p1); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    
                    this.model.set('m_nm', null);
                    this.model.set('m_last_nm', null);
                    this.model.set('m_first_nm', null);
                    this.model.set('m_knm', null);
                    this.model.set('m_last_knm', null);
                    this.model.set('m_first_knm', null);
                    this.model.set('m_sex_kbn', null);
                    $('#m_male').removeAttr("checked", "checked");
                    $('#m_male').prev().removeClass("ui-state-active");
                    $('#m_female').removeAttr("checked", "checked");
                    $('#m_female').prev().removeClass("ui-state-active");
                    $('#m_female').removeClass("m_fm_sex_checked");
                    $('#m_male').addClass("m_m_sex_checked");
                    this.model.set('mg_yubin_no', null);
                    this.model.set('mg_addr1', null);
                    this.model.set('mg_addr2', null);
                    this.model.set('mg_tel', null);
                    this.model.set('mg_m_tel', null);
                    this.model.set('m_mail_address', null);
                    
                    this.model.set('m_birth_year', null);
                    this.model.set('m_birth_month', null);
                    this.model.set('m_birth_day', null);
                    this.model.set('m_seinengappi_ymd', null);
                    this.model.set('m_seinengappi_ymd_y', null);
                    this.model.set('m_nenrei_man', null);
                    this.model.set('mk_kinmusaki_nm', null);
                    this.model.set('mk_tel', null);
                }
            }
            if ( kokyakudlgData.kokyaku_upd_p2 ) { // 設定されていれば
                if ( !$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_no_p2) ) {
                    this.model.set('k_cif_no', kokyakudlgData.kokyaku_no_p2);
                     this.model.set('k_cif_status', 2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
//                    this.model.set('m_cif_status', 1);
                    if ( kokyakudlgData.kokyaku_data_p2) {
                        this.model.set('k_nm', kokyakudlgData.kokyaku_data_p2.kokyaku_hyoji_nm);
                        this.model.set('k_last_nm', kokyakudlgData.kokyaku_data_p2.kokyaku_nm1);
                        this.model.set('k_first_nm', kokyakudlgData.kokyaku_data_p2.kokyaku_nm2);
                        this.model.set('k_knm', kokyakudlgData.kokyaku_data_p2.kokyaku_hyoji_kana);
                        this.model.set('k_last_knm', kokyakudlgData.kokyaku_data_p2.kokyaku_kana1);
                        this.model.set('k_first_knm', kokyakudlgData.kokyaku_data_p2.kokyaku_kana2);
                        this.model.set('k_sex_kbn', kokyakudlgData.kokyaku_data_p2.sex_kbn);
                        if (kokyakudlgData.kokyaku_data_p2.sex_kbn == 2) {
                            $('#female').attr("checked", "checked");
                            $('#female').prev().addClass("ui-state-active");
                            $('#male').removeAttr("checked", "checked");
                            $('#male').prev().removeClass("ui-state-active");
                            $('#female').addClass("k_fm_sex_checked");
                            $('#male').removeClass("k_m_sex_checked");
                        } else if (kokyakudlgData.kokyaku_data_p2.sex_kbn == 1) {
                            $('#male').attr("checked", "checked");
                            $('#male').prev().addClass("ui-state-active");
                            $('#female').removeAttr("checked", "checked");
                            $('#female').prev().removeClass("ui-state-active");
                            $('#female').removeClass("k_fm_sex_checked");
                            $('#male').addClass("k_m_sex_checked");
                        } else {
                            $('#male').removeAttr("checked", "checked");
                            $('#male').prev().removeClass("ui-state-active");
                            $('#female').removeAttr("checked", "checked");
                            $('#female').prev().removeClass("ui-state-active");
                            $('#female').removeClass("k_fm_sex_checked");
                            $('#male').addClass("k_m_sex_checked");
                        }
                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p2.seinengappi_ymd)) {
                            var spSe = kokyakudlgData.kokyaku_data_p2.seinengappi_ymd.split('/');
                            this.model.set('k_birth_year', spSe[0]);
                            this.model.set('k_birth_month', spSe[1]);
                            this.model.set('k_birth_day', spSe[2]);
                            var wareki = $.msiJqlib.arrSeirekiToWareki(kokyakudlgData.kokyaku_data_p2.seinengappi_ymd);
                            if ( wareki[0] ) {
                                var gengo = wareki[0];
                                this.model.set('k_gengo', gengo);
                                this.model.set('k_wa_year', gengo+$.msiJqlib.fillPre(wareki[1]));
                            }
                        }

                        this.model.set('kg_yubin_no', kokyakudlgData.kokyaku_data_p2.yubin_no);
                        this.model.set('kg_addr1', kokyakudlgData.kokyaku_data_p2.addr1);
                        this.model.set('kg_addr2', kokyakudlgData.kokyaku_data_p2.addr2);
                        this.model.set('kg_tel', kokyakudlgData.kokyaku_data_p2.tel1);
                        this.model.set('kk_kinmusaki_nm', kokyakudlgData.kokyaku_data_p2.kimsaki_nm);
                        this.model.set('kk_tel', kokyakudlgData.kokyaku_data_p2.kimsaki_tel);
                        if (!$.msiJqlib.isNullEx2(kokyakudlgData.kokyaku_data_p2.seizenyoyaku_keiyaku_ymd)) {
                            this.model.set('d_free2', kokyakudlgData.kokyaku_data_p2.seizenyoyaku_keiyaku_ymd.replace(/-/g, '/'));
                        }
                        this.calcNereiK();
                    }
                } else {
                    this.model.set('k_cif_no', kokyakudlgData.kokyaku_no_p2);
                     this.model.set('k_cif_status', kokyakudlgData.kokyaku_result_p2); // 検索結果区分(7722){0:未検索,1:該当なし,2:該当あり}
                    
                    this.model.set('k_nm', null);
                    this.model.set('k_last_nm', null);
                    this.model.set('k_first_nm', null);
                    this.model.set('k_knm', null);
                    this.model.set('k_last_knm', null);
                    this.model.set('k_first_knm', null);
                    this.model.set('k_sex_kbn', null);
                    $('#male').removeAttr("checked", "checked");
                    $('#male').prev().removeClass("ui-state-active");
                    $('#female').removeAttr("checked", "checked");
                    $('#female').prev().removeClass("ui-state-active");
                    $('#female').removeClass("k_fm_sex_checked");
                    $('#male').addClass("k_m_sex_checked");
                    this.model.set('kg_yubin_no', null);
                    this.model.set('kg_addr1', null);
                    this.model.set('kg_addr2', null);
                    this.model.set('kg_tel', null);
                    
                    this.model.set('k_birth_year', null);
                    this.model.set('k_birth_month', null);
                    this.model.set('k_birth_day', null);
                    this.model.set('k_seinengappi_ymd', null);
                    this.model.set('k_seinengappi_ymd_y', null);
                    this.model.set('k_nenrei_man', null);
                    this.model.set('d_free2', null);
                    this.model.set('kk_kinmusaki_nm', null);
                    this.model.set('kk_tel', null);
                }
            }

        },

        // データ選択時ハンドラ
        // offset: 順序番号, selectedData, kokyakudlgData: モデルデータ
        _kokyakuHelperOnSet: function( offset, selectedData, kokyakudlgData ) {
            console.log( '_kokyakuHelperOnSet [offset, selectedData, kokyakudlgData]=>', [offset, selectedData, kokyakudlgData] );
        },
        // コース情報 pickup
        gojokaiHelper: function (e) {
            if (msiLib2.isChildWindow()) {
                return;
                msiLib2.showWarn('子画面からはNoの変更はできません');
            }
            var kijunYmd = this.model.get('sougi_ymd');
            if (!$.msiJqlib.isNullEx2(kijunYmd)) {
                if (!kijunYmd.match(/^(\d{4})[\/](\d{2})[\/](\d{2})$/) ) {
                    kijunYmd = $.msiJqlib.getStdDate();
                } 
            } else {
                kijunYmd = $.msiJqlib.getStdDate();
            }
            msiGlobalObj.mockaiindlgOpen( this, { 
                    // s_cust_nm: this.model.get('user_name'), // 氏名. 検索条件の初期値
                    // s_member_no: '00-0-00000-000', // 加入者番号
                    // または個別設定、s_member_no2: '22', s_member_no3: '3', s_member_no4: '44444', s_member_no5: '555',
                    // s_tel_ex: '044-4444-4444', // TEL
                    // s_yubin_no: '123-4567', // 郵便番号
                    // s_addr_ex: '住所',
                    // s2_is_add_sisan_data: 0, // 「契約情報検索」応答形式で返す場合は 0. 通常不要.
                    s2_application_date: kijunYmd, // 利用予定日(契約情報参照で使う)
                    s2_user_name: this.model.get('k_last_nm')+'　'+this.model.get('k_first_nm')
                }, // 利用者(契約情報参照で使う)
                this.selectMocCustp,   // [決定]時のハンドラ
//                this.clearMocCustp     // [取消]時のハンドラ. 現状、ダイアログに[取消]ボタンは非表示なので使いません
            );
        },
        selectMocCustp: function (data) {
            console.log('@@@ selectMocCustp =>', data);
            if (!data) {
                return;
            }

            // 空いている行を見つける. 
            var emptyOffset = _.reduce( appcst.gojokaiMemberCol.models,
                                        function(ac, m, i) { if ( ac === null && !m.get('kain_no') ) ac = i; return ac; }, null );
            // 施行互助会会員の会員番号を取得する
            var kaiin_no_array = [];
            appcst.gojokaiMemberCol.each(function (m, i) {
                kaiin_no_array.push(m.get('kain_no'));
            });
            var err_flg = false;
            var err_msg = '';
            _.each(data, function (v, k) {
                var course_snm_cd = null;
                _.each(appcst.data.gojokaiCouseMst, function (item, iw) {
                    if (!$.msiJqlib.isNullEx2(course_snm_cd)) {
                        return false;
                    }
                    if (!$.msiJqlib.isNullEx2(iw) && v['CourseName'] === iw) {
                        course_snm_cd = iw;
                    } else if (!$.msiJqlib.isNullEx2(iw) && v['CourseName'].indexOf(iw) === 0) {
                        course_snm_cd = iw;
                    } else if (!$.msiJqlib.isNullEx2(iw) && iw.indexOf(v['CourseName']) === 0) {
                        course_snm_cd = iw;
                    }
                });
                var taxInfo = appcst.data.taxInfoAll;
                var zei_cd = null;
                _.each(taxInfo, function (item) {
                    if (item.zei_rtu == changeToNum(String(v['ContractTaxRate']))) {
                        zei_cd = item.zei_cd;
                    }
                });
                var kain_no = null;
                if (!$.msiJqlib.isNullEx2(v['member_no']) && !$.msiJqlib.isNullEx2(v['member_no_id'])) {
                    var kainNoAry = v['member_no_id'].split('-');
                    kain_no = kainNoAry[0]+'-'+v['member_no'];
                }
                var data = {cif_no: v['CustomerNo'],
                    course_snm_cd: course_snm_cd,
                    kain_no: kain_no,
                    kanyu_nm: v['CustomerName'],
                    kanyu_dt: v['ContractDate'],
                    keiyaku_gaku: changeToNum(String(v['TerminationValue'])),
                    harai_no: changeToNum(String(v['TotalPayNum'])),
                    harai_gaku: changeToNum(String(v['TotalPayValue'])) - changeToNum(String(v['BonusAmount'])) - changeToNum(String(v['BalanceDiscountValue'])) - changeToNum(String(v['PrepaymentDiscountValue'])),
                    wari_gaku: changeToNum(String(v['PrepaymentDiscountValue'])),
                    waribiki_gaku: changeToNum(String(v['BalanceDiscountValue'])),
                    early_use_cost_disp: changeToNum(String(v['EarlyUseCost'])),
                    meigi_chg_cost_disp: changeToNum(String(v['RenameCommission'])),
                    zei_cd: zei_cd,
                    cur_cd: v['ContractCode'],
                    v_free10: v['ContractStatus'],
                    v_free11: v['EarlyUseOriginDate'],
                    v_free12: v['EarlyUseLimitedDate'],
                    v_free13: v['RenameCommissionUsage'],
                    v_free14: v['TargetUser'],
                    v_free15: v['TargetDate'],
                    v_free16: kain_no,
                    v_free17: v['ContractNo'],
                    kanyu_tax: changeToNum(String(v['ContractTax'])),
//                    kaiin_info_kbn: '3',  // テスト
                    kaiin_info_kbn: '1',
                    n_free3: changeToNum(String(v['BonusAmount'])),
                    n_free4: changeToNum(String(v['PremiumMonths'])),
                    cur_cd: changeToNum(String(v['ContractStatus'])),
                    zan_gaku: (changeToNum(v['TerminationValue'])-changeToNum(v['TotalPayValue'])-changeToNum(v['PremiumMonths'])),
                };
                if (kaiin_no_array.indexOf(v['ContractNo']) !== -1) {
                    return;
                }
                var m = appcst.gojokaiMemberCol.at(emptyOffset++); // 確保分を超えるとエラー
                if (emptyOffset > 10) {
//                    err_flg = true;
//                    err_msg = '10件を超えての登録はできません。';
                    return;
                }
                m.set(data);
            });
//            if (err_flg) {
//                $.msiJqlib.showErr(err_msg);
//                return;
//            }
        },
        clearMocCustp: function (data) {
            console.log('@@@ clearMocCustp =>', data);
            this._clearCustAll();
            this.$('#mocRes').val(null);
        },
        _clearCustOne: function (offset) {
            this.model.set('ContractNo' + offset, null);
            this.model.set('CustomerName' + offset, null);
            this.model.set('CustomerEtc' + offset, null);
        },
        _clearCustAll: function () {
            var that = this;
            _.each([1, 2, 3, 4], function (cnt) {
                that._clearCustOne(cnt);
            });
        },
        // 見積担当者ヘルパー処理 
        mitsuTanHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'mitsu_tanto_cd': data.code, 'mitsu_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'mitsu_tanto_cd': null, 'mitsu_tanto_nm': null});
                }
            });
        },
        // 故人年齢計算処理
        calcNereiK: function () {
            var era = this.model.get('k_wa_year');
            var seireki = this.model.get('k_birth_year');
            var month = this.model.get('k_birth_month');
            var day = this.model.get('k_birth_day');
            if ($.msiJqlib.isNullEx2(era) || $.msiJqlib.isNullEx2(month) || $.msiJqlib.isNullEx2(day)) {
                return;
            }
            var gengo = era.slice(0, 1);
            var wareki_y = era.slice(1);
            var seinengappi = seireki + '/' + month + '/' + day;
            if (!$.msiJqlib.chkDate(seinengappi)) { // 日付チェック
                seinengappi = null;
            }
            this.model.set('k_gengo', gengo);
            this.model.set('k_seinengappi_ymd', wareki_y + '/' + month + '/' + day);
            this.model.set('k_seinengappi_ymd_y', seinengappi);

            if (seinengappi) {
                var pram = {
                    seinengappi: seinengappi,
                    setData: function (data) { // コールバック
                        this.model.set("k_nenrei_man", data.man);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("k_nenrei_man", null);
            }
        },
        // 喪主年齢計算処理
        calcNereiM: function () {
            var era = this.model.get('m_wa_year');
            var seireki = this.model.get('m_birth_year');
            var month = this.model.get('m_birth_month');
            var day = this.model.get('m_birth_day');
            if ($.msiJqlib.isNullEx2(era) || $.msiJqlib.isNullEx2(month) || $.msiJqlib.isNullEx2(day)) {
                return;
            }
            var gengo = era.slice(0, 1);
            var wareki_y = era.slice(1);
            var seinengappi = seireki + '/' + month + '/' + day;
            if (!$.msiJqlib.chkDate(seinengappi)) { // 日付チェック
                seinengappi = null;
            }
            this.model.set('m_gengo', gengo);
            this.model.set('m_seinengappi_ymd', wareki_y + '/' + month + '/' + day);
            this.model.set('m_seinengappi_ymd_y', seinengappi);
            if (seinengappi) {
                var pram = {
                    nakunariymd: null,
                    seinengappi: seinengappi,
                    setData: function (data) {
                        this.model.set("m_nenrei_man", data.man);
                    }
                };
                this.calcNerei(pram);
            } else {
                this.model.set("m_nenrei_man", null);
            }
        },
        // 年齢計算処理
        calcNerei: function (pram) {
            var that = this;
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/mitsu/calcnenrei',
                data: {
                    nakunariymd: pram.nakunariymd,
                    seinengappi: pram.seinengappi
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        if (_.has(pram, "setData")) {
                            pram.setData.apply(that, [mydata]);
                        }
                    }
                }
            });
        },
        setShuhaOther: function () {
            if (this.model.get("syushi_cd") === '9') { // その他
                this.model.set("syuha_cd", '');
                this.$("#syuha_nm_other").show();
                this.$("#syuha_knm2").show();
                this.$("#syuha_knm").hide();
            } else {
                this.$("#syuha_nm_other").hide();
                this.$("#syuha_knm2").hide();
                this.$("#syuha_knm").show();
            }
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        // 宗派情報をクリアする
        clearSyuha: function (e) {
            if (e.val !== this.model.get("syushi_cd")) {
                this.model.set({'syuha_cd': null
                    , 'syuha_kbn': null
                    , 'syuha_nm': null
                    , 'syuha_knm': null
//                    , 'jyusho_cd': null
//                    , 'jyusho_nm': null
//                    , 'jyusho_knm': null
//                    , 'temple_tel': null
//                    , 'temple_yubin_no': null
//                    , 'temple_addr1': null
//                    , 'temple_addr2': null
//                    , 'temple_fax': null
                });
            }
        },
        // 葬儀情報(ヘッダー)の台帳番号バインディング処理
        setHeaderDaichoNo: function (d1, d2, d3) {
            this.$("#hd_daicho_no").text(d1 + '-' + d2 + '-' + d3);
        },
        // 葬儀情報(ヘッダー)の喪主バインディング処理
        setHeadeMaddr: function (addr1, addr2) {
            if ($.msiJqlib.isNullEx2(addr1)) {
                addr1 = '';
            }
            if ($.msiJqlib.isNullEx2(addr2)) {
                addr2 = '';
            }
            this.$("#hd_mg_addr").text(addr1 + ' ' + addr2);
        },
        // 相談者住所を対象者住所にコピー
        fromMtoKCopy: function () {
            this.model.set('kg_yubin_no', this.model.get('mg_yubin_no'))
            this.model.set('kg_addr1', this.model.get('mg_addr1'))
            this.model.set('kg_addr2', this.model.get('mg_addr2'))
            this.model.set('kg_tel', this.model.get('mg_tel'))
        },
        initialize: function () {

            Backbone.Validation.bind(this);
            this.listenTo(appcst.consulthistoryCol, 'reset', this.addAllConsultHistoryCol);
            // 見積式場切り替え処理
            this.setSideMenuStatus();
            this.render();
        },
        render: function () {
            var m = this.model;
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.stickit();
            this.setSelect2();
            return this;
        },
        // select2設定処理
        setSelect2: function () {
            // ヘッダー部
            // 申込区分
            var moushi_kbn = $.msiJqlib.objToArray3(data.dataKbns.moushi_kbn);
            if (!(data.dataSekoKihon.moushi_cd === MOUSHI_KBN_SEIZEN)) {
                // 事前相談を表示しない 2017/06/26 ADD Otake
                for (var idx = moushi_kbn.length - 1; 0 <= idx; idx--) {
                    if (moushi_kbn[idx].id === MOUSHI_KBN_SEIZEN) {
                        moushi_kbn.splice(idx, 1);
                    }
                }
            }
            // 申込区分
            $.msiJqlib.setSelect2Com1(this.$("#apply_type"), {data: moushi_kbn});
            // 葬儀区分
            $.msiJqlib.setSelect2Com1(this.$("#funeral_type"), {data: $.msiJqlib.objToArray3(data.dataKbns.sougi_kbn)});
            // 個人情報保護
            $.msiJqlib.setSelect2Com1(this.$("#personal_info"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.p_info)}, $.msiJqlib.setSelect2Default1)));
            // 会員区分
            $.msiJqlib.setSelect2Com1(this.$("#member"), {data: $.msiJqlib.objToArray3(data.dataKbns.kaiin_kbn)});
            // 見積式場
            $.msiJqlib.setSelect2Com1(this.$("#est_shikijo_cd"), ($.extend({data: data.dataKbns.est_shikijo}, $.msiJqlib.setSelect2Default2)));
            // 受付情報タブ
            // 配偶者
            $.msiJqlib.setSelect2Com1(this.$("#spouse"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.haigu_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #birthday_era"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.gengo)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #birthday_month"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.month)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #birthday_day"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.day)}, $.msiJqlib.setSelect2Default1)));
            // 故人CIFNoステータス
            $.msiJqlib.setSelect2Com1(this.$("#k_cif_status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.cif_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // ペースメーカー有無区分
            $.msiJqlib.setSelect2Com1(this.$("#pacemaker"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.pacemaker_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 勤務先
            $.msiJqlib.setSelect2Com1(this.$("#input-tab #employee"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kinmu_kbn_k)}, $.msiJqlib.setSelect2Default1)));
            // 喪主請求情報タブ
            // 続柄
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #s_chief_relationship"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 喪主様からみた続柄
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #s_chief_relationship2"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.zoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 生年月日
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #birthday_era"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.gengo)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #birthday_month"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.month)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #birthday_day"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.day)}, $.msiJqlib.setSelect2Default1)));
            // 喪主CIFNoステータス
            $.msiJqlib.setSelect2Com1(this.$("#m_cif_status"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.cif_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 勤務先
            $.msiJqlib.setSelect2Com1(this.$("#infochief-tab #employee"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kinmu_kbn_m)}, $.msiJqlib.setSelect2Default1)));
            // 打合せ事項①タブ
            // 葬儀形式
            $.msiJqlib.setSelect2Com1(this.$("#funeral_style"), {data: $.msiJqlib.objToArray3(data.dataKbns.keishiki_kbn)});
            // 宗旨区分
            $.msiJqlib.setSelect2Com1(this.$("#syushi_cd"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.syushi_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 宗派区分
            appcst.syuha_kbns = $.msiJqlib.objToArray3(data.dataKbns.syuha_kbn);
            $.msiJqlib.setSelect2Com1(this.$("#syuha_cd"), ($.extend({data: function () {
                    return {results: appcst.syuha_kbns};
                }}, $.msiJqlib.setSelect2Default2)));
            // 寺紹介者
            $.msiJqlib.setSelect2Com1(this.$("#tera_shokai"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.tera_shokai)}, $.msiJqlib.setSelect2Default1)));
            // 戒名
            $.msiJqlib.setSelect2Com1(this.$("#kaimyo"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kai_name)}, $.msiJqlib.setSelect2Default1)));
        },
        // 見積式場変更によるサイドメニュー切り替え処理
        setSideMenuStatus: function () {
            var sideMenu = data.sideMenuDataCol;
            var moushi_kbn = this.model.get('moushi_kbn');
            if ($.msiJqlib.isNullEx2(this.model.get('est_shikijo_cd'))) {
                // お客様情報以外非表示
                _.each(sideMenu, function (item) {
                    var css = item.css_class;
                    if (css != 'customer') {
                        $("#side ." + css).hide();
                    }
                });
            } else {
                _.each(sideMenu, function (item) {
                    var css = item.css_class;
                    if (css == 'schdule') {
                        $("#side ." + css).hide();
                    } else {
                        $("#side ." + css).show();
                    }
                });
            }
        },
        addConsultHistoryOne: function (memo) {
            var v = new ConsultHistoryView({model: memo});
            this.$("#consult_msi").append(v.render().el);
        },
        addAllConsultHistoryCol: function (collection) {
            var $consultMsi = this.$("#consult_msi");
            $consultMsi.find('tbody').remove();
            collection.each(this.addConsultHistoryOne, this);
        },
        // チェックボックス切り替え処理
        setCheckBox: function (e, pro, target) {
            var val = $(target + ':checked').val();
            this.model.set(pro, val === "1" ? "1" : "0");
        },
        // タブ切り替え処理
        changeTab: function (e) {
            $.msiJqlib.changeTab(this.$('.tab-contents'), this.$('.tab li'), $(e.currentTarget));
            var $tabId = $.msiJqlib.getTabId();
            // 互助会確認の場合、加入状況をクリックする
            if ($tabId.is('#tab-kaiin-info')) {
                this.$("#member_group_set #member_group_1").click();
            }
        },
        isInputOk: function () {

            var aMsg = [];
            // 施行基本フリーモデルチェック
            var result = appcst.kfModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    if ($.inArray(v, aMsg) < 0) {
                        aMsg.push(v);
                    }
                });
            }
            // 施行基本モデルチェック
            var result = appcst.appModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 事前相談履歴モデルチェック
            var result = appcst.jizenModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 相談履歴コレクションチェック
            appcst.consulthistoryCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                    });
                }
            });

            // 施行互助会会員コレクションチェック
            appcst.gojokaiMemberCol.each(function (m, i) {
                var resLine = m.validate();
                if (resLine) {
                    _.each(resLine, function (v, k) {
                        if ($.inArray(v, aMsg) < 0) {
                            aMsg.push(v);
                        }
                        ;
                    });
                }
            });

            // 施行互助会情報モデルチェック
            var result = appcst.gojokaiInfoModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }
            // 契約先情報モデルチェック
            var result = appcst.sekoKeiyakusakiInfoModel.validate();
            if (result) {
                _.each(result, function (v, k) {
                    aMsg.push(v);
                });
            }

            // NG
            if (aMsg.length > 0) {
                var errClsNm = ".error1";
                var $li = this.$('.tab');
                if (this.$("#input-tab").find(errClsNm).length) {   // 受付情報
                    $li.find("#tab-input-info a").click();
                } else if (this.$("#infochief-tab").find(errClsNm).length) {    // 喪主請求情報
                    $li.find("#tab-seikyu-info a").click();
                } else if (this.$("#infochief2-tab").find(errClsNm).length) {   // 相談履歴
                    $li.find("#tab-input-info2 a").click();
                } else if (this.$("#infomember-tab").find(errClsNm).length) {   //会員情報
                    $li.find("#tab-kaiin-info a").click();
                }
                $.msiJqlib.showErr(aMsg.join(', '));
                return false;
            }

            // OK
            $.msiJqlib.clearAlert();
            // console.log( 'valid OK' );
            return true;
        },
        doSave: function () {
            if (!this.isInputOk()) {
                return;
            }
            if (!this.gojoYotoCheck()) {
                return;
            }
            if (this.model.get('moushi_kbn') == "5") {
                if (!$.msiJqlib.isNullEx2(this.model.get('copy_moto_seko_no'))) {
                    $.msiJqlib.showWarn('事前相談施行からコピーした施行は事前相談にはできません');
                    return;
                }
            }
            // 相談者チェック
//            if (!$.msiJqlib.isNullEx2(this.model.get('m_first_nm')) || !$.msiJqlib.isNullEx2(this.model.get('m_last_nm')) 
//                    || !$.msiJqlib.isNullEx2(this.model.get('m_first_knm')) || !$.msiJqlib.isNullEx2(this.model.get('m_last_knm'))) {
//                if ($.msiJqlib.isNullEx2(this.model.get('m_cif_status')) || this.model.get('m_cif_status') == '0') {
//                    $.msiJqlib.showErr('相談者の顧客検索状況が未検索のままです。');
//                    return;
//                }
//            }
            // 対象者チェック
//            if (!$.msiJqlib.isNullEx2(this.model.get('k_first_nm')) || !$.msiJqlib.isNullEx2(this.model.get('k_last_nm')) 
//                    || !$.msiJqlib.isNullEx2(this.model.get('k_first_knm')) || !$.msiJqlib.isNullEx2(this.model.get('k_last_knm'))) {
//                if ($.msiJqlib.isNullEx2(this.model.get('k_cif_status')) || this.model.get('k_cif_status') == '0') {
//                    $.msiJqlib.showErr('対象者の顧客検索状況が未検索のままです。');
//                    return;
//                }
//            }
            
            var bumonCd = $('#hall_cd').val();
            this.exeSave();
        },
        gojoYotoCheck: function () {
            var flg = true;
            var kaiin_kbn = appcst.appModel.get('kaiin_kbn');
            // 会員区分が互助会以外はスルーする
//            if (kaiin_kbn != KAIIN_KBN_GOJO) {
//                return flg;
//            }
            var yoto_cnt = 0;
            if (appcst.gojokaiMemberCol.length != 0) {
                _.each(appcst.gojokaiMemberCol.models, function (item) {
                    if (!$.msiJqlib.isNullEx2(item.get('yoto_kbn')) && (item.get('yoto_kbn') == YOTO_COURSE) && item.get('delete_check') == '0') {
                        yoto_cnt++;
                    }
                });
            }
            // コース施行が２件以上あればfalse
            if (yoto_cnt > 1) {
                $.msiJqlib.showErr('用途にコース施行が複数件選択されています。');
                flg = false;
            }

            return flg;
        },
        exeSave: function () {
            // 施行基本イコール
            var sekoKihonEq = $.msiJqlib.isEqual(appcst.appModel.toJSON(), orgDataApp);
            // 施行基本汎用フリー情報イコール
            var sekoKihonFreeEq = $.msiJqlib.isEqual(appcst.kfModel.toJSON(), orgDataKihonFree);
            // 顧客基本情報イコール
            var jizenEq = $.msiJqlib.isEqual(appcst.jizenModel.toJSON(), orgDataJizen);
            // 相談履歴イコール
            var consultHistoryEq = $.msiJqlib.isEqual(appcst.consulthistoryCol.toJSON(), orgDataConsultHistoryCol);
            // 施行互助会情報イコール
            var sekoGojokaiInfoEq = $.msiJqlib.isEqual(appcst.gojokaiInfoModel.toJSON(), orgDataGojokaiInfo);
            // 施行契約先情報イコール
            var sekoKeiyakusakiInfoEq = $.msiJqlib.isEqual(appcst.sekoKeiyakusakiInfoModel.toJSON(), orgDataSekoKeiyakusakiInfo);
            // 施行互助会加入者イコール
            var sekoGojokaiMemberEq = $.msiJqlib.isEqual(appcst.gojokaiMemberCol.toJSON(), orgDataGojokaiMemberCol);

            if (sekoKihonEq && sekoKihonFreeEq && jizenEq && consultHistoryEq && sekoGojokaiInfoEq && sekoKeiyakusakiInfoEq && sekoGojokaiMemberEq) {
                $.msiJqlib.showInfo('データの変更がありません');
                return;
            }
            // チェンジフラグ設定
            var changeFlg = JSON.stringify({
                kihonChangeFlg: !sekoKihonEq,
                kihonFreeChangeFlg: !sekoKihonFreeEq,
                jizenChangeFlg: !jizenEq,
                consultHistoryChangeFlg: !consultHistoryEq,
                gojokaiInfoChangeFlg: !sekoGojokaiInfoEq,
                sekoKeiyakusakiInfoChangeFlg: !sekoKeiyakusakiInfoEq,
                gojokaiMemberChangeFlg: !sekoGojokaiMemberEq,
            });
//            var bumonCd = $('#hall_cd').val();
//            appcst.appModel.set('bumon_cd', bumonCd);
            // 施行基本情報
            var dataSekoKihonJson = JSON.stringify(appcst.appModel.toJSON());
            // 施行基本汎用フリー情報
            var dataSekoKihonFreeJson = JSON.stringify(appcst.kfModel.toJSON());
            // 顧客基本情報
            var dataJizenJson = JSON.stringify(appcst.jizenModel.toJSON());
            // 相談履歴情報
            var dataConsultHistoryColJson = JSON.stringify(appcst.consulthistoryCol.toJSON());
            // 施行互助会情報
            var dataGojokaiInfoJson = JSON.stringify(appcst.gojokaiInfoModel.toJSON());
            // 施行契約先情報
            var dataSekoKeiyakusakiInfoJson = JSON.stringify(appcst.sekoKeiyakusakiInfoModel.toJSON());
            // 施行互助会加入者情報
            // 会員番号があるデータに絞込み
            var dataGojokaiMemberCol = this.gojokaiMemberfilter();
            var dataGojokaiMemberColJson = JSON.stringify(dataGojokaiMemberCol);
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/preconsultsave',
                data: {
                    controllerName: appcst.controllerName,
                    dataSekoKihonJson: dataSekoKihonJson,
                    dataSekoKihonFreeJson: dataSekoKihonFreeJson,
                    dataJizenJson: dataJizenJson,
                    dataConsultHistoryColJson: dataConsultHistoryColJson,
                    dataGojokaiInfoJson: dataGojokaiInfoJson,
                    oldDataGojokaiInfo: orgDataGojokaiInfo,
                    dataSekoKeiyakusakiInfoJson: dataSekoKeiyakusakiInfoJson,
                    dataGojokaiMemberColJson: dataGojokaiMemberColJson,
                    changeFlg: changeFlg,
                    orgModCnts: orgModCnts
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        // メニューの再設定
                        if ((Object.keys(appjh).length) > 0) {
                            appjh.setSide({data: mydata.dataSideMenu});
                        } else {
                            $.msiSideMenuLib.setSideMenu({data: mydata.dataSideMenu});
                        }
                        _resetData(mydata.dataSekoKihon,
                                mydata.dataSekoKihonFree,
                                mydata.dataJize,
                                mydata.dataConsultHistoryCol,
                                mydata.dataGojokaiInfo,
                                mydata.dataSekoKeiyakusakiInfo,
                                mydata.dataGojokaiMemberCol);
                        $("#header .id_number span.number").text(mydata.dataSekoKihon.seko_no);  // 施行番号設定
                        appcst.setFileLink();
                        orgModCnts = mydata.modCnts;
                        $.msiJqlib.showInfo(mydata.msg);

                        var matcheds = location.href.match(/(\/sn\/\d+)/);
                        if (!matcheds) {
                            var m = location.href.match(/\/(\D+)\/(\D+)\/(\D+)\/(\D+)/);
                            var herf;
                            if (m) {
                                herf = $.msiJqlib.baseUrl() + "/" + m[2] + "/" + m[3] + "/" + m[4].replace("/", '') + "/sn/" + mydata.dataSekoKihon.seko_no;
                            } else {
                                herf = location.href + "/sn/" + mydata.dataSekoKihon.seko_no;
                            }
                            location.href = herf;
                        }
                    } else if (mydata.status === 'NG') {
                        $.msiJqlib.showWarn(mydata.msg);
                    } else {
                        $.msiJqlib.showErr(mydata.msg);
                    }
                    //console.log('ajax res msg==>' + mydata.msg);
                }
            });
        },
        // コースがあるデータに絞込み
        gojokaiMemberfilter: function () {
            var dataGojokaiMemberCol = appcst.gojokaiMemberCol.filter(function (item) {
                return (!$.msiJqlib.isNullEx2(item.get("course_snm_cd")) || !$.msiJqlib.isNullEx2(item.get("other_cose_nm"))) && item.get("delete_check") == '0';
            });
            return dataGojokaiMemberCol;
        },
        doPrint: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            var pdfUrl = 'pdf0101';
            var $tabId = $.msiJqlib.getTabId();
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/' + pdfUrl,
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
            return;
        },
        doPrintUketamawari: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            var pdfUrl = 'pdf0102';
            var $tabId = $.msiJqlib.getTabId();
            // ajax 版サンプル
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/' + pdfUrl,
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo
                }
            });
            return;
        },
        doPrintHistory: function () { // 見積履歴
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            var pdfUrl = 'pdf0113';
            // ajax 版
            msiLib2.fileDlAjax({
                url: $.msiJqlib.baseUrl() + '/juchu/' + pdfUrl,
                data: {
                    preview: 'off',
                    send: 'off',
                    seko_no: sekoNo,
                    data_kbn: '1',
                    printSbt: 'history',
                    output_kbn: 1,
                    ptn: 'main',
                }
            });
            return;
        },
        doDelete: function () {
            var sekoNo = appcst.appModel.get("seko_no");
            if ($.msiJqlib.isNullEx2(sekoNo)) {
                return;
            }
            if (!confirm('お客様情報を削除します。よろしいですか？')) {
                return;
            }
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/juchu/juchuhenko/customerinfodelete',
                data: {
                    seko_no: sekoNo,
                    controllerName: appcst.controllerName
                },
                type: 'POST',
                success: function (mydata) {
                    if (mydata.status === 'OK') {
                        var matcheds = location.href.match(/(.+)(\/sn\/\d+)/);
                        if (matcheds) {
                            location.href = matcheds[1];
                        } else {
                            window.location.reload();
                        }
                        $.msiJqlib.showInfo(mydata.msg);
                    } else {
                        $.msiJqlib.showWarn(mydata.msg);
                    }
                }
            });
        },
        doCancel: function () {
            if (!confirm('初期状態に戻してよろしいですか？')) {
                return;
            }
            window.location.reload();
        },
        // 郵便番号ヘルパー処理
        zipHelper: function (e) {
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            if ($target.attr("disabled") === "disabled") {
                return;
            }
            var val = $target.data("zip");
            var zip, addr1;
            var m = this.model;
            if (val === 'k1') { // 故人現住所
                zip = 'kg_yubin_no';
                addr1 = 'kg_addr1';
            } else if (val === 'm1') {// 喪主現住所
                zip = 'mg_yubin_no';
                addr1 = 'mg_addr1';
            } else if (val === 't1') {// 菩提寺
                zip = 'temple_yubin_no';
                addr1 = 'temple_addr1';
            }
            this.$el.msiPickHelper({
                action: 'zipno',
                onSelect: function (data) {
                    m.set(zip, data.code);
                    m.set(addr1, data.name);
                },
                onClear: function () {
                    m.set(zip, null);
                    m.set(addr1, null);
                }
            });
        },

        // 受付担当者ヘルパー処理 
        uketukeHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'uketuke_tanto_cd': data.code, 'uketuke_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'uketuke_tanto_cd': null, 'uketuke_tanto_nm': null});
                }
            });
        },
        // 施行担当者ヘルパー処理
        sekoHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'seko_tanto_cd': data.code, 'seko_tanto_nm': data.name});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text(data.name);
                },
                onClear: function () {
                    m.set({'seko_tanto_cd': null, 'seko_tanto_nm': null});
                    // 葬儀情報(ヘッダー)の施行担当者バインディング処理
                    $("#hd_seko_tanto").text('');
                }
            });
        },
        // 名所住所ヘルパー処理
        nmjyushoHelper: function (e) {
            var t = this;
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var kind = $target.data("kind2");
            var code = $target.data("code");
            var name = $target.data("name");
            var kname = $target.data("kname");
            var tel = $target.data("tel");
            var fax = $target.data("fax");
            var m = this.model;
            var syuha_cd = null;
            if (kind === 1) {
                syuha_cd = this.model.get("syuha_cd");
            }
            this.$el.msiPickHelper({
                action: 'nmjyusho',
                kind2: kind,
                mydata: {is_easyreg_jiin:0},
                onSelect: function (data) {
                    m.set(code, data.code);
                    m.set(name, data.name);
                    if (!$.msiJqlib.isNullEx2(kname)) {
                        m.set(kname, data.jyusho_lknm);
                        m.set(tel, data.tel);
                        m.set(fax, data.fax);
                        m.set('temple_yubin_no', data.zip_no);
                        m.set('temple_addr1'   , data.addr1_nm);
                        m.set('temple_addr2'   , data.addr2_nm);
                        if (kind === 1) {
                            if (!$.msiJqlib.isNullEx2(data.syuha_cd) && m.get("syuha_cd") !== data.syuha_cd) {
                                m.set('syushi_cd', data.syushi_cd);
                                m.set('syuha_cd', data.syuha_cd);
                                m.set('syuha_kbn', data.syushi_kbn);
                                m.set('syuha_nm', data.syuha_nm);
                                m.set('syuha_knm', data.syuha_kana);
                                t.setShuhaOther();
                            }
                        }
                    }
                },
                onClear: function () {
                    m.set(code, null);
                    m.set(name, null);
                    if (!$.msiJqlib.isNullEx2(kname)) {
                        m.set(kname, null);
                        m.set(tel, null);
                    }
                }
            });
        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        },
    };
    // 全体ビュー
    var AppView = Backbone.View.extend($.extend(true, AppViewDef, $.customerFileup)); // AppView

    // 施行基本フリーモデル
    var KihonFreeModel = Backbone.Model.extend({
        defaults: function () {
            return {
                // 受付情報タブ
                tanto_cd6: null,    // 受注担当者コード
                tanto_nm6: null,    // 受注担当者名
                tanto_cd7: null,    // 発注担当者コード
                tanto_nm7: null,    // 発注担当者名
                ts_free1: null, // 受付日
                ts_free1_date: null, // 受付日(日付のみ)
                ts_free1_time: null, // 受付日(時間のみ)
                ts_free4: null, // 入金日
                ts_free4_date: null, // 入金日(日付のみ)
                free13_code_cd: '01', // 葬儀区分(受付結果)
                free14_code_cd: null, // 紹介項目
                free15_code_cd: null, // ステータス(承り入力・経過/結果入力)
                free_kbn9: null, // 関係分類
                free_kbn10: null, // 結果状況
                free_kbn11: '0', // 仕入先名称変更区分
                free_kbn14: null, // 相談に見えた方
                v_free27: null, // 紹介業者CD
                v_free28: null, // 紹介業者名
                v_free29: null, // 業者担当者名
                v_free30: null, // 受注部署CD
                v_free31: null, // 発注部署CD
                v_free32: null, // 施行番号(承り入力・経過/結果入力)
                v_free33: null, // 相談項目①
                v_free34: null, // 相談項目②
                v_free35: null, // 相談項目③
                v_free36: null, // 紹介項目(入力)
                n_free7: null, // 成約金額
                n_free8: null, // 手数料額
                d_free3: null, // 成約日
                f_name1: null, // 添付ファイル1
                f_free1: null, // 添付ファイルOID1一時
                f_name2: null, // 添付ファイル2
                f_free2: null, // 添付ファイルOID2一時
                f_name3: null, // 添付ファイル3
                f_free3: null, // 添付ファイルOID3一時
                f_name4: null, // 添付ファイル4
                f_free4: null, // 添付ファイルOID4一時
                f_name5: null, // 添付ファイル5
                f_free5: null, // 添付ファイルOID5一時
                dantai_nm1: null, // 契約団体1
                dantai_nm2: null, // 契約団体2
                dantai_nm3: null, // 契約団体3

            };
        },
        validation: {
            ts_free1_date: {
                required: true,
                customFun: function (value) {
                    return _chkYmd(value);
                }
            },
            ts_free1_time: {
                required: false,
                pattern: 'time'
            },
            ts_free4_date: {
                required: false,
                customFun: function (value) {
                    return _chkYmd(value);
                }
            },
            d_free3: {
                required: function () {
                    var require = false;
                    if (!$.msiJqlib.isNullEx2(this.get('free_kbn10')) && this.get('free_kbn10') == '1') {
                        require = true;
                    }
                    return require;
                },
                customFun: function (value) {
                    return _chkYmd(value);
                }
            },
            n_free7: {
                required: false,
                pattern: 'number'
            },
            n_free8: {
                required: false,
                pattern: 'number'
            },
        },
        labels: {
            ts_free1: '受付日',
            ts_free1_date: '受付日',
            ts_free1_time: '受付時間',
            ts_free4: '入金日',
            ts_free4_date: '入金日',
            d_free3: '成約日',
            n_free7: '成約金額',
            n_free8: '手数料額',
        },
    });

    // 施行基本フリービュー
    var KihonFreeView = Backbone.View.extend({
        el: $("#wrapper"),
        events: {
            "select2-opening #consult_contents1": function () {
                // 開いたときにデフォルト値で絞り込んで表示する
                var orgKbns = $.msiJqlib.objToArray3(data.dataKbns.soudan_contents);
                var fileredKbns = [];
                _.each(orgKbns, function (item) {
                    if (item.orgData.biko === '0000') {
                        fileredKbns.push(item);
                    }
                });
                appcst.consult_contents1 = fileredKbns;
            },
            "select2-opening #consult_contents2": function () {
                // 開いたときにconsult_contents1の値の備考で絞り込んで表示する
                var orgKbns = $.msiJqlib.objToArray3(data.dataKbns.soudan_contents);
                var orgKbns2 = orgKbns;
                var fileredKbns = [];
                var consult_contents1 = this.model.get('v_free33');
                _.each(orgKbns, function (item) {
                    if (item.id === consult_contents1) {
                        _.each(orgKbns2, function (item2) {
                            if (item2.orgData.biko === consult_contents1) {
                                fileredKbns.push(item2);
                            }
                        });
                    }
                });
                appcst.consult_contents2 = fileredKbns;
            },
            "select2-opening #consult_contents3": function () {
                // 開いたときにconsult_contents1の値の備考で絞り込んで表示する
                var orgKbns = $.msiJqlib.objToArray3(data.dataKbns.soudan_contents);
                var orgKbns2 = orgKbns;
                var fileredKbns = [];
                var consult_contents2 = this.model.get('v_free34');
                _.each(orgKbns, function (item) {
                    if (item.id === consult_contents2) {
                        _.each(orgKbns2, function (item2) {
                            if (item2.orgData.biko === consult_contents2) {
                                fileredKbns.push(item2);
                            }
                        });
                    }
                });
                appcst.consult_contents3 = fileredKbns;
            },
            "change #consult_contents1": function (e) {
                this.model.set('v_free34', null);
                this.model.set('v_free35', null);
            },
            "change #consult_contents2": function (e) {
                this.model.set('v_free35', null);
            },
            "click #juchu_tanto_nm,#hachu_tanto_nm,.label.dlg_juchu_tanto,.label.dlg_hachu_tanto": "tantoHelper",
            "click .label.dlg_gyosya": "siireHelper",
            "click #seiyaku_seko_no, .dlg_seiyaku_seko_no": "seiyakuSekoHelper",
            "click #seiyaku_seko_clear": function () {
                this.model.set("v_free32", null);
            },
            "change #shokai_kbn": function (e) {
                var shokai_kbn = e.val;
                var shokai_kbn_nm = e.added.text;
                if (shokai_kbn == SHOKAI_KBN_OTHER) {
                    this.model.set("v_free36", null);
                    $('#shokai_koumoku_nm').removeAttr('disabled');
                } else {
                    this.model.set("v_free36", shokai_kbn_nm);
                    $('#shokai_koumoku_nm').attr('disabled', 'disbaled');
                }
            },
        },
        // 仕入先 pickup
        siireHelper: function(e) {
            var bbm = this.model;
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var cd = $target.data("cd");
            var nm = $target.data("nm");
            var kbn = $target.data("kbn");
            this.$el.msiPickHelper({
                action: 'siire',
                onSelect: function(data) {
                    // console.log( JSON.stringify(data) );
                    bbm.set(cd, data.code);
                    bbm.set(nm, data.name);
                    bbm.set(kbn, data.trade_nm_chg_kbn);
                    if (data.trade_nm_chg_kbn == '1') {
                        $('#gyosya_nm').removeAttr('readonly');
                    } else {
                        $('#gyosya_nm').attr('readonly','readonly');
                    }
                },
                onClear: function() {
                    bbm.set(cd, null);
                    bbm.set(nm, null);
                    bbm.set(kbn, 0);
                },
                hookSetData: function() {
                    return {
                        s_gyosya_flg: 1,
                    }
                },
            });
        },
        // 担当者ヘルパー処理
        tantoHelper: function (e) {
            var m = this.model;
            var $target = $(e.currentTarget);
            // ダイアログイメージをクリックしたときは真上のinput要素を探す
            if ($target.is(".label")) {
                $target = $target.prev("input");
            }
            var cd = $target.data("cd");
            var nm = $target.data("nm");
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set(cd, data.code);
                    m.set(nm, data.name);
                },
                onClear: function () {
                    m.set(cd, null);
                    m.set(nm, null);
                }
            });
        },
        // 施行番号ヘルパー処理
        seiyakuSekoHelper: function () {
            var dataIn = {init_search: 0, no_cond_exp:true, s_moushi_ctxt: '0010',s_moushi_kbn:'1',s_apply:'1', mode:'seiyaku'}; // 初回表示時検索する/しない=1/0
            var t = this;
            $.msiJqlib.celemonyDialogOnSelect = function (data) {
                t.model.set("v_free32", data.seko_no);
                $("#seiyaku_seko_clear").show();
            };
            $.ajax({
                url: $.msiJqlib.baseUrl() + '/mref/sekodialog',
                type: 'GET',
                data: dataIn,
                dataType: 'html',
                success: function (html) {
                    // console.log( html );
                    $('#celemony_dialog').remove();
                    $(html).appendTo($('#wrapper')).fadeIn(400);
                }
                // error処理は共通設定を使う
            });
        },
        bindings: {
            '#uketuke_date': {
                observe: 'ts_free1_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('ts_free1_time'), 'ts_free1', this.model);
                    return $el.val();
                }
            },
            '#uketuke_time': {
                observe: 'ts_free1_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('ts_free1_date'), $el.val(), 'ts_free1', this.model);
                    return $el.val();
                }
            },
            '#seiyaku_nyukin_date': {
                observe: 'ts_free4_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), null, 'ts_free4', this.model);
                    return $el.val();
                }
            },
            '#seiyaku_date': 'd_free3',
            '#juchu_tanto_nm': 'tanto_nm6',
            '#hachu_tanto_nm': 'tanto_nm7',
            '#gyosya_nm': 'v_free28',
            '#gyosya_tanto_nm': 'v_free29',
            '#juchu_bumon_cd': $.msiJqlib.getSelect2Binding('v_free30'),
            '#hachu_bumon_cd': $.msiJqlib.getSelect2Binding('v_free31'),
            '#seiyaku_seko_no': 'v_free32',
            '#consult_contents1': $.msiJqlib.getSelect2Binding('v_free33'),
            '#consult_contents2': $.msiJqlib.getSelect2Binding('v_free34'),
            '#consult_contents3': $.msiJqlib.getSelect2Binding('v_free35'),
            '#shokai_koumoku_nm': 'v_free36',
            '#kankei_bunrui_kbn': $.msiJqlib.getSelect2Binding('free_kbn9'),
            '#result_condition': $.msiJqlib.getCheckBinding('free_kbn10'),
            '#soudan_kbn': $.msiJqlib.getSelect2Binding('free_kbn14'),
            '#jizen_sougi_kbn': $.msiJqlib.getSelect2Binding('free13_code_cd'),
            '#shokai_kbn': $.msiJqlib.getSelect2Binding('free14_code_cd'),
            '#uketamawari_status_kbn': $.msiJqlib.getSelect2Binding('free15_code_cd'),
            '#seiyaku_prc': {
                observe: 'n_free7',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },
            '#seiyaku_tesuryo': {
                observe: 'n_free8',
                onSet: 'commaOmit',
                onGet: 'commaAdd'
            },

        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.render();
        },
        render: function () {
            this.stickit();

            this.$("#uketuke_date, #sibo_date, #seiyaku_date, #seiyaku_nyukin_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$("#uketuke_time, #sibo_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));

            // 受付情報タブ
            // 関係分類
            $.msiJqlib.setSelect2Com1(this.$("#kankei_bunrui_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kankei_bunrui_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 葬儀区分(受付結果)
            $.msiJqlib.setSelect2Com1(this.$("#jizen_sougi_kbn"), {data: $.msiJqlib.objToArray3(data.dataKbns.jizen_sougi_kbn)});
            // 相談に見えた方
            $.msiJqlib.setSelect2Com1(this.$("#soudan_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.soudan_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 紹介項目
            $.msiJqlib.setSelect2Com1(this.$("#shokai_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.shokai_koumoku_kbn)}, $.msiJqlib.setSelect2Default1)));
            // ステータス
            $.msiJqlib.setSelect2Com1(this.$("#uketamawari_status_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.uketamawari_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 結果状況
            $.msiJqlib.setSelect2Com1(this.$("#result_condition_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.result_condition_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 受注部署
            $.msiJqlib.setSelect2Com1(this.$("#juchu_bumon_cd"), ($.extend({data: data.dataKbns.uketamawari_bumon}, $.msiJqlib.setSelect2Default1)));
            // 発注部署
            $.msiJqlib.setSelect2Com1(this.$("#hachu_bumon_cd"), ($.extend({data: data.dataKbns.uketamawari_bumon}, $.msiJqlib.setSelect2Default1)));
            // 相談項目
            appcst.consult_contents1 = $.msiJqlib.objToArray3(data.dataKbns.soudan_contents);
            $.msiJqlib.setSelect2Com1(this.$("#consult_contents1"), ($.extend({data: function () {
                    return {results: appcst.consult_contents1};
                }}, $.msiJqlib.setSelect2Default1)));
            appcst.consult_contents2 = $.msiJqlib.objToArray3(data.dataKbns.soudan_contents);
            $.msiJqlib.setSelect2Com1(this.$("#consult_contents2"), ($.extend({data: function () {
                    return {results: appcst.consult_contents2};
                }}, $.msiJqlib.setSelect2Default1)));
            appcst.consult_contents3 = $.msiJqlib.objToArray3(data.dataKbns.soudan_contents);
            $.msiJqlib.setSelect2Com1(this.$("#consult_contents3"), ($.extend({data: function () {
                    return {results: appcst.consult_contents3};
                }}, $.msiJqlib.setSelect2Default1)));

            return this;
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
    });
    
    // 相談履歴モデル
    var JizenModel = Backbone.Model.extend({
        defaults: function () {
            return {
                // 相談履歴タブ
                uketuke_date_disp: null, 
                contact_kbn: '0', 
                contact_way_kbn: null, 
                contact_process_biko: null, 
                status_kbn: null, 
                total_progress_kbn: null, 
                customer_mail_addr1: null, 
                pick_up_nm: null, 
                anchi_basho_kbn: null, 
                shikijo_cd: null, 
                other_shikijo_nm: null, 
                bodaiji_umu_kbn: null, 
                jyusho_cd: null, 
                bochi_umu_kbn: null, 
                kaiin_kbn: null, 
                seiyaku_status_kbn: null,
                syanai_renrakusaki: null, // 社内連絡先 text_free3 CUSTOMER_BASE_INFO
                text_free4: null, // 問合せ経緯
            };
        },
        validation: {
            contact_kbn: {
                required: true,
            },
        },
        labels: {
            contact_kbn: '問合せ区分'
        },
    });

    // 相談履歴ビュー
    var JizenView = Backbone.View.extend({
        el: $("#wrapper"),
        events: {
            'click .dlg_consult_tanto': 'tantoHelper',
        },
        bindings: {
            '#uketuke_date_disp': 'uketuke_date_disp',
            '#contact_kbn': {
                observe: 'contact_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#contact_way_kbn': {
                observe: 'contact_way_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#contact_process_kbn': {
                observe: 'text_free4',
                afterUpdate: function ($el, event, options) {
                    var vals = $el.val().split(',');
                    $el.select2("val", vals);
                },
                getVal: function ($el, event, options) {
                    return $el.val();
                }
            },
            '#contact_process_biko': 'contact_process_biko',
            '#status_kbn': {
                observe: 'status_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#total_progress_kbn': {
                observe: 'total_progress_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#contact_kahi': $.msiJqlib.getCheckBinding('contact_kahi'),
            '#yuso_futo_check': $.msiJqlib.getCheckBinding('yuso_futo_check'),
            '#dm_kahi': $.msiJqlib.getCheckBinding('dm_kahi'),
            '#customer_mail_addr1': 'customer_mail_addr1',
            '#pick_up_nm': 'pick_up_nm',
            '#anchi_basho_kbn': {
                observe: 'anchi_basho_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#other_shikijo_nm': 'other_shikijo_nm',
            '#shikijo_cd': {
                observe: 'shikijo_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#other_sikijo_nm': 'other_sikijo_nm',
            '#bodaiji_umu_kbn': {
                observe: 'bodaiji_umu_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#jyusho_cd': {
                observe: 'jyusho_cd',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#bochi_umu_kbn': {
                observe: 'bochi_umu_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#kaiin_kbn_jizen': {
                observe: 'kaiin_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#seiyaku_status_kbn': {
                observe: 'seiyaku_status_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                },
            },
            '#nyukai_point': 'nyukai_point',
            '#minyukai_reason': 'minyukai_reason',
            '#syanai_renrakusaki': 'syanai_renrakusaki',

        },
        initialize: function () {
            Backbone.Validation.bind(this, Backbone.Validation.msi_v_iv_callback(appcst.pro, "error1"));
            this.render();
        },
        render: function () {
            this.stickit();
            this.setSelect2();
            return this;
        },
        setSelect2: function () {
            $.msiJqlib.setSelect2Com1(this.$("#contact_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.contact_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#contact_way_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.contact_way_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#contact_process_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.contact_process_kbn)}, $.msiJqlib.setSelect2Default2, {multiple: true, placeholder: '(複数可)'})));
            $.msiJqlib.setSelect2Com1(this.$("#status_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.contact_status_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#total_progress_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.progress_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#anchi_basho_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.anchi_basho_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#shikijo_cd"), ($.extend({data: data.dataKbns.shikijo_mst}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#jyusho_cd"), ($.extend({data: data.dataKbns.jiin_mst}, $.msiJqlib.setSelect2Default2)));
            $.msiJqlib.setSelect2Com1(this.$("#bodaiji_umu_kbn, #bochi_umu_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.umu_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#kaiin_kbn_jizen"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.kaiin_jizen)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#seiyaku_status_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.seiyaku_kbn)}, $.msiJqlib.setSelect2Default1)));
            $.msiJqlib.setSelect2Com1(this.$("#ap_method_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.apoint_kbn)}, $.msiJqlib.setSelect2Default1)));
        },
        commaOmit: function (val, options) {
            var num = val.replace(/,/g, '');
            return num;
        },
        commaAdd: function (val, options) {
            return $.msiJqlib.commaAdd(val);
        },
        // 担当者ヘルパー処理
        tantoHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'consult_tanto_cd': data.code, 'consult_tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'consult_tanto_cd': null, 'consult_tanto_nm': null});
                }
            });
        },
    });
    
    // 相談履歴タブ 相談履歴モデル
    var ConsultHistoryModel = Backbone.Model.extend({
        defaults: function () {
            return {
                history_no: null, //　明細番号
                reception_date: null, // 日付
                reception_time: null, // 時間
                reception_ymd: null, // 時間
                tanto_nm: null, // 担当者名
                tanto_cd: null, // 担当者コード
                reception_kbn: null, // 応対区分
                reception_method_kbn: null, // 応対方法区分
                reception_memo: null, // 内容
            };
        },
        validation: {
            reception_ymd: function (val, attr, computed) {
                if ($.msiJqlib.isNullEx2(computed.reception_date) && !$.msiJqlib.isNullEx2(computed.reception_time)) {
                    return '日付と時刻の整合性がありません';
                }
            },
            reception_date: function (value) {
                return _chkYmd(value);
            },
            reception_time: {
                required: false,
                pattern: 'time'
            },
        },
        labels: {
            reception_date: '打合履歴日付',
            reception_time: '打合履歴時刻',
        }
    }); // ConsultHistroyModel

    // 相談履歴コレクション
    var ConsultHistoryCollection = Backbone.Collection.extend({
        model: ConsultHistoryModel,
        resetLineNo: function () {
            var i, max, m;
            for (i = 0, max = this.length; i < max; i++) {
                m = this.at(i);
                m.set('history_no', i + 1);
                // console.log( 'seq_no=>' + m.get('seq_no') + ' line_no=>' + m.get('line_no') + ' ' + m.get('msi_biko2') );
            }
        },
    });

    // 特記事項ビュー
    var ConsultHistoryView = Backbone.View.extend({
        tagName: 'tbody',
        tmpl: _.template($('#tmpl-consult').html()),
        events: {
            "click a.destroy": "clear",
            "click a.add": "add",
            "click .dlg_date, .dlg_time": "setDatePickerFocus",
            "click .tanto_nm, .label.dlg_staff": "tantoHelper",
        },
        bindings: {
            '.history_no': 'history_no',
            '.reception_date': {
                observe: 'reception_date',
                getVal: function ($el, event, options) {
                    _setNiteiymd($el.val(), this.model.get('reception_time'), 'reception_ymd', this.model);
                    return $el.val();
                }
            },
            '.reception_time': {
                observe: 'reception_time',
                getVal: function ($el, event, options) {
                    _setNiteiymd(this.model.get('reception_date'), $el.val(), 'reception_ymd', this.model);
                    return $el.val();
                }
            },
            '.tanto_nm': {
                observe: 'tanto_nm',
                onSet: function (val, options) {
                    if ($.msiJqlib.isNullEx2(val)) {
                        this.model.set('tanto_cd', null);
                    }
                    return val;
                }
            },
            '.renraku_nm': 'renraku_nm',
            '.douseki_nm': 'douseki_nm',
            '.reception_kbn': {
                observe: 'reception_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '.reception_method_kbn': {
                observe: 'reception_method_kbn',
                afterUpdate: function ($el, event, options) {
                    _setSelect2Val($el, $el.val());
                }
            },
            '.reception_memo': 'reception_memo',
        },
        initialize: function () {
            Backbone.Validation.bind(this);
            this.render();
        },
        render: function () {
            Backbone.Validation.bind(this, {
                valid: function (view, attr) {
                    _valid(view, attr);
                },
                invalid: function (view, attr, error) {
                    _invalid(view, attr, error);
                }
            });
            this.$el.html(this.tmpl({idx: this.model.cid}));
            this.stickit();
            this.$(".reception_date").datetimepicker($.extend({}, $.msiJqlib.datePickerDefault, {showTimepicker: false}));
            this.$(".reception_time").timepicker($.extend({}, $.msiJqlib.datetimePickerDefault));
            // select2
            // 応対
            $.msiJqlib.setSelect2Com1(this.$(".reception_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.outai_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 応対方法
            $.msiJqlib.setSelect2Com1(this.$(".reception_method_kbn"), ($.extend({data: $.msiJqlib.objToArray3(data.dataKbns.outai_method_kbn)}, $.msiJqlib.setSelect2Default1)));
            // 明細一行目は削除ボタンを非表示にする
            if (this.model.get('history_no') == '1') {
                this.$(".destroy").hide();
            }
            return this;
        },
        // Create a item. ugly...
        add: function () {

            var off = this.model.get('history_no');
            var newModel = new ConsultHistoryModel;

            // console.log( 'add line_no=>' + off + ' shift=>' + isUpside + ' isCopy=>' + isCopy );

            appcst.consulthistoryCol.add(newModel, {at: off, silent: true}); // add event を挙げない
            appcst.consulthistoryCol.resetLineNo(); // line_no を再設定
            this.addOne(newModel, appcst.consulthistoryCol, {at: off});
        },
        clear: function () {
            this.model.destroy();
            this.remove();
            appcst.consulthistoryCol.resetLineNo(); // line_no を再設定
        },
        addOne: function (model, list, options) {
            var v = new ConsultHistoryView({model: model});

            var off;
            if (_.has(options, 'at')) {
                off = options['at'];
            }
            // console.log( 'line_no=>' + line_no + ' of=>' + off + ' addOne *** => ' + JSON.stringify(meisai) );

            if (off === undefined) {
                $("#consult_msi").append(v.render().el);
            } else if (off > 0) {
                $("#consult_msi").find('tbody').eq(off - 1).after(v.render().el);
            } else { // off === 0
                $("#consult_msi").prepend(v.render().el);
            }

        },
        // 横アイコンクリック時ピッカーにフォーカス
        setDatePickerFocus: function (e) {
            // 横アイコンクリック時に真上のinput要素を探す
            var $target = $(e.currentTarget).prev("input");
            $target.datepicker("show");
        },
        // 担当者ヘルパー処理 
        tantoHelper: function () {
            var m = this.model;
            this.$el.msiPickHelper({
                action: 'tanto',
                onSelect: function (data) {
                    m.set({'tanto_cd': data.code, 'tanto_nm': data.name});
                },
                onClear: function () {
                    m.set({'tanto_cd': null, 'tanto_nm': null});
                }
            });
        },
    }); // ConsultHistoryView

    msiGlobalObj.markObj.mark('start');
    // データ取得
    try {
        // DBから取得したJsonデータ
        var data = JSON.parse(_.unescape($('#data-customerinfo').text()));
        //console.log(JSON.stringify(mydata.dataSekoDtlCol))
    } catch (e) {
        console.log('JSON error. ' + e);
        $.msiJqlib.showErr('JSON error. ' + e);
    }
    appcst.data = data;
    var codeKbns = data.codeKbns;
    var orgModCnts = data.modCnts;
    // コントローラー名
    appcst.controllerName = data.controllerName;
    appcst.role_nm = data.role_nm;
    // 相談履歴初期化
    appcst.consulthistoryCol = new ConsultHistoryCollection();
    // 互助会タブ初期化
    appcst.gojokaiInfoModel = new appgjk.GojokaiInfoModel();
    appcst.sekoKeiyakusakiInfoModel = new appgjk.SekoKeiyakusakiInfoModel();
    appcst.gojokaiMemberCol = new appgjk.GojokaiMemberCollection();
    appcst.gojokaiInfoView = new appgjk.GojokaiInfoView({model: appcst.gojokaiInfoModel});
    appcst.sekoKeiyakusakiInfoView = new appgjk.SekoKeiyakusakiInfoView({model: appcst.sekoKeiyakusakiInfoModel});
    // APP初期化処理
    appcst.kfModel = new KihonFreeModel();
    appcst.kfView = new KihonFreeView({model: appcst.kfModel});
    appcst.jizenModel = new JizenModel();
    appcst.jizenView = new JizenView({model: appcst.jizenModel});
    appcst.appModel = new AppModel();
    appcst.appView = new AppView({model: appcst.appModel});
    //
    var orgDataApp, orgDataKihonFree, orgDataJizen, orgDataConsultHistoryCol, orgDataGojokaiInfo, orgDataGojokaiMemberCol, orgDataSekoKeiyakusakiInfo;
    var _resetData = function (dataSekoKihon, dataKihonFree, dataJizen, dataConsultHistoryCol, dataGojokaiInfo, dataSekoKeiyakusakiInfo, dataGojokaiMemberCol) {
        // モデルのデータを設定
        appcst.kfView.model.set(dataKihonFree);
        appcst.jizenView.model.set(dataJizen);
        appcst.appView.model.set(dataSekoKihon);
        appcst.gojokaiInfoView.model.set(dataGojokaiInfo);
        appcst.sekoKeiyakusakiInfoView.model.set(dataSekoKeiyakusakiInfo);
        // resetによりAppViewのresetイベントが発火
        appcst.consulthistoryCol.reset(dataConsultHistoryCol);
        appcst.gojokaiMemberCol.reset(dataGojokaiMemberCol);

        // 基本・喪主・その他タブ 
        $("#hall_cd").removeAttr("disabled");
        if (dataSekoKihon || dataKihonFree) {
            if (dataSekoKihon.k_sex_kbn === "2") {
                $('#female').attr("checked", "checked");
                $('#female').prev().addClass("ui-state-active");
                $('#male').removeAttr("checked", "checked");
                $('#male').prev().removeClass("ui-state-active");
            } else if (dataSekoKihon.k_sex_kbn === "1") {
                $('#male').attr("checked", "checked");
                $('#male').prev().addClass("ui-state-active");
                $('#female').removeAttr("checked", "checked");
                $('#female').prev().removeClass("ui-state-active");
            } else {
                $('#male').removeAttr("checked", "checked");
                $('#male').prev().removeClass("ui-state-active");
                $('#female').removeAttr("checked", "checked");
                $('#female').prev().removeClass("ui-state-active");
            }
            // 基本タブ 性別を設定
            if (dataSekoKihon.m_sex_kbn === "2") {
                $('#m_female').attr("checked", "checked");
                $('#m_female').prev().addClass("ui-state-active");
                $('#m_male').removeAttr("checked", "checked");
                $('#m_male').prev().removeClass("ui-state-active");
            } else if (dataSekoKihon.m_sex_kbn === "1") {
                $('#m_male').attr("checked", "checked");
                $('#m_male').prev().addClass("ui-state-active");
                $('#m_female').removeAttr("checked", "checked");
                $('#m_female').prev().removeClass("ui-state-active");
            } else {
                $('#m_male').removeAttr("checked", "checked");
                $('#m_male').prev().removeClass("ui-state-active");
                $('#m_female').removeAttr("checked", "checked");
                $('#m_female').prev().removeClass("ui-state-active");
            }
            // 紹介項目区分が諸口の場合は活性
            if (!$.msiJqlib.isNullEx2(dataKihonFree.free14_code_cd) && dataKihonFree.free14_code_cd == SHOKAI_KBN_OTHER) {
                $('#shokai_koumoku_nm').removeAttr('disabled');
            } else {
                $('#shokai_koumoku_nm').attr('disabled','disabled');
            }
            if (!$.msiJqlib.isNullEx2(dataKihonFree.free_kbn11) && dataKihonFree.free_kbn11 == '1') {
                $('#gyosya_nm').removeAttr('readonly');
            } else {
                $('#gyosya_nm').attr('readonly','readonly');
            }
            // 宗派その他の場合の処理
            appcst.appView.setShuhaOther();
            // 故人名画像ファイルリンク設定
            appcst.appView.setImgLink("#input-tab", dataSekoKihon.k_file_nm, null);
            // 喪主名画像ファイルリンク設定
            appcst.appView.setImgLink("#infochief-tab #chief", dataSekoKihon.m_file_nm, null);
        }
//        console.log(str);
        // 部門コードの初期値セット
        if ($.msiJqlib.isNullEx2(appcst.appModel.get('bumon_cd'))) {
//            var bumonCd = $('#hall_cd').val();
            appcst.appModel.set('bumon_cd', appcst.appModel.get('login_bumon_cd'));
            msiLib2.setHallCd(appcst.appModel.get('login_bumon_cd'));
        }
        // 権限によって項目を表示・非表示
        if (appcst.role_nm == 'sysman' || appcst.role_nm == 'tachikawa_eigyo_kanri_mst' || appcst.role_nm == 'tachikawa_eigyo_jimu' || appcst.role_nm == 'msi') {
            $('.lbl_seiyaku_prc').show();
            $('#seiyaku_prc').show();
            $('.lbl_seiyaku_tesuryo').show();
            $('#seiyaku_tesuryo').show();
        } else {
            $('.lbl_seiyaku_prc').hide();
            $('#seiyaku_prc').hide();
            $('.lbl_seiyaku_tesuryo').hide();
            $('#seiyaku_tesuryo').hide();
        }
        // データを退避する
        orgDataApp = appcst.appModel.toJSON();
        orgDataKihonFree = appcst.kfModel.toJSON();
        orgDataJizen = appcst.jizenModel.toJSON();
        orgDataConsultHistoryCol = appcst.consulthistoryCol.toJSON();
    };
    msiGlobalObj.markObj.mark('reset');
    _resetData(data.dataSekoKihon, data.dataSekoKihonFree, data.dataJizen, data.dataConsultHistoryCol,data.dataGojokaiInfo,data.dataSekoKeiyakusakiInfo,data.dataGojokaiMemberCol);
    msiGlobalObj.markObj.markOutput();
    appcst.file1 = fileUpLib.upload({m: appcst.kfModel, attr_oid: 'f_free1', attr_fnm: 'f_name1', el: '#file_clip_r1', imgprv: false, filekbn: 6});
    appcst.file2 = fileUpLib.upload({m: appcst.kfModel, attr_oid: 'f_free2', attr_fnm: 'f_name2', el: '#file_clip_r2', imgprv: false, filekbn: 6});
    appcst.file3 = fileUpLib.upload({m: appcst.kfModel, attr_oid: 'f_free3', attr_fnm: 'f_name3', el: '#file_clip_r3', imgprv: false, filekbn: 6});
    appcst.file4 = fileUpLib.upload({m: appcst.kfModel, attr_oid: 'f_free4', attr_fnm: 'f_name4', el: '#file_clip_r4', imgprv: false, filekbn: 6});
    appcst.file5 = fileUpLib.upload({m: appcst.kfModel, attr_oid: 'f_free5', attr_fnm: 'f_name5', el: '#file_clip_r5', imgprv: false, filekbn: 6});
    // ファイルリンク設定処理
    appcst.setFileLink = function () {
        appcst.file1.setlink();
        appcst.file2.setlink();
        appcst.file3.setlink();
        appcst.file4.setlink();
        appcst.file5.setlink();
    };
    // かな自動入力ペア key:名称要素 value[カナ要素, カナモデルプロパティ, モデル]
    var _name_kana_pair = {
        '#input-tab #k_last_nm': ['#input-tab #k_last_knm', 'k_last_knm', appcst.appModel]
        , '#input-tab #k_first_nm': ['#input-tab #k_first_knm', 'k_first_knm', appcst.appModel]
        , '#infochief-tab #chief #m_last_nm': ['#infochief-tab #chief #m_last_knm', 'm_last_knm', appcst.appModel]
        , '#infochief-tab #chief #m_first_nm': ['#infochief-tab #chief #m_first_knm', 'm_first_knm', appcst.appModel]
        , '#infochief-tab #fc_last_nm': ['#infochief-tab #fc_last_knm', 'fc_last_knm', appcst.appModel]
        , '#infochief-tab #fc_first_nm': ['#infochief-tab #fc_first_knm', 'fc_first_knm', appcst.appModel]
        , '#input-tab #syuha_nm_other': ['#input-tab #syuha_knm2', 'syuha_knm', appcst.appModel]
    };
    // カナ自動入力設定
    $.msiJqlib.setAutoKanaModel(_name_kana_pair);

    var _zip_addr_pair = {
        '#input-tab #zip_1': ['kg_yubin_no', 'kg_addr1', appcst.appModel]
        , '#infochief-tab #zip_1': ['mg_yubin_no', 'mg_addr1', appcst.appModel]
        , '#input-tab #temple_yubin_no': ['temple_yubin_no', 'temple_addr1', appcst.appModel]
    };

    // 郵便番号による住所1自動入力設定
    $.msiJqlib.setAutoZipToAddrModel(_zip_addr_pair);

    // タブの横幅設定
    var $li = $("#detail .tab li");
    $li.width(100 / $li.length + "%");

    $("#customer-div-wrapper").show();

    // ヘッダー共通処理
    // 新規作成ボタン押下
    $("#header #btn_new").click(function () {
        location.href = $.msiJqlib.baseUrl() + '/juchu/preconsult/new';
    });
    // ページ遷移前の確認
    $(window).on('beforeunload', function () {
        if (_isChanged()) {
            return "保存されていないデータがあります.";
        }
    });
    var _isChanged = function () {
        var changed = true;
        // 施行基本イコール
        var sekoKihonEq = $.msiJqlib.isEqual(appcst.appModel.toJSON(), orgDataApp);
        // 施行基本汎用フリー情報イコール
        var sekoKihonFreeEq = $.msiJqlib.isEqual(appcst.kfModel.toJSON(), orgDataKihonFree);
        // 顧客基本情報イコール
        var jizenEq = $.msiJqlib.isEqual(appcst.jizenModel.toJSON(), orgDataJizen);
        // 相談履歴イコール
        var consultHistoryEq = $.msiJqlib.isEqual(appcst.consulthistoryCol.toJSON(), orgDataConsultHistoryCol);

        if (sekoKihonEq && sekoKihonFreeEq && jizenEq && consultHistoryEq) {
            changed = false;
        }
        return changed;
    };

});
